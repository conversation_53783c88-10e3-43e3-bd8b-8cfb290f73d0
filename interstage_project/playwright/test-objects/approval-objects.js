import { expect } from "@playwright/test";

class ApprovalPage {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async setPayout(valuedef) {
    const key = "commission-view-period";
    const value = valuedef;
    await this.page.evaluate(
      ({ key, value }) => {
        // Set the localStorage value for the current page
        localStorage.setItem(key, value);
      },
      { key, value }
    );
    await this.page.goto("/commissions", { waitUntil: "networkidle" });
  }

  async sendBulkRequest() {
    // await this.page.getByRole("row", { name: "Name" }).getByLabel("").click();

    await this.page.locator('input[name="row-0"]').check();
    await this.page.locator('input[name="row-1"]').check();

    await this.page.getByText("Request Approval").first().click();
    await this.page
      .getByRole("button", { name: "Ok, request approval" })
      .click();
    await this.page.getByText("WF1").click();
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
    await this.page
      .getByText("Bulk approval creation task submitted.")
      .first()
      .isVisible();
  }

  async statementsNav(url) {
    await this.page.goto(url, { waitUntil: "networkidle" });
  }

  async payouts() {
    await this.page.goto("http://localhost:3000/approvals/payouts", {
      waitUntil: "networkidle",
    });
  }

  async tabName(tab) {
    await this.page.getByRole("button", { name: tab }).click();
  }

  async selectorButton() {
    const date_picker = await this.page.locator(".ant-select-selector").first();
    await date_picker.waitFor({ state: "visible", timeout: 10000 });
    await this.page.waitForTimeout(2000);
    await this.page.locator(".ant-select-selector").first().click();
  }

  async approveButton() {
    await this.page.getByRole("button", { name: "Yes, approve" }).click();
  }

  async rejectButton() {
    await this.page.getByRole("button", { name: "Yes, reject" }).click();
  }

  async banner(message) {
    await this.page.getByText(message).first().isVisible({ timeout: 10000 });
  }

  async approvalsTab() {
    const approval_tab = await this.page
      .getByRole("tab", { name: "Approvals" })
      .locator("span");
    await approval_tab.waitFor({ state: "visible", timeout: 20000 });
    await this.page
      .getByRole("tab", { name: "Approvals" })
      .locator("span")
      .click();
  }

  async actionsButton(action) {
    await this.page.getByText(action).click();
  }

  async popupButton(buttonname) {
    await this.page.getByRole("button", { name: buttonname }).click();
  }

  async loaderpopUp(message) {
    await this.page
      .getByText(message)
      .first()
      .waitFor({ state: "hidden", timeout: 30000 });
  }

  async payeeApproval(period, plan, rowcount, planname, action) {
    await this.page.getByText(period).click();
    await this.page.getByText(plan).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payee$/ })
      .nth(2)
      .click();
    await this.page.getByTitle("A User").nth(1).click();
    await this.page
      .getByRole("row", { name: "Payee Name" })
      .getByRole("columnheader")
      .first()
      .click();
    await this.page.getByRole("button", { name: rowcount }).click();
    await this.page.waitForTimeout(1500);
    await this.page
      .getByLabel(planname)
      .getByRole("button", { name: action })
      .click();
  }

  async payeeApprovalMultiple(period, plan, planname, action) {
    await this.page.locator(`[title="${period}"]`).first().click();
    await this.page.getByText(plan).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payee$/ })
      .nth(2)
      .click();
    await this.page.getByTitle("A User").nth(1).click();
    await this.page
      .getByRole("row", { name: "Payee Name" })
      .getByRole("columnheader")
      .first()
      .click();
    await this.page
      .getByLabel(planname)
      .getByRole("button", { name: action })
      .last()
      .click();
  }

  async payeeReject(period, planname, rowcount, action) {
    await this.page.getByText(period).first().click();
    await this.page.getByText(planname).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payee$/ })
      .nth(2)
      .click();
    await this.page.getByTitle("A User").nth(1).click();
    await this.page
      .getByRole("row", { name: "Payee Name" })
      .getByRole("columnheader")
      .first()
      .click();
    await this.page.getByRole("button", { name: rowcount }).click();
    await this.page.waitForTimeout(1500);
    await this.page.getByRole("button", { name: action }).last().click();
    await this.page.getByRole("dialog").getByRole("textbox").click();
    await this.page.getByRole("dialog").getByRole("textbox").fill("reject");
  }

  async approvalRequest() {
    await this.page.getByRole("button").nth(4).click();
    await this.page.getByText("Request Approval").click();
    await this.page.getByText("Workflow", { exact: true }).click();
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
  }

  async rejectAction() {
    await this.page.getByRole("button", { name: "Reject" }).last().click();
    await this.page.getByPlaceholder("Add your comments").click();
    await this.page.getByPlaceholder("Add your comments").fill("reject");
    await this.page.getByRole("button", { name: "Reject Request" }).click();
  }

  async reRequest() {
    await this.page.locator("svg.ant-dropdown-trigger").click();
    await this.page.locator("svg.ant-dropdown-trigger").click();
    await expect(
      this.page.locator(
        "//span[text()='Request approval again']/parent::div/parent::div"
      )
    ).toBeDisabled();
  }

  async searchPeriod(periodvalue) {
    await this.page.locator(".ant-select-selector").click();
    await this.page.getByPlaceholder("Search Period").click();
    await this.page.getByPlaceholder("Search Period").press("CapsLock");
    await this.page.getByPlaceholder("Search Period").fill(periodvalue);
    await this.page.getByTitle(periodvalue).locator("div").first().click();
  }

  async drawApproval() {
    await this.page.getByRole("button").nth(4).click();
    await this.page.getByRole("menuitem", { name: "Request Approval" }).click();
    await this.page
      .getByText("2. Earned Commission is 0 (or)")
      .first()
      .isVisible();
    await this.page.getByRole("button", { name: "OK" }).click();
  }

  async settingsApproval() {
    await this.page.goto("/settings", { waitUntil: "networkidle" });
    await this.page.getByRole("link", { name: "Approval Workflows" }).click();
    await this.page
      .getByRole("tab", { name: "Settings" })
      .locator("span")
      .click();
    await this.page.locator(".relative > .w-4").click();
    await this.page
      .getByRole("tooltip", {
        name: "Line items in hidden criteria, adjustments and draws are not considered for approval",
      })
      .isVisible();
  }

  async viewingTabs() {
    await this.page.locator(".ant-select-selector").first().isVisible();
    await this.page
      .locator("span[class='ant-select-selection-placeholder']")
      .first()
      .isVisible();
  }

  async adminView() {
    await this.page.locator(".ant-select-selector").first().click();
    await this.page
      .locator("span[class='ant-select-selection-placeholder']")
      .first()
      .isVisible();
    await this.page
      .locator("span[class='ant-select-selection-placeholder']")
      .nth(1)
      .isVisible();
    await this.page
      .locator("span[class='ant-select-selection-placeholder']")
      .nth(2)
      .isVisible();
    await this.page
      .getByRole("button", { name: "Manage Workflows" })
      .isVisible();
  }

  async closeButton() {
    await this.page
      .getByRole("button", { name: "Close" })
      .filter({ hasText: "Close" })
      .click();
  }

  async validatingRejectmessage(status, message) {
    await this.page
      .getByRole("tab", { name: "Approvals" })
      .getByText("Approvals")
      .click();
    await this.page.getByText(status).click();
    await this.page
      .getByRole("gridcell", { name: message })
      .locator("svg")
      .nth(1)
      .click();
  }

  async deletingRequest() {
    await this.page
      .getByRole("button", { name: "Delete all requests" })
      .click();
    await this.page.getByRole("button", { name: "Yes, delete" }).click();
  }

  async approvePayee(period, user) {
    await this.page.locator(".ant-select-selector").first().click();
    await this.page.getByTitle(period).locator("div").first().click();
    await this.page.locator('div[data-testid="ever-select"]').nth(0).click(); // click payee dropdown
    await this.page.getByTitle(user).locator("div").first().click();
    await this.page.getByRole("gridcell").locator("svg").first().click();
  }

  async approveAdmin(period, user) {
    await this.page.locator(".ant-select-selector").first().click();
    await this.page.getByTitle(period).locator("div").first().click();
    await this.page
      .locator("div:nth-child(3) > .ant-select > .ant-select-selector")
      .click();
    await this.page.getByTitle(user).locator("div").first().click();
    await this.page.getByRole("gridcell").locator("svg").last().click();
  }

  async rerequestfromStatements() {
    await this.page.locator("svg.ant-dropdown-trigger").click();
    await this.page
      .locator(
        "//span[text()='Request approval again']/parent::div/parent::div"
      )
      .click();
    await this.page.getByRole("textbox").click();
    await this.page.getByRole("textbox").fill("resend");
    await this.page.getByRole("button", { name: "Send Request" }).click();
  }

  async approvingInApprovals() {
    await this.page
      .getByRole("tabpanel", { name: "Approvals" })
      .getByText("Pending")
      .click();
    await this.page.getByText("1 - 1of1rows").first().isVisible();
    await this.page
      .getByRole("row", { name: "₹1,166.67" })
      .locator("svg")
      .first()
      .click();
  }

  async approvingFromStatements() {
    await this.page.getByRole("button", { name: "Approve" }).click();
    await this.page.getByRole("button", { name: "Yes, approve" }).click();
    await this.page.getByText("Approved").first().isVisible();
  }

  async actionButtonClick(locate) {
    await this.page.locator(locate).click();
  }

  async dueDate() {
    await this.page.getByPlaceholder("Select date").click();
    const today = await this.page.getByText("Today");
    await today.waitFor({ state: "visible", timeout: 10000 });
    await this.page.getByText("Today").click();
    // await this.page.getByTitle(date).getByText("1").click();
  }

  async requestApprovalByEmail(email, workflowName) {
    await this.page.getByTestId(`${email}-actions-dd`).click();
    await this.page.getByText("Request Approval").click();
    await this.page.getByText(workflowName, { exact: true }).click();
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
    await this.page
      .getByText("Approval requested successfully.")
      .first()
      .waitFor({ state: "visible" });
    await this.page.waitForTimeout(2000);
  }

  async changeApprovalPayoutsLineItem() {
    await this.page.getByLabel("Line item level approvals").click();
    await this.page.getByRole("button", { name: "Yes, change" }).click();
    await this.page.waitForTimeout(3000);
  }

  async waitForApprovalsLoad() {
    await this.page
      .getByText("Approval", { exact: true })
      .waitFor({ state: "visible", timeout: 20000 });
  }

  async clickApprovalRequest(criteria) {
    await this.page.locator("button.ant-tabs-nav-more").hover();
    await this.page.getByLabel("expanded dropdown").getByText(criteria).click();
  }

  async revokeApprovalByEmail(email) {
    await this.page.getByTestId(`${email}-actions-dd`).click();
    await this.page.getByText("Revoke Approval").click();
    await this.page.getByRole("button", { name: "Revoke" }).click();
    await this.page
      .getByText("Request has been revoked successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  async bulkRevokeApproval() {
    await this.page
      .getByRole("row", { name: "Name", exact: true })
      .getByRole("columnheader")
      .first()
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Revoke Approval$/ })
      .click();
    await expect(
      this.page
        .getByText("Are you sure you want to revoke these approval request(s)?")
        .first()
    ).toBeVisible();
    await expect(
      this.page.getByText(
        "You're about to revoke 2 approval request(s). This will stop the approval process for all selected payouts. This action cannot be undone."
      )
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Revoke" }).click();
    await expect(
      this.page.getByText("Bulk approval revoking task submitted.").first()
    ).toBeVisible();
  }

  async verifyApprovalStatus(email, status) {
    await this.searchUserLocator.clear();
    await this.searchUserLocator.fill(email);
    await this.delay(1000);
    if (status === "Revoked") {
      await expect(
        this.page.getByRole("gridcell", { name: "Revoked by EA" })
      ).toBeVisible();
    } else if (status === "Approved") {
      await expect(
        this.page.getByRole("gridcell", { name: "Approved" })
      ).toBeVisible();
    } else if (status === "Withdrawn") {
      await expect(
        this.page.getByRole("gridcell", { name: "Withdrawn by EA" })
      ).toBeVisible();
    }
    await this.searchUserLocator.clear();
    await this.delay(1000);
  }

  async verifyRevokeApprovalButtonIsDisplayed(status) {
    await this.delay(1500);
    await this.page
      .locator(
        '//span[.//input[@placeholder="Search by name or email"]]/following-sibling::button'
      )
      .first()
      .click();
    await this.page
      .getByTestId("approval_status")
      .locator("div")
      .filter({ hasText: "Select" })
      .click();
    await this.page.keyboard.type(status);
    await this.page.keyboard.press("Enter");
    await this.page.getByRole("button", { name: "Apply" }).click();
    await expect(
      this.page.getByRole("button", { name: "Clear" })
    ).toBeVisible();
    await this.page
      .locator("div.transition-all  div.flex-col button.ant-btn")
      .first()
      .click();
    await this.delay(1000);
    await this.page.locator('[data-testid*="actions-dd"]').first().click();
    if (["Requested", "Needs Attention"].includes(status)) {
      await expect(
        this.page.getByRole("menuitem", { name: "Revoke Approval" })
      ).toBeVisible();
    } else {
      await expect(
        this.page.getByRole("menuitem", { name: "Revoke Approval" })
      ).not.toBeVisible();
    }
    await this.page.getByRole("button", { name: "Clear" }).click();
    await this.page.waitForTimeout(1500);
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async verifyPayoutColumnValues(email) {
    await this.searchUserLocator.clear();
    await this.searchUserLocator.fill(email);
    await this.delay(1000);
    await expect(
      this.page.getByRole("gridcell", { name: "--" }).first()
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "--" }).nth(1)
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "--" }).nth(2)
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "--" }).nth(3)
    ).toBeVisible();
  }

  async searchUserEmail(email) {
    await this.searchUserLocator.clear();
    await this.searchUserLocator.fill(email);
    await this.delay(1000);
  }

  async raiseApprovalRequest(workflowName) {
    await this.page.locator("button.ant-dropdown-trigger").last().click();
    await this.page.getByText("Request Approval").first().click();
    await this.page.getByText(workflowName).click();
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
    await this.page
      .getByText("Approval requested successfully.")
      .first()
      .waitFor({ state: "visible" });
  }

  async withdrawApprovalRequest() {
    await this.page.getByTestId("approval-request-dropdown").click();
    await this.actionsButton("Withdraw pending requests");
    await this.popupButton("Withdraw Request");
    await this.loaderpopUp("Withdrawing requests...");
    await this.banner("Request withdrawn successfully.");
  }

  async searchFilter(filter, option) {
    if (filter === "Awaiting Response From") {
      await this.page
        .locator(
          '//div[@data-testid="option-awaiting_response_from"]/parent::div/following-sibling::div'
        )
        .click();
      await this.delay(1000);
      await this.page
        .locator("div[class*='ant-select-dropdown'] [label='A User']")
        .click();
    } else if (filter === "Approval Status") {
      await this.page
        .getByTestId("approval_status")
        .locator("div")
        .filter({ hasText: "Select" })
        .click();
      await this.delay(1000);
      await this.page
        .getByTestId("approval_status")
        .getByText(option, { exact: true })
        .click();
    } else if (filter === "Stage Status") {
      await this.page
        .getByTestId("stage_status")
        .locator("div")
        .filter({ hasText: "Select" })
        .click();
      await this.delay(1000);
      await this.page.locator("div").filter({ hasText: option }).nth(1).click();
    }
    await this.page.getByRole("button", { name: "Apply" }).click();
    await expect(
      this.page
        .locator(
          '//span[.//input[@placeholder="Search by name or email"]]/following-sibling::button'
        )
        .first()
    ).toBeVisible();
  }

  async clearFilter() {
    await this.page.locator("span", { hasText: "Clear" }).click();
  }

  async bulkSelectLock(payee_email) {
    // await this.page.locator("label.ant-checkbox-wrapper").last().click();
    await this.page.getByTestId(payee_email).check();
    await this.page.getByText("Lock", { exact: true }).click();
    await this.page.getByRole("button", { name: "Lock" }).click();
    const successMessage = this.page
      .locator('text="Lock status updated successfully"')
      .first();
    await successMessage.waitFor({ state: "visible", timeout: 10000 });
  }

  async bulkApprovals(payee_email, workflowName) {
    // await this.page.locator("label.ant-checkbox-wrapper").click();
    await this.page.getByTestId(payee_email).check();
    await this.page.getByText("Request Approval").first().click();
    await this.page
      .getByRole("button", { name: "Ok, request approval" })
      .click();
    await this.page.getByText(workflowName).click();
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
    const successMessage = this.page
      .getByText("Bulk approval creation task submitted.")
      .first();
    await successMessage.waitFor({ state: "visible", timeout: 10000 });
  }

  async checkRequestedpayee(payee) {
    const firstElement = await this.page
      .getByRole("link", { name: payee })
      .first();
    await firstElement.waitFor({ state: "visible", timeout: 10000 });
  }

  async searchUserEmailInPayouts(email) {
    await this.searchUserLocator.clear();
    await this.searchUserLocator.fill(email);
    await this.delay(1000);
  }

  async closeReviewCard() {
    await this.page.getByLabel("Close", { exact: true }).click();
  }

  async searchUserInUsersModule(email) {
    await this.searchUserLocator.fill(email);
    await this.page.waitForTimeout(4000);
  }

  async isTextVisible(text) {
    await expect(this.page.getByText(text).first()).toBeVisible();
  }

  async navigateToUsersAndImpersonate(email) {
    await this.page.goto("/users", { waitUntil: "networkidle" });
    await this.searchUserLocator.fill(email);
    await this.page.waitForTimeout(4000);
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Login as user" }).click();
    await this.page
      .getByText("Logged in as")
      .waitFor({ state: "visible", timeout: 60000 });
  }

  async verifyApprovalResponse(email, period, status) {
    await this.payouts();
    await this.page.getByTestId("all-requests-tab").click();
    await this.page.getByTestId("period-select").getByText("All").click();
    await this.page.getByText(period).click();
    await this.searchUserEmailInPayouts(email);
    await expect(
      this.page.getByRole("gridcell", { name: status }).first()
    ).toBeVisible();
  }

  async reviewCardIcon() {
    await this.page
      .locator('span[role="presentation"]>div>button>div>svg')
      .first()
      .click();
    await this.delay(1000);
  }

  async verifyApprovalStatusAndMessage(fullUsername, email, period, status) {
    await this.payouts();
    await this.page.getByTestId("all-requests-tab").click();
    await this.page.getByTestId("period-select").getByText("All").click();
    await this.page.getByText(period).click();
    await this.searchUserEmailInPayouts(email);
    await expect(
      this.page.getByRole("gridcell", { name: status }).first()
    ).toBeVisible();
    await this.page
      .locator('span[role="presentation"]>div>button>div>svg')
      .first()
      .click(); //Review button
    await this.delay(1000);
    if (status === "Rejected") {
      await this.isTextVisible(`rejected ${fullUsername}'s payout`);
    } else if (status === "Withdrawn") {
      await this.isTextVisible(
        `have withdrawn ${fullUsername}'s payout approval request`
      );
    } else if (status === "Revoked") {
      await this.isTextVisible(
        `everstage revoked ${fullUsername}'s payout for the period`
      );
    } else if (status === "Auto cancelled") {
      await this.isTextVisible(
        `We have cancelled ${fullUsername}'s payout approval request`
      );
      await this.isTextVisible("For further details, contact your admin.");
    }

    await this.closeReviewCard();
    await this.page.getByRole("button", { name: "Exit" }).click();
  }

  async selectMonth(month) {
    await this.page.locator("div.ant-select-selector").click();
    await this.page.getByText("" + month + "").click();
  }

  async unlockStatements(email) {
    await this.page.getByTestId(`${email}-actions-dd`).click();
    await this.page.getByText("Unlock Statements").click();
    await this.page
      .getByText(
        "Any pending approval requests and previous approvals will be cancelled if unlocked."
      )
      .click();
    await this.page.getByRole("button", { name: "Confirm" }).click();
    await this.page
      .getByText("Lock status updated successfully")
      .first()
      .waitFor({ state: "visible" });
    this.delay(10000);
    await this.page.reload();
  }

  async lockStatements(email) {
    await this.page.getByTestId(`${email}-actions-dd`).click();
    await this.page.getByText("Lock Statements").click();
    await this.page
      .getByText("Lock status updated successfully")
      .first()
      .waitFor({ state: "visible" });
    this.delay(10000);
    await this.page.reload();
  }

  async approveAsPayee() {
    await this.page.getByRole("button", { name: "Approve" }).last().click();
    await this.page.getByRole("button", { name: "Yes, approve" }).click();
    await this.page
      .getByText("You've approved the request!")
      .last()
      .waitFor({ state: "visible" });
  }

  async sortByReponse(order) {
    if (order == "ASC") {
      await this.page.getByText("Response").click();
    } else if (order == "DSC") {
      await this.page.getByText("Response").click();
      this.delay(500);
      await this.page.getByText("Response").click();
    }
  }

  async verifyApprovalBannerToBe(status) {
    if (status == "Visible") {
      await this.page.getByTestId("pending-requests-tab").click();
      await expect(
        this.page.getByText("Manage approval requests")
      ).toBeVisible();
      await expect(
        this.page.getByText(
          "Track and act on all your approval requests here. Click "
        )
      ).toBeVisible();
      await expect(
        this.page.getByText("for quick insights into Payout and Quota details.")
      ).toBeVisible();
    } else if (status == "Hidden") {
      await this.page.getByTestId("pending-requests-tab").click();
      await expect(
        this.page.getByText("Manage approval requests")
      ).toBeHidden();
      await expect(
        this.page.getByText(
          "Track and act on all your approval requests here. Click "
        )
      ).toBeHidden();
      await expect(
        this.page.getByText("for quick insights into Payout and Quota details.")
      ).toBeHidden();
    }
  }

  async searchUserInPendingRequestsTab(email, period) {
    await this.page.getByTestId("pending-requests-tab").click();
    await this.page.getByTestId("period-select").getByText("All").click();
    await this.page.getByText(period).click();
    await this.searchUserEmailInPayouts(email);
  }

  async verifyRejectReasonCharacterlimit() {
    await this.page.getByRole("button", { name: "Reject" }).last().click();
    await expect(
      this.page.getByText("Amount$NaN", { exact: true })
    ).toBeVisible();
    await this.page.getByPlaceholder("Add your comments").click();
    const reasontextWithMoreThan250 =
      "reejectededddd 2efyg3eiu23879837193fdhgbckjwdicbwuw8eufuyefuwhebuwh cuwdchwbdcwuygfuhwbuwhfijwnefwnefiuwhbeufUBHFWIEJFNIWEJFWUHEBFUWH9UEHF837TY2984UR0P3O4KOH8&$#%^&*(IBFEHRIFUEBRUFYUFURHFBNRIFN3IUYFVU3JFNURHF38RFY9348Y8347R834GHFI3UFGB3RUGFH3ruhgLASTextra";
    const reasontextWith250 =
      "reejectededddd 2efyg3eiu23879837193fdhgbckjwdicbwuw8eufuyefuwhebuwh cuwdchwbdcwuygfuhwbuwhfijwnefwnefiuwhbeufUBHFWIEJFNIWEJFWUHEBFUWH9UEHF837TY2984UR0P3O4KOH8&$#%^&*(IBFEHRIFUEBRUFYUFURHFBNRIFN3IUYFVU3JFNURHF38RFY9348Y8347R834GHFI3UFGB3RUGFH3ruhgLAST";
    await this.page
      .getByPlaceholder("Add your comments")
      .fill(reasontextWithMoreThan250);
    const Expected = await this.page
      .getByPlaceholder("Add your comments")
      .textContent();
    expect(reasontextWith250).toEqual(Expected);
    await this.page.getByRole("button", { name: "Cancel" }).click();
  }
}

export default ApprovalPage;
