import ApprovalPage from "../../../../test-objects/approval-objects";
import Revertexitpage from "../../../../test-objects/revertexit-objects";

const {
  localplayFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 600000);
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "In before Each, unable to click on Exit button for Logged in user"
      );
    }
  }
});

test.describe(
  "Approvals",
  { tag: ["@approvals", "@regression", "@adminchamp-2"] },
  () => {
    test("Approve a request", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", { waitUntil: "networkidle" });
      await page.waitForTimeout(2000);
      await page
        .getByRole("gridcell", {
          name: "<NAME_EMAIL>",
        })
        .first()
        .click();
      await page.getByRole("button", { name: "Approve" }).last().click();
      await page.getByRole("button", { name: "Yes, approve" }).click();
      const approvetext = await page
        .locator("div")
        .filter({
          hasText: "You've approved the request!",
        })
        .nth(3);
      await approvetext.waitFor({ state: "visible", timeout: 10000 });
    });

    test("Create a workflow", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Approval Workflows" }).click();
      await page.getByRole("button", { name: "Create Workflow" }).click();
      await page.getByRole("textbox").nth(1).click();
      await page.getByRole("textbox").nth(1).press("CapsLock");
      await page.getByRole("textbox").nth(1).fill("Test QA");
      await page.locator(".ant-select-selector").click();
      await page.getByTitle("Payouts").locator("div").first().click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByPlaceholder("Search", { exact: true }).fill("lak");
      await page.getByText("<EMAIL>").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.waitForTimeout(2000);
      await page
        .getByLabel(
          "Anyone approvesStage is considered Rejected when the first person responding rejects."
        )
        .check();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .getByRole("listitem")
        .filter({ hasText: "Reporting manager of payee" })
        .click();
      await page
        .getByRole("listitem")
        .filter({ hasText: "Manager of approvers in previous stage" })
        .click();
      await page.getByRole("switch").click();
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("1");
      await page
        .getByLabel(
          "Automatically approve stage if no response beyond the due date"
        )
        .check();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByText("Payee", { exact: true }).click();
      await page.waitForTimeout(3000);
      await page.getByRole("button", { name: "Save Workflow" }).click();
      await page
        .getByText("Workflow created successfully")
        .first()
        .waitFor({ state: "visible" });
    });

    test("delete a workflow", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Approval Workflows" }).click();
      await page.locator(".ant-dropdown-trigger").first().click();
      await page.getByRole("listitem").filter({ hasText: "Delete" }).click();
      await page.getByRole("button", { name: "Yes, Confirm" }).click();
      const popUp = await page
        .locator("div")
        .filter({
          hasText: "Workflow deleted successfully",
        })
        .nth(3);
      await popUp.waitFor({ state: "visible" });
    });

    test("Clone a workflow", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Approval Workflows" }).click();
      await page.locator(".ant-dropdown-trigger").first().click();
      await page.getByRole("listitem").filter({ hasText: "Clone" }).click();
      const popUp = await page
        .locator("div")
        .filter({
          hasText: "Workflow cloned successfully",
        })
        .nth(3);
      await popUp.waitFor({ state: "visible" });
    });

    test("Raise Approval Request", async ({ adminPage }) => {
      const page = adminPage.page;
      const statementUrl =
        "/statements/eyJwYXllZUVtYWlsSWQiOiJyZWplY3RAbG9jYWxwbGF5LmNvbSIsInBzZCI6IjIwMjMtMDYtMDEiLCJwZWQiOiIyMDIzLTA2LTMwIn0=";
      await page.goto(statementUrl, { waitUntil: "networkidle" });
      await page.locator("button.ant-dropdown-trigger").last().click();
      await page.getByText("Request Approval").click();
      await page.getByText("Request Workflow").click();
      await page.getByRole("button", { name: "Send Approval Request" }).click();
      const popUpApproval = await page
        .locator("div")
        .filter({
          hasText: "Approval requested successfully.",
        })
        .nth(3);
      await popUpApproval.waitFor({ state: "visible" });
    });

    test("Reject a request", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", { waitUntil: "networkidle" });
      await page.waitForTimeout(2000);
      await page
        .getByRole("gridcell", { name: "RU <NAME_EMAIL>" })
        .click();
      await page.getByRole("button", { name: "Reject" }).last().click();
      await page.getByPlaceholder("Add your comments").click();
      await page
        .getByPlaceholder("Add your comments")
        .fill("rejected by playwright");
      await page.getByRole("button", { name: "Reject Request" }).click();
      const rejectText = await page
        .locator("div")
        .filter({
          hasText: "You've rejected the request!",
        })
        .nth(3);
      await rejectText.waitFor({ state: "visible", timeout: 5000 });
    });

    test("Negative workflow", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Approval Workflows" }).click();
      await page.getByRole("button", { name: "Create Workflow" }).click();
      await page
        .getByRole("dialog", { name: "New Workflow" })
        .getByRole("textbox")
        .first()
        .click();
      await page
        .getByRole("dialog", { name: "New Workflow" })
        .getByRole("textbox")
        .first()
        .press("CapsLock");
      await page
        .getByRole("dialog", { name: "New Workflow" })
        .getByRole("textbox")
        .first()
        .fill("Negative workflow");
      await page.getByRole("button", { name: "Save Workflow" }).click();
      const popUpNeg = await page
        .locator("div")
        .filter({
          hasText: "Please fill all mandatory fields.",
        })
        .nth(3);
      await popUpNeg.waitFor({ state: "visible" });
      await page.getByRole("button", { name: "Cancel" }).click();
    });

    test("Raise Approval when statement is unlocked", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJsYWtzaG1pQGV2ZXJzdGFnZS5jb20ifQ==",
        { waitUntil: "networkidle" }
      );
      await page.getByText("June 2023", { exact: true }).first().click();
      await page.getByTitle("March 2023").locator("div").first().click();
      await page.locator("button.ant-dropdown-trigger").last().click();
      await page.getByText("Request Approval").click();
      const unlockPopup = await page.getByText(
        "You cannot request approval when"
      );
      await unlockPopup.waitFor({ state: "visible" });
      await page.getByRole("button", { name: "OK" }).click();
    });

    test("Navigate statement from approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", { waitUntil: "networkidle" });
      await page.reload();
      const [newPage] = await Promise.all([
        page.context().waitForEvent("page"),
        page.locator("//a[text()='Lakshmi Anantharamakrishnan']").click(),
      ]);
      await newPage.waitForLoadState("networkidle");
      await newPage.waitForFunction(
        (expectedTitle) => document.title === expectedTitle,
        "Lakshmi Anantharamakrishnan - Jun 2023 - Statements - Everstage"
      );
      const pageTitle = await newPage.title();
      expect(pageTitle).toBe(
        "Lakshmi Anantharamakrishnan - Jun 2023 - Statements - Everstage"
      );
      await expect(
        await newPage.getByRole("button", { name: "Approve" })
      ).toBeVisible();
      await expect(
        await newPage.getByRole("button", { name: "Reject" })
      ).toBeVisible();
      await expect(
        await newPage.getByText("Lakshmi Anantharamakrishnan")
      ).toBeVisible();
      await newPage.close();
      await page.bringToFront();
    });

    test("Create a workflow without module name", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Approval Workflows" }).click();
      await page.getByRole("button", { name: "Create Workflow" }).click();
      await page.getByPlaceholder("Enter workflow name").click();
      await page.getByPlaceholder("Enter workflow name").press("CapsLock");
      await page.getByPlaceholder("Enter workflow name").fill("Test1234567");
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByText("Payee", { exact: true }).click();
      await page.getByRole("button", { name: "Save Workflow" }).click();
      await page
        .getByText("Please fill all mandatory fields.")
        .first()
        .isVisible();
      await page.getByLabel("Close", { exact: true }).click();
    });

    test("Verify whether Reject Reason is displayed", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11171" },
        {
          type: "Description",
          description:
            "Verify whether the Reject reason is displyed along with updated by details",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The Reject reason must be displayed along with updated by details",
        }
      );
      const page = adminPage.page;
      await page.goto("/commissions", { waitUntil: "networkidle" });
      const approvalPage = new ApprovalPage(adminPage.page);

      await approvalPage.setPayout("June-2023");
      await approvalPage.verifyPayoutColumnValues("<EMAIL>");
      await approvalPage.searchUserEmail("<EMAIL>");
      await expect(
        page.getByRole("gridcell", { name: "Rejected by ES" })
      ).toBeVisible();
      await page
        .getByRole("gridcell", { name: "Rejected by ES" })
        .locator("svg")
        .nth(1)
        .click();
      await expect(
        page
          .getByText(
            "everstage support rejected Reject User’s June 2023 payout.rejected by playwright"
          )
          .first()
      ).toBeVisible();
      await page.getByRole("button", { name: "Close" }).click();
    });

    test.skip("Approve from a user and verify status of all reviewers (Everyone Workflow)", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11724" },
        {
          type: "Description",
          description:
            "Verify the status of all reviewers (Everyone Workflow) after one user approves ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The status and Review card message must be displayed properly",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(adminPage.page);
      const revertExitPage = new Revertexitpage(adminPage.page);

      //Approve from Rijo User
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserInPendingRequestsTab(
        "<EMAIL>",
        "February 2025"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.approveAsPayee();
      await revertExitPage.exitImpersonation();

      //verify the status of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "February 2025",
        "Approved"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "You approved Ishan Kishan's payout for the period Feb 2025."
      );
      await expect(
        page.getByRole("button", { name: "Draw adjustments" })
      ).toBeHidden();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Earned commission$/ })
          .first()
      ).toBeHidden();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Deferred commission$/ })
          .first()
      ).toBeHidden();
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "February 2025",
        "Pending"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "Waiting for your approval [Due by Feb 05, 2025]"
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "February 2025",
        "Pending"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "Waiting for your approval [Due by Feb 05, 2025]"
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();
    });

    test.skip("Verify the status of approvers after one reviewer rejected a payout", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11724" },
        {
          type: "Description",
          description:
            "Verify the status & message of approvers after one reviewer rejected a payout",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The status and Review card message must be displayed properly",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(adminPage.page);
      const revertExitPage = new Revertexitpage(adminPage.page);

      //Reject the request
      await revertExitPage.gotoUsersPage();
      await revertExitPage.searchUser("<EMAIL>");
      await revertExitPage.impersonateUser();
      await approvalPage.payouts();
      await approvalPage.verifyApprovalBannerToBe("Visible");
      await expect(
        page.getByRole("columnheader", { name: "Response" })
      ).toBeVisible();
      await approvalPage.searchUserEmailInPayouts("<EMAIL>");
      await approvalPage.rejectAction();
      await revertExitPage.exitImpersonation();
      await approvalPage.delay(3000);

      //verify the status and message of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "September 2024",
        "Rejected"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "You rejected Klassen H's payout for the period Sep 2024."
      );
      await approvalPage.closeReviewCard();
      await approvalPage.verifyApprovalBannerToBe("Hidden");
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "September 2024",
        "Withdrawn"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "We have withdrawn Klassen H's payout approval request for Sep 2024, after Rijo rejected it."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "September 2024",
        "Withdrawn"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "We have withdrawn Klassen H's payout approval request for Sep 2024, after Rijo rejected it."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();
    });

    test.skip("Verify the status of approvers after admin revoked a payout in Requested status", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11724" },
        {
          type: "Description",
          description:
            "Verify the status of approvers after admin revoked a payout in Requested status",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The status and Review card message must be displayed properly",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(adminPage.page);
      const revertExitPage = new Revertexitpage(adminPage.page);

      await page.goto("/commissions", { waitUntil: "networkidle" });

      //Revoke the Payout request
      await approvalPage.setPayout("October-2024");
      await approvalPage.revokeApprovalByEmail("<EMAIL>");
      await expect(
        page.getByRole("gridcell", { name: "Revoked by ES" })
      ).toBeVisible();

      //verify the status and message of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "October 2024",
        "Revoked"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage revoked Tristan Stubbs's payout for the period Oct 2024."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "October 2024",
        "Revoked"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage revoked Tristan Stubbs's payout for the period Oct 2024."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "October 2024",
        "Revoked"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage revoked Tristan Stubbs's payout for the period Oct 2024."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();
    });

    test.skip("Verify the status of approvers after admin unlocks a statement(Anyone & Everyone Workflow) which is InProgress", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11724" },
        {
          type: "Description",
          description:
            "Verify the status of approvers after admin unlocks a statement(Anyone & Everyone Workflow) which is InProgress",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The status and Review card message must be displayed accordingly",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(adminPage.page);
      const revertExitPage = new Revertexitpage(adminPage.page);
      await page.goto("/commissions", { waitUntil: "networkidle" });

      //Unlock the Statement before anyone approves
      await approvalPage.setPayout("November-2024");
      await approvalPage.unlockStatements("<EMAIL>");
      await expect(
        page.getByRole("gridcell", { name: "Auto cancelled" })
      ).toBeVisible();

      //verify the status and message of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "We have cancelled Raghul D's payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "We have cancelled Raghul D's payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "We have cancelled your payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      //lock the Statement & request approval(Anyone)
      await page.goto("/commissions", { waitUntil: "networkidle" });
      await approvalPage.setPayout("November-2024");
      await approvalPage.lockStatements("<EMAIL>");
      await approvalPage.requestApprovalByEmail(
        "<EMAIL>",
        "Status validation"
      );
      await expect(
        page.getByRole("gridcell", { name: "Requested" })
      ).toBeVisible();

      //Approve from one user
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserEmailInPayouts("<EMAIL>");
      await approvalPage.approveAsPayee();
      await revertExitPage.exitImpersonation();

      //unlock the statement again
      await approvalPage.setPayout("November-2024");
      await approvalPage.unlockStatements("<EMAIL>");
      await expect(
        page.getByRole("gridcell", { name: "Auto cancelled" })
      ).toBeVisible();

      //verify the status and message of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.sortByReponse("ASC");
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "We have cancelled Raghul D's payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Withdrawn"
      );
      await approvalPage.sortByReponse("ASC");
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.isTextVisible(
        "We have withdrawn Raghul D's payout approval request for Nov 2024, after Rijo approved it."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Withdrawn"
      );
      await approvalPage.sortByReponse("ASC");
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.isTextVisible(
        "We have withdrawn your payout approval request for Nov 2024, after Rijo approved it."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      /* Verify status when statements are unlocked after 1 user approved, which has Everyone should approve worflow */

      //lock the Statement & request approval(Everyone)
      await page.goto("/commissions", { waitUntil: "networkidle" });
      await approvalPage.setPayout("November-2024");
      await approvalPage.lockStatements("<EMAIL>");
      await approvalPage.requestApprovalByEmail(
        "<EMAIL>",
        "Everyone workflow"
      );
      await expect(
        page.getByRole("gridcell", { name: "Requested" })
      ).toBeVisible();

      //Approve from one user
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserEmailInPayouts("<EMAIL>");
      await approvalPage.approveAsPayee();
      await revertExitPage.exitImpersonation();

      //unlock the statement again
      await approvalPage.setPayout("November-2024");
      await approvalPage.unlockStatements("<EMAIL>");
      await expect(
        page.getByRole("gridcell", { name: "Auto cancelled" })
      ).toBeVisible();

      //verify the status and message of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.sortByReponse("ASC");
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.isTextVisible(
        "We have cancelled Raghul D's payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.sortByReponse("ASC");
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.isTextVisible(
        "We have cancelled Raghul D's payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "November 2024",
        "Auto cancelled"
      );
      await approvalPage.sortByReponse("ASC");
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.isTextVisible(
        "We have cancelled your payout approval request for Nov 2024. For further details, contact your admin."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();
    });

    test.skip("Verify the status of approvers after admin withdraws Pending requests", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11724" },
        {
          type: "Description",
          description:
            "Verify the status of approvers after admin withdraws Pending requests",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The status and Review card message must be displayed accordingly",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(adminPage.page);
      const revertExitPage = new Revertexitpage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 100000);

      //verify the status and message of all reviewers
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Withdrawn"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage has withdrawn Ashish M's payout for the period Dec 2024."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Withdrawn"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage has withdrawn your payout for the period Dec 2024."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Pending"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible("Waiting for your approval");
      await approvalPage.isTextVisible(
        "Hey Stage 2 Approvers, kindly verify the payout"
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      //Unlock the Statement after 1 reviewer approves
      await page.goto("/commissions", { waitUntil: "networkidle" });
      await approvalPage.setPayout("December-2024");
      await approvalPage.unlockStatements("<EMAIL>");
      await approvalPage.lockStatements("<EMAIL>");
      await approvalPage.requestApprovalByEmail(
        "<EMAIL>",
        "Everyone workflow"
      );
      await expect(
        page.getByRole("gridcell", { name: "Requested" })
      ).toBeVisible();

      //Approve from one user
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserEmailInPayouts("<EMAIL>");
      await approvalPage.approveAsPayee();
      await revertExitPage.exitImpersonation();

      //Withdraw the request from Admin
      await approvalPage.statementsNav(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJhc2hpc2hAZXZlcnN0YWdlLmNvbSIsInBzZCI6IjIwMjQtMTItMDEiLCJwZWQiOiIyMDI0LTEyLTMxIn0="
      );
      await approvalPage.approvalsTab();
      await approvalPage.actionButtonClick(
        ".flex > div > .flex > .ant-dropdown-trigger"
      );
      await approvalPage.actionsButton("Withdraw pending requests");
      await approvalPage.popupButton("Withdraw Request");
      await approvalPage.loaderpopUp("Withdrawing requests...");
      await approvalPage.banner("Request withdrawn successfully.");

      //verify the status and message of all reviewers after one user approving and then admin withdraws
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Approved"
      );
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.delay(500);
      await approvalPage.isTextVisible(
        "You approved Ashish M's payout for the period Dec 2024."
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Withdrawn"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage has withdrawn your payout for the period Dec 2024."
      );
      await approvalPage.isTextVisible(
        "Go to your statements for detailed Commission insights"
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Withdrawn"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible(
        "everstage has withdrawn Ashish M's payout for the period Dec 2024."
      );
      await expect(
        page.getByText("Quick insights", { exact: true })
      ).toBeHidden();
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.verifyApprovalResponse(
        "<EMAIL>",
        "December 2024",
        "Pending"
      );
      await page
        .locator('span[role="presentation"]>div>button>div>svg')
        .nth(1)
        .click();
      await approvalPage.isTextVisible("Waiting for your approval");
      await approvalPage.isTextVisible(
        "Hey Stage 2 Approvers, kindly verify the payout"
      );
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();
    });

    test.skip("Review Card verification", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11724" },
        {
          type: "Description",
          description: "Verify the elements in Review Card",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The expected elements must be displayed accordingly",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(adminPage.page);
      const revertExitPage = new Revertexitpage(adminPage.page);

      //Verify the Earned, Deferred Commissions, Draw & Commission Adjustments
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserInPendingRequestsTab(
        "<EMAIL>",
        "July 2024"
      );
      await approvalPage.reviewCardIcon();

      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Payout amountAL\$1,601,000\.00$/ })
          .nth(1)
      ).toBeVisible();
      await expect(page.locator(".ant-collapse-content-box")).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Quota attainment" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Primary" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "18,000.00" })
      ).toBeVisible();
      await expect(page.getByRole("gridcell", { name: "36%" })).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Earned commissionAL\$468,000\.00$/ })
          .first()
      ).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Deferred commissionAL\$0\.00$/ })
          .first()
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Draw adjustments - AL$2,000.00" })
      ).toBeVisible();
      await page
        .getByRole("button", { name: "Draw adjustments - AL$" })
        .click();
      await expect(
        page
          .getByLabel("Quick insights")
          .getByRole("gridcell", { name: "AL$0.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "AL$2,000.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Commission adjustments" })
      ).toBeVisible();
      await page
        .getByRole("button", { name: "Commission adjustments" })
        .click();
      await expect(
        page.getByRole("gridcell", { name: "AL$2,500.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Playwright Commission" })
      ).toBeVisible();
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserInPendingRequestsTab(
        "<EMAIL>",
        "August 2024"
      );
      await approvalPage.reviewCardIcon();
      await approvalPage.isTextVisible("Approval Summary");
      await approvalPage.isTextVisible(
        "Get quick insights into Payout and Quota to guide your approval."
      );
      await approvalPage.isTextVisible("PayeeZehra V");
      await approvalPage.isTextVisible("PeriodAug");
      await expect(page.getByText("Payout amount$").first()).toBeVisible();
      await approvalPage.isTextVisible(
        "Waiting for your approval [Due by Feb 04, 2025]Hey Approvers, kindly verify the"
      );
      await expect(
        page.getByText("Quick insights", { exact: true })
      ).toBeVisible();
      await approvalPage.isTextVisible("Approval timeline");
      await expect(
        page.getByRole("button", { name: "Approve" }).nth(2)
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Reject" }).nth(2)
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "View Statement" })
      ).toBeVisible();

      //Verify View Statement link is working
      const [newTab1] = await Promise.all([
        page.context().waitForEvent("page"),
        page.getByRole("link", { name: "View Statement" }).click(),
      ]);
      await newTab1.waitForLoadState("networkidle");
      await page.getByText("Statements").waitFor({ state: "visible" });
      await approvalPage.delay(7000);

      // Verify text in the new tab
      const pageTitle = await newTab1.title();
      expect(pageTitle).toBe("Zehra V - Aug 2024 - Statements - Everstage");
      await expect(
        newTab1.getByText("Waiting for your approval")
      ).toBeVisible();

      await newTab1.close();
      await page.bringToFront();
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();

      //login as Payee and verify
      await approvalPage.navigateToUsersAndImpersonate("<EMAIL>");
      await approvalPage.payouts();
      await approvalPage.searchUserInPendingRequestsTab(
        "<EMAIL>",
        "August 2024"
      );
      await expect(page.getByRole("gridcell", { name: "$NaN" })).toHaveCount(3);
      await approvalPage.reviewCardIcon();
      await expect(
        page.getByText(
          "Oops! You don’t have permission to access this content."
        )
      ).toBeVisible();
      await expect(
        page.getByText(
          "Looks like you can’t access it right now. Try reaching out to your admin for help!"
        )
      ).toBeVisible();
      await expect(page.getByText("Payout amount$NaN")).toBeVisible();
      await approvalPage.verifyRejectReasonCharacterlimit();

      //Verify Statement details are not shown for Payee role
      const [newTab2] = await Promise.all([
        page.context().waitForEvent("page"),
        page.getByRole("link", { name: "View Statement" }).click(),
      ]);
      await newTab2.waitForLoadState("domcontentloaded");

      // Verify text in the new tab
      await expect(
        newTab2.getByText("Sorry, you’re not authorized to access this page.")
      ).toBeVisible();

      await newTab2.close();
      await page.bringToFront();
      await approvalPage.closeReviewCard();
      await revertExitPage.exitImpersonation();
    });
  }
);
