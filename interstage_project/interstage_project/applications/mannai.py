import os
from typing import Dict, <PERSON><PERSON>

import jwt
from django.http import HttpRequest
from django.urls import include, path
from rest_framework.exceptions import AuthenticationFailed, NotFound

from commission_engine.accessors.client_accessor import get_client
from interstage_project.auth_utils import get_token_auth_header
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor

from .desktop_web import DesktopWeb
from .generic import BaseApplication
from .generic.utils import is_request_path_matching_with_urlpatterns


class Mannai(BaseApplication):
    """
    This class represents an application for handling mannai requests
    """

    urlpatterns = [
        path("mannai/", include(DesktopWeb.urlpatterns)),
    ]

    @classmethod
    def identify_request(cls, request: HttpRequest) -> bool:
        """
        Identifies if the request is a Mannai request.

        Args:
            request: The HTTP request object.

        Returns:
            bool: True if the request is a Mannai request, False otherwise.
        """
        return is_request_path_matching_with_urlpatterns(request.path, cls.urlpatterns)

    def authenticate(self) -> <PERSON><PERSON>[str, str, Dict]:
        """
        Authenticates the user and returns the user email, raw token and decoded token.

        Returns:
            Tuple[str, str, Dict]: The user email, raw token and decoded_token.
        """
        # extract token
        token = get_token_auth_header(self.request)
        payload = {}
        mannai_jwt_secret = os.getenv("MANNAI_JWT_SECRET")
        mannai_jwt_issuer = os.getenv("MANNAI_JWT_ISSUER")
        mannai_jwt_audience = os.getenv("MANNAI_JWT_AUDIENCE")
        payload = jwt.decode(
            token,
            mannai_jwt_secret,
            issuer=mannai_jwt_issuer,
            audience=mannai_jwt_audience,
            algorithms=["HS256"],
            leeway=20,
            options={"require": ["exp", "iss", "iat", "aud"]},
        )
        if not payload:
            raise AuthenticationFailed("Invalid access token")

        user_email = payload.get("email")
        if not user_email:
            raise NotFound("User email not found")  # noqa: TRY003
        employees = EmployeeAccessor.get_employees_with_same_email(user_email)
        if not employees.exists():
            raise NotFound(f"Employee {user_email} not found")  # noqa: TRY003

        return user_email, token, payload

    def get_client_claims(self) -> Tuple[int, str]:
        """
        Retrieves the client claims for the Superset request.

        Returns:
            Tuple[int, str]: A Tuple containing the client ID and domain.
        """
        client_id = self.decoded_token.get("client_id")
        if not client_id:
            raise NotFound("Client ID not found")  # noqa: TRY003

        is_active_employee = EmployeeAccessor(client_id).does_employee_exist(self.email)

        if not is_active_employee:
            raise NotFound(f"{self.email} not found")  # noqa: TRY003

        client = get_client(client_id)
        return client.client_id, client.domain
