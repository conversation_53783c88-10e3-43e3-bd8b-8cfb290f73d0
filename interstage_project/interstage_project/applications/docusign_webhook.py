import base64
import hashlib
import hmac
import json
import logging
from typing import <PERSON>ple

from django.http import HttpRequest
from django.urls import path
from rest_framework.exceptions import AuthenticationFailed

from everstage_ddd.cpq.docusign.accessors.docusign_accessor import (
    DocusignAccountSettingsAccessor,
)
from everstage_ddd.cpq.docusign.docusign_webhook_api import api as docusign_webhook_api

from .generic import BaseApplication
from .generic.utils import is_request_path_matching_with_urlpatterns

logger = logging.getLogger(__name__)


class DocusignWebhook(BaseApplication):
    urlpatterns = [path("ninja-webhook/", docusign_webhook_api.urls)]  # type: ignore

    @classmethod
    def identify_request(cls, request: HttpRequest) -> bool:
        """
        Identifies if the request matches the URL patterns of the application.

        Args:
            request: The request object.

        Returns:
            bool: True if the request matches the URL patterns, False otherwise.

        """
        return is_request_path_matching_with_urlpatterns(request.path, cls.urlpatterns)

    def _extract_account_id(self, request: HttpRequest) -> str:
        """
        Extracts the docusign accountId from the incoming webhook request body.
        Raises:
            AuthenticationFailed: If 'accountId' is missing in the payload.
        Returns:
            str: The extracted accountId.
        """
        body_data = json.loads(request.body)
        data = body_data.get("data", {})
        account_id = data.get("accountId")
        if not account_id:
            message = "Missing accountId in DocuSign webhook payload"
            logger.warning("WEBHOOK : %s", message)
            raise AuthenticationFailed(message)
        return account_id

    def _get_webhook_secret(self, account_id: str) -> str:
        """
        Retrieves the webhook secret key associated with the given accountId.
        This secret is used to verify the authenticity of the webhook request.
        Raises:
            AuthenticationFailed: If the secret key is not found for the account.
        Returns:
            str: The webhook secret key.
        """
        webhook_secret = DocusignAccountSettingsAccessor().get_secret_key_by_account_id(
            account_id
        )
        logger.info("WEBHOOK: Verifying secret...")
        if not webhook_secret:
            message = "Missing Webhook Secret in task definition"
            raise AuthenticationFailed(message)
        return webhook_secret

    def _get_signature_headers(self, request: HttpRequest):
        """
        Collects all DocuSign signature headers from the request.
        DocuSign sends one or more headers like:
            HTTP_X_DOCUSIGN_SIGNATURE_1, HTTP_X_DOCUSIGN_SIGNATURE_2, etc.
        The headers are sorted in descending order by their number.
        Raises:
            AuthenticationFailed: If no signature headers are found.
        Returns:
            list: List of (header_name, header_value) tuples.
        """
        signature_headers = [
            (k, v)
            for k, v in request.META.items()
            if k.startswith("HTTP_X_DOCUSIGN_SIGNATURE_") and k.split("_")[-1].isdigit()
        ]
        signature_headers.sort(key=lambda x: int(x[0].split("_")[-1]), reverse=True)
        if not signature_headers:
            message = "Missing signature in request"
            raise AuthenticationFailed(message)
        return signature_headers

    def _verify_signatures(self, signature_headers, expected_signature: str) -> str:
        """
        Compares each provided signature header value with the expected signature.
        If any header matches, authentication is successful.
        Raises:
            AuthenticationFailed: If none of the signatures match.
        Returns:
            str: The matching token signature.
        """
        for _, token_signature in signature_headers:
            if hmac.compare_digest(token_signature, expected_signature):
                logger.info("WEBHOOK: Request Authenticated!!")
                return token_signature
        message = "Invalid Secret"
        logger.info("WEBHOOK: Invalid secret")
        raise AuthenticationFailed(message)

    def authenticate(self) -> Tuple[str, str, str]:
        """
        Authenticates the incoming webhook request by verifying the signature against a secret key.

        Returns:
            A tuple containing empty string, the token signature, and the empty string.

        Raises:
            AuthenticationFailed: If the token signature is missing, the webhook secret is missing, or the token signature does not match the expected signature.
        """
        request = self.request
        account_id = self._extract_account_id(request)
        signature_headers = self._get_signature_headers(request)
        webhook_secret = self._get_webhook_secret(account_id)

        # Calculates the expected signature for the request body using the webhook secret.
        digest = hmac.new(
            webhook_secret.encode(), request.body, hashlib.sha256
        ).digest()
        expected_signature = base64.b64encode(digest).decode()

        token_signature = self._verify_signatures(signature_headers, expected_signature)

        # Returns on successfull authentication
        return ("", token_signature, "")
