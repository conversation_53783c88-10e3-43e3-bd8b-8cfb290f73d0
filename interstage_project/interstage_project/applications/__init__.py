"""
This module contains the list of applications used in the project.

Applications are divided into two lists: extended and primary.
The primary list contains the main applications of the project.
The extended list contains additional applications that can be added to the project.

The order of applications in the 'applications' list matters.
The DesktopWeb and StaticFiles applications should be kept at the end of the list.
"""

from .admin_ui import AdminUI
from .auth0_action import Auth0Action
from .chrome_extension import ChromeExtension
from .data_migrator import DataMigrator
from .dealroom import DealRoom
from .desktop_web import DesktopWeb
from .docusign_webhook import DocusignWebhook
from .estimator import Estimator
from .etl_infra_lambda import ETLInfraLambda
from .fivetran_webhook import FivetranWebhook
from .freshservice import FreshserviceForTsar
from .mannai import Mannai
from .mobile import Mobile
from .ms_teams import MsTeams
from .public_link import PublicLink
from .slack import Slack
from .spark import Spark
from .static_files import StaticFiles
from .superset import Superset
from .superset_worker import SupersetWorker

extended = [
    AdminUI,
    Auth0Action,
    ChromeExtension,
    Estimator,
    Mobile,
    MsTeams,
    Slack,
    Superset,
    SupersetWorker,
    Mannai,
    FivetranWebhook,
    DataMigrator,
    Spark,
    FreshserviceForTsar,
    ETLInfraLambda,
    DocusignWebhook,
    DealRoom,
    PublicLink,
    # NOTE: Add new applications in this list
]

primary = [
    # NOTE: Do NOT add any new application in this list. Do NOT change the order of apps below.
    DesktopWeb,
    StaticFiles,
]

# Order of applications matters. Keep DesktopWeb and StaticFiles at the end
applications = extended + primary
