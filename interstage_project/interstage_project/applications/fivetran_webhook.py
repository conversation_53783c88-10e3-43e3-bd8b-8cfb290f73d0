import hashlib
import hmac
import logging
import os
from typing import <PERSON>ple

from django.http import HttpRequest
from django.urls import include, path
from rest_framework.exceptions import AuthenticationFailed

from .generic import BaseApplication
from .generic.utils import is_request_path_matching_with_urlpatterns

logger = logging.getLogger(__name__)


class FivetranWebhook(BaseApplication):
    """
    Estimator application class.

    This class represents the Estimator application in the Everstage project.
    It provides methods for authentication, request identification, and retrieving user details.

    Attributes:
        urlpatterns (list): List of URL patterns for the Estimator application.

    """

    urlpatterns = [
        path(
            "fivetran-webhook/", include("everstage_ddd.upstream.fivetran_webhook.urls")
        ),
    ]

    @classmethod
    def identify_request(cls, request: HttpRequest) -> bool:
        """
        Identifies if the request matches the URL patterns of the application.

        Args:
            request: The request object.

        Returns:
            bool: True if the request matches the URL patterns, False otherwise.

        """
        return is_request_path_matching_with_urlpatterns(request.path, cls.urlpatterns)

    def authenticate(self) -> <PERSON>ple[str, str, None]:
        """
        Authenticates the incoming webhook request by verifying the signature against a secret token.

        Returns:
            A tuple containing empty string, the token signature, and None.

        Raises:
            AuthenticationFailed: If the token signature is missing, the webhook secret is missing, or the token signature does not match the expected signature.
        """
        request = self.request

        # Extract the token from the request headers
        token_signature = request.META.get("HTTP_X_FIVETRAN_SIGNATURE_256", "").upper()
        webhook_secret = os.environ.get("FIVETRAN_WEBHOOK_SECRET")

        """
        Check active webhook connection and verify which client the request is coming for.
        add client_id context 
        """

        # Verify the webhook secret
        logger.info("WEBHOOK: Verifying secret...")
        if not token_signature:
            # Raise an exception if the token signature is missing
            raise AuthenticationFailed("WEBHOOK: Missing Secret in request")
        if not webhook_secret:
            # Raise an exception if the webhook secret is missing
            raise AuthenticationFailed(
                "WEBHOOK: Missing Webhook Secret in task definition"
            )

        # Calculate the expected signature using the webhook secret and the request body
        expected_signature = (
            hmac.new(webhook_secret.encode(), request.body, hashlib.sha256)
            .hexdigest()
            .upper()
        )
        if token_signature != expected_signature:
            # Raise an exception if the token signature does not match the expected signature
            logger.info("WEBHOOK: Invalid signature received")
            raise AuthenticationFailed("Invalid Secret")
        logger.info("WEBHOOK: Request Authenticated!!")

        # Set required attributes to instance
        return ("", token_signature, None)
