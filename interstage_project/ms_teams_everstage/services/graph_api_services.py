import functools
import json
import logging
import os

import requests
from requests import JSONDecodeError, Response

from commission_engine.utils.s3_utils import S3Uploader
from everstage_infra.aws_infra.ecs import is_prod_env
from ms_teams_everstage.accessors.msteams_admin_token_details_accessor import (
    MsteamsAdminTokenDetailsAccessor,
)
from ms_teams_everstage.services.custom_exceptions import AppNotFoundError

MSTEAMS_LOGIN_URL = "https://login.microsoftonline.com/{}/oauth2/v2.0/token"
GRAPH_API_URL = "https://graph.microsoft.com/v1.0/{}"

# Maximum number of items that can be requested in a single page from Microsoft Graph API
GRAPH_API_MAX_PAGE_SIZE = 999

logger = logging.getLogger(__name__)

if is_prod_env():
    S3_BUCKET_NAME = "everstage-msteams-prod"
else:
    S3_BUCKET_NAME = "everstage-msteams"


def handle_graph_api_errors(raise_exception: bool = True):
    """
    Decorator to handle graph API errors.

    Args:
        raise_exception: Whether to raise an exception when an error occurs.

    Returns:
        A decorator function that wraps the input function and handles graph API errors.
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except requests.HTTPError as e:
                status_code = e.response.status_code if e.response else "unknown"
                logger.exception(
                    "APP_INT_EXCEPTION: HTTP error in %s - Status: %s",
                    func.__name__,
                    str(status_code),
                )
                if raise_exception:
                    raise
            except requests.RequestException:
                logger.exception(
                    "APP_INT_EXCEPTION: Network error in %s", func.__name__
                )
                if raise_exception:
                    raise
            except Exception:
                logger.exception(
                    "APP_INT_EXCEPTION: Unexpected error in %s", func.__name__
                )
                if raise_exception:
                    raise

        return wrapper

    return decorator


def get_response_data(response: Response) -> dict | str:
    """
    Safely extract data from an HTTP response.

    Args:
        response: HTTP response object from requests library

    Returns:
        Dictionary if response can be parsed as JSON, string content otherwise,
        or empty string if neither is available
    """
    try:
        return response.json()
    except JSONDecodeError:
        # JSON parsing failed, try to get text content
        try:
            return response.text
        except AttributeError:
            logger.warning("Could not extract data from response")
            return ""


def get_response_data_wrapper(response: Response) -> dict | str:
    """Wrapper around 'get_response_data' that adds a generic exception
    handler so callers don't have to remember to handle unexpected parsing
    issues themselves.

    Args:
        response (requests.Response): HTTP response object.

    Returns:
        dict | str: Parsed response data using 'get_response_data' or empty string if parsing fails.
    """
    try:
        return get_response_data(response)
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Failed to extract response data %s",
            str(e),
        )
        return ""


def get_admin_access_token(code, login_email_id, redirect_uri="settings/notifications"):
    try:
        url = MSTEAMS_LOGIN_URL.format("common")
        payload = f"client_id={os.getenv('MSTEAMS_CLIENT_ID')}&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&client_secret={os.getenv('MSTEAMS_CLIENT_SECRET')}&grant_type=authorization_code&code={code}&redirect_uri={os.getenv('URL')}/{redirect_uri}"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(url, headers=headers, data=payload)
        return response.json().get("access_token", None), response.json().get(
            "refresh_token", None
        )
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when ms teams admin access token for %s %s",
            login_email_id,
            e,
        )
        raise Exception from e


def get_admin_access_token_from_refresh_token(
    client_id,
) -> tuple[str | None, str | None]:
    try:
        token_details = MsteamsAdminTokenDetailsAccessor(client_id).get()
        if not token_details:
            logger.error(
                "APP_INT_EXCEPTION: Token not found for client %s, Skipping ms teams install for new users",
                client_id,
            )
            raise Exception(
                f"Token not found for client {client_id}, Skipping ms teams install for new users"
            )
        tenant_id = token_details.tenant_id
        refresh_token = token_details.admin_refresh_token

        url = MSTEAMS_LOGIN_URL.format(tenant_id)
        payload = f"client_id={os.getenv('MSTEAMS_CLIENT_ID')}&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&client_secret={os.getenv('MSTEAMS_CLIENT_SECRET')}&grant_type=refresh_token&refresh_token={refresh_token}"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(url, headers=headers, data=payload)
        logger.info(
            "[MS_TEAMS_INSTALL] Admin access token response: %s",
            response.json(),
        )
        return response.json().get("access_token", None), response.json().get(
            "refresh_token", None
        )
    except Exception as e:  # pylint: disable=broad-except
        logger.error(
            "APP_INT_EXCEPTION: Error when ms refreshing admin access token for %s",
            e,
        )
        return None, None


def get_tenant_access_token(tenant_id):
    try:
        url = MSTEAMS_LOGIN_URL.format(tenant_id)
        payload = f"client_id={os.getenv('MSTEAMS_CLIENT_ID')}&scope=https://graph.microsoft.com/.default&client_secret={os.getenv('MSTEAMS_CLIENT_SECRET')}&grant_type=client_credentials"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(url, headers=headers, data=payload)
        logger.info(
            "[MS_TEAMS_INSTALL] Tenant access token response: %s",
            response.json(),
        )
        if response.status_code == 200:
            return response.json().get("access_token", None)
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when ms teams tenant access token for %s %s",
            tenant_id,
            e,
        )
        raise Exception from e


def publish_app_to_catalog(access_token):
    try:
        url = GRAPH_API_URL.format("appCatalogs/teamsApps?requiresReview=false")
        app_package_file = f"{os.getenv('MSTEAMS_APP_EXTERNAL_ID')}.zip"
        S3Uploader(logger=logger).download_file(
            app_package_file, app_package_file, S3_BUCKET_NAME
        )
        headers = {
            "Content-Type": "application/zip",
            "Authorization": f"Bearer {access_token}",
        }
        app_package = open(app_package_file, "rb").read()
        response = requests.post(url, headers=headers, data=app_package)
        if response.status_code in [200, 201, 409]:
            return response
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error("APP_INT_EXCEPTION: Error when publishing teams app %s", e)
        raise Exception from e


def update_published_app_to_catalog(access_token, app_catalog_id):
    try:
        url = GRAPH_API_URL.format(
            f"appCatalogs/teamsApps/{app_catalog_id}/appDefinitions?requiresReview=false"
        )
        app_package_file = f"{os.getenv('MSTEAMS_APP_EXTERNAL_ID')}.zip"
        S3Uploader(logger=logger).download_file(
            app_package_file, app_package_file, S3_BUCKET_NAME
        )
        headers = {
            "Content-Type": "application/zip",
            "Authorization": f"Bearer {access_token}",
        }
        app_package = open(app_package_file, "rb").read()
        response = requests.post(url, headers=headers, data=app_package)
        if response.status_code in [200, 201, 409]:
            return response
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when updating publishing teams app %s",
            e,
        )
        raise Exception from e


def update_app_installed_for_user(access_token, user_id, app_catalog_id):
    try:
        url = GRAPH_API_URL.format(
            f"users/{user_id}/teamwork/installedApps?$expand=teamsApp&$filter=teamsApp/id eq '{app_catalog_id}'"
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            if response.json().get("value"):
                app_installation_id = response.json().get("value").pop()["id"]
                url = GRAPH_API_URL.format(
                    f"users/{user_id}/teamwork/installedApps/{app_installation_id}/upgrade"
                )
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                response = requests.post(url, headers=headers)
                if response.status_code in [200, 201, 409, 204]:
                    response_data = get_response_data_wrapper(response)
                    logger.info(
                        "[MS_TEAMS_INSTALL] App installed for user %s : %s - %s",
                        user_id,
                        response.status_code,
                        response_data,
                    )
                    return response
                else:
                    raise Exception(response.json())
            else:
                return install_app_for_user(access_token, user_id, app_catalog_id)
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error("APP_INT_EXCEPTION: Error when publishing teams app %s", e)
        raise Exception from e


def get_app_details(access_token):
    try:
        url = GRAPH_API_URL.format(
            f"/appCatalogs/teamsApps?$filter=externalId eq '{os.getenv('MSTEAMS_APP_EXTERNAL_ID')}'&$expand=appDefinitions"
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            raise Exception(f"Failed to get app details: {response.json()}")

        response_data = response.json()
        logger.info("[MS_TEAMS_INSTALL] App details response: %s", response_data)
        if not response_data.get("value"):
            logger.error(
                "[MS_TEAMS_INSTALL] No app found with external ID",
            )
            raise AppNotFoundError("No app found with external ID")

        app_details = response_data.get("value").pop()
        return app_details
    except Exception as e:
        logger.exception("APP_INT_EXCEPTION: Error when getting app details %s", e)
        raise


def get_users_count(access_token: str) -> int | None:
    """
    Get the total count of users in the organization.

    Args:
        access_token (str): The access token for Microsoft Graph API authentication

    Returns:
        int: The total number of users in the organization
        None: If there is an error in getting the users count or if the response is not a valid integer
    """
    try:
        url = GRAPH_API_URL.format("users/$count")
        headers = {
            "Authorization": f"Bearer {access_token}",
            "ConsistencyLevel": "eventual",  # Required for $count
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        # Validate that the response text is a valid integer
        if not response.text.isdigit():
            logger.error(
                "APP_INT_EXCEPTION: Invalid response format when getting users count. Expected integer, got: %s",
                response.text,
            )
            return None

        return int(response.text)
    except requests.HTTPError as e:
        status_code = e.response.status_code if e.response else "unknown"
        logger.error(
            "APP_INT_EXCEPTION: HTTP error %s when getting users count: %s",
            status_code,
            str(e),
        )
        return None
    except requests.RequestException as e:
        logger.error(
            "APP_INT_EXCEPTION: Network error when getting users count: %s", str(e)
        )
        return None
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Unexpected error when getting users count: %s", str(e)
        )
        return None


def get_all_users(access_token: str) -> list[dict]:
    """
    Fetch all users from Microsoft Graph API with pagination support and validation.

    Args:
        access_token (str): The access token for Microsoft Graph API authentication

    Returns:
        list[dict]: A list of user objects containing mail, id, and userPrincipalName

    Raises:
        requests.HTTPError: If the API request fails with an HTTP error
        requests.RequestException: If there's a network or connection error
        Exception: For other unexpected errors
    """
    all_users = []
    next_link = GRAPH_API_URL.format(
        f"users?$top={GRAPH_API_MAX_PAGE_SIZE}&$select=mail,id,userPrincipalName"
    )
    headers = {"Authorization": f"Bearer {access_token}"}

    try:
        # Get total count for validation
        total_users = get_users_count(access_token)

        if total_users is None:
            logger.warning(
                "[MS_TEAMS_INSTALL] Unable to determine total user count, proceeding without validation"
            )
        elif total_users == 0:
            logger.warning(
                "[MS_TEAMS_INSTALL] Organization has no users according to count endpoint"
            )
            return []
        else:
            logger.info(
                "[MS_TEAMS_INSTALL] Expected total users in organization: %d",
                total_users,
            )

        while next_link:
            response = requests.get(next_link, headers=headers, timeout=30)
            response.raise_for_status()

            data = response.json()
            users = data.get("value", [])
            all_users.extend(users)
            next_link = data.get("@odata.nextLink")

            if total_users is not None:
                logger.info(
                    "[MS_TEAMS_INSTALL] Fetched %d/%d users",
                    len(all_users),
                    total_users,
                )

        if total_users is not None and len(all_users) != total_users:
            logger.warning(
                "[MS_TEAMS_INSTALL] Incomplete data: Fetched %d/%d users",
                len(all_users),
                total_users,
            )

        if all_users:
            logger.info(
                "[MS_TEAMS_INSTALL] %d users fetched successfully.",
                len(all_users),
            )
        else:
            logger.warning(
                "[MS_TEAMS_INSTALL] No users were fetched from Microsoft Graph API"
            )

        return all_users

    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Failed to complete user fetch operation: %s", str(e)
        )
        raise


def get_single_user(access_token, email_id):
    try:
        url = GRAPH_API_URL.format(
            f"users?$select=mail,id,userPrincipalName&$filter=mail eq '{email_id}' "
            f"OR userPrincipalName eq '{email_id}'"
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers, timeout=50)
        if response.status_code == 200:
            return response.json().get("value")
        else:
            raise Exception(response.json())
    except Exception as error:
        logger.error("APP_INT_EXCEPTION: Error when getting app details %s", error)
        raise Exception from error


def install_app_for_user(access_token, msteams_user_id, app_catalog_id):
    part_url = f"/users/{msteams_user_id}/teamwork/installedApps"
    url = GRAPH_API_URL.format(part_url)
    payload = json.dumps(
        {
            "<EMAIL>": f"https://graph.microsoft.com/v1.0/appCatalogs/teamsApps/{app_catalog_id}"
        }
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    install_app_response = requests.post(url, headers=headers, data=payload)
    return install_app_response


def get_conversation_id(access_token, msteams_user_id, app_catalog_id):
    try:
        part_url = f"/users/{msteams_user_id}/teamwork/installedApps?$filter=teamsApp/id eq '{app_catalog_id}'"
        url = GRAPH_API_URL.format(part_url)
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            raise Exception(response.json())

        response_data = response.json()
        logger.info("[MS_TEAMS] Conversation id response: %s", response_data)

        if not response_data.get("value"):
            raise Exception("No conversation id found")
        return response_data.get("value").pop()["id"]
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when getting conversation id for %s details %s",
            msteams_user_id,
            e,
        )
        raise Exception from e


def get_chat_id(access_token, msteams_user_id, conversation_id):
    try:
        part_url = (
            f"/users/{msteams_user_id}/teamwork/installedApps/{conversation_id}/chat"
        )
        url = GRAPH_API_URL.format(part_url)
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            logger.info("[MS_TEAMS] Chat id response: %s", response.json())
            return response.json().get("id")
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when getting chat id for %s details %s",
            msteams_user_id,
            e,
        )
        raise Exception from e


@handle_graph_api_errors(raise_exception=False)
def get_user_data_from_access_token(access_token: str) -> dict | None:
    """Retrieves user data from Microsoft Graph API using an access token.

    Args:
        access_token: The access token for API authentication.

    Returns:
        A dictionary containing user data.
        None: If there is an error in getting the user data or if the response is not a valid dictionary
    """
    url = GRAPH_API_URL.format("me")
    headers = {"Authorization": f"Bearer {access_token}"}

    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    return response.json()
