"""
Unit tests for MS Teams Graph API services.
"""

import os
from unittest.mock import <PERSON><PERSON>ock, mock_open, patch

import pytest
import requests
from requests import JSONDecodeError

from ms_teams_everstage.services.custom_exceptions import AppNotFoundError
from ms_teams_everstage.services.graph_api_services import (
    get_admin_access_token,
    get_admin_access_token_from_refresh_token,
    get_all_users,
    get_app_details,
    get_chat_id,
    get_conversation_id,
    get_response_data_wrapper,
    get_single_user,
    get_tenant_access_token,
    get_user_data_from_access_token,
    get_users_count,
    handle_graph_api_errors,
    install_app_for_user,
    publish_app_to_catalog,
    update_app_installed_for_user,
    update_published_app_to_catalog,
)


@pytest.fixture
def mock_requests():
    """Mock requests for all tests."""
    with patch("requests.get") as mock_get, patch("requests.post") as mock_post:
        yield {"get": mock_get, "post": mock_post}


@pytest.mark.django_db
class TestGraphAPIServices:
    @pytest.mark.parametrize(
        "mock_json, side_effect, expected_access_token, expected_refresh_token, should_raise",
        [
            (
                {"access_token": "ACCESS123", "refresh_token": "REFRESH123"},
                None,
                "ACCESS123",
                "REFRESH123",
                False,
            ),
            (
                {"access_token": "ACCESS_ONLY"},
                None,
                "ACCESS_ONLY",
                None,
                False,
            ),
            (
                {},
                None,
                None,
                None,
                False,
            ),
            (
                None,
                Exception("Connection error"),
                None,
                None,
                True,
            ),
        ],
    )
    def test_get_admin_access_token(
        self,
        mock_requests,
        mock_json,
        side_effect,
        expected_access_token,
        expected_refresh_token,
        should_raise,
    ):
        if side_effect:
            mock_requests["post"].side_effect = side_effect
        else:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_json
            mock_requests["post"].return_value = mock_response

        if should_raise:
            with pytest.raises(Exception):
                get_admin_access_token("dummy_code", "<EMAIL>")
        else:
            access_token, refresh_token = get_admin_access_token(
                "dummy_code", "<EMAIL>"
            )
            assert access_token == expected_access_token
            assert refresh_token == expected_refresh_token

        mock_requests["post"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, client_id, mock_token_details, mock_response, expected_result",
        [
            (
                "success",
                "test_client",
                MagicMock(tenant_id="test_tenant", admin_refresh_token="test_refresh"),
                {"access_token": "test_token", "refresh_token": "new_refresh"},
                ("test_token", "new_refresh"),
            ),
            (
                "no_token_details",
                "test_client",
                None,
                None,
                (None, None),
            ),
            (
                "error_response",
                "test_client",
                MagicMock(tenant_id="test_tenant", admin_refresh_token="test_refresh"),
                {"error": "invalid_grant"},
                (None, None),
            ),
        ],
    )
    def test_get_admin_access_token_from_refresh_token(
        self,
        mock_requests,
        test_case,
        client_id,
        mock_token_details,
        mock_response,
        expected_result,
    ):
        """Test getting admin access token from refresh token with different scenarios."""
        with patch(
            "ms_teams_everstage.services.graph_api_services.MsteamsAdminTokenDetailsAccessor"
        ) as mock_accessor:
            mock_accessor.return_value.get.return_value = mock_token_details

            if mock_response:
                mock_response_obj = MagicMock()
                mock_response_obj.json.return_value = mock_response
                mock_response_obj.status_code = 200
                mock_requests["post"].return_value = mock_response_obj

            result = get_admin_access_token_from_refresh_token(client_id)
            assert result == expected_result

            if mock_token_details:
                mock_requests["post"].assert_called_once()
            else:
                mock_requests["post"].assert_not_called()

    @pytest.mark.parametrize(
        "test_case, tenant_id, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_tenant",
                {"access_token": "test_token"},
                "test_token",
                None,
            ),
            (
                "request_exception",
                "test_tenant",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_get_tenant_access_token(
        self,
        mock_requests,
        test_case,
        tenant_id,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test getting tenant access token with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.json.return_value = mock_response
            mock_response_obj.status_code = 200
            mock_requests["post"].return_value = mock_response_obj
        else:
            mock_requests["post"].side_effect = requests.RequestException(
                "Network error"
            )

        if expected_exception:
            with pytest.raises(expected_exception):
                get_tenant_access_token(tenant_id)
        else:
            result = get_tenant_access_token(tenant_id)
            assert result == expected_result

        mock_requests["post"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                {"value": [{"id": "test_id", "externalId": "test_external"}]},
                {"id": "test_id", "externalId": "test_external"},
                None,
            ),
            (
                "no_app_found",
                "test_token",
                {},
                None,
                AppNotFoundError,
            ),
            (
                "error_response",
                "test_token",
                {"error": "invalid_token"},
                None,
                AppNotFoundError,
            ),
        ],
    )
    def test_get_app_details(
        self,
        mock_requests,
        test_case,
        access_token,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test getting app details with different scenarios."""
        mock_response_obj = MagicMock()
        mock_response_obj.json.return_value = mock_response
        mock_response_obj.status_code = 200
        mock_requests["get"].return_value = mock_response_obj

        if expected_exception:
            with pytest.raises(expected_exception):
                get_app_details(access_token)
        else:
            result = get_app_details(access_token)
            assert result == expected_result

        mock_requests["get"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, mock_response, expected_result",
        [
            (
                "success",
                "test_token",
                "100",
                100,
            ),
            (
                "invalid_response",
                "test_token",
                "invalid",
                None,
            ),
            (
                "error_response",
                "test_token",
                None,
                None,
            ),
        ],
    )
    def test_get_users_count(
        self, mock_requests, test_case, access_token, mock_response, expected_result
    ):
        """Test getting users count with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.text = mock_response
            mock_response_obj.status_code = 200
            mock_requests["get"].return_value = mock_response_obj
        else:
            mock_requests["get"].side_effect = requests.RequestException(
                "Network error"
            )

        result = get_users_count(access_token)
        assert result == expected_result

        mock_requests["get"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, mock_responses, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                [
                    {
                        "value": [
                            {"mail": "<EMAIL>", "id": "id1"},
                            {"mail": "<EMAIL>", "id": "id2"},
                        ],
                        "@odata.nextLink": None,
                    }
                ],
                [
                    {"mail": "<EMAIL>", "id": "id1"},
                    {"mail": "<EMAIL>", "id": "id2"},
                ],
                None,
            ),
            (
                "pagination",
                "test_token",
                [
                    {
                        "value": [{"mail": "<EMAIL>", "id": "id1"}],
                        "@odata.nextLink": "next_page",
                    },
                    {
                        "value": [{"mail": "<EMAIL>", "id": "id2"}],
                        "@odata.nextLink": None,
                    },
                ],
                [
                    {"mail": "<EMAIL>", "id": "id1"},
                    {"mail": "<EMAIL>", "id": "id2"},
                ],
                None,
            ),
            (
                "error",
                "test_token",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_get_all_users(
        self,
        mock_requests,
        test_case,
        access_token,
        mock_responses,
        expected_result,
        expected_exception,
    ):
        """Test getting all users with different scenarios."""
        if expected_exception:
            # For error case, mock get_users_count to raise an exception
            with patch(
                "ms_teams_everstage.services.graph_api_services.get_users_count"
            ) as mock_count:
                mock_count.side_effect = requests.RequestException("Network error")
                mock_requests["get"].side_effect = requests.RequestException(
                    "Network error"
                )
                with pytest.raises(expected_exception):
                    get_all_users(access_token)
        else:
            # For success cases, mock get_users_count to return valid count
            with patch(
                "ms_teams_everstage.services.graph_api_services.get_users_count"
            ) as mock_count:
                mock_count.return_value = len(expected_result) if expected_result else 0

                mock_response_objects = []
                for response in mock_responses:
                    mock_response_obj = MagicMock()
                    mock_response_obj.json.return_value = response
                    mock_response_obj.status_code = 200
                    mock_response_objects.append(mock_response_obj)
                mock_requests["get"].side_effect = mock_response_objects

                result = get_all_users(access_token)
                assert result == expected_result

                # Verify the number of calls matches the number of responses
                assert mock_requests["get"].call_count == len(mock_responses)

    @pytest.mark.parametrize(
        "test_case, access_token, email_id, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                "<EMAIL>",
                {"value": [{"mail": "<EMAIL>", "id": "test_id"}]},
                [{"mail": "<EMAIL>", "id": "test_id"}],
                None,
            ),
            (
                "no_user",
                "test_token",
                "<EMAIL>",
                {"value": []},
                [],
                None,
            ),
            (
                "error",
                "test_token",
                "<EMAIL>",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_get_single_user(
        self,
        mock_requests,
        test_case,
        access_token,
        email_id,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test getting single user with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.json.return_value = mock_response
            mock_response_obj.status_code = 200
            mock_requests["get"].return_value = mock_response_obj
        else:
            mock_requests["get"].side_effect = requests.RequestException(
                "Network error"
            )

        if expected_exception:
            with pytest.raises(expected_exception):
                get_single_user(access_token, email_id)
        else:
            result = get_single_user(access_token, email_id)
            assert result == expected_result

        mock_requests["get"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, user_id, app_catalog_id, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                "test_user",
                "test_app",
                {"id": "test_id"},
                {"id": "test_id"},
                None,
            ),
            (
                "error",
                "test_token",
                "test_user",
                "test_app",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_install_app_for_user(
        self,
        mock_requests,
        test_case,
        access_token,
        user_id,
        app_catalog_id,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test installing app for user with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.json.return_value = mock_response
            mock_response_obj.status_code = 200
            mock_requests["post"].return_value = mock_response_obj
        else:
            mock_requests["post"].side_effect = requests.RequestException(
                "Network error"
            )

        if expected_exception:
            with pytest.raises(expected_exception):
                install_app_for_user(access_token, user_id, app_catalog_id)
        else:
            result = install_app_for_user(access_token, user_id, app_catalog_id)
            assert result.json() == expected_result

        mock_requests["post"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, user_id, app_catalog_id, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                "test_user",
                "test_app",
                {"value": [{"id": "test_id"}]},
                "test_id",
                None,
            ),
            (
                "no_conversation",
                "test_token",
                "test_user",
                "test_app",
                {"value": []},
                None,
                Exception,
            ),
            (
                "error",
                "test_token",
                "test_user",
                "test_app",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_get_conversation_id(
        self,
        mock_requests,
        test_case,
        access_token,
        user_id,
        app_catalog_id,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test getting conversation ID with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.json.return_value = mock_response
            mock_response_obj.status_code = 200
            mock_requests["get"].return_value = mock_response_obj
        else:
            mock_requests["get"].side_effect = requests.RequestException(
                "Network error"
            )

        if expected_exception:
            with pytest.raises(expected_exception):
                get_conversation_id(access_token, user_id, app_catalog_id)
        else:
            result = get_conversation_id(access_token, user_id, app_catalog_id)
            assert result == expected_result

        mock_requests["get"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, user_id, conversation_id, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                "test_user",
                "test_conversation",
                {"id": "test_chat_id"},
                "test_chat_id",
                None,
            ),
            (
                "error",
                "test_token",
                "test_user",
                "test_conversation",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_get_chat_id(
        self,
        mock_requests,
        test_case,
        access_token,
        user_id,
        conversation_id,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test getting chat ID with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.json.return_value = mock_response
            mock_response_obj.status_code = 200
            mock_requests["get"].return_value = mock_response_obj
        else:
            mock_requests["get"].side_effect = requests.RequestException(
                "Network error"
            )

        if expected_exception:
            with pytest.raises(expected_exception):
                get_chat_id(access_token, user_id, conversation_id)
        else:
            result = get_chat_id(access_token, user_id, conversation_id)
            assert result == expected_result

        mock_requests["get"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, mock_response, expected_result",
        [
            (
                "success",
                "test_token",
                {"mail": "<EMAIL>", "id": "test_id"},
                {"mail": "<EMAIL>", "id": "test_id"},
            ),
            (
                "error",
                "test_token",
                None,
                None,
            ),
        ],
    )
    def test_get_user_data_from_access_token(
        self,
        mock_requests,
        test_case,
        access_token,
        mock_response,
        expected_result,
    ):
        """Test getting user data from access token with different scenarios."""
        if mock_response:
            mock_response_obj = MagicMock()
            mock_response_obj.json.return_value = mock_response
            mock_response_obj.status_code = 200
            mock_requests["get"].return_value = mock_response_obj
        else:
            mock_requests["get"].side_effect = requests.RequestException(
                "Network error"
            )

        result = get_user_data_from_access_token(access_token)
        assert result == expected_result

        mock_requests["get"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                {"id": "test_app_id"},
                {"id": "test_app_id"},
                None,
            ),
            (
                "error",
                "test_token",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_publish_app_to_catalog(
        self,
        mock_requests,
        test_case,
        access_token,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test publishing app to catalog with different scenarios."""
        with patch(
            "ms_teams_everstage.services.graph_api_services.S3Uploader"
        ) as mock_s3, patch.dict(os.environ, {"MSTEAMS_APP_EXTERNAL_ID": "test_app"}):
            mock_s3.return_value.download_file.return_value = None

            # Create a temporary file for testing
            with patch("builtins.open", mock_open(read_data=b"test_package")):
                if mock_response:
                    mock_response_obj = MagicMock()
                    mock_response_obj.json.return_value = mock_response
                    mock_response_obj.status_code = 200
                    mock_requests["post"].return_value = mock_response_obj
                else:
                    mock_requests["post"].side_effect = requests.RequestException(
                        "Network error"
                    )

                if expected_exception:
                    with pytest.raises(expected_exception):
                        publish_app_to_catalog(access_token)
                else:
                    result = publish_app_to_catalog(access_token)
                    assert result.json() == expected_result

                mock_requests["post"].assert_called_once()
                mock_s3.return_value.download_file.assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, user_id, app_catalog_id, mock_responses, expected_result, expected_exception",
        [
            (
                "success_installed",
                "test_token",
                "test_user",
                "test_app",
                [
                    {"value": [{"id": "test_installation_id"}]},
                    {"id": "test_upgrade_id"},
                ],
                {"id": "test_upgrade_id"},
                None,
            ),
            (
                "success_not_installed",
                "test_token",
                "test_user",
                "test_app",
                [{"value": []}],
                {"id": "test_install_id"},
                None,
            ),
            (
                "error",
                "test_token",
                "test_user",
                "test_app",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_update_app_installed_for_user(
        self,
        mock_requests,
        test_case,
        access_token,
        user_id,
        app_catalog_id,
        mock_responses,
        expected_result,
        expected_exception,
    ):
        """Test updating app installed for user with different scenarios."""
        if mock_responses:
            mock_response_objects = []
            for response in mock_responses:
                mock_response_obj = MagicMock()
                mock_response_obj.json.return_value = response
                mock_response_obj.status_code = 200
                mock_response_objects.append(mock_response_obj)
            mock_requests["get"].side_effect = mock_response_objects

            # For success_not_installed case, we need to mock the install_app_for_user response
            if test_case == "success_not_installed":
                install_response = MagicMock()
                install_response.json.return_value = expected_result
                install_response.status_code = 200
                mock_requests["post"].return_value = install_response
            else:
                mock_requests["post"].return_value = mock_response_objects[-1]
        else:
            mock_requests["get"].side_effect = requests.RequestException(
                "Network error"
            )

        if expected_exception:
            with pytest.raises(expected_exception):
                update_app_installed_for_user(access_token, user_id, app_catalog_id)
        else:
            result = update_app_installed_for_user(
                access_token, user_id, app_catalog_id
            )
            assert result.json() == expected_result

        mock_requests["get"].assert_called_once()
        if mock_responses and (
            mock_responses[0].get("value") or test_case == "success_not_installed"
        ):
            mock_requests["post"].assert_called_once()

    @pytest.mark.parametrize(
        "test_case, access_token, app_catalog_id, mock_response, expected_result, expected_exception",
        [
            (
                "success",
                "test_token",
                "test_app",
                {"id": "test_update_id"},
                {"id": "test_update_id"},
                None,
            ),
            (
                "error",
                "test_token",
                "test_app",
                None,
                None,
                Exception,
            ),
        ],
    )
    def test_update_published_app_to_catalog(
        self,
        mock_requests,
        test_case,
        access_token,
        app_catalog_id,
        mock_response,
        expected_result,
        expected_exception,
    ):
        """Test updating published app to catalog with different scenarios."""
        with patch(
            "ms_teams_everstage.services.graph_api_services.S3Uploader"
        ) as mock_s3, patch.dict(os.environ, {"MSTEAMS_APP_EXTERNAL_ID": "test_app"}):
            mock_s3.return_value.download_file.return_value = None

            # Create a temporary file for testing
            with patch("builtins.open", mock_open(read_data=b"test_package")):
                if mock_response:
                    mock_response_obj = MagicMock()
                    mock_response_obj.json.return_value = mock_response
                    mock_response_obj.status_code = 200
                    mock_requests["post"].return_value = mock_response_obj
                else:
                    mock_requests["post"].side_effect = requests.RequestException(
                        "Network error"
                    )

                if expected_exception:
                    with pytest.raises(expected_exception):
                        update_published_app_to_catalog(access_token, app_catalog_id)
                else:
                    result = update_published_app_to_catalog(
                        access_token, app_catalog_id
                    )
                    assert result.json() == expected_result

                mock_requests["post"].assert_called_once()
                mock_s3.return_value.download_file.assert_called_once()

    @pytest.mark.parametrize(
        "test_case, mock_response, expected_result",
        [
            (
                "status_200_valid_json",
                MagicMock(
                    json=MagicMock(return_value={"status": "ok"}),
                    text="",
                    status_code=200,
                ),
                {"status": "ok"},
            ),
            (
                "status_201_valid_json",
                MagicMock(
                    json=MagicMock(side_effect=JSONDecodeError("Invalid JSON", "", 0)),
                    text="",
                    status_code=201,
                ),
                "",
            ),
            (
                "status_209_valid_json",
                MagicMock(
                    json=MagicMock(return_value={"info": "non-standard status"}),
                    text="",
                    status_code=209,
                ),
                {"info": "non-standard status"},
            ),
            (
                "status_409_json_fails_fallback_to_text",
                MagicMock(
                    json=MagicMock(side_effect=JSONDecodeError("Invalid JSON", "", 0)),
                    text="Conflict error occurred",
                    status_code=409,
                ),
                "Conflict error occurred",
            ),
            (
                "status_404_json_fails_text_is_none",
                MagicMock(
                    json=MagicMock(side_effect=JSONDecodeError("Invalid JSON", "", 0)),
                    text=None,
                    status_code=404,
                ),
                None,
            ),
            (
                "status_200_text_with_unicode",
                MagicMock(
                    json=MagicMock(side_effect=JSONDecodeError("Invalid JSON", "", 0)),
                    text="Unicode response 🚀",
                    status_code=200,
                ),
                "Unicode response 🚀",
            ),
            (
                "status_201_empty_json",
                MagicMock(
                    json=MagicMock(side_effect=JSONDecodeError("Invalid JSON", "", 0)),
                    text="",
                    status_code=201,
                ),
                "",
            ),
            (
                "status_409_json_with_data",
                MagicMock(
                    json=MagicMock(return_value={"error": "duplicate", "code": 409}),
                    text="",
                    status_code=409,
                ),
                {"error": "duplicate", "code": 409},
            ),
            (
                "status_204_no_content",
                MagicMock(
                    spec=["status_code", "json"],
                    json=MagicMock(side_effect=JSONDecodeError("Invalid JSON", "", 0)),
                    status_code=204,
                ),
                "",
            ),
        ],
    )
    def test_get_response_data(self, test_case, mock_response, expected_result):
        """Test get_response_data for various HTTP status codes and data scenarios."""
        result = get_response_data_wrapper(mock_response)
        assert result == expected_result, f"Failed test case: {test_case}"

    @pytest.mark.parametrize(
        "test_case, raise_exception, exception_type, exception_kwargs, expected_behavior, expected_return, function_args, function_kwargs",
        [
            # HTTPError scenarios
            (
                "http_error_raise_true",
                True,
                requests.HTTPError,
                {"response": MagicMock(status_code=404)},
                "raises",
                None,
                (),
                {},
            ),
            (
                "http_error_raise_false",
                False,
                requests.HTTPError,
                {"response": MagicMock(status_code=500)},
                "returns_none",
                None,
                (),
                {},
            ),
            (
                "http_error_no_response_raise_false",
                False,
                requests.HTTPError,
                {"response": None},
                "returns_none",
                None,
                (),
                {},
            ),
            # RequestException scenarios
            (
                "request_exception_raise_true",
                True,
                requests.RequestException,
                {},
                "raises",
                None,
                (),
                {},
            ),
            (
                "request_exception_raise_false",
                False,
                requests.RequestException,
                {},
                "returns_none",
                None,
                (),
                {},
            ),
            # General Exception scenarios
            (
                "general_exception_raise_true",
                True,
                Exception,
                {},
                "raises",
                None,
                (),
                {},
            ),
            (
                "general_exception_raise_false",
                False,
                Exception,
                {},
                "returns_none",
                None,
                (),
                {},
            ),
            # Success scenarios
            (
                "no_exception_raise_true",
                True,
                None,
                {},
                "returns_value",
                "success",
                (),
                {},
            ),
            (
                "no_exception_raise_false",
                False,
                None,
                {},
                "returns_value",
                "success",
                (),
                {},
            ),
            # Function with arguments scenarios
            (
                "function_with_args_success",
                False,
                None,
                {},
                "returns_value",
                "hello_world_test",
                ("hello", "world"),
                {"kwarg": "test"},
            ),
            (
                "function_with_args_error",
                False,
                requests.RequestException,
                {},
                "returns_none",
                None,
                ("error", "world"),
                {},
            ),
        ],
    )
    def test_handle_graph_api_errors_decorator(
        self,
        test_case,
        raise_exception,
        exception_type,
        exception_kwargs,
        expected_behavior,
        expected_return,
        function_args,
        function_kwargs,
    ):
        """Test the handle_graph_api_errors decorator with comprehensive scenarios."""

        @handle_graph_api_errors(raise_exception=raise_exception)
        def test_function(arg1="default", arg2="default", kwarg=None):
            if exception_type == requests.HTTPError:
                raise requests.HTTPError("HTTP Error", **exception_kwargs)
            elif exception_type == requests.RequestException:
                raise requests.RequestException("Network Error")
            elif exception_type == Exception:
                raise Exception("General Error")
            elif test_case == "function_with_args_error" and arg1 == "error":
                raise requests.RequestException("Test error")
            else:
                return f"{arg1}_{arg2}_{kwarg}" if arg1 != "default" else "success"

        if expected_behavior == "raises":
            with pytest.raises(exception_type):
                test_function(*function_args, **function_kwargs)
        else:
            result = test_function(*function_args, **function_kwargs)
            assert result == expected_return

    def test_handle_graph_api_errors_decorator_preserves_function_metadata(self):
        """Test that the decorator preserves function metadata like __name__ and __doc__."""

        @handle_graph_api_errors()
        def test_function():
            """Test function docstring."""
            return "test"

        assert test_function.__name__ == "test_function"
        assert test_function.__doc__ == "Test function docstring."
