# Use this as reference for syntax - https://github.com/atlassian-api/atlassian-python-api/blob/master/pyproject.toml

[tool.black]

line-length = 88
exclude = '''
(
    aws_migrations
    | dev-notebooks
    | everstage-admin
    | frontend
    | logs
    | static
    | migrations
    | local_migrations
    | localdev
)
'''

[tool.pylint.format]
max-line-length = 88

[tool.pylint.MASTER]
ignore = 'migrations'
load-plugins = "pylint_django,pylint_pytest,pylint_unittest,pylint_custom"
django-settings-module = "interstage_project.settings"
ignore-patterns = [
    ".*toml",
    ".*txt",
    ".*yml",
    ".*sh",
    ".*js",
    ".gitignore",
    ".env.*",
    "conftest.py",
]
ignore-paths = ["selenium_api_testing", ".*tests", ".*views"]

# Specify a score threshold to be exceeded before program exits with error.
# fail-under = '5.0'

[tool.pylint."MESSAGES CONTROL"]
disable = [
    "too-few-public-methods",
    "line-too-long",
    "missing-function-docstring",
    "cannot-enumerate-pytest-fixtures",
    "broad-except",
    "broad-exception-raised",
    "bare-except",
    "missing-timeout",
    "logging-fstring-interpolation",
]

[tool.pylint.VARIABLES]
ignored-argument-names = "_.*|info"

[tool.pylint.MISCELLANEOUS]
# List of note tags to take in consideration, separated by a comma.
notes = ["FIXME", "XXX"]

[tool.isort]
skip = ["commission_engine/services/data_sources/__init__.py"]
profile = "black"
line_length = 88
skip_glob = [
    "docs/*",
    "crystal/migrations/*",
    "spm/migrations/*",
    "commission_engine/*",
    "crystal/local_migrations/*",
]

[tool.pytest.ini_options]
addopts = "--create-db --disable-pytest-warnings --capture=no --verbose"
DJANGO_SETTINGS_MODULE = "interstage_project.settings"
minversion = '6.0'
python_files = 'test_*.py'
python_classes = 'Test*'
python_functions = 'test_*'

[tool.coverage.run]
omit = [
    '**/aws_migrations/*',
    '**/migrations/**',
    '**/local_migrations/**',
    '**/models/*',
    '**/serializers/*',
    '**/tests/*',
    '**/__init__.py',
    '**/urls/*',
    '**/urls.py',
    '**/schema.py',
    '**/conftest.py',
    '**/admin.py',
    '**/apps.py',
    '**/models.py',
    '**/tests.py',
    '**/views.py',
    '**/abstract.py',
    '**/everstage_tests/**',
    '**/selenium_api_testing/**',
    '**/testresults/**',
    '**/ops/**',
    'everstage_etl/data_migration/**',    # These are one-off migration scripts that need not be tested
    '**/views/**',
    '**/management/**',
    '**/data_loaders.py',
    'everstage_etl/invariant_checker/**',
    # Temporarily excluding these files from coverage report - fix by 2023-03-20
    'everstage_etl/snowflake_calc_field_udf_modules/**',
]

[tool.pyright]
exclude = [
    "**/node_modules",
    "**/__pycache__",
    "aws_migrations",
    "everstage-admin",
    "frontend",
    "logs",
    "static",
    "migrations",
]

[tool.importlinter]
root_packages = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
    "everstage_ddd",
]

[[tool.importlinter.contracts]]
name = "forbid_everstage_ddd_mobile_imports"
type = "forbidden"
source_modules = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
]
forbidden_modules = ["everstage_ddd.mobile"]
allowed_importers = ["everstage_ddd.mobile"]
allow_indirect_imports = "True"
ignore_imports = [
    "interstage_project.applications.mobile -> everstage_ddd.mobile",
    "spm.views.session_management_views -> everstage_ddd.mobile",
]

[[tool.importlinter.contracts]]
name = "forbid_everstage_ddd_chrome_extension_imports"
type = "forbidden"
source_modules = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
]
forbidden_modules = ["everstage_ddd.chrome_extension"]
allowed_importers = ["everstage_ddd.chrome_extension"]
allow_indirect_imports = "True"
ignore_imports = [
    "interstage_project.applications.chrome_extension -> everstage_ddd.chrome_extension",
]

[[tool.importlinter.contracts]]
name = "forbid_everstage_ddd_datasheet_imports"
type = "forbidden"
source_modules = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
    "everstage_ddd.stormbreaker",
]
forbidden_modules = ["everstage_ddd.datasheet"]
allow_indirect_imports = "True"
ignore_imports = [
    "commission_engine.models.databook_models -> everstage_ddd.datasheet.models",
    "interstage_project.applications.desktop_web -> everstage_ddd.datasheet.databook_api",
    "interstage_project.applications.desktop_web -> everstage_ddd.datasheet.datasheet_api",
    "everstage_ddd.** -> everstage_ddd.datasheet",
    "everstage_ddd.stormbreaker.query_spec_builder.everstage_query_spec_builder -> everstage_ddd.datasheet.data_models",
    "common.tests.test_bitemporal_selector -> everstage_ddd.datasheet",
    "commission_engine.models -> everstage_ddd.datasheet",
    "commission_engine.models.skd_pkd_map_models -> everstage_ddd.datasheet",
    "spm.pydantic_serializers.custom_object -> everstage_ddd.datasheet",
    "commission_engine.pydantic_serializers.ever_object -> everstage_ddd.datasheet",
    "everstage_etl.tasks.datasheet_generation.generate -> everstage_ddd.datasheet",
    "commission_engine.services.expression_designer.autocomplete_context -> everstage_ddd.datasheet",
    "everstage_etl.tasks.datasheet_generation.generate -> everstage_ddd.datasheet",
    "everstage_etl.tasks.datasheet_generation.utils -> everstage_ddd.datasheet.services.datasheet_service",
    "everstage_etl.tests.** -> everstage_ddd.datasheet",
    "commission_engine.services.expression_designer.autocomplete_context -> everstage_ddd.datasheet.services.datasheet_service",
    "commission_engine.services.expression_designer.autocomplete_context -> everstage_ddd.datasheet.selectors.datasheet_selector",
    "commission_engine.services.expression_designer.autocomplete_context -> everstage_ddd.datasheet.selectors.datasheet_transformation_selector",
    "commission_engine.services.commission_calculation_service.parser_v2 -> everstage_ddd.datasheet",
    "commission_engine.services.expression_designer.converter_function_utils -> everstage_ddd.datasheet",
    "spm.services.analytics_default_dashboard -> everstage_ddd.datasheet.services.databook_services",
    "spm.services.analytics_default_dashboard -> everstage_ddd.datasheet.services.datasheet_service",
    "spm.services.databook_services -> everstage_ddd.datasheet.helpers.datasheet_version_writer",
    "commission_engine.services.expression_designer.autocomplete_context -> everstage_ddd.datasheet.services.datasheet_variables",
    "spm.services.databook_services -> everstage_ddd.datasheet",
    "everstage_ddd.stormbreaker.tests.test_utils -> everstage_ddd.datasheet.selectors",
    "everstage_ddd.stormbreaker.utils -> everstage_ddd.datasheet.selectors",
    "everstage_etl.tasks.datasheet_generation.utils -> everstage_ddd.datasheet.services.datasheet_service",
    "everstage_etl.tasks.datasheet_generation.post_generate -> everstage_ddd.datasheet.data_models",
    "everstage_etl.tasks.datasheet_generation.post_generate -> everstage_ddd.datasheet.enums",
    "everstage_etl.tasks.datasheet_generation.post_generate -> everstage_ddd.datasheet.selectors.datasheet_adjustment_selector"
]

[[tool.importlinter.contracts]]
name = "forbid_everstage_ddd_stormbreaker_imports"
type = "forbidden"
source_modules = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
    "everstage_ddd.datasheet",
]
forbidden_modules = ["everstage_ddd.stormbreaker"]
allow_indirect_imports = "True"
ignore_imports = [
    "everstage_ddd.** -> everstage_ddd.stormbreaker",
    "everstage_etl.** -> everstage_ddd.stormbreaker",
    "spm.** -> everstage_ddd.stormbreaker",
    "commission_engine.** -> everstage_ddd.stormbreaker",
    "crystal.** -> everstage_ddd.stormbreaker",
    "commission_engine.tests.services.datasheet_data_services.test_datasheet_retrieval_service -> everstage_ddd.stormbreaker.executors.snowflake_flattened_executor",
    "everstage_ddd.datasheet.views -> everstage_ddd.stormbreaker.stormbreaker_exception",
    "commission_engine.snowflake_accessors.base_snowflake_accessor -> everstage_ddd.stormbreaker.types"
]

[[tool.importlinter.contracts]]
name = "forbid_everstage_ddd_estimator_imports"
type = "forbidden"
source_modules = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
]
forbidden_modules = ["everstage_ddd.estimator"]
allowed_importers = ["everstage_ddd.estimator"]

[[tool.importlinter.contracts]]
name = "forbid_everstage_ddd_notification_imports"
type = "forbidden"
source_modules = [
    "interstage_project",
    "spm",
    "commission_engine",
    "async_tasks",
    "common",
    "crystal",
    "everstage_admin_backend",
    "everstage_etl",
    "everstage_infra",
    "kpi",
    "ms_teams_everstage",
    "slack_everstage",
    "snowflake_udf_modules",
    "superset",
]
forbidden_modules = ["everstage_ddd.notifications"]
allowed_importers = ["everstage_ddd.notifications"]
allow_indirect_imports = "True"
ignore_imports = [
    "spm.services.config_services.config_changes_notification_service -> everstage_ddd.notifications",
    "spm.services.drs_services -> everstage_ddd.notifications",
    "spm.services.commission_actions_service.commission_slack_services -> everstage_ddd.notifications",
    "commission_engine.tasks.admin_notifications -> everstage_ddd.notifications",
    "commission_engine.services.update_payee_email_service -> everstage_ddd.notifications",
    "spm.commission_adjustment_approvals.services.email_notification_services -> everstage_ddd.notifications",
    "spm.services.approval_workflow_services.approval_email_services -> everstage_ddd.notifications",
    "spm.services.settlement_actions_service.settlement_actions_service -> everstage_ddd.notifications",
    "spm.views.settlement_actions_views -> everstage_ddd.notifications",
    "commission_engine.tasks.approval_workflows -> everstage_ddd.notifications",
    "commission_engine.tasks.payee_notifications -> everstage_ddd.notifications",
]

[tool.ruff]

# Never enforce `E501` (line length violations).
# Ignore `TID252` (relative imports) in all files.
# Ignore `RET504` (assignment before return) in all files for better readability.
# Ignore `S101` (assertions) in all files for better readability.
# Ignore `FBT001` (boolean as positional argument) in all files for better readability.
# Ignore `FBT002` (boolean as positional argument) in all files for better readability.
# Ignore `S324` (md5 hash) in all files - md5 used for non-security purposes (query deduplication, etc.)
ignore = ["E501", "TID252", "RET504", "S101", "FBT001", "FBT002", "S324", "TRY003"]

# To make this easier to track, use the order in the ruff website
select = [
    "F",
    "E",
    "N",
    "S",
    "BLE",
    "FBT",
    "B",
    "DTZ",
    "SLF",
    "RET",
    "SIM",
    "TID",
    "ARG",
    "PTH",
    "ERA",
    "PL",
    "TRY",
    "PERF",
]

# Todo: @sairt24, @Vaishali-Everstage
# Ignore following folders Temporarily (To be fixed except external_libs)
extend-exclude = [
    "external_libs",
    "everstage_ddd/workflow_builder/accessors/workflow_housekeeping_accessor.py",
    "everstage_ddd/workflow_builder/service/workflow_service.py",
    "everstage_ddd/workflow_builder/service/workflow_data_service.py",
    "everstage_ddd/workflow_builder/enc_decoder.py",
    # TQM folder is temporarily excluded. To be fixed by @Malai04, @SreekarMouli
    "everstage_ddd/tqm/*",
    # Temporarily excluding cpq agent folder. To be fixed by @krishnakumar18
    "everstage_ddd/llm_agent/cpq_agent/*",
]


# Ignore `E402` (import violations) in all `__init__.py` files
[tool.ruff.per-file-ignores]
"__init__.py" = ["E402"]
# Ignore catching generation exceptions check under everstage_ddd folder
"everstage_ddd/*" = ["BLE001", "RET505", "TRY300", "FBT002"]
#S101 - asserts allowed in tests.
#ARG - Unused function args
#FBT - Booleans as positional arguments in tests
#PLR2004 - Magic value used in comparison
"**/{tests}/*" = ["S101", "FBT", "ARG", "PLR2004", "SLF001"]
"**/test_*.py" = ["S101", "FBT", "ARG", "PLR2004", "SLF001", "S608"]
"everstage_ddd/llm_agent/*" = ["ARG002", "FBT002", "PERF401"]

# Ignore `PLR0912`, `PLR0915` in workflow_builder
"everstage_ddd/workflow_builder/*" = ["PLR0912", "PLR0915", "S608"]

# Ignore `PLR0912`, `PLR0915` in thunderforge
"everstage_ddd/thunderforge/*" = ["S608"]

# Ignore 'ERA001', 'ARG001', 'N999' in everstage_ddd/cpq (will be fixed later)
"everstage_ddd/cpq/*" = ["ERA001", "ARG001", "N999"]

# Ignore SQL injection - less risk since user input is not there
"everstage_ddd/snowflake_reports/*" = ["S608", "PLR0913", "TRY003"]
# Ignore SQL injection - less risk since user input is not there
"everstage_ddd/settlement_snowflake_utils/*" = ["S608", "PLR0913", "TRY003"]
