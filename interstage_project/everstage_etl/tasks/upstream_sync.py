import datetime
import logging
import traceback
import uuid
from collections import defaultdict
from typing import Any, Dict
from uuid import UUID

from celery import shared_task
from django.utils import timezone
from django.utils.timezone import make_aware

import everstage_ddd.upstream as upstream_module
from commission_engine.accessors.client_accessor import (
    should_insert_meta_data_to_vec_db,
    should_take_co_snapshot,
)
from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ApiAccessConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    ETLLockAccessor,
    UpstreamETLStatusAccessor,
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.accessors.skd_pkd_map_accessor import CustomObjectPkdMapAccessor
from commission_engine.services import etl_sync_status_service
from commission_engine.services.etl_sync_status_service import (
    send_upstream_deleted_field_notification,
)
from commission_engine.third_party_connections.exceptions.missing_field_exception import (
    MissingFieldException,
)
from commission_engine.utils import ETL_STATUS
from commission_engine.utils.general_data import UpstreamETLVersions, UpstreamSyncModes
from commission_engine.utils.log_utils import merge_log_context
from common.celery.celery_base_task import EverCeleryBaseTask
from common.everstage_supabase.services import get_supabase_client
from everstage_ddd.global_search.meta_data_extractor.custom_object_meta_data import (
    upsert_co_meta_data_in_vector_db,
)
from everstage_ddd.upstream import (
    get_upstream_table_name,
    get_upstream_timestamps,
    set_upstream_timestamps,
)
from everstage_ddd.upstream.error_notification.integration_error_handler import (
    IntegrationErrorHandler,
)
from everstage_ddd.upstream.preprocessors import PreprocessingStrategy
from everstage_etl.snapshot_service import save_co_snapshot_to_sf_expanded_and_efs
from everstage_etl.tasks.extraction import upstream_data_extractor
from everstage_etl.tasks.loading import batch_upstream_loader, upstream_loader
from everstage_etl.tasks.transformation import (
    batch_upstream_transformer,
    upstream_transformer,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context

logger = logging.getLogger(__name__)


def create_realtime_record(
    client_id: int, realtime_task_id: UUID, params: Dict[str, Any]
) -> None:
    try:
        supabase = get_supabase_client()
        supabase.insert(
            {
                "task_name": "UPSTREAM_SYNC",
                "client_id": client_id,
                "task_id": str(realtime_task_id),
                "data": params,
            }
        ).execute()
    except Exception:  # pylint: disable=broad-except
        logger.exception("Error creating the realtime record")


def update_realtime_record(realtime_task_id: UUID, params: Dict[str, Any]) -> None:
    try:
        supabase = get_supabase_client()
        supabase.update({"data": params}).eq("task_id", str(realtime_task_id)).execute()
    except Exception:  # pylint: disable=broad-except
        logger.exception("Error updating realtime record %s", realtime_task_id)


# common primary columns and custom primary column
@shared_task(base=EverCeleryBaseTask)
def upstream_sync_v2(
    client_id: int,
    e2e_sync_run_id: UUID,
    object_id: str,
    sync_mode: str,
    destination_object_id: str,
    task: str,
    destination_object_type: str,
    integration_id: UUID,
    include_upstream_hard_delete_sync: bool = False,
    log_context=None,
    trigger=None,
):
    integration_record = IntegrationAccessor(
        client_id=client_id
    ).get_object_by_integration_id(integration_id=integration_id)

    preprocessing_metadata, page_size = (
        integration_record.preprocessing_metadata,  # type: ignore
        integration_record.batch_etl_page_size,  # type: ignore
    )

    additional_data = integration_record.additional_data  # type: ignore
    run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)
    is_source_data_as_variant = (additional_data or {}).get(
        "is_source_data_as_variant", True
    )
    transformation_logic = integration_record.transformation_logic  # type: ignore

    source_primary_keys = []

    if not page_size:
        page_size = 50000

    # Build row key from custom object PKs
    primary_keys = (
        CustomObjectAccessor(client_id)
        .get_primary_keys(int(destination_object_id))
        .get("primary_key", [])
    )

    if len(primary_keys) > 0:
        source_primary_keys = TransformationConfigAccessor(
            client_id=client_id
        ).get_record_by_destination_fields(
            integration_id=integration_id, destination_fields=primary_keys
        )

    sync_run_id = uuid.uuid4()
    realtime_task_id = uuid.uuid4()
    primary_kd = make_aware(datetime.datetime.now())
    lock_obj = None
    sync_run_log = {
        "task": "OBJECT_SYNC",
        "start_time": str(primary_kd),
        "end_time": None,
        "status": "started",
        "error_info": None,
    }

    log_context_local = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        "sync_run_id": sync_run_id,
        "object_id": object_id,
    }
    log_context = merge_log_context(log_context, log_context_local)
    set_threadlocal_context(log_context)

    if (
        include_upstream_hard_delete_sync
        and sync_mode.lower() == UpstreamSyncModes.CHANGES.value
    ):
        aa = ApiAccessConfigAccessor(client_id)
        hard_delete_sync_object = aa.get_obj_by_integration_id_and_source_id(
            integration_id, object_id.lower() + "_hard_delete"
        )
        # Run a all mode sync if include_upstream_hard_delete_sync is true and "_hard_delete" config object is present
        if hard_delete_sync_object:
            sync_mode = UpstreamSyncModes.ALL.value
            logger.info(
                "Syncing Hard Deletes: Running all mode sync for Object - %s", object_id
            )
        else:
            logger.info("Hard Delete Sync not present for Object - %s", object_id)

    usa = UpstreamETLStatusAccessor(client_id, e2e_sync_run_id, sync_run_id)
    changes_start_time, changes_end_time = None, None
    timestamps = get_upstream_timestamps(client_id, integration_id)
    if sync_mode.lower() != UpstreamSyncModes.ALL.value:
        changes_start_time = timestamps["api_changes_synced_till"]
        changes_end_time = primary_kd
    else:
        changes_start_time = timestamps["historic_sync_date"]
        changes_end_time = primary_kd

    params = {
        "object_id": task,
        "sync_start_time": primary_kd,
        "sync_status": ETL_STATUS.STARTED.value,
        "sync_mode": sync_mode,
        "changes_start_time": changes_start_time,
        "changes_end_time": changes_end_time,
        "source": "batch_job",
        "integration_id": integration_id,
        "trigger": trigger,
    }
    realtime_record_data = {
        "status": None,
        "integration_id": str(integration_id),
        "object_id": object_id,
        "sync_start_time": str(primary_kd),
        "sync_end_time": None,
        "task_name": task,
    }
    try:
        logger.info("BEGIN: UPSTREAM SYNC - %s", object_id)
        sync_obj = usa.insert_sync_status(params)
        create_realtime_record(
            client_id=client_id,
            realtime_task_id=realtime_task_id,
            params={**realtime_record_data, **{"status": "STARTED"}},
        )
        if sync_obj is not None:
            logger.debug("Acquiring Lock")
            # Acquire lock
            lock_name = str(client_id) + "#" + destination_object_id + "#" + sync_mode
            la = ETLLockAccessor(client_id, e2e_sync_run_id, sync_run_id)
            lock_obj = la.acquire_lock(lock_name, object_id, trigger=trigger)
            if lock_obj is not None:
                logger.debug(
                    "Acquired Lock Name - %s Id - %s", lock_name, lock_obj.lock_id
                )
                # Update sync status - LOCKED
                if usa.change_status_to_lock_acquired():
                    if preprocessing_metadata:  # preprocess logic
                        logger.info(
                            "Preprocessing data for integration %s", str(integration_id)
                        )
                        upstream_module.preprocess_upstream_data(
                            client_id=client_id,
                            sync_run_id=sync_run_id,
                            integration_id=integration_id,
                            destination_object_id=int(destination_object_id),
                            preprocessing_metadata=preprocessing_metadata,
                            changes_end_time=(
                                changes_end_time if changes_end_time else timezone.now()
                            ),
                        )
                    # Extraction #Return Data from extraction logic
                    logger.info("BEGIN: EXTRACTION - %s", object_id)

                    start_time = make_aware(datetime.datetime.now())
                    usa.change_status_to_extraction()
                    update_realtime_record(
                        realtime_task_id=realtime_task_id,
                        params={**realtime_record_data, **{"status": "EXTRACTION"}},
                    )
                    usa.update_extraction_details(
                        {
                            "start_time": start_time,
                            "status": "started",
                            "end_time": None,
                        }
                    )
                    if (
                        preprocessing_metadata
                        and preprocessing_metadata["strategy"]
                        == PreprocessingStrategy.SNAPSHOT_COMPARATOR.value
                    ):
                        logger.info(
                            "Skipping Extraction for %s as data extracted in preprocess step using %s",
                            str(integration_id),
                            PreprocessingStrategy.SNAPSHOT_COMPARATOR.value,
                        )
                    else:
                        upstream_data_extractor(
                            client_id,
                            e2e_sync_run_id,
                            sync_run_id,
                            task,
                            object_id,
                            primary_kd,
                            sync_mode,
                            changes_start_time,
                            integration_id,
                            destination_object_id,
                            destination_object_type,
                            upstream_etl_version=UpstreamETLVersions.V2.value,
                            source_primary_keys=source_primary_keys,
                        )

                    end_time = make_aware(datetime.datetime.now())
                    usa.update_extraction_details(
                        {
                            "start_time": start_time,
                            "status": "completed",
                            "end_time": end_time,
                        }
                    )
                    logger.info("END: EXTRACTION - %s", object_id)
                    logger.info("BEGIN: TRANSFORMATION - %s", object_id)
                    # print("US.. EXT DATA.. {}".format(ext_data))
                    # Transformation #Use data from extraction logic
                    start_time = make_aware(datetime.datetime.now())
                    usa.change_status_to_transformation()
                    update_realtime_record(
                        realtime_task_id=realtime_task_id,
                        params={**realtime_record_data, **{"status": "TRANSFORMATION"}},
                    )
                    usa.update_transformation_details(
                        {
                            "start_time": start_time,
                            "status": "started",
                            "end_time": None,
                        }
                    )
                    upstream_snowflake_sync = None
                    if run_snowflake_sync:
                        logger.info("Running Upstream in Snowflake")
                        source_data_table = get_upstream_table_name(
                            client_id, integration_id
                        )
                        upstream_sync_meta = upstream_module.UpstreamSyncParams(
                            source_data_table=source_data_table,
                            custom_object_id=int(destination_object_id),
                            integration_id=integration_id,
                            sync_mode=sync_mode,
                            client_id=client_id,
                            e2e_sync_run_id=e2e_sync_run_id,
                            sync_run_id=sync_run_id,
                            changes_sync_time=timestamps["upstream_source_synced_till"],
                            is_source_data_as_variant=is_source_data_as_variant,
                            transformation_logic=transformation_logic,
                            primary_kd=primary_kd,
                        )
                        upstream_snowflake_sync = upstream_module.UpstreamSnowflakeSync(
                            upstream_sync_meta
                        )

                    if run_snowflake_sync and upstream_snowflake_sync:
                        upstream_snowflake_sync.transform()
                    else:
                        batch_upstream_transformer(
                            client_id=client_id,
                            e2e_sync_run_id=e2e_sync_run_id,
                            sync_run_id=sync_run_id,
                            object_id=object_id,
                            destination_object_id=destination_object_id,
                            page_size=page_size,
                            source_primary_keys=source_primary_keys,
                        )
                    end_time = make_aware(datetime.datetime.now())
                    usa.update_transformation_details(
                        {
                            "start_time": start_time,
                            "status": "completed",
                            "end_time": end_time,
                        }
                    )
                    logger.info("END: TRANSFORMATION - %s", object_id)
                    logger.info("BEGIN: LOADING - %s", object_id)
                    # Loading
                    start_time = make_aware(datetime.datetime.now())
                    usa.change_status_to_loading()
                    update_realtime_record(
                        realtime_task_id=realtime_task_id,
                        params={**realtime_record_data, **{"status": "LOADING"}},
                    )
                    usa.update_loading_details(
                        {
                            "start_time": start_time,
                            "status": "started",
                            "end_time": None,
                        }
                    )
                    if run_snowflake_sync and upstream_snowflake_sync:
                        upstream_snowflake_sync.load()
                    else:
                        batch_upstream_loader(
                            client_id=client_id,
                            e2e_sync_run_id=e2e_sync_run_id,
                            sync_run_id=sync_run_id,
                            object_id=object_id,
                            destination_object_id=destination_object_id,
                            primary_kd=primary_kd,
                            sync_mode=sync_mode,
                            destination_object_type=destination_object_type,
                            page_size=page_size,
                            integration_id=integration_id,
                        )
                        set_upstream_timestamps(
                            client_id,
                            integration_id,
                            {
                                "api_changes_synced_till": primary_kd,
                                "api_deletes_synced_till": primary_kd,
                                "upstream_source_synced_till": primary_kd,
                            },
                            str(e2e_sync_run_id),
                        )
                    end_time = make_aware(datetime.datetime.now())
                    usa.update_loading_details(
                        {
                            "start_time": start_time,
                            "status": "completed",
                            "end_time": end_time,
                        }
                    )
                    logger.info("END: LOADING - %s", object_id)
                    if should_take_co_snapshot(client_id):
                        save_co_snapshot_to_sf_expanded_and_efs(
                            client_id, destination_object_id, str(primary_kd)
                        )
                        if should_insert_meta_data_to_vec_db(client_id):
                            upsert_co_meta_data_in_vector_db(
                                client_id, destination_object_id
                            )
                    # change status to completed
                    completion_time = make_aware(datetime.datetime.now())
                    if usa.change_status_to_complete(timestamp=completion_time):
                        la.release_lock()  # relese lock in etl lock
                        update_realtime_record(
                            realtime_task_id=realtime_task_id,
                            params={
                                **realtime_record_data,
                                **{
                                    "status": "COMPLETE",
                                    "sync_end_time": str(end_time),
                                },
                            },
                        )

                        custom_object_pkd_map_accessor = CustomObjectPkdMapAccessor(
                            client_id
                        )
                        custom_object_pkd_map_accessor.upsert_custom_object_primary_kd(
                            int(destination_object_id), primary_kd, e2e_sync_run_id
                        )

                        logger.debug(
                            "Released Lock Name - %s Id - %s",
                            lock_name,
                            lock_obj.lock_id,
                        )
            else:
                existing_lock = la.get_lock_by_lock_name(lock_name).first()
                if existing_lock is not None and existing_lock.trigger != trigger:
                    logger.info(f"Sync in progress through {existing_lock.trigger}")
                    timestamp = make_aware(datetime.datetime.now())
                    sync_run_log["end_time"] = str(timestamp)
                    sync_run_log["status"] = "skipped"
                    sync_run_log["error_info"] = (
                        f"Skipped due to ongoing sync through {existing_lock.trigger}"
                    )
                    usa.change_status_to_skipped(timestamp, sync_run_log)
                    update_realtime_record(
                        realtime_task_id,
                        params={
                            **realtime_record_data,
                            **{"status": "COMPLETE", "sync_end_time": str(timestamp)},
                        },
                    )
                    logger.info("END: UPSTREAM SYNC - %s", object_id)
                    return
                failed_timestamp = make_aware(datetime.datetime.now())
                sync_run_log["end_time"] = str(failed_timestamp)
                sync_run_log["status"] = "failed"
                sync_run_log["error_info"] = f"Lock failed: {object_id}"
                usa.change_status_failed_to_acquire_lock(
                    timestamp=failed_timestamp, sync_run_log=sync_run_log
                )
                update_realtime_record(
                    realtime_task_id=realtime_task_id,
                    params={
                        **realtime_record_data,
                        **{"status": "FAILED", "sync_end_time": str(failed_timestamp)},
                    },
                )
                etl_sync_status_service.change_status_to_failed(
                    client_id,
                    e2e_sync_run_id,
                    failed_timestamp,
                )
                logger.info("Failed to Acquire Lock - %s", lock_name)
        logger.info("END: UPSTREAM SYNC - %s", object_id)
    except Exception as err:
        error_handler = IntegrationErrorHandler(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            object_id=object_id,
            integration_id=integration_id,
        )
        parsed_error = error_handler.handle_integration_error(str(err))
        logger.info("Parsed error: %s", parsed_error)
        logger.error("EXCEPTION: UPSTREAM SYNC. %s - %s", err, object_id)
        logger.error("EXCEPTION TB: UPSTREAM SYNC traceback. %s", traceback.print_exc())
        failed_timestamp = make_aware(datetime.datetime.now())
        sync_run_log["end_time"] = str(failed_timestamp)
        sync_run_log["status"] = "failed"
        sync_run_log["error_info"] = f"Upstream sync failed for {object_id}"
        sync_run_log["ai_parsed_error"] = parsed_error
        etl_sync_status_service.change_status_to_failed(
            client_id,
            e2e_sync_run_id,
            failed_timestamp,
        )
        update_realtime_record(
            realtime_task_id=realtime_task_id,
            params={
                **realtime_record_data,
                **{"status": "FAILED", "sync_end_time": str(failed_timestamp)},
            },
        )
        if lock_obj is not None:  # release
            usa.change_status_to_failed(
                timestamp=failed_timestamp, sync_run_log=sync_run_log
            )
            la = ETLLockAccessor(client_id, e2e_sync_run_id, sync_run_id)
            la.release_lock()
            logger.error("EXCEP: UPSTREAM SYNC. Released Lock")
        else:
            logger.error("EXCEP: UPSTREAM SYNC. Failed to Acquire Lock")


@shared_task(base=EverCeleryBaseTask)
def upstream_sync(
    client_id: int,
    e2e_sync_run_id: UUID,
    object_id: str,
    sync_mode: str,
    destination_object_id: str,
    task: str,
    destination_object_type: str,
    integration_id: UUID,
    include_upstream_hard_delete_sync=False,
    log_context=None,
    trigger=None,
):
    integration_record = IntegrationAccessor(
        client_id=client_id
    ).get_object_by_integration_id(integration_id=integration_id)

    preprocessing_metadata = integration_record.preprocessing_metadata  # type: ignore
    additional_data = integration_record.additional_data  # type: ignore
    run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)
    is_source_data_as_variant = (additional_data or {}).get(
        "is_source_data_as_variant", True
    )
    transformation_logic = integration_record.transformation_logic  # type: ignore

    sync_run_id = uuid.uuid4()
    realtime_task_id = uuid.uuid4()
    primary_kd = timezone.now()
    lock_obj = None
    ext_data, trans_data = {}, {}
    sync_run_log = {
        "task": "OBJECT_SYNC",
        "start_time": str(primary_kd),
        "end_time": None,
        "status": "started",
        "error_info": None,
    }

    log_context_local = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        "sync_run_id": sync_run_id,
        "object_id": object_id,
        "include_upstream_hard_delete_sync": include_upstream_hard_delete_sync,
    }
    log_context = merge_log_context(log_context, log_context_local)
    set_threadlocal_context(log_context)

    if (
        include_upstream_hard_delete_sync
        and sync_mode.lower() == UpstreamSyncModes.CHANGES.value
    ):
        aa = ApiAccessConfigAccessor(client_id)
        hard_delete_sync_object = aa.get_obj_by_integration_id_and_source_id(
            integration_id, object_id.lower() + "_hard_delete"
        )
        # Run a all mode sync if include_upstream_hard_delete_sync is true and "_hard_delete" config object is present
        if hard_delete_sync_object:
            sync_mode = UpstreamSyncModes.ALL.value
            logger.info(
                "Syncing Hard Deletes: Running all mode sync for Object - %s", object_id
            )
        else:
            logger.info("Hard Delete Sync not present for Object - %s", object_id)

    usa = UpstreamETLStatusAccessor(client_id, e2e_sync_run_id, sync_run_id)
    changes_start_time, changes_end_time = None, None
    timestamps = get_upstream_timestamps(client_id, integration_id)
    if sync_mode.lower() != UpstreamSyncModes.ALL.value:
        changes_start_time = timestamps["api_changes_synced_till"]
        changes_end_time = primary_kd
    else:
        changes_start_time = timestamps["historic_sync_date"]
        changes_end_time = primary_kd

    params = {
        "object_id": task,
        "sync_start_time": primary_kd,
        "sync_status": ETL_STATUS.STARTED.value,
        "sync_mode": sync_mode,
        "changes_start_time": changes_start_time,
        "changes_end_time": changes_end_time,
        "source": "batch_job",
        "integration_id": integration_id,
        "trigger": trigger,
    }
    realtime_record_data = {
        "status": None,
        "integration_id": str(integration_id),
        "object_id": object_id,
        "sync_start_time": str(primary_kd),
        "sync_end_time": None,
        "task_name": task,
    }
    try:
        logger.info("BEGIN: UPSTREAM SYNC - %s", object_id)
        sync_obj = usa.insert_sync_status(params)
        create_realtime_record(
            client_id=client_id,
            realtime_task_id=realtime_task_id,
            params={**realtime_record_data, **{"status": "STARTED"}},
        )
        if sync_obj is not None:
            logger.debug("Acquiring Lock")
            # Acquire lock
            lock_name = str(client_id) + "#" + destination_object_id + "#" + sync_mode
            la = ETLLockAccessor(client_id, e2e_sync_run_id, sync_run_id)
            lock_obj = la.acquire_lock(lock_name, object_id, trigger=trigger)
            if lock_obj is not None:
                logger.debug(
                    "Acquired Lock Name - %s Id - %s", lock_name, lock_obj.lock_id
                )
                # Update sync status - LOCKED
                if usa.change_status_to_lock_acquired():
                    if preprocessing_metadata:  # preprocess logic
                        logger.info(
                            "Preprocessing data for integration %s", str(integration_id)
                        )
                        upstream_module.preprocess_upstream_data(
                            client_id=client_id,
                            sync_run_id=sync_run_id,
                            integration_id=integration_id,
                            destination_object_id=int(destination_object_id),
                            preprocessing_metadata=preprocessing_metadata,
                            changes_end_time=(
                                changes_end_time if changes_end_time else timezone.now()
                            ),
                        )
                    # Extraction #Return Data from extraction logic
                    logger.info("BEGIN: EXTRACTION - %s", object_id)
                    start_time = timezone.now()
                    usa.change_status_to_extraction()
                    update_realtime_record(
                        realtime_task_id=realtime_task_id,
                        params={**realtime_record_data, **{"status": "EXTRACTION"}},
                    )
                    usa.update_extraction_details(
                        {
                            "start_time": start_time,
                            "status": "started",
                            "end_time": None,
                        }
                    )
                    if (
                        preprocessing_metadata
                        and preprocessing_metadata["strategy"]
                        == PreprocessingStrategy.SNAPSHOT_COMPARATOR.value
                    ):
                        logger.info(
                            "Skipping Extraction for %s as data extracted in preprocess step using %s",
                            str(integration_id),
                            PreprocessingStrategy.SNAPSHOT_COMPARATOR.value,
                        )
                    else:
                        ext_data = upstream_data_extractor(
                            client_id,
                            e2e_sync_run_id,
                            sync_run_id,
                            task,
                            object_id,
                            primary_kd,
                            sync_mode,
                            changes_start_time,
                            integration_id,
                            destination_object_id,
                            destination_object_type,
                            upstream_etl_version=UpstreamETLVersions.V1.value,
                        )
                    end_time = timezone.now()
                    usa.update_extraction_details(
                        {
                            "start_time": start_time,
                            "status": "completed",
                            "end_time": end_time,
                        }
                    )
                    logger.info("END: EXTRACTION - %s", object_id)
                    logger.info("BEGIN: TRANSFORMATION - %s", object_id)
                    # print("US.. EXT DATA.. {}".format(ext_data))
                    # Transformation #Use data from extraction logic
                    start_time = timezone.now()
                    usa.change_status_to_transformation()
                    update_realtime_record(
                        realtime_task_id=realtime_task_id,
                        params={**realtime_record_data, **{"status": "TRANSFORMATION"}},
                    )
                    usa.update_transformation_details(
                        {
                            "start_time": start_time,
                            "status": "started",
                            "end_time": None,
                        }
                    )
                    upstream_snowflake_sync = None
                    if run_snowflake_sync:
                        source_data_table = get_upstream_table_name(
                            client_id, integration_id
                        )
                        upstream_sync_meta = upstream_module.UpstreamSyncParams(
                            source_data_table=source_data_table,
                            custom_object_id=int(destination_object_id),
                            integration_id=integration_id,
                            sync_mode=sync_mode,
                            client_id=client_id,
                            e2e_sync_run_id=e2e_sync_run_id,
                            sync_run_id=sync_run_id,
                            changes_sync_time=timestamps["upstream_source_synced_till"],
                            is_source_data_as_variant=is_source_data_as_variant,
                            transformation_logic=transformation_logic,
                            primary_kd=primary_kd,
                        )
                        upstream_snowflake_sync = upstream_module.UpstreamSnowflakeSync(
                            upstream_sync_meta
                        )

                    if run_snowflake_sync and upstream_snowflake_sync:
                        upstream_snowflake_sync.transform()
                    else:
                        trans_data = upstream_transformer(
                            client_id,
                            e2e_sync_run_id,
                            sync_run_id,
                            object_id,
                            destination_object_id,
                            ext_data["result"] if ext_data else [],
                        )
                    end_time = timezone.now()
                    usa.update_transformation_details(
                        {
                            "start_time": start_time,
                            "status": "completed",
                            "end_time": end_time,
                        }
                    )
                    logger.info("END: TRANSFORMATION - %s", object_id)
                    logger.info("BEGIN: LOADING - %s", object_id)
                    # Loading
                    start_time = timezone.now()
                    usa.change_status_to_loading()
                    update_realtime_record(
                        realtime_task_id=realtime_task_id,
                        params={**realtime_record_data, **{"status": "LOADING"}},
                    )
                    usa.update_loading_details(
                        {
                            "start_time": start_time,
                            "status": "started",
                            "end_time": None,
                        }
                    )
                    if run_snowflake_sync and upstream_snowflake_sync:
                        upstream_snowflake_sync.load()
                    else:
                        upstream_loader(
                            client_id,
                            e2e_sync_run_id,
                            sync_run_id,
                            object_id,
                            destination_object_id,
                            trans_data,
                            primary_kd,
                            sync_mode,
                            destination_object_type,
                            ext_data["deleted_records"] if ext_data else [],
                            integration_id,
                        )
                        set_upstream_timestamps(
                            client_id,
                            integration_id,
                            {
                                "api_changes_synced_till": primary_kd,
                                "api_deletes_synced_till": primary_kd,
                                "upstream_source_synced_till": primary_kd,
                            },
                            str(e2e_sync_run_id),
                        )
                    end_time = timezone.now()
                    usa.update_loading_details(
                        {
                            "start_time": start_time,
                            "status": "completed",
                            "end_time": end_time,
                        }
                    )
                    logger.info("END: LOADING - %s", object_id)
                    if should_take_co_snapshot(client_id):
                        save_co_snapshot_to_sf_expanded_and_efs(
                            client_id, destination_object_id, str(primary_kd)
                        )
                        if should_insert_meta_data_to_vec_db(client_id):
                            upsert_co_meta_data_in_vector_db(
                                client_id, destination_object_id
                            )
                    # change status to completed
                    completion_time = timezone.now()
                    sync_run_log_update = None
                    # Add upstream_deleted_fields to sync_run_log only if it exists
                    if ext_data and ext_data["upstream_deleted_fields"]:
                        sync_run_log_update = {
                            **sync_run_log,
                            "end_time": str(completion_time),
                            "status": "completed",
                            "upstream_deleted_fields": ext_data[
                                "upstream_deleted_fields"
                            ],
                        }

                    # Common completion tasks
                    if usa.change_status_to_complete(
                        timestamp=completion_time, sync_run_log=sync_run_log_update  # type: ignore
                    ):
                        la.release_lock()  # relese lock in etl lock
                        update_realtime_record(
                            realtime_task_id=realtime_task_id,
                            params={
                                **realtime_record_data,
                                **{
                                    "status": "COMPLETE",
                                    "sync_end_time": str(end_time),
                                },
                            },
                        )

                        custom_object_pkd_map_accessor = CustomObjectPkdMapAccessor(
                            client_id
                        )
                        custom_object_pkd_map_accessor.upsert_custom_object_primary_kd(
                            int(destination_object_id), primary_kd, e2e_sync_run_id
                        )
                        logger.debug(
                            "Released Lock Name - %s Id - %s",
                            lock_name,
                            lock_obj.lock_id,
                        )
            else:
                existing_lock = la.get_lock_by_lock_name(lock_name).first()
                if existing_lock is not None and existing_lock.trigger != trigger:
                    logger.info(f"Sync in progress through {existing_lock.trigger}")
                    timestamp = make_aware(datetime.datetime.now())
                    sync_run_log["end_time"] = str(timestamp)
                    sync_run_log["status"] = "skipped"
                    sync_run_log["error_info"] = (
                        f"Skipped due to ongoing sync through {existing_lock.trigger}"
                    )
                    usa.change_status_to_skipped(timestamp, sync_run_log)
                    update_realtime_record(
                        realtime_task_id,
                        params={
                            **realtime_record_data,
                            **{"status": "COMPLETE", "sync_end_time": str(timestamp)},
                        },
                    )
                    logger.info("END: UPSTREAM SYNC - %s", object_id)
                    return
                failed_timestamp = timezone.now()
                sync_run_log["end_time"] = str(failed_timestamp)
                sync_run_log["status"] = "failed"
                sync_run_log["error_info"] = f"Lock failed: {object_id}"
                usa.change_status_failed_to_acquire_lock(
                    timestamp=failed_timestamp, sync_run_log=sync_run_log
                )
                update_realtime_record(
                    realtime_task_id=realtime_task_id,
                    params={
                        **realtime_record_data,
                        **{"status": "FAILED", "sync_end_time": str(failed_timestamp)},
                    },
                )
                etl_sync_status_service.change_status_to_failed(
                    client_id,
                    e2e_sync_run_id,
                    failed_timestamp,
                )
                logger.info("Failed to Acquire Lock - %s", lock_name)
        logger.info("END: UPSTREAM SYNC - %s", object_id)
    except Exception as err:
        error_handler = IntegrationErrorHandler(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            object_id=object_id,
            integration_id=integration_id,
        )
        parsed_error = error_handler.handle_integration_error(str(err))
        logger.info("Parsed error: %s", parsed_error)
        logger.exception("EXCEPTION: UPSTREAM SYNC. %s - %s", err, object_id)
        failed_timestamp = timezone.now()
        sync_run_log["end_time"] = str(failed_timestamp)
        sync_run_log["status"] = "failed"
        sync_run_log["error_info"] = f"Upstream sync failed for {object_id}"
        sync_run_log["ai_parsed_error"] = parsed_error
        # Get deleted fields from either ext_data or MissingFieldException
        deleted_fields = []
        if ext_data and "upstream_deleted_fields" in ext_data:
            deleted_fields = ext_data["upstream_deleted_fields"]
        elif isinstance(err, MissingFieldException) and hasattr(err, "deleted_fields"):
            deleted_fields = err.deleted_fields

        sync_run_log["upstream_deleted_fields"] = deleted_fields
        etl_sync_status_service.change_status_to_failed(
            client_id,
            e2e_sync_run_id,
            failed_timestamp,
        )
        update_realtime_record(
            realtime_task_id=realtime_task_id,
            params={
                **realtime_record_data,
                **{"status": "FAILED", "sync_end_time": str(failed_timestamp)},
            },
        )
        if lock_obj is not None:  # release
            usa.change_status_to_failed(
                timestamp=failed_timestamp, sync_run_log=sync_run_log
            )
            la = ETLLockAccessor(client_id, e2e_sync_run_id, sync_run_id)
            la.release_lock()
            logger.exception("EXCEP: UPSTREAM SYNC. Released Lock")
        else:
            logger.exception("EXCEP: UPSTREAM SYNC. Failed to Acquire Lock")


@shared_task(base=EverCeleryBaseTask)
def upstream_deleted_field_notification(  # noqa: PLR0912, PLR0915
    client_id: int, e2e_sync_run_id: UUID, log_context=None
):
    """
    Send notification about deleted fields in upstream sources.
    Optimized to minimize database calls by pre-fetching data into maps.
    """
    log_context_local = {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
    log_context = merge_log_context(log_context, log_context_local)
    set_threadlocal_context(log_context)
    try:
        logger.info("BEGIN: UPSTREAM DELETED FIELD NOTIFICATION")

        # Get all sync records for this run in one query
        sync_records = UpstreamETLStatusReaderAccessor(
            client_id
        ).get_records_by_e2e_sync_run_id(e2e_sync_run_id)

        # Build initial map of integration_id to deleted fields
        deleted_field_map = {}
        integration_ids = set()
        for obj in list(sync_records):
            if obj.sync_run_log and (obj.sync_run_log or {}).get(
                "upstream_deleted_fields"
            ):
                deleted_fields = (obj.sync_run_log or {}).get("upstream_deleted_fields")
                if deleted_fields:  # Only add if there are deleted fields
                    deleted_field_map[obj.integration_id] = deleted_fields
                    integration_ids.add(obj.integration_id)

        if not deleted_field_map:
            logger.info("No deleted fields found. Skipping notification.")
            return

        # Pre-fetch all required data in bulk
        custom_object_var_accessor = CustomObjectVariableAccessor(client_id)

        # Get all extraction configs for these integrations
        extraction_records = ExtractionConfigAccessor(
            client_id
        ).get_record_by_integration_id(list(integration_ids))
        extraction_map = {
            record.integration_id: record for record in extraction_records
        }

        # Get all destination object IDs
        destination_object_ids = {
            record.destination_object_id for record in extraction_records
        }

        # Build custom object map
        custom_object_map = {
            str(obj.custom_object_id): obj  # type: ignore
            for obj in CustomObjectAccessor(client_id).get_object_by_ids(
                list(destination_object_ids)
            )
        }

        # Get all access token configs
        access_token_ids = {
            record.access_token_config_id for record in extraction_records
        }
        access_token_map = {
            config.access_token_config_id: config
            for config in AccessTokenConfigAccessor(client_id).get_all_records()
            if config.access_token_config_id in access_token_ids
        }

        # Build transformation config map with proper handling of multiple mappings
        transformation_map = {}
        all_destination_fields = set()

        for integration_id in integration_ids:
            records = TransformationConfigAccessor(
                client_id
            ).get_records_by_integration_id(integration_id)

            # Use defaultdict to handle multiple mappings for the same source field
            source_to_dest_map = defaultdict(list)

            for record in records:
                # Handle _converted fields
                if record.source_field.endswith("_converted"):
                    original_field = record.additional_config.get(
                        "function_meta", {}
                    ).get("field_name", record.source_field)
                    source_to_dest_map[original_field].append(record.destination_field)
                # Handle api_field in additional_config
                elif record.additional_config and record.additional_config.get(
                    "api_field"
                ):
                    source_to_dest_map[
                        record.additional_config.get("api_field")
                    ].append(record.destination_field)
                # Standard case
                else:
                    source_to_dest_map[record.source_field].append(
                        record.destination_field
                    )
                # Collect all destination fields for later use
                all_destination_fields.add(record.destination_field)
            transformation_map[integration_id] = dict(source_to_dest_map)

        # Build display name map using the simpler method
        display_name_map = {}
        for obj_id in destination_object_ids:
            # Use get_display_names_from_system_names instead of get_display_names_from_system_names_with_data_type
            display_names = (
                custom_object_var_accessor.get_display_names_from_system_names(
                    obj_id, list(all_destination_fields)
                )
            )

            # Create a simpler map of system_name to display_name
            display_name_map[obj_id] = dict(display_names)

        # Build the final deleted fields array
        deleted_fields_array = []

        for integration_id, deleted_fields in deleted_field_map.items():
            extraction_record = extraction_map.get(integration_id)
            if not extraction_record:
                continue

            destination_object_id = extraction_record.destination_object_id
            custom_object = custom_object_map.get(destination_object_id)
            access_token_config = access_token_map.get(
                extraction_record.access_token_config_id
            )

            if not all([custom_object, access_token_config]):
                continue

            # Format field list with display names
            formatted_field_list = []
            source_to_dest_map = transformation_map.get(integration_id, {})
            display_names = display_name_map.get(destination_object_id, {})

            for source_field in deleted_fields:
                dest_fields = source_to_dest_map.get(source_field, [])

                if not dest_fields:
                    formatted_field_list.append(source_field)
                    continue

                # Handle multiple destination fields for a single source field
                field_display_names = []
                for dest_field in dest_fields:
                    display_name = display_names.get(dest_field, dest_field)
                    field_display_names.append(display_name)

                # Join multiple display names if needed
                if len(field_display_names) == 1:
                    formatted_field_list.append(
                        f"{field_display_names[0]} ({source_field})"
                    )
                else:
                    # Handle multiple mappings by adding source field to each display name
                    formatted_fields = [
                        f"{name} ({source_field})" for name in field_display_names
                    ]
                    formatted_field_list.append(", ".join(formatted_fields))

            if formatted_field_list:
                deleted_fields_array.append(
                    {
                        "connection_name": access_token_config.connection_name,  # type: ignore
                        "object_name": custom_object.name,  # type: ignore
                        "field_list": ", ".join(formatted_field_list),
                    }
                )

        # Send notification if we have formatted deleted fields
        if deleted_fields_array:
            send_upstream_deleted_field_notification(
                client_id, e2e_sync_run_id, deleted_fields_array
            )
            logger.info(
                "SENDING UPSTREAM DELETED FIELD NOTIFICATION - %s", deleted_fields_array
            )

        logger.info("END: UPSTREAM DELETED FIELD NOTIFICATION")
    except Exception:
        logger.exception("UPSTREAM DELETED FIELD NOTIFICATION EXCEPTION")
