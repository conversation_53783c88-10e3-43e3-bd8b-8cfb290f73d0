from commission_engine.accessors.client_accessor import (
    get_commission_report_strategy,
    get_commission_summary_report_strategy,
    get_quota_attainment_report_strategy,
    get_settlement_report_strategy,
)
from commission_engine.utils.general_data import ModuleType, ReportObject


def can_run_snowflake_report_etl(client_id, object_id):
    if object_id in (
        ReportObject.COMMISSION.value,
        ReportObject.INTER_COMMISSION.value,
    ):
        return get_commission_report_strategy(client_id) == "snowflake"
    elif object_id in (
        ReportObject.QUOTA_ATTAINMENT.value,
        ReportObject.INTER_QUOTA_ATTAINMENT.value,
    ):
        return get_quota_attainment_report_strategy(client_id) == "snowflake"
    elif object_id == ReportObject.SETTLEMENT.value:
        return get_settlement_report_strategy(client_id) == "snowflake"
    elif object_id == ReportObject.COMMISSION_SUMMARY.value:
        return get_commission_summary_report_strategy(client_id) == "snowflake"

    # Since payout report is supported only in snowflake, return True if object_id is PAYOUT
    elif object_id == ReportObject.PAYOUT.value:
        return True
    elif object_id == ReportObject.STATEMENT_APPROVALS.value:
        return True
    elif object_id in ReportObject.values(ModuleType.CPQ.value):
        return True

    return False


def get_report_object_data_table_name(client_id: int | str, object_id: str) -> str:
    """
    Returns the report object data table name based on client_id and object_id.
    """
    table_name = f"report_object_data_{client_id}_{object_id}"
    return table_name


def resolve_report_object_table_name(
    client_id: int | str, object_id: str, table_name: str
) -> str:
    """
    Returns the object table name based on client_id, object_id and table_name.
    """
    if table_name == "report_object_data":
        return get_report_object_data_table_name(client_id, object_id)
    elif table_name == "system_object_data":
        return get_system_object_data_table_name(client_id, object_id)
    elif table_name == "forecast_object_data":
        return get_forecast_object_data_table_name(client_id, object_id)
    elif table_name == "inter_object_data":
        return get_inter_object_data_table_name(client_id, object_id)
    elif table_name == "inter_forecast_object_data":
        return get_inter_forecast_object_data_table_name(client_id, object_id)
    else:
        raise ValueError(f"Invalid table name: {table_name}")


def get_system_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the system object table name based on client_id and object_id.
    """
    table_name = f"system_object_data_{client_id}_{object_id}"
    return table_name


def get_forecast_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the forecast object table name based on client_id and object_id.
    """
    table_name = f"forecast_object_data_{client_id}_{object_id}"
    return table_name


def get_inter_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the inter object table name based on client_id and object_id.
    """
    table_name = f"inter_object_data_{client_id}_{object_id}"
    return table_name


def get_inter_forecast_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the inter forecast object table name based on client_id and object_id.
    """
    table_name = f"inter_forecast_object_data_{client_id}_{object_id}"
    return table_name
