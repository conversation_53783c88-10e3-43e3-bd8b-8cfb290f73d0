import json
from unittest.mock import patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.models.etl_config_models import (
    AccessTokenConfig,
    ApiAccessConfig,
    ExtractionConfig,
    Integration,
    TransformationConfig,
)
from commission_engine.third_party_connections.bullhorn import Bullhorn
from everstage_ddd.upstream.extraction.data_lake import UpstreamChangesWriter
from everstage_etl.tests.connectors.utils import create_upstream_table_utility
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from . import test_data


@pytest.fixture
def integration():
    # Delete any existing integration with the same ID first
    Integration.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    # Create the integration record for standard objects testing
    integration_obj = Integration.objects.create(
        client_id=test_data.CLIENT_ID,
        knowledge_begin_date=test_data.PRIMARY_KD,
        integration_id=test_data.INTEGRATION_ID,
        service_name="bullhorn",
        source_object_id="bullhorn-placement",
        is_api=True,
        destination_object_id=test_data.DESTINATION_OBJECT_ID,
        additional_data={
            "run_snowflake_sync": True,
            "changes_sync_field": "dateLastModified",
        },
    )

    yield integration_obj


@pytest.fixture
def integration_process_lines():
    # Delete any existing integration with the same ID first
    Integration.objects.filter(
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID
    ).delete()

    # Create the integration record for process lines testing
    integration_obj = Integration.objects.create(
        client_id=test_data.CLIENT_ID,
        knowledge_begin_date=test_data.PRIMARY_KD,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
        service_name="bullhorn",
        source_object_id="bullhorn-joborder-pl",
        is_api=True,
        destination_object_id=test_data.PROCESS_LINES_DESTINATION_OBJECT_ID,
        additional_data={
            "run_snowflake_sync": True,
            "changes_sync_field": "dateLastModified",
        },
    )

    yield integration_obj


@pytest.fixture
def extraction_config():
    # Delete any existing config first
    ExtractionConfig.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    config = ExtractionConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="bullhorn-placement",
        sync_type="changes",  # This is used by UpstreamChangesWriter.save()
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )

    yield config


@pytest.fixture
def extraction_config_process_lines():
    # Delete any existing config first
    ExtractionConfig.objects.filter(
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID
    ).delete()

    config = ExtractionConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
        source_object_id="bullhorn-joborder-pl",
        sync_type="changes",  # This is used by UpstreamChangesWriter.save()
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )

    yield config


@pytest.fixture
def transformation_configs_standard():
    # Delete any existing configs first
    TransformationConfig.objects.filter(
        integration_id=test_data.INTEGRATION_ID
    ).delete()

    # Field mappings for standard Placement object
    field_mappings = {
        "id": "id",
        "dateBegin": "date_begin",
        "dateLastModified": "date_last_modified",
        "fee": "fee",
        "flatFee": "flat_fee",
        "employmentType": "employment_type",
        "status": "status",
        "customInt4": "custom_int_4",
        "customInt10": "custom_int_10",
        "customDate1": "custom_date_1",
        "customDate2": "custom_date_2",
        "customText8": "custom_text_8",
        "candidate.id": "candidateid",
        "candidate.firstName": "candidatefirst_name",
        "candidate.lastName": "candidatelast_name",
        "clientCorporation.id": "client_corporationid",
        "clientCorporation.name": "client_corporationname",
        "jobOrder.id": "job_orderid",
        "jobOrder.title": "job_ordertitle",
        "owner.id": "ownerid",
    }
    created_configs = []
    for source_field, dest_field_suffix in field_mappings.items():
        created_configs.append(
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.INTEGRATION_ID,
                source_object_id="bullhorn-placement",
                destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
                source_field=source_field,
                destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",  # Defaulting to string for simplicity
            )
        )

    yield created_configs


@pytest.fixture
def transformation_configs_process_lines():
    # Delete any existing configs first
    TransformationConfig.objects.filter(
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID
    ).delete()

    # Field mappings for process lines (JobOrder with submissions)
    field_mappings = {
        "id": "id",
        "LineId": "line_id",
        "submission.id": "submissionid",
    }
    created_configs = []
    for source_field, dest_field_suffix in field_mappings.items():
        # Handle special configuration for LineId and submission.id
        additional_config = None
        if source_field == "LineId":
            additional_config = {"api_field": "id"}
        elif source_field == "submission.id":
            additional_config = {"api_field": "submissions"}

        created_configs.append(
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
                source_object_id="bullhorn-joborder-pl",
                destination_object_id=str(
                    test_data.PROCESS_LINES_DESTINATION_OBJECT_ID
                ),
                source_field=source_field,
                destination_field=f"co_{test_data.PROCESS_LINES_DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",  # Defaulting to string for simplicity
                additional_config=additional_config,
            )
        )

    yield created_configs


@pytest.fixture
def upstream_changes_writer():
    return UpstreamChangesWriter(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
    )


@pytest.fixture
def upstream_changes_writer_process_lines():
    return UpstreamChangesWriter(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
    )


@pytest.mark.django_db
@pytest.fixture
def access_token_config():
    AccessTokenConfig.objects.filter(
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID
    ).delete()

    return AccessTokenConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        service_name="bullhorn",
        access_type="REFRESH",
        access_token_url="https://auth.bullhornstaffing.com/oauth",  # noqa: S106
        access_request_body={
            "grant_type": "refresh_token",
            "refresh_token": "test_refresh_token",
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
        },
        additional_data={
            "bh_rest_url": "https://rest42.bullhornstaffing.com/rest-services/e999/login"
        },
    )


@pytest.mark.django_db
@pytest.fixture
def api_access_config_standard(access_token_config):
    return ApiAccessConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="bullhorn-placement",
        request_url="https://rest42.bullhornstaffing.com/rest-services/6allo9/query/Placement",
        request_type="post",
        request_body={
            "where": "dateLastModified > {start} AND dateLastModified < {end}"
        },
        request_header={
            "BhRestToken": "{bull_horn_rest_token}",
            "Content-type": "application/json",
        },
        additional_data={
            "id_key": "id",
        },
        response_key="data",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )


@pytest.mark.django_db
@pytest.fixture
def api_access_config_process_lines(access_token_config):
    return ApiAccessConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
        source_object_id="bullhorn-joborder-pl",
        request_url="https://rest42.bullhornstaffing.com/rest-services/6allo9/query/JobOrder",
        request_type="post",
        request_body={
            "where": "dateLastModified > {start} AND dateLastModified < {end} AND isDeleted = false"
        },
        request_header={
            "BhRestToken": "{bull_horn_rest_token}",
            "Content-type": "application/json",
        },
        additional_data={
            "id_key": "id",
            "list_id": "id",
            "result_key": "id",
            "composite_id": ["id"],
            "process_list": True,
            "new_list_name": "submission",
            "source_list_name": "submissions.data",
        },
        response_key="data",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )


@pytest.fixture
def create_upstream_table_standard():
    """Create the upstream table with the correct schema before test runs"""
    yield from create_upstream_table_utility(
        test_data.CLIENT_ID, test_data.INTEGRATION_ID
    )


@pytest.fixture
def create_upstream_table_process_lines():
    """Create the upstream table with the correct schema before test runs"""
    yield from create_upstream_table_utility(
        test_data.CLIENT_ID, test_data.PROCESS_LINES_INTEGRATION_ID
    )


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestBullhornExtraction:
    @patch(
        "commission_engine.third_party_connections.utils.check_primary_or_snapshot_key_deleted"
    )
    @patch(
        "commission_engine.third_party_connections.bullhorn.Bullhorn.all_fields_for_config_object"
    )
    @patch(
        "commission_engine.third_party_connections.bullhorn.Bullhorn.acquire_bullhorn_rest_token"
    )
    @patch("commission_engine.third_party_connections.bullhorn.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.bullhorn.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_get_changed_records_standard_objects_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        mock_acquire_rest_token,
        mock_all_fields_for_config_object,
        mock_check_primary_or_snapshot_key_deleted,
        integration,
        extraction_config,
        api_access_config_standard,
        transformation_configs_standard,
        create_upstream_table_standard,
    ):
        """Test Bullhorn extraction for standard objects (Placement) with pagination"""
        # Mock the token acquisition
        mock_acquire_rest_token.return_value = "fake_bullhorn_rest_token"

        # Mock field validation methods
        mock_all_fields_for_config_object.return_value = [
            {"name": "id", "label": "ID"},
            {"name": "dateBegin", "label": "Date Begin"},
            {"name": "fee", "label": "Fee"},
            {"name": "clientCorporation", "label": "Client Corporation"},
        ]
        mock_check_primary_or_snapshot_key_deleted.return_value = False

        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.BULLHORN_PLACEMENT_API_RESPONSE_PAGE_1,
            test_data.BULLHORN_PLACEMENT_API_RESPONSE_PAGE_2,
            {"count": 0, "start": 2, "data": []},
        ]
        mock_get_source_key_fields.return_value = (["data:id"], ["data:id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Bullhorn().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="bullhorn-placement",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=api_access_config_standard,
            _unused_snapshot_key="id",
            _unused_destination_object_type="Table",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Assert API was called twice for pagination
        assert mock_api_call.call_count == 3

        # Query the Snowflake table to verify data was written
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert correct number of records were written (3 total records)
        assert len(result_df) == 3

        # Build expected DataFrame
        expected_records = []

        # Combine all API responses and deduplicate by id
        all_api_records = []
        all_api_records.extend(test_data.BULLHORN_PLACEMENT_API_RESPONSE_PAGE_1["data"])
        all_api_records.extend(test_data.BULLHORN_PLACEMENT_API_RESPONSE_PAGE_2["data"])

        # Deduplicate by id (though in this test we don't have duplicates)
        unique_records = {record["id"]: record for record in all_api_records}

        for raw_record in unique_records.values():
            expected_snowflake_record = next(
                (
                    record
                    for record in test_data.EXPECTED_SNOWFLAKE_DATA_STANDARD
                    if record["id"] == raw_record["id"]
                ),
                None,
            )

            expected_records.append(
                {
                    "ROW_KEY": str(raw_record["id"]),
                    "IS_DELETED": False,
                    "DATA": json.dumps(expected_snowflake_record, sort_keys=True),
                }
            )

        expected_df = (
            pd.DataFrame(expected_records).sort_values("ROW_KEY").reset_index(drop=True)
        )

        # Prepare result DataFrame
        result_df.columns = result_df.columns.str.upper()
        result_df["UPDATED_ON"] = pd.to_datetime(result_df["UPDATED_ON"], utc=True)
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values("ROW_KEY").reset_index(drop=True)

        # Compare DataFrames
        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.utils.check_primary_or_snapshot_key_deleted"
    )
    @patch(
        "commission_engine.third_party_connections.bullhorn.Bullhorn.all_fields_for_config_object"
    )
    @patch(
        "commission_engine.third_party_connections.bullhorn.Bullhorn.acquire_bullhorn_rest_token"
    )
    @patch("commission_engine.third_party_connections.bullhorn.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.bullhorn.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_get_changed_records_process_lines_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        mock_acquire_rest_token,
        mock_all_fields_for_config_object,
        mock_check_primary_or_snapshot_key_deleted,
        integration_process_lines,
        extraction_config_process_lines,
        api_access_config_process_lines,
        transformation_configs_process_lines,
        create_upstream_table_process_lines,
    ):
        """Test Bullhorn extraction for process lines objects (JobOrder with submissions)"""
        # Mock the token acquisition
        mock_acquire_rest_token.return_value = "fake_bullhorn_rest_token"

        # Mock field validation methods
        mock_all_fields_for_config_object.return_value = [
            {"name": "id", "label": "ID"},
            {"name": "submissions", "label": "Submissions"},
        ]
        mock_check_primary_or_snapshot_key_deleted.return_value = False

        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.BULLHORN_JOBORDER_PROCESSLINES_API_RESPONSE_PAGE_1,
            test_data.BULLHORN_JOBORDER_PROCESSLINES_API_RESPONSE_PAGE_2,
            {"count": 0, "start": 2, "data": []},
        ]
        mock_get_source_key_fields.return_value = (["data:LineId"], ["data:LineId"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Bullhorn().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="bullhorn-joborder-pl",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=api_access_config_process_lines,
            _unused_snapshot_key="LineId",
            _unused_destination_object_type="Table",
            integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
            is_validation=False,
        )

        # Assert API was called twice for pagination
        assert mock_api_call.call_count == 3

        # Query the Snowflake table to verify data was written
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.PROCESS_LINES_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert correct number of records were written (3 submission records from 2 job orders)
        assert len(result_df) == 3

        # Build expected DataFrame for process lines
        expected_records = []

        # Process the expected records - each submission becomes a separate record
        expected_records = [
            {
                "ROW_KEY": record["LineId"],
                "IS_DELETED": False,
                "DATA": json.dumps(record, sort_keys=True),
            }
            for record in test_data.EXPECTED_SNOWFLAKE_DATA_PROCESSLINES
        ]

        expected_df = (
            pd.DataFrame(expected_records).sort_values("ROW_KEY").reset_index(drop=True)
        )

        # Prepare result DataFrame
        result_df.columns = result_df.columns.str.upper()
        result_df["UPDATED_ON"] = pd.to_datetime(result_df["UPDATED_ON"], utc=True)
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values("ROW_KEY").reset_index(drop=True)

        # Compare DataFrames
        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.accessors.custom_object_accessor.CustomObjectAccessor.get_primary_keys"
    )
    @patch(
        "commission_engine.third_party_connections.utils.check_primary_or_snapshot_key_deleted"
    )
    @patch(
        "commission_engine.third_party_connections.bullhorn.Bullhorn.all_fields_for_config_object"
    )
    @patch(
        "commission_engine.third_party_connections.bullhorn.Bullhorn.acquire_bullhorn_rest_token"
    )
    @patch("commission_engine.third_party_connections.bullhorn.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.bullhorn.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_get_deleted_records_standard_objects(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        mock_acquire_rest_token,
        mock_all_fields_for_config_object,
        mock_check_primary_or_snapshot_key_deleted,
        mock_get_primary_keys,
        integration,
        extraction_config,
        api_access_config_standard,
        transformation_configs_standard,
        create_upstream_table_standard,
    ):
        """Test Bullhorn deletion for standard objects"""
        # Mock the token acquisition
        mock_acquire_rest_token.return_value = "fake_bullhorn_rest_token"

        # Mock field validation methods
        mock_all_fields_for_config_object.return_value = [
            {"name": "id", "label": "ID"},
        ]
        mock_check_primary_or_snapshot_key_deleted.return_value = False
        mock_get_primary_keys.return_value = {
            "primary_key": [f"co_{test_data.DESTINATION_OBJECT_ID}_id"]
        }

        # Create delete config
        delete_config = ApiAccessConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="bullhorn-placement-delete",
            request_url="https://rest42.bullhornstaffing.com/rest-services/6allo9/query/Placement",
            request_type="post",
            request_body={
                "where": "dateLastModified > {start} AND dateLastModified < {end} AND isDeleted = true"
            },
            request_header={
                "BhRestToken": "{bull_horn_rest_token}",
                "Content-type": "application/json",
            },
            additional_data={
                "id_key": "id",
            },
            response_key="data",
            access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        )

        # Mock the API responses
        mock_api_call.side_effect = [
            test_data.BULLHORN_DELETED_RECORDS_RESPONSE_PAGE_1,
            {"count": 0, "start": 2, "data": []},
        ]
        mock_get_source_key_fields.return_value = (["data:id"], ["data:id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        bullhorn_connector = Bullhorn()
        bullhorn_connector.get_deleted_records(
            client_id=test_data.CLIENT_ID,
            object_id="bullhorn-placement-delete",
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="delete",
            del_config_obj=delete_config,
            _unused_destination_object_type="Table",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Assert API was called once
        assert mock_api_call.call_count == 2

        # Query Snowflake to verify deleted records were written
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert correct number of deleted records were written (2 records)
        assert len(result_df) == 2
