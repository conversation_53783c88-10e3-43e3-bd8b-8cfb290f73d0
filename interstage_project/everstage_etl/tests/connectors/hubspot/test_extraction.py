import json
from unittest.mock import patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.third_party_connections.hubspot import <PERSON><PERSON><PERSON>
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from . import test_data
from .test_fixture import (
    associations_api_access_config,
    associations_enrichment_configs,
    associations_extraction_config,
    associations_integration_config,
    associations_ref_api_access_config,
    associations_transformation_configs,
    create_associations_upstream_table,
    create_deals_upstream_table,
    create_owners_upstream_table,
    create_pipeline_stages_upstream_table,
    create_pipelines_upstream_table,
    deals_api_access_config,
    deals_extraction_config,
    deals_integration,
    deals_transformation_configs,
    owners_api_access_config,
    owners_extraction_config,
    owners_integration,
    owners_transformation_configs,
    pipeline_stages_api_access_config,
    pipeline_stages_extraction_config,
    pipeline_stages_integration_config,
    pipeline_stages_transformation_configs,
    pipelines_api_access_config,
    pipelines_extraction_config,
    pipelines_integration_config,
    pipelines_transformation_configs,
)


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestHubspotExtraction:
    @patch("commission_engine.third_party_connections.hubspot.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_deals_search_api_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        deals_integration,
        deals_extraction_config,
        deals_api_access_config,
        deals_transformation_configs,
        create_deals_upstream_table,
    ):
        """Test HubSpot Deals extraction using Search API with pagination and deduplication"""
        # Mock the API responses for pagination with duplicate records
        mock_api_call.side_effect = [
            {
                "results": test_data.HUBSPOT_DEALS_API_RESPONSE_PAGE_1,
                "paging": {"next": {"after": "2"}},
            },
            {
                "results": test_data.HUBSPOT_DEALS_API_RESPONSE_PAGE_2,
                "paging": {},  # No next page
            },
        ]

        mock_get_source_key_fields.return_value = (
            ["data:hs_object_id"],
            ["data:hs_object_id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Hubspot().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="deals",
            _unused_primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=deals_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.DEALS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.DEALS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records despite having a duplicate in the API response
        assert len(result_df) == len(test_data.EXPECTED_DEALS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = (
            test_data.HUBSPOT_DEALS_API_RESPONSE_PAGE_1
            + test_data.HUBSPOT_DEALS_API_RESPONSE_PAGE_2
        )
        unique_raw_records = {r["id"]: r for r in raw_records}.values()

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_DEALS_SNOWFLAKE_DATA, key=lambda x: x["hs_object_id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["hs_object_id"]
            raw_record = next(r for r in unique_raw_records if r["id"] == record_id)
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record["properties"], sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("commission_engine.third_party_connections.hubspot.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_owners_list_api_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        owners_integration,
        owners_extraction_config,
        owners_api_access_config,
        owners_transformation_configs,
        create_owners_upstream_table,
    ):
        """Test HubSpot Owners extraction using List API with pagination and deduplication"""
        # Mock the API responses for pagination with duplicate records
        mock_api_call.side_effect = [
            {
                "results": test_data.HUBSPOT_OWNERS_API_RESPONSE_PAGE_1,
                "paging": {"next": {"after": "2"}},
            },
            {
                "results": test_data.HUBSPOT_OWNERS_API_RESPONSE_PAGE_2,
                "paging": {},  # No next page
            },
        ]

        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Hubspot().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="owners",
            _unused_primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=owners_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.OWNERS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.OWNERS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records despite having a duplicate in the API response
        assert len(result_df) == len(test_data.EXPECTED_OWNERS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = (
            test_data.HUBSPOT_OWNERS_API_RESPONSE_PAGE_1
            + test_data.HUBSPOT_OWNERS_API_RESPONSE_PAGE_2
        )
        unique_raw_records = {r["id"]: r for r in raw_records}.values()

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_OWNERS_SNOWFLAKE_DATA, key=lambda x: x["id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["id"]
            raw_record = next(r for r in unique_raw_records if r["id"] == record_id)
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("commission_engine.third_party_connections.hubspot.ThirdPartyApi.api_call")
    @patch(
        "commission_engine.third_party_connections.hubspot.ApiAccessConfigAccessor.get_obj_by_integration_id_and_source_id"
    )
    def test_hubspot_associations_extraction_with_snowflake_integration(  # noqa: PLR0913
        self,
        mock_get_obj_by_integration_id_and_source_id,
        mock_api_call,
        mock_get_source_key_fields,
        mock_get_upstream_timestamps,
        associations_integration_config,
        associations_extraction_config,
        associations_api_access_config,
        associations_transformation_configs,
        associations_enrichment_configs,
        associations_ref_api_access_config,
        create_associations_upstream_table,
    ):
        """Test HubSpot associations extraction with Snowflake integration."""
        # Mock the primary and snapshot keys
        mock_get_source_key_fields.return_value = (
            ["data:hs_object_id"],
            ["data:hs_object_id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_get_obj_by_integration_id_and_source_id.return_value = (
            associations_ref_api_access_config
        )

        mock_api_call.side_effect = [
            {
                "results": test_data.HUBSPOT_DEALS_API_RESPONSE_PAGE_1,
                "paging": {"next": {"after": "2"}},
            },
            {
                "results": test_data.HUBSPOT_ASSOCIATIONS_CONTACTS_API_RESPONSE_PAGE_1,
                "paging": {"next": {"after": "2"}},
            },
            {
                "results": test_data.HUBSPOT_ASSOCIATIONS_CONTACTS_API_RESPONSE_PAGE_2,
                "paging": {},
            },
            {
                "results": test_data.HUBSPOT_DEALS_API_RESPONSE_PAGE_2,
                "paging": {},
            },
        ]

        # Execute the method under test
        Hubspot().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="deals",
            _unused_primary_kd=None,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=associations_api_access_config,
            _unused_snapshot_key=None,
            _unused_destination_object_type=None,
            integration_id=test_data.ASSOCIATIONS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that data was written to the Snowflake table
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.ASSOCIATIONS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        assert len(result_df) == 3  # 3 unique records after deduplication

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_ASSOCIATIONS_SNOWFLAKE_DATA,
            key=lambda x: x["hs_object_id"],
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["hs_object_id"]
            expected_rows.append(
                {
                    "ROW_KEY": record_id.lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(transformed_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("commission_engine.third_party_connections.hubspot.ThirdPartyApi.api_call")
    @patch(
        "commission_engine.third_party_connections.hubspot.ApiAccessConfigAccessor.get_obj_by_integration_id_and_source_id"
    )
    def test_hubspot_pipelines_extraction_with_snowflake_integration(  # noqa: PLR0913
        self,
        mock_get_obj_by_integration_id_and_source_id,
        mock_api_call,
        mock_get_source_key_fields,
        mock_get_upstream_timestamps,
        pipelines_integration_config,
        pipelines_extraction_config,
        pipelines_api_access_config,
        pipelines_transformation_configs,
        create_pipelines_upstream_table,
    ):
        """Test HubSpot pipelines extraction with Snowflake integration."""
        # Mock the primary and snapshot keys
        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_get_obj_by_integration_id_and_source_id.return_value = (
            pipelines_api_access_config
        )

        mock_api_call.side_effect = [
            {
                "results": test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_1,
                "paging": {"next": {"after": "2"}},
            },
            {
                "results": test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_2,
                "paging": {},
            },
        ]

        # Execute the method under test
        result, deleted_fields = Hubspot().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="pipelines_deals",
            _unused_primary_kd=None,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=pipelines_api_access_config,
            _unused_snapshot_key=None,
            _unused_destination_object_type=None,
            integration_id=test_data.PIPELINES_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify the result
        assert result == []  # When using snowflake_uploader, result should be empty
        assert deleted_fields == []

        # Verify that data was written to the Snowflake table
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.PIPELINES_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        assert len(result_df) == 3  # 3 unique records after deduplication

        # Verify deduplication worked (should have 3 unique records)
        raw_records = (
            test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_1
            + test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_2
        )
        unique_raw_records = {r["id"]: r for r in raw_records}.values()

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_PIPELINES_SNOWFLAKE_DATA, key=lambda x: x["id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["id"]
            # Find the corresponding raw record
            raw_record = next(
                (r for r in unique_raw_records if r["id"] == record_id), None
            )
            if raw_record:
                expected_rows.append(
                    {
                        "ROW_KEY": raw_record["id"].lower(),
                        "IS_DELETED": False,
                        "DATA": json.dumps(raw_record, sort_keys=True),
                    }
                )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("commission_engine.third_party_connections.hubspot.ThirdPartyApi.api_call")
    def test_hubspot_pipeline_stages_extraction_with_snowflake_integration(  # noqa: PLR0913
        self,
        mock_api_call,
        mock_get_source_key_fields,
        mock_get_upstream_timestamps,
        pipeline_stages_integration_config,
        pipeline_stages_extraction_config,
        pipeline_stages_api_access_config,
        pipeline_stages_transformation_configs,
        create_pipeline_stages_upstream_table,
    ):
        """Test HubSpot pipeline stages extraction with Snowflake integration."""

        # Mock the primary and snapshot keys
        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }

        # Mock API responses for pipeline stages
        # Pipeline stages API returns pipeline data, then stages are extracted via get_stages_records
        mock_api_call.side_effect = [
            {
                "results": test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_1,
                "paging": {"next": {"after": "2"}},
            },
            {"results": test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_2, "paging": {}},
        ]

        # Execute the method under test
        result, deleted_fields = Hubspot().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="pipeline_stages_deals",
            _unused_primary_kd=None,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=pipeline_stages_api_access_config,
            _unused_snapshot_key=None,
            _unused_destination_object_type=None,
            integration_id=test_data.PIPELINE_STAGES_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify the result
        assert result == []  # When using snowflake_uploader, result should be empty
        assert deleted_fields == []

        # Verify that data was written to the Snowflake table
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.PIPELINE_STAGES_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        assert len(result_df) == 3  # 3 unique stage records after processing

        # Verify stages data processing
        # The get_stages_records method extracts stages from pipeline data and adds pipeline_id
        expected_stages = []
        for pipeline in (
            test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_1
            + test_data.HUBSPOT_PIPELINES_API_RESPONSE_PAGE_2
        ):
            for stage in pipeline.get("stages", []):
                stage_copy = stage.copy()
                stage_copy["pipeline_id"] = pipeline["id"]
                expected_stages.append(stage_copy)

        expected_rows = [
            {
                "ROW_KEY": stage["id"].lower(),
                "IS_DELETED": False,
                "DATA": json.dumps(stage, sort_keys=True),
            }
            for stage in expected_stages
        ]

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )
