import json
from unittest.mock import patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.models.etl_config_models import (
    ApiAccessConfig,
    ExtractionConfig,
    Integration,
    TransformationConfig,
)
from commission_engine.third_party_connections.salesforce import Salesforce
from everstage_ddd.upstream.extraction.data_lake import UpstreamChangesWriter
from everstage_etl.tests.connectors.utils import create_upstream_table_utility
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from . import test_data


@pytest.fixture
def integration():
    # Delete any existing integration with the same ID first
    Integration.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    # Create the integration record
    integration_obj = Integration.objects.create(
        client_id=test_data.CLIENT_ID,
        knowledge_begin_date=test_data.PRIMARY_KD,
        integration_id=test_data.INTEGRATION_ID,
        service_name="salesforce",
        source_object_id="opportunity",
        is_api=True,
        destination_object_id=test_data.DESTINATION_OBJECT_ID,
        additional_data={
            "run_snowflake_sync": True,
            "delete_sync_field": "IsDeleted",
            "changes_sync_field": "LastModifiedDate",
        },
    )

    yield integration_obj


@pytest.fixture
def extraction_config(integration):
    # Delete any existing config first
    ExtractionConfig.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    config = ExtractionConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="opportunity",
        sync_type="changes",  # This is used by UpstreamChangesWriter.save()
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )

    yield config


@pytest.fixture
def transformation_configs(integration):
    # Delete any existing configs first
    TransformationConfig.objects.filter(
        integration_id=test_data.INTEGRATION_ID
    ).delete()

    field_mappings = {
        "Id": "opportunity_id",
        "Name": "name",
        "Amount": "amount",
        "CloseDate": "close_date",
        "StageName": "stage",
        "LastModifiedDate": "last_modified_date",
        "Owner.Name": "owner_name",
        "Description": "description",
        "IsDeleted": "deleted",
        "AccountId": "account_id",
        "OwnerId": "owner_id",
    }
    created_configs = []
    for source_field, dest_field_suffix in field_mappings.items():
        created_configs.append(
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.INTEGRATION_ID,
                source_object_id="opportunity",
                destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
                source_field=source_field,
                destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",  # Defaulting to string for simplicity
            )
        )

    yield created_configs


@pytest.fixture
def upstream_changes_writer(integration):
    return UpstreamChangesWriter(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
    )


@pytest.mark.django_db
@pytest.fixture
def api_access_config():
    return ApiAccessConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="opportunity",
        request_url="https://everstage-2e-dev-ed.develop.my.salesforce.com/services/data/v58.0/query",
        request_body={
            "q": "select {fields} from {sobject} where LastModifiedDate >= '{start}' and LastModifiedDate < '{end}' {offset_condition} order by {order_by_field}"
        },
        additional_data={
            "order_by_field": "Id",
            "limit_value": 3,  # Set low for pagination test
            "instance_url": "https://everstage-2e-dev-ed.develop.my.salesforce.com",
            "api_type": "query",
        },
        response_key="records",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )


@pytest.fixture
def create_upstream_table():
    """Create the upstream table with the correct schema before test runs"""
    yield from create_upstream_table_utility(
        test_data.CLIENT_ID, test_data.INTEGRATION_ID
    )


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestSalesforceExtraction:
    @patch(
        "commission_engine.third_party_connections.salesforce.ThirdPartyApi.api_call"
    )
    @patch(
        "commission_engine.third_party_connections.salesforce.get_primary_and_snapshot_keys_for_integration"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.salesforce.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_get_changed_records_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_primary_and_snapshot_keys,
        mock_api_call,
        integration,
        extraction_config,
        api_access_config,
        transformation_configs,
        create_upstream_table,
    ):
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            {
                "records": test_data.SALESFORCE_API_RESPONSE_PAGE_1,
                "done": False,
                "totalSize": 4,  # Total size including duplicates
                "nextRecordsUrl": "/services/data/v52.0/query/01g5i00000J5pLwAAJ-2",
            },
            {
                "records": test_data.SALESFORCE_API_RESPONSE_PAGE_2,
                "done": True,
                "totalSize": 2,
            },
        ]
        mock_get_primary_and_snapshot_keys.return_value = (
            ["Id"],
            ["Id"],
        )
        # Since data is not flattened, we need to use JSON path syntax
        mock_get_source_key_fields.return_value = (["data:Id"], ["data:Id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        salesforce_connector = Salesforce()
        salesforce_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="opportunity",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records
        assert len(result_df) == len(test_data.EXPECTED_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = (
            test_data.SALESFORCE_API_RESPONSE_PAGE_1
            + test_data.SALESFORCE_API_RESPONSE_PAGE_2
        )
        unique_raw_records = {r["Id"]: r for r in raw_records}.values()

        expected_rows = []
        # Ensure consistent order for comparison
        sorted_transformed_records = sorted(
            test_data.EXPECTED_SNOWFLAKE_DATA, key=lambda x: x["Id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["Id"]
            raw_record = next(r for r in unique_raw_records if r["Id"] == record_id)
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["Id"].lower(),
                    "IS_DELETED": raw_record["IsDeleted"],
                    "UPDATED_ON": pd.to_datetime(test_data.PRIMARY_KD),
                    "DATA": json.dumps(transformed_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["UPDATED_ON"] = pd.to_datetime(result_df["UPDATED_ON"]).dt.tz_convert(
            "UTC"
        )
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]  # Ensure column order

        assert_frame_equal(
            expected_df,
            result_df,
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.salesforce.ThirdPartyApi.api_call"
    )
    @patch(
        "commission_engine.third_party_connections.salesforce.get_primary_and_snapshot_keys_for_integration"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.salesforce.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_query_api_with_standard_fields(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_primary_and_snapshot_keys,
        mock_api_call,
        integration,
        extraction_config,
        transformation_configs,
        create_upstream_table,
    ):
        """
        Test the Query API with standard fields extraction.
        """
        # Create API access config for Query API
        api_access_config = ApiAccessConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity",
            request_url="https://everstage-2e-dev-ed.develop.my.salesforce.com/services/data/v58.0/query",
            request_body={
                "q": "select {fields} from {sobject} where SystemModstamp >= '{start}' and SystemModstamp <= '{end}' {offset_condition} order by {order_by_field} limit {limit_value}"
            },
            additional_data={
                "order_by_field": "Id",
                "limit_value": 2000,
                "instance_url": "https://everstage-2e-dev-ed.develop.my.salesforce.com",
                "api_type": "query",
            },
            response_key="records",
            access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        )

        # Mock standard fields API response
        mock_api_call.return_value = {
            "records": [
                {
                    "Id": "0065i00000J5pLwAAJ",
                    "Name": "Test Opportunity",
                    "Amount": 100000.0,
                    "CloseDate": "2024-06-30",
                    "StageName": "Closed Won",
                    "SystemModstamp": "2024-01-15T10:00:00.000+0000",
                    "IsDeleted": False,
                    "AccountId": "0015i00000J5pLwAAJ",
                    "LastModifiedDate": "2024-01-15T10:00:00.000+0000",
                    "Owner": {"Name": "John Doe"},
                    "Description": "Test opportunity description",
                    "OwnerId": "0055i00000J5pLwAAJ",
                    "attributes": {
                        "type": "Opportunity",
                        "url": "/services/data/v52.0/sobjects/Opportunity/0065i00000J5pLwAAJ",
                    },
                }
            ],
            "done": True,
            "totalSize": 1,
        }

        mock_get_primary_and_snapshot_keys.return_value = (["Id"], ["Id"])
        mock_get_source_key_fields.return_value = (["data:Id"], ["data:Id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        salesforce_connector = Salesforce()
        salesforce_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="opportunity",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called once
        assert mock_api_call.call_count == 1

        # Validate data written to Snowflake table
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        assert len(result_df) == 1

        # Verify the record was written with correct structure
        assert result_df.iloc[0]["ROW_KEY"] == "0065i00000j5plwaaj"
        assert result_df.iloc[0]["IS_DELETED"] == False  # noqa: E712

        # Parse and verify the data content
        data_content = json.loads(result_df.iloc[0]["DATA"])
        assert "Id" in data_content
        assert "Name" in data_content
        assert "Amount" in data_content
        assert data_content["Id"] == "0065i00000J5pLwAAJ"
        assert data_content["Name"] == "Test Opportunity"

    @patch(
        "commission_engine.third_party_connections.salesforce.ThirdPartyApi.api_call"
    )
    @patch(
        "commission_engine.third_party_connections.salesforce.get_primary_and_snapshot_keys_for_integration"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.salesforce.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_query_all_api_for_deleted_records(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_primary_and_snapshot_keys,
        mock_api_call,
        integration,
        extraction_config,
        transformation_configs,
        create_upstream_table,
    ):
        """
        Test the QueryAll API for retrieving deleted records.
        """
        # First, add a record to Snowflake that we'll later mark as deleted
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"

        # Insert a record that exists before deletion using dataframe
        initial_record_data = {
            "Id": "0065i00000J5pLwAAJ",
            "Name": "Test Opportunity to be deleted",
            "IsDeleted": False,
        }

        # Use dataframe to insert the record
        from snowflake.snowpark.types import (
            BooleanType,
            StringType,
            StructField,
            StructType,
            TimestampType,
            VariantType,
        )

        test_record = [
            {
                "KNOWLEDGE_BEGIN_DATE": test_data.PRIMARY_KD,
                "KNOWLEDGE_END_DATE": None,
                "ROW_KEY": "0065i00000j5plwaaj",
                "UPDATED_ON": test_data.PRIMARY_KD,
                "IS_DELETED": False,
                "DATA": initial_record_data,
            }
        ]

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            schema = StructType(
                [
                    StructField("KNOWLEDGE_BEGIN_DATE", TimestampType()),
                    StructField("KNOWLEDGE_END_DATE", TimestampType()),
                    StructField("ROW_KEY", StringType()),
                    StructField("UPDATED_ON", TimestampType()),
                    StructField("IS_DELETED", BooleanType()),
                    StructField("DATA", VariantType()),
                ]
            )

            data_frame = session.create_dataframe(data=test_record, schema=schema)
            data_frame.write.mode("append").save_as_table(  # type: ignore
                table_name, column_order="name"
            )
            session.sql("COMMIT").collect()

        # Create API access config for QueryAll API (for deleted records)
        api_access_config = ApiAccessConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity_delete",
            request_url="https://everstage-2e-dev-ed.develop.my.salesforce.com/services/data/v58.0/queryAll",
            request_body={
                "q": "select {fields} from {sobject} where isDeleted = true and SystemModstamp >= '{start}' and SystemModstamp <= '{end}' {offset_condition} order by {order_by_field} limit {limit_value}"
            },
            additional_data={
                "order_by_field": "Id",
                "limit_value": 2000,
                "instance_url": "https://everstage-2e-dev-ed.develop.my.salesforce.com",
                "api_type": "queryAll",
            },
            response_key="records",
            access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        )

        # Mock deleted records API response
        mock_api_call.return_value = {
            "records": [
                {
                    "Id": "0065i00000J5pLwAAJ",
                    "IsDeleted": True,
                    "SystemModstamp": "2024-01-15T10:00:00.000+0000",
                }
            ],
            "done": True,
            "totalSize": 1,
        }

        mock_get_primary_and_snapshot_keys.return_value = (["Id"], ["Id"])
        mock_get_source_key_fields.return_value = (["data:Id"], ["data:Id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        salesforce_connector = Salesforce()
        salesforce_connector.get_deleted_records(
            client_id=test_data.CLIENT_ID,
            object_id="opportunity",
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            primary_kd=test_data.PRIMARY_KD,
            delete_start_time=test_data.CHANGES_START_TIME,
            sync_mode="delete",
            config_object=api_access_config,
            _unused_destination_object_type="",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Validate data in Snowflake table - should now have the deleted record
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name} WHERE ROW_KEY = '0065i00000j5plwaaj' AND IS_DELETED = true"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        assert len(result_df) == 1

        # Verify the record was marked as deleted
        assert result_df.iloc[0]["ROW_KEY"] == "0065i00000j5plwaaj"
        assert result_df.iloc[0]["IS_DELETED"] == True  # noqa: E712

        # Parse and verify the data content for deleted record
        data_content = json.loads(result_df.iloc[0]["DATA"])
        assert data_content["Id"] == "0065i00000J5pLwAAJ"
        assert data_content["IsDeleted"] == True  # noqa: E712

    @patch(
        "commission_engine.third_party_connections.salesforce.SalesforceSelfServiceIntegration"
    )
    def test_convert_currency_applied_fields(
        self,
        mock_salesforce_self_service,
        integration,
        extraction_config,
    ):
        """
        Test handling of convertCurrency applied fields in transformation configs.
        """
        # Create transformation config with convertCurrency function
        TransformationConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity",
            destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
            source_field="Amount_converted",
            destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_amount_converted",
            field_type="Integer",
            additional_config={
                "api_field": "convertCurrency(Amount) Amount_converted",
                "function_meta": {
                    "field_name": "Amount",
                    "function_name": "convertCurrency",
                },
            },
        )

        # Mock the self-service integration
        mock_service_instance = mock_salesforce_self_service.return_value
        mock_service_instance.get_all_fields_in_object.return_value = [
            {"name": "Amount"},
            {"name": "Id"},
        ]

        salesforce_connector = Salesforce()

        # Test field verification with convertCurrency
        fields = salesforce_connector.get_fields_for_object(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            sobject="opportunity",
            is_validation=False,
        )

        # Should include the convertCurrency function call
        assert "convertCurrency(Amount) Amount_converted" in fields

    @patch(
        "commission_engine.third_party_connections.salesforce.SalesforceSelfServiceIntegration"
    )
    def test_lookup_fields_handling(
        self,
        mock_salesforce_self_service,
        integration,
        extraction_config,
    ):
        """
        Test handling of lookup fields like Owner.Name, Account.Id etc.
        """
        # Create transformation configs with lookup fields
        lookup_field_mappings = {
            "Owner.Name": "owner_name",
            "Account.Id": "account_id",
            "Contact.Email": "contact_email",
            "Opportunity.StageName": "opportunity_stage",
        }

        for source_field, dest_field_suffix in lookup_field_mappings.items():
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.INTEGRATION_ID,
                source_object_id="opportunity",
                destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
                source_field=source_field,
                destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",
            )

        # Mock the self-service integration
        mock_service_instance = mock_salesforce_self_service.return_value
        mock_service_instance.get_all_fields_in_object.return_value = [
            {"name": "Owner.Name"},
            {"name": "Account.Id"},
            {"name": "Contact.Email"},
            {"name": "Opportunity.StageName"},
            {"name": "Id"},
        ]

        salesforce_connector = Salesforce()

        # Test field verification with lookup fields
        fields = salesforce_connector.get_fields_for_object(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            sobject="opportunity",
            is_validation=False,
        )

        # Should include all lookup fields
        for lookup_field in lookup_field_mappings:
            assert lookup_field in fields

    def test_date_format_handling_in_transformation_config(
        self,
        integration,
        extraction_config,
    ):
        """
        Test date format configuration in transformation configs for Date fields.
        """
        # Create transformation config with date format
        date_transformation_config = TransformationConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity",
            destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
            source_field="LastModifiedDate",
            destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_last_modified_date",
            field_type="Date",
            additional_config={"date_format": ["YYYY-MM-DDTHH24:MI:SS.FF3TZHTZM"]},
        )

        # Create another date field with different format
        close_date_config = TransformationConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity",
            destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
            source_field="CloseDate",
            destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_close_date",
            field_type="Date",
            additional_config={"date_format": ["YYYY-MM-DD"]},
        )

        # Verify the date format configurations are properly set
        assert date_transformation_config.additional_config["date_format"] == [
            "YYYY-MM-DDTHH24:MI:SS.FF3TZHTZM"
        ]
        assert close_date_config.additional_config["date_format"] == ["YYYY-MM-DD"]
        assert date_transformation_config.field_type == "Date"
        assert close_date_config.field_type == "Date"

    @patch(
        "commission_engine.third_party_connections.salesforce.ThirdPartyApi.api_call"
    )
    @patch(
        "commission_engine.third_party_connections.salesforce.get_primary_and_snapshot_keys_for_integration"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.salesforce.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_complex_field_combinations(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_primary_and_snapshot_keys,
        mock_api_call,
        integration,
        extraction_config,
        create_upstream_table,
    ):
        """
        Test extraction with complex field combinations including standard fields,
        lookup fields, converted currency fields, and date fields.
        """
        # Create transformation configs with mixed field types
        complex_field_mappings = [
            # Standard field
            {
                "source_field": "Id",
                "destination_field": f"co_{test_data.DESTINATION_OBJECT_ID}_opportunity_id",
                "field_type": "String",
                "additional_config": None,
            },
            # Lookup field
            {
                "source_field": "Owner.Name",
                "destination_field": f"co_{test_data.DESTINATION_OBJECT_ID}_owner_name",
                "field_type": "String",
                "additional_config": None,
            },
            # Currency conversion field
            {
                "source_field": "Amount_converted",
                "destination_field": f"co_{test_data.DESTINATION_OBJECT_ID}_amount_converted",
                "field_type": "Integer",
                "additional_config": {
                    "api_field": "convertCurrency(Amount) Amount_converted",
                    "function_meta": {
                        "field_name": "Amount",
                        "function_name": "convertCurrency",
                    },
                },
            },
            # Date field with format
            {
                "source_field": "LastModifiedDate",
                "destination_field": f"co_{test_data.DESTINATION_OBJECT_ID}_last_modified_date",
                "field_type": "Date",
                "additional_config": {
                    "date_format": ["YYYY-MM-DDTHH24:MI:SS.FF3TZHTZM"]
                },
            },
        ]

        # Create API access config
        api_access_config = ApiAccessConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity",
            request_url="https://everstage-2e-dev-ed.develop.my.salesforce.com/services/data/v58.0/query",
            request_body={
                "q": "select {fields} from {sobject} where SystemModstamp >= '{start}' and SystemModstamp <= '{end}' {offset_condition} order by {order_by_field} limit {limit_value}"
            },
            additional_data={
                "order_by_field": "Id",
                "limit_value": 2000,
                "instance_url": "https://everstage-2e-dev-ed.develop.my.salesforce.com",
                "api_type": "query",
            },
            response_key="records",
            access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        )

        # Mock API response with complex field data
        mock_api_call.return_value = {
            "records": [
                {
                    "Id": "0065i00000J5pLwAAJ",
                    "Owner": {"Name": "John Doe"},
                    "Amount_converted": 150000,
                    "LastModifiedDate": "2024-01-15T10:00:00.000+0000",
                    "attributes": {
                        "type": "Opportunity",
                        "url": "/services/data/v52.0/sobjects/Opportunity/0065i00000J5pLwAAJ",
                    },
                }
            ],
            "done": True,
            "totalSize": 1,
        }

        mock_get_primary_and_snapshot_keys.return_value = (["Id"], ["Id"])
        mock_get_source_key_fields.return_value = (["data:Id"], ["data:Id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        salesforce_connector = Salesforce()
        salesforce_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="opportunity",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called with all field types
        assert mock_api_call.call_count == 1

        # Validate data written to Snowflake table
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        assert len(result_df) == 1

        # Verify the record was written with correct structure
        assert result_df.iloc[0]["ROW_KEY"] == "0065i00000j5plwaaj"
        assert result_df.iloc[0]["IS_DELETED"] == False  # noqa: E712

        # Parse and verify the data content contains all field types
        data_content = json.loads(result_df.iloc[0]["DATA"])

        # Standard field
        assert data_content["Id"] == "0065i00000J5pLwAAJ"
        # Lookup field
        assert data_content["Owner"]["Name"] == "John Doe"
        # Currency conversion field
        assert data_content["Amount_converted"] == 150000
        # Date field
        assert data_content["LastModifiedDate"] == "2024-01-15T10:00:00.000+0000"

        # Clean up the test-specific transformation configs
        TransformationConfig.objects.filter(
            integration_id=test_data.INTEGRATION_ID,
            source_field__in=[
                mapping["source_field"] for mapping in complex_field_mappings
            ],
        ).delete()

    @patch(
        "commission_engine.third_party_connections.salesforce.ThirdPartyApi.api_call"
    )
    @patch(
        "commission_engine.third_party_connections.salesforce.get_primary_and_snapshot_keys_for_integration"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.salesforce.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_pagination_with_offset_handling(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_primary_and_snapshot_keys,
        mock_api_call,
        integration,
        extraction_config,
        transformation_configs,
        create_upstream_table,
    ):
        """
        Test pagination handling with offset conditions in Salesforce Query API.
        """
        # Create API access config with small limit for pagination
        api_access_config = ApiAccessConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="opportunity",
            request_url="https://everstage-2e-dev-ed.develop.my.salesforce.com/services/data/v58.0/query",
            request_body={
                "q": "select {fields} from {sobject} where SystemModstamp >= '{start}' and SystemModstamp <= '{end}' {offset_condition} order by {order_by_field} limit {limit_value}"
            },
            additional_data={
                "order_by_field": "Id",
                "limit_value": 2,  # Small limit to force pagination
                "instance_url": "https://everstage-2e-dev-ed.develop.my.salesforce.com",
                "api_type": "query",
            },
            response_key="records",
            access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        )

        # Mock paginated API responses
        mock_api_call.side_effect = [
            # First page
            {
                "records": [
                    {
                        "Id": "0065i00000J5pLwAAJ",
                        "Name": "Test Opportunity 1",
                        "attributes": {"type": "Opportunity"},
                    },
                    {
                        "Id": "0065i00000J5pLxAAK",
                        "Name": "Test Opportunity 2",
                        "attributes": {"type": "Opportunity"},
                    },
                ],
                "done": False,
                "totalSize": 3,
            },
            # Second page
            {
                "records": [
                    {
                        "Id": "0065i00000J5pLyAAL",
                        "Name": "Test Opportunity 3",
                        "attributes": {"type": "Opportunity"},
                    }
                ],
                "done": True,
                "totalSize": 1,
            },
        ]

        mock_get_primary_and_snapshot_keys.return_value = (["Id"], ["Id"])
        mock_get_source_key_fields.return_value = (["data:Id"], ["data:Id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        salesforce_connector = Salesforce()
        salesforce_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="opportunity",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called twice for pagination
        assert mock_api_call.call_count == 2

        # Validate data written to Snowflake table
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name} ORDER BY ROW_KEY"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # Should have 3 records from pagination
        assert len(result_df) == 3

        # Verify all records were written correctly
        expected_ids = [
            "0065i00000j5plwaaj",
            "0065i00000j5plxaak",
            "0065i00000j5plyaal",
        ]
        actual_ids = sorted(result_df["ROW_KEY"].tolist())
        assert actual_ids == expected_ids

        # Verify all records are not deleted
        assert all(not deleted for deleted in result_df["IS_DELETED"])

        # Parse and verify the data content for each record
        for _, row in result_df.iterrows():
            data_content = json.loads(row["DATA"])
            assert "Id" in data_content
            assert "Name" in data_content
            assert data_content["Name"].startswith("Test Opportunity")
