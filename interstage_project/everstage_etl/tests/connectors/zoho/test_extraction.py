import json
from unittest.mock import patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.third_party_connections.zoho import <PERSON><PERSON><PERSON>
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from . import test_data
from .test_fixture import (
    accounts_api_access_config,
    accounts_extraction_config,
    accounts_integration,
    accounts_transformation_configs,
    analytics_api_access_config,
    analytics_extraction_config,
    analytics_integration,
    analytics_transformation_configs,
    create_accounts_upstream_table,
    create_analytics_upstream_table,
    create_deals_upstream_table,
    create_enriched_deals_upstream_table,
    create_quotes_upstream_table,
    deals_api_access_config,
    deals_extraction_config,
    deals_integration,
    deals_transformation_configs,
    enriched_deals_api_access_config,
    enriched_deals_enrichment_configs,
    enriched_deals_extraction_config,
    enriched_deals_integration,
    enriched_deals_ref_api_access_config,
    enriched_deals_transformation_configs,
    quotes_api_access_config,
    quotes_extraction_config,
    quotes_integration,
    quotes_transformation_configs,
)


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestZohoExtraction:
    @patch("commission_engine.third_party_connections.zoho.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_accounts_list_api_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        accounts_integration,
        accounts_extraction_config,
        accounts_api_access_config,
        accounts_transformation_configs,
        create_accounts_upstream_table,
    ):
        """Test Zoho Accounts extraction using List API with pagination and deduplication (standard fields)"""
        # Mock the API responses for pagination with duplicate records
        mock_api_call.side_effect = [
            {
                "data": test_data.ZOHO_ACCOUNTS_API_RESPONSE_PAGE_1,
                "info": {"more_records": True, "count": 2},
            },
            {
                "data": test_data.ZOHO_ACCOUNTS_API_RESPONSE_PAGE_2,
                "info": {"more_records": False, "count": 2},
            },
        ]

        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Zoho().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Accounts",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=accounts_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.ACCOUNTS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.ACCOUNTS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records despite having a duplicate in the API response
        assert len(result_df) == len(test_data.EXPECTED_ACCOUNTS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = (
            test_data.ZOHO_ACCOUNTS_API_RESPONSE_PAGE_1
            + test_data.ZOHO_ACCOUNTS_API_RESPONSE_PAGE_2
        )
        unique_raw_records = {r["id"]: r for r in raw_records}.values()

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_ACCOUNTS_SNOWFLAKE_DATA, key=lambda x: x["id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["id"]
            raw_record = next(r for r in unique_raw_records if r["id"] == record_id)
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("commission_engine.third_party_connections.zoho.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_deals_list_api_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        deals_integration,
        deals_extraction_config,
        deals_api_access_config,
        deals_transformation_configs,
        create_deals_upstream_table,
    ):
        """Test Zoho Deals extraction using List API with pagination and deduplication (standard fields)"""
        # Mock the API responses for pagination with duplicate records
        mock_api_call.side_effect = [
            {
                "data": test_data.ZOHO_DEALS_API_RESPONSE_PAGE_1,
                "info": {"more_records": True, "count": 2},
            },
            {
                "data": test_data.ZOHO_DEALS_API_RESPONSE_PAGE_2,
                "info": {"more_records": False, "count": 2},
            },
        ]

        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Zoho().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Deals",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=deals_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.DEALS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.DEALS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records despite having a duplicate in the API response
        assert len(result_df) == len(test_data.EXPECTED_DEALS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = (
            test_data.ZOHO_DEALS_API_RESPONSE_PAGE_1
            + test_data.ZOHO_DEALS_API_RESPONSE_PAGE_2
        )
        unique_raw_records = {r["id"]: r for r in raw_records}.values()

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_DEALS_SNOWFLAKE_DATA, key=lambda x: x["id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["id"]
            raw_record = next(r for r in unique_raw_records if r["id"] == record_id)
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("commission_engine.third_party_connections.zoho.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_quotes_process_lines_api_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        quotes_integration,
        quotes_extraction_config,
        quotes_api_access_config,
        quotes_transformation_configs,
        create_quotes_upstream_table,
    ):
        """Test Zoho Quotes extraction using List API with process_list functionality (process lines fields)"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            {
                "data": test_data.ZOHO_QUOTES_API_RESPONSE_PAGE_1,
                "info": {"more_records": True, "count": 2},
            },
            {
                "data": test_data.ZOHO_QUOTES_API_RESPONSE_PAGE_2,
                "info": {"more_records": False, "count": 1},
            },
        ]

        mock_get_source_key_fields.return_value = (
            ["data:LineId"],
            ["data:LineId"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Zoho().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Quotes",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=quotes_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.QUOTES_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.QUOTES_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 processed line items from the quotes
        assert len(result_df) == len(test_data.EXPECTED_QUOTES_SNOWFLAKE_DATA)

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_QUOTES_SNOWFLAKE_DATA, key=lambda x: x["LineId"]
        )
        for transformed_record in sorted_transformed_records:
            line_id = transformed_record["LineId"]
            expected_rows.append(
                {
                    "ROW_KEY": line_id.lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(transformed_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("commission_engine.third_party_connections.zoho.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_analytics_list_api_writes_to_snowflake_with_zoho_analytics_flag(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        analytics_integration,
        analytics_extraction_config,
        analytics_api_access_config,
        analytics_transformation_configs,
        create_analytics_upstream_table,
    ):
        """Test Zoho Analytics extraction using List API with zoho_analytics_integration flag"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            {
                "data": test_data.ZOHO_ANALYTICS_API_RESPONSE_PAGE_1,
                "info": {"more_records": True, "count": 2},
            },
            {
                "data": test_data.ZOHO_ANALYTICS_API_RESPONSE_PAGE_2,
                "info": {"more_records": False, "count": 1},
            },
        ]

        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Zoho().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Analytics_Reports",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=analytics_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.ANALYTICS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.ANALYTICS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records
        assert len(result_df) == len(test_data.EXPECTED_ANALYTICS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = (
            test_data.ZOHO_ANALYTICS_API_RESPONSE_PAGE_1
            + test_data.ZOHO_ANALYTICS_API_RESPONSE_PAGE_2
        )

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_ANALYTICS_SNOWFLAKE_DATA, key=lambda x: x["id"]
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["id"]
            raw_record = next(r for r in raw_records if r["id"] == record_id)
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch("commission_engine.third_party_connections.zoho.ThirdPartyApi.api_call")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    @patch(
        "commission_engine.third_party_connections.enrichment.ApiAccessConfigAccessor"
    )
    def test_enriched_deals_extraction_with_user_enrichment(  # noqa: PLR0913
        self,
        mock_api_access_config_accessor,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        enriched_deals_integration,
        enriched_deals_extraction_config,
        enriched_deals_api_access_config,
        enriched_deals_ref_api_access_config,
        enriched_deals_transformation_configs,
        enriched_deals_enrichment_configs,
        create_enriched_deals_upstream_table,
    ):
        """Test Zoho enriched deals extraction with user enrichment"""
        # Mock the ApiAccessConfigAccessor
        mock_accessor_instance = mock_api_access_config_accessor.return_value
        mock_accessor_instance.get_obj_by_integration_id_and_source_id.return_value = (
            enriched_deals_ref_api_access_config
        )

        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        # Mock the API responses for deals and users
        mock_api_call.side_effect = [
            {
                "data": test_data.ZOHO_ENRICHED_DEALS_API_RESPONSE_PAGE_1,
                "info": {"count": 2, "more_records": True, "page": 1},
            },
            {
                "users": test_data.ZOHO_USERS_API_RESPONSE_PAGE_1,
                "info": {"count": 2, "more_records": True, "page": 1},
            },
            {
                "data": test_data.ZOHO_ENRICHED_DEALS_API_RESPONSE_PAGE_2,
                "info": {"count": 1, "more_records": False, "page": 2},
            },
        ]

        Zoho().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Deals",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=enriched_deals_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.ENRICHED_DEALS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called the correct number of times (deals + users enrichments)
        assert mock_api_call.call_count == 3

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.ENRICHED_DEALS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique deals after deduplication and enrichment
        assert len(result_df) == 3

        # Prepare expected data - deals enriched with user details
        raw_deals = (
            test_data.ZOHO_ENRICHED_DEALS_API_RESPONSE_PAGE_1
            + test_data.ZOHO_ENRICHED_DEALS_API_RESPONSE_PAGE_2
        )
        raw_users = test_data.ZOHO_USERS_API_RESPONSE_PAGE_1

        # Create user lookup
        user_lookup = {user["id"]: user for user in raw_users}

        expected_rows = []
        for deal in raw_deals:
            # Enrich deal with user details
            owner_id = deal["Owner"]["id"]
            enriched_deal = deal.copy()
            if owner_id in user_lookup:
                enriched_deal["Owner_Details"] = user_lookup[owner_id]

            expected_rows.append(
                {
                    "ROW_KEY": deal["id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(enriched_deal, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )
