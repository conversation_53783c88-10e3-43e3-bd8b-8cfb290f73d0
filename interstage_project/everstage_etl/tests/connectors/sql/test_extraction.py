import json
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.third_party_connections.sql import Sql

from . import test_data
from .test_fixture import (
    create_products_table_upstream_table,
    create_quoted_fields_upstream_table,
    create_sales_table_upstream_table,
    create_suiteanalytics_upstream_table,
    products_table_api_access_config,
    products_table_extraction_config,
    products_table_integration,
    products_table_transformation_configs,
    quoted_fields_api_access_config,
    quoted_fields_extraction_config,
    quoted_fields_integration,
    quoted_fields_transformation_configs,
    sales_table_api_access_config,
    sales_table_extraction_config,
    sales_table_integration,
    sales_table_transformation_configs,
    suiteanalytics_api_access_config,
    suiteanalytics_extraction_config,
    suiteanalytics_integration,
    suiteanalytics_transformation_configs,
)


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestSqlExtraction:
    """Test cases for SQL connector extraction functionality"""

    @patch("commission_engine.third_party_connections.sql.Sql.get_connector")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_sales_table_standard_fields_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_connector,
        sales_table_integration,
        sales_table_extraction_config,
        sales_table_api_access_config,
        sales_table_transformation_configs,
        create_sales_table_upstream_table,
    ):
        """Test SQL extraction for standard fields scenario with deduplication"""

        # Setup mocks
        mock_create_upstream_table_if_not_exists.return_value = None
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )

        # Mock database connector
        mock_connector = MagicMock()
        mock_connector.connect.return_value = None
        mock_connector.disconnect.return_value = None
        mock_connector.get_object_and_schema_name_from_object_id.return_value = (
            "table_1",
            "test_new_schema__75",
        )
        mock_connector.get_all_fields_in_table.return_value = [
            {"name": "id"},
            {"name": "name"},
            {"name": "amount"},
            {"name": "is_deleted"},
            {"name": "created_date"},
        ]
        mock_connector.query_data.side_effect = [
            test_data.SQL_SALES_TABLE_RESPONSE_PAGE_1,
            test_data.SQL_SALES_TABLE_RESPONSE_PAGE_2,
        ]
        mock_get_connector.return_value = mock_connector

        # Create SQL connector and call get_changed_records
        sql_connector = Sql()
        result, deleted_fields = sql_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="test_new_schema__75.table_1",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=sales_table_api_access_config,
            snapshot_key="id",
            destination_object_type="custom_object",
            integration_id=test_data.SALES_TABLE_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify database connector was called correctly
        assert mock_connector.query_data.call_count == 2  # 2 pages
        assert len(deleted_fields) == 0  # No deleted fields expected

        # Query Snowflake to validate data
        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            table_name = create_sales_table_upstream_table
            query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608
            result_df = session.sql(query).to_pandas()

            assert not result_df.empty
            assert len(result_df) == len(test_data.EXPECTED_SALES_TABLE_SNOWFLAKE_DATA)

            # Prepare expected data
            raw_records = (
                test_data.SQL_SALES_TABLE_RESPONSE_PAGE_1
                + test_data.SQL_SALES_TABLE_RESPONSE_PAGE_2
            )
            unique_raw_records = {r["id"]: r for r in raw_records}.values()

            expected_rows = []
            sorted_transformed_records = sorted(
                test_data.EXPECTED_SALES_TABLE_SNOWFLAKE_DATA, key=lambda x: x["id"]
            )
            for transformed_record in sorted_transformed_records:
                record_id = transformed_record["id"]
                raw_record = next(
                    r
                    for r in unique_raw_records
                    if r["id"].upper() == record_id.upper()
                )
                expected_rows.append(
                    {
                        "ROW_KEY": str(record_id),
                        "IS_DELETED": False,
                        "DATA": json.dumps(raw_record, sort_keys=True),
                    }
                )

            expected_df = pd.DataFrame(expected_rows)
            expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

            # Prepare result data for comparison
            result_df.columns = list(map(str.upper, result_df.columns))
            result_df["DATA"] = result_df["DATA"].apply(
                lambda x: json.dumps(json.loads(x), sort_keys=True)
            )
            result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
            result_df = result_df[expected_df.columns]

            # Assert DataFrames are equal
            assert_frame_equal(
                expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                check_like=True,
            )

    @patch("commission_engine.third_party_connections.sql.Sql.get_connector")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_products_table_formatted_fields_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_connector,
        products_table_integration,
        products_table_extraction_config,
        products_table_api_access_config,
        products_table_transformation_configs,
        create_products_table_upstream_table,
    ):
        """Test SQL extraction for formatted fields scenario"""

        # Setup mocks
        mock_create_upstream_table_if_not_exists.return_value = None
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )

        # Mock database connector
        mock_connector = MagicMock()
        mock_connector.connect.return_value = None
        mock_connector.disconnect.return_value = None
        mock_connector.get_object_and_schema_name_from_object_id.return_value = (
            "table_1",
            "test_new_schema__75",
        )
        mock_connector.get_all_fields_in_table.return_value = [
            {"name": "id"},
            {"name": "name"},
            {"name": "amount_1"},
            {"name": "is_deleted"},
            {"name": "created_date"},
            {"name": "calmonth"},
        ]
        mock_connector.query_data.side_effect = [
            test_data.SQL_PRODUCTS_TABLE_RESPONSE_PAGE_1,
            test_data.SQL_PRODUCTS_TABLE_RESPONSE_PAGE_2,
        ]
        mock_get_connector.return_value = mock_connector

        # Create SQL connector and call get_changed_records
        sql_connector = Sql()
        result, deleted_fields = sql_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="test_new_schema__75.table_1",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=products_table_api_access_config,
            snapshot_key="id",
            destination_object_type="custom_object",
            integration_id=test_data.PRODUCTS_TABLE_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify database connector was called correctly
        assert mock_connector.query_data.call_count == 2  # 2 pages
        assert len(deleted_fields) == 0  # No deleted fields expected

        # Query Snowflake to validate data
        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            table_name = create_products_table_upstream_table
            query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608
            result_df = session.sql(query).to_pandas()

            assert not result_df.empty
            assert len(result_df) == len(
                test_data.EXPECTED_PRODUCTS_TABLE_SNOWFLAKE_DATA
            )

            # Prepare expected data
            raw_records = (
                test_data.SQL_PRODUCTS_TABLE_RESPONSE_PAGE_1
                + test_data.SQL_PRODUCTS_TABLE_RESPONSE_PAGE_2
            )
            unique_raw_records = {r["id"]: r for r in raw_records}.values()

            expected_rows = []
            sorted_transformed_records = sorted(
                test_data.EXPECTED_PRODUCTS_TABLE_SNOWFLAKE_DATA, key=lambda x: x["id"]
            )
            for transformed_record in sorted_transformed_records:
                record_id = transformed_record["id"]
                raw_record = next(r for r in unique_raw_records if r["id"] == record_id)
                expected_rows.append(
                    {
                        "ROW_KEY": str(record_id),
                        "IS_DELETED": False,
                        "DATA": json.dumps(raw_record, sort_keys=True),
                    }
                )

            expected_df = pd.DataFrame(expected_rows)
            expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

            # Prepare result data for comparison
            result_df.columns = list(map(str.upper, result_df.columns))
            result_df["DATA"] = result_df["DATA"].apply(
                lambda x: json.dumps(json.loads(x), sort_keys=True)
            )
            result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
            result_df = result_df[expected_df.columns]

            assert_frame_equal(
                expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                check_like=True,
            )

    @patch("commission_engine.third_party_connections.sql.Sql.get_connector")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_quoted_fields_table_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_connector,
        quoted_fields_integration,
        quoted_fields_extraction_config,
        quoted_fields_api_access_config,
        quoted_fields_transformation_configs,
        create_quoted_fields_upstream_table,
    ):
        """Test SQL extraction for quoted fields scenario with double quotes"""

        # Setup mocks
        mock_create_upstream_table_if_not_exists.return_value = None
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_get_source_key_fields.return_value = (
            ["data:id"],
            ["data:id"],
        )

        # Mock database connector
        mock_connector = MagicMock()
        mock_connector.connect.return_value = None
        mock_connector.disconnect.return_value = None
        mock_connector.get_object_and_schema_name_from_object_id.return_value = (
            "table_1",
            "qa_schema_sun",
        )
        mock_connector.get_all_fields_in_table.return_value = [
            {"name": "id"},
            {"name": "ALLCAPSCOLUMN"},
            {"name": "Column With Space"},
            {"name": "special %24@ !!"},
            {"name": "ALL CAPS WITH SPACE"},
        ]
        mock_connector.query_data.side_effect = [
            test_data.SQL_QUOTED_FIELDS_RESPONSE_PAGE_1,
            test_data.SQL_QUOTED_FIELDS_RESPONSE_PAGE_2,
        ]
        mock_get_connector.return_value = mock_connector

        # Create SQL connector and call get_changed_records
        sql_connector = Sql()
        result, deleted_fields = sql_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id='qa_schema_sun."table_1"',
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=quoted_fields_api_access_config,
            snapshot_key="id",
            destination_object_type="custom_object",
            integration_id=test_data.QUOTED_FIELDS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify database connector was called correctly
        assert mock_connector.query_data.call_count == 2  # 2 pages
        assert len(deleted_fields) == 0  # No deleted fields expected

        # Query Snowflake to validate data
        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            table_name = create_quoted_fields_upstream_table
            query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608
            result_df = session.sql(query).to_pandas()

            assert not result_df.empty
            assert len(result_df) == len(
                test_data.EXPECTED_QUOTED_FIELDS_SNOWFLAKE_DATA
            )

            # Prepare expected data
            raw_records = (
                test_data.SQL_QUOTED_FIELDS_RESPONSE_PAGE_1
                + test_data.SQL_QUOTED_FIELDS_RESPONSE_PAGE_2
            )
            unique_raw_records = {r["id"]: r for r in raw_records}.values()

            expected_rows = []
            sorted_transformed_records = sorted(
                test_data.EXPECTED_QUOTED_FIELDS_SNOWFLAKE_DATA, key=lambda x: x["id"]
            )
            for transformed_record in sorted_transformed_records:
                record_id = transformed_record["id"]
                raw_record = next(r for r in unique_raw_records if r["id"] == record_id)
                expected_rows.append(
                    {
                        "ROW_KEY": str(record_id),
                        "IS_DELETED": False,
                        "DATA": json.dumps(raw_record, sort_keys=True),
                    }
                )

            expected_df = pd.DataFrame(expected_rows)
            expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

            # Prepare result data for comparison
            result_df.columns = list(map(str.upper, result_df.columns))
            result_df["DATA"] = result_df["DATA"].apply(
                lambda x: json.dumps(json.loads(x), sort_keys=True)
            )
            result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
            result_df = result_df[expected_df.columns]

            assert_frame_equal(
                expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                check_like=True,
            )

    @patch("commission_engine.third_party_connections.sql.Sql.get_connector")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_suiteanalytics_connector_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_get_connector,
        suiteanalytics_integration,
        suiteanalytics_extraction_config,
        suiteanalytics_api_access_config,
        suiteanalytics_transformation_configs,
        create_suiteanalytics_upstream_table,
    ):
        """Test SQL extraction for SuiteAnalytics connector scenario"""

        # Setup mocks
        mock_create_upstream_table_if_not_exists.return_value = None
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_get_source_key_fields.return_value = (
            ["data:TransId"],
            ["data:TransId"],
        )

        # Mock database connector
        mock_connector = MagicMock()
        mock_connector.connect.return_value = None
        mock_connector.disconnect.return_value = None
        mock_connector.get_object_and_schema_name_from_object_id.return_value = (
            "transactionrecords",
            "everstage",
        )
        mock_connector.get_all_fields_in_table.return_value = [
            {"name": "TransId"},
            {"name": "SalesId"},
            {"name": "Amount"},
            {"name": "DateModified"},
            {"name": "IsDeleted"},
        ]
        mock_connector.query_data.side_effect = [
            test_data.SQL_SUITEANALYTICS_RESPONSE_PAGE_1,
            test_data.SQL_SUITEANALYTICS_RESPONSE_PAGE_2,
        ]
        mock_get_connector.return_value = mock_connector

        # Create SQL connector and call get_changed_records
        sql_connector = Sql()
        result, deleted_fields = sql_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id='everstage."transactionrecords"',
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            config_object=suiteanalytics_api_access_config,
            snapshot_key="TransId",
            destination_object_type="custom_object",
            integration_id=test_data.SUITEANALYTICS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify database connector was called correctly
        assert mock_connector.query_data.call_count == 2  # 2 pages
        assert len(deleted_fields) == 0  # No deleted fields expected

        # Query Snowflake to validate data
        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            table_name = create_suiteanalytics_upstream_table
            query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"  # noqa: S608
            result_df = session.sql(query).to_pandas()

            assert not result_df.empty
            assert len(result_df) == len(
                test_data.EXPECTED_SUITEANALYTICS_SNOWFLAKE_DATA
            )

            # Prepare expected data
            raw_records = (
                test_data.SQL_SUITEANALYTICS_RESPONSE_PAGE_1
                + test_data.SQL_SUITEANALYTICS_RESPONSE_PAGE_2
            )
            unique_raw_records = {r["TransId"]: r for r in raw_records}.values()

            expected_rows = []
            sorted_transformed_records = sorted(
                test_data.EXPECTED_SUITEANALYTICS_SNOWFLAKE_DATA,
                key=lambda x: x["trans_id"],
            )
            for transformed_record in sorted_transformed_records:
                record_id = transformed_record["trans_id"]
                raw_record = next(
                    r for r in unique_raw_records if r["TransId"] == record_id
                )
                expected_rows.append(
                    {
                        "ROW_KEY": str(record_id),
                        "IS_DELETED": False,
                        "DATA": json.dumps(raw_record, sort_keys=True),
                    }
                )

            expected_df = pd.DataFrame(expected_rows)
            expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

            # Prepare result data for comparison
            result_df.columns = list(map(str.upper, result_df.columns))
            result_df["DATA"] = result_df["DATA"].apply(
                lambda x: json.dumps(json.loads(x), sort_keys=True)
            )
            result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
            result_df = result_df[expected_df.columns]

            assert_frame_equal(
                expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
                check_like=True,
            )

    @patch("commission_engine.third_party_connections.sql.Sql.get_connector")
    def test_verify_deleted_fields_handles_formatted_fields(  # noqa: PLR0913
        self,
        mock_get_connector,
        products_table_integration,
        products_table_api_access_config,
        products_table_transformation_configs,
    ):
        """Test SQL connector properly handles formatted fields"""

        # Mock database connector
        mock_connector = MagicMock()
        mock_connector.connect.return_value = None
        mock_connector.get_object_and_schema_name_from_object_id.return_value = (
            "table_1",
            "test_new_schema__75",
        )
        mock_connector.get_all_fields_in_table.return_value = [
            {"name": "id"},
            {"name": "name"},
            {"name": "amount_1"},
            {"name": "is_deleted"},
            {"name": "created_date"},
            {"name": "calmonth"},
        ]
        mock_get_connector.return_value = mock_connector

        # Create SQL connector and call verify_deleted_fields
        sql_connector = Sql()
        verified_fields = sql_connector.verify_deleted_fields(
            client_id=test_data.CLIENT_ID,
            sobject="test_new_schema__75.table_1",
            trans_configs=products_table_transformation_configs,
            config_object=products_table_api_access_config,
            integration_id=test_data.PRODUCTS_TABLE_INTEGRATION_ID,
        )

        # Verify that formatted fields are properly handled
        assert (
            "amount as amount_1" in verified_fields
        )  # This should be the formatted field
        assert "id" in verified_fields
        assert "name" in verified_fields
        assert "is_deleted" in verified_fields
        assert "created_date" in verified_fields
        assert "calmonth" in verified_fields

        # Verify no fields are marked as deleted
        assert len(sql_connector.source_deleted_fields) == 0

    @patch("commission_engine.third_party_connections.sql.Sql.get_connector")
    def test_unix_timestamp_sync_field_type(  # noqa: PLR0913
        self,
        mock_get_connector,
        sales_table_integration,
        sales_table_api_access_config,
    ):
        """Test SQL connector handles unix timestamp sync field type"""

        # Mock database connector
        mock_connector = MagicMock()
        mock_get_connector.return_value = mock_connector

        # Modify API access config to use unix timestamp
        sales_table_api_access_config.additional_data["sync_field_type"] = "unix"

        # Create SQL connector and get extractor params
        sql_connector = Sql()
        params = sql_connector.get_extractor_params(
            client_id=test_data.CLIENT_ID,
            object_id="test_new_schema__75.table_1",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            sync_mode="changes",
            sync_field_type="unix",
            integration_id=test_data.SALES_TABLE_INTEGRATION_ID,
            config_object=sales_table_api_access_config,
        )

        # Verify unix timestamps are generated
        assert isinstance(params["start"], int)  # Should be unix timestamp
        assert isinstance(params["end"], int)  # Should be unix timestamp
        assert params["end"] > params["start"]  # End should be after start
