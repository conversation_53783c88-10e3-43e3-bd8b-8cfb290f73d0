import json
from unittest.mock import patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.third_party_connections.quickbooks import Quickbooks
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from . import test_data
from .test_fixture import (
    bills_api_access_config,
    bills_extraction_config,
    bills_integration,
    bills_transformation_configs,
    create_bills_upstream_table,
    create_customers_upstream_table,
    create_invoices_upstream_table,
    create_items_upstream_table,
    create_profit_loss_report_upstream_table,
    create_transaction_list_report_upstream_table,
    customers_api_access_config,
    customers_extraction_config,
    customers_integration,
    customers_transformation_configs,
    invoices_api_access_config,
    invoices_extraction_config,
    invoices_integration,
    invoices_transformation_configs,
    items_api_access_config,
    items_extraction_config,
    items_integration,
    items_transformation_configs,
    profit_loss_report_api_access_config,
    profit_loss_report_extraction_config,
    profit_loss_report_integration,
    profit_loss_report_transformation_configs,
    transaction_list_report_api_access_config,
    transaction_list_report_extraction_config,
    transaction_list_report_integration,
    transaction_list_report_transformation_configs,
)


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestQuickbooksExtraction:

    @patch(
        "commission_engine.third_party_connections.quickbooks.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_customers_standard_object_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        customers_integration,
        customers_extraction_config,
        customers_transformation_configs,
        customers_api_access_config,
        create_customers_upstream_table,
    ):
        """Test Quickbooks Customers extraction using standard object API with pagination and deduplication"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.QUICKBOOKS_CUSTOMERS_API_RESPONSE_PAGE_1,
        ]

        mock_get_source_key_fields.return_value = (
            ["data:Id"],
            ["data:Id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Quickbooks().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Customers",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=customers_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.CUSTOMERS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 1

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.CUSTOMERS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records
        assert len(result_df) == len(test_data.EXPECTED_CUSTOMERS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = test_data.QUICKBOOKS_CUSTOMERS_API_RESPONSE_PAGE_1[
            "QueryResponse"
        ]["Customer"]

        expected_rows, vis = [], set()
        for raw_record in raw_records:
            if raw_record["Id"] in vis:
                continue
            vis.add(raw_record["Id"])
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["Id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.quickbooks.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_items_standard_object_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        items_integration,
        items_extraction_config,
        items_transformation_configs,
        items_api_access_config,
        create_items_upstream_table,
    ):
        """Test Quickbooks Items extraction using standard object API with pagination and deduplication"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.QUICKBOOKS_ITEMS_API_RESPONSE_PAGE_1,
        ]

        mock_get_source_key_fields.return_value = (
            ["data:Id"],
            ["data:Id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Quickbooks().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Items",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=items_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.ITEMS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 1

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.ITEMS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records
        assert len(result_df) == len(test_data.EXPECTED_ITEMS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = test_data.QUICKBOOKS_ITEMS_API_RESPONSE_PAGE_1["QueryResponse"][
            "Item"
        ]

        expected_rows, vis = [], set()
        for raw_record in raw_records:
            if raw_record["Id"] in vis:
                continue
            vis.add(raw_record["Id"])
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["Id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.quickbooks.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_invoices_process_lines_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        invoices_integration,
        invoices_extraction_config,
        invoices_transformation_configs,
        invoices_api_access_config,
        create_invoices_upstream_table,
    ):
        """Test Quickbooks Invoices extraction using process_list functionality (process lines fields)"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.QUICKBOOKS_INVOICES_API_RESPONSE_PAGE_1,
        ]

        mock_get_source_key_fields.return_value = (
            ["data:LineId"],
            ["data:LineId"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Quickbooks().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Invoices",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=invoices_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.INVOICES_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 1

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INVOICES_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 processed line items from the invoices
        assert len(result_df) == len(test_data.EXPECTED_INVOICES_SNOWFLAKE_DATA)

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_INVOICES_SNOWFLAKE_DATA, key=lambda x: x["LineId"]
        )
        for transformed_record in sorted_transformed_records:
            line_id = transformed_record["LineId"]
            expected_rows.append(
                {
                    "ROW_KEY": line_id.lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(transformed_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.quickbooks.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_bills_standard_object_writes_to_snowflake_with_deduplication(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        bills_integration,
        bills_extraction_config,
        bills_transformation_configs,
        bills_api_access_config,
        create_bills_upstream_table,
    ):
        """Test Quickbooks Bills extraction using standard object API with pagination and deduplication"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.QUICKBOOKS_BILLS_API_RESPONSE_PAGE_1,
        ]

        mock_get_source_key_fields.return_value = (
            ["data:Id"],
            ["data:Id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Quickbooks().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="Bills",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=bills_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.BILLS_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called twice (for pagination)
        assert mock_api_call.call_count == 1

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.BILLS_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records
        assert len(result_df) == len(test_data.EXPECTED_BILLS_SNOWFLAKE_DATA)

        # Prepare expected data
        raw_records = test_data.QUICKBOOKS_BILLS_API_RESPONSE_PAGE_1["QueryResponse"][
            "Bill"
        ]

        expected_rows, vis = [], set()
        for raw_record in raw_records:
            if raw_record["Id"] in vis:
                continue
            vis.add(raw_record["Id"])
            expected_rows.append(
                {
                    "ROW_KEY": raw_record["Id"].lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(raw_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.quickbooks.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_transaction_list_report_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        transaction_list_report_integration,
        transaction_list_report_extraction_config,
        transaction_list_report_transformation_configs,
        transaction_list_report_api_access_config,
        create_transaction_list_report_upstream_table,
    ):
        """Test Quickbooks Transaction List Report extraction using list type report"""
        # Mock the API response for report
        mock_api_call.side_effect = [
            test_data.QUICKBOOKS_TRANSACTION_LIST_REPORT_API_RESPONSE,
        ]

        mock_get_source_key_fields.return_value = (
            ["data:Id"],
            ["data:Id"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Quickbooks().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="TransactionListReport",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=transaction_list_report_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.TRANSACTION_LIST_REPORT_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called once (for report)
        assert mock_api_call.call_count == 1

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.TRANSACTION_LIST_REPORT_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 3 unique records from the report
        # assert len(result_df) == len(
        #     test_data.EXPECTED_TRANSACTION_LIST_REPORT_SNOWFLAKE_DATA
        # )

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_TRANSACTION_LIST_REPORT_SNOWFLAKE_DATA,
            key=lambda x: x["Id"],
        )
        for transformed_record in sorted_transformed_records:
            record_id = transformed_record["Id"]
            expected_rows.append(
                {
                    "ROW_KEY": record_id.lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(transformed_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.quickbooks.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_profit_loss_report_writes_to_snowflake(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        profit_loss_report_integration,
        profit_loss_report_extraction_config,
        profit_loss_report_transformation_configs,
        profit_loss_report_api_access_config,
        create_profit_loss_report_upstream_table,
    ):
        """Test Quickbooks Profit and Loss Report extraction using transaction type report"""
        # Mock the API response for report
        mock_api_call.side_effect = [
            test_data.QUICKBOOKS_PROFIT_LOSS_REPORT_API_RESPONSE,
        ]

        mock_get_source_key_fields.return_value = (
            ["data:start_date"],
            ["data:start_date"],
        )
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        Quickbooks().get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="ProfitLossReport",
            primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=profit_loss_report_api_access_config,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=test_data.PROFIT_LOSS_REPORT_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify that api_call was called once (for report)
        assert mock_api_call.call_count == 1

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.PROFIT_LOSS_REPORT_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        assert not result_df.empty
        # We expect 2 unique records from the report (Jan 2024 and Total columns)
        # assert len(result_df) == len(
        #     test_data.EXPECTED_PROFIT_LOSS_REPORT_SNOWFLAKE_DATA
        # )

        expected_rows = []
        sorted_transformed_records = sorted(
            test_data.EXPECTED_PROFIT_LOSS_REPORT_SNOWFLAKE_DATA,
            key=lambda x: x["col_title"],
        )
        for transformed_record in sorted_transformed_records:
            col_title = transformed_record["col_title"]
            expected_rows.append(
                {
                    "ROW_KEY": str(transformed_record["start_date"]).lower(),
                    "IS_DELETED": False,
                    "DATA": json.dumps(transformed_record, sort_keys=True),
                }
            )

        expected_df = pd.DataFrame(expected_rows)
        expected_df = expected_df.sort_values(by="ROW_KEY").reset_index(drop=True)

        # Prepare result data for comparison
        result_df.columns = list(map(str.upper, result_df.columns))
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values(by="ROW_KEY").reset_index(drop=True)
        result_df = result_df[expected_df.columns]

        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )
