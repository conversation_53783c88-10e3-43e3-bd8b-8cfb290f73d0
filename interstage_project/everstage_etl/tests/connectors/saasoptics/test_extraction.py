import json
from unittest.mock import patch

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.models.etl_config_models import (
    ApiAccessConfig,
    ExtractionConfig,
    Integration,
    TransformationConfig,
)
from commission_engine.third_party_connections.saasoptics import Saasoptics
from everstage_etl.tests.connectors.utils import create_upstream_table_utility
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from . import test_data


@pytest.fixture
def customers_integration():
    """Integration fixture for standard customers object"""
    # Delete any existing integration with the same ID first
    Integration.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    # Create the integration record
    integration_obj = Integration.objects.create(
        client_id=test_data.CLIENT_ID,
        knowledge_begin_date=test_data.PRIMARY_KD,
        integration_id=test_data.INTEGRATION_ID,
        service_name="saasoptics",
        source_object_id="saasoptics-customer",
        is_api=True,
        destination_object_id=test_data.DESTINATION_OBJECT_ID,
        additional_data={
            "run_snowflake_sync": True,
            "changes_sync_field": "modified",
        },
    )

    yield integration_obj


@pytest.fixture
def payments_integration():
    """Integration fixture for process lines payments object"""
    # Delete any existing integration with the same ID first
    Integration.objects.filter(
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID
    ).delete()

    # Create the integration record
    integration_obj = Integration.objects.create(
        client_id=test_data.CLIENT_ID,
        knowledge_begin_date=test_data.PRIMARY_KD,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
        service_name="saasoptics",
        source_object_id="saas-payments",
        is_api=True,
        destination_object_id=test_data.PROCESS_LINES_DESTINATION_OBJECT_ID,
        additional_data={
            "run_snowflake_sync": True,
            "changes_sync_field": "modified",
        },
    )

    yield integration_obj


@pytest.fixture
def invoices_integration():
    """Integration fixture for invoices standard object"""
    # Delete any existing integration with the same ID first
    Integration.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    # Create the integration record
    integration_obj = Integration.objects.create(
        client_id=test_data.CLIENT_ID,
        knowledge_begin_date=test_data.PRIMARY_KD,
        integration_id=test_data.INTEGRATION_ID,
        service_name="saasoptics",
        source_object_id="saas-invoice",
        is_api=True,
        destination_object_id=test_data.DESTINATION_OBJECT_ID,
        additional_data={
            "run_snowflake_sync": True,
            "changes_sync_field": "modified",
        },
    )

    yield integration_obj


@pytest.fixture
def customers_extraction_config():
    # Delete any existing config first
    ExtractionConfig.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    config = ExtractionConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="saasoptics-customer",
        sync_type="changes",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )

    yield config


@pytest.fixture
def payments_extraction_config():
    # Delete any existing config first
    ExtractionConfig.objects.filter(
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID
    ).delete()

    config = ExtractionConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
        source_object_id="saas-payments",
        sync_type="changes",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )

    yield config


@pytest.fixture
def invoices_extraction_config():
    # Delete any existing config first
    ExtractionConfig.objects.filter(integration_id=test_data.INTEGRATION_ID).delete()

    config = ExtractionConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="saas-invoice",
        sync_type="changes",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )

    yield config


@pytest.fixture
def customers_transformation_configs():
    """Transformation configs for standard customers fields"""
    # Delete any existing configs first
    TransformationConfig.objects.filter(
        integration_id=test_data.INTEGRATION_ID
    ).delete()

    field_mappings = {
        "id": "id",
        "name": "customername",
        "parent": "parent_company",
        "number": "number__1",
        "test_field": "testfield",
    }
    created_configs = []
    for source_field, dest_field_suffix in field_mappings.items():
        created_configs.append(
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.INTEGRATION_ID,
                source_object_id="saasoptics-customer",
                destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
                source_field=source_field,
                destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",
            )
        )

    yield created_configs


@pytest.fixture
def payments_transformation_configs():
    """Transformation configs for process lines payments fields"""
    # Delete any existing configs first
    TransformationConfig.objects.filter(
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID
    ).delete()

    field_mappings = {
        "LineId": "id",
        "type": "type",
        "status": "status",
        "contract": "contract",
        "customer": "customer",
        "foreign_exchange_rate": "foreign_exchange_rate",
        "currency": "currency",
        "number": "number",
        "local_unapplied_amount": "localunappliedamount",
        "reference_number": "reference_number",
        "date": "date",
        "local_amount": "localamount",
        "local_applied_amount": "localappliedamount",
        "line_item.invoice": "invoice_id",
    }
    created_configs = []
    for source_field, dest_field_suffix in field_mappings.items():
        created_configs.append(
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
                source_object_id="saas-payments",
                destination_object_id=str(
                    test_data.PROCESS_LINES_DESTINATION_OBJECT_ID
                ),
                source_field=source_field,
                destination_field=f"co_{test_data.PROCESS_LINES_DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",
            )
        )

    yield created_configs


@pytest.fixture
def invoices_transformation_configs():
    """Transformation configs for invoices standard fields"""
    # Delete any existing configs first
    TransformationConfig.objects.filter(
        integration_id=test_data.INTEGRATION_ID
    ).delete()

    field_mappings = {
        "id": "id",
        "number": "number",
        "auditentry.created_by_name": "auditentrycreatedbyname",
    }
    created_configs = []
    for source_field, dest_field_suffix in field_mappings.items():
        created_configs.append(
            TransformationConfig.objects.create(
                client_id=test_data.CLIENT_ID,
                integration_id=test_data.INTEGRATION_ID,
                source_object_id="saas-invoice",
                destination_object_id=str(test_data.DESTINATION_OBJECT_ID),
                source_field=source_field,
                destination_field=f"co_{test_data.DESTINATION_OBJECT_ID}_{dest_field_suffix}",
                field_type="String",
            )
        )

    yield created_configs


@pytest.mark.django_db
@pytest.fixture
def customers_api_access_config():
    """API access config for customers standard object"""
    return ApiAccessConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="saasoptics-customer",
        request_url="https://j12.saasoptics.com/innovu/api/v1.0/customers/?modified__gte={start}",
        request_type="get",
        request_header={
            "Content-type": "application/json",
            "Authorization": "Token {access_token}",
        },
        additional_data={
            "id_key": "id",
        },
        response_key="results",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )


@pytest.mark.django_db
@pytest.fixture
def payments_api_access_config():
    """API access config for payments process lines object"""
    return ApiAccessConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
        source_object_id="saas-payments",
        request_url="https://j12.saasoptics.com/innovu/api/v1.0/payments?modified__gte={start}",
        request_type="get",
        request_header={
            "Content-type": "application/json",
            "Authorization": "Token {access_token}",
        },
        additional_data={
            "id_key": "id",
            "process_list": True,
            "source_list_name": "lines",
            "new_list_name": "line_item",
            "list_id": "id",
            "composite_id": ["id"],
        },
        response_key="results",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )


@pytest.mark.django_db
@pytest.fixture
def invoices_api_access_config():
    """API access config for invoices standard object"""
    return ApiAccessConfig.objects.create(
        client_id=test_data.CLIENT_ID,
        integration_id=test_data.INTEGRATION_ID,
        source_object_id="saas-invoice",
        request_url="https://j12.saasoptics.com/innovu/api/v1.0/invoices/?modified__gte={start}",
        request_type="get",
        request_header={
            "Content-type": "application/json",
            "Authorization": "Token {access_token}",
        },
        additional_data={
            "id_key": "id",
        },
        response_key="results",
        access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
    )


@pytest.fixture
def customers_create_upstream_table():
    """Create the upstream table for customers with the correct schema before test runs"""
    yield from create_upstream_table_utility(
        test_data.CLIENT_ID, test_data.INTEGRATION_ID
    )


@pytest.fixture
def payments_create_upstream_table():
    """Create the upstream table for payments with the correct schema before test runs"""
    yield from create_upstream_table_utility(
        test_data.CLIENT_ID, test_data.PROCESS_LINES_INTEGRATION_ID
    )


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
class TestSaasopticsExtraction:
    """Test class for Saasoptics connector extraction functionality"""

    @patch(
        "commission_engine.third_party_connections.saasoptics.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.saasoptics.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_standard_objects_customers_extraction(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        customers_integration,
        customers_extraction_config,
        customers_api_access_config,
        customers_transformation_configs,
        customers_create_upstream_table,
    ):
        """Test standard objects extraction with customers data"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.SAASOPTICS_CUSTOMERS_PAGINATED_PAGE_1,
            test_data.SAASOPTICS_CUSTOMERS_PAGINATED_PAGE_2,
        ]
        mock_get_source_key_fields.return_value = (["data:id"], ["data:id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        saasoptics_connector = Saasoptics()
        saasoptics_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="saasoptics-customer",
            _unused_primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=customers_api_access_config,
            _unused_snapshot_key="id",
            _unused_destination_object_type="saasoptics-customer",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called twice for pagination
        assert mock_api_call.call_count == 2

        # Query the Snowflake table to verify data was written
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert that 3 unique records were written (from both pages)
        assert len(result_df) == 3

        # Build expected DataFrame
        expected_data = [
            {
                "ROW_KEY": record["id"],
                "IS_DELETED": False,
                "DATA": json.dumps(record, sort_keys=True),
            }
            for record in test_data.EXPECTED_CUSTOMERS_SNOWFLAKE_DATA
        ]
        expected_df = pd.DataFrame(expected_data)

        # Prepare result DataFrame
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values("ROW_KEY").reset_index(drop=True)
        expected_df = expected_df.sort_values("ROW_KEY").reset_index(drop=True)

        # Assert DataFrames are equal
        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.saasoptics.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.saasoptics.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_process_lines_payments_extraction(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        payments_integration,
        payments_extraction_config,
        payments_api_access_config,
        payments_transformation_configs,
        payments_create_upstream_table,
    ):
        """Test process lines extraction with payments data"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.SAASOPTICS_PAYMENTS_PAGINATED_PAGE_1,
            test_data.SAASOPTICS_PAYMENTS_PAGINATED_PAGE_2,
        ]
        mock_get_source_key_fields.return_value = (["data:LineId"], ["data:LineId"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        saasoptics_connector = Saasoptics()
        saasoptics_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="saas-payments",
            _unused_primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=payments_api_access_config,
            _unused_snapshot_key="LineId",
            _unused_destination_object_type="saas-payments",
            integration_id=test_data.PROCESS_LINES_INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called twice for pagination
        assert mock_api_call.call_count == 2

        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.PROCESS_LINES_INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert that 3 unique line items were written (2 from first payment, 1 from second)
        assert len(result_df) == 3

        # Build expected DataFrame
        expected_data = [
            {
                "ROW_KEY": record["LineId"],
                "IS_DELETED": False,
                "DATA": json.dumps(record, sort_keys=True),
            }
            for record in test_data.EXPECTED_PAYMENTS_SNOWFLAKE_DATA
        ]
        expected_df = pd.DataFrame(expected_data)

        # Prepare result DataFrame
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values("ROW_KEY").reset_index(drop=True)
        expected_df = expected_df.sort_values("ROW_KEY").reset_index(drop=True)

        # Assert DataFrames are equal
        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.saasoptics.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.saasoptics.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_standard_fields_invoices_extraction(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        invoices_integration,
        invoices_extraction_config,
        invoices_api_access_config,
        invoices_transformation_configs,
        customers_create_upstream_table,
    ):
        """Test standard fields extraction with invoices data"""
        # Mock the API responses for pagination
        mock_api_call.side_effect = [
            test_data.SAASOPTICS_INVOICES_PAGINATED_PAGE_1,
            test_data.SAASOPTICS_INVOICES_PAGINATED_PAGE_2,
        ]
        mock_get_source_key_fields.return_value = (["data:id"], ["data:id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        saasoptics_connector = Saasoptics()
        saasoptics_connector.get_changed_records(
            client_id=test_data.CLIENT_ID,
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            object_id="saas-invoice",
            _unused_primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="changes",
            config_object=invoices_api_access_config,
            _unused_snapshot_key="id",
            _unused_destination_object_type="saas-invoice",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called twice for pagination
        assert mock_api_call.call_count == 2

        # Query the Snowflake table to verify data was written
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, UPDATED_ON, DATA FROM {table_name}"

        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert that 3 unique records were written
        assert len(result_df) == 3

        # Build expected DataFrame
        expected_data = [
            {
                "ROW_KEY": record["id"],
                "IS_DELETED": False,
                "DATA": json.dumps(record, sort_keys=True),
            }
            for record in test_data.EXPECTED_INVOICES_SNOWFLAKE_DATA
        ]
        expected_df = pd.DataFrame(expected_data)

        # Prepare result DataFrame
        result_df["DATA"] = result_df["DATA"].apply(
            lambda x: json.dumps(json.loads(x), sort_keys=True)
        )
        result_df = result_df.sort_values("ROW_KEY").reset_index(drop=True)
        expected_df = expected_df.sort_values("ROW_KEY").reset_index(drop=True)

        # Assert DataFrames are equal
        assert_frame_equal(
            expected_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            result_df[["ROW_KEY", "IS_DELETED", "DATA"]],
            check_like=True,
        )

    @patch(
        "commission_engine.third_party_connections.saasoptics.ThirdPartyApi.api_call"
    )
    @patch("everstage_ddd.upstream.extraction.data_lake.get_source_key_fields")
    @patch("everstage_ddd.upstream.extraction.data_lake.get_upstream_timestamps")
    @patch(
        "commission_engine.third_party_connections.saasoptics.UpstreamChangesWriter.create_upstream_table_if_not_exists"
    )
    def test_deleted_records_extraction(  # noqa: PLR0913
        self,
        mock_create_upstream_table_if_not_exists,
        mock_get_upstream_timestamps,
        mock_get_source_key_fields,
        mock_api_call,
        customers_integration,
        customers_extraction_config,
        customers_create_upstream_table,
    ):
        """Test deleted records extraction"""
        # Create API access config for deleted records
        del_config = ApiAccessConfig.objects.create(
            client_id=test_data.CLIENT_ID,
            integration_id=test_data.INTEGRATION_ID,
            source_object_id="saasoptics-customer_delete",
            request_url="https://j12.saasoptics.com/innovu/api/v1.0/customers/deleted",
            request_type="get",
            request_header={
                "Content-type": "application/json",
                "Authorization": "Token {access_token}",
            },
            additional_data={
                "id_key": "id",
            },
            response_key="results",
            access_token_config_id=test_data.ACCESS_TOKEN_CONFIG_ID,
        )

        # Mock the API responses
        mock_api_call.side_effect = [
            test_data.SAASOPTICS_DELETED_CUSTOMERS_PAGINATED,
        ]
        mock_get_source_key_fields.return_value = (["data:id"], ["data:id"])
        mock_get_upstream_timestamps.return_value = {
            "upstream_source_synced_till": test_data.PRIMARY_KD
        }
        mock_create_upstream_table_if_not_exists.return_value = None

        saasoptics_connector = Saasoptics()
        saasoptics_connector.get_deleted_records(
            client_id=test_data.CLIENT_ID,
            object_id="saasoptics-customer",
            e2e_sync_run_id=test_data.E2E_SYNC_RUN_ID,
            sync_run_id=test_data.SYNC_RUN_ID,
            _unused_primary_kd=test_data.PRIMARY_KD,
            changes_start_time=test_data.CHANGES_START_TIME,
            _unused_sync_mode="delete",
            del_config_obj=del_config,
            _unused_destination_object_type="saasoptics-customer",
            integration_id=test_data.INTEGRATION_ID,
            is_validation=False,
        )

        # Verify API was called once
        assert mock_api_call.call_count == 1

        # Query the Snowflake table to verify data was written
        table_name = f"upstream_source_changes_data_{test_data.CLIENT_ID}_{uuid_in_snakecase(test_data.INTEGRATION_ID)}"
        query = f"SELECT ROW_KEY, IS_DELETED, DATA FROM {table_name}"
        with create_snowpark_session_wrapper(client_id=test_data.CLIENT_ID) as session:
            result_df = session.sql(query).to_pandas()

        # Assert that 2 deleted records were written
        assert len(result_df) == 2
        assert all(result_df["IS_DELETED"] == True)  # noqa: E712

        # Verify the row keys are correct
        expected_row_keys = ["cust_deleted_001", "cust_deleted_002"]
        assert sorted(result_df["ROW_KEY"].tolist()) == sorted(expected_row_keys)
