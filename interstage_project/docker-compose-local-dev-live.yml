version: "3.7"

services:
  frontend:
    profiles:
      - frontend
    stdin_open: true
    build:
      context: frontend/.
      dockerfile: Dockerfile.dev.frontend
      args:
        - NPM_REGISTRY_TOKEN=${NPM_REGISTRY_TOKEN}
    ports:
      - 3000:3000
      - 3005:3005
    depends_on:
      - backend
    volumes:
      - ./frontend/src:/code/frontend/src
      - ./frontend/public:/code/frontend/public
    environment:
      - REACT_APP_ACTUAL_ENV=LOCALDEV
  everstage-admin:
    profiles:
      - frontend-admin
    stdin_open: true
    build:
      context: everstage-admin/.
      dockerfile: Dockerfile.dev.admin
      args:
        - NPM_REGISTRY_TOKEN=${NPM_REGISTRY_TOKEN}
    ports:
      - 3001:3001
    depends_on:
      - backend
    volumes:
      - ./everstage-admin/src:/code/everstage-admin/src
      - ./everstage-admin/public:/code/everstage-admin/public
    environment:
      - REACT_APP_ACTUAL_ENV=LOCALDEV
  backend:
    stdin_open: true
    hostname: backend
    extra_hosts:
      - "host.docker.internal:host-gateway"
    build:
      context: .
      dockerfile: Dockerfile.dev.backend
    volumes:
      - .:/code
      - ./snapshot:/mnt/efs
    ports:
      - ${EXPOSED_BACKEND_PORT:-8000}:8000
      - ${EXPOSED_BACKEND_DEBUG_PORT:-6000}:6000
    env_file:
      - docker_file_env/local-variables.env
    environment:
      - SNOWFLAKE_EXECUTION_MODE=interactive
    depends_on:
      - engine
  beat:
    profiles:
      - beat
    stdin_open: true
    build:
      context: .
      dockerfile: Dockerfile.dev.beat
    volumes:
      - .:/code
    env_file:
      - docker_file_env/local-variables.env
    depends_on:
      - engine
  engine:
    stdin_open: true
    build:
      context: .
      dockerfile: Dockerfile.dev.engine
    volumes:
      - .:/code
      - ./snapshot:/mnt/efs
      - ./graph_rag_index:/mnt/efs_graphrag
    env_file:
      - docker_file_env/local-variables.env
    depends_on:
      - ever-redis
  workflow-engine:
    profiles:
      - workflow-engine
    stdin_open: true
    build:
      context: .
      dockerfile: Dockerfile.dev.engine
    volumes:
      - .:/code
      - ./snapshot:/mnt/efs
    env_file:
      - docker_file_env/local-variables.env
    depends_on:
      - ever-redis
    environment:
      - TG_NAME=EVENT,IF,WHEN,THEN
      - CLIENT_LEVEL_QUEUES=
      - COMMON_QUEUES=WORKFLOW
  ever-flower:
    profiles:
      - flower
    build:
      context: .
      dockerfile: Dockerfile.dev.flower
    image: mher/flower:1.2.0
    ports:
      - ${EXPOSED_FLOWER_PORT:-5557}:5555
    volumes:
      - .:/code
    env_file:
      - docker_file_env/local-variables.env
    depends_on:
      - ever-redis
      - engine
  # Use profile 'redis-celery' to run separate Redis servers for Celery
  # Modify the corresponding variables in local-variables.env & vars.sh
  # CELERY_BROKER_URL=redis://redis-broker:6377
  # CELERY_RESULT_BACKEND=redis://redis-result:6378
  redis-broker:
    profiles:
      - redis-celery
    image: redis
    command: --port 6377
    ports:
      - 6377:6377
  redis-result:
    profiles:
      - redis-celery
    image: redis
    command: --port 6378
    ports:
      - 6378:6378
  ever-redis:
    image: redis
    ports:
      - ${EXPOSED_REDIS_PORT:-6379}:6379
  typesense:
    # Optional: Use '--profile typesense' to run Typesense server locally
    # Modify TYPESENSE_* vars accordingly in local-variables.env & vars.sh
    profiles:
      - typesense
    image: typesense/typesense:0.25.2
    restart: on-failure
    ports:
      - "8108:8108"
    volumes:
      - ./typesense-data:/data
    command: "--data-dir /data --api-key=xyz --enable-cors"
  zookeeper:
    profiles:
      - eda
    image: confluentinc/cp-zookeeper:7.6.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_SERVERS: zookeeper:2888:3888
  kafka:
    profiles:
      - eda
    image: confluentinc/cp-kafka:7.6.0
    hostname: kafka
    container_name: kafka
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      #these parameters are for local development only, for actual parameters refer to the MSK Cluster on AWS
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_DELETE_TOPIC_ENABLE: true
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    depends_on:
      - zookeeper
  postgres-connector:
    profiles:
      - eda
    image: debezium/connect:2.3.4.Final
    container_name: postgres-debezium
    ports:
      - 9090:8083
    links:
      - kafka
    depends_on:
      - kafka
    environment:
      - BOOTSTRAP_SERVERS=kafka:9092
      - GROUP_ID=pg
      - CONFIG_STORAGE_TOPIC=pg_connect_configs
      - OFFSET_STORAGE_TOPIC=pg_connect_offsets
      - STATUS_STORAGE_TOPIC=pg_connect_statuses
  consumer:
    profiles:
      - eda
    stdin_open: true
    build:
      context: .
      dockerfile: Dockerfile.dev.consumer
    volumes:
      - .:/code
      - ./snapshot:/mnt/efs
    env_file:
      - docker_file_env/local-variables.env
    depends_on:
      - kafka
  lambda-flask:
    profiles:
      - lambda-flask
    stdin_open: true
    build:
      context: ../../lambda-functions/compute_and_save_ast
      dockerfile: ../../lambda-functions/compute_and_save_ast/Dockerfile.local
    volumes:
      - ../../lambda-functions/compute_and_save_ast:/lambda_app
      - ./snapshot:/mnt/efs
    ports:
      - 5050:5050
  lambda-flask-calc-fields:
    profiles:
      - lambda-flask-calc-fields
    stdin_open: true
    build:
      context: ../../lambda-functions/compute_calc_fields
      dockerfile: ../../lambda-functions/compute_calc_fields/Dockerfile.local
    volumes:
      - ../../lambda-functions/compute_calc_fields:/lambda_app
      - ./snapshot:/mnt/efs
    ports:
      - 5051:5051
  lambda-flask-graph-rag:
    platform: linux/amd64
    profiles:
      - lambda-flask-graph-rag
    stdin_open: true
    build:
      context: ../../lambda-functions/graph_rag
      dockerfile: ../../lambda-functions/graph_rag/Dockerfile.local
    volumes:
      - ../../lambda-functions/graph_rag:/lambda_app
      - ../../lambda-functions/graph_rag/index-files:/mnt/efs
    ports:
      - 5052:5052

  salesforce-cdc:
    profiles:
      - eda
    build:
      context: .
      dockerfile: Dockerfile.dev.cdc
    volumes:
      - .:/code
      - ./snapshot:/mnt/efs
    env_file:
      - docker_file_env/local-variables.env
    ports:
      - "5002:5002"
    depends_on:
      - ever-redis
