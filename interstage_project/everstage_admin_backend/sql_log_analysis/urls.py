"""URL configuration for SQL log analysis API endpoints."""

from django.urls import path

from .views import (
    AnalyzeLogFileView,
    GetQueriesForRequestView,
    GetRequestSummariesView,
    ListLogFilesView,
)

app_name = "sql_logs"

urlpatterns = [
    # List available log files
    path("files/", ListLogFilesView.as_view(), name="list_files"),
    # Comprehensive log file analysis
    path("analyze/", AnalyzeLogFileView.as_view(), name="analyze"),
    # Get request summaries
    path(
        "request-summaries/",
        GetRequestSummariesView.as_view(),
        name="request_summaries",
    ),
    # Get queries for a specific request ID
    path(
        "request-queries/",
        GetQueriesForRequestView.as_view(),
        name="request_queries",
    ),
]
