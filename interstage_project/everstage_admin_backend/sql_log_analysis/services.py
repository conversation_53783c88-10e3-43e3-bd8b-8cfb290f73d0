"""SQL log analysis service.

This module provides services for analyzing SQL log files, including parsing,
filtering, and generating statistics. Designed for easy unit testing with
clear separation of concerns.
"""

import json
import logging
from collections import defaultdict
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Tuple

from .models import (
    AnalysisFilters,
    LogFileInfo,
    RequestSummary,
    SQLLogEntry,
    TableStatistics,
)

logger = logging.getLogger(__name__)

# Constants
SAMPLE_SIZE_LIMIT = 1000  # Lines to sample for file size estimation
DEFAULT_SLOW_QUERY_THRESHOLD_MS = 1000.0  # Milliseconds threshold for slow queries


class LogFileManager:
    """Manages log file operations and metadata."""

    def __init__(self, log_directory: str):
        """Initialize with log directory path.

        Args:
            log_directory: Path to the directory containing log files
        """
        self.log_directory = Path(log_directory)

    def list_log_files(self, pattern: str = "sql.json*") -> List[LogFileInfo]:
        """List available log files with metadata.

        Args:
            pattern: Glob pattern to match log files

        Returns:
            List of LogFileInfo objects sorted by modification time (newest first)
        """
        try:
            log_files = []
            current_file = "sql.json"

            for file_path in self.log_directory.glob(pattern):
                if file_path.is_file():
                    stat = file_path.stat()

                    # Estimate number of entries by counting lines (rough approximation)
                    estimated_entries = None
                    if file_path.stat().st_size > 0:
                        # Sample first 1000 lines to estimate average line size
                        sample_lines = 0
                        sample_size = 0
                        with file_path.open("r", encoding="utf-8") as f:
                            for i, line in enumerate(f):
                                if i >= SAMPLE_SIZE_LIMIT:
                                    break
                                sample_lines += 1
                                sample_size += len(line)

                        if sample_lines > 0:
                            avg_line_size = sample_size / sample_lines
                            estimated_entries = int(stat.st_size / avg_line_size)

                    log_info = LogFileInfo(
                        filename=file_path.name,
                        filepath=str(file_path),
                        size_bytes=stat.st_size,
                        created_time=datetime.fromtimestamp(
                            stat.st_ctime, tz=timezone.utc
                        ),
                        modified_time=datetime.fromtimestamp(
                            stat.st_mtime, tz=timezone.utc
                        ),
                        is_current=(file_path.name == current_file),
                        estimated_entries=estimated_entries,
                    )
                    log_files.append(log_info)

            # Sort by modification time, newest first
            return sorted(log_files, key=lambda x: x.modified_time, reverse=True)

        except (OSError, PermissionError):
            logger.exception("Error listing log files")
            return []

    def validate_file_access(self, filename: str) -> bool:
        """Validate that a file exists and is readable.

        Args:
            filename: Name of the file to validate

        Returns:
            True if file is accessible, False otherwise
        """
        file_path = self.log_directory / filename
        return file_path.exists() and file_path.is_file()

    def get_file_path(self, filename: str) -> str:
        """Get full path for a log file.

        Args:
            filename: Name of the log file

        Returns:
            Full path to the log file
        """
        return str(self.log_directory / filename)


class LogParser:
    """Parses SQL log files and converts to structured data."""

    def parse_json_log_file(self, file_path: str) -> Iterator[SQLLogEntry]:
        """Parse a JSON log file and yield SQLLogEntry objects.

        Args:
            file_path: Path to the JSON log file

        Yields:
            SQLLogEntry objects parsed from the file
        """
        with Path(file_path).open("r", encoding="utf-8") as file:
            for line_num, file_line in enumerate(file, 1):
                line = file_line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                    # Convert timestamp string to datetime
                    if isinstance(data.get("timestamp"), str):
                        data["timestamp"] = datetime.fromisoformat(
                            data["timestamp"].replace("Z", "+00:00")
                        )

                    yield SQLLogEntry(**data)

                except (json.JSONDecodeError, ValueError) as parse_error:
                    logger.warning(
                        "Error parsing line %d in %s: %s",
                        line_num,
                        file_path,
                        parse_error,
                    )
                    continue


class LogFilter:
    """Handles filtering of log entries based on criteria."""

    def apply_filters(
        self, entries: Iterator[SQLLogEntry], filters: Optional[AnalysisFilters]
    ) -> Iterator[SQLLogEntry]:
        """Apply filters to a stream of log entries.

        Args:
            entries: Iterator of SQLLogEntry objects
            filters: Filters to apply, or None for no filtering

        Yields:
            Filtered SQLLogEntry objects
        """
        if not filters:
            yield from entries
            return

        for entry in entries:
            if self._entry_matches_filters(entry, filters):
                yield entry

    def _entry_matches_filters(  # noqa: PLR0911 - too many return statements
        self, entry: SQLLogEntry, filters: AnalysisFilters
    ) -> bool:
        """Check if an entry matches the given filters.

        Args:
            entry: Log entry to check
            filters: Filters to apply

        Returns:
            True if entry matches all filters, False otherwise
        """
        # Time range filters
        if (
            filters.start_time
            and entry.timestamp
            and entry.timestamp < filters.start_time
        ):
            return False
        if filters.end_time and entry.timestamp and entry.timestamp > filters.end_time:
            return False

        # Request ID filter
        if filters.request_ids and entry.request_id not in filters.request_ids:
            return False

        # Endpoint filter
        if filters.endpoints and entry.endpoint not in filters.endpoints:
            return False

        # Execution time filters
        if (
            filters.min_execution_time_ms
            and entry.execution_time_ms is not None
            and entry.execution_time_ms < filters.min_execution_time_ms
        ):
            return False
        if (
            filters.max_execution_time_ms
            and entry.execution_time_ms is not None
            and entry.execution_time_ms > filters.max_execution_time_ms
        ):
            return False

        # Query type filter
        if filters.query_types and entry.query_type not in filters.query_types:
            return False

        # Table filter
        if (
            filters.tables
            and entry.tables
            and not any(table in entry.tables for table in filters.tables)
        ):
            return False

        # Client ID filter
        if filters.client_ids and entry.client_id not in filters.client_ids:
            return False

        return True


class LogAnalyzer:
    """Performs analysis and statistics generation on log entries."""

    def generate_request_summaries(
        self, entries: List[SQLLogEntry], slow_query_threshold_ms: float = 1000.0
    ) -> List[RequestSummary]:
        """Generate summary statistics grouped by request ID.

        Args:
            entries: List of log entries to analyze
            slow_query_threshold_ms: Threshold for considering queries as slow

        Returns:
            List of RequestSummary objects
        """
        request_groups = defaultdict(list)

        # Group entries by request ID
        for entry in entries:
            request_groups[entry.request_id].append(entry)

        summaries = []
        for request_id, request_entries in request_groups.items():
            if not request_entries:
                continue

            # Calculate statistics
            total_queries = len(request_entries)
            total_execution_time = sum(
                e.execution_time_ms if e.execution_time_ms else 0
                for e in request_entries
            )
            unique_tables = list(
                set(
                    table
                    for entry in request_entries
                    if entry.tables
                    for table in entry.tables
                )
            )

            # Count query types
            query_types = defaultdict(int)
            for entry in request_entries:
                query_types[entry.query_type] += 1

            # Count queries per table
            table_query_counts = defaultdict(int)
            for entry in request_entries:
                if entry.tables:
                    for table in entry.tables:
                        table_query_counts[table] += 1

            # Find time range
            timestamps = [
                entry.timestamp for entry in request_entries if entry.timestamp
            ]
            start_time = min(timestamps) if timestamps else None
            end_time = max(timestamps) if timestamps else None

            # Count slow queries
            slow_queries_count = sum(
                1
                for entry in request_entries
                if entry.execution_time_ms is not None
                and entry.execution_time_ms > slow_query_threshold_ms
            )

            # Get first entry details for endpoint and client_id
            first_entry = request_entries[0]

            summary = RequestSummary(
                request_id=request_id,
                endpoint=first_entry.endpoint,
                total_queries=total_queries,
                total_execution_time_ms=total_execution_time,
                unique_tables=unique_tables,
                query_types=dict(query_types),
                table_query_counts=dict(table_query_counts),
                start_time=start_time,
                end_time=end_time,
                client_id=first_entry.client_id,
                slow_queries_count=slow_queries_count,
                avg_execution_time_ms=(
                    total_execution_time / total_queries if total_queries > 0 else 0
                ),
            )
            summaries.append(summary)

        return sorted(summaries, key=lambda x: x.total_execution_time_ms, reverse=True)

    def generate_table_statistics(
        self, entries: List[SQLLogEntry]
    ) -> List[TableStatistics]:
        """Generate statistics for table access patterns.

        Args:
            entries: List of log entries to analyze

        Returns:
            List of TableStatistics objects
        """
        table_groups = defaultdict(list)
        table_requests = defaultdict(set)

        # Group entries by table
        for entry in entries:
            if entry.tables:
                for table in entry.tables:
                    table_groups[table].append(entry)
                    table_requests[table].add(entry.request_id)

        statistics = []
        for table_name, table_entries in table_groups.items():
            if not table_entries:
                continue

            total_queries = len(table_entries)
            total_execution_time = sum(
                e.execution_time_ms if e.execution_time_ms else 0 for e in table_entries
            )

            # Count query types for this table
            query_types = defaultdict(int)
            for entry in table_entries:
                query_types[entry.query_type] += 1

            stats = TableStatistics(
                table_name=table_name,
                total_queries=total_queries,
                total_execution_time_ms=total_execution_time,
                avg_execution_time_ms=(
                    total_execution_time / total_queries if total_queries > 0 else 0
                ),
                query_types=dict(query_types),
                unique_requests=len(table_requests[table_name]),
            )
            statistics.append(stats)

        return sorted(statistics, key=lambda x: x.total_queries, reverse=True)

    def find_slow_queries(
        self, entries: List[SQLLogEntry], threshold_ms: float
    ) -> Tuple[List[SQLLogEntry], Dict[str, Any]]:
        """Find slow queries and generate statistics.

        Args:
            entries: List of log entries to analyze
            threshold_ms: Threshold for considering queries as slow

        Returns:
            Tuple of (slow_queries, statistics_dict)
        """
        slow_queries = [
            entry
            for entry in entries
            if entry.execution_time_ms is not None
            and entry.execution_time_ms > threshold_ms
        ]

        # Generate statistics
        statistics = {
            "total_slow_queries": len(slow_queries),
            "threshold_ms": threshold_ms,
            "slowest_query_ms": max(
                (
                    q.execution_time_ms
                    for q in slow_queries
                    if q.execution_time_ms is not None
                ),
                default=0,
            ),
            "avg_slow_query_ms": (
                sum(
                    q.execution_time_ms
                    for q in slow_queries
                    if q.execution_time_ms is not None
                )
                / len(slow_queries)
                if slow_queries
                else 0
            ),
            "slow_queries_by_table": {},
            "slow_queries_by_endpoint": {},
            "slow_queries_by_type": {},
        }

        # Group slow queries by various dimensions
        if slow_queries:
            # By table
            table_counts = defaultdict(int)
            for query in slow_queries:
                if query.tables:
                    for table in query.tables:
                        table_counts[table] += 1
            statistics["slow_queries_by_table"] = dict(table_counts)

            # By endpoint
            endpoint_counts = defaultdict(int)
            for query in slow_queries:
                endpoint_counts[query.endpoint] += 1
            statistics["slow_queries_by_endpoint"] = dict(endpoint_counts)

            # By query type
            type_counts = defaultdict(int)
            for query in slow_queries:
                type_counts[query.query_type] += 1
            statistics["slow_queries_by_type"] = dict(type_counts)

        # Sort slow queries by execution time (slowest first)
        slow_queries.sort(
            key=lambda x: x.execution_time_ms if x.execution_time_ms is not None else 0,
            reverse=True,
        )

        return slow_queries, statistics


class SQLLogAnalysisService:
    """Main service class that orchestrates log analysis operations."""

    def __init__(self, log_directory: str):
        """Initialize the service with a log directory.

        Args:
            log_directory: Path to the directory containing log files
        """
        self.file_manager = LogFileManager(log_directory)
        self.parser = LogParser()
        self.filter = LogFilter()
        self.analyzer = LogAnalyzer()

    def _stream_paginated_entries(
        self,
        entries_iterator: Iterator[SQLLogEntry],
        limit: Optional[int] = None,
        offset: int = 0,
        estimate_total: bool = False,
    ) -> Tuple[List[SQLLogEntry], int]:
        """Stream entries with memory-efficient pagination.

        Args:
            entries_iterator: Iterator of SQLLogEntry objects
            limit: Maximum number of entries to return
            offset: Number of entries to skip
            estimate_total: If True, stops counting after reaching limit + offset

        Returns:
            Tuple of (paginated_entries, total_count_or_estimate)
        """
        current_index = 0
        result = []

        for entry in entries_iterator:
            if offset > 0 and current_index < offset:
                # Skip entries before offset
                current_index += 1
                continue

            # If we haven't reached the limit, collect the entry
            if not limit or len(result) < limit:
                result.append(entry)
                current_index += 1
                continue

            # We've collected enough entries
            if estimate_total:
                # For estimation, we can stop here and provide current count + estimate
                # This avoids processing the entire file
                current_index += 1  # Count this entry we didn't collect
                break
            # Continue counting for exact total
            current_index += 1

        return result, current_index

    def _get_analysis_entries(  # noqa: PLR0913 - argument count
        self,
        filename: str,
        filters: Optional[AnalysisFilters] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        need_all_for_analysis: bool = False,
    ) -> Tuple[List[SQLLogEntry], int, int]:
        """Get entries for analysis with memory-efficient processing.

        Args:
            filename: Name of the log file
            filters: Optional filters to apply
            limit: Maximum entries to return for pagination
            offset: Offset for pagination
            need_all_for_analysis: If True, processes all entries for analysis components

        Returns:
            Tuple of (paginated_entries, total_filtered_count, analysis_entries_count)
        """
        file_path = self.file_manager.get_file_path(filename)

        if need_all_for_analysis:
            # When we need analysis components (summaries, statistics), we need all entries
            # Use streaming approach but collect all for analysis
            entries_iterator = self.parser.parse_json_log_file(file_path)
            filtered_entries_iterator = self.filter.apply_filters(
                entries_iterator, filters
            )

            all_entries = list(filtered_entries_iterator)
            total_filtered = len(all_entries)

            # Apply pagination to the collected entries
            if limit:
                paginated_entries = all_entries[offset : offset + limit]
            else:
                paginated_entries = (
                    all_entries[offset:].copy() if offset else all_entries
                )

            return paginated_entries, total_filtered, total_filtered

        # For simple entry listing, use streaming pagination
        entries_iterator = self.parser.parse_json_log_file(file_path)
        filtered_entries_iterator = self.filter.apply_filters(entries_iterator, filters)

        # Use estimation for better performance when possible
        estimate_total = (
            limit is not None and limit <= SAMPLE_SIZE_LIMIT
        )  # Estimate for small page sizes

        paginated_entries, total_count = self._stream_paginated_entries(
            filtered_entries_iterator, limit, offset, estimate_total
        )

        return paginated_entries, total_count, len(paginated_entries)

    def list_available_files(self) -> List[LogFileInfo]:
        """List all available log files.

        Returns:
            List of LogFileInfo objects
        """
        return self.file_manager.list_log_files()

    def analyze_log_file(  # noqa: PLR0913 - argument count
        self,
        filename: str,
        filters: Optional[AnalysisFilters] = None,
        include_request_summaries: bool = True,
        include_table_statistics: bool = True,
        include_slow_queries: bool = True,
        slow_query_threshold_ms: float = DEFAULT_SLOW_QUERY_THRESHOLD_MS,
        limit: Optional[int] = None,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """Perform comprehensive analysis of a log file.

        Args:
            filename: Name of the log file to analyze
            filters: Optional filters to apply
            include_request_summaries: Whether to generate request summaries
            include_table_statistics: Whether to generate table statistics
            include_slow_queries: Whether to find slow queries
            slow_query_threshold_ms: Threshold for slow query detection
            limit: Maximum number of entries to return
            offset: Offset for pagination

        Returns:
            Dictionary containing analysis results
        """
        start_time = datetime.now(timezone.utc)

        if not self.file_manager.validate_file_access(filename):
            raise FileNotFoundError(f"Log file not accessible: {filename}")

        # Determine if we need all entries for analysis components
        need_all_for_analysis = (
            include_request_summaries
            or include_table_statistics
            or include_slow_queries
        )

        # Get entries using memory-efficient approach
        entries, total_filtered, analysis_entries_count = self._get_analysis_entries(
            filename=filename,
            filters=filters,
            limit=limit,
            offset=offset,
            need_all_for_analysis=need_all_for_analysis,
        )

        # Prepare base result
        result = {
            "total_entries": total_filtered,
            "filtered_entries": len(entries),
            "entries": entries,
            "analysis_metadata": {
                "processing_time_ms": (
                    datetime.now(timezone.utc) - start_time
                ).total_seconds()
                * 1000,
                "filters_applied": filters.model_dump() if filters else None,
                "slow_query_threshold_ms": slow_query_threshold_ms,
                "analyzed_at": datetime.now(timezone.utc).isoformat(),
                "analysis_entries_processed": analysis_entries_count,
                "memory_efficient_mode": not need_all_for_analysis,
            },
        }

        # Generate analysis components if requested
        # Note: For analysis components, we use all filtered entries, not just the paginated ones
        if need_all_for_analysis and analysis_entries_count > len(entries):
            # We processed more entries for analysis than returned for pagination
            # Get all entries again for analysis (this is the trade-off for analysis accuracy)
            file_path = self.file_manager.get_file_path(filename)
            entries_iterator = self.parser.parse_json_log_file(file_path)
            filtered_entries_iterator = self.filter.apply_filters(
                entries_iterator, filters
            )
            all_analysis_entries = list(filtered_entries_iterator)
        if not (need_all_for_analysis and analysis_entries_count > len(entries)):
            all_analysis_entries = entries

        if include_request_summaries:
            result["request_summaries"] = self.analyzer.generate_request_summaries(
                all_analysis_entries, slow_query_threshold_ms
            )

        if include_table_statistics:
            result["table_statistics"] = self.analyzer.generate_table_statistics(
                all_analysis_entries
            )

        if include_slow_queries:
            slow_queries, slow_stats = self.analyzer.find_slow_queries(
                all_analysis_entries, slow_query_threshold_ms
            )
            result["slow_queries"] = slow_queries
            result["analysis_metadata"]["slow_query_statistics"] = slow_stats

        return result

    def get_request_summaries(
        self,
        filename: str,
        filters: Optional[AnalysisFilters] = None,
        limit: Optional[int] = None,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """Get request summaries from a log file.

        Args:
            filename: Name of the log file to analyze
            filters: Optional filters to apply
            limit: Maximum number of summaries to return
            offset: Offset for pagination

        Returns:
            Dictionary containing request summaries and metadata
        """
        if not self.file_manager.validate_file_access(filename):
            raise FileNotFoundError(f"Log file not accessible: {filename}")

        file_path = self.file_manager.get_file_path(filename)

        # For request summaries, we need all filtered entries to generate accurate summaries
        # Use streaming approach but collect all entries
        entries_iterator = self.parser.parse_json_log_file(file_path)
        filtered_entries_iterator = self.filter.apply_filters(entries_iterator, filters)

        # Stream all filtered entries for summary generation
        filtered_entries = list(filtered_entries_iterator)

        # Generate summaries from all filtered entries
        summaries = self.analyzer.generate_request_summaries(filtered_entries)

        # Apply pagination to summaries (not to entries)
        total_summaries = len(summaries)
        if offset:
            summaries = summaries[offset:]
        if limit:
            summaries = summaries[:limit]

        return {
            "total_requests": total_summaries,
            "summaries": summaries,
            "analysis_metadata": {
                "filters_applied": filters.model_dump() if filters else None,
                "total_entries_analyzed": len(filtered_entries),
                "memory_efficient_mode": False,  # We need all entries for accurate summaries
            },
        }

    def get_queries_for_request(
        self,
        filename: str,
        request_id: str,
    ) -> Dict[str, Any]:
        """Get all queries for a specific request ID.

        Args:
            filename: Name of the log file to analyze
            request_id: The specific request ID to get queries for

        Returns:
            Dictionary containing queries and metadata for the request
        """
        start_time = datetime.now(timezone.utc)

        if not self.file_manager.validate_file_access(filename):
            raise FileNotFoundError(f"Log file not accessible: {filename}")

        file_path = self.file_manager.get_file_path(filename)

        # Parse entries and filter by request ID
        entries_iterator = self.parser.parse_json_log_file(file_path)
        request_queries = [
            entry for entry in entries_iterator if entry.request_id == request_id
        ]

        # Sort queries by query_number to maintain chronological order
        request_queries.sort(key=lambda x: x.query_number or 0)

        return {
            "request_id": request_id,
            "filename": filename,
            "queries": request_queries,
            "total_queries": len(request_queries),
            "analysis_metadata": {
                "processing_time_ms": (
                    datetime.now(timezone.utc) - start_time
                ).total_seconds()
                * 1000,
                "analyzed_at": datetime.now(timezone.utc).isoformat(),
            },
        }
