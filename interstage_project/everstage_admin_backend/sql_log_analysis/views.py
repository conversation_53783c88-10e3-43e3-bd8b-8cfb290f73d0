"""SQL log analysis API views."""

import logging
from pathlib import Path

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view

from .models import (
    AnalysisRequest,
    AnalysisResponse,
    ErrorResponse,
    RequestQueriesRequest,
    RequestQueriesResponse,
    RequestSummaryRequest,
    RequestSummaryResponse,
)
from .services import SQLLogAnalysisService

logger = logging.getLogger(__name__)


class BaseView(APIView):
    """
    Base view for datasheet to be inherited by all datasheet views
    It uses default REST framework JSONRenderer for rendering the response
    instead of CamelCaseJSONRenderer to avoid conversions of keys to camel case
    """

    renderer_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON>]


def get_log_analysis_service() -> SQLLogAnalysisService:
    """Factory function to create SQLLogAnalysisService instance."""
    # Get BASE_DIR from settings
    from interstage_project.settings import BASE_DIR

    log_directory = Path(BASE_DIR).resolve() / "logs"
    return SQLLogAnalysisService(str(log_directory))


class ListLogFilesView(BaseView):
    """API endpoint to list available SQL log files."""

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_ADMINUI.value]), name="dispatch"
    )
    @add_log_context_view("ListLogFiles")
    def get(self, request):  # noqa: ARG002 - request parameter required by Django
        """List available SQL log files with metadata."""
        try:
            logger.info("BEGIN: List SQL log files")

            service = get_log_analysis_service()
            log_files = service.list_available_files()

            logger.info(f"END: List SQL log files - found {len(log_files)} files")

            return Response(
                {"log_files": [file.model_dump() for file in log_files]},
                status=status.HTTP_200_OK,
            )

        except Exception as e:  # noqa: BLE001 - service errors should not break request
            logger.exception("Error listing SQL log files")
            error_response = ErrorResponse(
                error="Failed to list log files",
                details=str(e),
                error_code="LOG_FILES_LIST_ERROR",
            )
            return Response(
                error_response.model_dump(),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AnalyzeLogFileView(BaseView):
    """API endpoint for comprehensive log file analysis."""

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_ADMINUI.value]), name="dispatch"
    )
    @add_log_context_view("AnalyzeLogFile")
    def post(self, request):
        """Perform comprehensive analysis of a SQL log file."""
        try:
            logger.info("BEGIN: Analyze SQL log file", extra={"payload": request.data})

            # Validate request data
            try:
                analysis_request = AnalysisRequest(**request.data)
            except Exception:  # noqa: BLE001 - service errors should not break request
                logger.warning("Invalid analysis request data")
                error_response = ErrorResponse(
                    error="Invalid request data",
                    details="Request validation failed",
                    error_code="VALIDATION_ERROR",
                )
                return Response(
                    error_response.model_dump(), status=status.HTTP_400_BAD_REQUEST
                )

            service = get_log_analysis_service()

            # Perform analysis
            result = service.analyze_log_file(
                filename=analysis_request.filename,
                filters=analysis_request.filters,
                include_request_summaries=analysis_request.include_request_summaries,
                include_table_statistics=analysis_request.include_table_statistics,
                include_slow_queries=analysis_request.include_slow_queries,
                slow_query_threshold_ms=analysis_request.slow_query_threshold_ms,
                limit=analysis_request.limit_value,
                offset=analysis_request.offset_value or 0,
            )

            # Convert result to response model
            response_data = AnalysisResponse(
                total_entries=result["total_entries"],
                filtered_entries=result["filtered_entries"],
                entries=[entry.model_dump() for entry in result["entries"]],
                request_summaries=(
                    [
                        summary.model_dump()
                        for summary in result.get("request_summaries", [])
                    ]
                    if result.get("request_summaries")
                    else None
                ),
                table_statistics=(
                    [stats.model_dump() for stats in result.get("table_statistics", [])]
                    if result.get("table_statistics")
                    else None
                ),
                slow_queries=(
                    [query.model_dump() for query in result.get("slow_queries", [])]
                    if result.get("slow_queries")
                    else None
                ),
                analysis_metadata=result["analysis_metadata"],
            )

            logger.info(
                f"END: Analyze SQL log file - processed {result['filtered_entries']} entries"
            )

            return Response(response_data.model_dump(), status=status.HTTP_200_OK)

        except FileNotFoundError as not_found_error:
            logger.warning("Log file not found: %s", not_found_error)
            error_response = ErrorResponse(
                error="Log file not found",
                details=str(not_found_error),
                error_code="FILE_NOT_FOUND",
            )
            return Response(
                error_response.model_dump(), status=status.HTTP_404_NOT_FOUND
            )

        except (ValueError, TypeError) as validation_error:
            logger.warning("Invalid request data: %s", validation_error)
            error_response = ErrorResponse(
                error="Invalid request data",
                details=str(validation_error),
                error_code="VALIDATION_ERROR",
            )
            return Response(
                error_response.model_dump(), status=status.HTTP_400_BAD_REQUEST
            )

        except (
            Exception  # noqa: BLE001 - view errors should not break request
        ) as unexpected_error:
            logger.exception("Error analyzing SQL log file")
            error_response = ErrorResponse(
                error="Failed to analyze log file",
                details=str(unexpected_error),
                error_code="ANALYSIS_ERROR",
            )
            return Response(
                error_response.model_dump(),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetRequestSummariesView(BaseView):
    """API endpoint for getting request-level summaries from log files."""

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_ADMINUI.value]), name="dispatch"
    )
    @add_log_context_view("GetRequestSummaries")
    def post(self, request):
        """Get request summaries from a SQL log file."""
        try:
            logger.info("BEGIN: Get request summaries", extra={"payload": request.data})

            # Validate request data
            try:
                summary_request = RequestSummaryRequest(**request.data)
            except (
                Exception  # noqa: BLE001 - view errors should not break request
            ) as e:
                logger.warning(f"Invalid request data: {e}")
                error_response = ErrorResponse(
                    error="Invalid request data",
                    details=str(e),
                    error_code="VALIDATION_ERROR",
                )
                return Response(
                    error_response.model_dump(), status=status.HTTP_400_BAD_REQUEST
                )

            service = get_log_analysis_service()

            # Get summaries
            result = service.get_request_summaries(
                filename=summary_request.filename,
                filters=summary_request.filters,
                limit=summary_request.limit_value,
                offset=summary_request.offset_value or 0,
            )

            # Convert result to response model
            response_data = RequestSummaryResponse(
                total_requests=result["total_requests"],
                summaries=[summary.model_dump() for summary in result["summaries"]],
                analysis_metadata=result["analysis_metadata"],
            )

            logger.info(
                f"END: Get request summaries - returned {len(result['summaries'])} summaries"
            )

            return Response(response_data.model_dump(), status=status.HTTP_200_OK)

        except FileNotFoundError as e:
            logger.warning(f"Log file not found: {e}")
            error_response = ErrorResponse(
                error="Log file not found", details=str(e), error_code="FILE_NOT_FOUND"
            )
            return Response(
                error_response.model_dump(), status=status.HTTP_404_NOT_FOUND
            )

        except Exception:  # noqa: BLE001 - service errors should not break request
            logger.exception("Error getting request summaries")
            error_response = ErrorResponse(
                error="Failed to get request summaries",
                details="Service error occurred",
                error_code="SUMMARIES_ERROR",
            )
            return Response(
                error_response.model_dump(),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetQueriesForRequestView(BaseView):
    """API endpoint for getting queries for a specific request ID."""

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_ADMINUI.value]), name="dispatch"
    )
    @add_log_context_view("GetQueriesForRequest")
    def post(self, request):
        """Get queries for a specific request ID from a SQL log file."""
        try:
            logger.info(
                "BEGIN: Get queries for request", extra={"payload": request.data}
            )

            # Validate request data
            try:
                request_queries_request = RequestQueriesRequest(**request.data)
            except Exception:  # noqa: BLE001 - service errors should not break request
                logger.warning("Invalid request queries data")
                error_response = ErrorResponse(
                    error="Invalid request data",
                    details="Request validation failed",
                    error_code="VALIDATION_ERROR",
                )
                return Response(
                    error_response.model_dump(), status=status.HTTP_400_BAD_REQUEST
                )

            service = get_log_analysis_service()

            # Get queries for the request
            result = service.get_queries_for_request(
                filename=request_queries_request.filename,
                request_id=request_queries_request.request_id,
            )

            # Convert result to response model
            response_data = RequestQueriesResponse(
                request_id=result["request_id"],
                filename=result["filename"],
                queries=[query.model_dump() for query in result["queries"]],
                total_queries=result["total_queries"],
                analysis_metadata=result["analysis_metadata"],
            )

            logger.info(
                f"END: Get queries for request {request_queries_request.request_id} - returned {result['total_queries']} queries"
            )

            return Response(response_data.model_dump(), status=status.HTTP_200_OK)

        except FileNotFoundError as e:
            logger.warning(f"Log file not found: {e}")
            error_response = ErrorResponse(
                error="Log file not found", details=str(e), error_code="FILE_NOT_FOUND"
            )
            return Response(
                error_response.model_dump(), status=status.HTTP_404_NOT_FOUND
            )

        except Exception as e:
            logger.exception("Error getting queries for request")
            error_response = ErrorResponse(
                error="Failed to get queries for request",
                details=str(e),
                error_code="REQUEST_QUERIES_ERROR",
            )
            return Response(
                error_response.model_dump(),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
