"""Pydantic models for SQL log analysis.

This module contains data models used for SQL log analysis API endpoints.
All models use Pydantic for validation and serialization.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class SQLLogEntry(BaseModel):
    """Individual SQL log entry from JSON format."""

    timestamp: Optional[datetime] = None
    request_id: Optional[str] = None
    endpoint: Optional[str] = None
    query_number: Optional[int] = None
    thread_id: Optional[str] = None
    execution_time_ms: Optional[float] = None
    database_type: Optional[str] = None
    tables: Optional[List[str]] = None
    query_type: Optional[str] = None
    query: Optional[str] = None
    query_simplified: Optional[str] = None
    arguments: Optional[List[Any]] = None
    is_simplified: Optional[bool] = None
    query_hash: Optional[str] = None
    client_id: Optional[int] = None
    log_level: Optional[str] = None
    is_muted: Optional[bool] = None


class RequestSummary(BaseModel):
    """Summary statistics for a single request ID."""

    request_id: str
    endpoint: str
    total_queries: int
    total_execution_time_ms: float
    unique_tables: List[str]
    query_types: Dict[str, int] = Field(description="Count of each query type")
    table_query_counts: Dict[str, int] = Field(description="Query count per table")
    start_time: datetime
    end_time: datetime
    client_id: Optional[int] = None
    slow_queries_count: int = Field(description="Number of queries over threshold")
    avg_execution_time_ms: float


class TableStatistics(BaseModel):
    """Statistics for table access patterns."""

    table_name: str
    total_queries: int
    total_execution_time_ms: float
    avg_execution_time_ms: float
    query_types: Dict[str, int]
    unique_requests: int = Field(description="Number of unique request IDs")


class LogFileInfo(BaseModel):
    """Information about a log file."""

    filename: str
    filepath: str
    size_bytes: int
    created_time: datetime
    modified_time: datetime
    is_current: bool = Field(description="Whether this is the current active log file")
    estimated_entries: Optional[int] = None


class AnalysisFilters(BaseModel):
    """Filters for log analysis."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    request_ids: Optional[List[str]] = None
    endpoints: Optional[List[str]] = None
    min_execution_time_ms: Optional[float] = None
    max_execution_time_ms: Optional[float] = None
    query_types: Optional[List[str]] = None
    tables: Optional[List[str]] = None
    client_ids: Optional[List[int]] = None


class AnalysisRequest(BaseModel):
    """Request payload for log analysis."""

    filename: str
    filters: Optional[AnalysisFilters] = None
    include_request_summaries: bool = True
    include_table_statistics: bool = True
    include_slow_queries: bool = True
    slow_query_threshold_ms: float = 1000.0
    limit_value: Optional[int] = Field(
        default=1000, description="Max number of entries to return"
    )
    offset_value: Optional[int] = Field(default=0, description="Offset for pagination")


class AnalysisResponse(BaseModel):
    """Response from log analysis."""

    total_entries: int
    filtered_entries: int
    entries: List[SQLLogEntry]
    request_summaries: Optional[List[RequestSummary]] = None
    table_statistics: Optional[List[TableStatistics]] = None
    slow_queries: Optional[List[SQLLogEntry]] = None
    analysis_metadata: Dict[str, Any] = Field(
        description="Additional metadata like processing time, filters applied, etc."
    )


class RequestSummaryRequest(BaseModel):
    """Request payload for getting request summaries."""

    filename: str
    filters: Optional[AnalysisFilters] = None
    group_by_endpoint: bool = False
    limit_value: Optional[int] = Field(
        default=100, description="Max number of summaries to return"
    )
    offset_value: Optional[int] = Field(default=0, description="Offset for pagination")


class RequestSummaryResponse(BaseModel):
    """Response from request summary analysis."""

    total_requests: int
    summaries: List[RequestSummary]
    endpoint_statistics: Optional[Dict[str, Any]] = None
    analysis_metadata: Dict[str, Any]


class RequestQueriesRequest(BaseModel):
    """Request payload for getting queries for a specific request ID."""

    filename: str
    request_id: str


class RequestQueriesResponse(BaseModel):
    """Response with queries for a specific request ID."""

    request_id: str
    filename: str
    queries: List[SQLLogEntry]
    total_queries: int
    analysis_metadata: Dict[str, Any]


class ErrorResponse(BaseModel):
    """Standard error response."""

    error: str
    details: Optional[str] = None
    error_code: Optional[str] = None
