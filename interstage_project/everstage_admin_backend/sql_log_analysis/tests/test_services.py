"""Unit tests for SQL log analysis service.

This demonstrates how the service layer is easily testable due to clear
separation of concerns and dependency injection.
"""

import json
import tempfile
import unittest
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import patch

from ..models import AnalysisFilters, SQLLogEntry
from ..services import (
    LogAnalyzer,
    LogFileManager,
    LogFilter,
    LogParser,
    SQLLogAnalysisService,
)


class TestLogFileManager(unittest.TestCase):
    """Test LogFileManager functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = LogFileManager(self.temp_dir)

    def test_list_log_files_empty_directory(self):
        """Test listing files in empty directory."""
        files = self.manager.list_log_files()
        self.assertEqual(files, [])

    def test_validate_file_access_nonexistent_file(self):
        """Test validation of non-existent file."""
        result = self.manager.validate_file_access("nonexistent.json")
        self.assertFalse(result)

    def test_validate_file_access_existing_file(self):
        """Test validation of existing file."""
        # Create a test file
        test_file = Path(self.temp_dir) / "test.json"
        test_file.touch()

        result = self.manager.validate_file_access("test.json")
        self.assertTrue(result)

    def test_get_file_path(self):
        """Test getting full file path."""
        result = self.manager.get_file_path("test.json")
        expected = str(Path(self.temp_dir) / "test.json")
        self.assertEqual(result, expected)


class TestLogParser(unittest.TestCase):
    """Test LogParser functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.parser = LogParser()

    def test_parse_json_log_file_valid_entries(self):
        """Test parsing valid JSON log entries."""
        # Create test data
        test_data = [
            {
                "timestamp": "2025-06-29T16:12:53.007+05:30",
                "request_id": "test-request-1",
                "endpoint": "/test",
                "query_number": 1,
                "thread_id": "123",
                "execution_time_ms": 15.5,
                "database_type": "postgresql",
                "tables": ["users"],
                "query_type": "SELECT",
                "query": "SELECT * FROM users",
                "query_simplified": "SELECT * FROM users",
                "arguments": [],
                "is_simplified": False,
                "query_hash": "abc123",
                "client_id": 1,
                "log_level": "DEBUG",
                "is_muted": False,
            }
        ]

        # Create temporary file with test data
        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".json") as f:
            for entry in test_data:
                f.write(json.dumps(entry) + "\n")
            temp_file = f.name

        try:
            # Parse the file
            entries = list(self.parser.parse_json_log_file(temp_file))

            self.assertEqual(len(entries), 1)
            self.assertIsInstance(entries[0], SQLLogEntry)
            self.assertEqual(entries[0].request_id, "test-request-1")
            self.assertEqual(entries[0].endpoint, "/test")
            self.assertEqual(entries[0].execution_time_ms, 15.5)

        finally:
            Path(temp_file).unlink()

    def test_parse_json_log_file_invalid_json(self):
        """Test parsing file with invalid JSON."""
        # Create temporary file with invalid JSON
        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".json") as f:
            f.write('{"invalid": json}\n')
            f.write(
                '{"valid": "entry", "timestamp": "2025-06-29T16:12:53.007+05:30"}\n'
            )
            temp_file = f.name

        try:
            # Parse the file - should skip invalid entries but parse valid ones
            entries = list(self.parser.parse_json_log_file(temp_file))

            # Should have 1 entry since the second line is valid JSON (even with minimal fields)
            # SQLLogEntry model has all Optional fields, so a partial entry can be created
            self.assertEqual(len(entries), 1)
            self.assertIsInstance(entries[0], SQLLogEntry)

        finally:
            Path(temp_file).unlink()


class TestLogFilter(unittest.TestCase):
    """Test LogFilter functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.filter = LogFilter()

        # Create sample entries
        self.entries = [
            SQLLogEntry(
                timestamp=datetime(2025, 6, 29, 10, 0, 0, tzinfo=timezone.utc),
                request_id="req-1",
                endpoint="/api/users",
                query_number=1,
                thread_id="123",
                execution_time_ms=100.0,
                database_type="postgresql",
                tables=["users"],
                query_type="SELECT",
                query="SELECT * FROM users",
                query_simplified="SELECT * FROM users",
                arguments=[],
                is_simplified=False,
                query_hash="hash1",
                client_id=1,
                log_level="DEBUG",
                is_muted=False,
            ),
            SQLLogEntry(
                timestamp=datetime(2025, 6, 29, 11, 0, 0, tzinfo=timezone.utc),
                request_id="req-2",
                endpoint="/api/orders",
                query_number=2,
                thread_id="124",
                execution_time_ms=200.0,
                database_type="postgresql",
                tables=["orders"],
                query_type="INSERT",
                query="INSERT INTO orders VALUES (...)",
                query_simplified="INSERT INTO orders VALUES (...)",
                arguments=[],
                is_simplified=False,
                query_hash="hash2",
                client_id=2,
                log_level="DEBUG",
                is_muted=False,
            ),
        ]

    def test_apply_filters_no_filters(self):
        """Test applying no filters returns all entries."""
        filtered = list(self.filter.apply_filters(iter(self.entries), None))
        self.assertEqual(len(filtered), 2)

    def test_apply_filters_by_endpoint(self):
        """Test filtering by endpoint."""
        filters = AnalysisFilters(endpoints=["/api/users"])
        filtered = list(self.filter.apply_filters(iter(self.entries), filters))

        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].endpoint, "/api/users")

    def test_apply_filters_by_execution_time(self):
        """Test filtering by execution time."""
        filters = AnalysisFilters(min_execution_time_ms=150.0)
        filtered = list(self.filter.apply_filters(iter(self.entries), filters))

        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].execution_time_ms, 200.0)

    def test_apply_filters_by_client_id(self):
        """Test filtering by client ID."""
        filters = AnalysisFilters(client_ids=[1])
        filtered = list(self.filter.apply_filters(iter(self.entries), filters))

        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].client_id, 1)


class TestLogAnalyzer(unittest.TestCase):
    """Test LogAnalyzer functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = LogAnalyzer()

        # Create sample entries with same request ID
        self.entries = [
            SQLLogEntry(
                timestamp=datetime(2025, 6, 29, 10, 0, 0, tzinfo=timezone.utc),
                request_id="req-1",
                endpoint="/api/users",
                query_number=1,
                thread_id="123",
                execution_time_ms=100.0,
                database_type="postgresql",
                tables=["users"],
                query_type="SELECT",
                query="SELECT * FROM users",
                query_simplified="SELECT * FROM users",
                arguments=[],
                is_simplified=False,
                query_hash="hash1",
                client_id=1,
                log_level="DEBUG",
                is_muted=False,
            ),
            SQLLogEntry(
                timestamp=datetime(2025, 6, 29, 10, 0, 1, tzinfo=timezone.utc),
                request_id="req-1",
                endpoint="/api/users",
                query_number=2,
                thread_id="123",
                execution_time_ms=1500.0,  # Slow query
                database_type="postgresql",
                tables=["users", "orders"],
                query_type="SELECT",
                query="SELECT * FROM users JOIN orders",
                query_simplified="SELECT * FROM users JOIN orders",
                arguments=[],
                is_simplified=False,
                query_hash="hash2",
                client_id=1,
                log_level="DEBUG",
                is_muted=False,
            ),
        ]

    def test_generate_request_summaries(self):
        """Test generating request summaries."""
        summaries = self.analyzer.generate_request_summaries(
            self.entries, slow_query_threshold_ms=1000.0
        )

        self.assertEqual(len(summaries), 1)
        summary = summaries[0]

        self.assertEqual(summary.request_id, "req-1")
        self.assertEqual(summary.total_queries, 2)
        self.assertEqual(summary.total_execution_time_ms, 1600.0)
        self.assertEqual(summary.slow_queries_count, 1)
        self.assertEqual(set(summary.unique_tables), {"users", "orders"})

    def test_generate_table_statistics(self):
        """Test generating table statistics."""
        stats = self.analyzer.generate_table_statistics(self.entries)

        # Should have stats for both users and orders tables
        self.assertEqual(len(stats), 2)

        # Find users table stats
        users_stats = next(s for s in stats if s.table_name == "users")
        self.assertEqual(
            users_stats.total_queries, 2
        )  # Both queries involve users table
        self.assertEqual(users_stats.unique_requests, 1)

        # Find orders table stats
        orders_stats = next(s for s in stats if s.table_name == "orders")
        self.assertEqual(
            orders_stats.total_queries, 1
        )  # Only one query involves orders table
        self.assertEqual(orders_stats.unique_requests, 1)

    def test_find_slow_queries(self):
        """Test finding slow queries."""
        slow_queries, stats = self.analyzer.find_slow_queries(
            self.entries, threshold_ms=1000.0
        )

        self.assertEqual(len(slow_queries), 1)
        self.assertEqual(slow_queries[0].execution_time_ms, 1500.0)

        self.assertEqual(stats["total_slow_queries"], 1)
        self.assertEqual(stats["threshold_ms"], 1000.0)
        self.assertEqual(stats["slowest_query_ms"], 1500.0)


class TestSQLLogAnalysisService(unittest.TestCase):
    """Test main SQLLogAnalysisService integration."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.service = SQLLogAnalysisService(self.temp_dir)

        # Create sample entries for testing
        self.sample_entries = [
            SQLLogEntry(
                timestamp=datetime(2025, 6, 29, 10, 0, 0, tzinfo=timezone.utc),
                request_id="req-1",
                endpoint="/api/users",
                query_number=1,
                thread_id="123",
                execution_time_ms=100.0,
                database_type="postgresql",
                tables=["users"],
                query_type="SELECT",
                query="SELECT * FROM users",
                query_simplified="SELECT * FROM users",
                arguments=[],
                is_simplified=False,
                query_hash="hash1",
                client_id=1,
                log_level="DEBUG",
                is_muted=False,
            ),
            SQLLogEntry(
                timestamp=datetime(2025, 6, 29, 11, 0, 0, tzinfo=timezone.utc),
                request_id="req-2",
                endpoint="/api/orders",
                query_number=2,
                thread_id="124",
                execution_time_ms=200.0,
                database_type="postgresql",
                tables=["orders"],
                query_type="INSERT",
                query="INSERT INTO orders VALUES (...)",
                query_simplified="INSERT INTO orders VALUES (...)",
                arguments=[],
                is_simplified=False,
                query_hash="hash2",
                client_id=2,
                log_level="DEBUG",
                is_muted=False,
            ),
        ]

    def test_list_available_files(self):
        """Test listing available files."""
        files = self.service.list_available_files()
        self.assertIsInstance(files, list)

    @patch.object(LogFileManager, "validate_file_access")
    @patch.object(LogParser, "parse_json_log_file")
    def test_analyze_log_file_file_not_found(self, mock_parse, mock_validate):
        """Test analyzing non-existent file."""
        mock_validate.return_value = False

        with self.assertRaises(FileNotFoundError):
            self.service.analyze_log_file("nonexistent.json")

    @patch.object(LogFileManager, "validate_file_access")
    @patch.object(LogFileManager, "get_file_path")
    @patch.object(LogParser, "parse_json_log_file")
    def test_analyze_log_file_success(self, mock_parse, mock_get_path, mock_validate):
        """Test successful log file analysis."""
        # Setup mocks
        mock_validate.return_value = True
        mock_get_path.return_value = "/path/to/test.json"
        mock_parse.return_value = iter(self.sample_entries)

        # Test without analysis components (memory efficient mode)
        result = self.service.analyze_log_file(
            filename="test.json",
            include_request_summaries=False,
            include_table_statistics=False,
            include_slow_queries=False,
            limit=1,
            offset=0,
        )

        self.assertEqual(
            result["total_entries"], 2
        )  # Total count of all entries processed
        self.assertEqual(
            result["filtered_entries"], 1
        )  # Only 1 entry returned due to limit
        self.assertEqual(len(result["entries"]), 1)
        self.assertTrue(result["analysis_metadata"]["memory_efficient_mode"])
        self.assertNotIn("request_summaries", result)
        self.assertNotIn("table_statistics", result)
        self.assertNotIn("slow_queries", result)

    @patch.object(LogFileManager, "validate_file_access")
    @patch.object(LogFileManager, "get_file_path")
    @patch.object(LogParser, "parse_json_log_file")
    def test_streaming_pagination_memory_efficiency(
        self, mock_parse, mock_get_path, mock_validate
    ):
        """Test that streaming pagination doesn't load unnecessary entries."""
        # Setup mocks
        mock_validate.return_value = True
        mock_get_path.return_value = "/path/to/test.json"

        # Create a larger dataset to test pagination
        large_entries = []
        for i in range(100):
            entry = SQLLogEntry(
                timestamp=datetime(
                    2025, 6, 29, 10, i % 60, i % 60, tzinfo=timezone.utc
                ),
                request_id=f"req-{i}",
                endpoint=f"/api/endpoint-{i}",
                query_number=i + 1,
                thread_id=f"thread-{i}",
                execution_time_ms=float(i * 10),
                database_type="postgresql",
                tables=[f"table_{i}"],
                query_type="SELECT",
                query=f"SELECT * FROM table_{i}",
                query_simplified=f"SELECT * FROM table_{i}",
                arguments=[],
                is_simplified=False,
                query_hash=f"hash{i}",
                client_id=1,
                log_level="DEBUG",
                is_muted=False,
            )
            large_entries.append(entry)

        mock_parse.return_value = iter(large_entries)

        # Test pagination in memory efficient mode (no analysis components)
        result = self.service.analyze_log_file(
            filename="test.json",
            include_request_summaries=False,
            include_table_statistics=False,
            include_slow_queries=False,
            limit=10,
            offset=5,
        )

        # Should only return 10 entries starting from offset 5
        self.assertEqual(len(result["entries"]), 10)
        self.assertEqual(
            result["entries"][0].request_id, "req-5"
        )  # First entry after offset
        self.assertEqual(
            result["entries"][-1].request_id, "req-14"
        )  # Last entry in page
        self.assertTrue(result["analysis_metadata"]["memory_efficient_mode"])

    @patch.object(LogFileManager, "validate_file_access")
    @patch.object(LogFileManager, "get_file_path")
    @patch.object(LogParser, "parse_json_log_file")
    def test_streaming_pagination_with_analysis_components(
        self, mock_parse, mock_get_path, mock_validate
    ):
        """Test that analysis components work correctly with streaming pagination."""
        # Setup mocks
        mock_validate.return_value = True
        mock_get_path.return_value = "/path/to/test.json"
        mock_parse.return_value = iter(self.sample_entries)

        # Test with analysis components (needs all entries)
        result = self.service.analyze_log_file(
            filename="test.json",
            include_request_summaries=True,
            include_table_statistics=True,
            include_slow_queries=True,
            limit=1,
            offset=0,
        )

        self.assertEqual(len(result["entries"]), 1)  # Only 1 entry for pagination
        self.assertFalse(result["analysis_metadata"]["memory_efficient_mode"])
        self.assertIn("request_summaries", result)
        self.assertIn("table_statistics", result)
        self.assertIn("slow_queries", result)

        # Analysis should be based on all entries, not just paginated ones
        self.assertEqual(result["analysis_metadata"]["analysis_entries_processed"], 2)

    def test_stream_paginated_entries_basic_pagination(self):
        """Test the streaming pagination helper method."""
        # Test basic pagination functionality
        entries, total = self.service._stream_paginated_entries(
            iter(self.sample_entries), limit=1, offset=0, estimate_total=False
        )

        self.assertEqual(len(entries), 1)
        self.assertEqual(entries[0].request_id, "req-1")
        self.assertEqual(total, 2)  # Total count

    def test_stream_paginated_entries_with_offset(self):
        """Test streaming pagination with offset."""
        entries, total = self.service._stream_paginated_entries(
            iter(self.sample_entries), limit=1, offset=1, estimate_total=False
        )

        self.assertEqual(len(entries), 1)
        self.assertEqual(entries[0].request_id, "req-2")
        self.assertEqual(total, 2)

    def test_stream_paginated_entries_estimation_mode(self):
        """Test streaming pagination with estimation mode."""
        entries, count = self.service._stream_paginated_entries(
            iter(self.sample_entries), limit=1, offset=0, estimate_total=True
        )

        self.assertEqual(len(entries), 1)
        # In estimation mode, it might stop early and not count all entries
        self.assertGreaterEqual(count, 1)


if __name__ == "__main__":
    unittest.main()
