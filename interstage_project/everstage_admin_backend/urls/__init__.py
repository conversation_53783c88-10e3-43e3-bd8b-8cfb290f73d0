"""
Everstage Admin Backend URL Configuration

This module centralizes URL routing for the Everstage Admin Backend application.
It includes patterns for various sub-applications and provides a clean, organized
approach to URL management.

Key features:
- Centralized URL routing
- Support for versioned APIs
- Modular URL organization
- Easy integration of new sub-applications

Usage:
    Include this module in the main Django project's URL configuration.
"""

from django.urls import include, path

from everstage_admin_backend.qa_reports.urls import urlpatterns as qa_report_urls
from everstage_admin_backend.tsar.urls import urlpatterns as tsar_urls
from everstage_admin_backend.user_management.urls import (
    urlpatterns as user_management_urls,
)

from .bulk_permissions_urls import urlpatterns as bulk_permissions_urls
from .customer_urls import urlpatterns as customer_urls
from .etl_config_urls import urlpatterns as etl_config_urls
from .feature_flags_urls import urlpatterns as feature_flags_urls
from .session_management_urls import urlpatterns as session_management_urls

urlpatterns = [
    # SQL Log Analysis with app name prefix
    path("sql-logs/", include("everstage_admin_backend.sql_log_analysis.urls")),
] + (
    bulk_permissions_urls
    + customer_urls
    + session_management_urls
    + tsar_urls
    + user_management_urls
    + qa_report_urls
    + feature_flags_urls
    + etl_config_urls
)
