from ast import literal_eval

from django.utils.decorators import method_decorator
from rest_framework.views import APIView, Request, Response

from commission_engine.accessors.client_accessor import (
    get_client,
    get_clients_by_name,
    update_client,
)
from commission_engine.models.client_models import Client
from commission_engine.utils.general_data import RbacPermissions
from commission_engine.utils.report_enums import RunSettlementReport
from everstage_admin_backend.services.customer_id_management_service import (
    update_customer_id_mapping,
)
from everstage_admin_backend.services.update_client_service import (
    has_sub_plan_changed_for_client,
    set_global_run,
    turn_off_global_run_if_no_sync_is_running,
    update_admin_cron_tasks,
    update_approval_cron_task,
    update_client_email_invite_template,
    update_hard_delete_cron,
    update_tqm_access,
    update_user_group_sync_task,
    validate_fiscal_start_month,
)
from everstage_ddd.cpq.rbac.services import (
    handle_cpq_roles_and_permissions,
    remove_cpq_roles_for_users,
)
from everstage_ddd.tqm import setup_tqm_edit_permission
from interstage_project.auth_utils import requires_scope
from spm.services.rbac_services import handle_permissions_admin_ui


def validate_files(files: dict) -> bool:
    if not files.get("file"):
        return False
    return True


def validate_body(body: dict) -> bool:
    if (
        not body.get("client_id")
        or not body.get("name")
        or not body.get("domain")
        or not body.get("connection_type")
        or not body.get("connection_name")
        or not body.get("base_currency")
        or not body.get("fiscal_start_month")
        or not body.get("timezone")
        or not body.get("show_commission_percent")
        or not body.get("show_commission_buddy")
        or not body.get("show_territory_plan")
        or not body.get("status")
        or not body.get("type")
        or not body.get("show_return_v1_button")
        or not body.get("expose_comm_reports_in_plan")
        or not body.get("show_superset_dashboard")
        or not body.get("subscription_plan")
        or not body.get("plan_summary_model")
        or not body.get("profile_picture_permission")
        or not body.get("warn_on_unlock")
        or not body.get("chrome_extension_enabled")
        or not body.get("enable_everai")
        or not body.get("enable_ever_comparison")
        or not body.get("enable_concurrent_sessions")
        or not body.get("enable_support_user_access")
        or not body.get("enable_tsar_webapp_custom_roles")
        or not body.get("is_secure_admin_ui_auth0_user_mgmt")
        or not body.get("allow_adjustments_to_frozen_commission")
        or not body.get("show_chatgpt")
        or not body.get("show_payout_table_breakdown")
        or not body.get("isolated_snowflake_database")
        or not body.get("custom_calendar")
        or not body.get("quota_effective_dated")
        or not body.get("allow_quota_settings_override")
        or not body.get("allow_annual_quota_effective_dated")
        or not body.get("allow_csv_upload_bulk_payment_register")
        or not body.get("split_summation_to_li")
        or not body.get("show_metrics")
        or not body.get("avoid_iframe_in_contracts")
        or not body.get("insert_meta_data_to_vec_db")
        or not body.get("cpq_rag_loading_enabled")
        or not body.get("use_multi_engine_stormbreaker")
        or not body.get("show_forecast")
        or not body.get("enable_custom_workflows")
        or not body.get("revalidate_datasheet_on_update")
        or not body.get("enable_custom_theme")
        or not body.get("enable_sidebar_v3")
        or not body.get("enable_multi_language_support")
        or not body.get("crystal_custom_calendar_future_periods")
        or not body.get("settlement_v2")
        or not body.get("edit_locked_quota")
        or not body.get("run_settlement_report")
        or not body.get("show_g2_review_form")
        or not body.get("allow_only_admins_to_modify_user_name")
        or not body.get("is_auto_enrich_report")
        or not body.get("async_export_datasheet")
        or not body.get("modules")
        or body.get("enable_contract_permissions") not in ["true", "false"]
        or body.get("approval_workflow_search") not in ["true", "false"]
        or body.get("show_commission_percent") not in ["true", "false"]
        or body.get("show_commission_buddy") not in ["true", "false"]
        or body.get("show_territory_plan") not in ["true", "false"]
        or body.get("status") not in ["true", "false"]
        or body.get("type") not in ["true", "false"]
        or body.get("show_return_v1_button") not in ["true", "false"]
        or body.get("expose_comm_reports_in_plan") not in ["true", "false"]
        or body.get("show_superset_dashboard") not in ["true", "false"]
        or body.get("subscription_plan")
        not in [
            "BASIC",
            "ENTERPRISE",
            "MANUAL",
            "ICU",
            "IMPL_SLOT_1",
            "IMPL_SLOT_2",
            "ETL_SLOT_1",
            "ETL_SLOT_2",
            "ETL_SLOT_3",
        ]
        or body.get("chrome_extension_enabled") not in ["true", "false"]
        or body.get("enable_everai") not in ["true", "false"]
        or body.get("plan_summary_model")
        not in ["gpt-3.5-turbo", "gpt-4", "gpt-4-1106-preview"]
        or body.get("profile_picture_permission")
        not in ["NONE", "SELF", "ADMIN", "ALL"]
        or body.get("warn_on_unlock") not in ["NONE", "SUMMARY"]
        or body.get("enable_ever_comparison") not in ["true", "false"]
        or body.get("enable_concurrent_sessions") not in ["true", "false"]
        or body.get("enable_support_user_access") not in ["true", "false"]
        or body.get("enable_tsar_webapp_custom_roles") not in ["true", "false"]
        or body.get("is_secure_admin_ui_auth0_user_mgmt") not in ["true", "false"]
        or body.get("allow_adjustments_to_frozen_commission") not in ["true", "false"]
        or body.get("show_chatgpt") not in ["true", "false"]
        or body.get("show_payout_table_breakdown") not in ["true", "false"]
        or body.get("isolated_snowflake_database") not in ["true", "false"]
        or body.get("custom_calendar") not in ["true", "false"]
        or body.get("quota_effective_dated") not in ["true", "false"]
        or body.get("allow_quota_settings_override") not in ["true", "false"]
        or body.get("allow_annual_quota_effective_dated") not in ["true", "false"]
        or body.get("allow_csv_upload_bulk_payment_register") not in ["true", "false"]
        or body.get("split_summation_to_li") not in ["true", "false"]
        or body.get("show_metrics") not in ["true", "false"]
        or body.get("avoid_iframe_in_contracts") not in ["true", "false"]
        or body.get("insert_meta_data_to_vec_db") not in ["true", "false"]
        or body.get("cpq_rag_loading_enabled") not in ["true", "false"]
        or body.get("use_multi_engine_stormbreaker") not in ["true", "false"]
        or body.get("show_forecast") not in ["true", "false"]
        or body.get("enable_custom_workflows") not in ["true", "false"]
        or body.get("revalidate_datasheet_on_update") not in ["true", "false"]
        or body.get("enable_custom_theme") not in ["true", "false"]
        or body.get("enable_sidebar_v3") not in ["true", "false"]
        or body.get("enable_multi_language_support") not in ["true", "false"]
        or body.get("run_settlement_report")
        not in [item.value for item in RunSettlementReport]
        or body.get("settlement_v2") not in ["true", "false"]
        or body.get("edit_locked_quota") not in ["true", "false"]
        or body.get("show_g2_review_form") not in ["Static", "Dynamic", "Off"]
        or body.get("allow_only_admins_to_modify_user_name") not in ["true", "false"]
        or body.get("is_auto_enrich_report") not in ["true", "false"]
        or body.get("async_export_datasheet") not in ["true", "false"]
        # or body.get("databook_sync_strategy") not in [s.value for s in DATABOOK_SYNC_STRATEGY]
    ):
        return False
    return True


def validate_name(name: str, client_id: int) -> bool:
    clients: list = get_clients_by_name(name)

    if len(clients) == 0:
        return True

    if len(clients) == 1:
        if clients[0].client_id == client_id:
            return True

    return False


def validate_subscription_plan(status: str, subscription_plan: str) -> bool:
    is_live = status.capitalize() == "True"
    common_subscription_plans = [
        "MANUAL",
        "ICU",
        "ETL_SLOT_1",
        "ETL_SLOT_2",
        "ETL_SLOT_3",
    ]
    valid_subscription_plans = (
        [
            "BASIC",
            "ENTERPRISE",
            *common_subscription_plans,
        ]
        if is_live
        else ["IMPL_SLOT_1", "IMPL_SLOT_2", *common_subscription_plans]
    )
    return subscription_plan in valid_subscription_plans


def format_body(body: dict) -> dict:
    formatted_body = body

    formatted_body["client_id"] = int(body.get("client_id"))
    formatted_body["hubspot_company_id"] = (
        formatted_body["hubspot_company_id"].strip()
        if isinstance(formatted_body["hubspot_company_id"], str)
        else None
    )
    if not formatted_body["hubspot_company_id"] or formatted_body[
        "hubspot_company_id"
    ].lower() in ["null", "undefined"]:
        formatted_body["hubspot_company_id"] = None

    return formatted_body


class UpdateCustomer(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ADMINUI.value), name="dispatch"
    )
    def post(self, request: Request) -> Response:
        body: dict = request.data

        body["logo_s3_path"] = None
        body["statement_logo_s3_path"] = None
        if request.FILES:
            if validate_files(request.FILES):
                body["logo_s3_path"] = request.FILES.get("file")
            if "statement_logo" in request.FILES:
                body["statement_logo_s3_path"] = request.FILES.get("statement_logo")

        if request.audit and request.audit.get("updated_by"):
            body["updated_by"] = request.audit.get("updated_by")

        if not validate_body(body):
            return Response(
                status=400, data={"errors": ["Invalid (or) missing fields"]}
            )

        body: dict = format_body(body)

        if not validate_name(body.get("name"), body.get("client_id")):
            return Response(
                status=401,
                data={"errors": [{"field": "name", "errorText": "Invalid Name"}]},
            )

        if validate_fiscal_start_month(
            body.get("client_id"), body.get("fiscal_start_month")
        ):
            return Response(
                status=400,
                data={
                    "errors": [
                        "Remove All Employees Quota to change fiscal start month"
                    ]
                },
            )

        if not validate_subscription_plan(
            body.get("status"), body.get("subscription_plan")
        ):
            return Response(
                status=400,
                data={"errors": ["Invalid subscription plan"]},
            )

        was_global_run_disabled = False
        if has_sub_plan_changed_for_client(
            body.get("client_id"), body.get("subscription_plan")
        ):
            was_global_run_disabled = turn_off_global_run_if_no_sync_is_running(
                body.get("client_id")
            )
            if not was_global_run_disabled:
                return Response(
                    status=400,
                    data={
                        "errors": [
                            "Subscription plan cannot be changed now for this client as a sync is currently in-progress"
                        ]
                    },
                )

        try:
            if literal_eval(body.get("show_territory_plan", "false").capitalize()):
                setup_tqm_edit_permission(body.get("client_id"))

            handle_permissions_admin_ui(body)
            existing_client_data = get_client(body.get("client_id"))
            client: int = update_client(body)
            # Map Customer ID to Hubspot Company ID for the client
            update_customer_id_mapping(body.get("hubspot_company_id"), client)
            update_client_email_invite_template(
                client_id=body.get("client_id"),
                new_template_id=body.get("email_invite_template_id"),
            )
            update_approval_cron_task(existing_client_data, body)
            update_user_group_sync_task(existing_client_data, body)
            update_admin_cron_tasks(existing_client_data, body)
            update_hard_delete_cron(
                body.get("hard_delete_sync_frequency"),
                body.get("client_id"),
                body.get("day_of_week"),
                body.get("day_of_month"),
                request.audit,
            )
            # update the tqm accesss for client
            update_tqm_access(body)
            modules = list(body.get("modules", "").split(","))
            if "CPQ" in modules:
                # This is to handle the case where the client is being added to the CPQ module
                # Adds the CPQ roles and permissions for the client
                handle_cpq_roles_and_permissions(client)
            else:
                # This is to handle the case where the client is being removed from the CPQ module
                # Removes the roles of the users who have CPQ roles
                remove_cpq_roles_for_users(client)

        except Client.DoesNotExist:
            return Response(status=401, data={"errors": ["Invalid Client ID"]})
        finally:
            if was_global_run_disabled:
                set_global_run(body.get("client_id"))

        return Response(status=200, data={})
