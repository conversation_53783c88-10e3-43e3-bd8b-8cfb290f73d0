import logging
import re
import traceback
import uuid
from ast import literal_eval

# from django.db import connection
from django.utils import timezone

import commission_engine.accessors.client_accessor as ca
import interstage_project.auth_management_api as ama
import spm.accessors.variable_accessor as va
import spm.models.config_models.employee_models as em
import spm.models.variable_models as vm
from commission_engine.data.bootstrap_data import default_user_groups
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.database.snowflake_query_utils import (
    create_celery_table,
    create_co_snapshot_info_table,
    create_ds_snapshot_info_table,
    create_hris_approval_tracking_table,
    create_isolated_snowflake_database,
    create_isolated_snowflake_roles,
    create_report_snapshot_info_table,
    upload_udf_to_snowflake,
)
from commission_engine.services.periodic_sync_services.hard_delete_sync_status_service import (
    add_new_hard_delete_sync,
)
from commission_engine.snowflake_accessors.report_object_data_accessor import (
    get_create_report_object_data_table_query,
)
from commission_engine.utils.general_data import (
    APPROVALS_AUTO_APPROVE_TASKS_TIME,
    ReportObject,
    Task,
)
from commission_engine.utils.general_utils import log_time_taken
from commission_engine.utils.report_utils import get_report_object_data_table_name
from commission_engine.utils.snowflake_commission_utils import (
    create_commission_related_tables,
)
from everstage_admin_backend.services.customer_id_management_service import (
    update_customer_id_mapping,
)
from everstage_ddd.cpq.rbac.services import handle_cpq_roles_and_permissions
from everstage_ddd.global_search import AdminOperation, Module, initiate_ingestion
from everstage_ddd.tqm import setup_sigma_for_client
from interstage_project.utils import log_me
from spm.accessors.email_template_accessor import EmailTemplateDetailsAccessor
from spm.accessors.employee_accessor_v2 import EmployeeWriteAccessor
from spm.constants.custom_categories import CUSTOM_CATEGORIES
from spm.custom_types.notification_settings_types import notif_task_req_adapter
from spm.models.custom_categories_models import CustomCategory
from spm.models.user_group_models import UserGroup
from spm.rbac.permission_instance import (
    insert_default_user_roles,
    insert_permission_data,
)
from spm.services.approval_workflow_services.approval_cron_services import (
    create_approval_task_entry_for_client,
)
from spm.services.notification_settings_services import add_client_notifications
from spm.services.payout_filters_service import create_payout_filters_on_create_client
from spm.services.user_group_service import create_user_group_sync_task_entry_for_client

logger = logging.getLogger(__name__)


def create_primary_client_variables(client_id):
    try:
        d = timezone.now()
        options = {}
        options["model"] = ["Payroll"]
        variables = va.VariableAccessor().get_variables_for_client_creation(options)
        cva = va.ClientVariableAccessor(client_id)
        client_variables = []
        for var in variables:
            obj = vm.ClientVariable(
                client_id=client_id,
                knowledge_begin_date=d,
                variable_id=var.id,
                display_name=var.name,
                tags=var.tags,
                options=var.options,
            )
            client_variables.append(obj)
        cva.insert_new_data(client_variables)
    except Exception as e:
        log_me("EXCEPTION IN VARIABLES CREATION - {}".format(traceback.print_exc()))
        log_me("Exception: {}".format(e))


def create_default_user_groups(client, new_role_name_id_map):
    knowledge_date = timezone.now()
    user_groups = []
    for default_user_group in default_user_groups:
        if default_user_group["user_group_name"] == "All Payees":
            default_user_group["user_group_config"]["rules"]["rule_list"][0][
                "value"
            ] = new_role_name_id_map["Payee"]
        if default_user_group["user_group_name"] == "All Admins":
            default_user_group["user_group_config"]["rules"]["rule_list"][0][
                "value"
            ] = [new_role_name_id_map["Super Admin"], new_role_name_id_map["Admin"]]
        user_groups.append(
            UserGroup(
                **default_user_group,
                created_by="Everstage",
                created_at=knowledge_date,
                client=client,
                knowledge_begin_date=knowledge_date,
            )
        )
    UserGroup.objects.bulk_create(user_groups)


def create_default_data(client, new_role_name_id_map):
    create_default_user_groups(client, new_role_name_id_map)


def create_default_custom_categories(client_id, created_by):
    category_items = {
        CUSTOM_CATEGORIES.ADJUSTMENTS.value: [
            ("Calculation Issue", 1),
            ("CRM Issue", 2),
            ("Others", 3),
        ],
        CUSTOM_CATEGORIES.QUERIES.value: [
            ("General query", 1),
            ("Commission Calculation", 2),
            ("Approvals", 3),
        ],
    }
    curr_time = timezone.now()
    custom_categories = []
    for module_name, items in category_items.items():
        for item_name, display_order in items:
            custom_category = CustomCategory(
                custom_category_id=uuid.uuid4(),
                custom_category_name=item_name,
                created_by=created_by,
                created_at=curr_time,
                is_default=True,
                is_active=True,
                client_id=client_id,
                knowledge_end_date=None,
                knowledge_begin_date=curr_time,
                is_deleted=False,
                module_name=module_name,
                display_order=display_order,
            )
            custom_categories.append(custom_category)
    CustomCategory.objects.bulk_create(custom_categories)


@log_time_taken()
def create_report_object_data_shards(client_id: int | str):
    """
    Creates the default report object data table shards for the client
    """
    object_ids = ReportObject.get_values_by_table_name("report_object_data")

    logger.info(
        f"Creating report object shards for client {client_id} for the objects {object_ids}"
    )
    full_query = "begin\n"
    for object_id in object_ids:
        table_name = get_report_object_data_table_name(
            client_id=client_id, object_id=object_id
        )
        full_query += (
            get_create_report_object_data_table_query(table_name=table_name) + "\n"
        )
    full_query += "end;\n"
    with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
        snowpark_session.sql(full_query).collect()


@log_time_taken()
def create_system_object_data_shards(client_id: int | str):
    """
    Creates the default system object data table shards for the client
    """
    object_ids = ReportObject.get_values_by_table_name("system_object_data")
    logger.info(
        f"Creating system object shards for client {client_id} for the objects {object_ids}"
    )
    full_query = "begin\n"
    for object_id in object_ids:
        table_name = f"system_object_data_{client_id}_{object_id}"
        full_query += (
            get_create_report_object_data_table_query(table_name=table_name) + "\n"
        )
    full_query += "end;\n"
    with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
        snowpark_session.sql(full_query).collect()


@log_time_taken()
def create_forecast_object_data_shards(client_id: int | str):
    """
    Creates the default forecast object data table shards for the client
    """
    object_ids = ReportObject.get_values_by_table_name("forecast_object_data")
    logger.info(
        f"Creating forecast object shards for client {client_id} for the objects {object_ids}"
    )
    full_query = "begin\n"
    for object_id in object_ids:
        table_name = f"forecast_object_data_{client_id}_{object_id}"
        full_query += (
            get_create_report_object_data_table_query(table_name=table_name) + "\n"
        )
    full_query += "end;\n"
    with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
        snowpark_session.sql(full_query).collect()


@log_time_taken()
def create_inter_object_data_shards(client_id: int | str):
    """
    Creates the default inter object data table shards for the client
    """
    object_ids = ReportObject.get_values_by_table_name("inter_object_data")
    logger.info(
        f"Creating inter object shards for client {client_id} for the objects {object_ids}"
    )
    full_query = "begin\n"
    for object_id in object_ids:
        table_name = f"inter_object_data_{client_id}_{object_id}"
        full_query += (
            get_create_report_object_data_table_query(table_name=table_name) + "\n"
        )
    full_query += "end;\n"
    with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
        snowpark_session.sql(full_query).collect()


@log_time_taken()
def create_inter_forecast_object_data_shards(client_id: int | str):
    """
    Creates the default inter forecast object data table shards for the client
    """
    object_ids = ReportObject.get_values_by_table_name("inter_forecast_object_data")
    logger.info(
        f"Creating inter forecast object shards for client {client_id} for the objects {object_ids}"
    )
    full_query = "begin\n"
    for object_id in object_ids:
        table_name = f"inter_forecast_object_data_{client_id}_{object_id}"
        full_query += (
            get_create_report_object_data_table_query(table_name=table_name) + "\n"
        )
    full_query += "end;\n"
    with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
        snowpark_session.sql(full_query).collect()


def extract_domain_name(root_domain_name: str) -> str:
    """
    Extracts the domain name from the given root domain name.

    Args:
    - root_domain_name (str): The root domain name from which to extract the domain name.

    Returns:
    - str: The extracted domain name if found, otherwise the original root_domain_name.

    Example:
    >>> extract_domain_name('example.com.ci')
    'example'
    >>> extract_domain_name('example-domain.com')
    'example-domain'
    """
    match = re.search(r"^([a-zA-Z0-9-]+)", root_domain_name)
    if match:
        return match.group(1)
    return root_domain_name


def create_oauth_user(client_obj, power_admin_role_id):
    """Creating everstage power admin for new clients"""
    try:
        client_id = client_obj.client_id
        d = timezone.now()
        user_details = {}
        domain_name = extract_domain_name(root_domain_name=client_obj.domain.lower())
        employee_email_id = f"everstage.support@{domain_name}.admin.com"
        user_details["employee_email_id"] = employee_email_id
        user_details["user_role"] = power_admin_role_id
        user_details["first_name"] = "everstage"
        user_details["last_name"] = "admin"
        user_details["email_verified"] = True
        users = ama.create_oauth_user(user_details)
        # Save user details in employee table
        # User role should be json field
        user_role = [str(user_details["user_role"])]
        emp_obj = em.Employee(
            client_id=client_id,
            knowledge_begin_date=d,
            employee_email_id=user_details["employee_email_id"],
            user_role=user_role,
            first_name=user_details["first_name"],
            last_name=user_details["last_name"],
            created_date=d,
            status="Active",
            is_internal_support_user=True,
        )
        EmployeeWriteAccessor(client_id).persist_employee(emp_obj)
        return users
    except Exception as e:
        log_me("EXCEPTION IN USER CREATION - {}".format(traceback.print_exc()))
        log_me("Exception: {}".format(e))


def name_validation(name):
    is_name_exist = False
    res = ca.get_clients_by_name(name)
    if res:
        is_name_exist = True
    return is_name_exist


def domain_validation(domain):
    is_domain_exist = False
    res = ca.get_clients_by_domain(domain)
    if res:
        is_domain_exist = True
    return is_domain_exist


def domain_pattern_validation(params):
    is_domain_valid = False
    d = params["domain"].replace(" ", "")
    d = d.lower()
    params["domain"] = d
    regex = (
        "[a-zA-Z0-9@:%._\\+~#?&//=]"
        + "{2,256}\\.[a-z]"
        + "{2,6}\\b([-a-zA-Z0-9@:%"
        + "._\\+~#?&//=]*)"
    )
    p = re.compile(regex)
    if re.search(p, d):
        is_domain_valid = True
    return is_domain_valid


def create_client_with_properties(
    params,
    create_super_user=True,
    create_datasheet_data=True,  # pylint: disable=unused-argument
):
    try:
        # Create Client Obj
        errors = []
        status = "Success"
        is_name_exist = name_validation(params["name"])
        params["hubspot_company_id"] = (
            params["hubspot_company_id"].strip()
            if isinstance(params["hubspot_company_id"], str)
            else None
        )
        if not params["hubspot_company_id"] or params["hubspot_company_id"].lower() in [
            "null",
            "undefined",
        ]:
            params["hubspot_company_id"] = None
        is_domain_valid = domain_pattern_validation(params)
        if is_name_exist:
            errors.append("Name Already exist")
            status = "Error"
        if not is_domain_valid:
            errors.append("Invalid Domain")
            status = "Error"
        if not errors:
            client = ca.create_client(params)
            # Map Customer ID to Hubspot Company ID for the client
            update_customer_id_mapping(client.crm_company_id, client.client_id)

            # Create permissions for client
            insert_permission_data(client.client_id)
            new_role_name_id_map = insert_default_user_roles(client)
            modules = (
                list(params.get("modules", "").split(","))
                if params.get("modules")
                else []
            )
            if "CPQ" in modules:
                # This is to handle the case where the client is being added to the CPQ module
                # Adds the CPQ roles and permissions for the client
                handle_cpq_roles_and_permissions(client.client_id)

            if not new_role_name_id_map:
                raise Exception("No role permission map present!")

            if create_super_user:
                # Create user in Auth0
                users = create_oauth_user(client, new_role_name_id_map["Power Admin"])
                # Save user details in client meta_info
                client.meta_info["updated_by"] = params["created_by"]
                client.meta_info["super_admin_user"] = users

            client.client_features["enable_view_sync_details_permission"] = True
            client.client_features["enable_edit_split_delete_payroll_permission"] = True
            client.client_features["datasheet_etl_retry"] = True
            client.save()

            # create a database in snowflake based on client_id if isolation is enabled
            if literal_eval(params["isolated_snowflake_database"].capitalize()):
                logger.info(
                    f"BEGIN: Creating isolated snowflake database for client {client.client_id}"
                )
                create_isolated_snowflake_database(client.client_id)
                create_isolated_snowflake_roles(client.client_id)
                upload_udf_to_snowflake(client.client_id)
                create_celery_table(client.client_id)
                create_ds_snapshot_info_table(client.client_id)
                create_report_snapshot_info_table(client.client_id)
                create_co_snapshot_info_table(client.client_id)
                create_hris_approval_tracking_table(client.client_id)
                logger.info(
                    f"END: Creating isolated snowflake database for client {client.client_id}"
                )

            # Create Client Variables
            create_primary_client_variables(client.client_id)

            # Create default data
            create_default_data(client, new_role_name_id_map)
            # Create default custom categories for adjustments and queries
            create_default_custom_categories(client.client_id, params["created_by"])
            # Map client to invite template
            email_invite_template_id = params.get("email_invite_template_id")
            EmailTemplateDetailsAccessor().map_client_to_email_template(
                client.client_id, email_invite_template_id
            )

            # update the tqm accesss for client
            if literal_eval(params["show_territory_plan"].capitalize()):
                setup_sigma_for_client(client.client_id, client.name)

            # Create approval cron for client
            if literal_eval(params["show_approval_feature"].capitalize()):
                # Adding default approval notifications to client notifications table
                default_status = {"email": False, "slack": False, "ms_teams": False}
                approval_tasks = [
                    {
                        "name": "NEW_APPROVAL_REQUEST_NOTIFICATION",
                        "status": default_status,
                        "frequency": None,
                        "is_opted_out": False,
                    },
                    {
                        "name": "EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
                        "status": default_status,
                        "frequency": None,
                        "is_opted_out": False,
                    },
                ]
                add_client_notifications(
                    client.client_id,
                    notif_task_req_adapter.validate_python(approval_tasks),
                    timezone.now(),
                    audit={"updated_by": params.get("updated_by")},
                )
                create_approval_task_entry_for_client(
                    client.client_id,
                    client.time_zone,
                    Task.APPROVALS_AUTO_APPROVE_TASK.value["name"],
                    Task.APPROVALS_AUTO_APPROVE_TASK.value["function"],
                    APPROVALS_AUTO_APPROVE_TASKS_TIME,
                )

            # Create user groups sync cron for client
            create_user_group_sync_task_entry_for_client(
                client.client_id, client.time_zone
            )

            # Create default payout filter columns
            create_payout_filters_on_create_client(client.client_id)

            # create commission related tables
            create_commission_related_tables(client.client_id)

            # Create default shards

            create_report_object_data_shards(client.client_id)
            create_system_object_data_shards(client.client_id)
            create_forecast_object_data_shards(client.client_id)
            create_inter_object_data_shards(client.client_id)
            create_inter_forecast_object_data_shards(client.client_id)

            # Activate global search
            initiate_ingestion(
                client.client_id,
                params["created_by"],
                AdminOperation.ACTIVATION.value,
                Module.values(),
            )

            if literal_eval(
                params.get("insert_meta_data_to_vec_db", "false").capitalize()
            ):
                from everstage_ddd.global_search.meta_data_extractor.ever_objects_meta_data import (
                    upsert_ever_obj_meta_data_in_vector_db,
                )

                logger.info("BEGIN: Upsert ever object metadata in vector db")
                upsert_ever_obj_meta_data_in_vector_db(client.client_id)
                logger.info("END: Upsert ever object metadata in vector db")

            if (
                status.lower() == "success"
                and params["hard_delete_sync_frequency"] != "None"
            ):
                if params["day_of_week"] != "*":
                    params["day_of_week"] = str(int(params["day_of_week"]) - 1)
                cron_expression = {
                    "day_of_week": str(params["day_of_week"]),
                    "day_of_month": str(params["day_of_month"]),
                }
                add_new_hard_delete_sync(
                    client_id=client.client_id,
                    cron_expression=cron_expression,
                    audit=params["audit"],
                )

        return {"status": status, "errors": errors}
    except Exception as e:
        log_me("EXCEPTION IN CLIENT CREATION - {}".format(traceback.print_exc()))
        log_me("Exception: {}".format(e))
