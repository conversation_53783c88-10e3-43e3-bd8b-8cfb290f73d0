import React, { useEffect, useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import { 
  SQLLogHeader,
  SimpleSearchControls,
  RequestListPane,
  RequestDetailPane
} from './components';

import { 
  useSQLLogFiles, 
  useSQLLogAnalysis, 
  useURLState,
  useURLStateRestoration,
  useScrollPosition,
  useFilterAndSort,
  useRequestSelection
} from './hooks';

/**
 * SQL Log Analysis - Main Entry Point
 * 
 * A modular React component for analyzing SQL log files with advanced filtering,
 * sorting, and detailed query analysis capabilities.
 * 
 * =============================================================================
 * ARCHITECTURE OVERVIEW
 * =============================================================================
 * 
 * The system is organized into 3 main files with clear separation of concerns:
 * 
 * 1. ENTRY POINT & LAYOUT COMPONENTS (index.js - this file):
 *    - SQLLogAnalysis: Simple entry point that renders the main container
 *    - SQLLogAnalysisContainer: Main orchestrator managing all hooks and state
 *    - SQLLogAnalysisLayout: Overall page layout and structure
 *    - SQLLogSearchSection: Search controls and error display
 *    - SQLLogMainContent: Two-pane content area wrapper
 *    - SQLLogErrorBoundary: Centralized error handling
 *    - FileErrorDisplay: File loading error component
 *    - SummariesErrorDisplay: Request summaries error component
 * 
 * 2. STATE MANAGEMENT HOOKS (hooks/useStateManagement.js):
 *    - useURLStateRestoration: URL parameter restoration and file selection
 *    - useScrollPosition: Scroll position preservation across state changes
 * 
 * 3. DATA PROCESSING HOOKS (hooks/useDataProcessing.js):
 *    - useFilterAndSort: Search, sort, and filter logic with URL persistence
 *    - useRequestSelection: Request selection and query loading management
 * 
 * =============================================================================
 * COMPONENT HIERARCHY
 * =============================================================================
 * 
 * SQLLogAnalysis (Entry Point)
 * └── SQLLogAnalysisContainer (State Orchestrator)
 *     └── SQLLogAnalysisLayout (Page Structure)
 *         ├── SQLLogHeader (File Selection)
 *         ├── SQLLogSearchSection (Search Controls)
 *         │   └── SimpleSearchControls
 *         └── SQLLogMainContent (Two-Pane Layout)
 *             ├── RequestListPane (Left Pane)
 *             └── RequestDetailPane (Right Pane)
 * 
 * =============================================================================
 * DATA FLOW
 * =============================================================================
 * 
 * 1. Container Hook Orchestration:
 *    - useSQLLogFiles: Fetches available log files
 *    - useSQLLogAnalysis: Handles SQL analysis operations
 *    - useURLState: Manages URL search parameters
 *    - useURLStateRestoration: Restores state from URL on page load
 *    - useScrollPosition: Preserves scroll position across state changes
 *    - useFilterAndSort: Filters and sorts requests based on user input
 *    - useRequestSelection: Manages selected request and loads queries
 * 
 * 2. State Coordination:
 *    - URL parameters restore previous state (file, request, scroll, filters)
 *    - File selection triggers request summaries loading
 *    - Filter changes update URL and maintain scroll position
 *    - Request selection loads detailed queries and updates URL
 * 
 * 3. Error Handling:
 *    - File loading errors are blocking and show retry option
 *    - Request summaries errors are non-blocking and dismissible
 *    - Component-level error boundaries prevent crashes
 * 
 * =============================================================================
 * HOOK RESPONSIBILITIES
 * =============================================================================
 * 
 * STATE MANAGEMENT:
 * • useURLStateRestoration:
 *   - Handles file selection from URL or auto-selection
 *   - Manages restoration flow coordination
 *   - Provides file selection handlers
 *   - Coordinates between URL state and component state
 * 
 * • useScrollPosition:
 *   - Maintains scroll position across state changes
 *   - Handles scroll restoration with multiple timing strategies
 *   - Provides scroll container ref and management functions
 *   - Integrates with URL state for scroll position persistence
 * 
 * DATA PROCESSING:
 * • useFilterAndSort:
 *   - Manages search term, sort order, and filter criteria
 *   - Applies filtering and sorting to request summaries
 *   - Updates URL parameters when filters change
 *   - Preserves scroll position during filter operations
 * 
 * • useRequestSelection:
 *   - Manages selected request state
 *   - Loads detailed queries for selected requests
 *   - Updates URL with selected request ID
 *   - Handles loading states and error recovery
 * 
 * =============================================================================
 * FEATURES
 * =============================================================================
 * 
 * FILE MANAGEMENT:
 * • Auto-selection of current log file or manual file selection
 * • File refresh capability with preserved state
 * • URL-based file selection for deep linking
 * 
 * REQUEST ANALYSIS:
 * • Search requests by endpoint or request ID
 * • Sort by start time, total queries, execution time, or slow queries
 * • Filter by slow queries (all/slow/fast)
 * • Detailed query analysis with execution times
 * 
 * STATE PERSISTENCE:
 * • URL-based state persistence for sharing and bookmarking
 * • Scroll position restoration across navigation
 * • Filter and sort preferences maintained in URL
 * • Request selection preserved in URL
 * 
 * USER EXPERIENCE:
 * • Real-time search with immediate results
 * • Smooth scrolling preservation during state changes
 * • Loading states and error recovery
 * • Responsive two-pane layout
 * 
 * =============================================================================
 * DEVELOPMENT GUIDE
 * =============================================================================
 * 
 * ADDING NEW FEATURES:
 * • New filters: Extend useFilterAndSort hook
 * • New UI sections: Add components to this file
 * • New state logic: Create hooks in appropriate category file
 * • New data operations: Extend useDataProcessing.js
 * 
 * TESTING STRATEGY:
 * • Test hooks in isolation using React Testing Library
 * • Test components with mocked hook returns
 * • Test integration scenarios with real data
 * • Test error states and recovery flows
 * 
 * PERFORMANCE CONSIDERATIONS:
 * • All event handlers are memoized with useCallback
 * • Filtered data is computed with useMemo
 * • Components only re-render when their specific data changes
 * • Queries are loaded only when requests are selected
 * 
 * @component
 * @example
 * <SQLLogAnalysis />
 */

// =============================================================================
// MAIN COMPONENTS
// =============================================================================

/**
 * SQL Log Analysis - Main Entry Point Component
 * 
 * Simple entry point that renders the main container component.
 * This is the component that should be imported and used by parent components.
 * 
 * @component
 * @example
 * <SQLLogAnalysis />
 */
const SQLLogAnalysis = () => {
  return <SQLLogAnalysisContainer />;
};

/**
 * Main container component for SQL log analysis
 * 
 * Responsibilities:
 * - Orchestrates all custom hooks and manages overall state
 * - Provides data and handlers to child components
 * - Handles URL state restoration and coordination
 * - Manages component lifecycle and data flow
 * - Provides error boundary for the entire feature
 * 
 * This component was extracted from a 443-line monolithic component
 * and now serves as a clean, focused container that delegates
 * specific responsibilities to custom hooks and child components.
 * 
 * @component
 * @example
 * <SQLLogAnalysisContainer />
 */
export const SQLLogAnalysisContainer = observer(() => {
  // Core data management hooks
  const { files, loading: filesLoading, error: filesError, refetchFiles } = useSQLLogFiles();
  const { 
    requestSummaries, 
    loading: summariesLoading, 
    error: summariesError, 
    getRequestSummaries, 
    getQueriesForRequest 
  } = useSQLLogAnalysis();
  
  // URL state management
  const urlState = useURLState();
  
  // Scroll position management
  const scrollDependencies = [
    requestSummaries?.summaries, // Re-render when summaries change
  ];
  const scrollPosition = useScrollPosition(scrollDependencies);
  
  // URL state restoration and file selection
  const urlRestoration = useURLStateRestoration(
    files, 
    requestSummaries, 
    urlState, 
    getRequestSummaries,
    scrollPosition.setScrollFromURL
  );
  
  // Filter and sort management
  const filterAndSort = useFilterAndSort(
    requestSummaries, 
    urlState, 
    scrollPosition.saveScrollPosition
  );
  
  // Request selection management
  const requestSelection = useRequestSelection(
    urlRestoration.selectedFile,
    getQueriesForRequest,
    urlState,
    scrollPosition.saveScrollPosition
  );

  // Initialize filter state from URL parameters when file changes
  // Note: Depend on selectedFile to re-initialize filters when file changes
  useEffect(() => {
    if (urlRestoration.selectedFile) {
      const urlParams = urlRestoration.getURLParams();
      filterAndSort.initializeFromURL(urlParams);
    }
  }, [urlRestoration.selectedFile]); // Re-run when file changes

  // Restore selected request from URL after summaries are loaded
  // Note: Only depend on the actual data, not the hook objects to prevent infinite loops
  useEffect(() => {
    if (urlRestoration.isRestoring()) {
      const requestFromURL = urlRestoration.getRequestFromURL();
      if (requestFromURL) {
        requestSelection.setSelectedRequest(requestFromURL);
        urlRestoration.completeRestoration();
      }
    }
  }, [requestSummaries?.summaries]);

  // Clear selected request when file changes (unless restoring from URL)
  // Note: Using specific property to avoid hook object dependency
  useEffect(() => {
    if (!urlRestoration.isRestoring()) {
      requestSelection.clearSelectedRequest();
    }
  }, [urlRestoration.selectedFile]);

  // Note: Scroll restoration is handled by the useScrollPosition hook
  // with dependencies defined at hook initialization

  // Memoize selected request ID to prevent unnecessary recalculations
  const selectedRequestId = useMemo(() => {
    return requestSelection.selectedRequest?.request_id || null;
  }, [requestSelection.selectedRequest?.request_id]);

  // Handle refresh action
  const handleRefresh = useCallback(() => {
    urlRestoration.handleRefresh(refetchFiles);
  }, [urlRestoration.handleRefresh, refetchFiles]);

  // Prepare props for child components
  const headerProps = useMemo(() => ({
    files,
    selectedFile: urlRestoration.selectedFile,
    onFileSelect: urlRestoration.handleFileSelect,
    onRefresh: handleRefresh,
    filesLoading,
    summariesLoading
  }), [files, urlRestoration.selectedFile, urlRestoration.handleFileSelect, handleRefresh, filesLoading, summariesLoading]);

  const searchProps = useMemo(() => ({
    searchTerm: filterAndSort.searchTerm,
    onSearchChange: filterAndSort.handleSearchChange,
    onSearchClear: filterAndSort.handleSearchClear,
    sortBy: filterAndSort.sortBy,
    onSortChange: filterAndSort.handleSortChange,
    filterSlow: filterAndSort.filterSlow,
    onFilterChange: filterAndSort.handleFilterChange,
    totalCount: requestSummaries?.summaries?.length,
    filteredCount: filterAndSort.filteredAndSortedRequests?.length || 0
  }), [
    filterAndSort.searchTerm, 
    filterAndSort.handleSearchChange,
    filterAndSort.handleSearchClear,
    filterAndSort.sortBy, 
    filterAndSort.handleSortChange,
    filterAndSort.filterSlow, 
    filterAndSort.handleFilterChange,
    requestSummaries?.summaries?.length, 
    filterAndSort.filteredAndSortedRequests?.length
  ]);

  const listProps = useMemo(() => ({
    ref: scrollPosition.scrollContainerRef,
    requests: filterAndSort.filteredAndSortedRequests,
    selectedRequestId,
    onRequestSelect: requestSelection.handleRequestSelect,
    loading: summariesLoading,
    searchTerm: filterAndSort.searchTerm,
    filterSlow: filterAndSort.filterSlow
  }), [
    scrollPosition.scrollContainerRef,
    filterAndSort.filteredAndSortedRequests, 
    selectedRequestId, 
    requestSelection.handleRequestSelect,
    summariesLoading, 
    filterAndSort.searchTerm, 
    filterAndSort.filterSlow
  ]);

  const detailProps = useMemo(() => ({
    selectedRequest: requestSelection.selectedRequest,
    requestQueries: requestSelection.requestQueries,
    queriesLoading: requestSelection.queriesLoading
  }), [
    requestSelection.selectedRequest, 
    requestSelection.requestQueries, 
    requestSelection.queriesLoading
  ]);

  // Handle blocking errors (files error prevents the entire component from rendering)
  if (filesError) {
    return (
      <SQLLogErrorBoundary
        filesError={filesError}
        summariesError={summariesError}
        onRetry={refetchFiles}
      />
    );
  }

  // Render main layout
  return (
    <SQLLogAnalysisLayout
      headerProps={headerProps}
      searchProps={searchProps}
      summariesError={summariesError}
      listProps={listProps}
      detailProps={detailProps}
    />
  );
});

/**
 * Layout component for SQL log analysis
 * 
 * Responsibilities:
 * - Provides the overall page structure and layout
 * - Orchestrates header, search, and main content sections
 * - Manages flex layout and full-screen dimensions
 * - Ensures consistent styling and spacing
 * 
 * @param {Object} props - Component props
 * @param {Object} props.headerProps - Properties for SQLLogHeader
 * @param {Object} props.searchProps - Properties for search controls
 * @param {string} props.summariesError - Error message for summaries loading
 * @param {Object} props.listProps - Properties for request list pane
 * @param {Object} props.detailProps - Properties for request detail pane
 * @returns {React.ReactElement} Layout component
 */
export const SQLLogAnalysisLayout = ({ 
  headerProps, 
  searchProps, 
  summariesError, 
  listProps, 
  detailProps 
}) => {
  return (
    <div className="sql-log-analysis h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <SQLLogHeader {...headerProps} />

      {/* Search Section */}
      <SQLLogSearchSection 
        searchProps={searchProps}
        summariesError={summariesError}
      />

      {/* Main Content - Two Pane Layout */}
      <SQLLogMainContent 
        listProps={listProps}
        detailProps={detailProps}
      />
    </div>
  );
};

// =============================================================================
// UTILITY COMPONENTS
// =============================================================================

/**
 * Search section component for SQL log analysis
 * 
 * Responsibilities:
 * - Renders search controls in a consistent layout
 * - Displays summaries errors when they occur
 * - Provides proper spacing and styling
 * - Encapsulates search-related UI
 * 
 * @param {Object} props - Component props
 * @param {Object} props.searchProps - Properties for SimpleSearchControls
 * @param {string} props.summariesError - Error message for summaries loading
 * @returns {React.ReactElement} Search section component
 */
export const SQLLogSearchSection = ({ searchProps, summariesError }) => {
  return (
    <>
      {/* Search Controls */}
      <div className="bg-white border-b px-6 py-4">
        <SimpleSearchControls {...searchProps} />
      </div>

      {/* Error Display */}
      {summariesError && (
        <SummariesErrorDisplay error={summariesError} />
      )}
    </>
  );
};

/**
 * Main content component for SQL log analysis
 * 
 * Responsibilities:
 * - Provides the two-pane layout structure
 * - Handles flex layout and overflow management
 * - Coordinates between request list and detail panes
 * - Maintains consistent styling and spacing
 * 
 * @param {Object} props - Component props
 * @param {Object} props.listProps - Properties for RequestListPane
 * @param {Object} props.detailProps - Properties for RequestDetailPane
 * @returns {React.ReactElement} Main content component
 */
export const SQLLogMainContent = ({ listProps, detailProps }) => {
  return (
    <div className="flex-1 flex overflow-hidden">
      {/* Left Pane - Request List */}
      <RequestListPane {...listProps} />

      {/* Right Pane - Query Details */}
      <RequestDetailPane {...detailProps} />
    </div>
  );
};

/**
 * Centralized error boundary component for SQL log analysis
 * 
 * Responsibilities:
 * - Displays file loading errors with retry functionality
 * - Displays request summaries errors
 * - Provides consistent error UI across the application
 * - Handles different error types appropriately
 * 
 * @param {Object} props - Component props
 * @param {string} props.filesError - File loading error message
 * @param {string} props.summariesError - Summaries loading error message
 * @param {Function} props.onRetry - Retry function for file loading
 * @returns {React.ReactElement|null} Error display or null
 */
export const SQLLogErrorBoundary = ({ filesError, summariesError, onRetry }) => {
  // Handle file loading errors (blocking)
  if (filesError) {
    return <FileErrorDisplay error={filesError} onRetry={onRetry} />;
  }
  
  // Handle summaries loading errors (non-blocking)
  if (summariesError) {
    return <SummariesErrorDisplay error={summariesError} />;
  }
  
  // No errors to display
  return null;
};

/**
 * Error display component for file loading errors
 * 
 * @param {Object} props - Component props
 * @param {string} props.error - Error message
 * @param {Function} props.onRetry - Retry function
 * @returns {React.ReactElement} File error display
 */
export const FileErrorDisplay = ({ error, onRetry }) => (
  <Alert
    message="Error Loading Log Files"
    description={error}
    type="error"
    showIcon
    action={
      <Button onClick={onRetry} type="primary">
        Retry
      </Button>
    }
  />
);

/**
 * Error display component for summaries loading errors
 * 
 * @param {Object} props - Component props
 * @param {string} props.error - Error message
 * @returns {React.ReactElement} Summaries error display
 */
export const SummariesErrorDisplay = ({ error }) => (
  <div className="px-6 py-2">
    <Alert
      message="Error Loading Request Summaries"
      description={error}
      type="error"
      showIcon
      closable
    />
  </div>
);

export default SQLLogAnalysis; 