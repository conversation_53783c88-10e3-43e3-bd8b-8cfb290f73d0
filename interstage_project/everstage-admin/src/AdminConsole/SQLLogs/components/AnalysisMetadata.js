import React from "react";
import { Card, Statistic, Tag, Row, Col, Progress } from "antd";
import { 
  ClockIcon, 
  DatabaseIcon, 
  EyeIcon,
  LightningBoltIcon,
  ExclamationIcon 
} from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

/**
 * AnalysisMetadata component for displaying analysis results summary
 * Shows key metrics, performance insights, and processing information
 */
const AnalysisMetadata = ({ data, activeView, selectedFile }) => {
  if (!data) return null;

  const renderQueryAnalysisMetadata = () => {
    const { analysis_metadata, total_entries, filtered_entries, slow_queries } = data;
    const slowQueriesCount = slow_queries?.length || 0;
    const slowQueriesPercentage = filtered_entries > 0 
      ? (slowQueriesCount / filtered_entries * 100).toFixed(1)
      : 0;

    return (
      <Row gutter={16}>
        <Col span={4}>
          <Statistic
            title="Total Entries"
            value={total_entries.toLocaleString()}
            prefix={<EyeIcon className="w-4 h-4" />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Filtered Results"
            value={filtered_entries.toLocaleString()}
            prefix={<DatabaseIcon className="w-4 h-4" />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Slow Queries"
            value={slowQueriesCount}
            suffix={`(${slowQueriesPercentage}%)`}
            prefix={<ExclamationIcon className="w-4 h-4" />}
            valueStyle={{ 
              color: slowQueriesCount > 0 ? '#cf1322' : '#3f8600' 
            }}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Processing Time"
            value={analysis_metadata?.processing_time_ms ? 
              SQLLogService.formatExecutionTime(analysis_metadata.processing_time_ms) : 
              'N/A'}
            prefix={<LightningBoltIcon className="w-4 h-4" />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Unique Tables"
            value={analysis_metadata?.unique_tables_count || 0}
            prefix={<DatabaseIcon className="w-4 h-4" />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Unique Endpoints"
            value={analysis_metadata?.unique_endpoints_count || 0}
          />
        </Col>
      </Row>
    );
  };

  const renderRequestSummaryMetadata = () => {
    const { analysis_metadata, total_requests, summaries } = data;
    const totalQueries = summaries?.reduce((sum, s) => sum + s.total_queries, 0) || 0;
    const totalSlowQueries = summaries?.reduce((sum, s) => sum + s.slow_queries_count, 0) || 0;
    const avgQueriesPerRequest = summaries?.length > 0 ? (totalQueries / summaries.length).toFixed(1) : 0;

    return (
      <Row gutter={16}>
        <Col span={4}>
          <Statistic
            title="Total Requests"
            value={total_requests.toLocaleString()}
            prefix={<EyeIcon className="w-4 h-4" />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Filtered Requests"
            value={summaries?.length.toLocaleString() || 0}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Total Queries"
            value={totalQueries.toLocaleString()}
            prefix={<DatabaseIcon className="w-4 h-4" />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Avg Queries/Request"
            value={avgQueriesPerRequest}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Slow Queries"
            value={totalSlowQueries}
            valueStyle={{ 
              color: totalSlowQueries > 0 ? '#cf1322' : '#3f8600' 
            }}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="Processing Time"
            value={analysis_metadata?.processing_time_ms ? 
              SQLLogService.formatExecutionTime(analysis_metadata.processing_time_ms) : 
              'N/A'}
            prefix={<LightningBoltIcon className="w-4 h-4" />}
          />
        </Col>
      </Row>
    );
  };

  const renderPerformanceInsights = () => {
    if (activeView !== 'queries' || !data.entries) return null;

    const entries = data.entries;
    const executionTimes = entries.map(e => e.execution_time_ms);
    const avgTime = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
    const maxTime = Math.max(...executionTimes);
    const minTime = Math.min(...executionTimes);
    
    const slowQueries = entries.filter(e => e.execution_time_ms > 1000).length;
    const verySlowQueries = entries.filter(e => e.execution_time_ms > 5000).length;
    const fastQueries = entries.filter(e => e.execution_time_ms < 100).length;

    const slowPercentage = (slowQueries / entries.length) * 100;
    const fastPercentage = (fastQueries / entries.length) * 100;

    return (
      <Card title="Performance Insights" size="small" className="mt-4">
        <Row gutter={16}>
          <Col span={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{fastPercentage.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Fast Queries (&lt;100ms)</div>
              <Progress 
                percent={fastPercentage} 
                size="small" 
                strokeColor="#52c41a" 
                showInfo={false}
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{slowPercentage.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Slow Queries (&gt;1s)</div>
              <Progress 
                percent={slowPercentage} 
                size="small" 
                strokeColor="#ff4d4f" 
                showInfo={false}
              />
            </div>
          </Col>
          <Col span={4}>
            <Statistic
              title="Average Time"
              value={SQLLogService.formatExecutionTime(avgTime)}
              valueStyle={{ fontSize: '16px' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="Max Time"
              value={SQLLogService.formatExecutionTime(maxTime)}
              valueStyle={{ 
                fontSize: '16px',
                color: SQLLogService.getPerformanceColor(maxTime).includes('red') ? '#cf1322' : undefined
              }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="Min Time"
              value={SQLLogService.formatExecutionTime(minTime)}
              valueStyle={{ fontSize: '16px' }}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  const getViewTitle = () => {
    switch (activeView) {
      case 'queries': return 'SQL Queries Analysis';
      case 'requests': return 'Request Summaries Analysis';
      case 'tables': return 'Table Statistics Analysis';
      default: return 'Analysis Results';
    }
  };

  return (
    <div className="analysis-metadata mb-4">
      <Card title={
        <div className="flex items-center justify-between">
          <span>{getViewTitle()}</span>
          <div className="flex items-center gap-2">
            <Tag color="blue">{selectedFile}</Tag>
            <Tag color="green">
              {new Date().toLocaleTimeString()}
            </Tag>
          </div>
        </div>
      }>
        {activeView === 'queries' && renderQueryAnalysisMetadata()}
        {activeView === 'requests' && renderRequestSummaryMetadata()}
        
        {renderPerformanceInsights()}
      </Card>
    </div>
  );
};

export default AnalysisMetadata; 