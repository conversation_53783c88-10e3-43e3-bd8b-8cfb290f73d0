import React, { useState, use<PERSON>emo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>ip, Typography } from "antd";
import { 
  EyeIcon,
  ClipboardIcon,
  ChevronDownIcon,
  ChevronUpIcon 
} from "@heroicons/react/outline";

const { Text } = Typography;

/**
 * SQLQueryDisplay Component
 * 
 * A reusable component for displaying SQL queries with improved readability.
 * Features:
 * - Basic SQL syntax highlighting
 * - Expandable/collapsible view for long queries
 * - Copy to clipboard functionality
 * - Terminal-style dark theme
 * - Configurable max height and preview lines
 * 
 * @param {Object} props - Component props
 * @param {string} props.query - The SQL query string to display
 * @param {string} props.querySimplified - Optional simplified version of the query
 * @param {boolean} props.isPreview - Whether this is a preview (default: true)
 * @param {number} props.maxPreviewLines - Max lines to show in preview mode (default: 5)
 * @param {Function} props.onFullView - Callback when full view button is clicked
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showCopyButton - Whether to show copy button (default: true)
 * @param {boolean} props.showExpandButton - Whether to show expand button (default: true)
 */
const SQLQueryDisplay = ({
  query,
  querySimplified,
  isPreview = true,
  maxPreviewLines = 25,
  onFullView,
  className = "",
  showCopyButton = true,
  showExpandButton = true
}) => {
  const [isExpanded, setIsExpanded] = useState(!isPreview);
  const [copySuccess, setCopySuccess] = useState(false);

  // Use simplified query if available, otherwise use the main query
  const displayQuery = querySimplified || query;

  // Basic SQL formatting function
  const formatSQL = (sql) => {
    if (!sql) return "";
    
    let formatted = sql;
    
    // Add proper line breaks for better readability
    formatted = formatted
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/;\s*/g, ';\n') // Line break after semicolons
      .replace(/\s+(FROM|WHERE|JOIN|INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|ORDER BY|GROUP BY|HAVING|UNION|UNION ALL)\s+/gi, '\n$1 ')
      .replace(/\s+(AND|OR)\s+/gi, '\n  $1 ') // Indent AND/OR
      .replace(/,\s*/g, ',\n  ') // Line break after commas with indent
      .trim();

    return formatted;
  };

  // Function to create highlighted SQL JSX
  const createHighlightedSQL = (sql) => {
    if (!sql) return null;

    // SQL keywords to highlight
    const keywords = [
      'SELECT', 'FROM', 'WHERE', 'JOIN', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN',
      'ORDER BY', 'GROUP BY', 'HAVING', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP',
      'UNION', 'UNION ALL', 'WITH', 'AS', 'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN',
      'LIKE', 'IS NULL', 'IS NOT NULL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'DISTINCT',
      'COUNT', 'SUM', 'AVG', 'MAX', 'MIN', 'LIMIT', 'OFFSET', 'NULL', 'TRUE', 'FALSE'
    ];

    // Sort keywords by length (longest first) to avoid partial matches
    const sortedKeywords = [...keywords].sort((a, b) => b.length - a.length);
    
    // Split into lines first
    const lines = sql.split('\n');
    
    return lines.map((line, lineIndex) => {
      const parts = [];
      let remaining = line;
      let partIndex = 0;

      // Process each part of the line
      while (remaining.length > 0) {
        let matched = false;

        // Check for keywords (case insensitive)
        for (const keyword of sortedKeywords) {
          const regex = new RegExp(`^(\\b${keyword.replace(/\s+/g, '\\s+')}\\b)`, 'i');
          const match = remaining.match(regex);
          if (match) {
            parts.push(
              <span key={`${lineIndex}-${partIndex++}`} className="text-blue-300 font-bold">
                {match[1]}
              </span>
            );
            remaining = remaining.slice(match[1].length);
            matched = true;
            break;
          }
        }

        // Check for strings
        if (!matched) {
          const stringMatch = remaining.match(/^('([^']*)'|"([^"]*)")/);
          if (stringMatch) {
            parts.push(
              <span key={`${lineIndex}-${partIndex++}`} className="text-yellow-300">
                {stringMatch[1]}
              </span>
            );
            remaining = remaining.slice(stringMatch[1].length);
            matched = true;
          }
        }

        // Check for numbers
        if (!matched) {
          const numberMatch = remaining.match(/^\b\d+(\.\d+)?\b/);
          if (numberMatch) {
            parts.push(
              <span key={`${lineIndex}-${partIndex++}`} className="text-purple-300">
                {numberMatch[0]}
              </span>
            );
            remaining = remaining.slice(numberMatch[0].length);
            matched = true;
          }
        }

        // Check for comments
        if (!matched) {
          const commentMatch = remaining.match(/^(--.*$|\/\*[\s\S]*?\*\/)/);
          if (commentMatch) {
            parts.push(
              <span key={`${lineIndex}-${partIndex++}`} className="text-gray-500 italic">
                {commentMatch[1]}
              </span>
            );
            remaining = remaining.slice(commentMatch[1].length);
            matched = true;
          }
        }

        // If no match, take one character
        if (!matched) {
          parts.push(remaining.charAt(0));
          remaining = remaining.slice(1);
        }
      }

      return (
        <div key={lineIndex}>
          {parts}
        </div>
      );
    });
  };

  // Memoized formatted query
  const formattedQuery = useMemo(() => formatSQL(displayQuery), [displayQuery]);

  // Calculate if query needs truncation in preview mode
  const queryLines = formattedQuery.split('\n');
  const needsTruncation = isPreview && queryLines.length > maxPreviewLines;
  const truncatedQuery = needsTruncation && !isExpanded 
    ? queryLines.slice(0, maxPreviewLines).join('\n')
    : formattedQuery;

  // Memoized highlighted query JSX
  const highlightedQuery = useMemo(() => createHighlightedSQL(truncatedQuery), [truncatedQuery]);

  // Handle copy to clipboard
  const handleCopy = async (e) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(query || displayQuery);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy query:', err);
    }
  };

  // Handle expand/collapse
  const handleToggleExpand = (e) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Handle full view button click
  const handleFullView = (e) => {
    e.stopPropagation();
    if (onFullView) {
      onFullView();
    }
  };

  return (
    <div className={`sql-query-display ${className}`}>
      {/* Controls Bar */}
      <div className="flex items-center justify-between mb-2 px-3 py-2 bg-gray-100 rounded-t border-b">
        <div className="flex items-center gap-2">
          <Text className="text-xs font-medium text-gray-600">SQL Query</Text>
          {needsTruncation && (
            <Text className="text-xs text-gray-500">
              ({queryLines.length} lines)
            </Text>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {showCopyButton && (
            <Tooltip title={copySuccess ? "Copied!" : "Copy to clipboard"}>
              <Button
                type="text"
                size="small"
                icon={<ClipboardIcon className="w-3 h-3" />}
                onClick={handleCopy}
                className="text-gray-500 hover:text-gray-700"
              />
            </Tooltip>
          )}
          
          {needsTruncation && showExpandButton && (
            <Tooltip title={isExpanded ? "Collapse" : "Expand"}>
              <Button
                type="text"
                size="small"
                icon={isExpanded ? 
                  <ChevronUpIcon className="w-3 h-3" /> : 
                  <ChevronDownIcon className="w-3 h-3" />
                }
                onClick={handleToggleExpand}
                className="text-gray-500 hover:text-gray-700"
              />
            </Tooltip>
          )}

          {onFullView && (
            <Tooltip title="View in modal">
              <Button
                type="text"
                size="small"
                icon={<EyeIcon className="w-3 h-3" />}
                onClick={handleFullView}
                className="text-gray-500 hover:text-gray-700"
              />
            </Tooltip>
          )}
        </div>
      </div>

      {/* Query Display */}
      <div 
        className="sql-query-content bg-gray-900 text-green-400 rounded-b overflow-auto font-mono text-sm leading-relaxed cursor-pointer hover:bg-gray-800 transition-colors"
        style={{ 
          maxHeight: isPreview && !isExpanded ? `${Math.max(maxPreviewLines * 2.2, 10 * 2.2)}em` : '600px',
          minHeight: '22em' // Ensure at least 10 lines are visible (10 * 2.2em)
        }}
        onClick={onFullView}
      >
        <div className="p-4">
          {highlightedQuery}
          {needsTruncation && !isExpanded && (
            <div className="text-gray-500 mt-2">
              <span>... </span>
              <button 
                onClick={handleToggleExpand}
                className="text-blue-400 hover:text-blue-300 underline bg-transparent border-none cursor-pointer"
              >
                Show {queryLines.length - maxPreviewLines} more lines
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Query Stats (optional) */}
      {isExpanded && (
        <div className="px-3 py-1 bg-gray-50 rounded-b text-xs text-gray-500 flex justify-between border-t">
          <span>{queryLines.length} lines</span>
          <span>{(query || displayQuery).length} characters</span>
        </div>
      )}
    </div>
  );
};

export default SQLQueryDisplay; 