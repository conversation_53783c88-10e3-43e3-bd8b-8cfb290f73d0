import React from "react";
import { Table, Tag, Tooltip, Card, Statistic } from "antd";
import { ClockIcon, DatabaseIcon, EyeIcon } from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

/**
 * RequestSummariesView component for displaying request-level analysis
 * Shows aggregated statistics per request with performance insights
 */
const RequestSummariesView = ({ summaries, loading, pagination }) => {
  const columns = [
    {
      title: "Request ID",
      dataIndex: "request_id",
      key: "request_id",
      width: 120,
      ellipsis: {
        showTitle: false,
      },
      render: (requestId) => (
        <Tooltip title={requestId}>
          <span className="font-mono text-xs">{requestId.slice(-8)}</span>
        </Tooltip>
      ),
    },
    {
      title: "Endpoint",
      dataIndex: "endpoint",
      key: "endpoint",
      ellipsis: true,
      sorter: (a, b) => a.endpoint.localeCompare(b.endpoint),
    },
    {
      title: "Total Queries",
      dataIndex: "total_queries",
      key: "total_queries",
      width: 120,
      align: "center",
      sorter: (a, b) => a.total_queries - b.total_queries,
      render: (count) => (
        <span className="font-mono font-semibold">{count}</span>
      ),
    },
    {
      title: "Total Time",
      dataIndex: "total_execution_time_ms",
      key: "total_execution_time_ms",
      width: 120,
      sorter: (a, b) => a.total_execution_time_ms - b.total_execution_time_ms,
      render: (timeMs) => {
        const formatted = SQLLogService.formatExecutionTime(timeMs);
        const colorClass = SQLLogService.getPerformanceColor(timeMs);
        return (
          <span className={`font-mono ${colorClass}`}>{formatted}</span>
        );
      },
    },
    {
      title: "Avg Time",
      dataIndex: "avg_execution_time_ms",
      key: "avg_execution_time_ms",
      width: 120,
      sorter: (a, b) => a.avg_execution_time_ms - b.avg_execution_time_ms,
      render: (timeMs) => {
        const formatted = SQLLogService.formatExecutionTime(timeMs);
        const colorClass = SQLLogService.getPerformanceColor(timeMs);
        return (
          <span className={`font-mono ${colorClass}`}>{formatted}</span>
        );
      },
    },
    {
      title: "Slow Queries",
      dataIndex: "slow_queries_count",
      key: "slow_queries_count",
      width: 110,
      align: "center",
      sorter: (a, b) => a.slow_queries_count - b.slow_queries_count,
      render: (count, record) => {
        const percentage = record.total_queries > 0 
          ? (count / record.total_queries * 100).toFixed(1)
          : 0;
        
        return (
          <div className="text-center">
            <div className={`font-semibold ${count > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {count}
            </div>
            <div className="text-xs text-gray-500">
              {percentage}%
            </div>
          </div>
        );
      },
    },
    {
      title: "Tables Accessed",
      dataIndex: "unique_tables",
      key: "unique_tables",
      width: 200,
      render: (tables) => (
        <div className="flex flex-wrap gap-1">
          {tables.slice(0, 3).map((table, idx) => (
            <Tag key={idx} size="small" icon={<DatabaseIcon className="w-3 h-3" />}>
              {table}
            </Tag>
          ))}
          {tables.length > 3 && (
            <Tooltip title={tables.slice(3).join(", ")}>
              <Tag size="small" color="default">
                +{tables.length - 3} more
              </Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: "Time Range",
      key: "time_range",
      width: 180,
      render: (_, record) => (
        <div className="text-xs">
          <div>Start: {SQLLogService.formatTimestamp(record.start_time)}</div>
          <div>End: {SQLLogService.formatTimestamp(record.end_time)}</div>
        </div>
      ),
    },
  ];

  const getRowClassName = (record) => {
    if (record.slow_queries_count > 0) {
      const slowPercentage = record.slow_queries_count / record.total_queries;
      if (slowPercentage > 0.5) return "bg-red-50";
      if (slowPercentage > 0.2) return "bg-yellow-50";
    }
    return "";
  };

  // Calculate summary statistics
  const totalQueries = summaries.reduce((sum, s) => sum + s.total_queries, 0);
  const totalTime = summaries.reduce((sum, s) => sum + s.total_execution_time_ms, 0);
  const avgTime = summaries.length > 0 ? totalTime / summaries.length : 0;
  const totalSlowQueries = summaries.reduce((sum, s) => sum + s.slow_queries_count, 0);

  return (
    <div className="request-summaries-view">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-4">Request Summaries</h3>
        
        <div className="grid grid-cols-4 gap-4 mb-4">
          <Card>
            <Statistic
              title="Total Requests"
              value={summaries.length}
              prefix={<EyeIcon className="w-4 h-4" />}
            />
          </Card>
          <Card>
            <Statistic
              title="Total Queries"
              value={totalQueries}
              prefix={<DatabaseIcon className="w-4 h-4" />}
            />
          </Card>
          <Card>
            <Statistic
              title="Avg Time/Request"
              value={SQLLogService.formatExecutionTime(avgTime)}
              prefix={<ClockIcon className="w-4 h-4" />}
            />
          </Card>
          <Card>
            <Statistic
              title="Slow Queries"
              value={totalSlowQueries}
              valueStyle={{ color: totalSlowQueries > 0 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={summaries}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} requests`,
        }}
        rowKey="request_id"
        rowClassName={getRowClassName}
        scroll={{ x: 1000 }}
        size="small"
        bordered
      />
    </div>
  );
};

export default RequestSummariesView; 