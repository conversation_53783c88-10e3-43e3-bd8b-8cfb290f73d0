/**
 * SQL Logs Components Index
 * 
 * Central export file for all SQL logs components.
 * This file provides a clean API for importing components and maintains
 * a clear overview of the available component library.
 * 
 * Usage:
 * import { SQLLogHeader, SimpleSearchControls } from './components';
 * 
 * Or import specific components:
 * import SQLLogHeader from './components/SQLLogHeader';
 */

// Layout Components
export { default as SQLLogHeader } from './SQLLogHeader';
export { default as SimpleSearchControls } from './SimpleSearchControls';
export { default as RequestListPane } from './RequestListPane';
export { default as RequestDetailPane } from './RequestDetailPane';

// Core Components
export { default as FileSelector } from './FileSelector';
export { default as RequestCard } from './RequestCard';
export { default as RequestQueriesView } from './RequestQueriesView';

// Advanced Components
export { default as FilterPanel } from './FilterPanel';
export { default as AnalysisMetadata } from './AnalysisMetadata';
export { default as TableStatisticsView } from './TableStatisticsView';
export { default as RequestSummariesView } from './RequestSummariesView';
export { default as LogEntriesView } from './LogEntriesView';
export { default as ExportControls } from './ExportControls';

/**
 * Component Categories for Reference:
 * 
 * LAYOUT COMPONENTS:
 * - SQLLogHeader: Page header with title and file selection
 * - SimpleSearchControls: Basic search, sort, and filter controls
 * - RequestListPane: Left pane with scrollable request list
 * - RequestDetailPane: Right pane with query details
 * 
 * CONSOLIDATED LAYOUT COMPONENTS (from SQLLogLayout.js):
 * - SQLLogAnalysisContainer: Main container orchestrating all hooks and state
 * - SQLLogAnalysisLayout: Layout component managing overall structure
 * - SQLLogSearchSection: Search controls and error display section
 * - SQLLogMainContent: Two-pane content area wrapper
 * - SQLLogErrorBoundary: Centralized error handling component
 * - FileErrorDisplay: File loading error display
 * - SummariesErrorDisplay: Request summaries error display
 * 
 * CORE COMPONENTS:
 * - FileSelector: Dropdown for selecting log files
 * - RequestCard: Individual request display card
 * - RequestQueriesView: Detailed query view for requests
 * 
 * ADVANCED COMPONENTS:
 * - FilterPanel: Advanced filtering interface
 * - AnalysisMetadata: Analysis statistics and metadata
 * - TableStatisticsView: Database table usage statistics
 * - RequestSummariesView: Request summary overview
 * - LogEntriesView: Raw log entries display
 * - ExportControls: Data export functionality
 * 
 * FILE CONSOLIDATION ARCHITECTURE:
 * 
 * The original 443-line monolithic component and subsequent 9 separate files
 * have been consolidated into optimized file structure:
 * 
 * 1. ENTRY POINT:
 *    - index.js: Simple entry point (10 lines)
 * 
 * 2. CONSOLIDATED LAYOUT:
 *    - SQLLogLayout.js: All layout components in one file (~380 lines)
 * 
 * 3. CONSOLIDATED HOOKS:
 *    - useStateManagement.js: URL restoration + scroll position (~220 lines)
 *    - useDataProcessing.js: Filtering/sorting + request selection (~220 lines)
 * 
 * 4. EXISTING COMPONENTS:
 *    - Preserved original components (header, search controls, panes)
 * 
 * Consolidation Benefits:
 * - Reduced from 9 new files to 3 consolidated files
 * - Related functionality grouped together
 * - Easier navigation and maintenance
 * - Maintains modular architecture without file sprawl
 * - Clear separation between layout, state, and data concerns
 * 
 * Refactoring Benefits:
 * - Reduced main component from 443 lines to ~10 lines
 * - Better separation of concerns
 * - Improved testability and maintainability
 * - Enhanced reusability
 * - Cleaner state management
 */ 