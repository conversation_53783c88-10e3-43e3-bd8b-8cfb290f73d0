import React, { useState, useMemo } from "react";
import { Table, Tag, Tooltip, Modal, Button, Space, Pagination } from "antd";
import { 
  EyeIcon, 
  ClockIcon, 
  DatabaseIcon,
  ExclamationIcon 
} from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

/**
 * LogEntriesView component for displaying individual SQL log entries
 * Features sortable columns, query detail modal, and performance indicators
 */
const LogEntriesView = ({ entries, loading, pagination, slowQueries = [] }) => {
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [queryModalVisible, setQueryModalVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  // Get slow query IDs for highlighting
  const slowQueryIds = useMemo(() => 
    new Set(slowQueries.map(q => `${q.request_id}-${q.query_number}`)),
    [slowQueries]
  );

  const handleViewQuery = (entry) => {
    setSelectedEntry(entry);
    setQueryModalVisible(true);
  };

  const getRowClassName = (record) => {
    const key = `${record.request_id}-${record.query_number}`;
    if (slowQueryIds.has(key)) {
      return "bg-red-50";
    }
    if (record.execution_time_ms > 500) {
      return "bg-yellow-50";
    }
    return "";
  };

  const columns = [
    {
      title: "Timestamp",
      dataIndex: "timestamp",
      key: "timestamp",
      width: 160,
      sorter: (a, b) => new Date(a.timestamp) - new Date(b.timestamp),
      render: (timestamp) => (
        <div className="text-xs">
          {SQLLogService.formatTimestamp(timestamp)}
        </div>
      ),
    },
    {
      title: "Request ID",
      dataIndex: "request_id",
      key: "request_id",
      width: 120,
      ellipsis: {
        showTitle: false,
      },
      render: (requestId) => (
        <Tooltip title={requestId}>
          <span className="font-mono text-xs">{requestId.slice(-8)}</span>
        </Tooltip>
      ),
    },
    {
      title: "Endpoint",
      dataIndex: "endpoint",
      key: "endpoint",
      width: 200,
      ellipsis: true,
      sorter: (a, b) => a.endpoint.localeCompare(b.endpoint),
      render: (endpoint) => (
        <Tooltip title={endpoint}>
          <span className="text-sm">{endpoint}</span>
        </Tooltip>
      ),
    },
    {
      title: "Query #",
      dataIndex: "query_number",
      key: "query_number",
      width: 80,
      align: "center",
      sorter: (a, b) => a.query_number - b.query_number,
      render: (queryNumber) => (
        <span className="font-mono text-sm">{queryNumber}</span>
      ),
    },
    {
      title: "Type",
      dataIndex: "query_type",
      key: "query_type",
      width: 100,
      filters: [
        { text: "SELECT", value: "SELECT" },
        { text: "INSERT", value: "INSERT" },
        { text: "UPDATE", value: "UPDATE" },
        { text: "DELETE", value: "DELETE" },
      ],
      onFilter: (value, record) => record.query_type === value,
      render: (queryType) => {
        const color = {
          SELECT: "blue",
          INSERT: "green", 
          UPDATE: "orange",
          DELETE: "red",
        }[queryType] || "default";
        
        return <Tag color={color}>{queryType}</Tag>;
      },
    },
    {
      title: "Execution Time",
      dataIndex: "execution_time_ms",
      key: "execution_time_ms",
      width: 120,
      sorter: (a, b) => a.execution_time_ms - b.execution_time_ms,
      render: (timeMs, record) => {
        const formatted = SQLLogService.formatExecutionTime(timeMs);
        const colorClass = SQLLogService.getPerformanceColor(timeMs);
        const key = `${record.request_id}-${record.query_number}`;
        const isSlow = slowQueryIds.has(key);
        
        return (
          <div className="flex items-center gap-1">
            {isSlow && (
              <ExclamationIcon className="w-4 h-4 text-red-500" />
            )}
            <span className={`font-mono text-sm ${colorClass}`}>
              {formatted}
            </span>
          </div>
        );
      },
    },
    {
      title: "Tables",
      dataIndex: "tables",
      key: "tables",
      width: 200,
      render: (tables) => (
        <div className="flex flex-wrap gap-1">
          {tables.slice(0, 2).map((table, idx) => (
            <Tag key={idx} size="small" icon={<DatabaseIcon className="w-3 h-3" />}>
              {table}
            </Tag>
          ))}
          {tables.length > 2 && (
            <Tooltip title={tables.slice(2).join(", ")}>
              <Tag size="small" color="default">
                +{tables.length - 2} more
              </Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: "Query",
      dataIndex: "query_simplified",
      key: "query_simplified",
      ellipsis: {
        showTitle: false,
      },
      render: (query, record) => (
        <div className="flex items-center gap-2">
          <Tooltip title="Click to view full query">
            <span 
              className="font-mono text-xs text-gray-600 cursor-pointer hover:text-blue-600 flex-1"
              onClick={() => handleViewQuery(record)}
            >
              {SQLLogService.truncateText(query, 50)}
            </span>
          </Tooltip>
          <Button
            type="text"
            size="small"
            icon={<EyeIcon className="w-4 h-4" />}
            onClick={() => handleViewQuery(record)}
          />
        </div>
      ),
    },
    {
      title: "Database",
      dataIndex: "database_type",
      key: "database_type",
      width: 100,
      filters: [
        { text: "PostgreSQL", value: "postgresql" },
        { text: "MySQL", value: "mysql" },
        { text: "SQLite", value: "sqlite" },
      ],
      onFilter: (value, record) => record.database_type === value,
      render: (dbType) => (
        <Tag color="purple">{dbType}</Tag>
      ),
    },
  ];

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return entries.slice(startIndex, endIndex);
  }, [entries, currentPage, pageSize]);

  const handleTableChange = (pagination, filters, sorter) => {
    // Handle table changes if needed
    console.log("Table change:", { pagination, filters, sorter });
  };

  return (
    <div className="log-entries-view">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">SQL Queries</h3>
          <div className="text-sm text-gray-600">
            {entries.length} total entries
            {slowQueries.length > 0 && (
              <span className="ml-2 text-red-600">
                ({slowQueries.length} slow queries)
              </span>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-50 border border-red-200 rounded"></div>
            <span>Slow (&gt;1s)</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-yellow-50 border border-yellow-200 rounded"></div>
            <span>Medium (&gt;500ms)</span>
          </div>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={paginatedData}
        loading={loading}
        pagination={false}
        onChange={handleTableChange}
        rowKey={(record) => `${record.request_id}-${record.query_number}`}
        rowClassName={getRowClassName}
        scroll={{ x: 1200, y: 500 }}
        size="small"
        bordered
      />

      <div className="mt-4 flex justify-end">
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={entries.length}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => 
            `${range[0]}-${range[1]} of ${total} entries`
          }
          onChange={(page, size) => {
            setCurrentPage(page);
            setPageSize(size);
          }}
          pageSizeOptions={["25", "50", "100", "200"]}
        />
      </div>

      {/* Query Detail Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <DatabaseIcon className="w-5 h-5" />
            <span>Query Details</span>
          </div>
        }
        open={queryModalVisible}
        onCancel={() => setQueryModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setQueryModalVisible(false)}>
            Close
          </Button>,
        ]}
      >
        {selectedEntry && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded">
              <div>
                <label className="text-sm font-medium text-gray-700">Request ID</label>
                <div className="font-mono text-sm">{selectedEntry.request_id}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Query Number</label>
                <div className="font-mono text-sm">{selectedEntry.query_number}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Execution Time</label>
                <div className={`font-mono text-sm ${SQLLogService.getPerformanceColor(selectedEntry.execution_time_ms)}`}>
                  {SQLLogService.formatExecutionTime(selectedEntry.execution_time_ms)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Query Type</label>
                <div>
                  <Tag color="blue">{selectedEntry.query_type}</Tag>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Endpoint</label>
                <div className="text-sm">{selectedEntry.endpoint}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Timestamp</label>
                <div className="text-sm">{SQLLogService.formatTimestamp(selectedEntry.timestamp)}</div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Tables Accessed</label>
              <div className="flex flex-wrap gap-2">
                {selectedEntry.tables.map((table, idx) => (
                  <Tag key={idx} icon={<DatabaseIcon className="w-3 h-3" />}>
                    {table}
                  </Tag>
                ))}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">SQL Query</label>
              <div className="p-4 bg-gray-900 text-green-400 rounded overflow-auto max-h-64">
                <pre className="text-sm font-mono whitespace-pre-wrap">
                  {selectedEntry.query}
                </pre>
              </div>
            </div>

            {selectedEntry.arguments && selectedEntry.arguments.length > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">Query Arguments</label>
                <div className="p-4 bg-gray-50 rounded">
                  <pre className="text-sm">
                    {JSON.stringify(selectedEntry.arguments, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default LogEntriesView; 