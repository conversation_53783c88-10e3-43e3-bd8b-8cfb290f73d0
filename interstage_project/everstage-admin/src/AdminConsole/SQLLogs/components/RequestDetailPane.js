import React, { useState, useMemo } from "react";
import { Tag, Button, Modal, Typography, Divider, Empty, Spin, Select } from "antd";
import { 
  ClockIcon, 
  DatabaseIcon, 
  EyeIcon,
  ExclamationIcon,
  FilterIcon
} from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";
import SQLQueryDisplay from "./SQLQueryDisplay";

const { Text, Paragraph } = Typography;

/**
 * RequestDetailPane Component
 * 
 * Renders the right pane of the SQL log analysis interface containing a streamlined
 * list view of queries for the selected request. Uses line demarcation instead of
 * cards for a cleaner, more compact display.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.selectedRequest - The currently selected request object
 * @param {Array} props.requestQueries - Array of queries for the selected request
 * @param {boolean} props.queriesLoading - Whether the queries are being loaded
 * 
 * @example
 * <RequestDetailPane
 *   selectedRequest={currentRequest}
 *   requestQueries={queries}
 *   queriesLoading={false}
 * />
 */
const RequestDetailPane = ({
  selectedRequest,
  requestQueries,
  queriesLoading
}) => {
  const [selectedQuery, setSelectedQuery] = useState(null);
  const [queryModalVisible, setQueryModalVisible] = useState(false);
  const [hiddenTables, setHiddenTables] = useState([]);

  // Extract unique tables from all queries for hide dropdown
  const availableTables = useMemo(() => {
    if (!requestQueries || requestQueries.length === 0) return [];
    
    const tablesSet = new Set();
    requestQueries.forEach(query => {
      if (query.tables && Array.isArray(query.tables)) {
        query.tables.forEach(table => tablesSet.add(table));
      }
    });
    
    return Array.from(tablesSet).sort();
  }, [requestQueries]);

  // Hide queries based on selected tables
  const { displayQueries, placeholderQueries } = useMemo(() => {
    if (!requestQueries || hiddenTables.length === 0) {
      return { displayQueries: requestQueries || [], placeholderQueries: [] };
    }

    const filtered = [];
    const placeholders = [];

    requestQueries.forEach(query => {
      const queryTables = query.tables || [];
      // The queryTables should be of length 1 and should contain one of the hidden tables
      const accessesHiddenTable = queryTables.length === 1 &&  hiddenTables.includes(queryTables[0]);
      
      if (accessesHiddenTable) {
        placeholders.push(query);
      } else {
        filtered.push(query);
      }
    });

    return { displayQueries: filtered, placeholderQueries: placeholders };
  }, [requestQueries, hiddenTables]);

  const handleViewQuery = (query) => {
    setSelectedQuery(query);
    setQueryModalVisible(true);
  };

  const handleHiddenTablesChange = (value) => {
    setHiddenTables(value || []);
  };

  const clearHiddenTables = () => {
    setHiddenTables([]);
  };

  // Loading state
  if (queriesLoading) {
    return (
      <div className="flex-1 bg-white">
        <div className="h-full p-4 flex items-center justify-center">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  // No request selected
  if (!selectedRequest) {
    return (
      <div className="flex-1 bg-white">
        <div className="h-full p-4 flex items-center justify-center">
          <Empty
            description="Select a request from the left panel to view its queries"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      </div>
    );
  }

  // No queries found
  if (!requestQueries || requestQueries.length === 0) {
    return (
      <div className="flex-1 bg-white">
        <div className="h-full p-4">
          {/* Header */}
          <div className="border-b pb-4 mb-4">
            <h3 className="text-lg font-semibold mb-1">{selectedRequest.endpoint}</h3>
            <Text type="secondary">Request ID: {selectedRequest.request_id}</Text>
          </div>
          
          <div className="flex items-center justify-center h-64">
            <Empty
              description="No queries found for this request"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-white flex flex-col">
      <div className="p-4 border-b bg-gray-50">
        {/* Request Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold mb-1 truncate">{selectedRequest.endpoint}</h3>
            <Text type="secondary" className="text-sm">
              Request ID: {selectedRequest.request_id}
            </Text>
          </div>
          <div className="text-right ml-4">
            <div className="text-sm text-gray-600 mb-1">
              {hiddenTables.length > 0 ? (
                <>
                  {displayQueries.length} / {requestQueries.length} queries
                  {placeholderQueries.length > 0 && (
                    <span className="text-gray-500"> ({placeholderQueries.length} hidden)</span>
                  )}
                </>
              ) : (
                `${requestQueries.length} queries`
              )}
            </div>
            <div className={`text-sm font-mono ${SQLLogService.getPerformanceColor(selectedRequest.total_execution_time_ms)}`}>
              Total: {SQLLogService.formatExecutionTime(selectedRequest.total_execution_time_ms)}
            </div>
          </div>
        </div>
        
        {/* Hide Tables */}
        {availableTables.length > 0 && (
          <div className="flex items-center gap-3 mb-3">
            <FilterIcon className="w-4 h-4 text-gray-500" />
            <Text className="text-sm text-gray-600">Hide tables:</Text>
            <Select
              mode="multiple"
              placeholder="Select tables to hide"
              value={hiddenTables}
              onChange={handleHiddenTablesChange}
              allowClear
              onClear={clearHiddenTables}
              style={{ minWidth: 200 }}
              size="small"
              maxTagCount="responsive"
            >
              {availableTables.map(table => (
                <Select.Option key={table} value={table}>
                  <DatabaseIcon className="w-3 h-3 inline mr-1" />
                  {table}
                </Select.Option>
              ))}
            </Select>
            {hiddenTables.length > 0 && (
              <Text className="text-xs text-gray-500">
                Hiding queries that access: {hiddenTables.join(', ')}
              </Text>
            )}
          </div>
        )}
        
        {/* Request Metadata */}
        <div className="flex items-center gap-6 text-sm text-gray-600 flex-wrap">
          <span>Started: {SQLLogService.formatTimestamp(selectedRequest.start_time)}</span>
          <span>Ended: {SQLLogService.formatTimestamp(selectedRequest.end_time)}</span>
          {selectedRequest.slow_queries_count > 0 && (
            <span className="text-red-600 flex items-center gap-1">
              <ExclamationIcon className="w-4 h-4" />
              {selectedRequest.slow_queries_count} slow queries
            </span>
          )}
        </div>
      </div>

      {/* Queries List */}
      <div className="flex-1 overflow-auto">
        <div className="divide-y divide-gray-200">
          {displayQueries.map((query, index) => {
            const isSlowQuery = query.execution_time_ms > 1000;
            const isMediumQuery = query.execution_time_ms > 500;
            
            return (
              <div 
                key={`${query.query_number}-${index}`}
                className={`p-4 hover:bg-gray-50 transition-colors ${
                  isSlowQuery ? 'bg-red-50 border-l-4 border-l-red-400' : 
                  isMediumQuery ? 'bg-yellow-50 border-l-4 border-l-yellow-400' : 
                  ''
                }`}
              >
                {/* Query Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="font-mono text-sm font-semibold text-gray-700">
                      #{query.query_number}
                    </span>
                    <Tag 
                      size="small"
                      color={
                        query.query_type === 'SELECT' ? 'blue' :
                        query.query_type === 'INSERT' ? 'green' :
                        query.query_type === 'UPDATE' ? 'orange' :
                        query.query_type === 'DELETE' ? 'red' : 'default'
                      }
                    >
                      {query.query_type}
                    </Tag>
                    <span className="text-xs text-gray-500">
                      {SQLLogService.formatTimestamp(query.timestamp)}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {isSlowQuery && (
                      <ExclamationIcon className="w-4 h-4 text-red-500" />
                    )}
                    <span className={`font-mono text-sm font-medium ${SQLLogService.getPerformanceColor(query.execution_time_ms)}`}>
                      {SQLLogService.formatExecutionTime(query.execution_time_ms)}
                    </span>
                    <Button
                      type="text"
                      size="small"
                      icon={<EyeIcon className="w-4 h-4" />}
                      onClick={() => handleViewQuery(query)}
                      className="ml-2"
                    />
                  </div>
                </div>

                {/* Tables */}
                {query.tables && query.tables.length > 0 && (
                  <div className="flex items-center gap-2 mb-3">
                    <DatabaseIcon className="w-4 h-4 text-gray-400" />
                    <div className="flex flex-wrap gap-1">
                                              {query.tables.map((table, idx) => (
                          <Tag 
                            key={idx} 
                            size="small" 
                            className={`text-xs ${hiddenTables.includes(table) ? 'bg-red-100 border-red-300' : ''}`}
                          >
                            {table}
                          </Tag>
                        ))}
                    </div>
                  </div>
                )}

                {/* Query Display */}
                <SQLQueryDisplay
                  query={query.query}
                  querySimplified={query.query_simplified}
                  isPreview={true}
                  maxPreviewLines={20}
                  onFullView={() => handleViewQuery(query)}
                  showCopyButton={true}
                  showExpandButton={true}
                />
              </div>
            );
          })}

          {/* Placeholder rows for hidden queries */}
          {hiddenTables.length > 0 && placeholderQueries.map((query, index) => (
            <div 
              key={`placeholder-${query.query_number}-${index}`}
              className="p-2 bg-gray-100 border-l-4 border-l-gray-300 hover:bg-gray-150 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="font-mono text-xs text-gray-500">
                    #{query.query_number}
                  </span>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <DatabaseIcon className="w-3 h-3" />
                    <span>
                      {query.tables && query.tables.length > 0 
                        ? `Accessed: ${query.tables.join(', ')}`
                        : 'No tables'
                      }
                    </span>
                  </div>
                  <span className="text-xs text-gray-400">
                    {SQLLogService.formatTimestamp(query.timestamp)}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-mono text-xs text-gray-500">
                    {SQLLogService.formatExecutionTime(query.execution_time_ms)}
                  </span>
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeIcon className="w-3 h-3" />}
                    onClick={() => handleViewQuery(query)}
                    className="opacity-50 hover:opacity-100"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Query Detail Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <span>Query #{selectedQuery?.query_number} Details</span>
            {selectedQuery && (
              <Tag color={
                selectedQuery.query_type === 'SELECT' ? 'blue' :
                selectedQuery.query_type === 'INSERT' ? 'green' :
                selectedQuery.query_type === 'UPDATE' ? 'orange' :
                selectedQuery.query_type === 'DELETE' ? 'red' : 'default'
              }>
                {selectedQuery.query_type}
              </Tag>
            )}
          </div>
        }
        open={queryModalVisible}
        onCancel={() => setQueryModalVisible(false)}
        footer={null}
        width="80%"
        style={{ top: 20 }}
        destroyOnClose={true}
      >
        {selectedQuery && (
          <div className="space-y-4">
            {/* Query Metadata */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded">
              <div>
                <Text strong className="text-gray-700">Execution Time:</Text>
                <div className={`font-mono text-lg ${SQLLogService.getPerformanceColor(selectedQuery.execution_time_ms)}`}>
                  {SQLLogService.formatExecutionTime(selectedQuery.execution_time_ms)}
                </div>
              </div>
              <div>
                <Text strong className="text-gray-700">Timestamp:</Text>
                <div className="text-sm">
                  {SQLLogService.formatTimestamp(selectedQuery.timestamp)}
                </div>
              </div>
              <div>
                <Text strong className="text-gray-700">Database:</Text>
                <div className="text-sm">
                  {selectedQuery.database_type || 'Unknown'}
                </div>
              </div>
              <div>
                <Text strong className="text-gray-700">Query Number:</Text>
                <div className="font-mono text-sm">
                  #{selectedQuery.query_number}
                </div>
              </div>
            </div>

            {selectedQuery.tables && selectedQuery.tables.length > 0 && (
              <div>
                <Text strong className="text-gray-700">Tables Accessed:</Text>
                <div className="mt-2 flex flex-wrap gap-1">
                  {selectedQuery.tables.map((table, idx) => (
                    <Tag key={idx} icon={<DatabaseIcon className="w-3 h-3" />}>
                      {table}
                    </Tag>
                  ))}
                </div>
              </div>
            )}

            <Divider />

            {/* Full Query */}
            <div>
              <Text strong className="text-gray-700">Full Query:</Text>
              <div className="mt-2">
                <SQLQueryDisplay
                  query={selectedQuery.query}
                  querySimplified={selectedQuery.query_simplified}
                  isPreview={false}
                  showCopyButton={true}
                  showExpandButton={false}
                />
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RequestDetailPane; 