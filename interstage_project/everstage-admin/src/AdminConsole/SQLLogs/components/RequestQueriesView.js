import React, { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON>, Divider, Empty, Spin } from "antd";
import { 
  ClockIcon, 
  DatabaseIcon, 
  EyeIcon,
  ExclamationIcon 
} from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

const { Text, Paragraph } = Typography;

/**
 * RequestQueriesView component for displaying individual queries for a selected request
 * Used in the right pane of the two-pane SQL logs interface
 */
const RequestQueriesView = ({ request, queries, loading }) => {
  const [selectedQuery, setSelectedQuery] = useState(null);
  const [queryModalVisible, setQueryModalVisible] = useState(false);

  const handleViewQuery = (query) => {
    setSelectedQuery(query);
    setQueryModalVisible(true);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <div className="flex items-center justify-center h-64">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!request) {
    return (
      <Card className="h-full">
        <Empty
          description="Select a request from the left panel to view its queries"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  if (!queries || queries.length === 0) {
    return (
      <Card className="h-full">
        <div className="mb-4">
          <h3 className="text-lg font-semibold">{request.endpoint}</h3>
          <Text type="secondary">Request ID: {request.request_id}</Text>
        </div>
        <Empty
          description="No queries found for this request"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <Card className="mb-4">
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-1">{request.endpoint}</h3>
              <Text type="secondary" className="text-sm">
                Request ID: {request.request_id}
              </Text>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">
                {queries.length} queries
              </div>
              <div className={`text-sm font-mono ${SQLLogService.getPerformanceColor(request.total_execution_time_ms)}`}>
                Total: {SQLLogService.formatExecutionTime(request.total_execution_time_ms)}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>Started: {SQLLogService.formatTimestamp(request.start_time)}</span>
            <span>Ended: {SQLLogService.formatTimestamp(request.end_time)}</span>
                         {request.slow_queries_count > 0 && (
               <span className="text-red-600 flex items-center gap-1">
                 <ExclamationIcon className="w-4 h-4" />
                 {request.slow_queries_count} slow queries
               </span>
             )}
          </div>
        </div>
      </Card>

      {/* Queries List */}
      <div className="flex-1 overflow-auto">
        <div className="space-y-3">
          {queries.map((query, index) => {
            const isSlowQuery = query.execution_time_ms > 1000;
            const isMediumQuery = query.execution_time_ms > 500;
            
            return (
              <Card 
                key={`${query.query_number}-${index}`}
                size="small"
                className={`${
                  isSlowQuery ? 'border-red-300 bg-red-50' : 
                  isMediumQuery ? 'border-yellow-300 bg-yellow-50' : 
                  'border-gray-200'
                }`}
              >
                <div className="space-y-3">
                  {/* Query Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm font-semibold">
                        #{query.query_number}
                      </span>
                      <Tag color={
                        query.query_type === 'SELECT' ? 'blue' :
                        query.query_type === 'INSERT' ? 'green' :
                        query.query_type === 'UPDATE' ? 'orange' :
                        query.query_type === 'DELETE' ? 'red' : 'default'
                      }>
                        {query.query_type}
                      </Tag>
                      <span className="text-xs text-gray-500">
                        {SQLLogService.formatTimestamp(query.timestamp)}
                      </span>
                    </div>
                    
                                         <div className="flex items-center gap-2">
                       {isSlowQuery && (
                         <ExclamationIcon className="w-4 h-4 text-red-500" />
                       )}
                       <span className={`font-mono text-sm ${SQLLogService.getPerformanceColor(query.execution_time_ms)}`}>
                         {SQLLogService.formatExecutionTime(query.execution_time_ms)}
                       </span>
                      <Button
                        type="text"
                        size="small"
                        icon={<EyeIcon className="w-4 h-4" />}
                        onClick={() => handleViewQuery(query)}
                      />
                    </div>
                  </div>

                  {/* Tables */}
                  {query.tables && query.tables.length > 0 && (
                    <div className="flex items-center gap-2">
                      <DatabaseIcon className="w-4 h-4 text-gray-400" />
                      <div className="flex flex-wrap gap-1">
                        {query.tables.map((table, idx) => (
                          <Tag key={idx} size="small">
                            {table}
                          </Tag>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Query Preview */}
                  <div 
                    className="cursor-pointer hover:bg-gray-50 p-2 rounded border border-gray-100"
                    onClick={() => handleViewQuery(query)}
                  >
                    <Text 
                      code 
                      className="text-xs text-gray-700 block"
                      style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
                    >
                      {SQLLogService.truncateText(query.query_simplified || query.query, 200)}
                    </Text>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Query Detail Modal */}
      <Modal
        title={`Query #${selectedQuery?.query_number} Details`}
        open={queryModalVisible}
        onCancel={() => setQueryModalVisible(false)}
        footer={null}
        width="80%"
        style={{ top: 20 }}
      >
        {selectedQuery && (
          <div className="space-y-4">
            {/* Query Metadata */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text strong>Query Type:</Text>
                <div>
                  <Tag color={
                    selectedQuery.query_type === 'SELECT' ? 'blue' :
                    selectedQuery.query_type === 'INSERT' ? 'green' :
                    selectedQuery.query_type === 'UPDATE' ? 'orange' :
                    selectedQuery.query_type === 'DELETE' ? 'red' : 'default'
                  }>
                    {selectedQuery.query_type}
                  </Tag>
                </div>
              </div>
              <div>
                <Text strong>Execution Time:</Text>
                <div className={`font-mono ${SQLLogService.getPerformanceColor(selectedQuery.execution_time_ms)}`}>
                  {SQLLogService.formatExecutionTime(selectedQuery.execution_time_ms)}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text strong>Timestamp:</Text>
                <div className="text-sm">
                  {SQLLogService.formatTimestamp(selectedQuery.timestamp)}
                </div>
              </div>
              <div>
                <Text strong>Database:</Text>
                <div className="text-sm">
                  {selectedQuery.database_type || 'Unknown'}
                </div>
              </div>
            </div>

            {selectedQuery.tables && selectedQuery.tables.length > 0 && (
              <div>
                <Text strong>Tables Accessed:</Text>
                <div className="mt-1">
                  {selectedQuery.tables.map((table, idx) => (
                    <Tag key={idx} icon={<DatabaseIcon className="w-3 h-3" />}>
                      {table}
                    </Tag>
                  ))}
                </div>
              </div>
            )}

            <Divider />

            {/* Full Query */}
            <div>
              <Text strong>Full Query:</Text>
              <div className="mt-2 p-4 bg-gray-50 rounded border max-h-96 overflow-auto">
                <Paragraph
                  code
                  copyable
                  className="text-sm mb-0"
                  style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
                >
                  {selectedQuery.query || selectedQuery.query_simplified}
                </Paragraph>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RequestQueriesView; 