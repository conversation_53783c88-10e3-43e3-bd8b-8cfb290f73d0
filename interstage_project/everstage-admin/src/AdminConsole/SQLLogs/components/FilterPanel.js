import React, { useState, useEffect } from "react";
import { Card, DatePicker, Select, Slider, Input, Button, Space, Tag, Collapse } from "antd";
import { FilterIcon, XIcon } from "@heroicons/react/outline";
import { debounce } from "lodash";

const { RangePicker } = DatePicker;
const { Panel } = Collapse;

/**
 * FilterPanel component for advanced SQL log filtering
 * Provides time range, execution time, and multi-select filters with debouncing
 */
const FilterPanel = ({ filters, onFiltersChange, availableOptions, loading }) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [executionTimeRange, setExecutionTimeRange] = useState([0, 10000]);

  // Debounced filter update
  const debouncedUpdate = debounce((newFilters) => {
    onFiltersChange(newFilters);
  }, 300);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key, value) => {
    const updatedFilters = {
      ...localFilters,
      [key]: value,
    };
    setLocalFilters(updatedFilters);
    debouncedUpdate(updatedFilters);
  };

  const handleTimeRangeChange = (dates, dateStrings) => {
    const updatedFilters = {
      ...localFilters,
      start_time: dates?.[0]?.toISOString() || null,
      end_time: dates?.[1]?.toISOString() || null,
    };
    setLocalFilters(updatedFilters);
    debouncedUpdate(updatedFilters);
  };

  const handleExecutionTimeChange = (values) => {
    setExecutionTimeRange(values);
    const updatedFilters = {
      ...localFilters,
      min_execution_time_ms: values[0] > 0 ? values[0] : null,
      max_execution_time_ms: values[1] < 10000 ? values[1] : null,
    };
    setLocalFilters(updatedFilters);
    debouncedUpdate(updatedFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      start_time: null,
      end_time: null,
      min_execution_time_ms: null,
      max_execution_time_ms: null,
      request_ids: [],
      endpoints: [],
      query_types: [],
      tables: [],
      client_ids: [],
      limit: localFilters.limit,
      offset: 0,
    };
    setLocalFilters(clearedFilters);
    setExecutionTimeRange([0, 10000]);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = () => {
    return (
      localFilters.start_time ||
      localFilters.end_time ||
      localFilters.min_execution_time_ms !== null ||
      localFilters.max_execution_time_ms !== null ||
      localFilters.request_ids?.length > 0 ||
      localFilters.endpoints?.length > 0 ||
      localFilters.query_types?.length > 0 ||
      localFilters.tables?.length > 0 ||
      localFilters.client_ids?.length > 0
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (localFilters.start_time || localFilters.end_time) count++;
    if (localFilters.min_execution_time_ms !== null || localFilters.max_execution_time_ms !== null) count++;
    if (localFilters.request_ids?.length > 0) count++;
    if (localFilters.endpoints?.length > 0) count++;
    if (localFilters.query_types?.length > 0) count++;
    if (localFilters.tables?.length > 0) count++;
    if (localFilters.client_ids?.length > 0) count++;
    return count;
  };

  return (
    <Card className="mb-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <FilterIcon className="w-5 h-5 text-gray-600" />
          <h4 className="text-lg font-medium">Filters</h4>
          {hasActiveFilters() && (
            <Tag color="blue">{getActiveFilterCount()} active</Tag>
          )}
        </div>
        
        <Space>
          <Button
            size="small"
            onClick={clearAllFilters}
            disabled={!hasActiveFilters() || loading}
            icon={<XIcon className="w-4 h-4" />}
          >
            Clear All
          </Button>
        </Space>
      </div>

      <Collapse defaultActiveKey={['time', 'performance']} ghost>
        <Panel header="Time Range" key="time">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Date/Time Range
              </label>
              <RangePicker
                showTime={{
                  format: 'HH:mm:ss',
                }}
                format="YYYY-MM-DD HH:mm:ss"
                onChange={handleTimeRangeChange}
                value={[
                  localFilters.start_time ? new Date(localFilters.start_time) : null,
                  localFilters.end_time ? new Date(localFilters.end_time) : null,
                ]}
                placeholder={['Start time', 'End time']}
                className="w-full"
              />
            </div>
          </div>
        </Panel>

        <Panel header="Performance" key="performance">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Execution Time Range (ms): {executionTimeRange[0]} - {executionTimeRange[1]}
                {executionTimeRange[1] >= 10000 && "+"}
              </label>
              <Slider
                range
                min={0}
                max={10000}
                step={10}
                value={executionTimeRange}
                onChange={handleExecutionTimeChange}
                marks={{
                  0: '0ms',
                  100: '100ms',
                  500: '500ms',
                  1000: '1s',
                  5000: '5s',
                  10000: '10s+',
                }}
              />
            </div>
          </div>
        </Panel>

        <Panel header="Request & Endpoint Filters" key="request">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Request IDs
              </label>
              <Select
                mode="tags"
                placeholder="Enter request IDs"
                value={localFilters.request_ids}
                onChange={(value) => handleFilterChange('request_ids', value)}
                className="w-full"
                tokenSeparators={[',', ' ']}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Endpoints
              </label>
              <Select
                mode="multiple"
                placeholder="Select endpoints"
                value={localFilters.endpoints}
                onChange={(value) => handleFilterChange('endpoints', value)}
                className="w-full"
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {availableOptions.endpoints?.map((endpoint) => (
                  <Select.Option key={endpoint} value={endpoint}>
                    {endpoint}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
        </Panel>

        <Panel header="Query & Table Filters" key="query">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Query Types
              </label>
              <Select
                mode="multiple"
                placeholder="Select query types"
                value={localFilters.query_types}
                onChange={(value) => handleFilterChange('query_types', value)}
                className="w-full"
              >
                <Select.Option value="SELECT">SELECT</Select.Option>
                <Select.Option value="INSERT">INSERT</Select.Option>
                <Select.Option value="UPDATE">UPDATE</Select.Option>
                <Select.Option value="DELETE">DELETE</Select.Option>
                <Select.Option value="CREATE">CREATE</Select.Option>
                <Select.Option value="DROP">DROP</Select.Option>
                <Select.Option value="ALTER">ALTER</Select.Option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tables
              </label>
              <Select
                mode="multiple"
                placeholder="Select tables"
                value={localFilters.tables}
                onChange={(value) => handleFilterChange('tables', value)}
                className="w-full"
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {availableOptions.tables?.map((table) => (
                  <Select.Option key={table} value={table}>
                    {table}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
        </Panel>

        <Panel header="Client & Pagination" key="client">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Client IDs
              </label>
              <Select
                mode="tags"
                placeholder="Enter client IDs"
                value={localFilters.client_ids}
                onChange={(value) => handleFilterChange('client_ids', value)}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Results Limit
              </label>
              <Select
                value={localFilters.limit}
                onChange={(value) => handleFilterChange('limit', value)}
                className="w-full"
              >
                <Select.Option value={100}>100</Select.Option>
                <Select.Option value={500}>500</Select.Option>
                <Select.Option value={1000}>1,000</Select.Option>
                <Select.Option value={2000}>2,000</Select.Option>
                <Select.Option value={5000}>5,000</Select.Option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Offset
              </label>
              <Input
                type="number"
                min={0}
                value={localFilters.offset}
                onChange={(e) => handleFilterChange('offset', parseInt(e.target.value) || 0)}
                placeholder="0"
              />
            </div>
          </div>
        </Panel>
      </Collapse>

      {hasActiveFilters() && (
        <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
          <div className="text-sm text-blue-800">
            <strong>Active Filters:</strong>
            <div className="mt-2 flex flex-wrap gap-2">
              {localFilters.start_time && (
                <Tag color="blue" size="small">
                  Start: {new Date(localFilters.start_time).toLocaleString()}
                </Tag>
              )}
              {localFilters.end_time && (
                <Tag color="blue" size="small">
                  End: {new Date(localFilters.end_time).toLocaleString()}
                </Tag>
              )}
              {localFilters.min_execution_time_ms !== null && (
                <Tag color="orange" size="small">
                  Min time: {localFilters.min_execution_time_ms}ms
                </Tag>
              )}
              {localFilters.max_execution_time_ms !== null && (
                <Tag color="orange" size="small">
                  Max time: {localFilters.max_execution_time_ms}ms
                </Tag>
              )}
              {localFilters.endpoints?.map((endpoint, idx) => (
                <Tag key={idx} color="green" size="small">
                  Endpoint: {endpoint}
                </Tag>
              ))}
              {localFilters.query_types?.map((type, idx) => (
                <Tag key={idx} color="purple" size="small">
                  Type: {type}
                </Tag>
              ))}
              {localFilters.tables?.map((table, idx) => (
                <Tag key={idx} color="cyan" size="small">
                  Table: {table}
                </Tag>
              ))}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default FilterPanel; 