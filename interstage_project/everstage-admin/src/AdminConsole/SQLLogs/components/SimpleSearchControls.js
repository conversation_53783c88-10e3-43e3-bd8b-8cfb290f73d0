import React from "react";
import { Input, Select, Typography } from "antd";

const { Search } = Input;
const { Text } = Typography;

/**
 * SimpleSearchControls Component
 * 
 * Provides basic search, sorting, and filtering controls for the SQL log analysis.
 * This component handles the simplified filtering interface that's separate from
 * the more advanced FilterPanel component.
 * 
 * @param {Object} props - Component props
 * @param {string} props.searchTerm - Current search term
 * @param {Function} props.onSearchChange - Callback when search term changes
 * @param {Function} props.onSearchClear - Callback when search is cleared
 * @param {string} props.sortBy - Current sort field
 * @param {Function} props.onSortChange - Callback when sort field changes
 * @param {string} props.filterSlow - Current slow query filter ('all', 'slow', 'fast')
 * @param {Function} props.onFilterChange - Callback when filter changes
 * @param {number} props.totalCount - Total number of requests
 * @param {number} props.filteredCount - Number of filtered requests
 * 
 * @example
 * <SimpleSearchControls
 *   searchTerm="api/users"
 *   onSearchChange={handleSearchChange}
 *   onSearchClear={handleSearchClear}
 *   sortBy="start_time"
 *   onSortChange={handleSortChange}
 *   filterSlow="all"
 *   onFilterChange={handleFilterChange}
 *   totalCount={150}
 *   filteredCount={23}
 * />
 */
const SimpleSearchControls = ({
  searchTerm,
  onSearchChange,
  onSearchClear,
  sortBy,
  onSortChange,
  filterSlow,
  onFilterChange,
  totalCount,
  filteredCount
}) => {
  const sortOptions = [
    { value: "start_time", label: "Sort by Time" },
    { value: "total_queries", label: "Sort by Query Count" },
    { value: "total_execution_time_ms", label: "Sort by Total Time" },
    { value: "slow_queries_count", label: "Sort by Slow Queries" }
  ];

  const filterOptions = [
    { value: "all", label: "All Requests" },
    { value: "slow", label: "Slow Requests" },
    { value: "fast", label: "Fast Requests" }
  ];

  return (
    <div className="flex items-center gap-4">
      <Search
        placeholder="Search by endpoint or request ID..."
        value={searchTerm}
        onChange={onSearchChange}
        onSearch={onSearchClear}
        style={{ width: 300 }}
        allowClear
        onClear={onSearchClear}
      />
      
      <Select
        value={sortBy}
        onChange={onSortChange}
        style={{ width: 180 }}
        aria-label="Sort requests by"
      >
        {sortOptions.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.label}
          </Select.Option>
        ))}
      </Select>
      
      <Select
        value={filterSlow}
        onChange={onFilterChange}
        style={{ width: 150 }}
        aria-label="Filter by request speed"
      >
        {filterOptions.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.label}
          </Select.Option>
        ))}
      </Select>
      
      {totalCount !== undefined && (
        <Text type="secondary">
          {filteredCount} of {totalCount} requests
        </Text>
      )}
    </div>
  );
};

export default SimpleSearchControls; 