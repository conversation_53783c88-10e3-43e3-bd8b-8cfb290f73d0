import React from "react";
import { Card, Badge, Tag } from "antd";
import { ClockIcon, DatabaseIcon, ExclamationIcon } from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

/**
 * RequestCard component for displaying request summary information
 * Used in the left pane of the two-pane SQL logs interface
 */
const RequestCard = ({ request, isSelected, onClick }) => {
  const {
    request_id,
    endpoint,
    total_queries,
    total_execution_time_ms,
    avg_execution_time_ms,
    slow_queries_count,
    start_time,
    unique_tables = [],
  } = request;

  const hasSlowQueries = slow_queries_count > 0;
  const slowPercentage = total_queries > 0 ? (slow_queries_count / total_queries * 100).toFixed(1) : 0;

  const handleClick = () => {
    onClick(request);
  };

  return (
    <Card
      className={`cursor-pointer transition-all duration-200 mb-3 ${
        isSelected 
          ? "border-blue-500 bg-blue-50 shadow-md" 
          : "border-gray-200 hover:border-gray-300 hover:shadow-sm"
      }`}
      size="small"
      onClick={handleClick}
    >
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-mono text-xs text-gray-500">
                {request_id.slice(-8)}
              </span>
              <span className="text-xs text-gray-400">
                {SQLLogService.formatTimestamp(start_time)}
              </span>
            </div>
            
            <div className="text-sm font-medium text-gray-900 truncate" title={endpoint}>
              {endpoint}
            </div>
          </div>
          
          {hasSlowQueries && (
            <Badge 
              count={slow_queries_count} 
              style={{ backgroundColor: '#f5222d' }}
              title={`${slow_queries_count} slow queries (${slowPercentage}%)`}
            />
          )}
        </div>

        {/* Metrics */}
        <div className="grid grid-cols-2 gap-3">
          <div className="flex items-center gap-1">
            <DatabaseIcon className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-600">{total_queries} queries</span>
          </div>
          
          <div className="flex items-center gap-1">
            <ClockIcon className="w-4 h-4 text-gray-400" />
            <span className={`text-sm font-mono ${SQLLogService.getPerformanceColor(avg_execution_time_ms)}`}>
              {SQLLogService.formatExecutionTime(avg_execution_time_ms)} avg
            </span>
          </div>
        </div>

        {/* Tables */}
        {unique_tables.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {unique_tables.slice(0, 3).map((table, idx) => (
              <Tag key={idx} size="small" className="text-xs">
                {table}
              </Tag>
            ))}
            {unique_tables.length > 3 && (
              <Tag size="small" color="default" className="text-xs">
                +{unique_tables.length - 3}
              </Tag>
            )}
          </div>
        )}

        {/* Performance Indicator */}
        <div className="flex items-center justify-between">
          <div className={`text-xs ${SQLLogService.getPerformanceColor(total_execution_time_ms)}`}>
            Total: {SQLLogService.formatExecutionTime(total_execution_time_ms)}
          </div>
          
          {hasSlowQueries && (
            <div className="flex items-center gap-1 text-red-600">
              <ExclamationIcon className="w-3 h-3" />
              <span className="text-xs">{slowPercentage}% slow</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default RequestCard; 