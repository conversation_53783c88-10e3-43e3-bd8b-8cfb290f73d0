import React, { useState } from "react";
import { But<PERSON>, Dropdown, <PERSON>u, Modal, message } from "antd";
import { DownloadIcon, DocumentIcon } from "@heroicons/react/outline";

/**
 * ExportControls component for exporting analysis data
 * Supports CSV and JSON export formats with format selection
 */
const ExportControls = ({ onExport, activeView, disabled = false }) => {
  const [loading, setLoading] = useState(false);

  const handleExport = async (format) => {
    setLoading(true);
    
    try {
      await onExport(format);
      message.success(`Successfully exported ${activeView} data as ${format.toUpperCase()}`);
    } catch (error) {
      console.error("Export failed:", error);
      message.error(`Failed to export data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const exportMenu = (
    <Menu
      items={[
        {
          key: "csv",
          label: (
            <div className="flex items-center gap-2">
              <DocumentIcon className="w-4 h-4" />
              <span>Export as CSV</span>
            </div>
          ),
          onClick: () => handleExport("csv"),
        },
        {
          key: "json",
          label: (
            <div className="flex items-center gap-2">
              <DocumentIcon className="w-4 h-4" />
              <span>Export as JSON</span>
            </div>
          ),
          onClick: () => handleExport("json"),
        },
      ]}
    />
  );

  const getViewDisplayName = () => {
    switch (activeView) {
      case "queries":
        return "SQL Queries";
      case "requests":
        return "Request Summaries";
      case "tables":
        return "Table Statistics";
      default:
        return "Data";
    }
  };

  return (
    <Dropdown
      menu={exportMenu}
      placement="bottomRight"
      disabled={disabled}
      trigger={["click"]}
    >
      <Button
        type="default"
        icon={<DownloadIcon className="w-4 h-4" />}
        loading={loading}
        disabled={disabled}
        size="small"
      >
        Export {getViewDisplayName()}
      </Button>
    </Dropdown>
  );
};

export default ExportControls; 