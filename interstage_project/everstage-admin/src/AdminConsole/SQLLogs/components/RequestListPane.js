import React, { forwardRef } from "react";
import { Spin, Typography } from "antd";
import RequestCard from "./RequestCard";

const { Text } = Typography;

/**
 * Memoized RequestCard component to prevent unnecessary re-renders
 * 
 * @param {Object} props - Component props
 * @param {Object} props.request - Request data object
 * @param {boolean} props.isSelected - Whether this request is currently selected
 * @param {Function} props.onClick - Callback when request is clicked
 */
const MemoizedRequestCard = React.memo(({ request, isSelected, onClick }) => (
  <RequestCard
    request={request}
    isSelected={isSelected}
    onClick={onClick}
  />
), (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.request.request_id === nextProps.request.request_id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.onClick === nextProps.onClick
  );
});

MemoizedRequestCard.displayName = 'MemoizedRequestCard';

/**
 * RequestListPane Component
 * 
 * Renders the left pane of the SQL log analysis interface containing the scrollable
 * list of requests. Supports loading states, empty states, and request selection.
 * 
 * @param {Object} props - Component props
 * @param {Array} props.requests - Array of request objects to display
 * @param {string} props.selectedRequestId - ID of the currently selected request
 * @param {Function} props.onRequestSelect - Callback when a request is selected
 * @param {boolean} props.loading - Whether the requests are being loaded
 * @param {string} props.searchTerm - Current search term for empty state messaging
 * @param {string} props.filterSlow - Current filter setting for empty state messaging
 * @param {React.Ref} ref - Ref to the scrollable container for scroll position management
 * 
 * @example
 * <RequestListPane
 *   requests={filteredRequests}
 *   selectedRequestId={selectedRequest?.request_id}
 *   onRequestSelect={handleRequestSelect}
 *   loading={false}
 *   searchTerm="api/users"
 *   filterSlow="slow"
 *   ref={scrollContainerRef}
 * />
 */
const RequestListPane = forwardRef(({
  requests,
  selectedRequestId,
  onRequestSelect,
  loading,
  searchTerm,
  filterSlow
}, ref) => {
  const hasFilters = searchTerm || filterSlow !== "all";
  const hasRequests = requests && requests.length > 0;

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-32">
          <Spin size="large" />
        </div>
      );
    }

    if (hasRequests) {
      return (
        <div>
          {requests.map((request) => (
            <MemoizedRequestCard
              key={request.request_id}
              request={request}
              isSelected={selectedRequestId === request.request_id}
              onClick={onRequestSelect}
            />
          ))}
        </div>
      );
    }

    return (
      <div className="text-center text-gray-500 mt-8">
        {hasFilters ? "No requests match your filters" : "No requests found"}
      </div>
    );
  };

  return (
    <div className="w-1/3 bg-white border-r flex flex-col">
      <div className="px-4 py-3 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <Text strong>Requests</Text>
          {loading && <Spin size="small" />}
        </div>
      </div>
      
      <div ref={ref} className="flex-1 overflow-auto p-4">
        {renderContent()}
      </div>
    </div>
  );
});

RequestListPane.displayName = 'RequestListPane';

export default RequestListPane; 