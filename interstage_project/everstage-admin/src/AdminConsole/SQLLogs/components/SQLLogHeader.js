import React from "react";
import { <PERSON>ton, Typography } from "antd";
import { RefreshIcon } from "@heroicons/react/outline";
import FileSelector from "./FileSelector";

const { Title, Text } = Typography;

/**
 * SQLLogHeader Component
 * 
 * Renders the header section of the SQL Log Analysis page including:
 * - Page title and description
 * - File selector for choosing log files
 * - Refresh button for reloading data
 * 
 * @param {Object} props - Component props
 * @param {Array} props.files - Array of available log files
 * @param {string} props.selectedFile - Currently selected file
 * @param {Function} props.onFileSelect - Callback when a file is selected
 * @param {Function} props.onRefresh - Callback when refresh button is clicked
 * @param {boolean} props.filesLoading - Whether files are being loaded
 * @param {boolean} props.summariesLoading - Whether summaries are being loaded
 * 
 * @example
 * <SQLLogHeader
 *   files={logFiles}
 *   selectedFile={currentFile}
 *   onFileSelect={handleFileSelect}
 *   onRefresh={handleRefresh}
 *   filesLoading={false}
 *   summariesLoading={true}
 * />
 */
const SQLLogHeader = ({
  files,
  selectedFile,
  onFileSelect,
  onRefresh,
  filesLoading,
  summariesLoading
}) => {
  return (
    <div className="bg-white border-b px-6 py-4">
      <div className="flex items-center justify-between mb-4">
        <div>
          <Title level={2} className="mb-1">
            SQL Log Analysis
          </Title>
          <Text type="secondary">
            Analyze database queries per request to understand data flow and also identify performance bottlenecks
          </Text>
        </div>
        
        <div className="flex items-center gap-3">
          <FileSelector
            files={files || []}
            selectedFile={selectedFile}
            onFileSelect={onFileSelect}
            loading={filesLoading}
          />
          
          <Button
            icon={<RefreshIcon className="w-4 h-4" />}
            onClick={onRefresh}
            loading={filesLoading || summariesLoading}
            type="default"
            size="middle"
          >
            Refresh
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SQLLogHeader; 