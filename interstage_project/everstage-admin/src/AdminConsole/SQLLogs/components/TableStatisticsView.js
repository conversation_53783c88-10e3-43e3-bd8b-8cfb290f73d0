import React from "react";
import { Table, Tag, Card, Statistic, Progress } from "antd";
import { DatabaseIcon, ClockIcon, ChartBarIcon } from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

/**
 * TableStatisticsView component for displaying table access patterns
 * Shows table usage statistics and performance metrics
 */
const TableStatisticsView = ({ statistics, loading }) => {
  const columns = [
    {
      title: "Table Name",
      dataIndex: "table_name",
      key: "table_name",
      ellipsis: true,
      sorter: (a, b) => a.table_name.localeCompare(b.table_name),
      render: (tableName) => (
        <div className="flex items-center gap-2">
          <DatabaseIcon className="w-4 h-4 text-blue-500" />
          <span className="font-medium">{tableName}</span>
        </div>
      ),
    },
    {
      title: "Total Queries",
      dataIndex: "total_queries",
      key: "total_queries",
      width: 120,
      align: "center",
      sorter: (a, b) => a.total_queries - b.total_queries,
      render: (count) => (
        <span className="font-mono font-semibold text-lg">{count}</span>
      ),
    },
    {
      title: "Total Time",
      dataIndex: "total_execution_time_ms",
      key: "total_execution_time_ms",
      width: 120,
      sorter: (a, b) => a.total_execution_time_ms - b.total_execution_time_ms,
      render: (timeMs) => {
        const formatted = SQLLogService.formatExecutionTime(timeMs);
        const colorClass = SQLLogService.getPerformanceColor(timeMs);
        return (
          <span className={`font-mono ${colorClass}`}>{formatted}</span>
        );
      },
    },
    {
      title: "Avg Time",
      dataIndex: "avg_execution_time_ms",
      key: "avg_execution_time_ms",
      width: 120,
      sorter: (a, b) => a.avg_execution_time_ms - b.avg_execution_time_ms,
      render: (timeMs) => {
        const formatted = SQLLogService.formatExecutionTime(timeMs);
        const colorClass = SQLLogService.getPerformanceColor(timeMs);
        return (
          <span className={`font-mono ${colorClass}`}>{formatted}</span>
        );
      },
    },
    {
      title: "Query Types",
      dataIndex: "query_types",
      key: "query_types",
      width: 200,
      render: (queryTypes) => (
        <div className="flex flex-wrap gap-1">
          {Object.entries(queryTypes).map(([type, count]) => {
            const color = {
              SELECT: "blue",
              INSERT: "green",
              UPDATE: "orange",
              DELETE: "red",
            }[type] || "default";
            
            return (
              <Tag key={type} color={color} size="small">
                {type}: {count}
              </Tag>
            );
          })}
        </div>
      ),
    },
    {
      title: "Unique Requests",
      dataIndex: "unique_requests",
      key: "unique_requests",
      width: 120,
      align: "center",
      sorter: (a, b) => a.unique_requests - b.unique_requests,
      render: (count) => (
        <span className="font-mono">{count}</span>
      ),
    },
    {
      title: "Usage Pattern",
      key: "usage_pattern",
      width: 150,
      render: (_, record) => {
        const maxQueries = Math.max(...statistics.map(s => s.total_queries));
        const percentage = (record.total_queries / maxQueries) * 100;
        
        return (
          <div>
            <Progress
              percent={percentage}
              size="small"
              showInfo={false}
              strokeColor={percentage > 80 ? '#ff4d4f' : percentage > 50 ? '#faad14' : '#52c41a'}
            />
            <span className="text-xs text-gray-500">
              {percentage.toFixed(1)}% of max
            </span>
          </div>
        );
      },
    },
  ];

  // Calculate summary statistics
  const totalQueries = statistics.reduce((sum, s) => sum + s.total_queries, 0);
  const totalTime = statistics.reduce((sum, s) => sum + s.total_execution_time_ms, 0);
  const avgTimePerTable = statistics.length > 0 ? totalTime / statistics.length : 0;
  const totalUniqueRequests = statistics.reduce((sum, s) => sum + s.unique_requests, 0);

  // Get top performers
  const topByQueries = [...statistics]
    .sort((a, b) => b.total_queries - a.total_queries)
    .slice(0, 5);
  
  const topByTime = [...statistics]
    .sort((a, b) => b.total_execution_time_ms - a.total_execution_time_ms)
    .slice(0, 5);

  return (
    <div className="table-statistics-view">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-4">Table Access Statistics</h3>
        
        <div className="grid grid-cols-4 gap-4 mb-6">
          <Card>
            <Statistic
              title="Tables Accessed"
              value={statistics.length}
              prefix={<DatabaseIcon className="w-4 h-4" />}
            />
          </Card>
          <Card>
            <Statistic
              title="Total Queries"
              value={totalQueries}
              prefix={<ChartBarIcon className="w-4 h-4" />}
            />
          </Card>
          <Card>
            <Statistic
              title="Avg Time/Table"
              value={SQLLogService.formatExecutionTime(avgTimePerTable)}
              prefix={<ClockIcon className="w-4 h-4" />}
            />
          </Card>
          <Card>
            <Statistic
              title="Unique Requests"
              value={totalUniqueRequests}
            />
          </Card>
        </div>

        {/* Top Tables Section */}
        <div className="grid grid-cols-2 gap-6 mb-6">
          <Card title="Top Tables by Query Count" size="small">
            <div className="space-y-2">
              {topByQueries.map((table, idx) => (
                <div key={table.table_name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-500">#{idx + 1}</span>
                    <span className="font-medium">{table.table_name}</span>
                  </div>
                  <span className="font-mono text-sm">{table.total_queries} queries</span>
                </div>
              ))}
            </div>
          </Card>

          <Card title="Top Tables by Execution Time" size="small">
            <div className="space-y-2">
              {topByTime.map((table, idx) => (
                <div key={table.table_name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-500">#{idx + 1}</span>
                    <span className="font-medium">{table.table_name}</span>
                  </div>
                  <span className={`font-mono text-sm ${SQLLogService.getPerformanceColor(table.total_execution_time_ms)}`}>
                    {SQLLogService.formatExecutionTime(table.total_execution_time_ms)}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={statistics}
        loading={loading}
        pagination={{
          pageSize: 50,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} tables`,
        }}
        rowKey="table_name"
        scroll={{ x: 1000 }}
        size="small"
        bordered
      />
    </div>
  );
};

export default TableStatisticsView; 