import React from "react";
import { <PERSON>, Toolt<PERSON>, <PERSON>ge, Spin } from "antd";
import { DocumentIcon, ClockIcon } from "@heroicons/react/outline";
import SQLLogService from "../services/sqlLogService";

/**
 * FileSelector component for selecting SQL log files
 * Shows file metadata and current/historical status
 */
const FileSelector = ({ files, selectedFile, onFileSelect, loading }) => {
  const renderFileOption = (file) => {
    const isCurrentFile = file.is_current;
    const formattedSize = SQLLogService.formatFileSize(file.size_bytes);
    const formattedTime = SQLLogService.formatTimestamp(file.modified_time);
    const estimatedEntries = file.estimated_entries 
      ? `~${file.estimated_entries.toLocaleString()} entries`
      : "";

    return (
      <div className="py-2">
        <div className="flex items-center gap-2 mb-2">
          <DocumentIcon className="w-4 h-4 text-gray-500 flex-shrink-0" />
          <span className="font-medium truncate" title={file.filename}>
            {file.filename}
          </span>
          {isCurrentFile && (
            <Badge 
              status="processing" 
              text="Current" 
              className="text-xs text-blue-600 ml-auto flex-shrink-0"
            />
          )}
        </div>
        
        <div className="text-xs text-gray-500 ml-6 space-y-1">
          <div className="flex items-center gap-3">
            <span>{formattedSize}</span>
            {estimatedEntries && <span>{estimatedEntries}</span>}
          </div>
          <div className="flex items-center gap-1">
            <ClockIcon className="w-3 h-3" />
            <span>{formattedTime}</span>
          </div>
        </div>
      </div>
    );
  };

  const getSimpleFileLabel = (file) => {
    return `${file.filename}${file.is_current ? ' • Current' : ''}`;
  };

  const getFileTooltip = (filename) => {
    const file = files.find(f => f.filename === filename);
    if (!file) return filename;

    const formattedSize = SQLLogService.formatFileSize(file.size_bytes);
    const formattedCreated = SQLLogService.formatTimestamp(file.created_time);
    const formattedModified = SQLLogService.formatTimestamp(file.modified_time);
    const estimatedEntries = file.estimated_entries 
      ? `${file.estimated_entries.toLocaleString()} entries (estimated)`
      : "Unknown entry count";

    return (
      <div className="text-sm">
        <div className="font-medium mb-2">{file.filename}</div>
        <div className="space-y-1">
          <div>Size: {formattedSize}</div>
          <div>Entries: {estimatedEntries}</div>
          <div>Created: {formattedCreated}</div>
          <div>Modified: {formattedModified}</div>
          {file.is_current && (
            <div className="text-blue-600 font-medium">✓ Current active log</div>
          )}
        </div>
      </div>
    );
  };

  const currentFile = files.find(f => f.filename === selectedFile);
  const hasFiles = files && files.length > 0;

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <Spin size="small" />
        <span>Loading log files...</span>
      </div>
    );
  }

  if (!hasFiles) {
    return (
      <div className="text-gray-500 text-sm">
        No log files available
      </div>
    );
  }

  return (
    <div className="file-selector">
      <div className="flex flex-col gap-3">
        <label className="text-sm font-medium text-gray-700">
          Select Log File
        </label>
        
        <Tooltip 
          title={selectedFile ? getFileTooltip(selectedFile) : "Select a log file"}
          placement="bottom"
        >
          <Select
            value={selectedFile}
            onChange={onFileSelect}
            placeholder="Select a log file to analyze"
            className="w-full min-w-80"
            showSearch
            optionLabelProp="label"
            filterOption={(input, option) => {
              const filename = option.label || '';
              return filename.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            dropdownStyle={{ minWidth: '400px' }}
          >
            {files.map((file) => (
              <Select.Option 
                key={file.filename} 
                value={file.filename}
                label={getSimpleFileLabel(file)}
              >
                {renderFileOption(file)}
              </Select.Option>
            ))}
          </Select>
        </Tooltip>

        {currentFile && (
          <div className="text-xs text-gray-600 bg-gray-50 px-3 py-2 rounded border">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <span>
                <strong>Size:</strong> {SQLLogService.formatFileSize(currentFile.size_bytes)}
              </span>
              {currentFile.estimated_entries && (
                <span>
                  <strong>Entries:</strong> ~{currentFile.estimated_entries.toLocaleString()}
                </span>
              )}
              <span>
                <strong>Modified:</strong> {SQLLogService.formatTimestamp(currentFile.modified_time)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileSelector; 