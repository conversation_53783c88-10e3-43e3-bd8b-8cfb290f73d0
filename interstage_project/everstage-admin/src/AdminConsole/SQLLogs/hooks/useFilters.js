import { useReducer, useCallback, useMemo } from "react";

// Filter action types
const FILTER_ACTIONS = {
  SET_TIME_RANGE: "SET_TIME_RANGE",
  SET_EXECUTION_TIME_RANGE: "SET_EXECUTION_TIME_RANGE",
  SET_REQUEST_IDS: "SET_REQUEST_IDS",
  SET_ENDPOINTS: "SET_ENDPOINTS",
  SET_QUERY_TYPES: "SET_QUERY_TYPES",
  SET_TABLES: "SET_TABLES",
  SET_CLIENT_IDS: "SET_CLIENT_IDS",
  SET_LIMIT: "SET_LIMIT",
  SET_OFFSET: "SET_OFFSET",
  RESET_FILTERS: "RESET_FILTERS",
  SET_MULTIPLE: "SET_MULTIPLE",
};

// Initial filter state
const initialFilters = {
  start_time: null,
  end_time: null,
  min_execution_time_ms: null,
  max_execution_time_ms: null,
  request_ids: [],
  endpoints: [],
  query_types: [],
  tables: [],
  client_ids: [],
  limit: 1000,
  offset: 0,
};

// Filter reducer
const filterReducer = (state, action) => {
  switch (action.type) {
    case FILTER_ACTIONS.SET_TIME_RANGE:
      return {
        ...state,
        start_time: action.payload.start_time,
        end_time: action.payload.end_time,
      };
    
    case FILTER_ACTIONS.SET_EXECUTION_TIME_RANGE:
      return {
        ...state,
        min_execution_time_ms: action.payload.min,
        max_execution_time_ms: action.payload.max,
      };
    
    case FILTER_ACTIONS.SET_REQUEST_IDS:
      return {
        ...state,
        request_ids: action.payload,
      };
    
    case FILTER_ACTIONS.SET_ENDPOINTS:
      return {
        ...state,
        endpoints: action.payload,
      };
    
    case FILTER_ACTIONS.SET_QUERY_TYPES:
      return {
        ...state,
        query_types: action.payload,
      };
    
    case FILTER_ACTIONS.SET_TABLES:
      return {
        ...state,
        tables: action.payload,
      };
    
    case FILTER_ACTIONS.SET_CLIENT_IDS:
      return {
        ...state,
        client_ids: action.payload,
      };
    
    case FILTER_ACTIONS.SET_LIMIT:
      return {
        ...state,
        limit: action.payload,
      };
    
    case FILTER_ACTIONS.SET_OFFSET:
      return {
        ...state,
        offset: action.payload,
      };
    
    case FILTER_ACTIONS.SET_MULTIPLE:
      return {
        ...state,
        ...action.payload,
      };
    
    case FILTER_ACTIONS.RESET_FILTERS:
      return {
        ...initialFilters,
        limit: state.limit, // Preserve limit when resetting
      };
    
    default:
      return state;
  }
};

/**
 * Custom hook for managing SQL log analysis filters
 * Provides centralized filter state management with reducer pattern
 */
export const useFilters = () => {
  const [filters, dispatch] = useReducer(filterReducer, initialFilters);

  /**
   * Set time range filter
   */
  const setTimeRange = useCallback((startTime, endTime) => {
    dispatch({
      type: FILTER_ACTIONS.SET_TIME_RANGE,
      payload: {
        start_time: startTime,
        end_time: endTime,
      },
    });
  }, []);

  /**
   * Set execution time range filter
   */
  const setExecutionTimeRange = useCallback((min, max) => {
    dispatch({
      type: FILTER_ACTIONS.SET_EXECUTION_TIME_RANGE,
      payload: { min, max },
    });
  }, []);

  /**
   * Set request IDs filter
   */
  const setRequestIds = useCallback((requestIds) => {
    dispatch({
      type: FILTER_ACTIONS.SET_REQUEST_IDS,
      payload: Array.isArray(requestIds) ? requestIds : [requestIds],
    });
  }, []);

  /**
   * Set endpoints filter
   */
  const setEndpoints = useCallback((endpoints) => {
    dispatch({
      type: FILTER_ACTIONS.SET_ENDPOINTS,
      payload: Array.isArray(endpoints) ? endpoints : [endpoints],
    });
  }, []);

  /**
   * Set query types filter
   */
  const setQueryTypes = useCallback((queryTypes) => {
    dispatch({
      type: FILTER_ACTIONS.SET_QUERY_TYPES,
      payload: Array.isArray(queryTypes) ? queryTypes : [queryTypes],
    });
  }, []);

  /**
   * Set tables filter
   */
  const setTables = useCallback((tables) => {
    dispatch({
      type: FILTER_ACTIONS.SET_TABLES,
      payload: Array.isArray(tables) ? tables : [tables],
    });
  }, []);

  /**
   * Set client IDs filter
   */
  const setClientIds = useCallback((clientIds) => {
    dispatch({
      type: FILTER_ACTIONS.SET_CLIENT_IDS,
      payload: Array.isArray(clientIds) ? clientIds : [clientIds],
    });
  }, []);

  /**
   * Set pagination limit
   */
  const setLimit = useCallback((limit) => {
    dispatch({
      type: FILTER_ACTIONS.SET_LIMIT,
      payload: limit,
    });
  }, []);

  /**
   * Set pagination offset
   */
  const setOffset = useCallback((offset) => {
    dispatch({
      type: FILTER_ACTIONS.SET_OFFSET,
      payload: offset,
    });
  }, []);

  /**
   * Update multiple filters at once
   */
  const updateFilters = useCallback((filterUpdates) => {
    dispatch({
      type: FILTER_ACTIONS.SET_MULTIPLE,
      payload: filterUpdates,
    });
  }, []);

  /**
   * Reset all filters to initial state
   */
  const resetFilters = useCallback(() => {
    dispatch({ type: FILTER_ACTIONS.RESET_FILTERS });
  }, []);

  /**
   * Check if any filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return (
      filters.start_time !== null ||
      filters.end_time !== null ||
      filters.min_execution_time_ms !== null ||
      filters.max_execution_time_ms !== null ||
      filters.request_ids.length > 0 ||
      filters.endpoints.length > 0 ||
      filters.query_types.length > 0 ||
      filters.tables.length > 0 ||
      filters.client_ids.length > 0
    );
  }, [filters]);

  /**
   * Get clean filters object (removing null/empty values)
   */
  const getCleanFilters = useCallback(() => {
    const cleanFilters = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value) && value.length > 0) {
          cleanFilters[key] = value;
        } else if (!Array.isArray(value)) {
          cleanFilters[key] = value;
        }
      }
    });

    return cleanFilters;
  }, [filters]);

  /**
   * Get analysis request object with filters
   */
  const getAnalysisRequest = useCallback((filename) => {
    const cleanFilters = getCleanFilters();
    
    return {
      filename,
      filters: {
        start_time: cleanFilters.start_time,
        end_time: cleanFilters.end_time,
        min_execution_time_ms: cleanFilters.min_execution_time_ms,
        max_execution_time_ms: cleanFilters.max_execution_time_ms,
        request_ids: cleanFilters.request_ids,
        endpoints: cleanFilters.endpoints,
        query_types: cleanFilters.query_types,
        tables: cleanFilters.tables,
        client_ids: cleanFilters.client_ids,
      },
      include_request_summaries: true,
      include_table_statistics: true,
      include_slow_queries: true,
      slow_query_threshold_ms: 1000,
      limit: cleanFilters.limit,
      offset: cleanFilters.offset,
    };
  }, [getCleanFilters]);

  /**
   * Get request summary request object
   */
  const getRequestSummaryRequest = useCallback((filename) => {
    const cleanFilters = getCleanFilters();
    
    return {
      filename,
      filters: {
        start_time: cleanFilters.start_time,
        end_time: cleanFilters.end_time,
        min_execution_time_ms: cleanFilters.min_execution_time_ms,
        max_execution_time_ms: cleanFilters.max_execution_time_ms,
        request_ids: cleanFilters.request_ids,
        endpoints: cleanFilters.endpoints,
        query_types: cleanFilters.query_types,
        tables: cleanFilters.tables,
        client_ids: cleanFilters.client_ids,
      },
      limit: 100,
      offset: cleanFilters.offset,
    };
  }, [getCleanFilters]);

  /**
   * Get filter summary for display
   */
  const getFilterSummary = useMemo(() => {
    const summary = [];
    
    if (filters.start_time || filters.end_time) {
      summary.push("Time Range");
    }
    if (filters.min_execution_time_ms !== null || filters.max_execution_time_ms !== null) {
      summary.push("Execution Time");
    }
    if (filters.request_ids.length > 0) {
      summary.push(`${filters.request_ids.length} Request IDs`);
    }
    if (filters.endpoints.length > 0) {
      summary.push(`${filters.endpoints.length} Endpoints`);
    }
    if (filters.query_types.length > 0) {
      summary.push(`${filters.query_types.length} Query Types`);
    }
    if (filters.tables.length > 0) {
      summary.push(`${filters.tables.length} Tables`);
    }
    if (filters.client_ids.length > 0) {
      summary.push(`${filters.client_ids.length} Client IDs`);
    }

    return summary;
  }, [filters]);

  return {
    filters,
    setTimeRange,
    setExecutionTimeRange,
    setRequestIds,
    setEndpoints,
    setQueryTypes,
    setTables,
    setClientIds,
    setLimit,
    setOffset,
    updateFilters,
    resetFilters,
    hasActiveFilters,
    getCleanFilters,
    getAnalysisRequest,
    getRequestSummaryRequest,
    getFilterSummary,
  };
}; 