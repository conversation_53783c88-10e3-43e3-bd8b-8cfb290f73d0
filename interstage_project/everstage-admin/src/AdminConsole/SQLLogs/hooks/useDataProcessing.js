import { useState, useCallback, useMemo, useEffect } from 'react';

/**
 * Custom hook for managing request filtering and sorting
 * 
 * Responsibilities:
 * - Manages search, sort, and filter state
 * - Provides filtered and sorted request data
 * - Handles all filter and sort change events
 * - Manages URL state updates for filter preferences
 * 
 * @param {Object} requestSummaries - Raw request summaries data
 * @param {Object} urlState - URL state management functions
 * @param {Function} saveScrollPosition - Function to save current scroll position
 * @returns {Object} Filter/sort state and handlers
 */
export const useFilterAndSort = (requestSummaries, urlState, saveScrollPosition) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("start_time");
  const [filterSlow, setFilterSlow] = useState("all");

  const { setMultipleParams, deleteParam, setParam } = urlState;

  /**
   * Apply filtering and sorting to request summaries
   */
  const filteredAndSortedRequests = useMemo(() => {
    if (!requestSummaries?.summaries) return [];

    let filtered = requestSummaries.summaries;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.endpoint.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.request_id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply slow query filter
    if (filterSlow === "slow") {
      filtered = filtered.filter(request => request.slow_queries_count > 0);
    } else if (filterSlow === "fast") {
      filtered = filtered.filter(request => request.slow_queries_count === 0);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "start_time":
          return new Date(b.start_time).getTime() - new Date(a.start_time).getTime();
        case "total_queries":
          return b.total_queries - a.total_queries;
        case "total_execution_time_ms":
          return b.total_execution_time_ms - a.total_execution_time_ms;
        case "slow_queries_count":
          return b.slow_queries_count - a.slow_queries_count;
        default:
          return 0;
      }
    });

    return filtered;
  }, [requestSummaries?.summaries, searchTerm, filterSlow, sortBy]);

  /**
   * Handle search term changes
   * 
   * @param {Event} e - Input change event
   */
  const handleSearchChange = useCallback((e) => {
    const scrollPosition = saveScrollPosition();
    const value = e.target.value;
    setSearchTerm(value);
    
    // Update URL to include search term
    setMultipleParams({
      search: value || undefined,
      scroll: scrollPosition.toString()
    }, { replace: true });
  }, [setMultipleParams, saveScrollPosition]);

  /**
   * Handle search clear
   */
  const handleSearchClear = useCallback(() => {
    const scrollPosition = saveScrollPosition();
    setSearchTerm("");
    
    // Update URL to remove search term
    deleteParam('search', { replace: true });
    setParam('scroll', scrollPosition.toString(), { replace: true });
  }, [deleteParam, setParam, saveScrollPosition]);

  /**
   * Handle sort change
   * 
   * @param {string} value - Sort field value
   */
  const handleSortChange = useCallback((value) => {
    const scrollPosition = saveScrollPosition();
    setSortBy(value);
    
    // Update URL to include sort preference
    setMultipleParams({
      sort: value,
      scroll: scrollPosition.toString()
    }, { replace: true });
  }, [setMultipleParams, saveScrollPosition]);

  /**
   * Handle filter change
   * 
   * @param {string} value - Filter value
   */
  const handleFilterChange = useCallback((value) => {
    const scrollPosition = saveScrollPosition();
    setFilterSlow(value);
    
    // Update URL to include filter preference
    setMultipleParams({
      filter: value !== 'all' ? value : undefined,
      scroll: scrollPosition.toString()
    }, { replace: true });
  }, [setMultipleParams, saveScrollPosition]);

  /**
   * Initialize filter state from URL parameters
   * 
   * @param {Object} urlParams - URL parameters
   */
  const initializeFromURL = useCallback((urlParams) => {
    const { search, sort, filter } = urlParams;
    
    if (search) {
      setSearchTerm(search);
    }
    if (sort) {
      setSortBy(sort);
    }
    if (filter) {
      setFilterSlow(filter);
    }
  }, []);

  return {
    // State
    searchTerm,
    sortBy,
    filterSlow,
    filteredAndSortedRequests,
    
    // Handlers
    handleSearchChange,
    handleSearchClear,
    handleSortChange,
    handleFilterChange,
    
    // Utilities
    initializeFromURL,
    
    // Setters for direct state updates
    setSearchTerm,
    setSortBy,
    setFilterSlow
  };
};

/**
 * Custom hook for managing request selection and query loading
 * 
 * Responsibilities:
 * - Manages selected request state
 * - Handles request queries state and loading
 * - Provides request selection handlers
 * - Manages query loading for selected requests
 * 
 * @param {string} selectedFile - Currently selected file
 * @param {Function} getQueriesForRequest - Function to fetch queries for a request
 * @param {Object} urlState - URL state management functions
 * @param {Function} saveScrollPosition - Function to save current scroll position
 * @returns {Object} Request selection state and handlers
 */
export const useRequestSelection = (selectedFile, getQueriesForRequest, urlState, saveScrollPosition) => {
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [requestQueries, setRequestQueries] = useState([]);
  const [queriesLoading, setQueriesLoading] = useState(false);

  const { setMultipleParams } = urlState;

  /**
   * Load queries for a specific request
   * 
   * @param {Object} request - The request object to load queries for
   */
  const loadQueriesForRequest = useCallback(async (request) => {
    if (!selectedFile || !request) return;

    setQueriesLoading(true);
    try {
      const response = await getQueriesForRequest(selectedFile, request.request_id);
      setRequestQueries(response.queries || []);
    } catch (error) {
      console.error("Error loading queries for request:", error);
      setRequestQueries([]);
    } finally {
      setQueriesLoading(false);
    }
  }, [selectedFile, getQueriesForRequest]);

  /**
   * Handle request selection
   * 
   * @param {Object} request - Selected request object
   */
  const handleRequestSelect = useCallback((request) => {
    // Save current scroll position before state change
    const scrollPosition = saveScrollPosition();
    
    setSelectedRequest(request);
    
    // Update URL with selected request and scroll position
    setMultipleParams({
      file: selectedFile,
      request: request.request_id,
      scroll: scrollPosition.toString()
    }, { replace: true });
  }, [selectedFile, setMultipleParams, saveScrollPosition]);

  /**
   * Clear selected request
   */
  const clearSelectedRequest = useCallback(() => {
    setSelectedRequest(null);
    setRequestQueries([]);
  }, []);

  // Load queries when request is selected
  useEffect(() => {
    if (selectedRequest && selectedFile) {
      loadQueriesForRequest(selectedRequest);
    }
  }, [selectedRequest, selectedFile, loadQueriesForRequest]);

  return {
    selectedRequest,
    requestQueries,
    queriesLoading,
    handleRequestSelect,
    loadQueriesForRequest,
    clearSelectedRequest,
    setSelectedRequest
  };
}; 