/**
 * SQL Logs Hooks Index
 * 
 * Central export file for all SQL logs custom hooks.
 * This file provides a clean API for importing hooks and maintains
 * a clear overview of the available hook library.
 * 
 * Usage:
 * import { useURLState, useSQLLogFiles } from './hooks';
 * 
 * Or import specific hooks:
 * import { useURLState } from './hooks/useURLState';
 */

// State Management Hooks
export { useURLState } from './useURLState';
export { useFilters } from './useFilters';

// Data Management Hooks
export { useSQLLogFiles } from './useSQLLogFiles';
export { useSQLLogAnalysis } from './useSQLLogAnalysis';

// Consolidated UI State Management Hooks
export { useURLStateRestoration, useScrollPosition } from './useStateManagement';
export { useFilterAndSort, useRequestSelection } from './useDataProcessing';

/**
 * Hook Categories for Reference:
 * 
 * STATE MANAGEMENT HOOKS:
 * - useURLState: URL search parameter management
 * - useFilters: Advanced filtering state and logic
 * 
 * DATA MANAGEMENT HOOKS:
 * - useSQLLogFiles: SQL log file data fetching and management
 * - useSQLLogAnalysis: SQL analysis operations and request data
 * 
 * CONSOLIDATED UI STATE MANAGEMENT HOOKS:
 * 
 * From useStateManagement.js:
 * - useURLStateRestoration: URL parameter restoration and file selection
 * - useScrollPosition: Scroll position preservation and restoration
 * 
 * From useDataProcessing.js:
 * - useFilterAndSort: Request filtering and sorting logic
 * - useRequestSelection: Request selection and query loading
 * 
 * Hook Design Principles:
 * 1. Single Responsibility: Each hook handles one specific concern
 * 2. Reusability: Hooks can be used across multiple components
 * 3. Testability: Pure functions with predictable inputs/outputs
 * 4. Performance: Optimized with proper memoization
 * 5. Error Handling: Comprehensive error states and recovery
 * 
 * File Consolidation Benefits:
 * - Reduced from 9 individual files to 3 consolidated files
 * - Related hooks are grouped by purpose
 * - Easier to maintain and navigate
 * - Maintains modular architecture without file sprawl
 * 
 * Refactoring Benefits:
 * - Reduced component complexity from 443 lines to ~100 lines
 * - Better separation of concerns
 * - Improved testability and maintainability
 * - Enhanced reusability across components
 */ 