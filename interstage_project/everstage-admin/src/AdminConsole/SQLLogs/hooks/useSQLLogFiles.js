import { useState, useEffect, useCallback } from "react";
import { useAuthStore } from "GlobalStores/AuthStore";
import SQLLogService from "../services/sqlLogService";

/**
 * Custom hook for managing SQL log files
 * Provides file listing, selection, and metadata operations
 */
export const useSQLLogFiles = () => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const authStore = useAuthStore();
  const { accessToken } = authStore;

  /**
   * Fetch available log files from the server
   */
  const fetchFiles = useCallback(async () => {
    if (!accessToken) {
      setError("No access token available");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await SQLLogService.listFiles(accessToken);
      const logFiles = response.log_files || [];
      
      // Sort files by modified time (newest first)
      const sortedFiles = logFiles.sort((a, b) => 
        new Date(b.modified_time) - new Date(a.modified_time)
      );
      
      setFiles(sortedFiles);
    } catch (err) {
      console.error("Error fetching SQL log files:", err);
      setError(err.message || "Failed to fetch log files");
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  /**
   * Refresh files list
   */
  const refetchFiles = useCallback(() => {
    fetchFiles();
  }, [fetchFiles]);

  /**
   * Get file by filename
   */
  const getFile = useCallback((filename) => {
    return files.find(file => file.filename === filename);
  }, [files]);

  /**
   * Get current active log file
   */
  const getCurrentFile = useCallback(() => {
    return files.find(file => file.is_current);
  }, [files]);

  /**
   * Get file statistics
   */
  const getFilesStats = useCallback(() => {
    return {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size_bytes, 0),
      currentFiles: files.filter(file => file.is_current).length,
      averageSize: files.length > 0 
        ? files.reduce((sum, file) => sum + file.size_bytes, 0) / files.length 
        : 0,
    };
  }, [files]);

  // Fetch files on mount and when access token changes
  useEffect(() => {
    if (accessToken) {
      fetchFiles();
    }
  }, [accessToken, fetchFiles]);

  return {
    files,
    loading,
    error,
    refetchFiles,
    getFile,
    getCurrentFile,
    getFilesStats,
  };
}; 