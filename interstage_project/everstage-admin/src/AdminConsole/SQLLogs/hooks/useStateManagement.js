import { useState, useEffect, useRef, useLayoutEffect, useCallback } from 'react';

/**
 * Custom hook for managing scroll position preservation and restoration
 * 
 * Responsibilities:
 * - Manages scroll container ref and saved position
 * - Provides functions to save and restore scroll position
 * - Handles scroll restoration timing with multiple strategies
 * - Maintains scroll position across state changes
 * 
 * @param {Array} dependencies - Dependencies that should trigger scroll restoration
 * @returns {Object} Scroll management utilities
 */
export const useScrollPosition = (dependencies = []) => {
  const scrollContainerRef = useRef(null);
  const savedScrollPosition = useRef(0);

  /**
   * Save current scroll position
   * 
   * @returns {number} Current scroll position
   */
  const saveScrollPosition = () => {
    if (scrollContainerRef.current) {
      savedScrollPosition.current = scrollContainerRef.current.scrollTop;
    }
    return savedScrollPosition.current;
  };

  /**
   * Restore saved scroll position
   * 
   * @param {number} position - Optional specific position to restore
   */
  const restoreScrollPosition = (position = null) => {
    const targetPosition = position !== null ? position : savedScrollPosition.current;
    
    if (scrollContainerRef.current && targetPosition > 0) {
      const restoreScroll = () => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop = targetPosition;
        }
      };

      // Try immediate restoration
      restoreScroll();
      
      // Also try after a short delay for DOM updates
      const timeoutId = setTimeout(restoreScroll, 10);
      
      // And use requestAnimationFrame for next paint
      const rafId = requestAnimationFrame(restoreScroll);
      
      return () => {
        clearTimeout(timeoutId);
        cancelAnimationFrame(rafId);
      };
    }
  };

  /**
   * Reset scroll position to top
   */
  const resetScrollPosition = () => {
    savedScrollPosition.current = 0;
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
  };

  /**
   * Set scroll position from URL parameter
   * 
   * @param {string} scrollFromURL - Scroll position from URL
   */
  const setScrollFromURL = (scrollFromURL) => {
    if (scrollFromURL) {
      savedScrollPosition.current = parseInt(scrollFromURL, 10);
    }
  };

  // Handle scroll restoration with improved timing
  useLayoutEffect(() => {
    const cleanup = restoreScrollPosition();
    return cleanup;
  }, dependencies);

  return {
    scrollContainerRef,
    savedScrollPosition,
    saveScrollPosition,
    restoreScrollPosition,
    resetScrollPosition,
    setScrollFromURL
  };
};

/**
 * Custom hook for managing URL state restoration and file selection
 * 
 * Responsibilities:
 * - Manages file selection state
 * - Handles URL parameter restoration
 * - Manages restoration flow coordination
 * - Provides file selection handlers
 * 
 * @param {Array} files - Available log files
 * @param {Object} requestSummaries - Request summaries data
 * @param {Object} urlState - URL state management functions
 * @param {Function} getRequestSummaries - Function to fetch request summaries
 * @param {Function} setScrollFromURL - Function to set scroll position from URL
 * @returns {Object} URL restoration state and handlers
 */
export const useURLStateRestoration = (
  files, 
  requestSummaries, 
  urlState, 
  getRequestSummaries,
  setScrollFromURL
) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const isRestoringFromURL = useRef(false);

  const { getParam, setParam, deleteParam } = urlState;

  /**
   * Handle file selection
   * 
   * @param {string} filename - Selected filename
   */
  const handleFileSelect = useCallback((filename) => {
    if (filename !== selectedFile) {
      setSelectedFile(filename);
      
      // Update URL and clear request selection when file changes
      setParam('file', filename, { replace: true });
      deleteParam('request', { replace: true });
      deleteParam('scroll', { replace: true });
      
      // Reset restoration flag when manually selecting a file
      isRestoringFromURL.current = false;
    }
  }, [selectedFile, setParam, deleteParam]);

  /**
   * Handle refresh action
   */
  const handleRefresh = useCallback((refetchFiles) => {
    refetchFiles();
    if (selectedFile) {
      const request = {
        filename: selectedFile,
        filters: {},
        limit_value: 1000,
        offset_value: 0,
      };
      getRequestSummaries(request);
    }
  }, [selectedFile, getRequestSummaries]);

  // Initialize from URL parameters and handle file selection
  useEffect(() => {
    const fileFromURL = getParam('file');
    const requestFromURL = getParam('request');
    const scrollFromURL = getParam('scroll');

    // Handle file selection from URL or auto-select
    if (fileFromURL && files && files.length > 0) {
      const file = files.find(f => f.filename === fileFromURL);
      if (file && file.filename !== selectedFile) {
        setSelectedFile(file.filename);
      }
    } else if (files && files.length > 0 && !selectedFile) {
      // Auto-select the current log file when files are loaded, but only if no file is selected
      const currentFile = files.find(file => file.is_current) || files[0];
      setSelectedFile(currentFile.filename);
    }

    // Handle scroll position restoration
    if (scrollFromURL) {
      setScrollFromURL(scrollFromURL);
    }

    // Mark that we're restoring from URL to handle scroll differently
    if (requestFromURL || scrollFromURL) {
      isRestoringFromURL.current = true;
    }
  }, [files]); // Simplified dependencies to prevent infinite loops

  // Load request summaries when file changes
  useEffect(() => {
    if (selectedFile) {
      const request = {
        filename: selectedFile,
        filters: {},
        limitValue: 1000,
        offsetValue: 0,
      };
      getRequestSummaries(request);
    }
  }, [selectedFile]); // Remove getRequestSummaries to prevent infinite loops

  /**
   * Get URL parameters for restoration
   * 
   * @returns {Object} URL parameters
   */
  const getURLParams = () => {
    return {
      file: getParam('file'),
      request: getParam('request'),
      scroll: getParam('scroll'),
      search: getParam('search'),
      sort: getParam('sort'),
      filter: getParam('filter')
    };
  };

  /**
   * Check if currently restoring from URL
   * 
   * @returns {boolean} Whether restoration is in progress
   */
  const isRestoring = () => {
    return isRestoringFromURL.current;
  };

  /**
   * Complete URL restoration process
   */
  const completeRestoration = () => {
    isRestoringFromURL.current = false;
  };

  /**
   * Find and return request from URL if available
   * 
   * @returns {Object|null} Request object or null
   */
  const getRequestFromURL = () => {
    const requestFromURL = getParam('request');
    
    if (requestFromURL && requestSummaries?.summaries && isRestoringFromURL.current) {
      return requestSummaries.summaries.find(
        r => r.request_id === requestFromURL
      );
    }
    
    return null;
  };

  return {
    // State
    selectedFile,
    isRestoringFromURL,
    
    // Handlers
    handleFileSelect,
    handleRefresh,
    
    // Utilities
    getURLParams,
    isRestoring,
    completeRestoration,
    getRequestFromURL,
    
    // Setters
    setSelectedFile
  };
}; 