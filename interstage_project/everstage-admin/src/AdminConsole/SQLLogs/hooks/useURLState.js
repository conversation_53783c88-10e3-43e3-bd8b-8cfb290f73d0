import { useCallback } from "react";
import { useLocation, useHistory } from "react-router-dom";

/**
 * useURLState Hook
 * 
 * Custom hook for managing URL search parameters in React Router v5.
 * Provides utilities for reading and updating URL search parameters
 * while maintaining browser history correctly.
 * 
 * @returns {Object} Object containing URL state management functions
 * @returns {Function} getSearchParams - Function to get current URLSearchParams
 * @returns {Function} setSearchParams - Function to update URL search parameters
 * @returns {Function} getParam - Function to get a specific parameter value
 * @returns {Function} setParam - Function to set a specific parameter
 * @returns {Function} deleteParam - Function to delete a specific parameter
 * 
 * @example
 * const { getParam, setParam, deleteParam } = useURLState();
 * 
 * // Get a parameter
 * const fileParam = getParam('file');
 * 
 * // Set a parameter
 * setParam('file', 'log.txt');
 * 
 * // Delete a parameter
 * deleteParam('search');
 */
export const useURLState = () => {
  const location = useLocation();
  const history = useHistory();

  /**
   * Get current URLSearchParams object
   * 
   * @returns {URLSearchParams} Current search parameters
   */
  const getSearchParams = useCallback(() => {
    return new URLSearchParams(location.search);
  }, [location.search]);

  /**
   * Update URL search parameters
   * 
   * @param {URLSearchParams} newParams - New search parameters to set
   * @param {Object} options - Options for navigation
   * @param {boolean} options.replace - Whether to replace current history entry
   */
  const setSearchParams = useCallback((newParams, options = {}) => {
    const newSearch = `?${newParams.toString()}`;
    if (options.replace) {
      history.replace({ ...location, search: newSearch });
    } else {
      history.push({ ...location, search: newSearch });
    }
  }, [history, location]);

  /**
   * Get a specific parameter value
   * 
   * @param {string} key - Parameter key to get
   * @returns {string|null} Parameter value or null if not found
   */
  const getParam = useCallback((key) => {
    const searchParams = getSearchParams();
    return searchParams.get(key);
  }, [getSearchParams]);

  /**
   * Set a specific parameter
   * 
   * @param {string} key - Parameter key to set
   * @param {string} value - Parameter value to set
   * @param {Object} options - Options for navigation
   * @param {boolean} options.replace - Whether to replace current history entry
   */
  const setParam = useCallback((key, value, options = {}) => {
    const searchParams = getSearchParams();
    const newParams = new URLSearchParams(searchParams);
    
    if (value === null || value === undefined || value === '') {
      newParams.delete(key);
    } else {
      newParams.set(key, value);
    }
    
    setSearchParams(newParams, options);
  }, [getSearchParams, setSearchParams]);

  /**
   * Delete a specific parameter
   * 
   * @param {string} key - Parameter key to delete
   * @param {Object} options - Options for navigation
   * @param {boolean} options.replace - Whether to replace current history entry
   */
  const deleteParam = useCallback((key, options = {}) => {
    const searchParams = getSearchParams();
    const newParams = new URLSearchParams(searchParams);
    newParams.delete(key);
    setSearchParams(newParams, options);
  }, [getSearchParams, setSearchParams]);

  /**
   * Set multiple parameters at once
   * 
   * @param {Object} params - Object with key-value pairs to set
   * @param {Object} options - Options for navigation
   * @param {boolean} options.replace - Whether to replace current history entry
   * @param {boolean} options.merge - Whether to merge with existing params or replace all
   */
  const setMultipleParams = useCallback((params, options = {}) => {
    const { merge = true, ...navOptions } = options;
    const searchParams = merge ? getSearchParams() : new URLSearchParams();
    const newParams = new URLSearchParams(searchParams);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });
    
    setSearchParams(newParams, navOptions);
  }, [getSearchParams, setSearchParams]);

  /**
   * Clear all search parameters
   * 
   * @param {Object} options - Options for navigation
   * @param {boolean} options.replace - Whether to replace current history entry
   */
  const clearParams = useCallback((options = {}) => {
    setSearchParams(new URLSearchParams(), options);
  }, [setSearchParams]);

  return {
    getSearchParams,
    setSearchParams,
    getParam,
    setParam,
    deleteParam,
    setMultipleParams,
    clearParams
  };
}; 