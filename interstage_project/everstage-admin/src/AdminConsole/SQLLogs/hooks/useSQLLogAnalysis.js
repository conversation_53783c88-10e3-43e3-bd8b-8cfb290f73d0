import { useState, useCallback } from "react";
import { useAuthStore } from "GlobalStores/AuthStore";
import SQLLogService from "../services/sqlLogService";

/**
 * Custom hook for managing SQL log analysis operations
 * <PERSON>les request summaries and query retrieval
 */
export const useSQLLogAnalysis = () => {
  const [requestSummaries, setRequestSummaries] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const authStore = useAuthStore();
  const { accessToken } = authStore;

  /**
   * Get request summaries for a SQL log file
   */
  const getRequestSummaries = useCallback(async (summaryRequest) => {
    if (!accessToken) {
      setError("No access token available");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await SQLLogService.getRequestSummaries(accessToken, summaryRequest);
      setRequestSummaries(response);
      return response;
    } catch (err) {
      console.error("Error getting request summaries:", err);
      setError(err.message || "Failed to get request summaries");
      throw err;
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  /**
   * Get queries for a specific request ID
   */
  const getQueriesForRequest = useCallback(async (filename, requestId) => {
    if (!accessToken) {
      setError("No access token available");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await SQLLogService.getQueriesForRequest(accessToken, filename, requestId);
      return response;
    } catch (err) {
      console.error("Error getting queries for request:", err);
      setError(err.message || "Failed to get queries for request");
      throw err;
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  return {
    requestSummaries,
    loading,
    error,
    getRequestSummaries,
    getQueriesForRequest,
  };
}; 