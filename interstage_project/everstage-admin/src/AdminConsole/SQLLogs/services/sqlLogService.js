/**
 * API service for SQL log analysis operations
 */

const BASE_URL = "/everstage_admin/sql-logs";

class SQLLogService {
  /**
   * Construct standard headers for API requests
   */
  static getHeaders(accessToken) {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    };
  }

  /**
   * Handle API response and errors
   */
  static async handleResponse(response) {
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      const errorMessage = errorData?.error || errorData?.details || `HTTP ${response.status}: ${response.statusText}`;
      throw new Error(errorMessage);
    }
    return response.json();
  }

  /**
   * List available SQL log files
   * @param {string} accessToken - Authentication token
   * @returns {Promise<{log_files: LogFileInfo[]}>}
   */
  static async listFiles(accessToken) {
    try {
      const response = await fetch(`${BASE_URL}/files/`, {
        method: "GET",
        headers: this.getHeaders(accessToken),
      });
      
      return this.handleResponse(response);
    } catch (error) {
      console.error("Error listing SQL log files:", error);
      throw error;
    }
  }

  /**
   * Get request-level summaries from a SQL log file
   * @param {string} accessToken - Authentication token
   * @param {RequestSummaryRequest} request - Request summary parameters
   * @returns {Promise<RequestSummaryResponse>}
   */
  static async getRequestSummaries(accessToken, request) {
    try {
      const response = await fetch(`${BASE_URL}/request-summaries/`, {
        method: "POST",
        headers: this.getHeaders(accessToken),
        body: JSON.stringify(request),
      });
      
      return this.handleResponse(response);
    } catch (error) {
      console.error("Error getting request summaries:", error);
      throw error;
    }
  }

  /**
   * Get queries for a specific request ID
   * @param {string} accessToken - Authentication token
   * @param {string} filename - Log file name
   * @param {string} requestId - Request ID to get queries for
   * @returns {Promise<{queries: QueryEntry[]}>}
   */
  static async getQueriesForRequest(accessToken, filename, requestId) {
    try {
      const response = await fetch(`${BASE_URL}/request-queries/`, {
        method: "POST",
        headers: this.getHeaders(accessToken),
        body: JSON.stringify({
          filename,
          request_id: requestId,
        }),
      });
      
      return this.handleResponse(response);
    } catch (error) {
      console.error("Error getting queries for request:", error);
      throw error;
    }
  }

  /**
   * Format file size in human readable format
   * @param {number} bytes - Size in bytes
   * @returns {string} Formatted size
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Format timestamp for display in IST timezone
   * @param {string} timestamp - ISO timestamp
   * @returns {string} Formatted timestamp in IST
   */
  static formatTimestamp(timestamp) {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('en-IN', { 
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }) + ' IST';
    } catch (error) {
      return timestamp;
    }
  }

  /**
   * Format execution time for display
   * @param {number} timeMs - Time in milliseconds
   * @returns {string} Formatted time
   */
  static formatExecutionTime(timeMs) {
    if (timeMs < 1) {
      return `${(timeMs * 1000).toFixed(2)}μs`;
    } else if (timeMs < 1000) {
      return `${timeMs.toFixed(2)}ms`;
    } else {
      return `${(timeMs / 1000).toFixed(2)}s`;
    }
  }

  /**
   * Get performance color based on execution time
   * @param {number} timeMs - Time in milliseconds
   * @returns {string} CSS color class
   */
  static getPerformanceColor(timeMs) {
    if (timeMs < 100) return "text-green-600";
    if (timeMs < 500) return "text-yellow-600";
    if (timeMs < 1000) return "text-orange-600";
    return "text-red-600";
  }

  /**
   * Truncate long text for display
   * @param {string} text - Text to truncate
   * @param {number} maxLength - Maximum length
   * @returns {string} Truncated text
   */
  static truncateText(text, maxLength = 100) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  }
}

export default SQLLogService; 