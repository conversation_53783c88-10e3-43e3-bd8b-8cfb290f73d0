import React from "react";
import { makeAutoObservable } from "mobx";
import { useLocalStore } from "mobx-react";
import { getRedirectUrl } from "Utils";

const ROLE_NAMESPACE = "https://everstage.com/roles";
const redirectUrl = getRedirectUrl();

export const SCREENS = {
  DASHBOARD: "DASHBOARD",
  CUSTOMERS: "CUSTOMERS",
  ETL_STATUS: "ETL_STATUS",
  DAILY_SYNC: "DAILY_SYNC",
  CONFIGURE: "CONFIGURE",
  DEPLOYMENT: "DEPLOYMENT",
  GENIE: "GENIE",
  AGENT_STUDIO: "AGENT_STUDIO",
  AGENT_DEPLOYMENT: "AGENT_DEPLOYMENT",
  AUDIT_LOGS: "AUDIT_LOGS",
  TSAR: "TSAR",
  QA_REPORTS: "QA_REPORTS",
  CUSTOM_THEME: "CUSTOM_THEME",
  FEATURE_FLAGS: "FEATURE_FLAGS",
  ENGINE_TOOLS: "ENGINE_TOOLS",
  SQL_LOGS: "SQL_LOGS",
  BULK_PERMISSION_MANAGEMENT: "BULK_PERMISSION_MANAGEMENT",
};

export const L1_OPS_SCREENS = [SCREENS.ETL_STATUS, SCREENS.CUSTOMERS];

export const ROLES = {
  EVERSTAGE_ADMIN_ROLE: "EverstageAdmin",
  EVERSTAGE_OPS_ADMIN_ROLE: "EverstageOpsAdmin",
  GENIE_USER_ROLE: "GenieUser",
  AGENT_STUDIO_ADMIN: "AgentStudioAdmin",
  AGENT_DEPLOYMENT_ADMIN: "AgentDeploymentAdmin",
  QA_REPORTS_ADMIN: "QAReportsAdmin",
  L1_OPS: "L1Ops",
  BULK_ROLES_MANAGEMENT: "BulkRolesManage",
};

class AuthStore {
  constructor() {
    makeAutoObservable(this);
  }

  auth0;
  accessToken;
  user = {};
  idTokenClaims = {};
  loading = { status: false };
  sessionManagementOK = false;
  isTsarAccessible = false;
  permissions = {
    isAuth0UsersManageable: false,
    isEtlStatusManageable: false,
    isIntegrationsManageable: false,
    isAdminUiManageable: false,
    isCustomersManageable: false,
  };

  setIsTsarAccessible(value) {
    this.isTsarAccessible = value ?? false;
  }

  setPermissions(permissions) {
    this.permissions = {
      isAuth0UsersManageable: permissions?.isAuth0UsersManageable ?? false,
      isEtlStatusManageable: permissions?.isEtlStatusManageable ?? false,
      isIntegrationsManageable: permissions?.isIntegrationsManageable ?? false,
      isAdminUiManageable: permissions?.isAdminUiManageable ?? false,
      isCustomersManageable: permissions?.isCustomersManageable ?? false,
    };
  }

  setAuth0(auth0) {
    this.auth0 = auth0;
  }

  setAccessToken(token) {
    this.accessToken = token;
  }

  setIdTokenClaims(idTokenClaims) {
    this.idTokenClaims = idTokenClaims ?? {};
  }

  setUser(user) {
    this.user = user ?? {};
  }

  setLoading(s) {
    this.loading = s ?? { status: false };
  }

  setSessionManagementOK(ok) {
    this.sessionManagementOK = ok ?? false;
  }

  get email() {
    return this.user.email;
  }

  get name() {
    return this.user.name;
  }

  get domain() {
    return this.user.email
      ? this.user.email.split("@")[1]
      : "everstage-test.com";
  }

  get isEmailVerified() {
    return this.user.email_verified;
  }

  get roles() {
    return this.idTokenClaims[ROLE_NAMESPACE] || [];
  }

  get isOpsAdmin() {
    return this.roles.includes(ROLES.EVERSTAGE_OPS_ADMIN_ROLE);
  }

  get hasGenieAccess() {
    return this.roles.includes(ROLES.GENIE_USER_ROLE);
  }

  get isValidEverstageAdminUser() {
    return this.roles.includes(ROLES.EVERSTAGE_ADMIN_ROLE); // add is verified condition
  }

  get isUserAllowedForAdminUILogin() {
    return this.sessionManagementOK;
  }

  get isAgentStudioAdmin() {
    return this.roles.includes(ROLES.AGENT_STUDIO_ADMIN);
  }

  get isAgentDeploymentAdmin() {
    return this.roles.includes(ROLES.AGENT_DEPLOYMENT_ADMIN);
  }

  get isQAReportsAdmin() {
    return this.roles.includes(ROLES.QA_REPORTS_ADMIN);
  }

  get isL1Ops() {
    return this.roles.includes(ROLES.L1_OPS);
  }

  get hasBulkRolesManagementAccess() {
    return this.roles.includes(ROLES.BULK_ROLES_MANAGEMENT);
  }

  get accessibleScreens() {
    const screens = [];
    if (this.isValidEverstageAdminUser) {
      screens.push(
        // SCREENS.DASHBOARD,
        SCREENS.CUSTOMERS,
        SCREENS.ETL_STATUS,
        SCREENS.ENGINE_TOOLS,
        SCREENS.DAILY_SYNC,
        SCREENS.CONFIGURE,
        SCREENS.AUDIT_LOGS,
        SCREENS.SQL_LOGS,
        SCREENS.CUSTOM_THEME,
        SCREENS.FEATURE_FLAGS
      );

      if (this.isOpsAdmin) screens.push(SCREENS.DEPLOYMENT);
    }
    if (this.isL1Ops) {
      L1_OPS_SCREENS.forEach((screen) => {
        if (!screens.includes(screen)) {
          screens.push(screen);
        }
      });
    }
    if (this.hasGenieAccess) screens.push(SCREENS.GENIE);
    if (this.isTsarAccessible) screens.push(SCREENS.TSAR);
    if (this.isAgentStudioAdmin) screens.push(SCREENS.AGENT_STUDIO);
    if (this.isAgentDeploymentAdmin) screens.push(SCREENS.AGENT_DEPLOYMENT);
    if (this.isQAReportsAdmin) screens.push(SCREENS.QA_REPORTS);
    if (this.hasBulkRolesManagementAccess) screens.push(SCREENS.BULK_PERMISSION_MANAGEMENT);
    return screens;
  }

  sessionManager() {
    return fetch("/everstage_admin/session-induction", {
      method: "POST",
      headers: this.constructRestApiHeaders(),
      body: JSON.stringify({ idToken: this.idTokenClaims.__raw }),
    });
  }

  checkTokenHealth() {
    return fetch("/everstage_admin/token-health-check", {
      method: "POST",
      headers: this.constructRestApiHeaders(),
    });
  }

  logoutManager() {
    return fetch("/everstage_admin/session-conclusion", {
      method: "POST",
      headers: this.constructRestApiHeaders(),
      body: JSON.stringify({ idToken: this.idTokenClaims.__raw }),
    });
  }

  clearBrowserData() {
    // delete all localStorage
    localStorage.clear();

    // delete all sessionStorage
    sessionStorage.clear();

    // delete all indexedDB
    // TODO: .databases() is not supported in Firefox; so, we need to find an alternative for Firefox.
    window.indexedDB
      .databases()
      .then((dbs) =>
        dbs.forEach((db) => window.indexedDB.deleteDatabase(db.name))
      );

    // delete all cache
    window.caches.keys().then((cacheNames) => {
      cacheNames.forEach((cacheName) => {
        window.caches.delete(cacheName);
      });
    });

    // delete all cookies
    document.cookie.split(";").forEach((c) => {
      document.cookie = c
        .replace(/^ +/, "")
        .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    // delete all service workers
    navigator.serviceWorker.getRegistrations().then((registrations) => {
      registrations.forEach((registration) => {
        registration.unregister();
      });
    });
  }

  killSession() {
    this.auth0.logout({ returnTo: redirectUrl }); // auth0's logout api
    window.onfocus = null; // remove event listener for 'token health check'.
    // clearing localStorage will also remove "sessionKeyAdminUI"; which will trigger auto-refresh for all other tabs in the same browser instance.
    this.clearBrowserData();
  }

  async userLogout() {
    await this.logoutManager();
    this.killSession();
  }

  constructRestApiHeaders(additionalHeaders = {}) {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.accessToken}`,
      ...additionalHeaders,
    };
  }
}

const authContext = React.createContext(null);

export const AuthStoreProvider = (props) => {
  const { children } = props;
  const store = useLocalStore((observableProps) => new AuthStore());
  return (
    <authContext.Provider value={store}>
      {store && children}
    </authContext.Provider>
  );
};

export const useAuthStore = () => {
  return React.useContext(authContext);
};
