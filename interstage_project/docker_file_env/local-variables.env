ENV=LOCALDEV
TSAR_ENV=localdev
DEPLOY_ENV=LOCALDEV
URL=http://localhost:3000

DEBUG=1
SECRET_KEY=@r*&z!0xi#*-t80h0n+w3%4rqr7i(*f6(5k#1vjz70dfp7h^vt
DJANGO_ALLOWED_HOSTS="localhost 0.0.0.0 backend host.docker.internal"

DB_SCHEMA=public
DB_HOST=host.docker.internal
DB_NAME=u-test
DB_USER=interstagedevuser
DB_PASSWORD=localdev
DB_PORT=5432

ADMIN_DB_USER=interstage-admin
ADMIN_DB_PASSWORD=adminuser

AUTH_AUDIENCE=https://everstage-icm
AUTH_ISSUER=https://login.dev.everstage.com/
AUTH_MGMT_AUDIENCE=https://everstage-dev.us.auth0.com/api/v2/
AUTH_MGMT_CLIENT_ID=XTa7W41i7DCk1gmN7YPqVKkDmSIApCV3
AUTH_MGMT_CLIENT_SECRET=****************************************************************
AUTH_MGMT_ISSUER=https://everstage-dev.us.auth0.com/

S3_REGION_NAME=us-west-1
AWS_REGION=ap-south-1
AWS_DEFAULT_REGION=ap-south-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=LUIqsLRl/KNzoMYDZwlbY7QNXGyLjlETx1s5xXjU
S3_CDN=https://d177w6gi8jmdvz.cloudfront.net
S3_BACKUP_BUCKET=local-snowflake-backup
S3_PVT_ASSETS_BUCKET=everstage-pvt-assets-local
S3_AVATAR_CDN=https://d3js4o1ysqtjdu.cloudfront.net

# Redis related variables
EVENT_REDIS=redis://ever-redis:6379
CELERY_BROKER_URL=redis://ever-redis:6379
CELERY_RESULT_BACKEND=redis://ever-redis:6379
ELASTIC_CACHE_SERVER=redis://ever-redis:6379
ELASTIC_CACHE_RO_SERVER=redis://ever-redis:6379
INSTRUCTION_WORKER_PROCESSES=1
TASK_WORKER_PROCESSES=4

SENDGRID_API_KEY=*********************************************************************
SENDGRID_WORKFLOW_TEMPLATE_ID=d-b5f8bb82115a40ae9f402400e6534049
SENDGRID_WORKFLOW_ATTACHMENT_TEMPLATE_ID=d-630237a041944203b83fcaa11048638f
CSP=frame-ancestors self https://*.force.com https://*.salesforce.com  https://*.visualforce.com
HOTP_SECRET=HNJSREM5CWNSEN54WNT2EGMRSB4UCFG4

DOCUSIGN_CLIENT_ID=************************************
DOCUSIGN_CLIENT_SECRET=75657500-86fc-405b-8011-31834f6c912c

SLACK_SIGNING_SECRET=a054e6ec880e213a14a4956a303ad7ea
SLACK_CLIENT_ID=2943567919989.3599016742054
SLACK_CLIENT_SECRET=4fed015a2cc717d1f51cca83ac27b047

CPQ_SLACK_SIGNING_SECRET=31bc97419a231dff8c21856ae2364b01
CPQ_SLACK_CLIENT_ID=8630298362022.8702989451572
CPQ_SLACK_CLIENT_SECRET=090c7d63e6416d46b2ab3e68d59b5ca6

DOCUMENT360_READER_GROUP_ID_PAYEES=************************************
DOCUMENT360_READER_GROUP_ID_ADMINS=************************************
DOCUMENT360_BASE64ENCODED_CLIENT_ID_SECRET=************************************************************************************************************

SUPERSET_API_USERNAME=admin
SUPERSET_API_PASSWORD=admin
SUPERSET_HOST=http://localhost:8088

MSTEAMS_CLIENT_ID=************************************
MSTEAMS_CLIENT_SECRET=****************************************
MSTEAMS_APP_EXTERNAL_ID=************************************

SNOWFLAKE_USER=EVERSTAGE_LOCAL_USER
SNOWFLAKE_PASSWORD=Tes#Pwd3
SNOWFLAKE_ACCOUNT=qt08442.ap-south-1.aws
SNOWFLAKE_REGION=ap-south-1
SNOWFLAKE_WAREHOUSE=EVERSTAGE_LOCAL
SNOWFLAKE_DATABASE=EVERSTAGE_LOCAL_INDIA
SNOWFLAKE_SCHEMA=PUBLIC
SNOWFLAKE_ROLE=ACCOUNTADMIN
SNOWFLAKE_EXECUTION_MODE=etl

STORMBREAKER_SNOWFLAKE_USER=EVERSTAGE_LOCAL_USER
STORMBREAKER_SNOWFLAKE_PASSWORD=Tes#Pwd3
STORMBREAKER_SNOWFLAKE_ACCOUNT=qt08442.ap-south-1.aws
STORMBREAKER_SNOWFLAKE_REGION=ap-south-1
STORMBREAKER_SNOWFLAKE_DATABASE=EVERSTAGE_LOCAL_INDIA
STORMBREAKER_SNOWFLAKE_WAREHOUSE=EVERSTAGE_LOCAL
STORMBREAKER_SNOWFLAKE_SCHEMA=PUBLIC
STORMBREAKER_SNOWFLAKE_ROLE=ACCOUNTADMIN

ANALYTICS_SNOWFLAKE_USER=EVERSTAGE_LOCAL_USER
ANALYTICS_SNOWFLAKE_PASSWORD=Tes#Pwd3
ANALYTICS_SNOWFLAKE_ACCOUNT=qt08442.ap-south-1.aws
ANALYTICS_SNOWFLAKE_REGION=ap-south-1
ANALYTICS_SNOWFLAKE_DATABASE=ANALYTICS_DEV_DB
ANALYTICS_SNOWFLAKE_INTERNAL_DATABASE=ANALYTICS_DEV_INTERNAL_DB	
ANALYTICS_SNOWFLAKE_PROD_REPLICATION_DATABASE=ANALYTICS_DEV_INTERNAL_DB
ANALYTICS_SNOWFLAKE_AGGREGATED_DATABASE=CUSTOMER_ANALYTICS_DB
ANALYTICS_SNOWFLAKE_WAREHOUSE=EVERSTAGE_LOCAL
ANALYTICS_SNOWFLAKE_SCHEMA=PUBLIC
ANALYTICS_SNOWFLAKE_ROLE=ACCOUNTADMIN
ANALYTICS_BILLING_INFO_S3_BUCKET_NAME=everstage-pvt-assets-dev
ANALYTICS_BILLING_INFO_SERVICE_ACCOUNT_FILE=speedy-web-460316-g8-f88181ec0031.json
ANALYTICS_BILLING_INFO_GOOGLE_SHEET_URL=https://docs.google.com/spreadsheets/d/15u0Ijpv724b3UKeyNRyRxwz5PDtx4gnVZeUuI8F4DAU/edit?usp=sharing
ANALYTICS_BILLING_INFO_WORKSHEET_ID=0
ANALYTICS_ACTIVE_USERS_CLIENT_ID=10048
ANALYTICS_ACTIVE_USERS_CUSTOM_OBJECT_NAME=ACTIVE_USERS_CO

SUPABASE_URL=https://syauwwbuqwukdpynwgoc.supabase.co
SUPABASE_SERVICE_ROLE_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.yoOfFDssD7xbxZ41LskLlrjax7n6xdSJq5sZRlpAi9A
SUPABASE_JWT_SECRET=0f1KxEYLW9Q0SX08uKeipUZ3VzQ6Q/3XbqeZwyC9SW9da1VHSSfuoMnea/BjSXryYYJA0Lv33RS+JIlsL7Od8A==
SUPABASE_REALTIME_TABLE=realtime_events_local

SALESFORCE_OAUTH_CLIENT_ID=3MVG9n_HvETGhr3DuXxRseE41WsFe1uoZwivzZIokLsIznsCnWh28b3O9ZH0oej44qG5JsLv3x3Xdvws7FDOV
SALESFORCE_OAUTH_CLIENT_SECRET=5E9E45C642DD949A80D9ADAF1E63D05DA107829F02F80E442AC5D5ECAA3C4000

SALESFORCE_CPQ_OAUTH_CLIENT_ID=3MVG9pRzvMkjMb6mU3Qta.UeKRXQlUn_l2UqaDkCD53s1l1UyGIgBimAtjht4k9P1.hReVhFLVHwo3m8foPA8
SALESFORCE_CPQ_OAUTH_CLIENT_SECRET=46803D5A52E247D594DAAD169BCEFDF49967A3AE10DA27BEA5A1479AF8BE3BA6

C_FORCE_ROOT=1
ROW_CALCULATED_FIELDS_BATCH_SIZE=30000
SEGMENT_WRITE_KEY=IwyQ3mwHZMg6vKc0eIpRYNna8nICWMKp

TG_NAME=UPSTREAM,DATABOOK,COMMISSION,SETTLEMENT,REPORT,EMAILNOTIFICATION,MISC,SNAPSHOT,USERGROUPMEMBERS,DOCUSIGNDOCUMENTS,FORECAST,SEARCHENGINE,SPARK_DRY_RUN,DOWNSTREAM,MAESTRO_ROUTER,SETTLEMENT_V3_DRY_RUN
SUB_PLAN=BASIC,ENTERPRISE,MANUAL,ICU,IMPL_SLOT_1,IMPL_SLOT_2,ETL_SLOT_1,ETL_SLOT_2,ETL_SLOT_3
CLIENT_LEVEL_QUEUES=S3_EXPORT,SFTP_EXPORT
COMMON_QUEUES=ROUTER,COMMISSIONNOTIFICATION,INVARIANT,APPROVALS,OTHERS

REACT_APP_SENTRY_DSN=https://<EMAIL>/4505159644086272
REACT_APP_SENTRY_RELEASE=everstage-local-release
REACT_APP_SENTRY_PROJECT=everstage-non-prod

STABILITY_API_KEY=sk-aqflxXHSd9ZKE6PmDtmzVhMF17ScudknR1Z9gNcmnXAA9B4d
STABILITY_API_HOST=https://api.stability.ai
STABILITY_ENGINE_ID=stable-diffusion-xl-beta-v2-2-2

REACT_APP_EVERSTAGE_ASSETS_URL=https://everstage-public-assets.s3.us-west-1.amazonaws.com/app-graphics
EVERSTAGE_SUPERSET_API_CLIENT_SECRET=3cc937c3-6188-4800-875b-7327b0ca9bcd

SALESFORCE_ESTIMATOR_SECRET=8591de43-41fb-4030-bc46-bc8cac73602d
DATA_MIGRATOR_SECRET=0d7ca17b-b20d-4d8e-9c9a-750a504c64e9

EFS_BASE_PATH=/mnt/efs
METRICS_LAMBDA_NAME=metrics_test

DJANGO_REDIS_SCAN_ITERSIZE=100000

CELERYD_MAX_TASKS_PER_CHILD=100
# 1048576 kilobytes = 1 GB
CELERY_MAX_MEMORY_PER_CHILD=1048576
WORKER_PROC_ALIVE_TIMEOUT=30
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_TASK_ACKS_LATE=True
#10 hours
CELERY_BROKER_VISIBILITY_TIMEOUT=36000

GMAIL_CLIENT_ID=162136598441-gnsu8uigp07dmo1fi6gd6tn5jq4qvl11.apps.googleusercontent.com
GMAIL_CLIENT_SECRET=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
GMAIL_REDIRECT_URI=https://mail.google.com

REACT_APP_FRESHDESK_SLUG=1709199349448
REACT_APP_FRESHDESK_CLIENT_ID=451980303908285122
REACT_APP_FRESHDESK_REDIRECT_URL=https%3A%2F%2Feverstage-support.freshdesk.com%2Ffreshid%2Fcustomer_authorize_callback%3Fhd%3Deverstage-support.freshdesk.com
REACT_APP_FRESHDESK_OAUTH_ID=683282743077122323
REACT_APP_ENV_LEARNUPON_VISIBLE=false
REACT_APP_BRAND_FETCH_CLIENT_ID=1idce0oQGHTnX120CcV

TYPESENSE_API_KEY=vN7S2iILVmrjLTzLSxZDIKDYcPdWP9L8
TYPESENSE_HOST=47plzx9tc6nf5a2ip-1.a1.typesense.net
TYPESENSE_PORT=443
TYPESENSE_PROTOCOL=https
TYPESENSE_CONNECTION_TIMEOUT=5

ES_BACKEND_API_KEY=3ea54018-bad0-4366-8abc-62d21a5bea0b
MANNAI_ENDPOINT=http://host.docker.internal:9500
EVERSTAGE_MANNAI_API_CLIENT_SECRET=ee773009-2fd8-49da-88b9-4bf20f3ed8f6
EVERSTAGE_SLACK_BOT_TOKEN=*********************************************************

PDF_GENERATION_TIMEOUT_SECS=500
S3_GONG_CALL_BUCKET=everstage-cpq-ai-assets

KAFKA_BROKER_URL=kafka:9092
AWS_REGION=ap-south-1
TOPIC_DEFAULT_PARTITION_NUMBER=3

DEPLOYMENT_TAG=LOCAL_MASTER

LIVY_HOST = host.docker.internal
LIVY_PORT=8998
S3_ETL_SNAPSHOT_FOLDER=s3://everstage-emr-dev-1/snapshot_data/
EVERSTAGE_SPARK_API_CLIENT_SECRET=ce1860fc9059505d67f613ae8681ec7d9d3d5e73f5d0191e42313c8ed74683d0
EVERSTAGE_ETL_INFRA_LAMBDA_API_CLIENT_SECRET=1ce4ca3a13c968a82ac8c5fb0df5807bd17e24af11adc144985522d74137e2dc

ETL_INFRA_SNOWFLAKE_USER=EVERSTAGE_LOCAL_USER
ETL_INFRA_SNOWFLAKE_PASSWORD=Tes#Pwd3
ETL_INFRA_SNOWFLAKE_ACCOUNT=qt08442.ap-south-1.aws
ETL_INFRA_SNOWFLAKE_REGION=ap-south-1
ETL_INFRA_SNOWFLAKE_WAREHOUSE=EVERSTAGE_LOCAL
ETL_INFRA_SNOWFLAKE_DATABASE=ETL_INFRA_LOCAL
ETL_INFRA_SNOWFLAKE_SCHEMA=PUBLIC
ETL_INFRA_SNOWFLAKE_ROLE=ACCOUNTADMIN

SNOWFLAKE_DRY_RUN_WAREHOUSE=EVERSTAGE_LOCAL

TEMP_TABLE_DB_NAME=EVERSTAGE_LOCAL_INDIA
TEMP_TABLE_SCHEMA_NAME=PUBLIC
UDF_S3_LOCATION=s3://udf-bundle

UDF_SECRET_NAME=dev/iam/snowflake-udf-user
UDF_REGION_NAME=us-west-1

DEALROOM_JWT_SECRET=9KfJ@u#rL2WvE!zPx0B^G8qNdCv

MANNAI_JWT_SECRET=9ChTDs5rVmEn7gQtBKX8TyRlk0h6_sMrDEjfgpOcfo4
MANNAI_JWT_AUDIENCE=mannai-tools
MANNAI_JWT_ISSUER=mannai

LEARNUPON_SECRET_KEY=4b12ab18d9e3109aa352081015
LEARNUPON_API_USERNAME=1821a91cva9fq0120554
LEARNUPON_API_PASSWORD=9e258ca1820a14db4bb4f09ee2ede5
LEARNUPON_ADMIN_GROUP_ID=936981
LEARNUPON_PAYEE_GROUP_ID=621914
LEARNUPON_SERVICE_ENABLED=false

SIGMA_API_CLIENT_ID=f2240318a62508a2c91cb6bbb440490e002eab8240efd036def0c5194b93c195
SIGMA_API_CLIENT_SECRET=86928c3e43e59dab1f046bf02bd360321ac5ab75bef0c907f331e16526f64553357aaac8e992506ec20f0c684f3c8813a577b5990a12d6b0cb5e48c4f0594779
SIGMA_BASE_URL=https://aws-api.sigmacomputing.com
SIGMA_TEMPLATE_WORKBOOK_ID=173d748b-f53f-43db-a84c-b25122685612
SIGMA_SNOWFLAKE_USER_PASSWORD=abyC5vt5tKGLtN
SIGMA_EMBED_CLIENT_ID=e84dc675ff48e91b42cecc696a0ef7b189744cc25151f48ed831ea40c1431e40
SIGMA_EMBED_CLIENT_SECRET=b00410a719fba45027ab1bb3af9240ff89e1eec634769fa52f74e30b44660bfc3de6aecaf1caa6ea64628888e9206951053e79ba89544419058493724f926a5b

PVT_KEY_REGION=us-west-1
AWS_SNOWFLAKE_PRIVATE_KEY_SECRET_NAME=dev/iam/local-snowflake-user-pvt-key
AWS_STORMBREAKER_PRIVATE_KEY_SECRET_NAME=dev/iam/local-snowflake-user-pvt-key
PRIVATE_KEY_MANAGER_LOCK_TIMEOUT=5

SIGMA_APP_BASE_URL=https://app.sigmacomputing.com/everstage
SIGMA_JWT_EXPIRY=86400

#EverAI
ANTHROPIC_API_KEY=sk-ant-None
OPENAI_API_KEY=sk-None
GROQ_API_KEY=gsk_None
GOOGLE_API_KEY=AINone
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
LANGCHAIN_API_KEY="lsv2_pt_None"

EVERSTAGE_SQL_LOGGING_COLORS="false"
