from datetime import datetime

from django import setup
from django.utils.timezone import now
from pytz import utc

if __name__ == "__main__":
    setup()
    from everstage_ddd.cpq.reports.custom.report_etl import QRGenerator

    print()
    client_id = int(input("Enter client_id: "))
    start_date = input("Enter start_date: ")
    end_date = input("Enter end_date: ")
    print()

    start_date = datetime.strptime(start_date, "%Y-%m-%d").astimezone(utc)
    end_date = datetime.strptime(end_date, "%Y-%m-%d").astimezone(utc)

    dataframe = QRGenerator(client_id).run(start_date, end_date)
    curr_timestamp = now().strftime("%Y-%m-%d_%H:%M")
    dataframe.to_csv(f"cpq_report_{client_id}_{curr_timestamp}.csv", index=False)
