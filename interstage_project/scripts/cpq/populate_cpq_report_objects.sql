INSERT INTO public.ever_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, ever_object_id, "name", data_origin, source_table, primary_key, table_name) VALUES
(current_timestamp, NULL, false, NULL, 'quote', 'Quote', 'quote_object', NULL, '["quote_id"]'::jsonb, 'report_object_data'),
(current_timestamp, NULL, false, NULL, 'quote_line_item', 'Quote Line Item', 'quote_object', NULL, '["quote_id", "line_item_id"]'::jsonb, 'report_object_data'),
(current_timestamp, NULL, false, NULL, 'quote_line_item_tier', 'Quote Line Item Tier', 'quote_object', NULL, '["quote_id", "line_item_id", "tier_id"]'::jsonb, 'report_object_data');

INSERT INTO public.ever_object_variables 
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES
(current_timestamp, NULL, false, NULL, 4, 'quote_id', 'Quote ID', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'display_id', 'Display ID', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'name', 'Name', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'form_id', 'Form ID', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'form_name', 'Form Name', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'currency', 'Currency', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'status', 'Status', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'owner_name', 'Owner Name', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'owner_email', 'Owner Email', 'quote'),
(current_timestamp, NULL, false, NULL, 2, 'valid_till', 'Valid Till', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'opportunity_id', 'Opportunity ID', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'account_id', 'Account ID', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'contact_id', 'Contact ID', 'quote'),
(current_timestamp, NULL, false, NULL, 2, 'subscription_start_date', 'Subscription Start Date', 'quote'),
(current_timestamp, NULL, false, NULL, 2, 'subscription_end_date', 'Subscription End Date', 'quote'),
(current_timestamp, NULL, false, NULL, 1, 'duration', 'Duration', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'duration_type', 'Duration Type', 'quote'),
(current_timestamp, NULL, false, NULL, 1, 'list_total', 'List Total', 'quote'),
(current_timestamp, NULL, false, NULL, 1, 'net_total', 'Net Total', 'quote'),
(current_timestamp, NULL, false, NULL, 1, 'discount', 'Discount', 'quote'),
(current_timestamp, NULL, false, NULL, 3, 'primary_quote', 'Primary Quote', 'quote'),
(current_timestamp, NULL, false, NULL, 2, 'won_date', 'Won Date', 'quote'),
(current_timestamp, NULL, false, NULL, 2, 'created_date', 'Created Date', 'quote'),
(current_timestamp, NULL, false, NULL, 2, 'last_updated_date', 'Last Updated Date', 'quote'),
(current_timestamp, NULL, false, NULL, 3, 'deleted', 'Deleted', 'quote'),
(current_timestamp, NULL, false, NULL, 4, 'quote_id', 'Quote ID', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'line_item_id', 'Line Item ID', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'quote_display_id', 'Quote Display ID', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'product_id', 'Product ID', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'pricepoint_id', 'Price Point ID', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'product_code', 'Product Code/SKU', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'product_name', 'Product Name', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'product_description', 'Product Description', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'category', 'Category', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'charged_on', 'Charged On', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'currency', 'Currency', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'pricing_model', 'Pricing Model', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'billing_type', 'Billing Type', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'billing_frequency', 'Billing Frequency', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'phase_name', 'Phase Name', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 2, 'phase_start_date', 'Phase Start Date', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 2, 'phase_end_date', 'Phase End Date', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 1, 'quantity', 'Quantity', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 1, 'list_price', 'List Price', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 1, 'net_price', 'Net Price', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 1, 'discount_percentage', 'Discount Percentage', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 1, 'total', 'Total', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 2, 'last_updated_date', 'Last Updated Date', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 3, 'deleted', 'Deleted', 'quote_line_item'),
(current_timestamp, NULL, false, NULL, 4, 'quote_id', 'Quote ID', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 4, 'line_item_id', 'Line Item ID', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 4, 'tier_id', 'Tier ID', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 4, 'quote_display_id', 'Quote Display ID', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 4, 'product_id', 'Product ID', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 4, 'product_name', 'Product Name', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 4, 'product_code', 'Product Code/SKU', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_number', 'Tier Number', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_start', 'Tier Start', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_end', 'Tier End', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_quantity', 'Tier Quantity', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_list_unit_price', 'Tier List Unit Price', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_net_unit_price', 'Tier Net Unit Price', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_list_flat_price', 'Tier List Flat Price', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_net_flat_price', 'Tier Net Flat Price', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 1, 'tier_total', 'Tier Total', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 2, 'last_updated_date', 'Last Updated Date', 'quote_line_item_tier'),
(current_timestamp, NULL, false, NULL, 3, 'deleted', 'Deleted', 'quote_line_item_tier');