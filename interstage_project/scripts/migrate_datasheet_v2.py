# ruff: noqa
import logging
import traceback
import uuid

import django
import typer
from django.db import transaction
from rich import print
from typing_extensions import Annotated

django.setup()
from commission_engine.accessors.client_accessor import get_active_clients, get_client
from commission_engine.accessors.databook_accessor import DatasheetFilterAccessor
from everstage_ddd.datasheet.helpers.datasheet_version_writer import (
    DatasheetVersionWriter,
)
from everstage_ddd.datasheet.models import Datasheet, DatasheetView, DatasheetViewFilter
from everstage_ddd.datasheet.selectors.datasheet_selector import DatasheetSelector
from everstage_ddd.datasheet.utils.datasheet_utils import (
    convert_v1_pivot_to_v2,
    convert_v1_ui_filter_spec_to_v2,
)

"""
This script migrates the datasheets in v1 to v2
"""

logger = logging.getLogger(__name__)


@transaction.atomic
def migrate_datasheets_to_v2(client_id, without_filters, without_pivots, force):
    client = get_client(client_id)

    if client is None:
        print(f"[bold red] Client {client_id} not found [/bold red]")
        return

    if not force:
        if client.client_features.get("show_data_sources_v2", False):
            print(
                f"[bold yellow] Skipping client {client_id} as v2 is already enabled [/bold yellow]"
            )
            return
    else:
        if client.client_features.get("show_data_sources_v2", False):
            print(
                f"[bold yellow] Switching back to v1 for client {client_id} [/bold yellow]"
            )
            DatasheetSelector(client_id).switch_transformation_spec_version("v1")
            client.client_features["show_data_sources_v2"] = False
            without_filters = True
            without_pivots = True

    client.client_features["datasheet_v2_migration"] = True
    client.client_features["datasheet_v2"] = False
    client.save()

    datasheets = Datasheet.objects.filter(
        knowledge_end_date__isnull=True,
        is_deleted=False,
        client_id=client_id,
    ).order_by("order")

    print(f"Migrating {datasheets.count()} datasheets for client {client_id}")

    for idx, datasheet in enumerate(datasheets):
        print(f"Migrating datasheet {idx + 1} - {datasheet.datasheet_id}")

        DatasheetVersionWriter(
            client_id=client_id,
            datasheet_id=datasheet.datasheet_id,
        ).transform_and_save()

        if not without_filters:
            convert_v1_ui_filters_spec_to_v2(client_id, datasheet.datasheet_id)
        if not without_pivots:
            convert_v1_pivots_to_v2(client_id, datasheet.datasheet_id)
        print(f"Migrated datasheet - {idx + 1} - {datasheet.datasheet_id}")

    client = get_client(client_id)
    client.client_features["datasheet_v2_migration"] = False
    client.client_features["datasheet_v2"] = True
    client.save()

    print(f"Migration completed for client {client_id}")


@transaction.atomic
def convert_v1_ui_filters_spec_to_v2(client_id, datasheet_id):
    logger.info(f"BEGIN: Converting UI filters for datasheet {datasheet_id}")
    datasheet_v1_filters = list(
        DatasheetFilterAccessor(client_id)
        .client_latest_kd_aware()
        .filter(datasheet_id=datasheet_id, config_type="FILTER")
        .order_by("knowledge_begin_date")
    )
    for filter in datasheet_v1_filters:
        convert_v1_ui_filter_spec_to_v2(
            client_id, datasheet_id, filter.filter_id, ignore_v2_enabled=True
        )

    logger.info("END: Converting UI filters for datasheet %s", datasheet_id)


@transaction.atomic
def convert_v1_pivots_to_v2(client_id, datasheet_id):
    """
    Convert version 1 pivots to version 2 for a specific datasheet.

    This function retrieves all pivot filters for a given datasheet and client,
    then converts each pivot from version 1 to version 2 format.

    Args:
        client_id (int): The ID of the client.
        datasheet_id (str): The ID of the datasheet.

    Raises:
        Any exceptions raised by the underlying database operations or the
        convert_v1_pivot_to_v2 function.
    """
    logger.info("BEGIN: Converting pivots for datasheet %s", datasheet_id)
    datasheet_v1_pivots = list(
        DatasheetFilterAccessor(client_id)
        .client_latest_kd_aware()
        .filter(datasheet_id=datasheet_id, config_type="PIVOT")
        .order_by("knowledge_begin_date")
    )

    for pivot in datasheet_v1_pivots:
        filter_id = pivot.filter_id
        convert_v1_pivot_to_v2(
            client_id, datasheet_id, filter_id, ignore_v2_enabled=True
        )

    logger.info("END: Converting pivots for datasheet %s", datasheet_id)


def main(
    client_ids=typer.Option(
        help="The client for which the datasheet v2 migration should be done. Leave blank to migrate for all clients.",
        default=None,
    ),
    without_filters: bool = typer.Option(
        help="If provided, the script will not convert filters in v1 to v2.",
        default=False,
    ),
    without_pivots: bool = typer.Option(
        help="If provided, the script will not convert pivots in v1 to v2.",
        default=False,
    ),
    exclude_client_ids=typer.Option(
        help="The client ids to exclude from the migration. Leave blank to migrate for all clients.",
        default="",
    ),
    force_run: bool = typer.Option(
        help="If provided, the script will force the migration without filters and pivots even if v2 is already enabled.Used for operational issues.",
        default=False,
    ),
):
    """
    This script migrates the datasheets in v1 to v2
    Example:
        python migrate_datasheet_v2.py --client_ids 1,2 --without_filters --without_pivots
        python migrate_datasheet_v2.py --exclude_client_ids 3
        python migrate_datasheet_v2.py --client_ids 1 --force_run
    """
    exclude_client_ids = exclude_client_ids.split(",")
    failed_clients = []
    if client_ids is not None:
        for client_id in client_ids.split(","):
            if client_id in exclude_client_ids:
                print(
                    f"[bold yellow] Skipping client {client_id} as it is in the exclude list [/bold yellow]"
                )
                continue
            print(
                f"[bold blue] Populating variable details for Client ID {client_id} [/bold blue]"
            )
            try:
                migrate_datasheets_to_v2(
                    client_id, without_filters, without_pivots, force_run
                )
                print(
                    f"[bold green] Migration done for Client ID {client_id} [/bold green]"
                )
            except Exception:
                print(
                    f"[bold red] Error: Migration failed for Client ID {client_id} [/bold red]"
                )
                print(traceback.format_exc())
                failed_clients.append(client_id)
    else:
        active_clients = get_active_clients()
        for client in active_clients:
            if client["client_id"] in exclude_client_ids:
                print(
                    f"[bold yellow] Skipping client {client['client_id']} as it is in the exclude list [/bold yellow]"
                )
                continue
            client_id = client["client_id"]
            client_name = client["name"]

            try:
                print(
                    f"[bold blue] Migration started for Client {client_name} with ID {client_id} [/bold blue]"
                )
                migrate_datasheets_to_v2(
                    client_id, without_filters, without_pivots, force_run
                )
                print(
                    f"[bold green] Migration ended for Client {client_name} with ID {client_id} [/bold green]"
                )
            except Exception:
                print(
                    f"[bold red] Error: Migration failed for Client {client_name} with ID {client_id} [/bold red]"
                )
                print(traceback.format_exc())
                failed_clients.append(client_id)

        print("[bold blue] Datasheet migration v2 script complete [/bold blue]")
    if len(failed_clients) > 0:
        print(f"[bold red] Failed clients: {failed_clients}")
    else:
        print("[bold green] All clients migrated successfully [/bold green]")


if __name__ == "__main__":
    typer.run(main)
