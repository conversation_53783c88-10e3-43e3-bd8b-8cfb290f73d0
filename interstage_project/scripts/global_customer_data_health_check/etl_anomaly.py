import logging
import os
from datetime import datetime

import pandas as pd
import pytz
from celery import shared_task
from django.db import connection

from common.celery.celery_base_task import EverCeleryBaseTask
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)

logger = logging.getLogger(__name__)

PREFIX = ""


def fetch_past_e2e_sync_run_ids(client_id, e2e_sync_run_id):
    """
    Fetch the e2e sync run ids of the past 14 days for the given client and e2e sync run id.
    """
    data = []
    logger.info("BEGIN: Fetching past e2e sync run ids for client %s", client_id)
    query = f"""
        WITH reference_time AS (
            SELECT sync_start_time 
            FROM {PREFIX}etl_sync_status
            WHERE client_id = {client_id}
            AND e2e_sync_run_id = '{e2e_sync_run_id}'   
        )
        SELECT DISTINCT e2e_sync_run_id
        FROM {PREFIX}etl_sync_status 
        WHERE client_id = {client_id}
        AND task = 'End to End Sync' 
        AND sync_status = 'complete'
        AND audit IS NULL
        AND e2e_sync_run_id != '{e2e_sync_run_id}'
        AND sync_start_time >= (SELECT sync_start_time FROM reference_time) - INTERVAL '14 days';
    """

    with connection.cursor() as cursor:
        cursor.execute(query)
        results = cursor.fetchall()
        data = list(str(i[0]) for i in results)

    logger.info("END: Fetching past e2e sync run ids for client %s", client_id)

    return data


def get_data_from_e2e(client_id: int, e2e_sync_run_id: str) -> dict:
    """
    Get the data of different stages of the end to end sync from the e2e sync run id for the given client.
    """
    data = {}
    logger.info(
        "BEGIN: Getting data from e2e sync for client %s and e2e sync run id %s",
        client_id,
        e2e_sync_run_id,
    )

    queries = {
        "Upstream ETL": f"""
            SELECT count(mues.sync_run_id)
            FROM {PREFIX}upstream_etl_status mues
            WHERE mues.client_id = {client_id}
            AND mues.e2e_sync_run_id = '{e2e_sync_run_id}';
        """,
        "Report ETL": f"""
            SELECT count(mres.sync_run_id)
            from {PREFIX}report_etl_status mres 
            WHERE client_id = {client_id}
            and mres.e2e_sync_run_id = '{e2e_sync_run_id}'
            and (task = 'SYSTEM_REPORT_WRAPPER_SYNC' or task = 'QUOTA_REPORT_SYNC' or task = 'USER_REPORT_SYNC');
        """,
        "Databook ETL": f"""
            SELECT count(mdes.sync_run_id)
            from {PREFIX}databook_etl_status mdes
            where mdes.client_id = {client_id}
            and mdes.e2e_sync_run_id = '{e2e_sync_run_id}'
            and mdes.sync_type = 'SYSTEM_CUSTOM_DB_SYNC';
        """,
        "Commission L1": f"""
            SELECT count(mces.sync_run_id)
            from {PREFIX}commission_etl_status mces 
            WHERE mces.client_id = {client_id}
            and mces.e2e_sync_run_id = '{e2e_sync_run_id}'
            and (mces.task = 'COMMISSION_PAYEE_SYNC_L1' or mces.task = 'TEAM_PAYEE_SYNC_L1');
        """,
        "Intercommission Report Object": f"""
            select count(mres.sync_run_id)
            from {PREFIX}report_etl_status mres
            where client_id = {client_id}
            and mres.e2e_sync_run_id = '{e2e_sync_run_id}'
            and (task = 'INTER_COMMISSION_REPORT_SYNC' or task = 'INTER_REPORT_WRAPPER_SYNC' or task = 'INTER_QUOTA_ATTAINMENT_REPORT_SYNC');
        """,
        "Inter Databook": f"""
            SELECT count(mdes.sync_run_id)
            from {PREFIX}databook_etl_status mdes 
            where client_id = {client_id}
            and mdes.e2e_sync_run_id = '{e2e_sync_run_id}'
            and mdes.sync_type = 'INTER_OBJECT_DB_SYNC';
        """,
        "Commission L2": f"""
            SELECT count(mces.sync_run_id)
            from {PREFIX}commission_etl_status mces 
            where client_id = {client_id}
            and mces.e2e_sync_run_id = '{e2e_sync_run_id}'
            and (mces.task = 'COMMISSION_PAYEE_SYNC' or mces.task = 'TEAM_PAYEE_SYNC');
        """,
        "Settlement ETL": f"""
            SELECT count(mses.sync_run_id)
            from {PREFIX}settlement_etl_status mses
            where client_id = {client_id}
            and mses.e2e_sync_run_id = '{e2e_sync_run_id}'
            and mses.task = 'SETTLEMENT_SYNC';
        """,
        "Report Object": f"""
            SELECT count(mres.sync_run_id)
            from {PREFIX}report_etl_status mres 
            where client_id = {client_id}
            and mres.e2e_sync_run_id = '{e2e_sync_run_id}'
            and (task = 'PAYOUT_REPORT_SYNC' or 
                task = 'COMMISSION_REPORT_WRAPPER_SYNC' or 
                task = 'COMMISSION_REPORT_SYNC' or 
                task = 'COMMISSION_SUMMARY_REPORT_SYNC' or
                task = 'QUOTA_ATTAINMENT_REPORT_SYNC' or
                task = 'SETTLEMENT_REPORT_SYNC'
            ); 
        """,
        "Databook L2": f"""
            SELECT count(mdes.sync_run_id)
            from {PREFIX}databook_etl_status mdes 
            where client_id = {client_id}
            and mdes.e2e_sync_run_id = '{e2e_sync_run_id}'
            and sync_type = 'COMMISSION_DB_SYNC'; 
        """,
    }

    with connection.cursor() as cursor:
        for query_name, query in queries.items():
            cursor.execute(query)
            results = cursor.fetchall()
            data[query_name] = results[0][0]

    logger.info(
        "END: Getting data from e2e sync for client %s and e2e sync run id %s",
        client_id,
        e2e_sync_run_id,
    )

    return data


def get_past_data_for_all_e2e_syncs(client_id, e2e_sync_run_ids):
    """
    Get the past data for all the e2e syncs.
    """
    logger.info("BEGIN: Getting past data from past e2e syncs for client %s", client_id)
    str_e2e_sync_run_ids = ", ".join(
        f"'{e2e_sync_run_id}'" for e2e_sync_run_id in e2e_sync_run_ids
    )

    queries = {
        "Upstream ETL": f"""
            SELECT mues.e2e_sync_run_id, count(mues.sync_run_id)
            FROM {PREFIX}upstream_etl_status mues
            WHERE mues.client_id = {client_id}
            and mues.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            GROUP BY mues.e2e_sync_run_id;
        """,
        "Report ETL": f"""
            SELECT mres.e2e_sync_run_id, count(mres.sync_run_id)
            from {PREFIX}report_etl_status mres 
            WHERE mres.client_id = {client_id}
            and mres.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and (task = 'SYSTEM_REPORT_WRAPPER_SYNC' or task = 'QUOTA_REPORT_SYNC' or task = 'USER_REPORT_SYNC')
            GROUP BY mres.e2e_sync_run_id;
        """,
        "Databook ETL": f"""
            SELECT mdes.e2e_sync_run_id, count(mdes.sync_run_id)
            from {PREFIX}databook_etl_status mdes
            where mdes.client_id = {client_id}
            and mdes.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and mdes.sync_type = 'SYSTEM_CUSTOM_DB_SYNC'
            GROUP BY mdes.e2e_sync_run_id;
        """,
        "Commission L1": f"""
            SELECT mces.e2e_sync_run_id, count(mces.sync_run_id)
            from {PREFIX}commission_etl_status mces 
            WHERE mces.client_id = {client_id}
            and mces.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and (mces.task = 'COMMISSION_PAYEE_SYNC_L1' or mces.task = 'TEAM_PAYEE_SYNC_L1')
            GROUP BY mces.e2e_sync_run_id;
        """,
        "Intercommission Report Object": f"""
            select mres.e2e_sync_run_id, count(mres.sync_run_id)
            from {PREFIX}report_etl_status mres
            WHERE mres.client_id = {client_id}
            and mres.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and (task = 'INTER_COMMISSION_REPORT_SYNC' or task = 'INTER_REPORT_WRAPPER_SYNC' or task = 'INTER_QUOTA_ATTAINMENT_REPORT_SYNC')
            GROUP BY mres.e2e_sync_run_id;
        """,
        "Inter Databook": f"""
            SELECT mdes.e2e_sync_run_id, count(mdes.sync_run_id)
            from {PREFIX}databook_etl_status mdes 
            WHERE mdes.client_id = {client_id}
            and mdes.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and mdes.sync_type = 'INTER_OBJECT_DB_SYNC'
            GROUP BY mdes.e2e_sync_run_id;
        """,
        "Commission L2": f"""
            SELECT mces.e2e_sync_run_id, count(mces.sync_run_id)
            from {PREFIX}commission_etl_status mces 
            WHERE mces.client_id = {client_id}
            and mces.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and (mces.task = 'COMMISSION_PAYEE_SYNC' or mces.task = 'TEAM_PAYEE_SYNC')
            GROUP BY mces.e2e_sync_run_id;
        """,
        "Settlement ETL": f"""
            SELECT mses.e2e_sync_run_id, count(mses.sync_run_id)
            from {PREFIX}settlement_etl_status mses
            WHERE mses.client_id = {client_id}
            and mses.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and mses.task = 'SETTLEMENT_SYNC'
            GROUP BY mses.e2e_sync_run_id;
        """,
        "Report Object": f"""
            SELECT mres.e2e_sync_run_id, count(mres.sync_run_id)
            from {PREFIX}report_etl_status mres 
            WHERE mres.client_id = {client_id}
            and mres.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and (task = 'PAYOUT_REPORT_SYNC' or 
                task = 'COMMISSION_REPORT_WRAPPER_SYNC' or 
                task = 'COMMISSION_REPORT_SYNC' or 
                task = 'COMMISSION_SUMMARY_REPORT_SYNC' or
                task = 'QUOTA_ATTAINMENT_REPORT_SYNC' or
                task = 'SETTLEMENT_REPORT_SYNC'
            )
            GROUP BY mres.e2e_sync_run_id;
        """,
        "Databook L2": f"""
            SELECT mdes.e2e_sync_run_id, count(mdes.sync_run_id)
            from {PREFIX}databook_etl_status mdes 
            WHERE mdes.client_id = {client_id}
            and mdes.e2e_sync_run_id in ({str_e2e_sync_run_ids})
            and sync_type = 'COMMISSION_DB_SYNC'
            GROUP BY mdes.e2e_sync_run_id;
        """,
    }

    data = {
        e2e_sync_run_id: {stage_name: 0 for stage_name in queries.keys()}
        for e2e_sync_run_id in e2e_sync_run_ids
    }

    with connection.cursor() as cursor:
        for stage_name, query in queries.items():
            cursor.execute(query)
            results = cursor.fetchall()
            for e2e_sync_run_id, count in results:
                data[str(e2e_sync_run_id)][stage_name] = count

    logger.info("END: Getting past data from past e2e syncs for client %s", client_id)

    return data


def get_distribution_from_past_e2e_syncs(client_id, e2e_sync_run_ids):
    """
    Fetches past ETL data for multiple e2e_sync_run_ids and computes the 5th and 95th percentiles.
    """
    all_data = get_past_data_for_all_e2e_syncs(client_id, e2e_sync_run_ids)
    df = pd.DataFrame(all_data).T

    high_percentile = df.quantile(0.95).to_dict()  # 95th percentile
    low_percentile = df.quantile(0.05).to_dict()  # 5th percentile
    logger.info("Distribution computed from past e2e syncs for client %s", client_id)

    return high_percentile, low_percentile


def compare_past_data_with_current_data(past_high, past_low, current):
    """
    Compare the data to see if there is a deviation beyond ±20% of the percentiles.

    Args:
        past_high (dict): 95th percentile historical data.
        past_low (dict): 5th percentile historical data.
        current (dict): Current ETL data for the latest sync run.

    Returns:
        dict: A dictionary with deviation percentages and flags if deviation exceeds 20%.
    """
    deviation_results = {}
    is_anomaly = False

    for key in past_high.keys():
        high_percentile = past_high[key]
        low_percentile = past_low[key]
        current_value = current.get(key, 0)

        # Define acceptable range
        lower_bound = max(0, low_percentile * 0.8)  # Ensure it doesn't go negative
        upper_bound = high_percentile * 1.2

        # Compute deviation percentage (from midpoint of range)
        if current_value < lower_bound:
            deviation = ((lower_bound - current_value) / lower_bound) * 100
            status = "Below expected range"
            is_anomaly = True
        elif current_value > upper_bound:
            deviation = ((current_value - upper_bound) / upper_bound) * 100
            status = "Above expected range"
            is_anomaly = True
        else:
            deviation = "N/A"
            status = "Within expected range"

        deviation_results[key] = {
            "Low Percentile (5th)": int(low_percentile),
            "High Percentile (95th)": int(high_percentile),
            "Lower Bound": int(lower_bound),
            "Upper Bound": int(upper_bound),
            "Current Value": current_value,
            "Deviation Percentage": deviation,
            "Status": status,
        }

    return deviation_results, is_anomaly


def generate_dataframe_from_comparison_result(comparison_result):
    df = pd.DataFrame(comparison_result).T  # Convert dict to DataFrame and Transpose
    df.reset_index(inplace=True)
    df.rename(columns={"index": "ETL Process"}, inplace=True)  # Rename index column

    return df


def get_e2e_sync_run_ids_for_past_hours(hours):
    """
    1. Get the past running syncs and for what clients for the given amount of time.
    2. run the whole shebang for all the clients.
    """
    data = {}

    query = f"""
        select distinct(e2e_sync_run_id), client_id from {PREFIX}etl_sync_status
        where sync_status = 'complete'
        and task = 'End to End Sync'
        and audit is null
        and sync_start_time >= now() - interval '{hours} hours';
    """

    with connection.cursor() as cursor:
        cursor.execute(query)
        results = cursor.fetchall()

        for e2e, client_id in results:
            data[str(e2e)] = client_id

    return data


def calculate_deviation_data_for_e2e_sync(client_id, e2e_sync_run_id):
    """
    Calculate the deviation data for the given e2e sync.
    """
    logger.info(
        "BEGIN: Calculating deviation data for e2e sync %s for client %s",
        e2e_sync_run_id,
        client_id,
    )
    # get the data for the given e2e sync
    current_data = get_data_from_e2e(client_id, e2e_sync_run_id)

    # get the data for all the e2e syncs from the past 2 weeks
    past_e2e_sync_run_ids = fetch_past_e2e_sync_run_ids(client_id, e2e_sync_run_id)

    # calculate the high and low bounds for the distribution
    past_high, past_low = get_distribution_from_past_e2e_syncs(
        client_id, past_e2e_sync_run_ids
    )

    # calculate the deviation for the given e2e sync
    deviation_data, is_anomaly = compare_past_data_with_current_data(
        past_high, past_low, current_data
    )
    logger.info(
        "END: Calculated deviation data for e2e sync %s for client %s",
        e2e_sync_run_id,
        client_id,
    )

    return deviation_data, is_anomaly


@shared_task(base=EverCeleryBaseTask)
def detect_etl_anomalies(hours=2):
    from commission_engine.accessors.client_accessor import get_client

    """
    1. Get the past running syncs and for what clients for the given amount of time.
    2. run the whole shebang for all the clients.
    """
    data = {}
    all_dataframes = []

    logger.info("Getting e2e sync run ids for past %d hours", hours)
    e2e_sync_run_ids = get_e2e_sync_run_ids_for_past_hours(hours)
    # e2e_sync_run_ids = {"26c19e37-9780-4842-95e9-b43affe45657":205}
    logger.info("Processing %d e2e sync run ids", len(e2e_sync_run_ids))

    for e2e_sync_run_id, client_id in e2e_sync_run_ids.items():
        logger.info("Processing %s", e2e_sync_run_id)
        deviation_data, is_anomaly = calculate_deviation_data_for_e2e_sync(
            client_id, e2e_sync_run_id
        )

        if is_anomaly:
            client_name = get_client(client_id).name
            logger.info(
                "Anomaly detected for client %s for e2e sync %s Check the report for more details.",
                client_id,
                e2e_sync_run_id,
            )
            log_anomaly_with_nature(
                check_type="etl_task_count_anomaly",
                client_id=client_id,
                client_name=client_name,
                module="ETL Pipeline",
                alert_name="ETL task count anomaly",
                created_at=datetime.now().isoformat(),
                description="Anomaly detected for client %s for e2e sync %s .",
                alert_key=f"{client_name}_{e2e_sync_run_id}_task_count_anomaly",
                logger_email_id="<EMAIL>",
                assignee="etlinfra",
                cc_email_id="",
                env=os.getenv("ENV", "LOCALDEV").lower(),
                metadata="",
            )
        else:
            logger.info(
                "No anomaly detected for client %s for e2e sync %s",
                client_id,
                e2e_sync_run_id,
            )

            log_heartbeat(
                client_id=client_id,
                client_name=client_name,
                check_type="etl_task_count_anomaly",
                env=os.getenv("ENV", "LOCALDEV"),
                module="ETL Pipeline",
            )

        data[e2e_sync_run_id] = deviation_data
        df = generate_dataframe_from_comparison_result(deviation_data)
        df.insert(0, "E2E Sync Run ID", e2e_sync_run_id)
        all_dataframes.append(df)

        # Add a delimiter row
        all_dataframes.append(
            pd.DataFrame([[""] * len(df.columns)], columns=df.columns)
        )


# if __name__ == "__main__":
#     router_queue_name = get_common_queue_name(CommonQueues.ROUTER.value)
#     detect_etl_anomalies.si(hours=2).set(queue=router_queue_name).apply_async(
#         compression="lzma", serializer="pickle"
#     )
