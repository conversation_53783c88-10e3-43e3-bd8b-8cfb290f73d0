import logging
import os
from datetime import datetime, timezone

from celery import group, shared_task

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.commission_anomalies.helper import (
    PRIME_LOGIC_EMAIL_ID,
    AnomalyType,
    create_alert_data,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_for_empty_quotas(client_id):
    """
    This function checks for quotas used in the published plans without any data in the entire
    """
    from commission_engine.accessors.client_accessor import get_client
    from commission_engine.utils.date_utils import get_fiscal_year

    client = get_client(client_id)
    client_fiscal_start_month = client.fiscal_start_month
    client_name = client.name
    curr_dt = datetime.now(timezone.utc)
    env = os.getenv("ENV", "LOCALDEV").lower()
    fiscal_year = int(get_fiscal_year(client_fiscal_start_month, curr_dt))

    logger.info(
        f"Checking for empty quotas for client {client_id} in fiscal year {fiscal_year}"
    )
    zero_team_quota_payees, zero_quota_payees = get_empty_quotas_in_fiscal_year(
        client_id, fiscal_year, client_fiscal_start_month
    )

    alerts = []

    alerts.extend(
        create_zero_quota_alert_data(
            client_id, fiscal_year, zero_team_quota_payees, True  # noqa: FBT003
        )
    )
    alerts.extend(
        create_zero_quota_alert_data(
            client_id, fiscal_year, zero_quota_payees, False  # noqa: FBT003
        )
    )

    created_at = datetime.now(timezone.utc)
    if alerts:
        logger.info(f"Found {len(alerts)} Empty Quota anomalies for client {client_id}")
    else:
        logger.info(f"No Empty Quota anomalies found for client {client_id}")

    if alerts:
        for alert in alerts:
            log_anomaly_with_nature(
                check_type=alert["check_type"],
                client_id=client_id,
                client_name=client_name,
                module="scripts",
                alert_name=alert["alert_name"],
                created_at=created_at,
                description=alert["description"],
                alert_key=alert["alert_key"],
                logger_email_id=PRIME_LOGIC_EMAIL_ID,
                assignee=PRIME_LOGIC_EMAIL_ID,
                cc_email_id="",
                env=env,
                metadata=alert["metadata"],
            )
    else:
        log_heartbeat(
            client_id=client_id,
            client_name=client_name,
            check_type="zero_quota_data",
            env=env,
            module="scripts",
        )


def check_zero_quota_record(quota_record):
    return all(record["quota"] == 0 for record in quota_record)


def create_zero_quota_alert_data(client_id, fiscal_year, quota_data, is_team_quota):
    alert_key_prefix = f"client_{client_id}_zero_quota_payee"
    alerts = []
    for payee, quota in quota_data:
        if is_team_quota:
            alert_key_prefix += "_team"
        alert_key = alert_key_prefix + f"_{payee}_{quota}_{fiscal_year}"
        alerts.append(
            create_alert_data(
                anomaly_type=AnomalyType.QUOTA_DATA_ZERO,
                alert_key=alert_key,
                check_type="zero_quota_data",
                metadata={
                    "affected_data": {
                        "payee_email_id": payee,
                        "quota_name": quota,
                        "fiscal_year": fiscal_year,
                    },
                    "expected_value": {},
                    "actual_value": {},
                    "additional_context": {},
                },
            )
        )
    return alerts


def get_empty_quotas_in_fiscal_year(  # noqa: PLR0912,PLR0915
    client_id, fiscal_year, client_fiscal_start_month
):
    from commission_engine.utils.date_utils import get_date_range_for_period
    from spm.accessors.commission_plan_accessor import (
        CommissionPlanAccessor,
        PlanCriteriaAccessor,
    )
    from spm.accessors.config_accessors.employee_accessor import PlanDetailsAllAccessor
    from spm.accessors.quota_acessors import EmployeeQuotaAccessor

    start_date, end_date = get_date_range_for_period(
        "Year", fiscal_year, client_fiscal_start_month
    )
    plan_ids = CommissionPlanAccessor(
        client_id
    ).get_all_published_plans_valid_in_date_range(start_date, end_date)
    criteria_data = PlanCriteriaAccessor(client_id).get_quotas_used_in_plans(plan_ids)
    payee_data = PlanDetailsAllAccessor(client_id).get_employees_in_plans(
        plan_ids, start_date, end_date
    )
    plan_quota_map = {}
    plan_team_quota_map = {}
    payee_quota_map = {}
    payee_team_quota_map = {}
    for rec in criteria_data:
        plan_id = rec["plan_id"]
        quota_name = rec["criteria_data__quota_name"]
        criteria_type = rec["criteria_type"]

        if criteria_type == "CustomTeam":
            if plan_id not in plan_team_quota_map:
                plan_team_quota_map[plan_id] = []
            plan_team_quota_map[plan_id].append(quota_name)
        else:
            if plan_id not in plan_quota_map:
                plan_quota_map[plan_id] = []
            plan_quota_map[plan_id].append(quota_name)

    for rec in payee_data:
        plan_id = rec["plan_id"]
        payee_email_id = rec["employee_email_id"]
        quotas = plan_quota_map.get(plan_id, [])
        team_quotas = plan_team_quota_map.get(plan_id, [])

        if payee_email_id not in payee_quota_map:
            payee_quota_map[payee_email_id] = set()
        payee_quota_map[payee_email_id].update(quotas)

        if payee_email_id not in payee_team_quota_map:
            payee_team_quota_map[payee_email_id] = set()
        payee_team_quota_map[payee_email_id].update(team_quotas)

    all_quota_details = EmployeeQuotaAccessor(client_id).get_all_quotas_in_fiscal_year(
        fiscal_year
    )

    zero_team_quota_payees = []
    zero_quota_payees = []

    payee_quota_record_map = {}
    payee_team_quota_record_map = {}
    for rec in all_quota_details:
        payee_email_id = rec["employee_email_id"]
        quota_name = rec["quota_category_name"]
        is_team = rec["is_team_quota"]
        if is_team:
            if payee_email_id not in payee_team_quota_record_map:
                payee_team_quota_record_map[payee_email_id] = {}
            if quota_name not in payee_team_quota_record_map[payee_email_id]:
                payee_team_quota_record_map[payee_email_id][quota_name] = rec[
                    "schedule_quota"
                ]
        else:
            if payee_email_id not in payee_quota_record_map:
                payee_quota_record_map[payee_email_id] = {}
            if quota_name not in payee_quota_record_map[payee_email_id]:
                payee_quota_record_map[payee_email_id][quota_name] = rec[
                    "schedule_quota"
                ]

    for payee, quotas in payee_quota_map.items():
        for quota in quotas:
            if (
                payee not in payee_quota_record_map
                or quota not in payee_quota_record_map[payee]
                or check_zero_quota_record(payee_quota_record_map[payee][quota])
            ):
                zero_quota_payees.append((payee, quota))  # noqa: PERF401

    for payee, quotas in payee_team_quota_map.items():
        for quota in quotas:
            if (
                payee not in payee_team_quota_record_map
                or quota not in payee_team_quota_record_map[payee]
                or check_zero_quota_record(payee_team_quota_record_map[payee][quota])
            ):
                zero_team_quota_payees.append((payee, quota))  # noqa: PERF401

    return zero_team_quota_payees, zero_quota_payees


@shared_task(base=EverCeleryBaseTask)
def detect_empty_quotas(client_id):
    """
    This function detects empty quotas for a given client id.
    """
    logger.info(f"Detecting empty quotas for client {client_id}")
    start_time = datetime.now(timezone.utc)
    check_for_empty_quotas(client_id)
    end_time = datetime.now(timezone.utc)
    logger.info(
        f"Detecting empty quotas for client {client_id} completed in {end_time - start_time}"
    )


@shared_task(base=EverCeleryBaseTask)
def detect_empty_quotas_wrapper(exclude_client_ids=None):
    """
    This function detects empty quotas for all clients except the ones in the exclude_client_ids list.
    """
    if exclude_client_ids is None:
        exclude_client_ids = []
    from commission_engine.accessors.client_accessor import (
        get_client_id_subscription_plan_map,
    )

    client_data = get_client_id_subscription_plan_map(exclude_client_ids)
    task_group = TaskGroupEnum.ANOMALY_DETECT.value

    task = group(
        [
            detect_empty_quotas.si(client_id).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id, subscription_plan or "BASIC", task_group
                )
            )
            for client_id, subscription_plan in client_data.items()
        ]
    )
    task.apply_async(compression="lzma", serializer="pickle")
