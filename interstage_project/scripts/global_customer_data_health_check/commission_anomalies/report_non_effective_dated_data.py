import logging
import os
from datetime import datetime
from typing import Optional

from celery import group, shared_task
from django.utils import timezone

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.alert_mapping import (
    get_alert_nature_and_severity,
)
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly,
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.commission_anomalies.helper import (
    PRIME_LOGIC_EMAIL_ID,
    AnomalyType,
)

logger = logging.getLogger(__name__)

commission_report = {
    "payee_name": "payee_email_id",
    "plan_name": "commission_plan_id",
    "plan_type": "commission_plan_id",
    "databook_name": "criteria_id",
    "datasheet_name": "criteria_id",
    "criteria_name": "criteria_id",
}

settlement_report = {
    "payee_name": "payee_email_id",
    "plan_name": "plan_id",
    "settlement_criteria_name": "settlement_rule_id",
    "criteria_name": "criteria_id",
    "settlement_databook": "settlement_rule_id",
    "settlement_datasheet": "settlement_rule_id",
}

quota_attainment_report = {
    "payee_name": "payee_email_id",
    "exit_date": "payee_email_id",
}


payout_snapshot = {
    "plan_name": "plan_id",
    "plan_type": "plan_id",
    "databook_name": "criteria_id",
    "datasheet_name": "criteria_id",
    "data_full_name": "payee_email_id",
    "criteria_name": "criteria_id",
}

settlement_snapshot = {
    "criteria_name": "criteria_id",
    "payee_name": "payee_email_id",
    "plan_name": "plan_id",
    "plan_type": "plan_id",
    "settlement_criteria_name": "settlement_rule_id",
    "settlement_databook": "settlement_rule_id",
    "settlement_datasheet": "settlement_rule_id",
}


report_obj_variable_map = {
    "commission": commission_report,
    "settlement": settlement_report,
    "quota_attainment": quota_attainment_report,
    "inter_commission": commission_report,
    "inter_quota_attainment": quota_attainment_report,
}

snapshot_variable_map = {
    "payout": payout_snapshot,
    "settlement": settlement_snapshot,
    "inter_commission": payout_snapshot,
}


class ReportNonEffectiveDatedData:
    def __init__(self, client_id, client_name=None, client_features=None) -> None:
        from commission_engine.accessors.client_accessor import get_client_for_id
        from commission_engine.utils.general_data import ReportObject

        self.client_id = client_id
        self.client_name = client_name
        self.created_at = timezone.now()
        self.env = os.getenv("ENV", "LOCALDEV").lower()
        if (client_name is None) or (client_features is None):
            client = get_client_for_id(client_id)
            self.client_name = client.name if client else "Unknown"
            features = client.client_features if client else {}
            self.expose_comm_reports_in_plan = bool(
                features.get("expose_comm_reports_in_plan", False)
            )
        self.expose_comm_reports_in_plan = client_features.get(
            "expose_comm_reports_in_plan", False
        )
        self.reports = {
            "commission": ReportObject.COMMISSION,
            "settlement": ReportObject.SETTLEMENT,
            "quota_attainment": ReportObject.QUOTA_ATTAINMENT,
            "inter_commission": ReportObject.INTER_COMMISSION,
            "inter_quota_attainment": ReportObject.INTER_QUOTA_ATTAINMENT,
        }

    def get_base_query(self, table_name, grp_by_cols, col_to_check):
        cols_list = []
        for each_col in grp_by_cols:
            cols_list.append(f"data:{each_col}")  # noqa: PERF401
        query = f"""
            SELECT {', '.join(cols_list)}, COUNT( distinct DATA:{col_to_check}) as record_count
            FROM {table_name}
            WHERE knowledge_end_date is null
            AND (is_deleted = False or is_deleted is null)
            GROUP BY {', '.join(cols_list)}
            HAVING COUNT(distinct DATA:{col_to_check}) > 1"""  # noqa: S608
        return query

    def get_base_query_for_snapshot(self, table_name, grp_by_cols, col_to_check):
        query = f"""
            SELECT {', '.join(grp_by_cols)}, COUNT( distinct ADDITIONAL_DATA:{col_to_check}) as record_count
            FROM {table_name}
            GROUP BY {', '.join(grp_by_cols)}
            HAVING COUNT(distinct ADDITIONAL_DATA:{col_to_check}) > 1"""  # noqa: S608
        return query

    def get_table_cols_map_for_report(self, report_object):
        from everstage_ddd.snowflake_reports.snowflake_report_utils import (
            get_report_object_table_name,
        )

        report_obj = self.reports[report_object]
        report_object_data_table = get_report_object_table_name(
            self.client_id,
            report_obj.value,
            report_obj.table_name,
        )
        columns_map = report_obj_variable_map[report_object]
        return (report_object_data_table, columns_map)

    def get_table_cols_map_for_snapshot(self, snapshot):
        if snapshot == "payout":
            table = f"payout_snapshot_data_{self.client_id}"
        elif snapshot == "settlement":
            table = f"settlement_snapshot_data_{self.client_id}"
        elif snapshot == "inter_commission":
            table = f"inter_comm_snapshot_data_{self.client_id}"

        columns_map = snapshot_variable_map[snapshot]
        return (table, columns_map)

    def _create_alert_data(
        self, check_type: str, entry: tuple, anomaly_type: AnomalyType
    ):
        return {
            "client_id": self.client_id,
            "client_name": self.client_name,
            "module": "reports",
            "check_type": check_type,
            "alert_name": f"{anomaly_type.value['alert_key'].replace('_', ' ').title()}",
            "severity": "high",
            "created_at": self.created_at,
            "description": anomaly_type.value["description"],
            "alert_key": f"client_{self.client_id}_report_non_effective_duplicates",
            "logger_email_id": PRIME_LOGIC_EMAIL_ID,
            "assignee": PRIME_LOGIC_EMAIL_ID,
            "cc_email_id": "",
            "env": self.env,
            "metadata": {
                "affected_data": entry,
                "expected_value": {},
                "actual_value": {},
                "additional_context": {},
            },
        }

    def process_report_object(self, report_object):
        from commission_engine.database.snowflake_connection import (
            create_snowpark_session,
        )

        anomalies = []
        (table_name, columns_map) = self.get_table_cols_map_for_report(report_object)
        with create_snowpark_session(client_id=self.client_id) as snowpark_session:
            for col_to_check, grp_by_cols in columns_map.items():
                cols_list = []
                if isinstance(grp_by_cols, list):
                    cols_list = grp_by_cols
                else:
                    cols_list = [grp_by_cols]
                query = self.get_base_query(table_name, cols_list, col_to_check)
                result = snowpark_session.sql(query).collect()
                for rec in result:
                    key = ""
                    if isinstance(grp_by_cols, list):
                        for col in grp_by_cols:
                            key += rec[f"DATA:{col.upper()}"]
                            key += " ## "
                    else:
                        key = rec[f"DATA:{grp_by_cols.upper()}"]
                    alert_obj = self._create_alert_data(
                        "report_non_effective_data_duplicate",
                        {
                            "report": report_object,
                            "column": col_to_check,
                            "id_columns": grp_by_cols,
                            "id_values": key,
                            "object_type": "report",
                        },
                        AnomalyType.REPORT_NON_EFF_DATA_DUPLICATE,
                    )
                    anomalies.append(alert_obj)
        return anomalies

    def process_snapshot_object(self, snapshot):
        from commission_engine.database.snowflake_connection import (
            create_snowpark_session,
        )

        anomalies = []
        (table_name, columns_map) = self.get_table_cols_map_for_snapshot(snapshot)
        with create_snowpark_session(client_id=self.client_id) as snowpark_session:
            for col_to_check, grp_by_cols in columns_map.items():
                cols_list = []
                if isinstance(grp_by_cols, list):
                    cols_list = grp_by_cols
                else:
                    cols_list = [grp_by_cols]
                query = self.get_base_query_for_snapshot(
                    table_name, cols_list, col_to_check
                )
                result = snowpark_session.sql(query).collect()
                for rec in result:
                    key = ""
                    if isinstance(grp_by_cols, list):
                        for col in grp_by_cols:
                            key += rec[f"{col.upper()}"]
                            key += " ## "
                    else:
                        key = rec[f"{grp_by_cols.upper()}"]
                    alert_obj = self._create_alert_data(
                        "snapshot_non_effective_data_duplicate",
                        {
                            "report": snapshot,
                            "column": col_to_check,
                            "id_columns": grp_by_cols,
                            "id_values": key,
                            "object_type": "snapshot",
                        },
                        AnomalyType.REPORT_NON_EFF_DATA_DUPLICATE,
                    )
                    anomalies.append(alert_obj)
        return anomalies

    def process_all_reports_snapshots(self):
        anomalies = []
        for report in self.reports.keys():  # noqa: SIM118
            if not self.expose_comm_reports_in_plan and (
                report in ["inter_commission", "inter_quota_attainment"]
            ):
                continue
            logger.info(
                "Processing {} Report for client {}".format(report, self.client_id)
            )
            duplicates = self.process_report_object(report)
            anomalies.extend(duplicates)

        for snapshot in snapshot_variable_map.keys():  # noqa: SIM118
            if not self.expose_comm_reports_in_plan and (
                snapshot == "inter_commission"
            ):
                continue
            logger.info(
                "Processing {} Snapshot for client {}".format(snapshot, self.client_id)
            )
            duplicates = self.process_snapshot_object(snapshot)
            anomalies.extend(duplicates)

        if anomalies:
            for anomaly in anomalies:
                log_anomaly_with_nature(**anomaly)
        else:
            log_heartbeat(
                client_id=self.client_id,
                client_name=self.client_name,
                check_type="report_non_effective_data_duplicate",
                env=self.env,
                module="scripts",
            )
        logger.info(
            "Report Non Effective dated data Anamoly count for client {} - {}".format(
                self.client_id, len(anomalies)
            )
        )


@shared_task(base=EverCeleryBaseTask)
def check_non_effective_dated_data_dups(
    client_id, client_name=None, client_features=None
):
    try:
        logger.info(
            "Running Report Non Effective dated data check for client - {}".format(
                client_id
            )
        )
        start_time = datetime.now(timezone.utc)
        if client_name:
            rd = ReportNonEffectiveDatedData(
                client_id, client_name=client_name, client_features=client_features
            )
        else:
            rd = ReportNonEffectiveDatedData(client_id)
        rd.process_all_reports_snapshots()
        end_time = datetime.now(timezone.utc)
        logger.info(
            "Completed Report Non Effective dated data check for client - {} in {}".format(
                client_id, end_time - start_time
            )
        )
    except Exception:
        logger.exception(
            "Report Non Effective dated data Exception for client {}".format(client_id)
        )


@shared_task(base=EverCeleryBaseTask)
def check_non_effective_dated_data_wrapper(client_ids: Optional[list] = None):
    from commission_engine.accessors.client_accessor import (
        get_active_clients_features_excluding_churned,
    )

    tasks = []
    task_group_name = TaskGroupEnum.ANOMALY_DETECT.value
    if not client_ids:
        clients = get_active_clients_features_excluding_churned()
        for client_data in clients:
            client_id = client_data[0]
            client_name = client_data[1]
            features = client_data[2]
            subscription_plan = features.get("subscription_plan", "BASIC")
            queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, task_group_name
            )
            task = check_non_effective_dated_data_dups.si(
                client_id, client_name, features
            ).set(queue=queue_name)
            tasks.append(task)
        group(tasks).apply_async(compression="lzma", serializer="pickle")
