import decimal
import logging
import os
from datetime import datetime, timedelta
from typing import Optional

from celery import group, shared_task
from django.utils import timezone

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.commission_anomalies.helper import (  # get_monitored_clients,
    PRIME_LOGIC_EMAIL_ID,
    AnomalyType,
)

logger = logging.getLogger(__name__)


class PayoutValueCheck:
    def __init__(self, client_id, curr_date=None, payees=None, client_name=None):
        from commission_engine.accessors.client_accessor import get_client_for_id
        from everstage_etl.upstream_utils import get_current_prev_psd_ped

        self.client_id = client_id
        self.curr_date = curr_date if curr_date is not None else timezone.now()
        self.payees = payees if payees is not None else []
        self.freq_payee_map = {}
        period_data = get_current_prev_psd_ped(client_id, self.curr_date)
        self.psd_ped = period_data["current_ps_pe"]
        self.client_name = client_name
        self.created_at = timezone.now()
        self.env = os.getenv("ENV", "LOCALDEV").lower()
        if self.client_name is None:
            client = get_client_for_id(client_id)
            self.client_name = client.name if client else "Unknown"
        self.get_payees_and_freq_map()

    def get_valid_payees(self):
        from spm.models.config_models.employee_models import EmployeePayroll

        query = """SELECT DISTINCT 1 AS temporal_id, epd.employee_email_id, epd.payout_frequency
            FROM employee emp
            JOIN employee_payroll_details epd ON emp.employee_email_id = epd.employee_email_id
            AND emp.client_id = epd.client_id
            JOIN plan_details pd ON emp.employee_email_id = pd.employee_email_id
            AND emp.client_id = pd.client_id
            AND pd.effective_start_date <= %s
            AND (pd.effective_end_date >= %s or pd.effective_end_date ISNULL)
            WHERE emp.client_id = %s
            AND (emp.last_commission_date ISNULL OR emp.last_commission_date >= %s)
            AND emp.knowledge_end_date ISNULL
            AND NOT epd.is_deleted AND NOT emp.is_deleted
            AND epd.knowledge_end_date ISNULL
            AND epd.effective_start_date <= %s
            AND (epd.effective_end_date >= %s or epd.effective_end_date ISNULL)"""
        date_string = str(self.curr_date)
        params = [
            date_string,
            date_string,
            self.client_id,
            date_string,
            date_string,
            date_string,
        ]
        emp_payrolls = list(EmployeePayroll.objects.raw(query, params))
        return emp_payrolls

    def get_payees_and_freq_map(self):
        from spm.accessors.config_accessors.employee_accessor import (
            EmployeePayrollAccessor,
        )

        if not self.payees:
            emp_payroll = self.get_valid_payees()
        else:
            employee_payroll_acc = EmployeePayrollAccessor(self.client_id)
            emp_payroll = employee_payroll_acc.get_employee_payroll(
                self.curr_date, self.payees, as_dicts=False
            )

        for emp in emp_payroll:
            self.payees.append(emp.employee_email_id)  # type: ignore
            freq = emp.payout_frequency.lower()
            if freq not in self.freq_payee_map:
                self.freq_payee_map[freq] = []
            self.freq_payee_map[freq].append(emp.employee_email_id)

    def get_past_periods_in_last_day(self):
        from commission_engine.accessors.payout_status_accessor import (
            PayoutStatusAccessor,
        )
        from commission_engine.utils.date_utils import start_of_day

        kd = start_of_day(self.curr_date - timedelta(days=1))
        ps_accessor = PayoutStatusAccessor(self.client_id)
        payee_peds = ps_accessor.get_all_modified_entries_after_kd(kd)
        ped_payees_map = {}
        for payee_ped in payee_peds:
            ped = payee_ped["period_end_date"]
            payee = payee_ped["payee_email_id"]
            if ped not in ped_payees_map:
                ped_payees_map[ped] = set()
            ped_payees_map[ped].add(payee)
        return ped_payees_map

    def get_final_ped_payee_map(self):
        past_sync_map = self.get_past_periods_in_last_day()
        ped_payee_map = {}
        for freq, payees in self.freq_payee_map.items():
            if freq in self.psd_ped:
                (_, ped) = self.psd_ped[freq]
                if ped in ped_payee_map:
                    ped_payee_map[ped].extend(payees)
                else:
                    ped_payee_map[ped] = payees

        for ped, payee_set in past_sync_map.items():
            if ped in ped_payee_map:
                payees_list = list(set(list(payee_set) + ped_payee_map[ped]))
                ped_payee_map[ped] = payees_list
            else:
                ped_payee_map[ped] = list(payee_set)
        return ped_payee_map

    def _create_alert_data(
        self, check_type: str, entry: tuple, anomaly_type: AnomalyType
    ):
        return {
            "client_id": self.client_id,
            "client_name": self.client_name,
            "module": "payout status",
            "check_type": check_type,
            "alert_name": f"{anomaly_type.value['alert_key'].replace('_', ' ').title()}",
            "severity": "high",
            "created_at": self.created_at,
            "description": anomaly_type.value["description"],
            "alert_key": f"client_{self.client_id}_data_missing_{entry[0]}_{entry[1]}",
            "logger_email_id": PRIME_LOGIC_EMAIL_ID,
            "assignee": PRIME_LOGIC_EMAIL_ID,
            "cc_email_id": "",
            "env": self.env,
            "metadata": {
                "affected_data": {
                    "payee_email_id": entry[0],
                    "period_end_date": entry[1],
                },
                "expected_value": {},
                "actual_value": {},
                "additional_context": {},
            },
        }

    def compare_payout_and_status(self):  # noqa: PLR0912
        from commission_engine.accessors.payout_status_accessor import (
            PayoutStatusAccessor,
        )
        from commission_engine.utils.date_utils import convert_str_to_date, end_of_day
        from spm.services.settlement_actions_service.settlement_total_service import (
            SettlementTotalService,
        )

        threshold = 0.1
        payout_status_acc = PayoutStatusAccessor(self.client_id)
        payout_status_missing = []
        payout_data_mismatch = []
        anomoly_data = []
        ped_payee_map = self.get_final_ped_payee_map()
        for ped, payees in ped_payee_map.items():
            payout_data_dict = {}
            payout_status_data_dict = {}
            logger.info(
                "Running Payout Value Check script for {} client - {} period".format(
                    self.client_id, ped
                )
            )
            sts = SettlementTotalService(self.client_id, ped, payees)
            payout_data = sts.get_payout_data()
            for pd in payout_data:
                payout_data_dict[pd["key"]] = pd
            payout_status_data = payout_status_acc.get_payee_period_records(payees, ped)
            for ps_d in payout_status_data:
                payout_status_data_dict[ps_d["payee_email_id"]] = ps_d

            for payee in payees:
                emp_ps_data = payout_status_data_dict.get(payee)
                emp_payout_data = payout_data_dict.get(payee)
                if emp_ps_data and emp_payout_data:
                    total_diff = (
                        decimal.Decimal(emp_payout_data["total_payout"])
                        - emp_ps_data["total_payout"]
                    )
                    fx_rate_diff = (
                        decimal.Decimal(emp_payout_data["fx_rate"])
                        - emp_ps_data["fx_rate"]
                    )
                    paid_amt_diff = (
                        decimal.Decimal(emp_payout_data["paid_amount"])
                        - emp_ps_data["paid_amount"]
                    )
                    processed_amt_diff = (
                        decimal.Decimal(emp_payout_data["processed_amount"])
                        - emp_ps_data["processed_amount"]
                    )
                    ignored_amt_diff = (
                        decimal.Decimal(emp_payout_data["ignored_amount"])
                        - emp_ps_data["ignored_amount"]
                    )
                    pending_amt_diff = (
                        decimal.Decimal(emp_payout_data["pending_amount"])
                        - emp_ps_data["pending_amount"]
                    )
                    commission_diff = (
                        emp_payout_data["payout_split_up"]["commission"]
                        - emp_ps_data["payout_split_up"]["commission"]
                    )
                    settlement_diff = (
                        emp_payout_data["payout_split_up"]["settlement"]
                        - emp_ps_data["payout_split_up"]["settlement"]
                    )
                    commission_adj_diff = (
                        emp_payout_data["payout_split_up"]["commission_adj"]
                        - emp_ps_data["payout_split_up"]["commission_adj"]
                    )
                    draw_diff = (
                        emp_payout_data["payout_split_up"]["draw_adj"]
                        - emp_ps_data["payout_split_up"]["draw_adj"]
                    )
                    payout_psd = convert_str_to_date(
                        emp_payout_data["period_start_date"]
                    )
                    payout_ped = end_of_day(
                        convert_str_to_date(emp_payout_data["period_end_date"])
                    )

                    if (
                        (payout_ped == emp_ps_data["period_end_date"])
                        and (payout_psd == emp_ps_data["period_start_date"])
                        and (abs(draw_diff) <= threshold)
                        and (abs(commission_adj_diff) <= threshold)
                        and (abs(settlement_diff) <= threshold)
                        and (abs(commission_diff) <= threshold)
                        and (abs(pending_amt_diff) <= threshold)
                        and (abs(ignored_amt_diff) <= threshold)
                        and (abs(processed_amt_diff) <= threshold)
                        and (abs(paid_amt_diff) <= threshold)
                        and (abs(fx_rate_diff) <= threshold)
                        and (abs(total_diff) <= threshold)
                    ):
                        pass
                    else:
                        payout_data_mismatch.append((payee, ped))
                elif emp_payout_data and emp_ps_data is None:
                    payout_status_missing.append((payee, ped))

        # anomalies for payout status missing
        for entry in payout_status_missing:
            anomoly_data.append(  # noqa: PERF401
                self._create_alert_data(
                    "payout_status_missing", entry, AnomalyType.PAYOUT_STATUS_MISSING
                )
            )

        # payout status and statement value mismatch
        for entry in payout_data_mismatch:
            anomoly_data.append(  # noqa: PERF401
                self._create_alert_data(
                    "payout_and_statement_value_mismatch",
                    entry,
                    AnomalyType.PAYOUT_VALUE_MISMATCH,
                )
            )

        # log anomalies
        if anomoly_data:
            for anomaly in anomoly_data:
                log_anomaly_with_nature(**anomaly)
        else:
            log_heartbeat(
                client_id=self.client_id,
                client_name=self.client_name,
                check_type="payout_and_statement_value_mismatch",
                env=self.env,
                module="scripts",
            )
        logger.info(
            "Payout Value Check Anomaly Count for client - {}, {}".format(
                self.client_id, len(anomoly_data)
            )
        )


@shared_task(base=EverCeleryBaseTask)
def compare_payout_status_and_statements(client_id, client_name=None):
    try:
        logger.info(
            "Running Payout value mismatch check for client - {}".format(client_id)
        )
        start_time = datetime.now(timezone.utc)
        if client_name:
            pv = PayoutValueCheck(client_id, client_name=client_name)
        else:
            pv = PayoutValueCheck(client_id)
        pv.compare_payout_and_status()
        end_time = datetime.now(timezone.utc)
        logger.info(
            "Payout value mismatch check for client - {} completed in {}".format(
                client_id, end_time - start_time
            )
        )
    except Exception:
        logger.exception("Payout value Exception for client {}".format(client_id))


@shared_task(base=EverCeleryBaseTask)
def compare_ps_and_statement_wrapper(client_ids: Optional[list] = None):
    from commission_engine.accessors.client_accessor import (
        get_active_clients_features_excluding_churned,
    )

    tasks = []
    task_group_name = TaskGroupEnum.ANOMALY_DETECT.value
    if not client_ids:
        clients = get_active_clients_features_excluding_churned()
        for client_data in clients:
            client_id = client_data[0]
            client_name = client_data[1]
            features = client_data[2]
            subscription_plan = features.get("subscription_plan", "BASIC")
            queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, task_group_name
            )
            task = compare_payout_status_and_statements.si(client_id, client_name).set(
                queue=queue_name
            )
            tasks.append(task)
        group(tasks).apply_async(compression="lzma", serializer="pickle")
