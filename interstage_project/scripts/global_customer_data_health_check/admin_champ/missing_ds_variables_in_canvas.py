"""
Check Missing Datasheet Variables that are used in Canvas (Commission, Forecast,
and Settlement criteria)
"""

import django

django.setup()
import json
import logging
import os
from abc import ABC, abstractmethod
from collections import defaultdict
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple

from celery import group, shared_task

from commission_engine.accessors.databook_accessor import (
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.settlement_accessor import (
    CommissionSettlementMapAccessor,
    SettlementRuleAccessor,
)
from commission_engine.utils.criteria_data_utils import (
    get_datasheet_variables_from_criteria_config_sort_cols,
    get_datasheet_variables_from_criteria_data,
)
from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanCriteriaAccessor,
)
from spm.accessors.forecast_plan_accessor import (
    ForecastPlanAccessor,
    ForecastPlanCriteriaAccessor,
)

logger = logging.getLogger(__name__)


def is_get_env() -> str:
    """Returns the environment name, defaulting to 'local' if not set."""
    return os.environ.get("ENV", "local")


class BaseDatasheetVariableChecker(ABC):
    def __init__(self, client_id: int) -> None:
        self.client_id = client_id

    @abstractmethod
    def get_datasheet_variables(
        self, criteria: Dict[str, Any]
    ) -> Tuple[Optional[str], Set[str], Optional[str]]:
        """
        Must return a tuple: (datasheet_id, set of used variables, error message if any)
        """
        pass

    def check_datasheet_variable_existence(
        self, criteria: Dict[str, Any]
    ) -> Tuple[bool, Optional[str], Set[str], Optional[str]]:
        ds_id, cols_used, err = self.get_datasheet_variables(criteria)
        is_active, ds_return, missing, err2 = (
            self.validate_datasheet_variables_used_in_criteria(ds_id, cols_used)
        )
        error_message = err if err else err2
        return is_active, ds_return, missing, error_message

    def validate_datasheet_variables_used_in_criteria(
        self, ds_id: Optional[str], used_columns: Set[str]
    ) -> Tuple[bool, Optional[str], Set[str], Optional[str]]:
        """
        Validates that all used columns exist in the datasheet.
        Returns: (check_status, ds_id (if error), missing_columns, error message)
        """
        try:
            if not ds_id or not used_columns:
                return True, None, set(), None

            existing = DatasheetVariableAccessor(
                self.client_id
            ).get_variables_for_ds_ids(
                datasheet_ids=[ds_id],
                as_dicts=True,
                columns=["system_name", "datasheet_id"],
            )
            existing_map: Dict[str, Set[str]] = defaultdict(set)
            for entry in existing:
                existing_map[str(entry["datasheet_id"])].add(entry["system_name"])

            missing = used_columns - existing_map.get(str(ds_id), set())
            if missing:
                return False, str(ds_id), missing, None
            return True, None, set(), None
        except Exception as e:
            logger.exception(
                f"Error validating datasheet_id {ds_id} for client_id {self.client_id}"
            )
            return False, str(ds_id), used_columns, str(e)


class CommissionDatasheetVariableChecker(BaseDatasheetVariableChecker):
    def get_datasheet_variables(
        self, criteria: Dict[str, Any]
    ) -> Tuple[Optional[str], Set[str], Optional[str]]:
        default_criteria_columns = {"commission", "tierName", "quotaErosion"}
        try:
            criteria_data = criteria["criteria_data"]
            ds_id = criteria_data.get("datasheet_id")
            if not ds_id:
                return None, set(), None

            vars_used = (
                get_datasheet_variables_from_criteria_data(
                    self.client_id, criteria_data
                )
                | get_datasheet_variables_from_criteria_config_sort_cols(
                    criteria["criteria_config"]
                )
                | (set(criteria.get("criteria_column", [])) - default_criteria_columns)
                | (set(criteria.get("simulate_column", [])) - default_criteria_columns)
            )
            criteria_id = criteria.get("criteria_id")
            join_keys, err = self.get_commission_join_keys_used_in_settlements(
                criteria_id
            )
            vars_used.update(join_keys)
            return ds_id, vars_used, err
        except Exception as e:
            logger.exception(
                f"Error processing commission criteria_id {criteria.get('criteria_id')} for client_id {self.client_id}"
            )
            return None, set(), str(e)

    def get_commission_join_keys_used_in_settlements(
        self, criteria_id: str
    ) -> Tuple[Set[str], Optional[str]]:
        try:
            rule_ids = CommissionSettlementMapAccessor(
                self.client_id
            ).get_settlement_rule_ids_mapped_to_criteria(criteria_id)
            if not rule_ids:
                return set(), None
            return (
                SettlementRuleAccessor(self.client_id).get_commission_join_keys(
                    rule_ids
                ),
                None,
            )
        except Exception as e:
            logger.exception(
                f"Error retrieving commission join keys for criteria_id {criteria_id} for client_id {self.client_id}"
            )
            return set(), str(e)


class ForecastDatasheetVariableChecker(BaseDatasheetVariableChecker):
    def get_datasheet_variables(
        self, criteria: Dict[str, Any]
    ) -> Tuple[Optional[str], Set[str], Optional[str]]:
        default_criteria_columns = {"commission", "tierName", "quotaErosion"}
        try:
            criteria_data = criteria["criteria_data"]
            ds_id = criteria_data.get("datasheet_id")
            if not ds_id:
                return None, set(), None

            vars_used = (
                get_datasheet_variables_from_criteria_data(
                    self.client_id, criteria_data
                )
                | get_datasheet_variables_from_criteria_config_sort_cols(
                    criteria["criteria_config"]
                )
                | (set(criteria.get("criteria_column", [])) - default_criteria_columns)
                | (set(criteria.get("simulate_column", [])) - default_criteria_columns)
            )
            return ds_id, vars_used, None
        except Exception as e:
            logger.exception(
                f"Error processing forecast criteria_id {criteria.get('criteria_id')} for client_id {self.client_id}"
            )
            return None, set(), str(e)


class SettlementDatasheetVariableChecker(BaseDatasheetVariableChecker):
    def get_datasheet_variables(
        self, criteria: Dict[str, Any]
    ) -> Tuple[Optional[str], Set[str], Optional[str]]:
        try:
            ds_id = criteria.get("datasheet_id")
            if not ds_id:
                return None, set(), None

            flag_expr = criteria["settlement_flag_expr"]
            settlement_join_keys = criteria.get("settlement_join_keys", [])
            flag_expr["join_keys"] = settlement_join_keys
            flag_expr["date_field"] = criteria.get("date_field")

            vars_used = get_datasheet_variables_from_criteria_data(
                self.client_id, criteria["amount_expr"]
            ) | get_datasheet_variables_from_criteria_data(self.client_id, flag_expr)
            return ds_id, vars_used, None
        except Exception as e:
            logger.exception(
                f"Error processing settlement criteria_id {criteria.get('settlement_rule_id')} for client_id {self.client_id}"
            )
            return None, set(), str(e)


def check_missing_ds_variables_in_canvas_for_client(client_id, client_name):
    """
    Check Missing Datasheet Variables In Canvas for a specific client.
    Args:
        client_id: The client ID to check
    """
    alert_constants = {
        "module_val": "Commission Canvas",
        "check_type_val": "missing_datasheet_variables_in_canvas",
        "alert_name_val": "Missing Datasheet Variables In Canvas",
        "severity_val": "High",
        "description_val": "There are some datasheet or datasheet variables used in canvas that don't exist or are missing, which leads to failure in sync.",
        "logger_email_id_val": "<EMAIL>",
        "assignee_val": "<EMAIL>",
        "cc_email_val": "<EMAIL>",
        "env_val": is_get_env(),
    }
    try:
        criteria_list = PlanCriteriaAccessor(
            client_id
        ).retrieve_all_criteria_for_client(
            projections=[
                "criteria_id",
                "criteria_name",
                "criteria_data",
                "criteria_config",
                "criteria_column",
                "simulate_column",
                "plan_id",
            ]
        )
        forecast_criteria_list = ForecastPlanCriteriaAccessor(
            client_id
        ).retrieve_all_criteria_for_client(
            projections=[
                "criteria_id",
                "criteria_name",
                "criteria_data",
                "criteria_config",
                "criteria_column",
                "simulate_column",
                "plan_id",
            ]
        )
        settlement_criteria_list = SettlementRuleAccessor(
            client_id
        ).retrieve_all_settlement_for_client(
            projections=[
                "settlement_rule_id",
                "name",
                "settlement_flag_expr",
                "settlement_join_keys",
                "date_field",
                "amount_expr",
            ]
        )
        commission_plan_list = CommissionPlanAccessor(
            client_id
        ).retrieve_all_plans_for_client(projections=["plan_id", "plan_name"])
        forecast_plan_list = ForecastPlanAccessor(
            client_id
        ).retrieve_all_plans_for_client(projections=["plan_id", "plan_name"])
        datasheets_list = DatasheetAccessor(
            client_id
        ).retrieve_all_datasheets_for_client(projections=["datasheet_id", "name"])
        comm_settlement_map_list = CommissionSettlementMapAccessor(
            client_id
        ).retrieve_all_comm_settlement_map_for_client(
            projections=["commission_plan_id", "settlement_rule_id"]
        )

        commission_plan_name_map = {
            plan["plan_id"]: plan["plan_name"] for plan in commission_plan_list
        }
        forecast_plan_name_map = {
            plan["plan_id"]: plan["plan_name"] for plan in forecast_plan_list
        }
        datasheet_name_map = {
            str(ds["datasheet_id"]): ds["name"] for ds in datasheets_list
        }
        # For settlement, we map settlement_rule_id to plan name using the comm settlement map
        settlement_plan_name_map = {
            str(mapping["settlement_rule_id"]): (
                mapping["commission_plan_id"],
                commission_plan_name_map.get(mapping["commission_plan_id"], "N/A"),
            )
            for mapping in comm_settlement_map_list
        }
        logger.info(
            f"Client ({client_id}) - CRITERIA: {len(criteria_list)}, SETTLEMENT: {len(settlement_criteria_list)}, FORECAST: {len(forecast_criteria_list)}"
        )

        def process_criteria_list(
            checker: BaseDatasheetVariableChecker,
            criteria_list: List[Dict[str, Any]],
            plan_map: Optional[Dict[Any, str]],
            plan_type: str,
        ) -> bool:
            has_anomalies = False
            for crit in criteria_list:
                try:
                    is_active, ds_id, missing_cols, err_msg = (
                        checker.check_datasheet_variable_existence(crit)
                    )
                    if not is_active:
                        has_anomalies = True
                        ds_name = datasheet_name_map.get(
                            ds_id, "Datasheet doesn't Exist"
                        )
                        if plan_type == "Settlement":
                            settlement_id = str(crit.get("settlement_rule_id"))
                            plan_tuple = plan_map.get(settlement_id, ("N/A", "N/A"))
                            plan_id, plan_name = plan_tuple
                        else:
                            plan_id = crit.get("plan_id")
                            plan_name = plan_map.get(plan_id, "N/A")
                        created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        alert_key_val = f"{client_name}_{alert_constants['check_type_val']}_{created_at}"
                        additional_context_val = err_msg or "-"
                        metadata_val = json.dumps(
                            {
                                "affected_data": {
                                    "Datasheet Name": ds_name,
                                    "Datasheet ID": ds_id,
                                    "Criteria Name": crit.get(
                                        "criteria_name", crit.get("name", "N/A")
                                    ),
                                    "Criteria ID": (
                                        str(crit.get("settlement_rule_id", "N/A"))
                                        if plan_type == "Settlement"
                                        else str(crit.get("criteria_id", "N/A"))
                                    ),
                                    "Plan Name": plan_name,
                                    "Plan Id": str(plan_id),
                                    "Type": plan_type,
                                },
                                "expected_value": [],
                                "actual_value": list(missing_cols),
                                "additional_context": additional_context_val,
                            }
                        )
                        # Log the anomaly
                        log_anomaly_with_nature(
                            client_id=client_id,
                            client_name=client_name,
                            module=alert_constants["module_val"],
                            check_type=alert_constants["check_type_val"],
                            alert_name=alert_constants["alert_name_val"],
                            created_at=created_at,
                            description=alert_constants["description_val"],
                            alert_key=alert_key_val,
                            logger_email_id=alert_constants["logger_email_id_val"],
                            assignee=alert_constants["assignee_val"],
                            cc_email_id=alert_constants["cc_email_val"],
                            env=alert_constants["env_val"],
                            metadata=metadata_val,
                        )
                except Exception as e:
                    logger.exception(
                        f"Error processing {plan_type} criteria for client_id {client_id}"
                    )
            return has_anomalies

        commission_anomalies = process_criteria_list(
            checker=CommissionDatasheetVariableChecker(client_id),
            criteria_list=list(criteria_list),
            plan_map=commission_plan_name_map,
            plan_type="Commission",
        )
        forecast_anomalies = process_criteria_list(
            checker=ForecastDatasheetVariableChecker(client_id),
            criteria_list=list(forecast_criteria_list),
            plan_map=forecast_plan_name_map,
            plan_type="Forecast",
        )
        settlement_anomalies = process_criteria_list(
            checker=SettlementDatasheetVariableChecker(client_id),
            criteria_list=list(settlement_criteria_list),
            plan_map=settlement_plan_name_map,
            plan_type="Settlement",
        )

        # Log heartbeat if no anomalies were found
        if not (commission_anomalies or forecast_anomalies or settlement_anomalies):
            log_heartbeat(
                client_id=client_id,
                client_name=client_name,
                check_type=alert_constants["check_type_val"],
                env=alert_constants["env_val"],
                module=alert_constants["module_val"],
            )
    except Exception as e:
        logger.exception(f"Error processing client_id {client_id} ({client_name})")


@shared_task(base=EverCeleryBaseTask)
def check_missing_ds_variables_in_canvas(client_id, client_name):
    try:
        start_time = datetime.now()
        logger.info(
            "Checking Missing Datasheet Variables in Canvas for Client - {}".format(
                client_id
            )
        )
        check_missing_ds_variables_in_canvas_for_client(client_id, client_name)
        duration = datetime.now() - start_time
        logger.info(
            f"Completed Checking Missing Datasheet Variables in Canvas for Client - {client_id}. Total execution time: {duration}"
        )
    except Exception as e:
        logger.error(
            "Checking Missing Datasheet Variables in Canvas for client {} - {}".format(
                client_id, e
            )
        )


@shared_task(base=EverCeleryBaseTask)
def check_missing_ds_variables_in_canvas_wrapper():
    from commission_engine.accessors.client_accessor import (
        get_active_clients_features_excluding_churned,
    )

    task_group_name = TaskGroupEnum.ANOMALY_DETECT.value
    clients = get_active_clients_features_excluding_churned()

    tasks = group(
        [
            check_missing_ds_variables_in_canvas.si(client_id, client_name).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id,
                    features.get("subscription_plan", "BASIC"),
                    task_group_name,
                )
            )
            for client_id, client_name, features in clients
        ]
    )

    tasks.apply_async(compression="lzma", serializer="pickle")
