import logging
import os
import time
from datetime import datetime, timezone
from math import inf
from typing import Any, Dict, List, Optional

from celery import group, shared_task
from django.db import connection

from commission_engine.accessors.client_accessor import (
    get_client_id_subscription_plan_map,
)
from commission_engine.services.client_feature_service import has_feature
from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
)
from superset.services.dashboard_api_services import SupersetApi

# Constants
MAX_COLUMNS_TO_DISPLAY = 200

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_datasheet_dataset_mapping(client_id: int) -> List[Dict[str, Any]]:
    """Get datasheet data from table datasheet_dataset_map in Everstage DB for a specific client

    Args:
        client_id: The client ID to fetch data for

    Returns:
        List of dictionaries with datasheet mapping information
    """
    data = []
    query = """
        SELECT ddm.superset_dataset_id, ddm.datasheet_id, ddm.databook_id, 
               ddm.client_id, ddm.table_name
        FROM datasheet_dataset_map ddm
        WHERE ddm.knowledge_end_date is null 
        AND ddm.client_id = %s
    """

    with connection.cursor() as cursor:
        cursor.execute(query, [client_id])
        columns = [col[0] for col in cursor.description]
        data = [dict(zip(columns, row)) for row in cursor.fetchall()]

    logger.info(
        "Found %s datasheet-dataset mappings for client %s", len(data), client_id
    )
    return data


def get_dataset_info_from_superset(dataset_id: int) -> Dict[str, Any]:
    """Get dataset information from Superset API using dataset ID

    Args:
        dataset_id: The Superset dataset ID

    Returns:
        Dictionary containing dataset information from Superset
    """
    analytics_api = SupersetApi()
    response = analytics_api.get_dataset_info(dataset_id=dataset_id)

    if response.status_code == 200:
        dataset_data = response.json().get("result")
        logger.info("Successfully retrieved dataset info for dataset ID %s", dataset_id)
        return dataset_data

    logger.warning(
        "Failed to retrieve dataset info for dataset ID %s: %s",
        dataset_id,
        response.status_code,
    )
    return {}


def get_dataset_columns_from_superset(dataset_id: int) -> List[str]:
    """Get column names from a Superset dataset

    Args:
        dataset_id: The Superset dataset ID

    Returns:
        List of column names in the dataset
    """
    dataset_data = get_dataset_info_from_superset(dataset_id)
    columns = []

    if dataset_data and "columns" in dataset_data:
        columns = [col["column_name"] for col in dataset_data.get("columns", [])]

    return columns


def get_datasheet_columns_from_everstage_spm(
    client_id: int, datasheet_id: str, databook_id: str
) -> List[str]:
    """Get variable names (columns) for a specific datasheet

    Args:
        client_id: The client ID
        datasheet_id: The datasheet ID
        databook_id: The databook ID

    Returns:
        List of display names (column names) in the datasheet
    """
    query = """
        SELECT dv.display_name 
        FROM datasheet_variable dv
        WHERE dv.client_id = %s
        AND dv.datasheet_id = %s
        AND dv.databook_id = %s
        AND dv.knowledge_end_date IS NULL
    """

    with connection.cursor() as cursor:
        cursor.execute(query, [client_id, datasheet_id, databook_id])
        variables = [row[0] for row in cursor.fetchall()]

    logger.info("Found %s variables for datasheet ID %s", len(variables), datasheet_id)
    return variables


def is_datasheet_deleted(client_id: int, datasheet_id: str) -> bool:
    """Check if a datasheet is deleted in Everstage

    Args:
        client_id: The client ID
        datasheet_id: The datasheet ID

    Returns:
        True if the datasheet is deleted, False otherwise
    """
    query = """
        SELECT COUNT(*) FROM datasheet 
        WHERE client_id = %s AND datasheet_id = %s AND 
        knowledge_end_date IS NULL
    """
    count = inf
    with connection.cursor() as cursor:
        cursor.execute(query, [client_id, datasheet_id])
        count = cursor.fetchone()[0]

    return count == 0


def compare_columns(
    everstage_datasheet_columns: List[str], superset_dataset_columns: List[str]
) -> Dict[str, List[str]]:
    """Compare column names between Everstage datasheet variables and Superset dataset columns

    Args:
        everstage_datasheet_columns: List of column names from Everstage
        superset_dataset_columns: List of column names from Superset

    Returns:
        Dictionary with lists of missing and extra columns
    """

    everstage_set = set(var.lower() for var in everstage_datasheet_columns)
    superset_set = set(col.lower() for col in superset_dataset_columns)

    missing_columns_in_superset = list(everstage_set - superset_set)
    extra_columns_in_superset = list(superset_set - everstage_set)

    return {
        "missing_columns_in_superset": missing_columns_in_superset,
        "extra_columns_in_superset": extra_columns_in_superset,
    }


def log_datasheet_dataset_anomalies(
    client_id: int, client_name: str, anomalies: Dict[str, Any]
) -> None:
    """Log any datasheet dataset anomalies found

    Args:
        client_id: The client ID
        client_name: The client name
        anomalies: Dictionary containing different types of anomalies
    """
    env = os.getenv("ENV", "LOCALDEV").lower()

    # Log deleted datasheets still in use
    if "deleted_datasheets" in anomalies and anomalies["deleted_datasheets"]:
        log_anomaly_with_nature(
            check_type="deleted_datasheet_still_in_use",
            client_id=client_id,
            client_name=client_name,
            module="Superset",
            alert_name="Deleted Datasheet Still in Superset",
            created_at=datetime.now(timezone.utc),
            description=f"{len(anomalies['deleted_datasheets'])} deleted datasheets still in use in Superset",
            alert_key=f"deleted_datasheet_in_superset_{client_id}",
            logger_email_id="<EMAIL>",
            assignee="<EMAIL>",
            cc_email_id="",
            env=env,
            metadata=anomalies["deleted_datasheets"],
        )

    # Log column mismatches between datasheet variables and dataset columns
    if "dataset_out_of_sync" in anomalies and anomalies["dataset_out_of_sync"]:
        log_anomaly_with_nature(
            check_type="column_mismatch",
            client_id=client_id,
            client_name=client_name,
            module="Superset",
            alert_name="Dataset Columns Out of Sync",
            created_at=datetime.now(timezone.utc),
            description=f"{len(anomalies['dataset_out_of_sync'])} datasets have column mismatches",
            alert_key=f"dataset_column_mismatch_{client_id}",
            logger_email_id="<EMAIL>",
            assignee="<EMAIL>",
            cc_email_id="",
            env=env,
            metadata=anomalies["dataset_out_of_sync"],
        )


def get_client_name(client_id: int) -> str:
    """Get the client name for a given client ID

    Args:
        client_id: The client ID

    Returns:
        The client name or a default if not found
    """
    query = "SELECT name FROM interstage_clients WHERE client_id = %s"

    try:
        with connection.cursor() as cursor:
            cursor.execute(query, [client_id])
            result = cursor.fetchone()
            if result:
                return result[0]
    except Exception:
        logger.exception("Error getting client name for client %s", client_id)

    return f"Client {client_id}"


@shared_task(base=EverCeleryBaseTask)
def detect_datasheet_dataset_anomalies(client_id: int) -> None:
    """Detect anomalies in datasheet data for a specific client

    This function performs two main checks:
    1. Identifies datasheets that are deleted in Everstage but still mapped in Superset
    2. Compares variables/columns between Everstage datasheets and Superset datasets

    Args:
        client_id: The client ID to check
    """
    task_start_time = time.time()
    logger.info("BEGIN: Detecting datasheet anomalies for client %s", client_id)

    try:
        client_name = get_client_name(client_id)

        datasheet_dataset_mapping = get_datasheet_dataset_mapping(client_id)

        anomalies = {"deleted_datasheets": [], "dataset_out_of_sync": []}

        for mapping in datasheet_dataset_mapping:
            # Check if the datasheet is deleted but still mapped
            if is_datasheet_deleted(client_id, mapping["datasheet_id"]):
                anomaly_data = {
                    "client_id": client_id,
                    "client_name": client_name,
                    "databook_id": mapping["databook_id"],
                    "datasheet_id": mapping["datasheet_id"],
                    "superset_dataset_id": mapping["superset_dataset_id"],
                    "superset_dataset_name": mapping["table_name"],
                }
                anomalies["deleted_datasheets"].append(anomaly_data)
                logger.warning(
                    "Deleted datasheet still in use: client_id=%s, "
                    "datasheet_id=%s, dataset_id=%s",
                    client_id,
                    mapping["datasheet_id"],
                    mapping["superset_dataset_id"],
                )

            else:

                everstage_datasheet_columns = get_datasheet_columns_from_everstage_spm(
                    client_id, mapping["datasheet_id"], mapping["databook_id"]
                )

                superset_dataset_columns = get_dataset_columns_from_superset(
                    mapping["superset_dataset_id"]
                )
                logger.info(
                    "Everstage datasheet columns: %s", everstage_datasheet_columns
                )
                logger.info("Superset dataset columns: %s", superset_dataset_columns)
                column_comparison = compare_columns(
                    everstage_datasheet_columns, superset_dataset_columns
                )

                if (
                    column_comparison["missing_columns_in_superset"]
                    or column_comparison["extra_columns_in_superset"]
                ):
                    mismatch_data = {
                        "superset_dataset_id": mapping["superset_dataset_id"],
                        "superset_dataset_name": mapping["table_name"],
                        "datasheet_id": mapping["datasheet_id"],
                        "databook_id": mapping["databook_id"],
                        "client_id": client_id,
                        "missing_columns_in_superset": column_comparison[
                            "missing_columns_in_superset"
                        ],
                        "extra_columns_in_superset": column_comparison[
                            "extra_columns_in_superset"
                        ],
                    }
                    anomalies["dataset_out_of_sync"].append(mismatch_data)
                    logger.warning(
                        "Column mismatch found: client_id=%s, "
                        "datasheet_id=%s, dataset_id=%s, "
                        "missing_columns=%s, "
                        "extra_columns=%s",
                        client_id,
                        mapping["datasheet_id"],
                        mapping["superset_dataset_id"],
                        column_comparison["missing_columns_in_superset"],
                        column_comparison["extra_columns_in_superset"],
                    )

        log_datasheet_dataset_anomalies(client_id, client_name, anomalies)

        task_end_time = time.time()
        execution_time = task_end_time - task_start_time
        logger.info(
            "END: Detecting datasheet anomalies for client %s, execution time: %.2f seconds",
            client_id,
            execution_time,
        )
    except Exception:
        task_end_time = time.time()
        execution_time = task_end_time - task_start_time
        logger.exception(
            "Error detecting datasheet anomalies for client %s, execution time: %.2f seconds",
            client_id,
            execution_time,
        )


@shared_task(base=EverCeleryBaseTask)
def detect_datasheet_dataset_anomalies_wrapper(
    exclude_client_ids: Optional[List[int]] = None,
) -> None:
    """Wrapper function to run datasheet-dataset anomaly detection for all clients

    This function retrieves all clients with datasheet-dataset mappings and
    schedules individual anomaly detection tasks for each client.

    Args:
        exclude_client_ids: Optional list of client IDs to exclude from checking
    """
    logger.info("BEGIN: Detecting datasheet anomalies for all clients")
    try:
        client_data = get_client_id_subscription_plan_map(exclude_client_ids)
        superset_clients = {
            client_id: subscription_plan
            for client_id, subscription_plan in client_data.items()
            if has_feature(client_id, "show_superset_dashboard")
        }
        logger.info("superset_clients %s", superset_clients)
        logger.info(
            "Found %s clients with datasheet-dataset mappings", len(superset_clients)
        )

        tasks = []

        tasks = group(
            [
                detect_datasheet_dataset_anomalies.si(client_id).set(
                    queue=get_queue_name_respect_to_task_group(
                        client_id,
                        subscription_plan or "BASIC",
                        TaskGroupEnum.ANOMALY_DETECT.value,
                    )
                )
                for client_id, subscription_plan in superset_clients.items()
            ]
        )

        tasks.apply_async(compression="lzma", serializer="pickle")
        logger.info(
            "Scheduled datasheet-dataset anomaly detection for %s clients", len(tasks)
        )
    except Exception:
        logger.exception("Error in datasheet-dataset anomalies wrapper")
