import logging
import os
from collections import defaultdict
from typing import List, Optional

from celery import group, shared_task
from django.utils import timezone
from snowflake.snowpark import Session

from commission_engine.accessors.client_accessor import (
    get_active_clients_features_excluding_churned,
)
from commission_engine.accessors.etl_config_accessor import (
    ApiAccessConfigAccessor,
    IntegrationAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.database.snowflake_connection import get_database_name
from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.global_utils.secrets_manager import get_snowflake_auth_param
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.upstream_anomalies.helper import (
    MODULE_NAME,
    UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
    UpstreamAnomalyType,
)

logger = logging.getLogger(__name__)
alert_dict = UpstreamAnomalyType.EXTRACTED_LOAD_CHECKER.value


def connect_to_snowflake(client_id: Optional[str | int]) -> Optional[Session]:
    try:
        connection_parameters = {
            **get_snowflake_auth_param(),
            "account": os.getenv("SNOWFLAKE_ACCOUNT"),
            "user": os.getenv("SNOWFLAKE_USER"),
            "warehouse": os.getenv("SNOWFLAKE_WAREHOUSE"),
            "database": get_database_name(
                client_id=client_id,
                default_database=os.environ.get("SNOWFLAKE_DATABASE"),
            ),
            "schema": os.getenv("SNOWFLAKE_SCHEMA"),
            "role": os.getenv("SNOWFLAKE_ROLE"),
        }
        session = Session.builder.configs(connection_parameters).create()  # type: ignore
        logger.info("Connected to Snowflake successfully.")
        return session
    except Exception:
        logger.exception("Error connecting to Snowflake")
        raise


def get_loaded_count_from_snowflake(
    session, client_id, destination_object_id, sync_start_time
):
    try:
        table_name = f"custom_object_data_{client_id}_{destination_object_id}"
        formatted_time = sync_start_time.strftime("%Y-%m-%d %H:%M:%S")
        query = f"""
            SELECT COUNT(*) 
            FROM {table_name} 
            WHERE knowledge_begin_date >= TIMESTAMP '{formatted_time}'
            and source = 'upstream'
        """

        result = session.sql(query).collect()
        return result[0][0] if result else 0

    except Exception as e:
        logger.error(f"Error querying Snowflake for {table_name}: {e}")
        return None


@shared_task(base=EverCeleryBaseTask)
def check_extract_load_count(client: dict):
    logger.info(
        f"Begin extract and load count check for client {client['name']}-{client['client_id']}"
    )

    integrations_for_client = IntegrationAccessor(
        client_id=client["client_id"]
    ).get_all_records()
    integration_ids = [
        integration.integration_id for integration in integrations_for_client
    ]

    last_sync_qs = UpstreamETLStatusReaderAccessor(
        client_id=client["client_id"]
    ).get_last_successful_run_records_for_integrations(integration_ids)
    last_sync_map = {record.integration_id: record for record in last_sync_qs}

    api_access_configs = defaultdict(list)
    for config in ApiAccessConfigAccessor(
        client_id=client["client_id"]
    ).get_all_api_access_configs():
        api_access_configs[config.integration_id].append(config)

    session = connect_to_snowflake(client["client_id"])

    mismatched_integrations = []
    extracted_counts = []
    loaded_counts = []

    for integration in integrations_for_client:
        integration_id = integration.integration_id
        destination_object_id = integration.destination_object_id

        if str(integration_id) == "5df8ce88-c437-4abb-aa0c-a8fadd9f3413":
            continue  # Skip Gray TV integration

        last_successful_sync = last_sync_map.get(integration_id)
        if not last_successful_sync:
            continue

        sync_start_time = timezone.make_naive(last_successful_sync.changes_end_time)

        try:
            api_access_config_list = api_access_configs.get(integration_id, [])
            if not api_access_config_list:
                continue

            skip = any(
                config.additional_data.get("process_list", False)
                for config in api_access_config_list
            )
            if skip:
                continue

            loaded_count = get_loaded_count_from_snowflake(
                session, client["client_id"], destination_object_id, sync_start_time
            )

            if loaded_count is None:
                continue
            if last_successful_sync.extracted_records_count != loaded_count:
                mismatched_integrations.append(integration_id)
                extracted_counts.append(last_successful_sync.extracted_records_count)
                loaded_counts.append(loaded_count)

        except Exception as e:
            logger.info({"integration_id": str(integration_id), "exception": str(e)})

    if mismatched_integrations:
        log_anomaly_with_nature(
            check_type=alert_dict["check_type"],
            client_id=client["client_id"],
            client_name=client["name"],
            module=MODULE_NAME,
            alert_name=alert_dict["alert_name"],
            created_at=timezone.now().isoformat(),
            description=alert_dict["description"],
            alert_key=f"upstream_{client['client_id']}_{alert_dict['check_type']}",
            logger_email_id=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            assignee=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            cc_email_id="",
            env=os.environ.get("ENV"),
            metadata={
                "affected_data": [str(i) for i in mismatched_integrations],
                "expected_value": extracted_counts,
                "actual_value": loaded_counts,
                "additional_context": {},
            },
        )
    else:
        log_heartbeat(
            client_id=client["client_id"],
            client_name=client["name"],
            check_type=alert_dict["check_type"],
            env=os.environ.get("ENV"),
            module=MODULE_NAME,
        )

    if session is not None:
        session.close()

    logger.info(
        f"End extract and load count check for client {client['name']}-{client['client_id']}"
    )


@shared_task(base=EverCeleryBaseTask)
def check_extract_load_count_wrapper(clients: Optional[List[int]] = None):
    task_group = TaskGroupEnum.ANOMALY_DETECT.value
    active_clients = get_active_clients_features_excluding_churned()

    client_dicts = []
    for client in active_clients:
        client_id, name, details = client
        client_dicts.append(
            {
                "client_id": client_id,
                "name": name,
                "subscription": details.get("subscription_plan"),
            }
        )

    if clients:
        client_list = [
            client for client in client_dicts if client["client_id"] in clients
        ]
    else:
        client_list = client_dicts

    task = group(
        [
            check_extract_load_count.si(client).set(  # type: ignore
                queue=get_queue_name_respect_to_task_group(
                    client["client_id"], client["subscription"] or "BASIC", task_group
                )
            )
            for client in client_list
        ]
    )
    task.apply_async(compression="lzma", serializer="pickle")
