# ruff: noqa
import logging
import os
from collections import defaultdict
from os import environ
from typing import Any, Dict, List, Optional

import snowflake.connector
import snowflake.snowpark
import snowflake.snowpark.exceptions
from celery import group, shared_task
from django import setup
from django.db.models import Q
from django.utils import timezone
from snowflake.snowpark import Session

from commission_engine.database.snowflake_connection import get_database_name

environ.setdefault("DJANGO_SETTINGS_MODULE", "interstage_project.settings")
setup()

from commission_engine.accessors.client_accessor import (
    get_active_clients_features_excluding_churned,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.models.etl_config_models import (
    AccessTokenConfig,
    ApiAccessConfig,
    ExtractionConfig,
    Integration,
)
from commission_engine.third_party_connections.sql import Sql
from commission_engine.utils.general_data import UpstreamSyncModes
from commission_engine.utils.general_utils import get_custom_object_data_table_name
from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.self_service_integration.constants import SelfServiceConnectors
from everstage_ddd.upstream.extraction.accessors import UpstreamTimestampsReader
from everstage_infra.aws_infra.constants.environments import PRODUCTION_ENVIRONMENT
from interstage_project.celery import TaskGroupEnum
from interstage_project.global_utils.secrets_manager import get_snowflake_auth_param
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.upstream_anomalies.helper import (
    MODULE_NAME,
    UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
    UpstreamAnomalyType,
    group_clients_for_parallel_processing,
)

logger = logging.getLogger(__name__)


def extract_inner_query(outer_query: str) -> Optional[str]:
    start_marker = "SELECT {fields} from ("
    end_marker = ") WHERE rnum BETWEEN {row_start} AND {row_end}"

    start_index = outer_query.find(start_marker)
    if start_index == -1:
        logger.warning("Start marker for inner query not found.")
        return None

    start_of_inner = start_index + len(start_marker)
    end_index = outer_query.find(end_marker, start_of_inner)
    if end_index == -1:
        logger.warning("End marker for inner query not found.")
        return None

    inner_query = outer_query[start_of_inner:end_index].strip()
    return inner_query


def transform_query(query: str, db_type: str) -> Optional[str]:
    if db_type == "suiteanalytics":
        inner_query = extract_inner_query(query)
        if not inner_query:
            logger.warning("Failed to extract inner query from SuiteAnalytics query.")
            return None
        query = inner_query

    parts = query.split("FROM", 1)
    if len(parts) <= 1:
        logger.warning("Missing 'FROM' clause in query.")
        return None

    where_clause = parts[1].split("ORDER BY")[0].strip()
    return f"SELECT COUNT(*) as count FROM {where_clause}"


def connect_to_snowflake(client_id: Optional[str | int]) -> Optional[Session]:
    try:
        connection_parameters = {
            **get_snowflake_auth_param(),
            "account": os.getenv("SNOWFLAKE_ACCOUNT"),
            "user": os.getenv("SNOWFLAKE_USER"),
            "warehouse": os.getenv("SNOWFLAKE_WAREHOUSE"),
            "database": get_database_name(
                client_id=client_id, default_database=os.getenv("SNOWFLAKE_DATABASE")
            ),
            "schema": os.getenv("SNOWFLAKE_SCHEMA"),
            "role": os.getenv("SNOWFLAKE_ROLE"),
        }
        session = Session.builder.configs(connection_parameters).create()
        logger.info("Connected to Snowflake successfully.")
        return session
    except snowflake.connector.Error as e:
        logger.error(f"Error connecting to Snowflake: {e}")
        return None


def extract_count(df: snowflake.snowpark.DataFrame) -> Optional[float]:
    try:
        if df.count() == 1 and len(df.columns) == 1:
            return int(df.collect()[0][0])
        logger.warning("DataFrame does not contain exactly one count column.")
        return None
    except Exception as e:
        logger.error(f"Error extracting count from DataFrame: {e}")
        return None


def execute_sql_statement(
    sql_statement: str, client_id: Optional[str | int]
) -> Optional[float]:
    session = connect_to_snowflake(client_id)
    if not session:
        logger.error("Failed to establish a Snowflake session.")
        return None
    try:
        df = session.sql(sql_statement)
        result = extract_count(df)
        if result is None:
            logger.error(f"Failed to extract count from SQL statement: {sql_statement}")
        return result
    except snowflake.snowpark.exceptions.SnowparkSQLException as e:
        logger.error(f"SQL execution error: {e}\nQuery: {sql_statement}")
        return None
    finally:
        session.close()


def check_retrospective_updates(
    client: Dict[str, Any],
    integrations: List,
    upstream_etl: Dict,
    api_access_config_map: Dict,
    atc_config_map: Dict,
) -> None:
    client_id = client["client_id"]
    client_name = client["name"]
    now = timezone.now()
    alert_dict = UpstreamAnomalyType.PERIODIC_ALL_MODE_SYNC.value

    if not integrations:
        logger.info(f"No SQL integrations found for client {client_id}.")
        log_heartbeat(
            client_id=client_id,
            client_name=client_name,
            check_type=alert_dict["check_type"],
            env=os.environ.get("ENV"),
            module=MODULE_NAME,
        )
        return

    invalid_integrations = []
    for integration in integrations:
        source_object_id = integration["source_object_id"]
        destination_object_id = integration["destination_object_id"]
        integration_id = integration["integration_id"]

        if not source_object_id or not integration_id or not destination_object_id:
            logger.warning(
                f"Skipping integration with missing IDs for client {client_id}."
            )
            continue

        aac_config = api_access_config_map[integration_id]
        if not aac_config:
            continue

        access_token_config_id = aac_config["access_token_config_id"]
        sync_field_type = aac_config["sync_field_type"]
        db_type = atc_config_map.get(access_token_config_id)
        if not db_type:
            logger.warning("Missing required parameters: db_type.")
            continue

        try:
            sql = Sql()
            db_connector = sql.get_connector(
                client_id=client_id,
                access_token_config_id=access_token_config_id,
            )
            source_query = aac_config["query"]

            custom_object_table_name = get_custom_object_data_table_name(
                client_id=client_id, custom_object_id=destination_object_id
            )
            sync_changes_timestamp = upstream_etl.get(integration_id)

            if not sync_changes_timestamp:
                logger.info("Upstream records not found")
                continue

            changes_start_time = sync_changes_timestamp.get("earliest_start_time")
            changes_end_time = sync_changes_timestamp.get("recent_completion_time")

            if not changes_start_time or not changes_end_time:
                logger.error(
                    f"Missing changes_start_time or changes_end_time for integration {integration_id}, client {client_id}."
                )
                continue

            source_query = transform_query(source_query, db_type)

            destination_query = (
                f"SELECT COUNT(*) as count FROM {custom_object_table_name} "
                "WHERE KNOWLEDGE_END_DATE IS NULL and IS_DELETED = FALSE"
            )

            if not source_query:
                logger.error(
                    f"Failed to transform query for integration {integration_id}, client {client_id}."
                )
                continue

            general_params = sql.get_extractor_params(
                client_id=client_id,
                object_id=source_object_id,
                primary_kd=changes_end_time,
                changes_start_time=changes_start_time,
                sync_mode=UpstreamSyncModes.CHANGES.value,
                sync_field_type=sync_field_type,
                integration_id=integration_id,
            )

            source_query = source_query.format(**general_params)

            db_connector.connect()
            source_output = db_connector.query_data(query=source_query)
            db_connector.disconnect()

            destination_total_record = execute_sql_statement(
                destination_query, client_id
            )

            if not source_output or destination_total_record is None:
                logger.warning(
                    f"No results returned for integration {integration_id}, client {client_id}."
                )
                continue
            source_total_record = source_output[0].get("count", 0)

            if source_total_record != destination_total_record:
                invalid_integrations.append(
                    {
                        "integration_id": integration_id,
                        "source_id": source_object_id,
                        "source_record_count": source_total_record,
                        "destination_total_record": destination_total_record,
                    }
                )
        except Exception as e:
            logger.error(
                f"Error processing client {client_id}, integration {integration_id}: {e}"
            )
            continue

    if not invalid_integrations:
        logger.info(f"Source and destination are synced for client {client_id}.")
        log_heartbeat(
            client_id=client_id,
            client_name=client_name,
            check_type=alert_dict["check_type"],
            env=os.environ.get("ENV"),
            module=MODULE_NAME,
        )
        return

    for integration in invalid_integrations:
        integration_id = str(integration.get("integration_id"))
        metadata = {
            "affected_data": {"integration_id": integration_id},
            "expected_value": integration["source_record_count"],
            "actual_value": integration["destination_total_record"],
            "additional_context": "",
        }

        log_anomaly_with_nature(
            check_type=alert_dict["check_type"],
            client_id=client_id,
            client_name=client_name,
            module=MODULE_NAME,
            alert_name=alert_dict["alert_name"],
            created_at=now.isoformat(),
            description=alert_dict["description"],
            alert_key=f"upstream_{client_id}_{alert_dict['check_type']}",
            logger_email_id=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            assignee=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            cc_email_id="",
            env=os.environ.get("ENV"),
            metadata=metadata,
        )


@shared_task(base=EverCeleryBaseTask)
def detect_retrospective_updates(
    clients: List[dict],
) -> None:
    middleware_filter = list(
        AccessTokenConfig.objects.filter(
            knowledge_end_date__isnull=True,
            client_id__in=[
                monitored_client["client_id"] for monitored_client in clients
            ],
            access_request_body__middleware_name__isnull=False,
        ).values_list("access_token_config_id", flat=True)
    )
    integration_filter = list(
        ExtractionConfig.objects.filter(
            knowledge_end_date__isnull=True,
            client_id__in=[
                monitored_client["client_id"] for monitored_client in clients
            ],
        )
        .filter(
            Q(is_disabled=True)
            | Q(sync_type=UpstreamSyncModes.SNAPSHOT.value)
            | Q(access_token_config_id__in=middleware_filter)
        )
        .values_list("integration_id", flat=True)
    )

    grouped_integrations = defaultdict(list)
    integrations = (
        Integration.objects.filter(
            knowledge_end_date__isnull=True,
            service_name=SelfServiceConnectors.SQL.value,
            client_id__in=[
                monitored_client["client_id"] for monitored_client in clients
            ],
            preprocessing_metadata__isnull=True,
        )
        .exclude(integration_id__in=integration_filter)
        .values(
            "source_object_id",
            "destination_object_id",
            "integration_id",
            "client_id",
        )
    )

    integration_ids = []
    for integration in integrations:
        grouped_integrations[integration["client_id"]].append(integration)
        integration_ids.append(integration["integration_id"])

    sync_timestamp_map = {}
    for client_id, integration_records in grouped_integrations.items():
        qs = UpstreamETLStatusReaderAccessor(
            client_id
        ).get_last_successful_run_records_for_integrations(
            [record["integration_id"] for record in integration_records]
        )
        for result in list(qs):
            earliest_start_time = UpstreamTimestampsReader(
                client_id
            ).get_historic_sync_date_for_integration_id(result.integration_id)
            sync_timestamp_map[result.integration_id] = {
                "earliest_start_time": earliest_start_time,
                "recent_completion_time": result.changes_end_time,
            }

    api_access_configs = list(
        ApiAccessConfig.objects.filter(
            client_id__in=[client["client_id"] for client in clients],
            integration_id__in=integration_ids,
            knowledge_end_date__isnull=True,
        )
        .exclude(source_object_id__contains="_delete")
        .values(
            "client_id",
            "integration_id",
            "source_object_id",
            "access_token_config_id",
            "request_body__query",
            "additional_data__sync_field_type",
        )
    )

    api_access_config_map, access_token_config_ids = dict(), list()
    for config in api_access_configs:
        integration_id = config["integration_id"]
        access_token_config_id = config["access_token_config_id"]
        query = config["request_body__query"]
        sync_field_type = config["additional_data__sync_field_type"]

        access_token_config_ids.append(access_token_config_id)
        api_access_config_map[integration_id] = {
            "access_token_config_id": access_token_config_id,
            "query": query,
            "sync_field_type": sync_field_type,
        }

    atc_config_map = dict(
        AccessTokenConfig.objects.filter(
            client_id__in=[client["client_id"] for client in clients],
            access_token_config_id__in=access_token_config_ids,
            knowledge_end_date__isnull=True,
        ).values_list("access_token_config_id", "access_request_body__db_type")
    )

    for client in clients:
        logger.info(f"Processing client {client['client_id']} - {client['name']}.")
        client_integration = grouped_integrations[client["client_id"]]
        check_retrospective_updates(
            client,
            client_integration,
            sync_timestamp_map,
            api_access_config_map,
            atc_config_map,
        )

    logger.info(
        "Retrospective updates where last modified date is backdated checker completed..."
    )


@shared_task(base=EverCeleryBaseTask)
def detect_retrospective_updates_wrapper(
    client_ids: Optional[list[int]] = None,
) -> None:
    if os.getenv("ENV") not in PRODUCTION_ENVIRONMENT:
        logger.info(
            "Skipping retrospective updates checker in non-production environment"
        )
        return
    active_clients = get_active_clients_features_excluding_churned()
    client_dicts = []
    for client in active_clients:
        client_id, name, details = client
        client_dicts.append(
            {
                "client_id": client_id,
                "name": name,
                "subscription": details.get("subscription_plan"),
            }
        )

    if client_ids:
        clients = [
            client for client in client_dicts if client["client_id"] in client_ids
        ]
    else:
        clients = client_dicts

    process_groups = group_clients_for_parallel_processing(clients, 20)
    task_group = TaskGroupEnum.ANOMALY_DETECT.value

    task = group(
        [
            detect_retrospective_updates.si(
                clients=clients_list,
            ).set(
                queue=get_queue_name_respect_to_task_group(
                    clients_list[0]["client_id"],
                    clients_list[0]["subscription"] or "BASIC",
                    task_group,
                )
            )
            for clients_list in process_groups
        ]
    )
    task.apply_async(compression="lzma", serializer="pickle")
