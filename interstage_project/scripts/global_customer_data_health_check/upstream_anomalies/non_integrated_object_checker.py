import os

from django import setup

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "interstage_project.settings")
setup()

import logging
from datetime import datetime, timedelta
from typing import Any, List, Optional

import snowflake.connector
import snowflake.snowpark
import snowflake.snowpark.exceptions
from celery import group, shared_task
from django.utils import timezone
from snowflake.snowpark import Session

from commission_engine.accessors.client_accessor import (
    get_active_clients_features_excluding_churned,
)
from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.etl_config_accessor import IntegrationAccessor
from commission_engine.database.snowflake_connection import get_database_name
from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.global_utils.secrets_manager import get_snowflake_auth_param
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.upstream_anomalies.helper import (
    MODULE_NAME,
    UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
    UpstreamAnomalyType,
)

logger = logging.getLogger(__name__)
alert_dict = UpstreamAnomalyType.UNUSED_UNINTEGRATED_OBJECT.value


def connect_to_snowflake(client_id: Optional[str | int]) -> Optional[Session]:
    try:

        database = get_database_name(
            client_id=client_id, default_database=os.getenv("SNOWFLAKE_DATABASE")
        )

        connection_parameters = {
            **get_snowflake_auth_param(),
            "account": os.getenv("SNOWFLAKE_ACCOUNT"),
            "user": os.getenv("SNOWFLAKE_USER"),
            "warehouse": os.getenv("SNOWFLAKE_WAREHOUSE"),
            "database": database,
            "schema": os.getenv("SNOWFLAKE_SCHEMA"),
            "role": os.getenv("SNOWFLAKE_ROLE"),
        }
        session = Session.builder.configs(connection_parameters).create()  # type: ignore
        logger.info("Connected to Snowflake successfully.")
        return session
    except Exception:
        logger.exception("Error connecting to Snowflake")
        raise


def execute_sql_statement(session: Session, sql_statement: str) -> Optional[List[Any]]:
    try:
        df = session.sql(sql_statement).collect()
        return df
    except snowflake.snowpark.exceptions.SnowparkSQLException as e:
        logger.error(f"SQL execution error: {e}\nQuery: {sql_statement}")
        return None


def get_last_begin_or_end_date(client_id, object_id, session):
    query1 = f"""
        SELECT knowledge_begin_date 
        FROM custom_object_data_{client_id}_{object_id} 
        ORDER BY knowledge_begin_date DESC 
        LIMIT 1
    """
    query2 = f"""
        SELECT knowledge_end_date 
        FROM custom_object_data_{client_id}_{object_id} 
        WHERE knowledge_end_date IS NOT NULL 
        ORDER BY knowledge_end_date DESC 
        LIMIT 1
    """

    print("Executing:", query1)
    latest_begin_date_result = execute_sql_statement(session, query1)

    print("Executing:", query2)
    latest_end_date_result = execute_sql_statement(session, query2)

    latest_begin_date = (
        latest_begin_date_result[0][0] if latest_begin_date_result else None
    )
    latest_end_date = latest_end_date_result[0][0] if latest_end_date_result else None

    latest_date = (
        max(filter(None, [latest_begin_date, latest_end_date]))
        if (latest_begin_date or latest_end_date)
        else None
    )

    return latest_date


@shared_task(base=EverCeleryBaseTask)
def check_unuploaded_sync_data(threshold: int, client: dict):  # noqa: E999
    logger.info(
        f'Begin non integrated unused objects check for client {client["name"]}-{client["client_id"]}'
    )

    threshold = datetime.today() - timedelta(days=threshold)

    integrated_objects = IntegrationAccessor(
        client_id=client.get("client_id")
    ).get_all_records()

    integrated_object_ids = []
    for integrated_object in integrated_objects:
        integrated_object_ids.append(integrated_object.destination_object_id)

    unintegrated_objects = CustomObjectAccessor(
        client_id=client.get("client_id")
    ).get_objects_excluding_ids(integrated_object_ids)

    session = connect_to_snowflake(client.get("client_id"))

    unused_objects = []
    for unintegrated_object in unintegrated_objects:
        last_begin_or_end_date = get_last_begin_or_end_date(
            client_id=client.get("client_id"),
            object_id=unintegrated_object.custom_object_id,
            session=session,
        )
        if last_begin_or_end_date is not None and last_begin_or_end_date < threshold:
            unused_objects.append(
                {
                    "object_id": unintegrated_object.custom_object_id,
                    "object_name": unintegrated_object.name,
                }
            )

    if unused_objects:
        log_anomaly_with_nature(
            check_type=alert_dict["check_type"],
            client_id=client.get("client_id"),
            client_name=client.get("name"),
            module=MODULE_NAME,
            alert_name=alert_dict["alert_name"],
            created_at=datetime.now(timezone.utc),
            description=alert_dict["description"],
            alert_key=f"upstream_{client.get('client_id')}_{alert_dict['check_type']}",
            logger_email_id=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            assignee=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            cc_email_id="",
            env=os.environ.get("ENV"),
            metadata={
                "affected_data": unused_objects,
                "expected_value": {},
                "actual_value": {},
                "additional_context": {},
            },
        )
    else:
        log_heartbeat(
            client_id=client["client_id"],
            client_name=client["name"],
            check_type=alert_dict["check_type"],
            env=os.environ.get("ENV"),
            module=MODULE_NAME,
        )

    session.close()  # type: ignore

    logger.info(
        f'End non integrated unused objects check for client {client["name"]}-{client["client_id"]}'
    )


@shared_task(base=EverCeleryBaseTask)
def check_unuploaded_sync_data_wrapper(
    threshold: Optional[int] = 90, clients: Optional[List[int]] = None
):
    task_group = TaskGroupEnum.ANOMALY_DETECT.value
    active_clients = get_active_clients_features_excluding_churned()

    client_dicts = []
    for client in active_clients:
        client_id, name, details = client
        client_dicts.append(
            {
                "client_id": client_id,
                "name": name,
                "subscription": details.get("subscription_plan"),
            }
        )

    if clients:
        client_list = [
            client for client in client_dicts if client["client_id"] in clients
        ]
    else:
        client_list = client_dicts

    task = group(
        [
            check_unuploaded_sync_data.si(threshold, client).set(  # type: ignore
                queue=get_queue_name_respect_to_task_group(
                    client["client_id"], client["subscription"] or "BASIC", task_group
                )
            )
            for client in client_list
        ]
    )
    task.apply_async(compression="lzma", serializer="pickle")
