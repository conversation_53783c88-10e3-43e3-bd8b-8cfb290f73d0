# ruff: noqa
import logging
import os
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from os import environ
from typing import List, Optional

import numpy as np
import pandas as pd
from celery import group, shared_task
from django import setup
from django.db.models import <PERSON><PERSON>an<PERSON>ield, Case, Value, When
from django.utils import timezone

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly_with_nature,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.upstream_anomalies.helper import (
    MODULE_NAME,
    UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
    UpstreamAnomalyType,
    group_clients_for_parallel_processing,
)

environ.setdefault("DJANGO_SETTINGS_MODULE", "interstage_project.settings")
setup()

from commission_engine.accessors.client_accessor import (
    get_active_clients_features_excluding_churned,
)
from commission_engine.models.custom_object_models import CustomObject
from commission_engine.models.etl_config_models import ExtractionConfig
from commission_engine.models.etl_housekeeping_models import UpstreamETLStatus
from commission_engine.utils.general_data import (
    ETL_STATUS,
    SYNC_OBJECT,
    UpstreamSyncModes,
)

logger = logging.getLogger(__name__)


def calculate_z_score(historical_weeks, latest_week_total):
    mean_historical = np.mean(historical_weeks)
    std_historical = np.std(historical_weeks)
    threshold = 2

    if std_historical == 0:
        return True, None, "deviate"

    z_score = (latest_week_total - mean_historical) / std_historical

    is_anomaly = abs(z_score) > threshold
    if not is_anomaly:
        return is_anomaly, z_score, None

    anomaly_type = "spike" if z_score > 2 else "drop"
    return is_anomaly, z_score, anomaly_type


def calculate_modified_z_score(historical_weeks, latest_week_total, threshold):
    median_historical = np.median(historical_weeks)

    mad_historical = max(1, np.median(np.abs(historical_weeks - median_historical)))

    if mad_historical == 0:
        return True, None, "deviate"

    modified_z_score = 0.6745 * (latest_week_total - median_historical) / mad_historical

    is_anomaly = abs(modified_z_score) > threshold
    if not is_anomaly:
        return is_anomaly, modified_z_score, None

    anomaly_type = "spike" if modified_z_score > threshold else "drop"

    return is_anomaly, modified_z_score, anomaly_type


def enough_record_for_analysis(continuous_records):
    return len(continuous_records) >= 30 and (
        continuous_records[0].get("sync_start_time")
        < timezone.now() - timedelta(days=30)
    )


def filter_continuous_records(records):
    continuous_records = []
    previous_sync_end_time = None

    for record in records:
        if (
            previous_sync_end_time is None
            or record.get("changes_start_time") > previous_sync_end_time
        ):
            continuous_records.append(record)
            if record.get("changes_end_time"):
                previous_sync_end_time = record.get("changes_end_time")
            else:
                previous_sync_end_time = record.get("changes_start_time")

    return continuous_records


def group_records_by_week(continuous_records):
    df = pd.DataFrame(continuous_records)
    df["changes_start_time"] = pd.to_datetime(df["changes_start_time"])

    reference_start = df["changes_start_time"].min()
    df["week_start"] = (df["changes_start_time"] - reference_start).dt.floor(
        "7D"
    ) + reference_start

    weekly_summary = (
        df.groupby("week_start")["extracted_records_count"].sum().reset_index()
    )

    weekly_totals = weekly_summary["extracted_records_count"].tolist()

    return weekly_totals


def anomaly_detector(
    client, integrations, custom_object_map, upstream_etl_map, now, timeframe, threshold
):

    affected_records = []
    expected_averages = []
    actual_averages = []
    additional_context = []
    for integration in integrations:
        custom_object = custom_object_map.get(integration["destination_object_id"])
        records = upstream_etl_map.get(integration["integration_id"])

        if not custom_object:
            logger.info(
                f"custom object not found for integration_id: {integration['integration_id']}"
            )
            continue

        if not records:
            logger.info(
                f"No etl status records found for integration_id: {integration['integration_id']}"
            )
            continue

        historical_records = [record for record in records if record["is_historical"]]
        current_records = [record for record in records if not record["is_historical"]]

        if len(historical_records) > 2:
            historical_records = historical_records[2:]
        else:
            # logger.info(
            #     f"Not enough record for analysis for integration id: {integration['integration_id']}"
            # )
            continue

        historical_continuous_records = filter_continuous_records(historical_records)
        current_continuous_records = filter_continuous_records(current_records)

        if not enough_record_for_analysis(historical_continuous_records):
            # logger.info(
            #     f"Not enough record for analysis for integration id: {integration['integration_id']}"
            # )
            continue

        historical_weekly_totals = group_records_by_week(historical_continuous_records)
        current_week_total = 0
        for record in current_continuous_records:
            current_week_total += record.get("extracted_records_count") or 0

        if len(historical_weekly_totals) <= 2:
            # logger.info(
            #     f"Not enough record for analysis for integration id: {integration['integration_id']}"
            # )
            continue

        # is_anomaly_z, z_score, anomaly_type = calculate_z_score(
        #     historical_weekly_totals, current_week_total
        # )
        is_anomaly_modified_z, modified_z_score, anomaly_type = (
            calculate_modified_z_score(
                historical_weekly_totals, current_week_total, threshold
            )
        )

        if not is_anomaly_modified_z or not anomaly_type:
            continue

        expected_average = np.mean(historical_weekly_totals)

        actual_average = current_week_total
        violation = {"spike": "greater", "drop": "lesser", "deviate": "deviate"}

        expected_averages.append(expected_average)
        actual_averages.append(actual_average)
        affected_records.append(
            {
                "integration_id": str(integration["integration_id"]),
                "destination_object_id": integration["destination_object_id"],
                "destination_object_name": (
                    custom_object["name"] if custom_object else None
                ),
            }
        )
        additional_context.append(
            {
                "rationale": (
                    f"The extracted records count shows a {anomaly_type} over the last {timeframe} days. "
                    f"In the past, the average was {expected_average}, but the latest {timeframe}-day period recorded only {actual_average}. "
                    f"The Modified Z-score is {modified_z_score}, which is {violation[anomaly_type]} than the threshold = {threshold}, indicating a significant deviation."
                )
            }
        )

    alert_dict = UpstreamAnomalyType.EXTRACTED_RECORD_COUNT_ANOMALY.value
    if not affected_records:
        logger.info(
            f"No anomaly detected for client_{client.get('client_id')}_{client.get('name')}"
        )
        log_heartbeat(
            client_id=client.get("client_id"),
            client_name=client.get("name"),
            check_type=alert_dict["check_type"],
            env=os.environ.get("ENV"),
            module=MODULE_NAME,
        )

    for index in range(len(affected_records)):
        integration_id = affected_records[index].get("integration_id")
        metadata = {
            "affected_data": affected_records[index],
            "expected_value": expected_averages[index],
            "actual_value": actual_averages[index],
            "additional_context": additional_context[index],
        }

        log_anomaly_with_nature(
            check_type=alert_dict["check_type"],
            client_id=client.get("client_id"),
            client_name=client.get("name"),
            module=MODULE_NAME,
            alert_name=alert_dict["alert_name"],
            created_at=now.isoformat(),
            description=alert_dict["description"],
            alert_key=f"upstream_{client.get('client_id')}_{alert_dict['check_type']}",
            logger_email_id=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            assignee=UPSTREAM_ANOMALY_LOGGER_EMAIL_ID,
            cc_email_id="",
            env=os.environ.get("ENV"),
            metadata=metadata,
        )


@shared_task(base=EverCeleryBaseTask)
def detect_periodic_extracted_count_checker(
    clients: list[dict], timeframe=7, threshold=3.5, now=timezone.now()
):
    time_threshold = now - timedelta(days=timeframe)

    grouped_integrations = defaultdict(list)

    task_group = SYNC_OBJECT.UPSTREAM_WRAPPER.value
    integrations = ExtractionConfig.objects.filter(
        knowledge_end_date__isnull=True,
        task_group=task_group,
        is_disabled=False,
        sync_type=UpstreamSyncModes.CHANGES.value,
        client_id__in=[client["client_id"] for client in clients],
    ).values("destination_object_id", "integration_id", "client_id")

    destination_ids = []
    for integration in integrations:
        grouped_integrations[integration["client_id"]].append(integration)
        destination_ids.append(integration["destination_object_id"])

    custom_objects = CustomObject.objects.filter(
        is_deleted=False,
        knowledge_end_date__isnull=True,
        custom_object_id__in=destination_ids,
    ).values("custom_object_id", "name")

    custom_object_map = {
        str(custom_object["custom_object_id"]): custom_object
        for custom_object in custom_objects
    }

    records = list(
        UpstreamETLStatus.objects.filter(
            integration_id__in=[
                integration["integration_id"] for integration in integrations
            ],
            sync_status=ETL_STATUS.COMPLETE.value,
            sync_mode=UpstreamSyncModes.CHANGES.value,
            changes_start_time__isnull=False,
            changes_end_time__isnull=False,
            sync_start_time__isnull=False,
        )
        .values(
            "extracted_records_count",
            "changes_start_time",
            "changes_end_time",
            "integration_id",
            "sync_start_time",
        )
        .annotate(
            is_historical=Case(
                When(sync_start_time__lt=time_threshold, then=Value(True)),
                default=Value(False),
                output_field=BooleanField(),
            )
        )
        .order_by("sync_start_time")
    )

    upstream_etl_map = defaultdict(list)
    for record in records:
        upstream_etl_map[record["integration_id"]].append(record)

    alert_dict = UpstreamAnomalyType.EXTRACTED_RECORD_COUNT_ANOMALY.value
    for client in clients:
        client_integration = grouped_integrations[client["client_id"]]
        if not client_integration:
            log_heartbeat(
                client_id=client["client_id"],
                client_name=client.get("name"),
                check_type=alert_dict["check_type"],
                env=os.environ.get("ENV"),
                module=MODULE_NAME,
            )
            continue

        logger.info(
            f"Getting sync data for client_{client.get('client_id')}_{client.get('name')}..."
        )
        anomaly_detector(
            client,
            client_integration,
            custom_object_map,
            upstream_etl_map,
            now,
            timeframe,
            threshold,
        )

    logger.info(
        f"Completed checking upstream sync extracted count for client group - {[client['client_id'] for client in clients]}"
    )


@shared_task(base=EverCeleryBaseTask)
def detect_periodic_extracted_count_checker_wrapper(
    client_ids: Optional[List[int]] = None, timeframe=7, threshold=3.5
):
    now = timezone.now()
    logger.info(
        "Checking upstream sync extracted count for all active monitored clients"
    )

    active_clients = get_active_clients_features_excluding_churned()
    client_dicts = []
    for client in active_clients:
        client_id, name, details = client
        client_dicts.append(
            {
                "client_id": client_id,
                "name": name,
                "subscription": details.get("subscription_plan"),
            }
        )

    if client_ids:
        clients = [
            client for client in client_dicts if client["client_id"] in client_ids
        ]
    else:
        clients = client_dicts

    process_groups = group_clients_for_parallel_processing(clients, 20)
    task_group = TaskGroupEnum.ANOMALY_DETECT.value

    task = group(
        [
            detect_periodic_extracted_count_checker.si(
                clients=clients_list,
                timeframe=timeframe,
                threshold=threshold,
                now=now,
            ).set(
                queue=get_queue_name_respect_to_task_group(
                    clients_list[0]["client_id"],
                    clients_list[0]["subscription"] or "BASIC",
                    task_group,
                )
            )
            for clients_list in process_groups
        ]
    )
    task.apply_async(compression="lzma", serializer="pickle")
