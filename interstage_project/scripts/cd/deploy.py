"""
ECS Deployment Script

This script provides functionality to deploy and manage ECS (Elastic Container Service) services
in AWS. It supports both listing available services and deploying specific services with
rolling updates and deployment tracking.

Key Features:
- Loads deployment configuration from AWS Systems Manager Parameter Store
- Supports rolling updates for zero-downtime deployments
- Tracks deployment metadata (SHA, branch, URL, user, timestamp)
- Provides comprehensive error handling and debugging information
- Configurable timeout for service stability checks

Usage:
    python deploy.py <environment> --list-services
    python deploy.py <environment> --deploy --cluster-name <cluster> --service-name <service> --app-image <image> --nginx-image <image>
"""

import argparse
import json
import os
import sys
import time

import boto3
from botocore.exceptions import WaiterError


class ECS:
    """
    ECS deployment manager class that handles service deployment and management.

    This class provides methods to:
    - Load deployment configuration from SSM Parameter Store
    - List available services for deployment
    - Deploy services with rolling updates
    - Monitor deployment progress and stability
    """

    def __init__(self, env, ssm_prefix="/deployment_config/"):
        """
        Initialize the ECS deployment manager.

        Args:
            env (str): Environment name (e.g., 'dev', 'staging', 'prod')
            ssm_prefix (str): SSM parameter prefix for configuration storage
        """
        self.env = env
        self.ssm_prefix = ssm_prefix
        # Load deployment configuration from SSM Parameter Store
        self.config = self._load_config()
        # Initialize ECS client with the configured AWS region
        self.ecs = boto3.client("ecs", region_name=self._get_aws_region())

        # Build a mapping of services for quick lookup
        # Format: {cluster_name##service_name: service_config}
        _services = self.get_services()
        self.services_map = {}

        for service in _services:
            service_key = f"{service['cluster_name']}##{service['service_name']}"
            self.services_map[service_key] = service

    def _get_aws_region(self):
        """
        Get AWS region from environment variables.

        Returns:
            str: AWS region name

        Raises:
            SystemExit: If no AWS region is configured in environment variables
        """
        # Try environment variables first (AWS_REGION takes precedence)
        region = os.environ.get("AWS_PARAMETER_STORE_REGION") or os.environ.get(
            "AWS_DEFAULT_REGION"
        )
        if region:
            return region

        # Exit if no environment variable found
        print("Error: AWS_REGION or AWS_DEFAULT_REGION environment variable not found")
        sys.exit(1)

    def _load_config(self):
        """
        Load deployment configuration from AWS Systems Manager Parameter Store.

        The configuration contains service definitions, cluster information,
        and deployment settings for the specified environment.

        Returns:
            dict: Parsed JSON configuration from SSM Parameter Store
        """
        ssm = boto3.client("ssm", region_name=self._get_aws_region())
        param_name = f"{self.ssm_prefix}{self.env}"
        # print(f"Loading config from {param_name}")
        value = ssm.get_parameter(Name=param_name)["Parameter"]["Value"]
        # print(f"Loaded config: {value}")
        return json.loads(value)

    def get_services(self):
        """
        Returns a list of ECS service names or container names that should be deployed.
        This is used for GitHub Actions matrix to determine which services to deploy.

        Only returns services that are explicitly enabled in the configuration.

        Returns:
            list: List of enabled service configurations
        """
        if "services" in self.config:
            return [
                svc
                for svc in self.config["services"]
                if "enabled" in svc and svc["enabled"] == "true"
            ]
        return []

    def deploy_service(
        self, cluster_name, service_name, app_image, nginx_image, timeout_minutes=30
    ):
        """
        Deploys/updates a single ECS service by updating its image/tag and forcing new deployment.

        This method performs the following steps:
        1. Validates the service exists in configuration
        2. Fetches current task definition
        3. Updates container images (app and nginx)
        4. Adds deployment metadata as environment variables
        5. Registers new task definition revision
        6. Updates ECS service with new task definition
        7. Waits for service to become stable

        Args:
            cluster_name (str): The ECS cluster name
            service_name (str): The ECS service name (or container name, if using 1:1 mapping)
            app_image (str): Application container image URI
            nginx_image (str): Nginx container image URI
            timeout_minutes (int): Maximum time to wait for service to become stable (default: 30 minutes)

        Raises:
            Exception: If service not found in config or deployment fails
        """
        print(f"Starting deploy for: {service_name}")

        # Create service key for lookup in services map
        service_key = f"{cluster_name}##{service_name}"

        # Validate service exists in configuration
        if service_key not in self.services_map:
            raise Exception(f"Service {service_name} not found in config")
        service = self.services_map[service_key]

        aws_region = self.config["aws_region"]

        # Reinitialize ECS client with the correct region
        self.ecs = boto3.client("ecs", region_name=aws_region)

        # Fetch the current task definition to use as a template
        resp = self.ecs.describe_task_definition(
            taskDefinition=service["task_definition"]
        )
        task_def = resp["taskDefinition"]
        containers = task_def["containerDefinitions"]

        # Check if rolling update is enabled for this service
        # Rolling updates allow zero-downtime deployments by maintaining 100% capacity
        rolling_update = (
            True
            if "rolling_update" in service and service["rolling_update"] == "true"
            else False
        )

        # Get deployment details from environment variables
        # These are typically set by CI/CD pipeline (GitHub Actions)
        github_sha_short = os.environ.get("GITHUB_SHA_SHORT", "unknown")
        deployed_branch = os.environ.get("DEPLOYED_BRANCH", "unknown")
        deployment_url = os.environ.get("DEPLOYMENT_URL", "unknown")
        deployed_by = os.environ.get("DEPLOYED_BY", "unknown")
        deployed_at = os.environ.get("DEPLOYED_AT", "unknown")

        # Prepare deployment environment variables to track deployment metadata
        # These will be added to the application containers for observability
        deployment_env_vars = []
        if github_sha_short:
            deployment_env_vars.append(
                {"name": "GITHUB_SHA_SHORT", "value": github_sha_short}
            )
        if deployed_branch:
            deployment_env_vars.append(
                {"name": "DEPLOYED_BRANCH", "value": deployed_branch}
            )
        if deployment_url:
            deployment_env_vars.append(
                {"name": "DEPLOYMENT_URL", "value": deployment_url}
            )
        if deployed_by:
            deployment_env_vars.append({"name": "DEPLOYED_BY", "value": deployed_by})
        if deployed_at:
            deployment_env_vars.append({"name": "DEPLOYED_AT", "value": deployed_at})

        # Update container definitions with new images and deployment metadata
        for cdef in containers:
            if cdef["name"] == "nginx":
                # Update nginx container with new image
                cdef["image"] = nginx_image
            else:
                # Update application container with new image
                cdef["image"] = app_image
                # Add deployment environment variables to application containers for tracking
                if deployment_env_vars:
                    if "environment" not in cdef:
                        cdef["environment"] = []
                    cdef["environment"].extend(deployment_env_vars)

        # Register new revision of the task definition
        # Only include fields that are present in the original task definition
        reg_args = {
            k: task_def[k]
            for k in [
                "family",
                "taskRoleArn",
                "executionRoleArn",
                "networkMode",
                "containerDefinitions",
                "volumes",
                "placementConstraints",
                "requiresCompatibilities",
                "cpu",
                "memory",
            ]
            if k in task_def
        }
        reg_resp = self.ecs.register_task_definition(**reg_args)
        new_task_def_arn = reg_resp["taskDefinition"]["taskDefinitionArn"]
        print(f"✅ Registered new task definition: {new_task_def_arn}")

        # Prepare update service parameters
        update_params = {
            "cluster": cluster_name,
            "service": service_name,
            "taskDefinition": new_task_def_arn,
            "forceNewDeployment": True,  # Force immediate deployment of new task definition
        }

        # Add rolling update specific parameters if enabled
        # This ensures zero-downtime deployments by maintaining 100% capacity
        if rolling_update:
            update_params.update(
                {
                    "minimumHealthyPercent": 100,  # Maintain at least 100% capacity
                    "maximumPercent": 200,  # Allow up to 200% capacity during deployment
                }
            )

        # Update ECS service to use new task definition
        self.ecs.update_service(**update_params)
        print(f"✅ Updated ECS service {service_name}")

        # Wait for the service to become stable with better error handling
        print("⏳ Waiting for service to become stable...")
        try:
            # Configure waiter with custom timeout
            waiter = self.ecs.get_waiter("services_stable")
            waiter_config = {
                "Delay": 30,  # Wait 30 seconds between checks
                "MaxAttempts": (timeout_minutes * 60)
                // 30,  # Convert minutes to attempts (30 seconds per attempt)
            }
            waiter.wait(
                cluster=cluster_name,
                services=[service_name],
                WaiterConfig=waiter_config,
            )
            print(f"✅ {service_name} is stable. Deploy complete.")
        except WaiterError as e:
            print(f"❌ Waiter timeout exceeded after {timeout_minutes} minutes")
            print(f"Error details: {e}")

            # Get service status for debugging when deployment fails
            try:
                service_response = self.ecs.describe_services(
                    cluster=cluster_name, services=[service_name]
                )
                if service_response["services"]:
                    service_info = service_response["services"][0]
                    print(f"Service status: {service_info.get('status', 'unknown')}")
                    print(
                        f"Desired count: {service_info.get('desiredCount', 'unknown')}"
                    )
                    print(
                        f"Running count: {service_info.get('runningCount', 'unknown')}"
                    )
                    print(
                        f"Pending count: {service_info.get('pendingCount', 'unknown')}"
                    )

                    # Check for recent deployments to understand what went wrong
                    deployments = service_info.get("deployments", [])
                    if deployments:
                        print("Recent deployments:")
                        for deployment in deployments[:3]:  # Show last 3 deployments
                            print(
                                f"  - {deployment.get('status', 'unknown')}: {deployment.get('taskDefinition', 'unknown')}"
                            )
            except Exception as debug_error:
                print(f"Could not get service details for debugging: {debug_error}")

            raise Exception(
                f"Service {service_name} failed to become stable within {timeout_minutes} minutes"
            )


if __name__ == "__main__":
    # Set up command line argument parser
    parser = argparse.ArgumentParser(description="ECS deployment script")
    parser.add_argument("env", help="Environment name (e.g., dev, staging, prod)")
    parser.add_argument(
        "--list-services",
        action="store_true",
        help="List available services for deployment",
    )
    parser.add_argument(
        "--deploy", action="store_true", help="Deploy a specific service"
    )
    parser.add_argument("--cluster-name", help="ECS cluster name")
    parser.add_argument("--service-name", help="ECS service name")
    parser.add_argument("--app-image", help="Application image URI")
    parser.add_argument("--nginx-image", help="Nginx image URI")
    parser.add_argument(
        "--timeout-minutes",
        type=int,
        default=30,
        help="Timeout in minutes for service to become stable (default: 30)",
    )

    args = parser.parse_args()

    # Initialize ECS deployment manager
    ecs = ECS(args.env)

    # Handle different command modes
    if args.list_services:
        # List available services (used by GitHub Actions matrix)
        services = ecs.get_services()
        print(json.dumps(services))
    elif args.deploy:
        # Validate required arguments for deployment
        if not all(
            [args.cluster_name, args.service_name, args.app_image, args.nginx_image]
        ):
            print(
                "Error: --deploy requires --cluster-name, --service-name, --app-image, and --nginx-image"
            )
            parser.print_help()
            sys.exit(1)
        # Execute deployment
        ecs.deploy_service(
            cluster_name=args.cluster_name,
            service_name=args.service_name,
            app_image=args.app_image,
            nginx_image=args.nginx_image,
            timeout_minutes=args.timeout_minutes,
        )
    else:
        # Show help if no valid command provided
        parser.print_help()
        sys.exit(1)
