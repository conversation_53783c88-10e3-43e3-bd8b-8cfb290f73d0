<!DOCTYPE html>
<html lang="en">
  <head>
    <base href="/" />
    <meta charset="utf-8" />
    <link rel="icon" href="https://du4a3cteiciwm.cloudfront.net/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Automate sales commissions.
      Motivate payees."
    />
    <link rel="apple-touch-icon" href="/favicon.svg" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- <link rel="manifest" href="manifest.json" /> -->
    <link
      href="https://fonts.googleapis.com/css?family=IBM+Plex+Sans:400,500,600"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap"
      rel="stylesheet"
    />
    <!-- Template variable that will be replaced by esbuild cachebuster plugin in the esbuild-plugins.mjs file-->
    <link rel="manifest" href="manifest.json" />
    <link rel="stylesheet" href="%index.css%" />
    <title>Everstage</title>
    <style>
      .init-loader {
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
      body {
        margin: 0;
      }
    </style>
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div
      id="root"
      style="
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      "
    >
      <div
        style="
          width: 100vw;
          height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <svg
          class="init-loader"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid"
          style="
            background: none;
            display: block;
            shape-rendering: auto;
            width: 40px;
            height: 40px;
          "
        >
          <circle
            cx="50"
            cy="50"
            r="40"
            stroke="#0A1C53"
            fill="none"
            stroke-width="10"
            stroke-dasharray="125.664"
            transform="rotate(-90 50 50)"
            stroke-linecap="round"
          ></circle>
        </svg>
      </div>
    </div>
    <script type="module" src="%index.js%"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
