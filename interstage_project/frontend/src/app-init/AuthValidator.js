import Arrow<PERSON>eftOutlined from "@ant-design/icons/ArrowLeftOutlined";
import MailTwoTone from "@ant-design/icons/MailTwoTone";
import { useAuth0 } from "@auth0/auth0-react";
import { Result, Row } from "antd";
import { observer } from "mobx-react";
import React, { Suspense, useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import { LOGIN_METHOD, LOGIN_METHOD_TO_AUTH0_CONNECTION } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverButton, EverLoader } from "~/v2/components";
import IFrameLogin from "~/v2/features/iframe-login";

const PublicLinkApp = React.lazy(() => import("~/v2/public-link"));

const STATUS_VALUES = {
  NOT_VERIFIED: "NOT_VERIFIED",
  SUCCESS: "SUCCESS",
  IFRAME_LOGIN: "IFRAME_LOGIN",
};

// This component will be displayed when the user's email is not verified in
// Auth0 in case of username password authentication.
const EmailNotVerified = ({ logoutCbk }) => {
  return (
    <div className="antdButtonWrapperClass flex flex-col items-center">
      <Result
        icon={<MailTwoTone />}
        title={
          <span className="not-invited-title">
            Please verify your email address and try again!
          </span>
        }
      />
      <EverButton
        color="primary"
        icon={<ArrowLeftOutlined />}
        onClick={logoutCbk}
      >
        Go back
      </EverButton>
    </div>
  );
};

const AuthedValidator = observer(({ children }) => {
  const isInIframe = window.self !== window.top;
  const queryParams = new URLSearchParams(window.location.search);
  // set sessionId to sessionStorage
  sessionStorage.setItem("sessionId", uuidv4());

  const auth0client = useAuth0();

  // Stores the status of the logging in user. Possible values are:
  // * IFRAME_LOGIN: User is in iframe and needs to login.
  // * NOT_VERIFIED: User is added to the application but not in auth0
  // * SUCCESS: User can successfully login.
  const [userStatus, setUserStatus] = useState(null);

  const {
    isLoading,
    loginWithRedirect,
    loginWithPopup,
    isAuthenticated,
    getAccessTokenSilently,
    getIdTokenClaims,
  } = auth0client;

  const authStore = useAuthStore();
  const { isEmailVerified, loading } = authStore;

  if (window.location.pathname.includes("/public")) {
    const pathParts = window.location.pathname.split("/");
    const publicAccessToken = pathParts.pop() || "";

    return (
      <Suspense
        fallback={
          <div className="h-full flex items-center justify-center">
            <EverLoader
              indicatorType="spinner"
              className="w-screen h-screen"
              size={12}
              spinning
            />
          </div>
        }
      >
        <PublicLinkApp publicAccessToken={publicAccessToken} />
      </Suspense>
    );
  }

  const userAsyncLogout = () => {
    authStore.logoutManager();
    authStore.killSession();
  };

  useEffect(() => {
    if (isLoading) {
      authStore.setLoading({
        status: true,
        stage: "Sales champion mode: ON!",
        percentage: 0,
      });
    } else {
      authStore.setLoading({ status: false });
      if (!isAuthenticated) {
        const skipAutoIFrameLogin =
          queryParams.get("skipautoiframelogin") === "true";
        if (isInIframe) {
          // If Everstage is loaded in iframe, then automatically peform salesforce login.
          // In case skipautoiframelogin is set to true, then don't perform this auto-login
          // and show the iframe login page instead.
          if (skipAutoIFrameLogin) {
            setUserStatus(STATUS_VALUES.IFRAME_LOGIN);
          } else {
            loginWithPopup({
              appState: {
                returnTo: window.location.pathname + window.location.search,
              },
              connection:
                LOGIN_METHOD_TO_AUTH0_CONNECTION[LOGIN_METHOD.SALESFORCE],
            });
          }
          return;
        }
        // If a login method is provided in the query params, then perform the login
        // using that method.
        // For example, if loginmethod=google is provided in the query params, then
        // perform login using google sso directly.
        const loginMethod = queryParams.get("loginmethod");
        const connection = LOGIN_METHOD_TO_AUTH0_CONNECTION?.[loginMethod];
        loginWithRedirect({
          appState: {
            returnTo: window.location.pathname + window.location.search,
          },
          ...(connection ? { connection } : {}),
        });
      }
    }
  }, [isLoading, isAuthenticated]);

  useEffect(() => {
    const getAccessToken = async () => {
      const accessToken = await getAccessTokenSilently({
        audience: `https://everstage-icm`,
        scope: "create:user",
      });
      const idTokenClaims = await getIdTokenClaims();
      authStore.setAccessToken(accessToken);
      authStore.setIdTokenClaims(idTokenClaims);
      authStore.setAuth0(auth0client);

      if (isEmailVerified) {
        setUserStatus(STATUS_VALUES.SUCCESS);
      } else {
        setUserStatus(STATUS_VALUES.NOT_VERIFIED);
      }
    };

    if (isAuthenticated) getAccessToken();
  }, [isEmailVerified, isAuthenticated, auth0client]);

  const { stage } = loading || {};

  // Setting the content to be visible based on user status
  let content = (
    <div className="h-screen w-screen flex justify-center items-center">
      <EverLoader tip={stage || ""} indicatorType="logo" spinning />
    </div>
  );
  if (userStatus === STATUS_VALUES.NOT_VERIFIED) {
    content = <EmailNotVerified logoutCbk={userAsyncLogout} />;
  } else if (userStatus === STATUS_VALUES.IFRAME_LOGIN) {
    content = <IFrameLogin />;
  }

  return userStatus === STATUS_VALUES.SUCCESS ? (
    children
  ) : (
    <Row justify="center" align="middle" className="h-full bg-ever-base">
      <div className="flex w-full h-full justify-center items-center">
        {content}
      </div>
    </Row>
  );
});

export default AuthedValidator;
