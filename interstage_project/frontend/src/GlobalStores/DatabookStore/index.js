import { gql, useApolloClient } from "@apollo/client";
import { sortBy } from "lodash";
import { makeAutoObservable } from "mobx";
import { useLocalStore } from "mobx-react";
import React from "react";
import { RBAC_ROLES } from "~/Enums";

const ALL_DATABOOK_DETAILS_QUERY = gql`
  query AllDatabookDetails {
    allDatabookDetails {
      databookId
      name
      isDraft
      isArchived
      additionalDetails

      datasheet {
        datasheetId
        databookId
        name
        primaryKey
        orderedColumns
        dataOrigin
      }
    }
  }
`;

class DatabookStore {
  databookLoading = false;
  databooks = [];
  datasheets = [];
  isAllowed = false;

  constructor(apolloClient, userPermissions) {
    this.apolloClient = apolloClient;
    this.isAllowed = this.isAllowedToFetch(userPermissions);
    if (this.isAllowed) {
      this.databookLoading = true;
      apolloClient
        .query({ query: ALL_DATABOOK_DETAILS_QUERY })
        .then((result) => {
          this.setDatabookData(result.data);
          this.databookLoading = false;
        })
        .catch((error) => {
          console.error(error);
          this.databookLoading = false;
        });
    }
    makeAutoObservable(this);
  }

  isAllowedToFetch = (userPermissions) => {
    const databookPermissions = new Set([
      RBAC_ROLES.VIEW_COMMISSIONPLAN,
      RBAC_ROLES.MANAGE_DATASETTINGS,
      RBAC_ROLES.VIEW_DATABOOK,
      RBAC_ROLES.MANAGE_ALL_ADMINS,
      RBAC_ROLES.MANAGE_REPORTENRICH,
      RBAC_ROLES.VIEW_PAYOUTS,
      RBAC_ROLES.VIEW_STATEMENTS,
    ]);

    const permissionsAllowed = userPermissions.filter((permission) =>
      databookPermissions.has(permission)
    );
    return permissionsAllowed.length > 0;
  };

  refetchDatabooks = () => {
    if (this.isAllowed) {
      this.databookLoading = true;
      this.apolloClient
        .query({ query: ALL_DATABOOK_DETAILS_QUERY, fetchPolicy: "no-cache" })
        .then((result) => {
          this.setDatabookData(result.data);
          this.databookLoading = false;
        })
        .catch((error) => {
          console.error(error);
          this.databookLoading = false;
        });
    }
  };

  setDatabookData = (data) => {
    const filteredDatabooks = (data?.allDatabookDetails ?? []).filter(
      (db) =>
        JSON.parse(db.additionalDetails ?? "{}")
          .analytics_default_dashboard_databook !== true
    );
    this.databooks = filteredDatabooks;
    this.datasheets = this.databooks.flatMap((db) => db.datasheet ?? []);
  };

  getDatasheetData = (datasheetId) => {
    return this.datasheets.find((x) => x.datasheetId === datasheetId);
  };

  getDatabookData = (databookId) => {
    return this.databooks.find((x) => x.databookId === databookId);
  };

  getDatasheetName = (datasheetId) => {
    return this.getDatasheetData(datasheetId)?.name;
  };

  getDatasheetPrimaryKey = (datasheetId) => {
    return JSON.parse(this.getDatasheetData(datasheetId)?.primaryKey ?? "[]");
  };

  getDatasheetOrderedColumns = (datasheetId) => {
    return this.getDatasheetData(datasheetId)?.orderedColumns ?? [];
  };

  getDatasheets = (databookId) => {
    return this.getDatabookData(databookId)?.datasheet ?? [];
  };

  getDatasheetsByOrigin = (databookId, dataOrigin) => {
    return this.getDatasheets(databookId).filter((ds) =>
      (dataOrigin ?? []).includes(ds.dataOrigin)
    );
  };

  getSortedDatasheetsByOrigin = (databookId, dataOrigin) => {
    const datasheets = this.getDatasheets(databookId).filter((ds) =>
      (dataOrigin ?? []).includes(ds.dataOrigin)
    );
    return sortBy(datasheets, [(datasheet) => datasheet.name.toLowerCase()]);
  };

  getDatasheetOrigin = (datasheetId) => {
    return this.getDatasheetData(datasheetId)?.dataOrigin;
  };

  get sortedDatabooks() {
    return sortBy(this.databooks, [(databook) => databook.name.toLowerCase()]);
  }

  get activeSortedDatabooks() {
    return sortBy(
      this.databooks.filter((db) => !db.isArchived),
      [(databook) => databook.name.toLowerCase()]
    );
  }

  get activeDatabooks() {
    return this.databooks.filter((db) => !db.isArchived);
  }

  get isDatabookLoading() {
    return this.databookLoading;
  }
}

const databooksContext = React.createContext(null);

export const DatabookStoreProvider = (props) => {
  const { children, userPermissions } = props;
  const client = useApolloClient();
  const databookStore = useLocalStore(
    () => new DatabookStore(client, userPermissions)
  );
  return (
    <databooksContext.Provider value={databookStore}>
      {databookStore ? children : null}
    </databooksContext.Provider>
  );
};

export const useDatabookStore = () => {
  return React.useContext(databooksContext);
};
