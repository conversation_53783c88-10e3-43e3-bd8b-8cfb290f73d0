import { isEmpty } from "lodash";
import { useEffect, useState, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import { DASHBOARD_URLS, RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { MODULES } from "~/Utils";
import { getDashboardsDetails } from "~/v2/features/dashboard23/restApi";
import {
  addMissingDashboardIds,
  dashboardsUrl,
} from "~/v2/features/dashboard23/utils.js";

export const useQueryParams = () => {
  const { search } = useLocation();

  return useMemo(() => new URLSearchParams(search), [search]);
};

export const useModules = () => {
  const module = window.localStorage.getItem("module");

  return {
    isCPQ: module === MODULES.CPQ,
    isICM: module === MODULES.ICM,
  };
};

export const useFetchApiWithAuth = () => {
  // Retrieve the access token from the authentication store
  const { accessToken } = useAuthStore();

  // Define the fetchData function for making API requests
  const fetchData = async (
    url,
    method = "GET",
    body = null,
    additionalOptions = {}
  ) => {
    const isFormData = body instanceof FormData;
    try {
      // Prepare the request options, including the access token in the Authorization header
      const headers = {
        Authorization: `Bearer ${accessToken}`,
      };
      if (!isFormData) {
        headers["Content-Type"] = "application/json";
      }
      const options = {
        method,
        headers,
        body: body ? (isFormData ? body : JSON.stringify(body)) : null,
        ...(isEmpty(additionalOptions) ? {} : additionalOptions),
      };

      // Make the API request using fetch
      const response = await fetch(url, options);

      // Parse the response JSON
      const data = await response.json();

      // Check if the response is ok (status code in the 2xx range)
      if (!response.ok) {
        // If there's an error in the response data, extract the error message
        const errorMessage =
          data?.error?.message || data?.message || "Something went wrong";

        // Throw an Error object with the error message
        throw new Error(errorMessage);
      }

      // If the response is successful, return the data
      return data;
    } catch (error) {
      // Log the error to the console for debugging
      console.error("Error fetching data:", error);

      // Re-throw the error to propagate it to the caller
      throw error;
    }
  };

  // Return the fetchData function for use in components
  return { fetchData };
};

const useDashboardInformation = () => {
  const [dashboardType, setDashboardType] = useState(null);
  const [dashboards, setDashboards] = useState([]);
  const [dashboardsLoading, setDashboardsLoading] = useState(false);
  const { hasPermissions } = useUserPermissionStore();
  const navigate = useNavigate();
  const { accessToken } = useAuthStore();
  const { pathname } = useLocation();
  const query = useQueryParams();

  const loggedInUserDashboards = dashboards.map((dashboard) => ({
    label: dashboard.name || "",
    value: dashboard.dashboardId || "",
    type: dashboard.dashboardType || "",
  }));
  const dashboardId = query.get("id");
  const type = query.get("type");

  useEffect(() => {
    if (dashboardId && type && loggedInUserDashboards.length > 0) {
      setDashboardType(dashboardId);
    } else {
      setDashboardType(null);
    }
  }, [dashboardId, type, loggedInUserDashboards]);

  const canLoadDashboards = () => {
    /* Checks if dashboards need to be pre-loaded */
    if (
      !["/", DASHBOARD_URLS.DASHBOARD, DASHBOARD_URLS.DASHBOARDS].includes(
        pathname
      )
    ) {
      return false;
    }
    if (pathname === DASHBOARD_URLS.DASHBOARDS && query.get("type") === "all") {
      return false;
    }
    if (!hasPermissions(RBAC_ROLES.VIEW_DASHBOARD)) {
      return false;
    }
    if (query.get("redirect_to")) {
      return false;
    }
    return true;
  };

  const getDashboardsWrapper = async () => {
    /* Get dashboards from state variable or from API call */
    if (dashboards.length > 0) {
      return dashboards;
    }
    return getDashboards();
  };

  const getDashboards = async () => {
    /* Get dashboards from API call */
    let _dashboards;
    setDashboardsLoading(true);
    const response = await getDashboardsDetails(accessToken, "all-dashboards");
    if (response?.status === "Failure" || response?.data?.length === 0) {
      setDashboardsLoading(false);
      return [];
    }
    _dashboards = addMissingDashboardIds(response?.data);
    setDashboards(_dashboards);
    setDashboardsLoading(false);
    return _dashboards;
  };

  const redirectToDashboard = (_dashboards) => {
    /* Redirect to the default or a specific dashboard */
    if (!_dashboards.length) {
      return;
    }
    if (
      pathname != DASHBOARD_URLS.DASHBOARD ||
      !query.get("id") ||
      !query.get("type")
    ) {
      navigate(
        dashboardsUrl(_dashboards[0].dashboardId, _dashboards[0].dashboardType)
      );
    }
  };

  useEffect(() => {
    /* Load dashboards & redirect to specific dashboard */
    if (!canLoadDashboards()) {
      return;
    }
    getDashboardsWrapper()
      .then(redirectToDashboard)
      .catch((err) => {
        console.log("Failure while getting dashboards: ", err);
      })
      .finally(() => {
        setDashboardsLoading(false);
      });
  }, [pathname, query]);

  const handleDashboardChange = (value, option) => {
    navigate(`${DASHBOARD_URLS.DASHBOARD}?id=${value}&type=${option.type}`);
  };

  return {
    dashboardType,
    handleDashboardChange,
    loggedInUserDashboards,
    dashboardsLoading,
    getDashboards,
  };
};

export default useDashboardInformation;
