import * as React from "react";
import { ButtonProps } from "antd/es/button";

/**
 * Props for EverButton component. Extends antd ButtonProps and adds EverButton-specific props.
 *
 * @example
 * // Basic usage
 * <EverButton type="filled">Click me</EverButton>
 *
 * @example
 * // With icon
 * <EverButton type="ghost" prependIcon={<DownloadIcon />}>
 *   Download
 * </EverButton>
 */
export interface EverButtonProps extends Omit<ButtonProps, "type" | "size"> {
  /**
   * Set the type of the button. 'filled' maps to antd 'primary'.
   * @default "filled"
   */
  type?: "filled" | "ghost" | "text" | "link" | "dashed";
  /**
   * Set the color of the button.
   * @default "primary"
   */
  color?: "primary" | "success" | "error" | "info" | "warning" | "base";
  /**
   * Set the size of the button.
   * @default "medium"
   */
  size?: "large" | "small" | "medium";
  /**
   * Set the icon to be prepended in front of the button text. Accepts SVG or React elements.
   */
  prependIcon?: React.ReactNode;
  /**
   * Set the icon to be appended after the button text. Accepts SVG or React elements.
   */
  appendIcon?: React.ReactNode;
  /**
   * Provide custom size for the append icon. If not provided, it will take the size of the button.
   */
  appendIconSize?: "large" | "small" | "medium" | "middle";
  /**
   * Tooltip text to show on hover.
   */
  tooltipTitle?: string;
  /**
   * Children (button content).
   */
  children?: React.ReactNode;
  /**
   * Hide the loader.
   */
  hideLoader?: boolean;
}

/**
 * Icon button variant of EverButton.
 */
export interface EverButtonIconProps
  extends Omit<EverButtonProps, "prependIcon" | "appendIcon"> {
  /**
   * The icon to display in the button.
   */
  icon: React.ReactNode;
}

export interface EverButtonComponent
  extends React.ForwardRefExoticComponent<
    EverButtonProps & React.RefAttributes<any>
  > {
  /**
   * Icon button variant of EverButton.
   * @example
   * <EverButton.Icon icon={<DownloadIcon />} />
   */
  Icon: React.ForwardRefExoticComponent<
    EverButtonIconProps & React.RefAttributes<any>
  >;
}
