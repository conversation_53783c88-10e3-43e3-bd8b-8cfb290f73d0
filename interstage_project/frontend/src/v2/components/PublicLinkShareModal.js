import {
  XCloseIcon,
  LinkIcon,
  MailIcon,
  TrashIcon,
  CheckCircleIcon,
} from "@everstage/evericons/outlined";
import { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { useQuery } from "react-query";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverButton,
  EverTg,
  EverInput,
  EverSwitch,
  EverTooltip,
  EverLoader,
  EverModal,
  message,
} from "~/v2/components";

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const entityMap = {
  quote: "Quote",
  dealroom: "Deal room",
};

const entityDefaultMessage = {
  quote: {
    subject: "Your quote is ready for review",
    message:
      "I've created a quote based on our recent discussion. Let me know if you'd like to walk through it together or if there's anything you'd like to adjust.",
  },
  dealroom: {
    subject: "Invite to collaborate on a deal room",
    message:
      "I have put together a custom deal room for you - a single space where you can explore product demos, case studies, keep track of action items and next steps. Let me know if you have any questions or need a walkthrough.",
  },
};

const sharePublicLink = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };
  const response = await fetch("/ninja/public_link/share", requestOptions);
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "Something went wrong");
  }
  return data;
};

export const createPublicLink = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };
  const response = await fetch("/ninja/public_link/create", requestOptions);
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "Something went wrong");
  }
  return data;
};

export function PublicLinkShareModal({
  visible,
  onCancel,
  entity,
  publicLink,
  isLoading,
  publicLinkId,
  sharedUsers,
}) {
  const { accessToken } = useAuthStore();

  const [emails, setEmails] = useState(sharedUsers || []);
  const [addedEmails, setAddedEmails] = useState([]);
  const [deletedEmails, setDeletedEmails] = useState([]);
  const [isCopied, setIsCopied] = useState(false);
  const [input, setInput] = useState("");
  const [notify, setNotify] = useState(false);
  const [inputError, setInputError] = useState("");
  const [additionalMessage, setAdditionalMessage] = useState(
    entityDefaultMessage[entity]?.message || ""
  );
  const [subject, setSubject] = useState(
    entityDefaultMessage[entity]?.subject || ""
  );
  const inputRef = useRef();
  // Initialize emails from sharedUsers if available
  useEffect(() => {
    if (sharedUsers && sharedUsers.length > 0) {
      setEmails(sharedUsers);
      setAddedEmails([]);
      setDeletedEmails([]);
    }
  }, [sharedUsers]);

  const {
    isLoading: isSharePublicLinkLoading,
    refetch: refetchSharePublicLink,
  } = useQuery(
    ["share-entity"],
    () =>
      sharePublicLink(
        {
          public_link_id: publicLinkId,
          shared_users: emails,
          added_emails: addedEmails,
          deleted_emails: deletedEmails,
          notify: notify,
          subject: subject,
          additional_message: additionalMessage,
        },
        accessToken
      ),
    {
      enabled: false,
      onSuccess: () => {
        message.success(`${entityMap[entity]} shared successfully`);
        onCancel();
      },
    }
  );

  // Reset all state when modal is closed
  useEffect(() => {
    if (!visible) {
      setEmails(sharedUsers || []); // Reset to sharedUsers if available, otherwise empty array
      setAddedEmails([]);
      setDeletedEmails([]);
      setInput("");
      setNotify(false);
      setInputError("");
      setAdditionalMessage(entityDefaultMessage[entity]?.message || "");
      setSubject(entityDefaultMessage[entity]?.subject || "");
    }
  }, [visible, sharedUsers, entity]);

  const handleAddEmail = () => {
    if (!input) return;
    if (!emailRegex.test(input)) {
      setInputError("Please enter a valid email address");
      return;
    }
    if (emails.includes(input)) {
      setInputError("Email already added");
      return;
    }
    setEmails([...emails, input]);
    if (!(sharedUsers || []).includes(input)) {
      setAddedEmails([...addedEmails, input]);
    }
    // If the email was previously deleted, remove from deletedEmails
    setDeletedEmails(deletedEmails.filter((e) => e !== input));
    setInput("");
    setInputError("");
  };

  const handleInputKeyDown = (e) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleAddEmail();
    }
  };

  const handleInputChange = (e) => {
    setInput(e.target.value);
    if (inputError) setInputError("");
  };

  const removeEmail = (email) => {
    console.log("removeEmail", email);
    setEmails(emails.filter((e) => e !== email));
    if ((sharedUsers || []).includes(email)) {
      setDeletedEmails([...deletedEmails, email]);
    }
    setAddedEmails(addedEmails.filter((e) => e !== email));
  };

  const handleCopyLink = () => {
    if (publicLink && !isCopied) {
      navigator.clipboard.writeText(publicLink);
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
      }, 5000);
    }
  };

  return isLoading ? (
    createPortal(
      <EverLoader className="absolute" wrapperClassName="z-[1000]" />,
      document.querySelector("#root")
    )
  ) : (
    <EverModal
      visible={visible}
      onCancel={onCancel}
      destroyOnClose
      centered
      width={624}
      footer={
        <div className="flex justify-between items-center w-full mt-1">
          <div>
            {isCopied ? (
              <EverTg.Text className="flex items-center text-ever-primary font-medium">
                <CheckCircleIcon className="w-4 h-4 mr-1" />
                Link Copied!
              </EverTg.Text>
            ) : (
              <EverButton
                type="ghost"
                prependIcon={<LinkIcon />}
                size="small"
                onClick={handleCopyLink}
                disabled={!publicLink || isLoading}
              >
                {isLoading
                  ? "Loading..."
                  : `Copy ${entityMap[entity].toLowerCase()} link`}
              </EverButton>
            )}
          </div>
          <div className="flex gap-1">
            <EverButton
              type="filled"
              color="base"
              size="medium"
              onClick={onCancel}
            >
              Cancel
            </EverButton>
            <EverTooltip
              title={emails.length === 0 ? "Add at least one users" : ""}
              mode="hover"
            >
              <EverButton
                type="filled"
                color="primary"
                size="medium"
                disabled={emails.length === 0 || isSharePublicLinkLoading}
                onClick={() => refetchSharePublicLink()}
              >
                Share
              </EverButton>
            </EverTooltip>
          </div>
        </div>
      }
    >
      <div className="h-[500px] flex flex-col">
        {isLoading ? (
          <div className="flex items-center justify-center h-full z-[1000]">
            <EverLoader />
          </div>
        ) : (
          <>
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <EverTg.Heading2>
                Share {entityMap[entity].toLowerCase()}
              </EverTg.Heading2>
              <EverButton type="text" size="small" onClick={onCancel} />
            </div>

            {/* Invite Buyers */}
            <EverTg.Text className="font-medium text-ever-base-content-mid">
              Invite users <span className="text-ever-error">*</span>
            </EverTg.Text>
            <div
              className={`flex flex-wrap items-start gap-2 mt-2 px-2 py-1 rounded-lg border-2 transition-all ${
                inputError
                  ? "border-ever-error"
                  : "border-ever-primary focus-within:border-ever-primary"
              }`}
              onClick={() => inputRef.current && inputRef.current.focus()}
            >
              {emails.map((email) => (
                <span
                  key={email}
                  className="flex items-center bg-ever-base-100 text-ever-base-content px-3 py-1 rounded-full mr-1 mb-1"
                >
                  {email}
                  <button
                    type="button"
                    className="ml-2"
                    onClick={() => removeEmail(email)}
                    tabIndex={-1}
                  >
                    <XCloseIcon className="w-4 h-4 text-ever-base-content-mid" />
                  </button>
                </span>
              ))}
              <input
                ref={inputRef}
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                className="flex-1 min-w-[120px] outline-none border-none bg-transparent py-1"
                placeholder={
                  emails.length === 0
                    ? "Enter mail address to add users..."
                    : ""
                }
              />
            </div>
            {inputError && (
              <EverTg.Caption className="text-ever-error mt-1">
                {inputError}
              </EverTg.Caption>
            )}

            {/* Shared With */}
            {emails.length > 0 && (
              <div className="mt-4">
                <EverTg.Caption className="uppercase tracking-wide mb-2 font-medium">
                  SHARED WITH ({emails.length})
                </EverTg.Caption>
                <div className="flex flex-col gap-2">
                  {emails.map((email) => (
                    <div
                      key={email}
                      className="flex items-center bg-ever-base-25 rounded-lg px-3 py-1.5"
                    >
                      <MailIcon className="w-5 h-5 mr-2 text-ever-base-content-mid" />
                      <span className="flex-1 text-ever-base-content">
                        {email}
                      </span>
                      <button
                        type="button"
                        onClick={() => removeEmail(email)}
                        className="ml-2"
                      >
                        <TrashIcon className="w-5 h-5 text-ever-error" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Notify Users Switch */}
            <div className="flex items-center mt-6 mb-6">
              <EverSwitch checked={notify} onChange={(val) => setNotify(val)} />
              <EverTg.Text className="ml-2 flex items-center">
                Notify users via email
              </EverTg.Text>
            </div>
            {notify && (
              <div>
                <EverInput
                  className="mb-6"
                  placeholder="Subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
                <EverInput.TextArea
                  className="mb-6"
                  placeholder="Additional message to include in the email..."
                  rows={4}
                  value={additionalMessage}
                  onChange={(e) => setAdditionalMessage(e.target.value)}
                />
              </div>
            )}
          </>
        )}
      </div>
    </EverModal>
  );
}
