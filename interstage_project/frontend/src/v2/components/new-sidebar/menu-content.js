import {
  EVE<PERSON><PERSON><PERSON>_PAGE_NAMES,
  RBAC_ROLES,
  DASH<PERSON>ARD_URLS,
  EVERSIDER_PAGE_NAME_LOCALIZED,
  CPQ_DASHBOARD_URLS,
} from "~/Enums";

export function getCpqMenuElements() {
  return [
    {
      label: EVERSIDER_PAGE_NAMES.DASHBOARD,
      path: CPQ_DASHBOARD_URLS.DASHBOARDS,
      icon: "dashboards",
      rbacPermission: RBAC_ROLES.VIEW_CPQ_DASHBOARD,
      type: "routingItem",
      name: "Dashboards",
    },
    {
      label: EVERSIDER_PAGE_NAMES.QUOTES,
      path: "/cpq/quotes",
      icon: "quotes",
      rbacPermission: RBAC_ROLES.VIEW_QUOTES,
      hasExtraPermission: false,
      type: "routingItem",
      name: "Quotes",
    },
    {
      label: EVERSIDER_PAGE_NAMES.SETTINGS,
      path: "/cpq/deal-rooms",
      icon: "dealrooms",
      rbacPermission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      type: "routingItem",
      name: "Dealrooms",
    },
    {
      label: "Data",
      icon: "databook",
      type: "submenu",
      name: "Data",
      items: [
        {
          label: EVERSIDER_PAGE_NAMES.DATASHEETS,
          path: "/cpq/datasheet",
          hasExtraPermission: false,
          rbacPermission: RBAC_ROLES.VIEW_DATABOOK,
          type: "routingItem",
          name: EVERSIDER_PAGE_NAMES.DATASHEETS,
        },
        {
          label: EVERSIDER_PAGE_NAMES.OBJECTS,
          path: "/cpq/objects",
          hasExtraPermission: false,
          rbacPermission: RBAC_ROLES.MANAGE_DATASETTINGS,
          type: "routingItem",
          name: EVERSIDER_PAGE_NAMES.OBJECTS,
        },
      ],
    },
    {
      label: EVERSIDER_PAGE_NAMES.CATALOG,
      path: "/cpq/product-catalog",
      icon: "catalog",
      rbacPermission: RBAC_ROLES.MANAGE_PRODUCTS_AND_PRICES,
      hasExtraPermission: false,
      type: "routingItem",
      name: "Catalog",
    },
    // {
    //   label: EVERSIDER_PAGE_NAMES.CATALOG,
    //   icon: "catalog",
    //   type: "submenu",
    //   name: "Catalog",
    //   items: [
    //     {
    //       label: EVERSIDER_PAGE_NAMES.PRODUCT,
    //       path: "/cpq/product",
    //       hasExtraPermission: false,
    //       rbacPermission: RBAC_ROLES.VIEW_DATABOOK,
    //       type: "routingItem",
    //       name: "Product",
    //     },
    //     {
    //       label: EVERSIDER_PAGE_NAMES.PRICE_BOOK,
    //       path: "/cpq/pricebook",
    //       hasExtraPermission: false,
    //       rbacPermission: RBAC_ROLES.MANAGE_DATASETTINGS,
    //       type: "routingItem",
    //       name: "PriceBook",
    //     },
    //   ],
    // },
    {
      label: "People",
      icon: "people",
      type: "submenu",
      name: "People",
      items: [
        {
          label: EVERSIDER_PAGE_NAMES.USERS,
          path: "/cpq/users",
          rbacPermission: RBAC_ROLES.MANAGE_CPQ_USERS_AND_GROUPS,
          hasExtraPermission: false,
          type: "routingItem",
          name: "Users",
        },
        {
          label: EVERSIDER_PAGE_NAMES.USER_GROUPS,
          path: "/cpq/groups",
          rbacPermission: RBAC_ROLES.MANAGE_CPQ_USERS_AND_GROUPS,
          hasExtraPermission: false,
          type: "routingItem",
          name: "Groups",
        },
      ],
    },
    {
      label: EVERSIDER_PAGE_NAMES.SETTINGS,
      path: "/cpq/settings",
      icon: "settings",
      rbacPermission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      type: "routingItem",
      name: "Settings",
    },
    {
      label: EVERSIDER_PAGE_NAMES.EVERAI,
      path: "/cpq/everAI",
      icon: "everai",
      rbacPermission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      type: "routingItem",
      name: "EverAI",
    },
  ];
}

export function getEverMenuElements(t) {
  return [
    {
      label: t(EVERSIDER_PAGE_NAME_LOCALIZED.DASHBOARDS),
      path: `${DASHBOARD_URLS.DASHBOARDS}?type=all`,
      icon: "dashboards",
      rbacPermission: RBAC_ROLES.VIEW_DASHBOARD,
      type: "routingItem",
      name: t(EVERSIDER_PAGE_NAME_LOCALIZED.DASHBOARDS),
    },

    {
      label: EVERSIDER_PAGE_NAMES.DATASHEET,
      icon: "databook",
      type: "submenu",
      name: "Data",

      items: [
        {
          label: EVERSIDER_PAGE_NAMES.DATASHEETS,
          path: "/datasheet",
          rbacPermission: RBAC_ROLES.VIEW_DATABOOK,
          type: "routingItem",
          name: EVERSIDER_PAGE_NAMES.DATASHEETS,
        },
        {
          label: EVERSIDER_PAGE_NAMES.OBJECTS,
          path: "/objects",
          rbacPermission: RBAC_ROLES.MANAGE_DATASETTINGS,
          type: "routingItem",
          name: EVERSIDER_PAGE_NAMES.OBJECTS,
        },
      ],
    },
    {
      label: EVERSIDER_PAGE_NAMES.DATABOOK,
      path: "/databook",
      icon: "databook",
      rbacPermission: RBAC_ROLES.VIEW_DATABOOK,
      type: "routingItem",
      name: EVERSIDER_PAGE_NAMES.DATABOOK,
    },
    {
      label: t(EVERSIDER_PAGE_NAME_LOCALIZED.STATEMENTS),
      path: "/statements",
      icon: "statements",
      rbacPermission: RBAC_ROLES.VIEW_STATEMENTS,
      type: "routingItem",
      name: t(EVERSIDER_PAGE_NAME_LOCALIZED.STATEMENTS),
    },
    {
      label: "Commissions",
      icon: "commissions",
      type: "submenu",
      name: t("COMMISSION"),

      items: [
        {
          label: EVERSIDER_PAGE_NAMES.COMMISSION_PLAN,
          path: "/plans",
          icon: "commissionplan",
          rbacPermission: RBAC_ROLES.VIEW_COMMISSIONPLAN,
          type: "routingItem",
          name: "Plans",
        },
        {
          label: EVERSIDER_PAGE_NAMES.FORECAST,
          path: "/forecasts",
          icon: "forecast",
          rbacPermission: RBAC_ROLES.VIEW_COMMISSIONPLAN,
          type: "routingItem",
          name: "Forecasts",
        },
        {
          label: EVERSIDER_PAGE_NAMES.COMMISSION_PAYOUTS,
          path: "/commissions",
          icon: "commissionPayout",
          rbacPermission: RBAC_ROLES.VIEW_PAYOUTS,
          type: "routingItem",
          name: t("PAYOUTS"),
        },
      ],
    },
    {
      label: EVERSIDER_PAGE_NAMES.APPROVALS,
      icon: "approvals",
      type: "submenu",
      name: "Approvals",

      items: [
        {
          label: EVERSIDER_PAGE_NAMES.PAYOUT_APPROVALS,
          path: "/approvals/payouts",
          type: "routingItem",
          name: t("PAYOUTS"),
          countBadgeKey: "payoutApprovals",
        },
        {
          label: EVERSIDER_PAGE_NAMES.COMMISSION_ADJUSTMENT_APPROVALS,
          path: "/approvals/commission-adjustments",
          type: "routingItem",
          name: t("COMMISSION_ADJUSTMENTS"),
          countBadgeKey: "commissionAdjApprovals",
        },
      ],
      rbacPermission: false,
    },
    {
      label: EVERSIDER_PAGE_NAMES.CRYSTAL,
      path: "/crystal",
      icon: "crystal",
      //rbacPermission: RBAC_ROLES.MANAGE_CRYSTAL,
      type: "routingItem",
      name: "Crystal",
    },
    {
      label: "People",
      icon: "people",
      type: "submenu",
      name: "People",

      items: [
        {
          label: EVERSIDER_PAGE_NAMES.USERS,
          path: "/users",
          rbacPermission: RBAC_ROLES.VIEW_USERS,
          type: "routingItem",
          name: "Users",
        },
        {
          label: EVERSIDER_PAGE_NAMES.USER_GROUPS,
          path: "/groups",
          rbacPermission: RBAC_ROLES.MANAGE_USERGROUPS,
          type: "routingItem",
          name: "Groups",
        },
        {
          label: EVERSIDER_PAGE_NAMES.TEAMS,
          path: "/teams",
          rbacPermission: RBAC_ROLES.VIEW_TEAMS,
          type: "routingItem",
          name: "Teams",
        },
      ],
    },
    {
      label: t("QUOTAS"),
      path: "/quotas",
      icon: "quotas",
      rbacPermission: RBAC_ROLES.VIEW_QUOTAS,
      type: "routingItem",
      name: t("QUOTAS"),
    },
    {
      label: t(EVERSIDER_PAGE_NAME_LOCALIZED.QUERIES),
      path: "/queries/allTickets",
      icon: "queries",
      rbacPermission: RBAC_ROLES.VIEW_QUERIES,
      type: "routingItem",
      name: t(EVERSIDER_PAGE_NAME_LOCALIZED.QUERIES),
    },
    {
      label: EVERSIDER_PAGE_NAMES.CONTRACTS,
      path: "/contracts",
      icon: "contracts",
      rbacPermission: RBAC_ROLES.MANAGE_CONTRACTS,
      type: "routingItem",
      name: "Contracts",
    },
    {
      label: EVERSIDER_PAGE_NAMES.DRAWS,
      path: "/draws",
      icon: "draws",
      rbacPermission: RBAC_ROLES.VIEW_DRAWS,
      type: "routingItem",
      name: "Draws",
    },
    {
      label: "KPI",
      icon: "kpi",
      type: "submenu",
      name: "KPI",

      items: [
        {
          label: EVERSIDER_PAGE_NAMES.METRICS,
          path: "/metrics",
          rbacPermission: RBAC_ROLES.MANAGE_USERS,
          type: "routingItem",
          name: "Metrics",
        },
        {
          label: EVERSIDER_PAGE_NAMES.EXPLORER,
          path: "/explorer",
          rbacPermission: RBAC_ROLES.MANAGE_USERS,
          type: "routingItem",
          name: "Explorer",
        },
        {
          label: EVERSIDER_PAGE_NAMES.TRANSFORMATION,
          path: "/realtime-transformation",
          rbacPermission: RBAC_ROLES.MANAGE_USERS,
          type: "routingItem",
          name: "Realtime Transformation",
        },
      ],
    },
    {
      label: EVERSIDER_PAGE_NAMES.EVERAI,
      path: "/everAI",
      icon: "everai",
      rbacPermission: RBAC_ROLES.MANAGE_AGENT_WORKBENCH,
      type: "routingItem",
      name: "EverAI",
    },
    {
      label: EVERSIDER_PAGE_NAMES.TERRITORY_PLANS,
      path: "/planning",
      icon: "territoryPlans",
      rbacPermission: RBAC_ROLES.VIEW_TERRITORY_PLANS,
      type: "routingItem",
      name: "Planning",
    },
    {
      label: EVERSIDER_PAGE_NAMES.SETTINGS,
      path: "/settings",
      icon: "settings",
      rbacPermission: [
        RBAC_ROLES.MANAGE_CONFIG,
        RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
        RBAC_ROLES.MANAGE_CONTRACTS,
        RBAC_ROLES.MANAGE_REPORTENRICH,
        RBAC_ROLES.MANAGE_ROLES,
        RBAC_ROLES.MANAGE_USERCUSTOMFIELD,
        RBAC_ROLES.MANAGE_DATASETTINGS,
        RBAC_ROLES.MANAGE_DRAWS,
      ],
      type: "routingItem",
      name: "Settings",
    },
  ];
}
