import {
  <PERSON><PERSON><PERSON>ottie,
  A<PERSON><PERSON><PERSON><PERSON>ottie,
  <PERSON><PERSON>ottie,
  Con<PERSON><PERSON>ottie,
  DatabookLottie,
  <PERSON>sLottie,
  PeopleLottie,
  QueriesLottie,
  QuotasLottie,
  SettingsLottie,
  StatementsLottie,
  <PERSON>ChartLottie,
  CrystalLottie,
  MagicWandLottie,
  TransformationLottie,
  TerritoryPlansLottie,
} from "@everstage/evericons/lotties";
import {
  PieChartIcon as PieChartIconOL,
  LayoutGridAltIcon as LayoutGridIconOL,
  PercentSquareIcon as PercentSquareIconOL,
  MessageChatSquareIcon as MessageChatSquareIconOL,
  Target04Icon as Target04IconOL,
  FileContractIcon as FileContractIconOL,
  UsersIcon as UsersIconOL,
  SettingsIcon as SettingsIconOL,
  WalletIcon as WalletIconOL,
  ApprovalsIcon as ApprovalsIconOL,
  Crystal2Icon as Crystal2IconOL,
  FileIcon as FileIconOL,
  SearchMdIcon as SearchIconOL,
  MagicWandIcon as MagicWandIconOL,
  TerritoryPlansIcon as TerritoryPlansIconOL,
  QuotesIcon as QuotesIconOL,
  DealRoomIcon as DealRoomIconOL,
  QuotesSelectedIcon as QuotesSelectedIconOL,
  CatalogIcon as CatalogIconOL,
  CatalogSelectedIcon,
} from "@everstage/evericons/outlined";
import {
  WalletIcon,
  Target04Icon,
  LayoutGridIcon,
  PieChartIcon,
  PercentSquareIcon,
  MessageChatSquareIcon,
  FileContractIcon,
  UsersIcon,
  SettingsIcon,
  ApprovalsIcon,
  FileIcon,
  Crystal2Icon,
  LineChartIcon,
  MagicWandIcon,
  TransformationIcon,
  TerritoryPlansIcon,
  DealRoomIcon,
} from "@everstage/evericons/solid";
export const cpqMenuIcons = {
  quotes: {
    main: <QuotesIconOL className="size-[18px]" />,
    active: QuotasLottie,
    selected: <QuotesSelectedIconOL className="size-[18px]" />,
  },
  catalog: {
    main: <CatalogIconOL className="size-[18px]" />,
    active: StatementsLottie,
    selected: <CatalogSelectedIcon className="size-[18px]" />,
  },
  dealrooms: {
    main: <DealRoomIconOL className="size-[18px]" />,
    // active: DatabookLottie,
    selected: <DealRoomIcon className="size-[18px] text-ever-accent-content" />,
  },
};

export const sidebarMenuIcons = {
  dashboards: {
    main: <PieChartIconOL className="size-[18px]" />,
    active: DashboardLottie,
    selected: <PieChartIcon className="size-[18px] text-ever-accent-content" />,
  },
  everai: {
    main: <MagicWandIconOL className="size-[18px]" />,
    active: MagicWandLottie,
    selected: (
      <MagicWandIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  databook: {
    main: <LayoutGridIconOL className="size-[18px]" />,
    active: DatabookLottie,
    selected: (
      <LayoutGridIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  commissions: {
    main: <PercentSquareIconOL className="size-[18px]" />,
    active: CommissionLottie,
    selected: (
      <PercentSquareIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  queries: {
    main: <MessageChatSquareIconOL className="size-[18px]" />,
    active: QueriesLottie,
    selected: (
      <MessageChatSquareIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  territoryPlans: {
    main: <TerritoryPlansIconOL className="size-[18px]" />,
    active: TerritoryPlansLottie,
    selected: (
      <TerritoryPlansIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  crystal: {
    main: <Crystal2IconOL className="size-[18px]" />,
    active: CrystalLottie,
    selected: <Crystal2Icon className="size-[18px]" />,
  },
  quotas: {
    main: <Target04IconOL className="size-[18px]" />,
    active: QuotasLottie,
    selected: <Target04Icon className="size-[18px] text-ever-accent-content" />,
  },
  contracts: {
    main: <FileContractIconOL className="size-[18px]" />,
    active: ContractsLottie,
    selected: (
      <FileContractIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  people: {
    main: <UsersIconOL className="size-[18px]" />,
    active: PeopleLottie,
    selected: <UsersIcon className="size-[18px] text-ever-accent-content" />,
  },
  settings: {
    main: <SettingsIconOL className="size-[18px]" />,
    active: SettingsLottie,
    selected: <SettingsIcon className="size-[18px] text-ever-accent-content" />,
  },
  draws: {
    main: <WalletIconOL className="size-[18px]" />,
    active: DrawsLottie,
    selected: <WalletIcon className="size-[18px] text-ever-accent-content" />,
  },
  approvals: {
    main: <ApprovalsIconOL className="size-[18px]" />,
    active: ApprovalsLottie,
    selected: (
      <ApprovalsIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  statements: {
    main: <FileIconOL className="size-[18px]" />,
    active: StatementsLottie,
    selected: <FileIcon className="size-[18px] text-ever-accent-content" />,
  },
  kpi: {
    main: <LineChartIcon className="size-[18px] scale-150" />,
    active: LineChartLottie,
    selected: (
      <LineChartIcon className="size-[18px] scale-150 text-ever-accent-content" />
    ),
  },
  transformation: {
    main: <TransformationIcon className="size-[18px]" />,
    active: TransformationLottie,
    selected: (
      <TransformationIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  search: {
    main: <SearchIconOL className="size-[18px]" />,
    active: () => <SearchIconOL className="size-[18px]" />,
    selected: <SearchIconOL className="size-[18px] text-ever-accent-content" />,
  },
  ...cpqMenuIcons,
};

export const PRODUCT_ACCOUNT_MAP = {
  ICM: {
    name: "Everstage Incentives",
    key: "ICM",
    tag: "COMMISSIONS",
  },
  CPQ: {
    name: "Everstage CPQ",
    key: "CPQ",
    tag: "CPQ",
  },
};

export const getMenuList = (menuList, hasPermissions, extraConditions) => {
  const filteredMenu = [];
  // Helper function to check if an item is accessible based on client features and permissions
  const isItemAccessible = (item) => {
    let baseCondition =
      !item.rbacPermission || hasPermissions(item.rbacPermission);
    // Apply additional condition if it exists for this label
    if (extraConditions[item.label]) {
      baseCondition = baseCondition && extraConditions[item.label]();
    }
    return baseCondition;
  };

  for (const item of menuList) {
    if (isItemAccessible(item)) {
      if (item.type === "submenu" && Array.isArray(item.items)) {
        // Filter accessible submenu items
        // eslint-disable-next-line unicorn/no-array-callback-reference
        const accessibleSubItems = item.items.filter(isItemAccessible);
        // Only add the submenu if it has accessible items
        if (accessibleSubItems.length > 0) {
          filteredMenu.push({
            ...item,
            items: accessibleSubItems,
          });
        }
      } else {
        // Add non-submenu item directly
        filteredMenu.push(item);
      }
    }
  }

  return filteredMenu;
};

export function isRoute(currentPath, buttonPath) {
  if (
    buttonPath.includes("/dashboards") &&
    (currentPath.includes(buttonPath) || buttonPath.includes(currentPath))
  ) {
    return true;
  }
  if (currentPath.startsWith(buttonPath)) return true;
  return false;
}
