import { ArchiveIcon } from "@everstage/evericons/outlined";
import React from "react";

import { EverTooltip } from "~/v2/components";

export default function DatasheetOptionWithIcon({
  name,
  id,
  forceSkippedDatasheetIds = [],
}) {
  const forceSkippedDatasheetIdsSet = React.useMemo(
    () => new Set(forceSkippedDatasheetIds.map(String)),
    [forceSkippedDatasheetIds]
  );
  const isForceSkipped = forceSkippedDatasheetIdsSet.has(String(id));
  return (
    <div className="flex items-center gap-2 min-w-0">
      <EverTooltip title={name} mode={undefined}>
        <span className="truncate block">{name}</span>
      </EverTooltip>
      {isForceSkipped && (
        <EverTooltip title="This datasheet is archived" mode={undefined}>
          <ArchiveIcon className="w-4 h-4 text-ever-base-content-mid shrink-0" />
        </EverTooltip>
      )}
    </div>
  );
}
