import { isEmpty, cloneDeep } from "lodash";
import { observer } from "mobx-react";
import React, { Fragment, useCallback, useEffect, useState } from "react";
import { Helmet } from "react-helmet";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { useSpring, animated } from "react-spring";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import {
  CPQ_DASHBOARD_URLS,
  DASHBOARD_URLS,
  EVERSIDER_PAGE_NAME_LOCALIZED,
} from "~/Enums";
import { breadcrumbAtom, breadcrumbPortalAtom } from "~/GlobalStores/atoms";
import { segmentPage } from "~/Utils/SegmentUtils";
import { useModules } from "~/v2/hooks";

import { EverTg } from "../EverTypography";

function isValidUUID(uuid) {
  const regex =
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return regex.test(uuid);
}

function isBase64(encodedUrl) {
  const base64Regex = /(?<=\b)[A-Za-z0-9+/=]{40,}(?=\b)/;
  return base64Regex.test(encodedUrl);
}

function toTitleCase(str) {
  return str.replace(/\b\w/g, function (char) {
    return char.toUpperCase();
  });
}

const BREADCRUMB_LINK =
  "text-ever-base-content whitespace-nowrap inline-block align-middle hover:underline active:underline disabled:no-underline";

const cpqGeneratePageName = (item, routes) => {
  switch (item) {
    case "dashboard": {
      return {
        name: "Dashboards",
        path: `${CPQ_DASHBOARD_URLS.DASHBOARDS}?type=all`,
      };
    }
    case "quotes": {
      return {
        name: "Quotes",
        path: "/cpq/quotes",
      };
    }
    case "quotes-details": {
      return {
        name: "Quotes",
        path: "/cpq/quotes",
      };
    }
    case "databook": {
      return {
        name: routes?.length > 1 ? "Datasheet" : "Databook",
        path: "/cpq/databook",
      };
    }
    case "objects": {
      return {
        name: "Objects",
        path: "/cpq/objects",
      };
    }
    case "product": {
      return {
        name: "Product",
        path: "/cpq/product",
      };
    }
    case "pricebook": {
      return {
        name: "PriceBook",
        path: "/cpq/pricebook",
      };
    }
    case "users": {
      return {
        name: "Users",
        path: "/cpq/users",
      };
    }
    case "groups": {
      return {
        name: "Groups",
        path: "/cpq/groups",
      };
    }
    case "everAI": {
      return {
        name: "Agents",
        path: "/cpq/everAI",
      };
    }
    case "settings": {
      return {
        name: "Settings",
        path: "/cpq/settings",
      };
    }
    case "manage-data": {
      return {
        name: "Manage data",
        path: "/cpq/settings/manage-data",
      };
    }
    case "product-catalog": {
      return { name: "Products", path: "/cpq/product-catalog" };
    }
    case "connectors": {
      return { name: "Connectors", path: "/cpq/settings/connectors" };
    }
    case "docusign-integration": {
      return { name: "eSignature", path: "/cpq/settings/docusign-integration" };
    }
    case "quote-forms": {
      return { name: "Quote Forms", path: "/cpq/settings/quote-forms" };
    }
    case "terms": {
      return {
        name: "Terms Library",
        path: "/cpq/settings/terms",
      };
    }
    case "approval-rules": {
      return {
        name: "Approval Rules",
        path: "/cpq/settings/approval-rules",
      };
    }
    case "quote-rules": {
      return { name: "Quote Rules", path: "/cpq/settings/quote-rules" };
    }
    case "deal-rooms": {
      return { name: "Deal Rooms", path: "/cpq/deal-rooms" };
    }
    default: {
      return { name: toTitleCase(item), path: `/${item}` };
    }
  }
};

const generatePageName = ({
  item,
  translate: t,
  routes = [],
  hasHelmet = false,
  isCPQ,
}) => {
  if (isCPQ) {
    return cpqGeneratePageName(item, routes, hasHelmet);
  } else {
    switch (item) {
      case "dashboard": {
        return {
          name: t(EVERSIDER_PAGE_NAME_LOCALIZED.DASHBOARDS),
          path: `${DASHBOARD_URLS.DASHBOARDS}?type=all`,
        };
      }
      case "dashboards": {
        return {
          name: t(EVERSIDER_PAGE_NAME_LOCALIZED.DASHBOARDS),
          path: `${DASHBOARD_URLS.DASHBOARDS}`,
        };
      }
      case "databook": {
        return {
          name: routes?.length > 1 ? "Datasheet" : "Databook",
          path: "/databook",
        };
      }
      case "objects": {
        return {
          name: "Objects",
          path: "/objects",
        };
      }
      case "commissions": {
        return {
          name: hasHelmet ? t("PAYOUTS", "") : t("COMMISSION", ""),
          path: "/commissions",
        };
      }
      case "plans": {
        return {
          name: t("COMMISSION_PLANS", ""),
          path: "/plans",
        };
      }
      case "forecasts": {
        return {
          name: t("COMMISSION_FORECASTING", ""),
          path: "/forecasts",
        };
      }
      case "commission-feed": {
        return {
          name: t("COMMISSION_FEED", ""),
          path: "/commission-feed",
        };
      }
      case "crystal": {
        return { name: "Crystal", path: "/crystal" };
      }
      case "teams": {
        return { name: "Teams", path: "/teams" };
      }
      case "quotas": {
        return { name: t("QUOTAS", ""), path: "/quotas" };
      }
      case "draws": {
        return { name: "Draws", path: "/draws" };
      }
      case "users": {
        return { name: "Users", path: "/users" };
      }
      case "queries": {
        return {
          name: t(EVERSIDER_PAGE_NAME_LOCALIZED.QUERIES),
          path: "/queries/allTickets",
        };
      }
      case "notification-preference": {
        return {
          name: "Profile Notifications",
          path: "/notification-preference",
        };
      }
      case "settings": {
        return { name: "Settings", path: "/settings" };
      }
      case "basic": {
        return {
          name: hasHelmet ? "Basic" : "Currencies and Rates",
          path: "/settings/basic",
        };
      }
      case "data-sync": {
        return { name: "Data Sync", path: "/settings/data-sync" };
      }
      case "connectors": {
        return { name: "Connectors", path: "/settings/connectors" };
      }
      case "hris-integration": {
        return {
          name: "HRIS Integration",
          path: "/settings/hris-integration",
        };
      }
      case "review-updates": {
        return {
          name: "Review updates",
          path: "/settings/hris-integration/review-updates",
        };
      }
      case "processed-ignored-records": {
        return {
          name: "Processed & Ignored Records",
          path: "/settings/hris-integration/processed-ignored-records",
        };
      }
      case "upload-data": {
        return {
          name: hasHelmet ? "Data upload" : "Upload Data",
          path: "/settings/upload-data",
        };
      }
      case "audit-logs": {
        return { name: "Audit Log", path: "/settings/audit-logs" };
      }
      case "adjustments": {
        const isDatasheetAdjustments = routes?.findIndex(
          (path) => path.name === "datasheet"
        );
        if (isDatasheetAdjustments >= 0) {
          return {
            name: "",
            path: "",
          };
        } else {
          return {
            name: t("ADJUSTMENTS", ""),
            path: "/settings/adjustments",
          };
        }
      }
      case "user-roles": {
        return { name: "Roles", path: "/settings/user-roles" };
      }
      case "custom-fields": {
        return { name: "Custom Fields", path: "/settings/custom-fields" };
      }
      case "report-enrichment": {
        return {
          name: "Report Enrichment",
          path: "/settings/report-enrichment",
        };
      }
      case "statements": {
        return {
          name: t(EVERSIDER_PAGE_NAME_LOCALIZED.STATEMENTS),
          path: "/statements",
        };
      }
      case "commissions-and-data-sync": {
        return {
          name: t("COMM_AND_DATA_SYNC", ""),
          path: "/settings/commissions-and-data-sync",
        };
      }
      case "custom-calendar": {
        return {
          name: "Custom Calendar",
          path: "/settings/custom-calendar",
        };
      }
      case "workflows": {
        return {
          name: "Approval Workflows",
          path: "/settings/approval-workflows",
        };
      }
      case "statement-settings": {
        return {
          name: "Statement Settings",
          path: "/settings/statement-settings",
        };
      }
      case "manage-data": {
        return {
          name: "Manage data",
          path: "/settings/manage-data",
        };
      }
      case "databook-builder": {
        return {
          name: "Databook Builder",
          path: "/settings/connectors/databook-builder",
        };
      }
      case "activity-logs": {
        return {
          name: "Activity Logs",
          path: "/settings/activity-logs",
        };
      }
      case "profile-settings": {
        return {
          name: "Profile settings",
          path: "/profile-settings",
        };
      }
      case "custom-triggers": {
        return {
          name: "Custom Triggers",
          path: "/settings/workflow-builders/custom-triggers",
        };
      }
      case "workflow-builders": {
        return {
          name: "Custom Workflows",
          path: "/settings/workflow-builders",
        };
      }
      case "workflow-history": {
        return {
          name: "Workflow History",
          path: "/settings/workflow-builders/workflow-history",
        };
      }
      case "custom-terminology": {
        return {
          name: "Custom Terminology",
          path: "/settings/custom-terminology",
        };
      }
      case "v1": {
        return {
          name: "V1",
          path: "/settings/manage-data/v1",
        };
      }
      case "commission-adjustments": {
        return {
          name: "",
          path: `/approvals/commission-adjustments`,
        };
      }
      case "payouts": {
        return {
          name: "",
          path: `/approvals/payouts`,
        };
      }
      case "update": {
        return {
          name: "",
          path: "",
        };
      }
      case "split": {
        return {
          name: "",
          path: "",
        };
      }
      case "ignore": {
        return {
          name: "",
          path: "",
        };
      }
      case "edit": {
        return {
          name: "",
          path: "",
        };
      }

      default: {
        return { name: toTitleCase(item), path: `/${item}` };
      }
    }
  }
};

const BreadCrumbArrow = ({ cursor, isTerritoryPlans }) => {
  return (
    <EverTg.Heading2
      className={twMerge(
        "mx-2 text-ever-base-content-low",
        isTerritoryPlans && "text-xs"
      )}
    >
      {cursor ? cursor : "/"}
    </EverTg.Heading2>
  );
};

const BreadcrumbItem = ({ children, title }) => {
  const props = useSpring({
    from: { transform: "translateX(-40%)", opacity: 0 },
    to: { transform: "translateX(0)", opacity: 1 },
  });

  return (
    <animated.div className="flex items-center" style={props} title={title}>
      {children}
    </animated.div>
  );
};

const BREADCRUMB_ENABLE_LINK = new Set(["dashboard"]);

const Breadcrumbs = ({
  paths,
  t,
  routeAsObjects,
  doesCustomBreadcrumbExist,
  applyCPQStyle,
}) => {
  const { isCPQ } = useModules();
  const pathsLength = paths.length;
  const location = useLocation();
  const { pathname } = location;

  if (pathname === "/no-permission") {
    return null;
  }

  const isTerritoryPlans = pathname.includes("/planning/");

  const visiblePaths = paths.filter((item) => {
    const checkIfUUID = isValidUUID(item.name);
    const checkIfBase64 = isBase64(item.name);
    const pageName = generatePageName({
      item: item.name,
      translate: t,
      isCPQ,
    }).name;

    // if the page name is undefined and the item is not hidden and the item is not a UUID or Base64, then return true
    return (
      !item.hidden && pageName !== "undefined" && !checkIfUUID && !checkIfBase64
    );
  });

  const element = visiblePaths.map((item, index) => {
    const { name, url, disabled, cursor, truncate } = item || {};
    const urlSameAsOrigin = routeAsObjects?.find((op) => op.url === url)
      ? generatePageName({ item: url, translate: t, isCPQ }).path
      : url;

    const BreadcrumbHeader = !isTerritoryPlans
      ? visiblePaths.length === 1 && !doesCustomBreadcrumbExist
        ? EverTg.Heading2
        : EverTg.Heading3
      : EverTg.Caption.Thick;

    return (
      <BreadcrumbItem
        key={`breadcrumb_${index}`}
        title={truncate ? name : null}
      >
        {index > 0 && (
          <BreadCrumbArrow
            cursor={cursor}
            isTerritoryPlans={isTerritoryPlans}
          />
        )}
        <Link
          to={urlSameAsOrigin}
          disabled={
            (pathsLength === index + 1 && !BREADCRUMB_ENABLE_LINK.has(name)) ||
            disabled
          }
        >
          <BreadcrumbHeader
            className={twMerge(
              truncate && "truncate max-w-60",
              BREADCRUMB_LINK,
              ((visiblePaths.length > 1 && index === visiblePaths.length - 1) ||
                (visiblePaths.length === 1 && index === 0)) &&
                !doesCustomBreadcrumbExist
                ? applyCPQStyle
                  ? "!text-ever-base-content-mid"
                  : "!text-ever-base-content"
                : applyCPQStyle
                ? "text-ever-base-content-low"
                : "text-ever-base-content-mid",
              applyCPQStyle && "text-sm"
            )}
          >
            {generatePageName({ item: name, translate: t, isCPQ }).name}
          </BreadcrumbHeader>
        </Link>
      </BreadcrumbItem>
    );
  });
  segmentPage();
  return element;
};

const modifyRouteLocation = (routeAsObjects, modifications) => {
  let routeArr = cloneDeep(routeAsObjects);
  const mods = Array.isArray(modifications) ? modifications : [modifications];

  mods.forEach(
    ({ index, name, url, title, hidden, disabled, cursor, truncate }) => {
      // If index is not provided, target the last item in the array
      if (index === undefined) {
        index = routeArr.length - 1;
      } else if (index >= routeArr.length) {
        // Adjust index if it's out of bounds to point to the last item
        index = routeArr.length - 1;
      }

      // Remove the item at the specified index if only index is provided and no other modifications
      if (
        index !== undefined &&
        name === undefined &&
        title === undefined &&
        url === undefined &&
        hidden === undefined &&
        disabled === undefined &&
        cursor === undefined &&
        truncate === undefined
      ) {
        routeArr.splice(index, 1);
      } else if (index >= 0 && index < routeArr.length) {
        const currentItem = routeArr[index];

        if (name !== undefined) currentItem.name = name;
        if (title !== undefined) currentItem.title = title;
        if (url !== undefined) currentItem.url = url;
        if (hidden !== undefined) currentItem.hidden = hidden; // Handle the hidden property
        if (disabled !== undefined) currentItem.disabled = disabled;
        if (cursor !== undefined) currentItem.cursor = cursor;
        if (truncate !== undefined) currentItem.truncate = truncate;
      }
    }
  );

  return routeArr;
};

const isInnerCatalogRoute = (pathname) => {
  const segments = pathname.split("/").filter(Boolean);

  // Define patterns for inner routes
  const innerRoutePatterns = [
    // Product catalog inner route pattern
    {
      prefix: ["cpq", "product-catalog"],
      minSegments: 3,
    },
    // Approval rules detail route pattern
    {
      prefix: ["cpq", "settings", "approval-rules"],
      minSegments: 4,
    },
    // Terms library detail route pattern
    {
      prefix: ["cpq", "settings", "terms"],
      minSegments: 4,
    },
    // Deal room route pattern
    {
      prefix: ["cpq", "deal-rooms"],
      minSegments: 3,
    },
    {
      prefix: ["cpq", "quotes-details"],
      minSegments: 3,
    },
  ];

  // Check if any pattern matches
  return innerRoutePatterns.some((pattern) => {
    // Check if we have enough segments
    if (segments.length < pattern.minSegments) return false;

    // Check if the prefix matches
    for (let i = 0; i < pattern.prefix.length; i++) {
      if (segments[i] !== pattern.prefix[i]) return false;
    }

    return true;
  });
};

const EverHeader = observer(({ animationStarted }) => {
  const location = useLocation();
  const { t } = useTranslation();
  const { isCPQ } = useModules();

  const { state, pathname, key } = location;
  const { backActionRoute } = state || {};
  const backActionRouteTitle = backActionRoute?.replace("/", "");
  const [doesCustomBreadcrumbExist, setDoesCustomBreadcrumbExist] =
    useState(false);

  const breadcrumbName = useRecoilValue(breadcrumbAtom);
  const setBreadcrumb = useSetRecoilState(breadcrumbAtom);

  const setBreadcrumbPortalTarget = useSetRecoilState(breadcrumbPortalAtom);

  let routeLocation = pathname.split("/").filter(Boolean);
  routeLocation = isCPQ ? routeLocation.slice(1) : routeLocation;
  const routeAsObjects = routeLocation.map((route) => ({
    name: route,
    title: route,
    url: route,
  }));

  const isTerritoryPlans = pathname.includes("/planning");

  const modifiedRouteObjects = !isEmpty(breadcrumbName)
    ? modifyRouteLocation(routeAsObjects, breadcrumbName)
    : routeAsObjects;

  if (backActionRoute) {
    modifiedRouteObjects.unshift({
      name: backActionRouteTitle,
      title: backActionRouteTitle,
      url: backActionRoute.replace("/", ""),
    });
  }

  // Checking query params to get the name of the product
  // For now works only for Product catalog routes in CPQ

  const applyCPQStyle = isCPQ && isInnerCatalogRoute(pathname);

  const reverseRoutes = [...modifiedRouteObjects].reverse();

  const breadcrumbPortal = useCallback((node) => {
    if (node !== null) {
      setBreadcrumbPortalTarget(node);
      const resizeObserver = new ResizeObserver((entries) => {
        setDoesCustomBreadcrumbExist(
          entries[0].target.querySelectorAll("span#breadcrumb-divider-icon")
            .length > 0 &&
            entries[0].target
              .querySelector("span#breadcrumb-divider-icon")
              ?.textContent?.trim().length > 0
        );
      });
      resizeObserver.observe(node);
    }
  }, []);

  useEffect(() => {
    if (key) {
      setBreadcrumb({});
    }
  }, [key]);

  const createHelmetTitle = () => {
    const titleParts = reverseRoutes.map((path) => {
      const isTitleChanged = path.name !== path.title;
      const titleFromRoute = routeAsObjects?.find(
        (op) => op.title === path.title
      )
        ? generatePageName({
            item: path.title,
            translate: t,
            routes: routeAsObjects,
            hasHelmet: true,
            isCPQ,
          }).name
        : path.title;

      return isTitleChanged
        ? titleFromRoute
        : generatePageName({
            item: path.name,
            translate: t,
            routes: routeAsObjects,
            hasHelmet: true,
            isCPQ,
          }).name;
    });

    const nonEmptyTitles = titleParts.filter((title) =>
      isValidUUID(title) || isBase64(title)
        ? null
        : title && title.trim().length > 0
    );

    const pageTitle = nonEmptyTitles.join(" - ") + " - Everstage";

    return pageTitle;
  };

  return (
    <>
      <Helmet>
        <title>{createHelmetTitle()}</title>
      </Helmet>
      <div
        className={twMerge(
          "flex items-center relative",
          applyCPQStyle && "bg-ever-base-50",
          isTerritoryPlans && "h-12"
        )}
      >
        <div
          className={twMerge(
            "px-6 py-4 grow flex items-center [&>div+div>a[disabled='']>span]:text-ever-base-content-low",
            applyCPQStyle && "pl-8 pb-0"
          )}
        >
          <Breadcrumbs
            paths={modifiedRouteObjects}
            t={t}
            routeAsObjects={routeAsObjects}
            doesCustomBreadcrumbExist={doesCustomBreadcrumbExist}
            applyCPQStyle={applyCPQStyle}
          />
          {!animationStarted ? (
            <div
              ref={breadcrumbPortal}
              className="w-full flex items-center"
            ></div>
          ) : null}
        </div>
        <div id="invisible-anchor-for-appcues" className="float-right" />
      </div>
    </>
  );
});

export { EverHeader };
