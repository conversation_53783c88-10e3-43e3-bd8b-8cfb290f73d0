import React, { useEffect, useCallback } from "react";
import { useLocation, useMatch } from "react-router-dom";
import { useSetRecoilState, useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { everBodyDomAtom } from "~/GlobalStores/atoms";

const isRoutePresent = (
  routeName,
  { isCommissionCanvas, isWorkflowDetail, isAgentCanvas }
) => {
  const nonOverflowRoutes = [
    "queries",
    "groups",
    isWorkflowDetail && "settings/workflow-builders",
    isCommissionCanvas && "plans",
    "forecasts",
    "quotas",
    "draws",
    "settings/hris-integration/review-updates",
    "cpq/quotes/",
    "cpq/product-catalog",
    "cpq/settings/quote-forms",
    "cpq/settings/quote-rules",
    "cpq/settings",
    "cpq/settings/deal-room-templates/",
    "cpq/deal-rooms",
    isAgentCanvas && "agent-canvas",
  ].filter(Boolean);
  return nonOverflowRoutes.some((route) => routeName.includes(route));
};

export function EverBody({
  children,
  pathName,
  isCommissionCanvas = false,
  isNewSidebar,
}) {
  const location = useLocation();
  const isWorkflowDetail =
    useMatch("/settings/workflow-builders/:workflowId") &&
    !location.pathname.includes("workflow-history") &&
    !location.pathname.includes("custom-triggers");

  const isAgentCanvas = location.pathname.includes("agent-canvas");

  const isTerritoryPlans = pathName.includes("/planning");

  const withoutPadding = [
    "/groups",
    "/profile-settings",
    "/settings/connectors",
    isWorkflowDetail && "/settings/workflow-builders",
    isCommissionCanvas && "/plans",
    "/forecasts",
    "/quotas",
    "/draws",
    "/datasheet",
    "/settings/hris-integration/review-updates",
    "/settings/hris-integration/processed-ignored-records",
    "/everAI",
    "/cpq/quotes",
    "/cpq/product-catalog",
    "/cpq/settings/terms",
    "/cpq/settings/pdf-settings",
    "/cpq/settings/basic-settings",
    "/cpq/settings/quote-forms",
    "/cpq/settings/quote-rules",
    "/cpq/settings/docusign-integration",
    "/cpq/settings/approval-rules",
    "/cpq/deal-rooms",
    "/cpq/quotes-details",
    isTerritoryPlans && "/planning",
    "/cpq/settings",
  ]
    .filter(Boolean)
    .some((route) => pathName.includes(route));

  const everbodyDOM = useCallback(
    (node) => {
      if (node !== null && !location.pathname.includes("queries")) {
        /* Preventing to setDOM again when using custom scroll body eg:- queries */
        setEverbodyDOM(node);
      }
    },
    [location.pathname]
  );

  // const headerHeight = useRecoilValue(everHeaderHeightAtom);
  const setEverbodyDOM = useSetRecoilState(everBodyDomAtom);
  const everbodyDOMItem = useRecoilValue(everBodyDomAtom);

  useEffect(() => {
    if (everbodyDOMItem) {
      everbodyDOMItem.scrollTo(0, 0); //Resetting scroll position on different route visit
    }
  }, [location.pathname]);

  return (
    <div
      className={twMerge(
        `grid w-full transition duration-300 ease-in-out h-full overflow-auto`,
        // scrollingUp ? "min-h-auto h-full" : "min-h-full h-auto",
        //scrollingUp ? "translate-y-0" : `translate-y-${headerHeight}`,
        isRoutePresent(location.pathname, {
          isCommissionCanvas,
          isWorkflowDetail,
          isAgentCanvas,
        }) &&
          !isNewSidebar &&
          "overflow-hidden",
        withoutPadding ? "px-0" : "px-6",
        withoutPadding ||
          isRoutePresent(location.pathname, {
            isCommissionCanvas,
            isWorkflowDetail,
            isAgentCanvas,
          })
          ? "pt-0"
          : "pt-4"
      )}
      ref={everbodyDOM}
    >
      <div
        className={twMerge(
          "w-full h-full flex flex-col",
          isRoutePresent(location.pathname, {
            isCommissionCanvas,
            isWorkflowDetail,
            isAgentCanvas,
          })
            ? "overflow-hidden"
            : ""
        )}
      >
        {children}
      </div>
    </div>
  );
}
