/**
 * @typedef {import("../types/ever-button").EverButtonProps} EverButtonProps
 */
/**
 * @typedef {import("../types/ever-button").EverButtonIconProps} EverButtonIconProps
 */

import { Button, Tooltip } from "antd";
import React, { forwardRef, useState } from "react";
import { twMerge } from "tailwind-merge";

import { buttonStyles } from "./buttonStyles";
import { IconButton } from "./IconButton";

/**
 * EverButton is a styled wrapper over Ant Design's Button component.
 *
 * @param {EverButtonProps} props - Props for the button component.
 * @param {React.Ref<any>} ref
 * @returns {JSX.Element} The rendered button component.
 */
const EverButton = forwardRef(function EverButton(
  /** @type {EverButtonProps} */ props,
  ref
) {
  const {
    type = "filled",
    color = "primary",
    size = "medium",
    prependIcon = null,
    appendIcon = null,
    appendIconSize = null,
    className,
    children,
    tooltipTitle = "",
    hideLoader = false,
    ...rest
  } = props;
  const [isLoading, setLoading] = useState(false);
  const { icon, ...remainingProps } = rest;

  if (remainingProps.onClick) {
    const onClick = remainingProps.onClick;
    remainingProps.onClick = async (e) => {
      try {
        setLoading(true);
        const onClickVal = onClick(e);
        // Use Promise.resolve to handle both sync and async
        await Promise.resolve(onClickVal)
          .then((res) => {
            if (typeof res !== "undefined") console.log(res);
          })
          .catch((error) => {
            if (typeof error !== "undefined") console.log(error);
          });
        setLoading(false);
      } catch {
        setLoading(false);
      }
    };
  }

  // Hide the loader if the hideLoader prop is true
  // Otherwise, show the loader if the loader based on the prop or isLoading state
  if (hideLoader) {
    remainingProps.loading = false;
  } else {
    remainingProps.loading = remainingProps.loading || isLoading;
  }

  const iconSizes = {
    small: "h-4 w-4",
    medium: "h-5 w-5",
    large: "h-6 w-6",
  };
  const prependIconComponent = prependIcon || icon;

  const everButton = (
    <Button
      ref={ref}
      type={type === "filled" ? "primary" : type}
      className={twMerge(
        buttonStyles({
          type: type,
          intent: color,
          size: size,
        }),
        className
      )}
      {...remainingProps}
    >
      <div className="flex gap-2 justify-center items-center">
        {prependIconComponent && React.isValidElement(prependIconComponent) && (
          <div className={twMerge("flex text-inherit", iconSizes[size])}>
            {React.cloneElement(prependIconComponent, {
              ...(prependIconComponent.props ?? {}),
              className: twMerge(
                prependIconComponent.props?.className ?? "",
                "h-full w-full"
              ),
            })}
          </div>
        )}
        {children}
        {appendIcon && React.isValidElement(appendIcon) && (
          <div
            className={twMerge(
              "flex text-inherit",
              iconSizes[appendIconSize ?? size]
            )}
          >
            {React.cloneElement(appendIcon, {
              ...(appendIcon.props ?? {}),
              className: twMerge(
                appendIcon.props?.className ?? "",
                "h-full w-full"
              ),
            })}
          </div>
        )}
      </div>
    </Button>
  );
  // In antd version 4, the tooltip does not appear when the button is disabled,
  // therefore this is a workaround. we need to provide a proper solution for this issue.
  if (tooltipTitle !== "") {
    return (
      <Tooltip title={tooltipTitle}>
        <span className="inline-block">{everButton}</span>
      </Tooltip>
    );
  }
  return everButton;
});

// Add the static Icon property
const EverButtonWithStatic = Object.assign(EverButton, {
  Icon: IconButton,
});

// Export only once
export { EverButtonWithStatic as EverButton };
