import { InfoCircleIcon } from "@everstage/evericons/duotone";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { cloneDeep, get, isNil } from "lodash";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { TRIGGER_CATEGORIES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverForm,
  EverModal,
  EverSelect,
  EverTg,
  EverTimePicker,
} from "~/v2/components";
import DatasheetOptionWithIcon from "~/v2/components/DatasheetOptionWithIcon";
import { useForceSkipModal } from "~/v2/features/datasheet/helperComponents/useForceSkipModal";
import { getForceSkippedDatasheetIds } from "~/v2/features/datasheet/restApi";

import { useWorkflowBuilder } from "../../hooks";

/**
 * DatasheetData is a React functional component that manages the datasheet data in a workflow.
 * It uses the useDatabookStore and useWorkflowBuilder hooks to get the active databooks, datasheets, and active node.
 * It renders an EverForm that includes form items for the databook id, datasheet id, changes, and change type.
 * The form includes onValuesChange logic for resetting the datasheet id when the databook id changes and resetting the change type when changes changes.
 *
 * @param {Object} props The properties passed to the component.
 * @param {Object} props.form The form instance to use.
 * @param {Function} props.onChange The function to call when the form values change.
 * @param {boolean} props.disableFields Whether to disable the form fields.
 * @param {string} props.name The name of the trigger.
 * @param {Object} props.setNodeError The state setter function for node error.
 * @returns {JSX.Element} An EverForm that contains the form items for the databook id, datasheet id, changes, and change type.
 */
const DatasheetData = ({
  form,
  onChange,
  disableFields,
  name,
  setNodeError,
}) => {
  const { activeDatabooks, getDatasheets } = useDatabookStore();
  const {
    activeNode,
    handleNodeDataChange,
    validateErrors,
    resetNodesData,
    updateWorkflowValidation,
    hasIfThenNodes,
    updateWorkflowTrigger,
  } = useWorkflowBuilder();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [tempChangedValues, setTempChangedValues] = useState(null);
  const numberOfRunsToShow = 10;
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const clientTimeZone = myClient?.timeZone;

  // Feature flag for force-skip datasheet validation
  const isDsSkipValidationEnabled =
    clientFeatures?.isDsSkipValidationEnabled || false;

  // Fetch forceSkippedDatasheetIds using React Query (only when feature flag is enabled)
  const { accessToken } = useAuthStore();
  const { data: forceSkipData } = useReactQuery(
    ["forceSkippedDatasheetIds", accessToken],
    () => getForceSkippedDatasheetIds(accessToken),
    { enabled: !!accessToken && isDsSkipValidationEnabled }
  );
  let forceSkippedDatasheetIds = [];
  if (isDsSkipValidationEnabled && forceSkipData?.skipped_datasheet_ids) {
    forceSkippedDatasheetIds = forceSkipData.skipped_datasheet_ids.map(String);
  }

  // Helper function to get datasheet object by ID (only when feature flag is enabled)
  const getDatasheetObjById = (id) => {
    if (!isDsSkipValidationEnabled) return null;
    const databookId = form.getFieldValue("databookId");
    if (!databookId) return null;
    const datasheets = getDatasheets(databookId);
    return datasheets.find((ds) => String(ds.datasheetId) === String(id));
  };

  // Handle datasheet change with force-skip modal integration (only when feature flag is enabled)
  const handleDatasheetChange = async (value) => {
    if (!isDsSkipValidationEnabled) return true;
    // This will be called by the modal when user proceeds
    form.setFieldsValue({ datasheetId: value });
    return true;
  };

  // Use force-skip modal hook (only when feature flag is enabled)
  const { onDatasheetSelect, modal: forceSkipModal } = useForceSkipModal({
    forceSkippedDatasheetIds,
    handleDatasheetChange,
    getDatasheetObjById,
  });

  // Calculate GMT offset string once
  //const gmtOffset = moment.tz(clientTimeZone).format("Z"); // returns like '+08:00'
  const NEXT_RUN_FORMAT = `ddd, MMM DD, YYYY hh:mm A [(]${clientTimeZone}[)]`;

  const convertToClientTimeZone = (time) => {
    if (!time) return null;

    // If clientTimeZone is UTC, avoid unnecessary conversion
    return clientTimeZone === "UTC"
      ? moment.utc(time)
      : moment.utc(time).tz(clientTimeZone);
  };

  const getInitialValues = () => {
    const {
      databookId = null,
      datasheetId = null,
      triggerCategory = null,
      trigger = null,
      frequency = null,
      dailyTime = null,
      weeklyDay = null,
      weeklyDayTime = null,
      occurrence = null,
      occurrenceTime = null,
      occurrenceDate = null,
      occurrenceDateTime = null,
      changes = false, // default value for changes
    } = get(activeNode, `data.componentParams`, {});
    return {
      databookId,
      datasheetId,
      triggerCategory,
      trigger,
      frequency,
      dailyTime: convertToClientTimeZone(dailyTime),
      weeklyDay,
      weeklyDayTime: convertToClientTimeZone(weeklyDayTime),
      occurrence,
      occurrenceTime: convertToClientTimeZone(occurrenceTime),
      occurrenceDate,
      occurrenceDateTime: convertToClientTimeZone(occurrenceDateTime),
      changes,
    };
  };

  const prevValues = useRef(getInitialValues());

  const [nextRuns, setNextRuns] = useState([]);
  const [showAllRuns, setShowAllRuns] = useState(false);

  const handleConfirmChange = (confirm) => {
    if (confirm) {
      handleFormChanges(tempChangedValues.changedValues);
      const paramsToPreserve = {
        databookId: form.getFieldValue("databookId"),
        datasheetId: form.getFieldValue("datasheetId"),
      };
      resetNodesData(paramsToPreserve);
    } else {
      form.setFieldsValue({
        databookId: prevValues.current.databookId,
        datasheetId: prevValues.current.datasheetId,
      });
    }

    setShowConfirmationModal(false);
    setTempChangedValues(null);
  };

  const handleFormChanges = (changedValues) => {
    if (changedValues.databookId) {
      form.setFieldsValue({ datasheetId: null });
    }

    if (changedValues.triggerCategory) {
      resetTriggerCategoryFields(
        prevValues.current.triggerCategory,
        changedValues.triggerCategory
      );
      // Set changes based on triggerCategory
      const changes = changedValues.triggerCategory === "event_based";
      form.setFieldsValue({ changes });
    }

    if (changedValues.frequency) {
      resetFrequencyRelatedFields(
        prevValues.current.frequency,
        changedValues.frequency
      );
    }

    if (changedValues.occurrence) {
      resetMonthlyoccurrenceFields(
        prevValues.current.occurrence,
        changedValues.occurrence
      );
    }

    prevValues.current = form.getFieldsValue();
    onChange();
    handleNodeDataChange(
      {
        label: name,
        componentParams: {
          name,
          ...form.getFieldsValue(),
        },
      },
      true
    );
    updateNextRuns();
    updateBlockValidation();
  };

  // Add validation check on mount and unmount
  useEffect(() => {
    updateBlockValidation();
    // Populate nextRuns based on initial values when component mounts
    updateNextRuns(getInitialValues());
  }, []);

  const updateNextRuns = (values = null) => {
    const formValues = values || form.getFieldsValue();
    const {
      frequency,
      dailyTime,
      weeklyDay,
      weeklyDayTime,
      occurrence,
      occurrenceTime,
      occurrenceDate,
      occurrenceDateTime,
    } = formValues;
    let runs = [];

    if (frequency === "daily" && dailyTime) {
      runs = getNextDailyRuns(dailyTime);
    } else if (frequency === "weekly" && weeklyDay && weeklyDayTime) {
      runs = getNextWeeklyRuns(weeklyDay, weeklyDayTime);
    } else if (frequency === "monthly") {
      if (occurrence === "first" && occurrenceTime) {
        runs = getNextMonthlyRuns(1, occurrenceTime);
      } else if (occurrence === "last" && occurrenceTime) {
        runs = getNextMonthlyRuns(-1, occurrenceTime);
      } else if (
        occurrence === "adate" &&
        occurrenceDate &&
        occurrenceDateTime
      ) {
        runs = getNextMonthlyRuns(occurrenceDate, occurrenceDateTime);
      }
    }

    setNextRuns(runs);
  };

  const checkIfDateIsInPast = (date) => {
    return date.isBefore(moment().tz(clientTimeZone));
  };

  const getNextDailyRuns = (time) => {
    let date = convertToClientTimeZone(time).clone();
    if (checkIfDateIsInPast(date)) {
      date = date.add(1, "day");
    }
    return Array.from({ length: 10 }, (_, i) =>
      date.clone().add(i, "days").format(NEXT_RUN_FORMAT)
    );
  };

  const getNextWeeklyRuns = (day, time) => {
    let date = convertToClientTimeZone(time).day(day);
    if (checkIfDateIsInPast(date)) {
      date = date.add(1, "week");
    }
    return Array.from({ length: 10 }, (_, i) =>
      date.clone().add(i, "weeks").format(NEXT_RUN_FORMAT)
    );
  };

  const getNextMonthlyRuns = (dayOfMonth, time) => {
    let date;
    time = convertToClientTimeZone(time);

    if (dayOfMonth === -1) {
      date = time
        .clone()
        .endOf("month")
        .hour(time.hour())
        .minute(time.minute())
        .second(0);

      // If current last day of month is in the past, start from next month
      if (checkIfDateIsInPast(date)) {
        date = date.add(1, "month").endOf("month");
      }

      return Array.from({ length: 10 }, (_, i) =>
        date
          .clone()
          .add(i, "months")
          .endOf("month")
          .hour(time.hour())
          .minute(time.minute())
          .format(NEXT_RUN_FORMAT)
      );
    }

    // Start with current month
    let currentDate = time.clone().date(1);
    let foundDates = [];

    // Find 10 valid future dates
    while (foundDates.length < 10) {
      const daysInMonth = currentDate.daysInMonth();

      if (dayOfMonth <= daysInMonth) {
        const validDate = currentDate
          .clone()
          .date(dayOfMonth)
          .hour(time.hour())
          .minute(time.minute())
          .second(0);

        if (!checkIfDateIsInPast(validDate)) {
          foundDates.push(validDate.format(NEXT_RUN_FORMAT));
        }
      }

      currentDate = currentDate.add(1, "month").date(1);
    }

    return foundDates;
  };

  const updateBlockValidation = () => {
    const currentFormValues = form.getFieldsValue();
    const selectedTriggerCategory = currentFormValues.triggerCategory;

    let areAllRequiredFieldsFilled = false;

    if (selectedTriggerCategory === "event_based") {
      areAllRequiredFieldsFilled = !isNil(currentFormValues.trigger);
    } else if (selectedTriggerCategory === "scheduled") {
      const frequency = currentFormValues.frequency;

      if (frequency === "daily") {
        areAllRequiredFieldsFilled = !isNil(currentFormValues.dailyTime);
      } else if (frequency === "weekly") {
        areAllRequiredFieldsFilled =
          !isNil(currentFormValues.weeklyDay) &&
          !isNil(currentFormValues.weeklyDayTime);
      } else if (frequency === "monthly") {
        if (currentFormValues.occurrence === "adate") {
          areAllRequiredFieldsFilled =
            !isNil(currentFormValues.occurrenceDate) &&
            !isNil(currentFormValues.occurrenceDateTime);
        } else {
          areAllRequiredFieldsFilled =
            !isNil(currentFormValues.occurrence) &&
            !isNil(currentFormValues.occurrenceTime);
        }
      }

      // Check if frequency is selected
      areAllRequiredFieldsFilled =
        areAllRequiredFieldsFilled && !isNil(frequency);
    } else if (selectedTriggerCategory === "on_demand") {
      areAllRequiredFieldsFilled = true;
    }

    // Check common required fields
    areAllRequiredFieldsFilled =
      areAllRequiredFieldsFilled &&
      !isNil(selectedTriggerCategory) &&
      !isNil(currentFormValues.databookId) &&
      !isNil(currentFormValues.datasheetId);

    if (!areAllRequiredFieldsFilled) {
      setNodeError({
        [activeNode.id]: {
          message: "When block is incomplete",
          isValid: false,
        },
      });
    } else {
      let newValidationError = cloneDeep(validateErrors);
      const currentError = newValidationError[activeNode.id];

      if (currentError) {
        if (currentError === "When block is incomplete") {
          delete newValidationError[activeNode.id];
        } else {
          newValidationError[activeNode.id] = currentError
            .replace(new RegExp(`\\b${"When block is incomplete"}\\b`), "") // Remove specific error message
            .replace(/\s*,\s*/g, ",") // Normalize spaces around commas
            .replace(/,+/g, ",") // Replace multiple consecutive commas with one
            .replace(/^,|,$/g, ""); // Remove leading or trailing commas
        }

        setNodeError((prevState) => {
          const { [activeNode.id]: _, ...newState } = prevState;
          return newState;
        });

        updateWorkflowValidation({
          validateErrors: newValidationError,
        });
      } else {
        setNodeError((prevState) => {
          const { [activeNode.id]: _, ...newState } = prevState;
          return newState;
        });
      }
    }
  };

  const resetTriggerCategoryFields = (oldCategory, newCategory) => {
    const fieldsToReset = {
      event_based: ["trigger"],
      scheduled: [
        "frequency",
        "dailyTime",
        "weeklyDay",
        "weeklyDayTime",
        "occurrence",
        "occurrenceTime",
        "occurrenceDate",
        "occurrenceDateTime",
      ],
      on_demand: [],
    };

    if (oldCategory && oldCategory !== newCategory) {
      form.setFieldsValue(
        Object.fromEntries(
          fieldsToReset[oldCategory]?.map((key) => [key, null]) || []
        )
      );
    }
  };

  const resetFrequencyRelatedFields = (oldFrequency, newFrequency) => {
    const fieldsToReset = {
      daily: ["dailyTime"],
      weekly: ["weeklyDay", "weeklyDayTime"],
      monthly: ["occurrence"],
    };

    if (oldFrequency && oldFrequency !== newFrequency) {
      form.setFieldsValue(
        Object.fromEntries(
          fieldsToReset[oldFrequency]?.map((key) => [key, null]) || []
        )
      );

      // Additional reset for monthly frequency
      if (oldFrequency === "monthly") {
        const currentoccurrence = form.getFieldValue("occurrence");
        if (currentoccurrence === "adate") {
          form.setFieldsValue({
            occurrenceDate: null,
            occurrenceDateTime: null,
          });
        } else {
          form.setFieldsValue({ occurrenceTime: null });
        }
      }
    }
  };

  const resetMonthlyoccurrenceFields = (oldoccurrence, newoccurrence) => {
    if (oldoccurrence && oldoccurrence !== newoccurrence) {
      if (newoccurrence === "adate") {
        form.setFieldsValue({ occurrenceTime: null });
      } else {
        form.setFieldsValue({
          occurrenceDate: null,
          occurrenceDateTime: null,
        });
      }
    }
  };

  // Add validation check on mount and unmount
  useEffect(() => {
    updateBlockValidation();
  }, []);

  // Update category when it changes
  useEffect(() => {
    const category = form.getFieldValue("triggerCategory");
    if (category) {
      updateWorkflowTrigger({ category });
    }
  }, [form.getFieldValue("triggerCategory"), updateWorkflowTrigger]);

  return (
    <>
      <EverForm
        form={form}
        layout="vertical"
        className="custom-period-form"
        initialValues={getInitialValues()}
        onValuesChange={(changedValues) => {
          const workflowHasIfThenNodes = hasIfThenNodes();
          if (changedValues.databookId || changedValues.datasheetId) {
            if (prevValues.current.datasheetId && workflowHasIfThenNodes) {
              setShowConfirmationModal(true);
              const newValues = form.getFieldsValue();
              setTempChangedValues({ changedValues, newValues });
              return;
            }
          }

          handleFormChanges(changedValues);
        }}
      >
        <EverForm.Item
          name="databookId"
          label="Databook"
          rules={[{ required: true, message: "Databook is required" }]}
        >
          <EverSelect
            placeholder="Select Databook"
            options={activeDatabooks.map((databook) => ({
              value: databook.databookId,
              label: databook.name,
            }))}
            disabled={disableFields}
            showSearch
          />
        </EverForm.Item>
        <EverForm.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.databookId !== curValues.databookId
          }
        >
          {({ getFieldValue }) => (
            <EverForm.Item
              name="datasheetId"
              label="Datasheet"
              rules={[{ required: true, message: "Datasheet is required" }]}
            >
              {isDsSkipValidationEnabled ? (
                // New force-skip enabled dropdown
                <EverSelect
                  placeholder="Select Datasheet"
                  disabled={!getFieldValue("databookId") || disableFields}
                  showSearch
                  onChange={onDatasheetSelect}
                  optionLabelProp="label"
                >
                  {getDatasheets(getFieldValue("databookId")).map(
                    (datasheet) => (
                      <EverSelect.Option
                        value={String(datasheet.datasheetId)}
                        key={datasheet.datasheetId}
                        label={datasheet.name}
                        title={datasheet.name}
                      >
                        <DatasheetOptionWithIcon
                          name={datasheet.name}
                          id={String(datasheet.datasheetId)}
                          forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                        />
                      </EverSelect.Option>
                    )
                  )}
                </EverSelect>
              ) : (
                // Old simple dropdown behavior
                <EverSelect
                  placeholder="Select Datasheet"
                  options={getDatasheets(getFieldValue("databookId")).map(
                    (datasheet) => ({
                      value: datasheet.datasheetId,
                      label: datasheet.name,
                    })
                  )}
                  disabled={!getFieldValue("databookId") || disableFields}
                  showSearch
                />
              )}
            </EverForm.Item>
          )}
        </EverForm.Item>
        <EverForm.Item
          name="triggerCategory"
          label="Trigger Category"
          rules={[{ required: true, message: "Trigger Category is required" }]}
        >
          <EverSelect
            placeholder="Select Trigger Category"
            options={Object.entries(TRIGGER_CATEGORIES).map(
              ([key, category]) => ({
                value: key,
                label: category.label,
                icon: category.icon,
                className: "flex items-center gap-2",
                iconJSX: (
                  <div className="flex items-center gap-2">
                    <div className="flex p-1 bg-ever-primary-ring rounded-sm">
                      {category.icon}
                    </div>
                    <span>{category.label}</span>
                  </div>
                ),
              })
            )}
            disabled={disableFields}
            optionLabelProp="iconJSX"
          />
        </EverForm.Item>

        <EverForm.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.triggerCategory !== curValues.triggerCategory ||
            prevValues.trigger !== curValues.trigger ||
            prevValues.frequency !== curValues.frequency ||
            prevValues.dailyTime !== curValues.dailyTime ||
            prevValues.weeklyDay !== curValues.weeklyDay ||
            prevValues.weeklyDayTime !== curValues.weeklyDayTime ||
            prevValues.occurrence !== curValues.occurrence ||
            prevValues.occurrenceDate !== curValues.occurrenceDate ||
            prevValues.occurrenceDateTime !== curValues.occurrenceDateTime
          }
        >
          {({ getFieldValue }) => {
            const selectedTriggerCategory = getFieldValue("triggerCategory");
            if (selectedTriggerCategory === "event_based") {
              return (
                <EverForm.Item
                  name="trigger"
                  label="Trigger"
                  rules={[{ required: true, message: "Trigger is required" }]}
                >
                  <EverSelect
                    placeholder="Select Trigger"
                    options={[
                      { value: "new", label: "When a record is added" },
                      { value: "updated", label: "When a record is updated" },
                      { value: "deleted", label: "When a record is deleted" },
                    ]}
                    disabled={disableFields}
                  />
                </EverForm.Item>
              );
            } else if (selectedTriggerCategory === "scheduled") {
              return (
                <>
                  <EverForm.Item
                    name="frequency"
                    label="Frequency"
                    rules={[
                      { required: true, message: "Frequency is required" },
                    ]}
                  >
                    <EverSelect
                      placeholder="Select Frequency"
                      options={[
                        {
                          value: "daily",
                          label: "Every day at a certain time on all records",
                        },
                        {
                          value: "weekly",
                          label:
                            "Every week on a certain day and a certain time on all records",
                        },
                        {
                          value: "monthly",
                          label:
                            "Every month on a certain date and a certain time on all records",
                        },
                      ]}
                      disabled={disableFields}
                    />
                  </EverForm.Item>
                  {getFieldValue("frequency") === "daily" && (
                    <EverForm.Item
                      name="dailyTime"
                      label="Time"
                      rules={[{ required: true, message: "Time is required" }]}
                    >
                      <EverTimePicker
                        format="hh:mm A"
                        placeholder="Select time"
                        disabled={disableFields}
                        timezone={clientTimeZone}
                      />
                    </EverForm.Item>
                  )}
                  {getFieldValue("frequency") === "weekly" && (
                    <>
                      <EverForm.Item
                        name="weeklyDay"
                        label="Day"
                        rules={[{ required: true, message: "Day is required" }]}
                      >
                        <EverSelect
                          placeholder="Select Day"
                          options={[
                            { value: "sunday", label: "Sunday" },
                            { value: "monday", label: "Monday" },
                            { value: "tuesday", label: "Tuesday" },
                            { value: "wednesday", label: "Wednesday" },
                            { value: "thursday", label: "Thursday" },
                            { value: "friday", label: "Friday" },
                            { value: "saturday", label: "Saturday" },
                          ]}
                          disabled={disableFields}
                        />
                      </EverForm.Item>
                      {getFieldValue("weeklyDay") && (
                        <EverForm.Item
                          name="weeklyDayTime"
                          label="Time"
                          rules={[
                            { required: true, message: "Time is required" },
                          ]}
                        >
                          <EverTimePicker
                            format="hh:mm A"
                            placeholder="Select time"
                            disabled={disableFields}
                            timezone={clientTimeZone}
                          />
                        </EverForm.Item>
                      )}
                    </>
                  )}
                  {getFieldValue("frequency") === "monthly" && (
                    <>
                      <EverForm.Item
                        name="occurrence"
                        label="Occurrence"
                        rules={[
                          { required: true, message: "Occurrence is required" },
                        ]}
                      >
                        <EverSelect
                          placeholder="Select Occurrence"
                          options={[
                            { value: "first", label: "First day of the month" },
                            { value: "last", label: "Last day of the month" },
                            { value: "adate", label: "A date" },
                          ]}
                          disabled={disableFields}
                        />
                      </EverForm.Item>
                      {["first", "last"].includes(
                        getFieldValue("occurrence")
                      ) && (
                        <EverForm.Item
                          name="occurrenceTime"
                          label="Time"
                          rules={[
                            { required: true, message: "Time is required" },
                          ]}
                        >
                          <EverTimePicker
                            format="hh:mm A"
                            placeholder="Select time"
                            disabled={disableFields}
                            timezone={clientTimeZone}
                          />
                        </EverForm.Item>
                      )}
                      {getFieldValue("occurrence") === "adate" && (
                        <>
                          <EverForm.Item
                            name="occurrenceDate"
                            label="Date"
                            rules={[
                              { required: true, message: "Date is required" },
                            ]}
                          >
                            <EverSelect
                              placeholder="Select date"
                              options={Array.from({ length: 31 }, (_, i) => ({
                                value: i + 1,
                                label: `${i + 1}`,
                              }))}
                              disabled={disableFields}
                            />
                          </EverForm.Item>
                          {getFieldValue("occurrenceDate") && (
                            <EverForm.Item
                              name="occurrenceDateTime"
                              label="Time"
                              rules={[
                                { required: true, message: "Time is required" },
                              ]}
                            >
                              <EverTimePicker
                                format="hh:mm A"
                                placeholder="Select time"
                                disabled={disableFields}
                                timezone={clientTimeZone}
                              />
                            </EverForm.Item>
                          )}
                        </>
                      )}
                    </>
                  )}
                </>
              );
            }
            return null;
          }}
        </EverForm.Item>
        <EverForm.Item name="changes" hidden>
          <input />
        </EverForm.Item>
        {nextRuns.length > 0 && (
          <div className="flex flex-col gap-3 bg-ever-primary-lite p-3 rounded-lg">
            <div className="flex gap-2 items-center">
              <InfoCircleIcon className="w-4 h-4 text-ever-primary" />
              <EverTg.SubHeading4 className="base-content">
                Next run
              </EverTg.SubHeading4>
            </div>
            <div className="flex flex-col gap-3 ml-6">
              {nextRuns
                .slice(0, showAllRuns ? numberOfRunsToShow : 1)
                .map((run, index) => (
                  <EverTg.Text key={index}>
                    • <span className="ml-2">{run}</span>
                  </EverTg.Text>
                ))}
            </div>
            {nextRuns.length > 1 && (
              <EverButton
                className="w-48"
                size="small"
                type="link"
                color="primary"
                prependIcon={<ChevronDownIcon className="w-4 h-4" />}
                onClick={() =>
                  setShowAllRuns((prevState) => {
                    return !prevState;
                  })
                }
              >
                {showAllRuns
                  ? `Hide next ${numberOfRunsToShow} runs`
                  : `Show next ${numberOfRunsToShow} runs`}
              </EverButton>
            )}
          </div>
        )}
      </EverForm>
      {/* Render the force-skip modal only when feature flag is enabled */}
      {isDsSkipValidationEnabled && forceSkipModal}
      <EverModal.Confirm
        visible={showConfirmationModal}
        title="Are you sure you want to change the data source?"
        subtitle="This will reset the existing datasheet fields used in the actions."
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => handleConfirmChange(false)}
          >
            No
          </EverButton>,
          <EverButton
            color="primary"
            key="edit"
            onClick={() => handleConfirmChange(true)}
          >
            Yes
          </EverButton>,
        ]}
      />
    </>
  );
};

export default DatasheetData;
