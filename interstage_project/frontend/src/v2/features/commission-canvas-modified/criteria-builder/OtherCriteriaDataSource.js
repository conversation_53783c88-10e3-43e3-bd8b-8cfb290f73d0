import { useQuery } from "@apollo/client";
import { HelpCircleIcon, XCloseIcon } from "@everstage/evericons/outlined";
import { isEmpty, sortBy } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";
import { useImmer } from "use-immer";

import {
  COMMISSION_TYPE,
  DATATYPE,
  DATA_ORIGIN,
  QUOTA_CATEGORIES,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import {
  getDatabooksForDataSourceConfiguration,
  getDataSheetVariables,
} from "~/Utils/canvasPlansUtils";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverLabel,
  EverLoader,
  EverModal,
  EverSelect,
  EverSwitch,
  EverTg,
  EverTooltip,
  IconButton,
  message,
} from "~/v2/components";
import DatasheetOptionWithIcon from "~/v2/components/DatasheetOptionWithIcon";
import { TEAM_MORE_INFO } from "~/v2/features/commission-canvas-modified/constants";
import { useCommissionPlanType } from "~/v2/features/commission-canvas-modified/context";
import { GET_ALL_QUOTA_CATERGORIES } from "~/v2/features/commission-canvas-modified/services/graphql";
import { useForceSkipModal } from "~/v2/features/datasheet/helperComponents/useForceSkipModal";
import { getForceSkippedDatasheetIds } from "~/v2/features/datasheet/restApi";

const showDependencyToast = (dependentSettlementNames) => {
  message.error(
    `Please remove this component from the following settlement rules before changing the data source: ${dependentSettlementNames.join(
      ", "
    )}`
  );
};

// specific to selected criteria
const OtherCriteriaDataSource = ({
  className,
  allTeams,
  criteriaType,
  criteriaId,
  criteriaW,
  criteriaO,
  onCriteriaConfigChange,
  onCriteriaDatasourceChange,
  onCriteriaSortColsConfigChange,
  handleCloseModal,
  isDraft,
  canEdit,
  onEnableTeamForCriteria,
  onDisableTeamForCriteria,
  getDependentSettlementRuleNames,
  isUnderReview,
}) => {
  const { planType } = useCommissionPlanType();
  const { accessToken } = useAuthStore();
  const { t } = useTranslation();

  const [selectComponentOptions, setSelectComponentOptions] = useImmer({
    email: [],
    date: [],
    all: [],
  });
  const [isTeamSelectionEnabled, updateIsTeamSelectionEnabled] = useState(
    criteriaW.type === "CustomTeam"
  );
  const [dsVariablesLoading, setDsVariablesLoading] = useState(false);

  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const { sortedDatabooks, getSortedDatasheetsByOrigin } = useDatabookStore();

  // Feature flag for force-skip datasheet validation
  const isDsSkipValidationEnabled =
    clientFeatures?.isDsSkipValidationEnabled || false;

  const criteriaLevel = criteriaW.criteriaLevel || "L1";

  // if criteria is quota or (team criteria with quota as subcriteria)
  const showQuotaCategory =
    criteriaType === "CustomQuota" ||
    (criteriaType === "CustomTeam" && criteriaW.data?.type === "Quota");

  const sortColumnValues = (() => {
    const sortCols = isEmpty(criteriaW.criteriaConfig.sortCols)
      ? []
      : criteriaW.criteriaConfig.sortCols;

    return sortCols.map((col) => col[0]);
  })();

  const { data: quotaCategories } = useQuery(GET_ALL_QUOTA_CATERGORIES, {
    skip: !showQuotaCategory,
    fetchPolicy: "no-cache",
  });

  const { data: forceSkipData } = useReactQuery(
    ["forceSkippedDatasheetIds", accessToken],
    () => getForceSkippedDatasheetIds(accessToken),
    {
      enabled: !!accessToken && isDsSkipValidationEnabled,
    }
  );

  // Normalize forceSkippedDatasheetIds from API response
  let forceSkippedDatasheetIds = [];
  if (isDsSkipValidationEnabled && forceSkipData?.skipped_datasheet_ids) {
    forceSkippedDatasheetIds = forceSkipData.skipped_datasheet_ids.map(String);
  }

  useEffect(() => {
    async function _useEffectAsync() {
      const savedDatabookId = criteriaW.data.databookId;
      const savedDatasheetId = criteriaW.data.datasheetId;
      const savedDateField = criteriaW.data.dateField;
      const savedPayeeField = criteriaW.data.payeeField;

      if (savedDatabookId) {
        const isDataBookFound = sortedDatabooks.some(
          (databook) => databook.databookId === savedDatabookId
        );

        if (isDataBookFound) {
          let datasheets = [];
          datasheets =
            clientFeatures.exposeCommReportsInPlan || !isDraft
              ? getSortedDatasheetsByOrigin(savedDatabookId, [
                  DATA_ORIGIN.CUSTOM_OBJECT,
                  DATA_ORIGIN.SYSTEM_OBJECT,
                  DATA_ORIGIN.INTER_OBJECT,
                  ...(planType === COMMISSION_TYPE.FORECAST_PLAN
                    ? [DATA_ORIGIN.INTER_FORECAST_OBJECT]
                    : []),
                ])
              : getSortedDatasheetsByOrigin(savedDatabookId, [
                  DATA_ORIGIN.CUSTOM_OBJECT,
                  DATA_ORIGIN.SYSTEM_OBJECT,
                ]);
          const isDataSheetFound = datasheets.some(
            (datasheet) => datasheet.datasheetId === savedDatasheetId
          );

          if (isDataSheetFound) {
            let payeeFields = [],
              dateFields = [];

            setDsVariablesLoading(true);
            let allVariableTokens = await getDataSheetVariables(
              accessToken,
              savedDatasheetId
            );
            let isDateFieldFound = false,
              isPayeeFieldFound = false;
            for (const v of allVariableTokens) {
              if (v.dataType_DataType === DATATYPE.DATE) {
                dateFields.push({
                  systemName: v.systemName,
                  name: v.displayName,
                });
                isDateFieldFound =
                  isDateFieldFound || v.systemName === savedDateField;
              } else if (v.dataType_DataType === DATATYPE.EMAIL) {
                payeeFields.push({
                  systemName: v.systemName,
                  name: v.displayName,
                });
                isPayeeFieldFound =
                  isPayeeFieldFound || v.systemName === savedPayeeField;
              }
            }

            setSelectComponentOptions((draft) => {
              draft.email = sortBy(payeeFields, [
                (payeeField) => payeeField.name.toLowerCase(),
              ]).map((field) => ({
                value: field.systemName,
                label: field.name,
              }));
              draft.date = sortBy(dateFields, [
                (dateField) => dateField.name.toLowerCase(),
              ]).map((field) => ({
                value: field.systemName,
                label: field.name,
              }));
              draft.all = sortBy(allVariableTokens, [
                (field) => field.displayName.toLowerCase(),
              ]).map((field) => ({
                value: field.systemName,
                label: field.displayName,
              }));
            });
            setDsVariablesLoading(false);
          }
        }
      }
    }
    _useEffectAsync();
  }, []);

  // get databook options
  const databookOptions = getDatabooksForDataSourceConfiguration(
    sortedDatabooks,
    criteriaO.data.databookId
  ).map((databook) => ({
    value: databook.databookId,
    label: databook.name,
  }));
  const datasheetOptions =
    clientFeatures.exposeCommReportsInPlan || !isDraft
      ? getSortedDatasheetsByOrigin(criteriaW.data.databookId, [
          DATA_ORIGIN.CUSTOM_OBJECT,
          DATA_ORIGIN.SYSTEM_OBJECT,
          DATA_ORIGIN.INTER_OBJECT,
          ...(planType === COMMISSION_TYPE.FORECAST_PLAN
            ? [DATA_ORIGIN.INTER_FORECAST_OBJECT]
            : []),
        ])?.map((datasheet) => ({
          value: datasheet.datasheetId,
          label: datasheet.name,
        }))
      : getSortedDatasheetsByOrigin(criteriaW.data.databookId, [
          DATA_ORIGIN.CUSTOM_OBJECT,
          DATA_ORIGIN.SYSTEM_OBJECT,
        ])?.map((datasheet) => ({
          value: datasheet.datasheetId,
          label: datasheet.name,
        }));

  // Quota category options
  const quotaOptions =
    sortBy(quotaCategories?.allQuotaCategories ?? [], [
      (category) =>
        (category.displayName ?? category.quotaCategoryName).toLowerCase(),
    ]).map((category) => ({
      value: category.quotaCategoryName,
      label: category.displayName,
    })) || [];
  // Insert the primary quota at index 0
  quotaOptions.splice(0, 0, {
    label: t("PRIMARY_QUOTA"),
    value: QUOTA_CATEGORIES.PRIMARY_QUOTA,
  });

  const handleDatasheetChange = async (v) => {
    const settlementRuleNames = getDependentSettlementRuleNames(criteriaW.id);
    if (!isEmpty(settlementRuleNames)) {
      showDependencyToast(settlementRuleNames);
      return;
    }

    const _handleDatasheetChange = async () => {
      onCriteriaDatasourceChange({
        datasheetId: v,
        payeeField: null,
        dateField: null,
        isValid: false,
      });

      setDsVariablesLoading(true);
      let allVariableTokens = await getDataSheetVariables(accessToken, v);
      let payeeFields = [],
        dateFields = [];

      for (const v of allVariableTokens) {
        if (v.dataType_DataType === DATATYPE.DATE) {
          dateFields.push({
            systemName: v.systemName,
            name: v.displayName,
          });
        } else if (v.dataType_DataType === DATATYPE.EMAIL) {
          payeeFields.push({
            systemName: v.systemName,
            name: v.displayName,
          });
        }
      }

      setSelectComponentOptions((draft) => {
        draft.email = payeeFields.map((field) => ({
          value: field.systemName,
          label: field.name,
        }));
        draft.date = dateFields.map((field) => ({
          value: field.systemName,
          label: field.name,
        }));
        draft.all = allVariableTokens.map((field) => ({
          value: field.systemName,
          label: field.displayName,
        }));
      });
      setDsVariablesLoading(false);
    };
    const previousDatasheetId = criteriaW.data.datasheetId;

    if (previousDatasheetId) {
      EverModal.confirm({
        width: 540,
        title: "Data source change erases the imported formula",
        subtitle:
          "If you change the data source, the entire imported formula will be lost, and you must begin again. Are you sure you want to proceed?",
        okText: "Confirm",
        cancelText: "Discard",
        centered: true,
        onOk: _handleDatasheetChange,
      });
    } else {
      _handleDatasheetChange();
    }
  };

  const handleDatabookChange = (v) => {
    const settlementRuleNames = getDependentSettlementRuleNames(criteriaW.id);
    if (!isEmpty(settlementRuleNames)) {
      showDependencyToast(settlementRuleNames);
      return;
    }

    const previousDatasheetId = criteriaW.data.datasheetId;
    if (previousDatasheetId) {
      EverModal.confirm({
        width: 540,
        title: "Data source change erases the imported formula",
        subtitle:
          "If you change the data source, the entire imported formula will be lost, and you must begin again. Are you sure you want to proceed?",
        okText: "Confirm",
        cancelText: "Discard",
        centered: true,
        onOk: () => {
          onCriteriaDatasourceChange({
            databookId: v,
            datasheetId: null,
            payeeField: null,
            dateField: null,
            isValid: false,
          });
          setSelectComponentOptions((draft) => {
            draft.email = [];
            draft.date = [];
            draft.all = [];
          });
        },
      });
    } else {
      onCriteriaDatasourceChange({
        databookId: v,
        datasheetId: null,
        payeeField: null,
        dateField: null,
        isValid: false,
      });
      setSelectComponentOptions((draft) => {
        draft.email = [];
        draft.date = [];
        draft.all = [];
      });
    }
  };

  const getDataSourceValidity = (newValues) => {
    const oldValues = {
      payeeField: criteriaW.data.payeeField,
      dateField: criteriaW.data.dateField,
      datasheetId: criteriaW.data.datasheetId,
      databookId: criteriaW.data.databookId,
    };
    if (showQuotaCategory) {
      oldValues.quotaName = criteriaW.data.quotaName;
    }

    const mergedValues = { ...oldValues, ...newValues };
    return Object.values(mergedValues).every((v) => !isEmpty(v));
  };

  const isNotEditable = !isDraft || !canEdit;

  // Helper function to get datasheet list
  const getDatasheetList = () => {
    return (
      getSortedDatasheetsByOrigin(criteriaW.data.databookId, [
        DATA_ORIGIN.CUSTOM_OBJECT,
        DATA_ORIGIN.SYSTEM_OBJECT,
        DATA_ORIGIN.INTER_OBJECT,
        ...(planType === COMMISSION_TYPE.FORECAST_PLAN
          ? [DATA_ORIGIN.INTER_FORECAST_OBJECT]
          : []),
      ]) || []
    );
  };

  // For force-skip modal logic
  const getDatasheetObjById = (id) => {
    const datasheetList = getDatasheetList();
    return datasheetList.find((ds) => ds.datasheetId === id);
  };
  const forceSkip = useForceSkipModal({
    forceSkippedDatasheetIds,
    handleDatasheetChange,
    getDatasheetObjById,
  });
  let onDatasheetSelect = handleDatasheetChange;
  let forceSkipModal = null;
  if (isDsSkipValidationEnabled) {
    onDatasheetSelect = forceSkip.onDatasheetSelect;
    forceSkipModal = forceSkip.modal;
  }

  return (
    <div
      className={twMerge(
        className,
        "overflow-auto absolute my-2 right-2 top-[60px] w-80 bg-ever-base-25 border border-solid border-ever-base-300 rounded-xl py-6 shadow-sm",
        "z-10 h-[calc(100%-132px)]" // 60 + 60 + 6 (header + footer + some extra space)
      )}
    >
      <IconButton
        onClick={handleCloseModal}
        icon={<XCloseIcon />}
        className="absolute top-2 right-2 z-10"
        type="text"
        color="base"
      />
      <EverLoader
        spinning={dsVariablesLoading}
        size={2}
        indicatorType="spinner"
      >
        <div className="px-6">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-4">
              <EverTg.Heading3>Data source</EverTg.Heading3>
              <EverTg.SubHeading4 className="text-ever-base-content">
                {`Choose the data based on which the ${t(
                  "COMMISSIONS_LOWERCASE"
                )} will be calculated.`}
              </EverTg.SubHeading4>
            </div>
            <div className="flex flex-col gap-1">
              <EverLabel required>Databook</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                value={criteriaW.data.databookId}
                disabled={dsVariablesLoading || isNotEditable || isUnderReview}
                onChange={handleDatabookChange}
                options={databookOptions}
              />
            </div>
            <div className="flex flex-col gap-1">
              <EverLabel required>Datasheet</EverLabel>
              {forceSkipModal}
              {isDsSkipValidationEnabled ? (
                <EverSelect
                  showSearch
                  placeholder="Select Datasheet"
                  value={criteriaW.data.datasheetId}
                  disabled={
                    dsVariablesLoading || isNotEditable || isUnderReview
                  }
                  onChange={onDatasheetSelect}
                  optionLabelProp="label"
                  filterOption={(input, option) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  }}
                >
                  {getDatasheetList().map((ds) => (
                    <EverSelect.Option
                      key={ds.datasheetId}
                      value={ds.datasheetId}
                      label={ds.name}
                      title={ds.name}
                    >
                      <DatasheetOptionWithIcon
                        name={ds.name}
                        id={String(ds.datasheetId)}
                        forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                      />
                    </EverSelect.Option>
                  ))}
                </EverSelect>
              ) : (
                <EverSelect
                  showSearch
                  placeholder="Select Datasheet"
                  value={criteriaW.data.datasheetId}
                  disabled={
                    dsVariablesLoading || isNotEditable || isUnderReview
                  }
                  onChange={handleDatasheetChange}
                  filterOption={(input, option) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  }}
                  options={datasheetOptions}
                />
              )}
            </div>
            <div className="flex flex-col gap-1">
              <EverLabel required>Email field</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                value={criteriaW.data.payeeField}
                disabled={dsVariablesLoading || isNotEditable || isUnderReview}
                onChange={(v) =>
                  onCriteriaDatasourceChange({
                    payeeField: v,
                    isValid: getDataSourceValidity({ payeeField: v }),
                  })
                }
                options={selectComponentOptions.email}
              />
            </div>
            <div className="flex flex-col gap-1">
              <EverLabel required>Date field</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                value={criteriaW.data.dateField}
                disabled={dsVariablesLoading || isNotEditable || isUnderReview}
                onChange={(v) =>
                  onCriteriaDatasourceChange({
                    dateField: v,
                    isValid: getDataSourceValidity({ dateField: v }),
                  })
                }
                options={selectComponentOptions.date}
              />
            </div>
            {showQuotaCategory && (
              <div className="flex flex-col gap-1">
                <EverLabel required>{`${t("QUOTA")} Category`}</EverLabel>
                <EverSelect
                  showSearch
                  filterOption={(input, option) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  }}
                  value={criteriaW.data.quotaName}
                  disabled={
                    dsVariablesLoading || isNotEditable || isUnderReview
                  }
                  onChange={(v) => {
                    onCriteriaDatasourceChange({
                      quotaName: v,
                      isValid: getDataSourceValidity({ quotaName: v }),
                    });
                  }}
                  options={quotaOptions}
                />
              </div>
            )}
            <div className="flex flex-col gap-2">
              <div className="flex items-center">
                <EverSwitch
                  checked={isTeamSelectionEnabled}
                  disabled={
                    dsVariablesLoading || isNotEditable || isUnderReview
                  }
                  onChange={(v) => {
                    if (!v) {
                      onDisableTeamForCriteria(criteriaId);
                      // reset selected team
                    }
                    updateIsTeamSelectionEnabled(v);
                  }}
                />
                <EverTg.Text className="ml-3 text-ever-base-content-mid">
                  Enable team calculation
                </EverTg.Text>
                <EverTooltip title={TEAM_MORE_INFO}>
                  <HelpCircleIcon className="ml-2 w-4 h-4 text-ever-base-content-mid" />
                </EverTooltip>
              </div>
              {isTeamSelectionEnabled && (
                <EverSelect
                  showSearch
                  filterOption={(input, option) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  }}
                  placeholder="Select team"
                  value={criteriaW.data?.team}
                  disabled={
                    dsVariablesLoading || isNotEditable || isUnderReview
                  }
                  onChange={(v) => {
                    onEnableTeamForCriteria(criteriaId, v);
                  }}
                  options={allTeams}
                />
              )}
            </div>
          </div>
          <div className="flex flex-col mt-4">
            <div className="flex items-center gap-1">
              <EverTg.Heading3>Order records (Asc)</EverTg.Heading3>
              <EverTooltip title="Records are sorted in ascending order. The date field is the default sort column. Custom sorting options, if added, will override the sort on date field.">
                <HelpCircleIcon className="w-4 h-4 text-ever-base-content-low" />
              </EverTooltip>
            </div>
            <div className="mt-1">
              <EverLabel className="text-xs">{`Select the fields to sort the records for ${t(
                "COMMISSIONS_LOWERCASE"
              )} calculation`}</EverLabel>
            </div>
            <div className="mt-3">
              <EverSelect
                className="w-full h-full"
                mode="multiple"
                multiSelectOverflow="default"
                disabled={dsVariablesLoading || !canEdit || isUnderReview}
                maxTagCount={false}
                value={sortColumnValues}
                options={selectComponentOptions.all}
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                onChange={onCriteriaSortColsConfigChange}
              />
            </div>
          </div>
          {(planType === COMMISSION_TYPE.COMMISSION_PLAN ||
            (criteriaLevel === "L1" &&
              (clientFeatures.exposeCommReportsInPlan ||
                (isDraft === false &&
                  criteriaW.criteriaConfig.intermediateOnly)))) && (
            <div className="flex flex-col gap-4 mt-6">
              <div className="flex flex-col gap-4">
                <EverTg.Heading3>Customize</EverTg.Heading3>
                <EverTg.SubHeading4 className="text-ever-base-content">
                  {"Manage what you show on the sales reps' Statements."}
                </EverTg.SubHeading4>
              </div>
              {planType === COMMISSION_TYPE.COMMISSION_PLAN && (
                <>
                  <div className="flex items-center">
                    <EverSwitch
                      checked={criteriaW.criteriaConfig.criteriaIsHidden}
                      disabled={dsVariablesLoading || !canEdit || isUnderReview}
                      onChange={(v) => {
                        onCriteriaConfigChange({
                          criteriaIsHidden: v,
                        });
                      }}
                    />
                    <EverTg.Text className="ml-3 text-ever-base-content-mid">
                      Hide this component
                    </EverTg.Text>
                    <EverTooltip
                      className="ml-2 mt-0.5"
                      title='Only payees with "View hidden component in statements" permission can see this'
                    >
                      <HelpCircleIcon className="w-4 h-4 text-ever-base-content-mid" />
                    </EverTooltip>
                  </div>
                  <div className="flex items-center">
                    <EverSwitch
                      checked={criteriaW.criteriaConfig.traceEnabled}
                      disabled={dsVariablesLoading || !canEdit || isUnderReview}
                      onChange={(v) =>
                        onCriteriaConfigChange({
                          traceEnabled: v,
                        })
                      }
                    />
                    <EverTg.Text className="ml-3 text-ever-base-content-mid">
                      {t("SHOW_COMMISSION_TRACE")}
                    </EverTg.Text>
                  </div>
                </>
              )}
              {criteriaLevel === "L1" &&
                (clientFeatures.exposeCommReportsInPlan ||
                  (isDraft === false &&
                    criteriaW.criteriaConfig.intermediateOnly)) && (
                  <div className="flex items-center">
                    <EverSwitch
                      disabled={
                        isDraft === false ||
                        dsVariablesLoading ||
                        !canEdit ||
                        isUnderReview
                      }
                      checked={criteriaW.criteriaConfig.intermediateOnly}
                      onChange={(v) =>
                        onCriteriaConfigChange({
                          intermediateOnly: v,
                        })
                      }
                    />
                    <EverTg.Text className="ml-3 text-ever-base-content-mid">
                      Intermediate only component
                    </EverTg.Text>
                  </div>
                )}
            </div>
          )}
        </div>
      </EverLoader>
    </div>
  );
};

export default OtherCriteriaDataSource;
