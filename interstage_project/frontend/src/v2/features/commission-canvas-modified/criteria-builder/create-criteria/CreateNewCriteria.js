import React from "react";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

// Global stores and utils
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { getForceSkippedDatasheetIds } from "~/v2/features/datasheet/restApi";

// Local components and hooks
import { SelectCriteriaType } from "./SelectCriteriaType";
import { SelectDataSource } from "./SelectDataSource";
import { useCreateCriteria } from "./useCreateCriteria";

export const CreateNewCriteria = ({
  criteriaOption,
  commissionPlanDetails,
  planCriteriasW,
  settlementRulesW,
  currentStep,
  quotaOptions,
  handleMoveNextStep,
  handleMovePrevStep: movePrevStep,
  onAddCriteria,
}) => {
  const {
    dataSourceForm,
    criteriaName,
    criteriaNameError,
    criteriaType,
    handleChangeCriteriaName,
    handleChangeCriteriaType,
    databookOptions,
    datasheetOptions,
    dateFieldOptions,
    emailFieldOptions,
    teamOptions,
    criteriaComponents,
    commissionJoinKeys,
    settlementJoinKeys,
    handleChangeDatabook,
    handleChangeDatasheet,
    handleChangeCriteriaComponent,
    handleCreateCriteria,
    checkCreateVisibility,
    dsVariablesLoading,
    handleMovePrevStep,
  } = useCreateCriteria({
    criteriaOption,
    commissionPlanDetails,
    planCriteriasW,
    settlementRulesW,
    movePrevStep,
    onAddCriteria,
  });

  const { accessToken } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  // Feature flag for force-skip datasheet validation
  const isDsSkipValidationEnabled =
    clientFeatures?.isDsSkipValidationEnabled || false;

  // Fetch forceSkippedDatasheetIds using React Query (only when feature flag is enabled)
  const { data: forceSkipData } = useReactQuery(
    ["forceSkippedDatasheetIds", accessToken],
    () => getForceSkippedDatasheetIds(accessToken),
    {
      enabled: !!accessToken && isDsSkipValidationEnabled,
    }
  );

  let forceSkippedDatasheetIds = [];
  if (isDsSkipValidationEnabled && forceSkipData?.skipped_datasheet_ids) {
    forceSkippedDatasheetIds = forceSkipData.skipped_datasheet_ids.map(String);
  }

  return (
    <>
      {currentStep === 1 && (
        <SelectCriteriaType
          handleMoveNextStep={handleMoveNextStep}
          handleChangeCriteriaType={handleChangeCriteriaType}
          criteriaType={criteriaType}
          criteriaName={criteriaName}
          criteriaNameError={criteriaNameError}
          isSettlement={commissionPlanDetails.isSettlementEndDate}
          handleChangeCriteriaName={handleChangeCriteriaName}
        />
      )}
      {currentStep === 2 && (
        <SelectDataSource
          criteriaType={criteriaType}
          criteriaName={criteriaName}
          form={dataSourceForm}
          databookOptions={databookOptions}
          datasheetOptions={datasheetOptions}
          dateFieldOptions={dateFieldOptions}
          emailFieldOptions={emailFieldOptions}
          quotaOptions={quotaOptions}
          teamOptions={teamOptions}
          criteriaComponents={criteriaComponents}
          commissionJoinKeys={commissionJoinKeys}
          settlementJoinKeys={settlementJoinKeys}
          handleChangeDatabook={handleChangeDatabook}
          handleChangeDatasheet={handleChangeDatasheet}
          handleChangeCriteriaComponent={handleChangeCriteriaComponent}
          handleCreateCriteria={handleCreateCriteria}
          handleMovePrevStep={handleMovePrevStep}
          checkCreateVisibility={checkCreateVisibility}
          dsVariablesLoading={dsVariablesLoading}
          forceSkippedDatasheetIds={forceSkippedDatasheetIds}
        />
      )}
    </>
  );
};
