import {
  Help<PERSON><PERSON><PERSON>Icon,
  ArrowNarrowLeftIcon,
} from "@everstage/evericons/outlined";
import { CheckCircleIcon } from "@everstage/evericons/solid";
import React from "react";
import { useTranslation } from "react-i18next";

import {
  EverButton,
  EverForm,
  EverInput,
  EverSelect,
  EverSwitch,
  EverTooltip,
  EverTg,
  EverLoader,
} from "~/v2/components";
import DatasheetOptionWithIcon from "~/v2/components/DatasheetOptionWithIcon";
import {
  CRITERIA_TYPES,
  TEAM_MORE_INFO,
} from "~/v2/features/commission-canvas-modified/constants";
import { useForceSkipModal } from "~/v2/features/datasheet/helperComponents/useForceSkipModal";

export const SelectDataSource = ({
  criteriaName,
  criteriaType,
  form,
  databookOptions,
  datasheetOptions,
  dateFieldOptions,
  emailFieldOptions,
  quotaOptions,
  teamOptions,
  criteriaComponents,
  commissionJoinKeys,
  settlementJoinKeys,
  handleChangeDatabook,
  handleChangeDatasheet,
  handleChangeCriteriaComponent,
  handleCreateCriteria,
  handleMovePrevStep,
  checkCreateVisibility,
  dsVariablesLoading,
  forceSkippedDatasheetIds = [],
}) => {
  const { t } = useTranslation();

  const CriteriaIcon = CRITERIA_TYPES[criteriaType].Icon;

  return (
    <div className="flex flex-col h-full w-full py-6 gap-8">
      <EverTg.SubHeading1 className="self-center text-ever-base-content-mid">
        Create new component
      </EverTg.SubHeading1>
      <EverForm
        id="data-source-form"
        name="data-source-form"
        form={form}
        initialValues={{ databook: null }}
        wrapperCol={{ span: 24 }}
        labelCol={{ span: 24 }}
        className="data-source-form"
      >
        <div className="flex flex-col h-full">
          <div className="flex-grow min-h-0 flex flex-col">
            <div className="flex flex-col w-[600px] self-center">
              <div className="flex items-center gap-2">
                <CheckCircleIcon className="w-4 h-4 text-ever-success" />
                <EverTg.Text className="text-ever-base-content-mid font-medium">
                  STEP 1
                </EverTg.Text>
              </div>
              <div className="mt-1 flex gap-2">
                <EverTg.Heading3 className="text-ever-base-content-mid">
                  Component name:
                </EverTg.Heading3>
                <EverTg.Heading3>{criteriaName}</EverTg.Heading3>
              </div>
              <div className="relative mt-2 w-full h-20 flex items-center gap-4 pl-4 pr-2 py-2 rounded-xl border border-solid border-ever-base-400">
                <CriteriaIcon className="w-12 h-12 flex-shrink-0" />
                <div className="flex flex-col w-[calc(100%_-_82px)]">
                  <EverTg.Heading3 className="text-ever-base-content">
                    {CRITERIA_TYPES[criteriaType].name(t)}
                  </EverTg.Heading3>
                  <EverTg.Description className="whitespace-normal">
                    {CRITERIA_TYPES[criteriaType].description(t)}
                  </EverTg.Description>
                </div>
                <CheckCircleIcon className="absolute top-2 right-2 w-[18px] h-[18px] text-ever-success" />
              </div>
            </div>
            <div className="flex-grow overflow-auto flex flex-col mt-6 mr-10">
              <div className="flex flex-col ml-[268px] w-[600px]">
                <EverTg.Text className="text-ever-base-content-mid font-medium">
                  STEP 2
                </EverTg.Text>
                <EverTg.Heading3 className="mt-1">
                  Select data source
                </EverTg.Heading3>
                <div className="mt-6">
                  {criteriaType === CRITERIA_TYPES.CustomSchedule.id ? (
                    <SelectDataSourceSettlement
                      databookOptions={databookOptions}
                      datasheetOptions={datasheetOptions}
                      dateFieldOptions={dateFieldOptions}
                      criteriaComponents={criteriaComponents}
                      commissionJoinKeys={commissionJoinKeys}
                      settlementJoinKeys={settlementJoinKeys}
                      dsVariablesLoading={dsVariablesLoading}
                      handleChangeDatabook={handleChangeDatabook}
                      handleChangeDatasheet={handleChangeDatasheet}
                      handleChangeCriteriaComponent={
                        handleChangeCriteriaComponent
                      }
                      forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                    />
                  ) : (
                    // Select data source for simple, conditional, tier and
                    // quota criteria.
                    <SelectDataSourceCriteria
                      form={form}
                      databookOptions={databookOptions}
                      datasheetOptions={datasheetOptions}
                      emailFieldOptions={emailFieldOptions}
                      dateFieldOptions={dateFieldOptions}
                      quotaOptions={quotaOptions}
                      teamOptions={teamOptions}
                      criteriaType={criteriaType}
                      dsVariablesLoading={dsVariablesLoading}
                      handleChangeDatabook={handleChangeDatabook}
                      handleChangeDatasheet={handleChangeDatasheet}
                      forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                    />
                  )}
                </div>
                <EverForm.Item
                  name="description"
                  label="Description"
                  className="mt-6 mb-0"
                >
                  <EverInput.TextArea
                    maxLength={500}
                    placeholder="Enter a description"
                  />
                </EverForm.Item>
              </div>
            </div>
            <div className="mt-10 mb-4 flex justify-between w-[600px] self-center">
              <EverButton
                type="ghost"
                color="base"
                prependIcon={
                  <ArrowNarrowLeftIcon className="w-5 h-5 text-ever-base-content-mid" />
                }
                onClick={() => handleMovePrevStep()}
              >
                Back
              </EverButton>
              <EverForm.Item shouldUpdate className="ml-auto mb-0">
                {({ getFieldsValue }) => {
                  const values = getFieldsValue(true);
                  const isVisible = checkCreateVisibility(values);
                  return (
                    <EverButton
                      className="w-28"
                      disabled={!isVisible}
                      onClick={handleCreateCriteria}
                    >
                      Create
                    </EverButton>
                  );
                }}
              </EverForm.Item>
            </div>
          </div>
        </div>
      </EverForm>
    </div>
  );
};

const filterOptionHandler = (input, option) => {
  return option.label?.toLowerCase()?.includes(input?.toLowerCase()) ?? false;
};

const SelectDataSourceCriteria = ({
  form,
  databookOptions,
  datasheetOptions,
  emailFieldOptions,
  dateFieldOptions,
  quotaOptions,
  teamOptions,
  criteriaType,
  dsVariablesLoading,
  handleChangeDatabook,
  handleChangeDatasheet,
  forceSkippedDatasheetIds = [],
}) => {
  const { t } = useTranslation();

  // Integrate useForceSkipModal
  const getDatasheetObjById = (id) =>
    datasheetOptions.find((ds) => ds.value === id);
  const { onDatasheetSelect, modal: forceSkipModal } = useForceSkipModal({
    forceSkippedDatasheetIds,
    handleDatasheetChange: handleChangeDatasheet,
    getDatasheetObjById,
  });

  return (
    <>
      {forceSkipModal}
      <EverLoader
        spinning={dsVariablesLoading}
        size={1}
        indicatorType="spinner"
      >
        <div className="flex flex-col p-0.5">
          <div className="grid grid-cols-2 gap-6">
            <EverForm.Item
              label="Databook"
              name="databook"
              rules={[{ required: true, message: "Please select databook" }]}
            >
              <EverSelect
                placeholder="Select databook"
                onChange={(databookId) => handleChangeDatabook(databookId)}
                disabled={dsVariablesLoading}
                options={databookOptions}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
            <EverForm.Item
              label="Datasheet"
              name="datasheet"
              rules={[{ required: true, message: "Please select datasheet" }]}
              shouldUpdate
            >
              <EverSelect
                placeholder="Select datasheet"
                onChange={onDatasheetSelect}
                disabled={dsVariablesLoading}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
                optionLabelProp="label"
              >
                {datasheetOptions.map((opt) => (
                  <EverSelect.Option
                    key={opt.value}
                    value={opt.value}
                    label={opt.label}
                    title={opt.label}
                  >
                    <DatasheetOptionWithIcon
                      name={opt.label}
                      id={opt.value}
                      forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                    />
                  </EverSelect.Option>
                ))}
              </EverSelect>
            </EverForm.Item>
            <EverForm.Item
              name="email"
              label="Email field"
              rules={[{ required: true, message: "Please select email field" }]}
            >
              <EverSelect
                placeholder="Choose email field"
                disabled={dsVariablesLoading}
                options={emailFieldOptions}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
            <EverForm.Item
              name="date"
              label="Date field"
              rules={[{ required: true, message: "Please select date field" }]}
            >
              <EverSelect
                placeholder="Choose date field"
                disabled={dsVariablesLoading}
                options={dateFieldOptions}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
          </div>
          {criteriaType === CRITERIA_TYPES.CustomQuota.id && (
            <EverForm.Item
              label={t("QUOTA_CATEGORY")}
              name="quota"
              rules={[
                {
                  required: true,
                  message: `Please select ${t("QUOTA_CATEGORY").toLowerCase()}`,
                },
              ]}
              className="mt-6"
            >
              <EverSelect
                placeholder={`Select ${t("QUOTA_CATEGORY").toLowerCase()}`}
                disabled={dsVariablesLoading}
                options={quotaOptions}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
          )}
          <div className="mt-6">
            <EverForm.Item
              name="isTeam"
              valuePropName="checked"
              className="[&>.ant-form-item-control>.ant-form-item-control-input]:min-h-[20px]"
            >
              <EverSwitch
                label={
                  <EverTg.Description className="inline-flex items-center">
                    Enable Manager/Team/Pod calculation
                    <EverTooltip title={TEAM_MORE_INFO}>
                      <HelpCircleIcon className="w-4 h-4 ml-2" />
                    </EverTooltip>
                  </EverTg.Description>
                }
                disabled={dsVariablesLoading}
                onChange={() => form.setFieldsValue({ teamType: null })}
              />
            </EverForm.Item>
            <EverForm.Item label="" dependencies={["isTeam"]} noStyle>
              {({ getFieldValue }) => {
                const isTeam = getFieldValue("isTeam");
                return isTeam ? (
                  <EverForm.Item name="teamType" noStyle>
                    <EverSelect
                      className="mt-2"
                      placeholder="Choose team type"
                      disabled={dsVariablesLoading}
                      options={teamOptions}
                      showSearch
                      filterOption={filterOptionHandler}
                      dropdownMatchSelectWidth
                    />
                  </EverForm.Item>
                ) : null;
              }}
            </EverForm.Item>
          </div>
        </div>
      </EverLoader>
    </>
  );
};

const SelectDataSourceSettlement = ({
  databookOptions,
  datasheetOptions,
  dateFieldOptions,
  criteriaComponents,
  commissionJoinKeys,
  settlementJoinKeys,
  dsVariablesLoading,
  handleChangeDatabook,
  handleChangeDatasheet,
  handleChangeCriteriaComponent,
  forceSkippedDatasheetIds = [],
}) => {
  const { t } = useTranslation();

  // Integrate useForceSkipModal for settlement datasheet dropdown
  const getDatasheetObjById = (id) =>
    datasheetOptions.find((ds) => ds.value === id);
  const { onDatasheetSelect, modal: forceSkipModal } = useForceSkipModal({
    forceSkippedDatasheetIds,
    handleDatasheetChange: handleChangeDatasheet,
    getDatasheetObjById,
  });

  return (
    <>
      {forceSkipModal}
      <EverLoader
        spinning={dsVariablesLoading}
        size={1}
        indicatorType="spinner"
      >
        <div className="flex flex-col gap-6 p-0.5">
          <EverForm.Item
            label="Select Component(s)"
            name="criteriaComponents"
            rules={[{ required: true, message: "Please select component" }]}
          >
            <EverSelect
              placeholder="Select component(s)"
              mode="multiple"
              className="w-full h-full"
              maxTagCount={false}
              disabled={dsVariablesLoading}
              onChange={(components) =>
                handleChangeCriteriaComponent(components)
              }
              options={criteriaComponents}
              filterOption={filterOptionHandler}
              dropdownMatchSelectWidth
            />
          </EverForm.Item>
          <EverTg.Heading3>{`Trigger ${t("COMMISSION_LOWERCASE")} ${t(
            "PAYOUTS_LOWERCASE"
          )} using this data`}</EverTg.Heading3>
          <div className="grid grid-cols-3 gap-6">
            <EverForm.Item
              label="Databook"
              name="databook"
              rules={[
                {
                  required: true,
                  message: "Please select databook for invoice data",
                },
              ]}
            >
              <EverSelect
                placeholder="Select databook"
                disabled={dsVariablesLoading}
                onChange={(databookId) => handleChangeDatabook(databookId)}
                options={databookOptions}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
            <EverForm.Item
              label="Datasheet"
              name="datasheet"
              rules={[{ required: true, message: "Please select datasheet" }]}
              shouldUpdate
            >
              <EverSelect
                placeholder="Select datasheet"
                disabled={dsVariablesLoading}
                onChange={onDatasheetSelect}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
                optionLabelProp="label"
              >
                {datasheetOptions.map((opt) => (
                  <EverSelect.Option
                    key={opt.value}
                    value={opt.value}
                    label={opt.label}
                    title={opt.label}
                  >
                    <DatasheetOptionWithIcon
                      name={opt.label}
                      id={opt.value}
                      forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                    />
                  </EverSelect.Option>
                ))}
              </EverSelect>
            </EverForm.Item>
            <EverForm.Item
              name="date"
              label={`Trigger ${t("PAYOUTS_LOWERCASE")} based on`}
              rules={[
                {
                  required: true,
                  message: "Please select invoice paid date field",
                },
              ]}
            >
              <EverSelect
                placeholder="Select invoice paid date field"
                disabled={dsVariablesLoading}
                options={dateFieldOptions}
                showSearch
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
          </div>
          <EverTg.Heading3>
            Specify the columns to join the two datasets
          </EverTg.Heading3>
          <div className="grid grid-cols-2 gap-6">
            <EverForm.Item
              label={`For ${t("COMMISSION_LOWERCASE")} component(s)`}
              name="commissionJoinKeys"
              rules={[{ required: true, message: "Please select join keys" }]}
              shouldUpdate
            >
              <EverSelect
                placeholder="Select join keys"
                mode="multiple"
                className="h-full"
                disabled={dsVariablesLoading}
                maxTagCount={100}
                options={commissionJoinKeys}
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
            <EverForm.Item
              label={`For triggering ${t("PAYOUTS_LOWERCASE")}`}
              name="settlementJoinKeys"
              rules={[
                {
                  required: true,
                  message: "Please select join keys",
                },
              ]}
            >
              <EverSelect
                placeholder="Select join keys"
                mode="multiple"
                className="h-full"
                disabled={dsVariablesLoading}
                maxTagCount={100}
                options={settlementJoinKeys}
                filterOption={filterOptionHandler}
                dropdownMatchSelectWidth
              />
            </EverForm.Item>
          </div>
        </div>
      </EverLoader>
    </>
  );
};
