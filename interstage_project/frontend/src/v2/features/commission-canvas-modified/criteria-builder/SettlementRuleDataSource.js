import { XCloseIcon } from "@everstage/evericons/outlined";
import { has, includes, intersection, isEmpty, map } from "lodash";
import { observer } from "mobx-react";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import {
  DATATYPE,
  DATA_ORIGIN,
  DATA_TYPES_TO_BE_IGNORED_IN_MODULE,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { useDSVariableStore } from "~/GlobalStores/DSVariableStore";
import { getDatabooksForDataSourceConfiguration } from "~/Utils/canvasPlansUtils";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverLabel,
  EverLoader,
  EverModal,
  EverSelect,
  EverTg,
  IconButton,
} from "~/v2/components";
import DatasheetOptionWithIcon from "~/v2/components/DatasheetOptionWithIcon";
import { useForceSkipModal } from "~/v2/features/datasheet/helperComponents/useForceSkipModal";
import { getForceSkippedDatasheetIds } from "~/v2/features/datasheet/restApi";

function joinKeyOptionsTransformer(allFields) {
  return allFields
    .filter(
      (field) =>
        !DATA_TYPES_TO_BE_IGNORED_IN_MODULE.CRITERIA_DESIGNER.includes(
          field.dataType
        )
    )
    .map((field) => ({
      label: field?.name,
      value: field?.meta?.systemName,
      dataType: field?.dataType,
    }));
}

const SettlementRuleDataSource = observer(
  ({
    className,
    criteriaW,
    criteriaO,
    planCriteriasW,
    handleCloseModal,
    onCriteriaDatasourceChange,
    isDraft,
    isUnderReview,
    canEdit,
  }) => {
    const [firstSelectedCriteriaId, setFirstSelectedCriteriaId] = useState(
      criteriaW.criteriaIds?.[0]
    );
    const { sortedDatabooks, getSortedDatasheetsByOrigin } = useDatabookStore();

    const { t } = useTranslation();
    const myClient = useRecoilValue(myClientAtom);
    const clientFeatures = getClientFeatures(myClient);

    // Feature flag for force-skip datasheet validation
    const isDsSkipValidationEnabled =
      clientFeatures?.isDsSkipValidationEnabled || false;

    // criteria list with line item level true
    const criteriaList = useMemo(
      () =>
        planCriteriasW.filter((criteria) => {
          return criteria.data?.isLineItemLevel === true;
        }),
      [planCriteriasW]
    );

    // get datasheet ids for which we need to fetch the variables
    const datasheetIdsForDsVariables = useMemo(() => {
      const dsIds = new Set();
      for (const criteria of criteriaList) {
        const dsId = criteria?.data?.datasheetId;
        if (dsId) dsIds.add(dsId);
      }
      if (criteriaW?.datasheetId) {
        dsIds.add(criteriaW.datasheetId);
      }
      return [...dsIds];
    }, [criteriaW.datasheetId, criteriaList]);

    // use getSortedDatasheetVariableTokens to fetch the variables
    const { getSortedDatasheetVariableTokens, loading } = useDSVariableStore(
      datasheetIdsForDsVariables
    );

    // Fetch forceSkippedDatasheetIds using React Query
    const { accessToken } = useAuthStore();
    const { data: forceSkipData } = useReactQuery(
      ["forceSkippedDatasheetIds", accessToken],
      () => getForceSkippedDatasheetIds(accessToken),
      {
        enabled: !!accessToken && isDsSkipValidationEnabled,
      }
    );
    let forceSkippedDatasheetIds = [];
    if (isDsSkipValidationEnabled && forceSkipData?.skipped_datasheet_ids) {
      forceSkippedDatasheetIds =
        forceSkipData.skipped_datasheet_ids.map(String);
    }

    // utility functions
    function shouldDisableCriteria(criteria) {
      /* we can only select the criterias for which the databook and datasheet
         id is same as that of the first selected criteria */
      const firstSelectedCriteria = criteriaList.find(
        (c) => c?.id === firstSelectedCriteriaId
      );
      if (!firstSelectedCriteria) return false;
      const sameDatabookId =
        has(criteria, ["data", "databookId"]) &&
        criteria?.data?.databookId === firstSelectedCriteria?.data?.databookId;
      const sameDatasheetId =
        has(criteria, ["data", "datasheetId"]) &&
        criteria?.data?.datasheetId ===
          firstSelectedCriteria?.data?.datasheetId;
      return !sameDatabookId || !sameDatasheetId;
    }

    // options to select components
    const [commissionJoinKeyOptions, commissionJoinKeyToDataTypeMap] =
      useMemo(() => {
        if (loading || !firstSelectedCriteriaId) {
          return [];
        }
        const firstSelectedCriteria = criteriaList.find(
          (c) => c?.id === firstSelectedCriteriaId
        );
        if (!firstSelectedCriteria) {
          return [];
        }
        const _criteriaFields = getSortedDatasheetVariableTokens(
          firstSelectedCriteria?.data?.datasheetId
        );
        const keyOptions = joinKeyOptionsTransformer(_criteriaFields);
        const keyToDataTypeMap = keyOptions.reduce((acc, obj) => {
          acc[obj.value] = obj.dataType;
          return acc;
        }, {});
        return [keyOptions, keyToDataTypeMap];
      }, [loading, firstSelectedCriteriaId, criteriaList]);

    const datasheetTokens = getSortedDatasheetVariableTokens(
      criteriaW.datasheetId
    );

    const criteriaOptions = criteriaList.map((criteria) => ({
      label: criteria?.name,
      value: criteria?.id,
      disabled: shouldDisableCriteria(criteria),
    }));

    const settlementJoinKeysOptions =
      joinKeyOptionsTransformer(datasheetTokens);
    const settlementJoinKeyToDataTypeMap = settlementJoinKeysOptions.reduce(
      (acc, obj) => {
        acc[obj.value] = obj.dataType;
        return acc;
      },
      {}
    );

    const dateFieldOptions = datasheetTokens
      .filter((field) => field?.dataType === DATATYPE.DATE)
      .map((field) => ({
        label: field?.name,
        value: field?.meta?.systemName,
      }));

    const datasheetOptions = criteriaW?.databookId
      ? getSortedDatasheetsByOrigin(criteriaW.databookId, [
          DATA_ORIGIN.CUSTOM_OBJECT,
          DATA_ORIGIN.SYSTEM_OBJECT,
        ]).map((datasheet) => ({
          label: datasheet?.name,
          value: String(datasheet?.datasheetId),
        }))
      : [];

    const databookOptions = getDatabooksForDataSourceConfiguration(
      sortedDatabooks,
      criteriaO.databookId
    ).map((databook) => ({
      label: databook?.name,
      value: databook?.databookId,
    }));

    // values to select components
    const criteriaIds = useMemo(
      () => intersection(map(criteriaList, "id"), criteriaW.criteriaIds),
      [criteriaList, criteriaW.criteriaIds]
    );

    const commissionJoinKeys = criteriaW.commissionJoinKeys || [];

    const databookId =
      includes(map(sortedDatabooks, "databookId"), criteriaW.databookId) &&
      criteriaW.databookId;

    const datasheetId = useMemo(
      () =>
        databookId &&
        includes(
          map(
            getSortedDatasheetsByOrigin(databookId, [
              DATA_ORIGIN.CUSTOM_OBJECT,
              DATA_ORIGIN.SYSTEM_OBJECT,
            ]),
            "datasheetId"
          ),
          criteriaW.datasheetId
        ) &&
        criteriaW.datasheetId,
      [databookId, criteriaW.datasheetId]
    );

    const dateField =
      includes(map(datasheetTokens, "meta.systemName"), criteriaW.dateField) &&
      criteriaW.dateField;

    const settlementJoinKeys = intersection(
      criteriaW.settlementJoinKeys,
      map(datasheetTokens, "meta.systemName")
    );

    const areJoinKeysDataTypesAligned = (
      commissionJoinKeys,
      settlementJoinKeys
    ) => {
      const commissionJoinKeysDataTypes = commissionJoinKeys.map(
        (key) => commissionJoinKeyToDataTypeMap[key]
      );
      const settlementJoinKeysDataTypes = settlementJoinKeys.map(
        (key) => settlementJoinKeyToDataTypeMap[key]
      );
      return commissionJoinKeysDataTypes.every(
        (_, index) =>
          commissionJoinKeysDataTypes[index] ===
          settlementJoinKeysDataTypes[index]
      );
    };

    const getDataSourceValidity = (newValues) => {
      const oldValues = {
        criteriaIds: criteriaW.criteriaIds,
        databookId: criteriaW.databookId,
        datasheetId: criteriaW.datasheetId,
        dateField: criteriaW.dateField,
        commissionJoinKeys: criteriaW.commissionJoinKeys,
        settlementJoinKeys: criteriaW.settlementJoinKeys,
      };

      const mergedValues = { ...oldValues, ...newValues };
      return (
        Object.values(mergedValues).every((v) => !isEmpty(v)) &&
        mergedValues.commissionJoinKeys.length ===
          mergedValues.settlementJoinKeys.length &&
        areJoinKeysDataTypesAligned(
          mergedValues.commissionJoinKeys,
          mergedValues.settlementJoinKeys
        )
      );
    };

    const handleDatabookChange = (v) => {
      const previousDatasheetId = criteriaW.datasheetId;
      if (previousDatasheetId) {
        // show warning modal if the databook is changed
        EverModal.confirm({
          width: 540,
          title: "Data source change erases the imported formula",
          subtitle:
            "If you change the data source, the entire imported formula will be lost, and you must begin again. Are you sure you want to proceed?",
          okText: "Confirm",
          cancelText: "Discard",
          centered: true,
          onOk: () => {
            onCriteriaDatasourceChange({
              databookId: v,
              datasheetId: null,
              dateField: null,
              settlementJoinKeys: [],
              isValid: false,
            });
          },
        });
      } else {
        onCriteriaDatasourceChange({
          databookId: v,
          datasheetId: null,
          dateField: null,
          settlementJoinKeys: [],
          isValid: false,
        });
      }
    };

    const handleDatasheetChange = (v) => {
      const previousDatasheetId = criteriaW.datasheetId;
      if (previousDatasheetId) {
        // show warning modal if the datasheet is changed
        EverModal.confirm({
          width: 540,
          title: "Data source change erases the imported formula",
          subtitle:
            "If you change the data source, the entire imported formula will be lost, and you must begin again. Are you sure you want to proceed?",
          okText: "Confirm",
          cancelText: "Discard",
          centered: true,
          onOk: () => {
            onCriteriaDatasourceChange({
              datasheetId: v,
              dateField: null,
              settlementJoinKeys: [],
              isValid: false,
            });
          },
        });
      } else {
        onCriteriaDatasourceChange({
          datasheetId: v,
          dateField: null,
          settlementJoinKeys: [],
          isValid: false,
        });
      }
    };

    const getDatasheetObjById = (id) =>
      datasheetOptions.find((ds) => ds.value === id);
    const { onDatasheetSelect, modal: forceSkipModal } = useForceSkipModal({
      forceSkippedDatasheetIds,
      handleDatasheetChange,
      getDatasheetObjById,
    });

    const isNotEditable = !isDraft || !canEdit;

    return (
      <div
        className={twMerge(
          className,
          "overflow-auto absolute my-2 right-2 top-[60px] w-80 bg-ever-base-25 border border-solid border-ever-base-300 rounded-xl py-6 shadow-sm",
          "z-10 h-[calc(100%-132px)]" // 60 + 60 + 6 (header + footer + some extra space)
        )}
      >
        <IconButton
          onClick={handleCloseModal}
          icon={<XCloseIcon />}
          className="absolute top-2 right-2 z-10"
          type="text"
          color="base"
        />
        <EverLoader spinning={loading} size={2} indicatorType="spinner">
          <div className="flex flex-col gap-4 px-6">
            <div className="flex flex-col gap-4">
              <EverTg.Heading3>Data source</EverTg.Heading3>
              <EverTg.SubHeading4 className="text-ever-base-content">
                {`Defer payouts for these ${t(
                  "COMMISSION_LOWERCASE"
                )} component(s)`}
              </EverTg.SubHeading4>
            </div>
            <div className="flex flex-col gap-2">
              <EverLabel required>Select Component(s)</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                mode="multiple"
                className="w-full h-full"
                maxTagCount={false}
                value={criteriaIds}
                disabled={loading || isNotEditable || isUnderReview}
                options={criteriaOptions}
                onChange={(selectedCriterias) => {
                  const dataToBeMerged = {};
                  if (selectedCriterias.length === 0) {
                    setFirstSelectedCriteriaId(null);
                    dataToBeMerged.commissionJoinKeys = [];
                  } else if (selectedCriterias.length === 1) {
                    setFirstSelectedCriteriaId(selectedCriterias[0]);
                  }

                  const dataSourceChanges = {
                    criteriaIds: selectedCriterias,
                    ...dataToBeMerged,
                  };
                  onCriteriaDatasourceChange({
                    ...dataSourceChanges,
                    isValid: getDataSourceValidity(dataSourceChanges),
                  });
                }}
              />
            </div>
            <EverTg.SubHeading4 className="text-ever-base-content">{`Trigger ${t(
              "COMMISSION_LOWERCASE"
            )} ${t("PAYOUTS_LOWERCASE")} using this data`}</EverTg.SubHeading4>
            <div className="flex flex-col gap-2">
              <EverLabel required>Databook</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                value={databookId}
                disabled={loading || isNotEditable || isUnderReview}
                options={databookOptions}
                onChange={(v) => {
                  handleDatabookChange(v);
                }}
              />
            </div>
            <div className="flex flex-col gap-2">
              <EverLabel required>Datasheet</EverLabel>
              {forceSkipModal}
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                value={datasheetId}
                disabled={loading || isNotEditable || isUnderReview}
                onChange={onDatasheetSelect}
                optionLabelProp="label"
              >
                {datasheetOptions.map((opt) => (
                  <EverSelect.Option
                    key={opt.value}
                    value={opt.value}
                    label={opt.label}
                    title={opt.label}
                  >
                    <DatasheetOptionWithIcon
                      name={opt.label}
                      id={opt.value}
                      forceSkippedDatasheetIds={forceSkippedDatasheetIds}
                    />
                  </EverSelect.Option>
                ))}
              </EverSelect>
            </div>
            <div className="flex flex-col gap-2">
              <EverLabel required>{`Trigger ${t(
                "PAYOUTS_LOWERCASE"
              )} based on`}</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                value={dateField}
                disabled={loading || isNotEditable || isUnderReview}
                options={dateFieldOptions}
                onChange={(v) => {
                  onCriteriaDatasourceChange({
                    dateField: v,
                    isValid: getDataSourceValidity({ dateField: v }),
                  });
                }}
              />
            </div>
            <EverTg.SubHeading4 className="text-ever-base-content">
              Specify the columns to join the two datasets
            </EverTg.SubHeading4>
            <div className="flex flex-col gap-2">
              <EverLabel required>{`For ${t(
                "COMMISSION_LOWERCASE"
              )} component(s)`}</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                mode="multiple"
                className="w-full h-full"
                maxTagCount={100}
                value={commissionJoinKeys}
                disabled={loading || isNotEditable || isUnderReview}
                options={commissionJoinKeyOptions}
                onChange={(selectedFields) => {
                  onCriteriaDatasourceChange({
                    commissionJoinKeys: selectedFields,
                    isValid: getDataSourceValidity({
                      commissionJoinKeys: selectedFields,
                    }),
                  });
                }}
              />
            </div>
            <div className="flex flex-col gap-2">
              <EverLabel required>{`For triggering ${t(
                "PAYOUTS_LOWERCASE"
              )}`}</EverLabel>
              <EverSelect
                showSearch
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                mode="multiple"
                className="w-full h-full"
                maxTagCount={100}
                value={settlementJoinKeys}
                disabled={loading || isNotEditable || isUnderReview}
                options={settlementJoinKeysOptions}
                onChange={(selectedFields) => {
                  onCriteriaDatasourceChange({
                    settlementJoinKeys: selectedFields,
                    isValid: getDataSourceValidity({
                      settlementJoinKeys: selectedFields,
                    }),
                  });
                }}
              />
            </div>
          </div>
        </EverLoader>
      </div>
    );
  }
);

export default SettlementRuleDataSource;
