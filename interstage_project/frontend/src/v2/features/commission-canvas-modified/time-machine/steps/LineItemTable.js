import {
  ColumnsIcon,
  ArrowNarrowLeftIcon,
} from "@everstage/evericons/outlined";
import { UploadIcon } from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { isEmpty, isNil } from "lodash";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

import { useVariableStore } from "~/GlobalStores/VariableStore";
import { numComparator } from "~/Utils/agGridUtils";
import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import { formatDateDDMMMYYYY } from "~/Utils/DateUtils";
import {
  EverButton,
  EverLoader,
  EverTg,
  EverTooltip,
  IconButton,
  message,
} from "~/v2/components";
import { CustomHeader, DynamicPagination } from "~/v2/components/ag-grid";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import StatementColumns from "~/v2/features/commission-canvas-modified/criteria-builder/StatementColumns";
import { getCurrencyValue } from "~/v2/features/commissions/constants";

const DATA_TYPE_FOR_DEFAULT_COLUMNS = {
  tier_name: "String",
  original_tier_name: "String",
  tier_value: "Integer",
  quota_erosion: "Integer",
  commission: "Integer",
};

export default function LineItemTable({
  simulatedPayeeData,
  handleTimeMachineStepChange,
  systemNameToDisplayNameMap,
  t,
  generateSimulatedRecordsColumnMap,
  dsOptionsMap,
  dsSystemNameToDisplayNameMap,
  getCommissionClasses,
  myClient,
  commissionPlanDetails,
  simulatedRecords,
  configureColumnsModalOpen,
  shouldReconstructColumnDefs,
  handleConfigureModalOpenChange,
  sortCallback,
  lineItemTableSortingUtils,
  exportSimulatedRecordsAsCsvHandler,
  handleSaveTimeMachineColumns,
  lineItemTableLoading,
  fetchSimulatedRecords,
  fetchSimulatedRecordsCount,
  simulatedRecordsCount,
  simulationData,
  selectedCriteriaInTimeMachine,
}) {
  const { dataTypesById } = useVariableStore();
  const gridRef = useRef(null);
  const [columnDefs, setColumnDefs] = useState([]);
  const {
    sortByField,
    sortOrder,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
  } = lineItemTableSortingUtils;

  const rowData = useMemo(() => {
    const rows = simulatedRecords?.data || [];
    const columns = simulatedRecords?.columns || [];

    return rows.map((row, index) => {
      const rowObj = {};
      for (const [index, column] of columns.entries()) {
        rowObj[column] = row[index];
      }
      rowObj.rowNumber = index + 1 + (currentPage - 1) * pageSize;
      return rowObj;
    });
  }, [simulatedRecords, currentPage, pageSize]);

  const getColumnDefs = () => {
    const columns = simulatedRecords?.columns || [];
    if (isEmpty(columns)) return [];

    //  it is a snake case column system name to display name map
    const columnsMap = generateSimulatedRecordsColumnMap(
      simulationData,
      systemNameToDisplayNameMap,
      t
    );

    const colDefs = [
      {
        suppressMenu: true,
        field: "rowNumber",
        headerName: "#",
        suppressColumnsToolPanel: true,
        suppressFiltersToolPanel: true,
        width: 80,
        maxWidth: 80,
        minWidth: 80,
        suppressSizeToFit: true,
        cellStyle: { paddingRight: 0 },
        cellClass: "!w-36",
        cellRenderer: (params) => {
          return (
            <EverTg.Heading4>
              {params.value === undefined
                ? t("TOTAL_COMMISSION")
                : params.value}
            </EverTg.Heading4>
          );
        },
      },
    ];

    for (const column of columns) {
      const columnDetail =
        dsOptionsMap?.[dsSystemNameToDisplayNameMap?.[column]];

      // data type
      const dataType =
        DATA_TYPE_FOR_DEFAULT_COLUMNS[column] ||
        dataTypesById?.[columnDetail?.meta?.dataTypeId] ||
        "String";

      const isDate = dataType === "Date";
      const isNumber = ["Integer", "Percentage"].includes(dataType);
      const isBoolean = dataType === "Boolean";

      colDefs.push({
        field: column,
        headerName: columnsMap[column],
        headerComponentParams: {
          menuIcon: dataType,
          enableSorting: false,
          serverSideSortable: true,
          sortOrder,
          sortByField,
          sortCallback,
        },
        valueFormatter: (params) => {
          if (!isNil(params.value)) {
            const val = params.value.toString();
            return isDate ? formatDateDDMMMYYYY(val) : val;
          }
          return "-";
        },
        ...(isBoolean && {
          cellDataType: false,
        }),
        ...((isNumber ||
          column === "quota_erosion" ||
          column === "tier_value") && {
          type: "rightAligned",
          comparator: numComparator,
          valueFormatter: (params) => {
            return formatCurrencyWrapper(params.value);
          },
          cellRenderer: (params) => (
            <EverTooltip
              title={formatCurrencyWrapper(params.value, {
                decimalPlaces: 6,
                truncate: true,
              })}
              placement="bottom"
              overlayClassName="roundedTooltip"
            >
              {DATA_TYPE_FOR_DEFAULT_COLUMNS[column] ? (
                <EverTg.SubHeading4 className="text-ever-base-content">
                  {params.valueFormatted}
                </EverTg.SubHeading4>
              ) : (
                <EverTg.Text className="text-ever-base-content">
                  {params.valueFormatted}
                </EverTg.Text>
              )}
            </EverTooltip>
          ),
        }),
        ...((column === "tier_name" || column === "original_tier_name") && {
          cellRenderer: (params) => (
            <EverTg.SubHeading4 className="text-ever-base-content">
              {params.valueFormatted}
            </EverTg.SubHeading4>
          ),
        }),
        ...(column === "commission" && {
          type: "rightAligned",
          pinned: "right",
          aggFunc: "sum",
          valueFormatter: (params) => {
            return getCurrencyValue(myClient.baseCurrencySymbol, params.value);
          },
          cellRenderer: (params) => {
            if (isNaN(params.value)) {
              return (
                <EverTg.SubHeading4 className="text-right w-full blur-[3px]">
                  000000
                </EverTg.SubHeading4>
              );
            }

            return (
              <EverTg.SubHeading4
                className={twMerge(
                  "text-right",
                  getCommissionClasses(params.value)
                )}
              >
                {params.valueFormatted}
              </EverTg.SubHeading4>
            );
          },
        }),
      });
    }
    return colDefs;
  };

  useEffect(() => {
    if (isEmpty(columnDefs) || shouldReconstructColumnDefs.current) {
      setColumnDefs(getColumnDefs());
      shouldReconstructColumnDefs.current = false;
    }
  }, [simulatedRecords, shouldReconstructColumnDefs]);

  useEffect(() => {
    shouldReconstructColumnDefs.current = true;
  }, [sortByField, sortOrder]);

  useEffect(() => {
    try {
      fetchSimulatedRecords();
      fetchSimulatedRecordsCount();
    } catch (error) {
      console.log("Error fetching simulated records", error);
      message.error("Unable to fetch data. Please try re-simulating.");
    }
  }, [pageSize, currentPage, sortByField, sortOrder]);

  return (
    <div className="flex flex-col w-full h-full gap-4">
      <div className="flex justify-between">
        <EverButton
          prependIcon={
            <ArrowNarrowLeftIcon className="text-ever-base-content-mid" />
          }
          type="text"
          color="base"
          onClick={() => handleTimeMachineStepChange(2)}
        >
          Back
        </EverButton>
        <div className="flex items-center gap-2 translate-x-14">
          <EverTg.SubHeading3 className="text-ever-base-content">
            {selectedCriteriaInTimeMachine.name}
          </EverTg.SubHeading3>
          <EverTg.SubHeading3 className="text-ever-base-content">
            /
          </EverTg.SubHeading3>
          <EverTg.SubHeading3>
            {simulatedPayeeData.employeeName}
          </EverTg.SubHeading3>
        </div>
        <div className="flex">
          <IconButton
            color="base"
            icon={<ColumnsIcon className="text-ever-base-content-mid" />}
            size="medium"
            type="ghost"
            className="ml-auto"
            onClick={() => handleConfigureModalOpenChange(true)}
          />
          <EverButton
            type="ghost"
            color="base"
            className="ml-4"
            disabled={rowData.length === 0}
            icon={<UploadIcon className="text-ever-base-content-mid w-5 h-5" />}
            onClick={() =>
              exportSimulatedRecordsAsCsvHandler({
                payeeEmailId: simulatedPayeeData?.employeeEmailId,
                payeeName: simulatedPayeeData?.employeeName,
                t: t,
              })
            }
          >
            Export as .CSV
          </EverButton>
        </div>
      </div>
      <div className="flex flex-col h-full flex-auto ag-theme-material no-border zebra-grid w-full">
        {lineItemTableLoading ? (
          <div className="h-full flex justify-center items-center">
            <EverLoader indicatorType="spinner" />
          </div>
        ) : (
          <>
            <AgGridReact
              ref={gridRef}
              {...getDefaultOptions({ type: "md" })}
              rowHeight={40}
              headerHeight={58}
              domLayout={
                rowData.length > 0 && rowData.length < 12
                  ? "autoHeight"
                  : "normal"
              }
              columnDefs={columnDefs}
              rowData={rowData}
              components={{ agColumnHeader: CustomHeader }}
              suppressColumnVirtualisation={true}
              paginationPageSize={pageSize}
            />
            {rowData.length > 0 && (
              <div className="w-full bg-ever-base-200 !h-12 flex justify-between px-6 items-center border-t-0 border-r-0 border-b border-l-0 border-solid border-ever-base-400">
                <EverTg.Heading4>{t("TOTAL_COMMISSION")}</EverTg.Heading4>
                {isNaN(simulatedPayeeData.commission) ? (
                  <EverTg.SubHeading4 className="blur-[3px]">
                    000000
                  </EverTg.SubHeading4>
                ) : (
                  <EverTg.SubHeading4
                    className={twMerge(
                      "text-right",
                      getCommissionClasses(simulatedPayeeData.commission)
                    )}
                  >
                    {getCurrencyValue(
                      myClient.baseCurrencySymbol,
                      simulatedPayeeData.commission
                    )}
                  </EverTg.SubHeading4>
                )}
              </div>
            )}
          </>
        )}
        {rowData.length > 0 && (
          <DynamicPagination
            minHeight="50px"
            rowPerPageOption={[10, 20, 50, 100]}
            pageCount={Math.ceil(simulatedRecordsCount / pageSize)}
            pageSize={pageSize}
            totalRows={simulatedRecordsCount}
            setPageSize={setPageSize}
            currentPage={currentPage - 1}
            setCurrentPage={setCurrentPage}
            gridRef={gridRef}
          />
        )}
      </div>
      <StatementColumns
        component="Simulate"
        visible={configureColumnsModalOpen}
        commissionPlanDetails={commissionPlanDetails}
        handleClose={() => handleConfigureModalOpenChange(false)}
        handleApply={handleSaveTimeMachineColumns}
        criteriaType={selectedCriteriaInTimeMachine?.type}
        criteriaId={selectedCriteriaInTimeMachine?.id}
        criteriaW={selectedCriteriaInTimeMachine}
      />
    </div>
  );
}
