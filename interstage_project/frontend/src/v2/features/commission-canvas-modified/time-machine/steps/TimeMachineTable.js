import { DownloadIcon, HelpCircleIcon } from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { isEmpty } from "lodash";
import moment from "moment";
import React, { useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import {
  EverGroupAvatar,
  EverSwitch,
  EverTg,
  EverTooltip,
  IconButton,
  message,
} from "~/v2/components";
import { CustomHeader } from "~/v2/components/ag-grid";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import { getCurrencyValue } from "~/v2/features/commissions/constants";
import ProfileDrawer from "~/v2/features/statements/profile-drawer";

export default function TimeMachineTable({
  simulationData,
  commissionPlanDetails,
  selectedCriteriaInTimeMachine,
  myClient,
  selectedDateRange,
  isDoNothingUsed,
  exportSimulatedRecordsAsCsvHandler,
  handleSimulatedPayeeDataChange,
  handleTimeMachineStepChange,
  getCommissionClasses,
  t,
  handleDoNothingSwitchToggle,
}) {
  const gridRef = useRef();

  const isValidResultAvailable =
    !isEmpty(simulationData) &&
    !isEmpty(selectedCriteriaInTimeMachine?.data?.datasheetId) &&
    !isEmpty(selectedCriteriaInTimeMachine?.data.type);
  let simulateColumns, criteriaType;
  if (isValidResultAvailable) {
    simulateColumns = selectedCriteriaInTimeMachine?.simulateColumns;
    criteriaType = selectedCriteriaInTimeMachine?.data.type;
  }

  const [payeeProfileDrawerVisibility, setPayeeProfileDrawerVisibility] =
    useState(false);
  const [selectedPayee, setSelectedPayee] = useState("");
  const columnDefs = useRef(null);
  if (columnDefs.current == null) columnDefs.current = constructColumnDefs();

  function constructColumnDefs() {
    if (!isValidResultAvailable) {
      console.log("Building columns failed");
      message.error({
        content: "Could not perform evaluation",
        duration: 1500,
      });
      handleTimeMachineStepChange(0);
      return [];
    }

    const colDefs = [
      {
        field: "rowNumbers",
        headerName: "#",
        lockPosition: "left",
        resizable: false,
        suppressMenu: true,
        suppressColumnsToolPanel: true,
        suppressFiltersToolPanel: true,
        valueGetter: (params) => {
          return params.node.childIndex + 1;
        },
        width: 80,
        maxWidth: 80,
        minWidth: 80,
        suppressSizeToFit: true,
        cellStyle: { paddingRight: 0 },
        cellRenderer: (params) =>
          Number.isNaN(params.value) ? (
            <EverTg.Heading4>&nbsp;Total</EverTg.Heading4>
          ) : (
            <EverTg.SubHeading4 className="text-ever-base-content">
              {params.value}
            </EverTg.SubHeading4>
          ),
      },
      {
        headerName: "Users",
        headerComponentParams: {
          menuIcon: "String",
        },
        flex: 1,
        lockPosition: "left",
        valueGetter: (params) => {
          return {
            name: params?.data?.employeeName,
            profilePicture: params?.data?.profilePicture,
            email: params?.data?.employeeEmailId,
          };
        },
        cellRenderer: (props) => {
          return props.value.name === null ? null : (
            <>
              <EverTooltip
                title="Show Profile"
                overlayClassName="roundedTooltip"
                placement="left"
              >
                <div
                  className="text-base flex items-center cursor-pointer"
                  onClick={() => {
                    setSelectedPayee(props.value.email);
                    setPayeeProfileDrawerVisibility(true);
                  }}
                >
                  <EverGroupAvatar
                    avatars={[
                      {
                        name: props.value.name,
                        image: props.value.profilePicture,
                      },
                    ]}
                  />
                </div>
              </EverTooltip>
              <div
                className="ml-3"
                onClick={() => {
                  handleSimulatedPayeeDataChange(props.data);
                  handleTimeMachineStepChange(3);
                }}
              >
                <EverTg.SubHeading4 className="underline cursor-pointer text-ever-primary">
                  {props.value.name}
                </EverTg.SubHeading4>
              </div>
            </>
          );
        },
      },
    ];

    // prepare columns to be displayed
    const columns = simulateColumns.filter((col) =>
      ["quotaErosion", "tierName", "commission"].includes(col)
    );
    // append tierValue if tier or custom tier; as it is always displayed
    if (["Tier", "CustomTier"].includes(criteriaType)) {
      columns.unshift("tierValue");
    }
    // tierName to be shown at parent grid only if summation level
    if (
      columns.includes("tierName") &&
      selectedCriteriaInTimeMachine.data.isLineItemLevel
    ) {
      columns.splice(columns.indexOf("tierName"), 1);
    }

    for (const col of columns) {
      switch (col) {
        case "commission": {
          colDefs.push({
            minWidth: 150,
            width: 150,
            headerName: t("COMMISSIONS"),
            headerComponentParams: {
              menuIcon: "Integer",
            },
            field: "commission",
            type: "rightAligned",
            lockPosition: "right",
            aggFunc: "sum",
            valueParser: "Number(newValue)",
            valueFormatter: (params) => {
              return getCurrencyValue(
                myClient.baseCurrencySymbol,
                params.value
              );
            },
            cellRenderer: (params) => {
              if (isNaN(params.value)) {
                return (
                  <EverTg.SubHeading4 className="text-right w-full blur-[3px]">
                    000000
                  </EverTg.SubHeading4>
                );
              }
              return (
                <EverTg.SubHeading4
                  className={twMerge(
                    getCommissionClasses(params.value),
                    "text-right w-full"
                  )}
                >
                  {getCurrencyValue(myClient.baseCurrencySymbol, params.value)}
                </EverTg.SubHeading4>
              );
            },
            cellClass: () => twMerge("text-right"),
          });
          break;
        }
        case "tierValue": {
          colDefs.push({
            minWidth: 120,
            width: 120,
            headerName: "Tier Value",
            headerComponentParams: {
              menuIcon: "Integer",
            },
            field: "tierValue",
            type: "rightAligned",
            valueFormatter: (params) => formatCurrencyWrapper(params.value),
            cellRenderer: (params) => (
              <EverTooltip
                title={formatCurrencyWrapper(params.value, {
                  decimalPlaces: 6,
                  truncate: true,
                })}
                placement="bottom"
                overlayClassName="roundedTooltip"
              >
                <EverTg.SubHeading4 className="text-ever-base-content">
                  {params.valueFormatted}
                </EverTg.SubHeading4>
              </EverTooltip>
            ),
          });
          break;
        }
        case "tierName": {
          colDefs.push({
            minWidth: 120,
            width: 150,
            headerName: "Tier",
            headerComponentParams: {
              menuIcon: "String",
            },
            field: "tierName",
            cellRenderer: (params) => (
              <EverTg.SubHeading4 className="text-ever-base-content">
                {params.value}
              </EverTg.SubHeading4>
            ),
          });
          break;
        }
        case "quotaErosion": {
          colDefs.push({
            minWidth: 150,
            width: 150,
            headerName: t("QUOTA_EROSION"),
            headerComponentParams: {
              menuIcon: "Integer",
            },
            field: "quotaErosion",
            type: "rightAligned",
            aggFunc: "sum",
            valueFormatter: (params) => formatCurrencyWrapper(params.value),
            cellRenderer: (params) => (
              <EverTooltip
                title={formatCurrencyWrapper(params.value, {
                  decimalPlaces: 6,
                  truncate: true,
                })}
                placement="bottom"
              >
                <EverTg.SubHeading4 className="text-ever-base-content">
                  {params.valueFormatted}
                </EverTg.SubHeading4>
              </EverTooltip>
            ),
          });
          break;
        }
        default: {
          break;
        }
      }
    }

    colDefs.push({
      field: "export_csv",
      headerName: "Download",
      suppressMenu: true,
      lockPosition: "right",
      suppressColumnsToolPanel: true,
      suppressFiltersToolPanel: true,
      width: 115,
      maxWidth: 115,
      minWidth: 115,
      suppressSizeToFit: true,
      valueGetter: (params) => {
        return {
          name: params?.data?.employeeName,
          email: params?.data?.employeeEmailId,
        };
      },
      cellRenderer: (props) => {
        return props.value.email ? (
          <div className="flex justify-center w-full">
            <IconButton
              type="link"
              onClick={() =>
                exportSimulatedRecordsAsCsvHandler({
                  payeeEmailId: props.value.email,
                  payeeName: props.value.email,
                  t: t,
                })
              }
              icon={<DownloadIcon className="w-8 h-8 text-ever-primary" />}
            />
          </div>
        ) : null;
      },
    });

    return colDefs;
  }

  return (
    <div className="flex flex-col w-full h-full gap-4">
      <div className="flex justify-between">
        {isDoNothingUsed() && commissionPlanDetails.planScope.canEdit && (
          <div className="flex items-center">
            <EverSwitch
              label={`Include 'Do nothing' records`}
              checked={
                selectedCriteriaInTimeMachine?.criteriaConfig.showDoNothing
              }
              onChange={handleDoNothingSwitchToggle}
            />
            <EverTooltip title='Records that satisfy "Do Nothing" are not shown by default in statements'>
              <HelpCircleIcon className="w-4 h-4 text-ever-base-content-mid" />
            </EverTooltip>
          </div>
        )}
      </div>
      <div className="flex flex-col h-full flex-auto ag-theme-material no-border zebra-grid w-full">
        <AgGridReact
          ref={gridRef}
          {...getDefaultOptions({ type: "md" })}
          rowHeight={40}
          headerHeight={58}
          domLayout="normal"
          paginationPageSize={100}
          columnDefs={columnDefs.current}
          rowData={simulationData?.payees}
          components={{ agColumnHeader: CustomHeader }}
          getRowClass={(params) => {
            return params.node.rowPinned === "bottom"
              ? "!bg-ever-base-200"
              : "";
          }}
          suppressColumnVirtualisation={true}
          processRowPostCreate={function (params) {
            if (params.node.rowPinned === "bottom") {
              let totalQuota = 0;
              let totalTierValue = 0;
              params.api.forEachNode(function (node) {
                totalQuota += Number.parseFloat(node.data.quotaErosion || 0);
                totalTierValue += Number.parseFloat(node.data.tierValue || 0);
              });

              for (const col of columnDefs.current) {
                switch (col.field) {
                  case "commission": {
                    params.node?.setDataValue(
                      "commission",
                      simulationData.totalCommissions
                    );
                    break;
                  }
                  case "tierValue": {
                    params.node?.setDataValue("tierValue", totalTierValue);
                    break;
                  }
                  case "quotaErosion": {
                    params.node?.setDataValue("quotaErosion", totalQuota);
                    break;
                  }
                  default: {
                    break;
                  }
                }
              }
            }
          }}
          pinnedBottomRowData={[{ employeeName: null }]}
        />
      </div>
      {selectedPayee && (
        <ProfileDrawer
          closeDrawer={() => setPayeeProfileDrawerVisibility(false)}
          visible={payeeProfileDrawerVisibility}
          selectedPayee={selectedPayee}
          dateRange={
            selectedDateRange ? moment.utc(selectedDateRange[1]) : null
          }
          periodStartDate={
            selectedDateRange ? moment(selectedDateRange[0]) : null
          }
          periodEndDate={
            selectedDateRange ? moment(selectedDateRange[1]) : null
          }
          customView={false}
        />
      )}
    </div>
  );
}
