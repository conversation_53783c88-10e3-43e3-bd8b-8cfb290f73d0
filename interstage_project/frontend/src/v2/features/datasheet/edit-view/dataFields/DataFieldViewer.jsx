import {
  DotsVerticalIcon,
  InfoCircleIcon,
  AlertTriangleIcon,
} from "@everstage/evericons/outlined";
import { AlertSquareIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { cloneDeep } from "lodash";
import React, { useEffect, useState, useContext } from "react";
import { twMerge } from "tailwind-merge";
import { useImmer } from "use-immer";

import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverTg,
  EverInput,
  EverCheckbox,
  EverHoverCard,
  message,
  EverTooltip,
} from "~/v2/components";

import { DatasheetContext } from "../../DatasheetStore";
import { DataTypeIcon, snakeToCamel } from "../utils";

/**
 * Renders a component for viewing and managing data fields.
 * @param {object} props - The props object.
 * @param {boolean} [props.isStale=false] - Flag indicating if the field is stale.
 * @param {function} props.triggerRename - Function to trigger renaming.
 * @param {boolean} [props.isFormulaField=false] - Flag indicating if it's a formula field.
 * @param {function} props.setShowFormulaWizard - Function to set formula field modal visibility.
 * @param {function} props.setSelectedFormulaField - Function to set the selected formula field.
 * @param {object} props.field - The field object.
 * @param {function} props.handleCheckboxToggle - Function to handle checkbox toggle.
 * @param {function} props.setIsUpdateView - Function to set the update view flag.
 * @param {function} [props.onDelete] - Function to handle delete action.
 * @param {function} [props.onClone] - Function to handle clone action.
 * @param {boolean} [props.isValidationInProgress=false] - Indicates if a validation process is in progress. If true, the field's checkbox is disabled.
 * @param {function} [props.updateCalculatedFieldDetails] - Function to update the calculated field details.
 * @returns {JSX.Element} The JSX element representing the data field viewer component.
 */

export function DataFieldViewer({
  isStale = false,
  triggerRename,
  isFormulaField = false,
  setShowFormulaWizard,
  setSelectedFormulaField,
  field,
  handleCheckboxToggle,
  setIsUpdateView,
  onDelete,
  onClone,
  updateCalculatedFieldDetails,
  isValidationInProgress = false,
}) {
  const displayName = field?.display_name || "";
  const systemName = field?.system_name || "";
  const sourceNameHistory = field?.source_name_history || "";

  const isDisabled =
    field?.is_primary ||
    field?.is_dependent ||
    field?.is_adjusted ||
    isValidationInProgress ||
    false;
  const isChecked = field?.is_selected || false;
  const [isFieldHistoryVisible, setIsFieldHistoryVisible] = useState(false);
  const { dataTypesById } = useVariableStore();
  const datasheetStore = useContext(DatasheetContext);

  const [dsFieldConfig, setDsFieldConfig] = useImmer({
    moreOptionVisible: false,
    showRenameInput: false,
    renameInput: displayName,
    sourceHistory: false,
  });

  useEffect(() => {
    if (displayName !== dsFieldConfig.renameInput) {
      setDsFieldConfig((draft) => {
        draft.renameInput = displayName;
      });
    }
  }, [displayName]);

  const fieldStyle = () => {
    if (isDisabled) return "bg-ever-base-50 border-transparent";
    if (isStale) return "border-ever-base-400 bg-ever-warning-lite";
    if (isChecked) return "border-ever-base-400 bg-ever-base";
    return "bg-ever-base-50 border-transparent";
  };

  const handleCheckboxChange = () => {
    // setAllDataFields((prevDataFields) => {
    //   const clonedPrevDataFields = cloneDeep(prevDataFields);
    //   clonedPrevDataFields[field.index].is_selected = !field.is_selected;
    //   return clonedPrevDataFields;
    // });
    handleCheckboxToggle(field.system_name);
  };

  const handleRenameKeyDown = (event) => {
    const trimmedInput = dsFieldConfig?.renameInput?.trim();

    // Handle Enter key for renaming
    if (event.key === "Enter") {
      if (trimmedInput === displayName) {
        setDsFieldConfig((draft) => {
          draft.showRenameInput = false; // Hide rename input
          draft.renameInput = displayName; // Reset to original display name
        });
        return;
      }
      // Check if input is valid
      if (trimmedInput && trimmedInput.length < 254) {
        setDsFieldConfig((draft) => {
          draft.showRenameInput = false; // Hide rename input
          draft.renameInput = displayName; // Reset to original display name
        });
        triggerRename({
          systemName: systemName,
          displayName: trimmedInput, // Trigger rename with new display name
        });
      } else if (trimmedInput.length >= 254) {
        message.error("Column name should be less than 254 characters"); // Error for too long input
      }
    }
    // Handle Escape key to cancel renaming
    else if (event.key === "Escape") {
      setDsFieldConfig((draft) => {
        draft.showRenameInput = false; // Hide rename input
        draft.renameInput = displayName; // Reset to original display name
      });
    }
  };

  const handleMoreOptionsClick = (event) => {
    event.stopPropagation();
  };

  const handleMenuVisibilityChange = (open) => {
    setDsFieldConfig((draft) => {
      draft.moreOptionVisible = open;
    });
  };

  const renderRenameInput = () => (
    <EverInput
      size="small"
      value={dsFieldConfig.renameInput}
      onChange={(event) => {
        setDsFieldConfig((draft) => {
          draft.renameInput = event.target.value;
        });
      }}
      onBlur={() => {
        const trimmedInput = dsFieldConfig?.renameInput?.trim();
        if (trimmedInput === displayName) {
          setDsFieldConfig((draft) => {
            draft.showRenameInput = false; // Hide rename input
            draft.renameInput = displayName; // Reset to original display name
          });
          // If the input is the same as the original display name, don't trigger the rename
          return;
        }
        if (trimmedInput && trimmedInput.length < 254) {
          setDsFieldConfig((draft) => {
            draft.showRenameInput = false;
            draft.renameInput = displayName;
          });
          triggerRename({
            systemName: systemName,
            displayName: trimmedInput,
          });
        } else if (trimmedInput.length >= 254) {
          message.error("Column name should be less than 254 characters");
        }
      }}
      autoFocus={true}
      onKeyDown={handleRenameKeyDown}
    />
  );

  function setUpdateFormula() {
    setShowFormulaWizard(true);
    let expression = [];
    if (updateCalculatedFieldDetails) {
      if (updateCalculatedFieldDetails?.meta_data?.infix) {
        if (
          updateCalculatedFieldDetails?.meta_data?.infix.token_type ===
          "FUNCTIONS"
        ) {
          expression = updateCalculatedFieldDetails?.meta_data?.infix;
        } else {
          expression =
            updateCalculatedFieldDetails?.meta_data?.infix?.filter(
              (token) => token.token_type !== "AST_META"
            ) || [];
        }
      } else {
        // window function ast doesn't contain an infix property, so assign the property directly to the updateCalculatedFieldDetails
        const clonedField = cloneDeep(updateCalculatedFieldDetails?.meta_data);
        if (clonedField?.used_system_names) {
          delete clonedField.used_system_names;
        }
        expression = updateCalculatedFieldDetails?.meta_data
          ? [clonedField]
          : [];
      }

      setSelectedFormulaField({
        initialExpression: snakeToCamel(expression),
        expressionType:
          updateCalculatedFieldDetails?.meta_data?.criteria_type || "simple",
        name: updateCalculatedFieldDetails?.display_name || "",
        systemName: updateCalculatedFieldDetails?.system_name || "",
        dataType: dataTypesById[updateCalculatedFieldDetails?.data_type_id],
      });
    } else {
      if (field?.meta_data?.infix) {
        if (field.meta_data?.infix.token_type === "FUNCTIONS") {
          expression = field?.meta_data?.infix;
        } else {
          expression =
            field?.meta_data?.infix?.filter(
              (token) => token.token_type !== "AST_META"
            ) || [];
        }
      } else {
        // window function ast doesn't contain an infix property, so assign the property directly to the field
        const clonedField = cloneDeep(field?.meta_data);
        if (clonedField?.used_system_names) {
          delete clonedField.used_system_names;
        }
        expression = field?.meta_data ? [clonedField] : [];
      }

      setSelectedFormulaField({
        initialExpression: snakeToCamel(expression),
        expressionType: field?.meta_data?.criteria_type || "simple",
        name: field?.display_name || "",
        systemName: field?.system_name || "",
        dataType: dataTypesById[field?.data_type_id],
      });
    }
    setIsUpdateView(true);
  }

  const renderMenu = () => (
    <Menu className="!p-1.5 rounded-lg border border-solid border-ever-base-400">
      {isFormulaField ? (
        <>
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="update"
            onClick={() => {
              setUpdateFormula();
            }}
          >
            <EverTg.Caption className="text-ever-base-content">
              Edit
            </EverTg.Caption>
          </Menu.Item>
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="clone"
            onClick={() => onClone(field)}
          >
            <EverTg.Caption className="text-ever-base-content">
              Clone
            </EverTg.Caption>
          </Menu.Item>
          {!isDisabled && (
            <Menu.Item
              className="!py-2 !px-3 flex gap-2 !h-8 hover:!bg-ever-error-lite"
              key="delete"
              onClick={() => onDelete(field)}
            >
              <EverTg.Caption className="text-ever-error-lite-content">
                Delete
              </EverTg.Caption>
            </Menu.Item>
          )}
        </>
      ) : (
        <Menu.Item
          className="!py-2 !px-3 flex gap-2 !h-8"
          key="rename"
          onClick={() => {
            setDsFieldConfig((draft) => {
              draft.showRenameInput = true;
              draft.moreOptionVisible = false;
              draft.sourceHistory = true;
            });
          }}
        >
          <EverTg.Caption className="text-ever-base-content">
            Rename
          </EverTg.Caption>
        </Menu.Item>
      )}
    </Menu>
  );

  const renderFieldContent = () => {
    const tooltipTitle = field?.is_primary
      ? "Primary columns are essential and cannot be deselected"
      : field?.is_dependent
      ? "This column is used in a dependent sheet and cannot be deselected"
      : field?.is_adjusted
      ? "Adjusted columns cannot be deselected"
      : "";

    const warning = field?.warning;
    return (
      <div className="flex gap-2 w-full flex-1 truncate">
        <EverTooltip title={tooltipTitle}>
          <EverCheckbox
            onChange={handleCheckboxChange}
            checked={isChecked}
            disabled={isDisabled}
          />
        </EverTooltip>
        <div
          className={`flex gap-2 flex-grow truncate items-center ${
            isDisabled ? "cursor-not-allowed" : "cursor-default"
          }`}
          onDoubleClick={() => {
            if (isValidationInProgress) return;
            setDsFieldConfig((draft) => {
              draft.showRenameInput = true;
              draft.moreOptionVisible = false;
              draft.sourceHistory = false;
            });
          }}
        >
          <div
            className="flex items-center cursor-default"
            onDoubleClick={(event) => {
              if (isValidationInProgress) return;
              event.stopPropagation();
            }}
          >
            <DataTypeIcon
              name={dataTypesById[field?.data_type_id] || "Integer"}
            />
          </div>

          <div className="truncate">
            <EverTg.Text className="text-ever-base-content flex-grow truncate ">
              {displayName}
            </EverTg.Text>
          </div>
        </div>
        {warning && (
          <EverTooltip title={warning}>
            <AlertTriangleIcon className="text-ever-warning w-6 h-6 flex" />
          </EverTooltip>
        )}
      </div>
    );
  };

  const renderFieldOptions = () => {
    const sourceNameHistoryArray = sourceNameHistory.split("<<");

    return (
      <div className="flex gap-1 items-center flex-none w-18">
        {!isFormulaField && (
          <EverHoverCard
            className={
              "px-2 py-2 bg-ever-base border border-solid border-ever-base-400 rounded-lg shadow-lg"
            }
            align="end"
            alignOffset={-20}
            onOpenChange={(value) => {
              setIsFieldHistoryVisible(value);
            }}
            side="top"
            content={
              <div className="text-ever-base-content max-w-sm flex flex-col gap-2 flex-wrap">
                <div className="flex gap-2 ">
                  <div className="flex">
                    <EverTg.Caption className="text-ever-base-content-mid font-medium">
                      Field:
                    </EverTg.Caption>
                  </div>
                  <div className="flex  gap-2 flex-wrap ">
                    <EverTg.Caption className="text-ever-base-content font-medium text-pretty break-all">
                      {sourceNameHistoryArray[0]}
                    </EverTg.Caption>
                  </div>
                </div>
                <div className="flex gap-2 ">
                  <div className="flex">
                    <EverTg.Caption className="text-ever-base-content-mid font-medium ">
                      Path:
                    </EverTg.Caption>
                  </div>
                  <div className="flex  gap-2  flex-wrap ">
                    {sourceNameHistoryArray.length > 1 ? (
                      // If there are multiple source names, display them with arrows
                      sourceNameHistoryArray.slice(1).map((item, index) => (
                        <>
                          {index !== 0 && (
                            <EverTg.Caption className="text-ever-base-content-mid font-medium ">
                              &lt;
                            </EverTg.Caption>
                          )}
                          <EverTg.Caption className="text-ever-base-content font-medium ">
                            {item}
                          </EverTg.Caption>
                        </>
                      ))
                    ) : field.source_id === datasheetStore.datasheetId ? (
                      // If there's only one source name and it matches the current datasheet, display its name
                      <EverTg.Caption className="text-ever-base-content font-medium ">
                        {datasheetStore?.currentDatasheetName}
                      </EverTg.Caption>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </div>
            }
            showArrow
            arrowClass={"stroke-ever-base-400"}
          >
            <InfoCircleIcon
              className={twMerge(
                "text-ever-base-content hidden group-hover:flex  w-4 h-4",
                isFieldHistoryVisible ? "flex" : ""
              )}
            />
          </EverHoverCard>
        )}

        {field?.is_primary && (
          <div className="h-5 border border-solid border-ever-chartColors-20 border-opacity-20 py-0.5 px-1.5 flex items-center rounded bg-ever-chartColors-20 bg-opacity-5">
            <EverTg.Caption className="text-ever-chartColors-20">
              Primary
            </EverTg.Caption>
          </div>
        )}
        {isStale && (
          <AlertSquareIcon className="text-ever-warning w-6 h-6 flex" />
        )}
        {isFormulaField && (
          <Dropdown
            overlay={renderMenu}
            trigger={["click"]}
            onVisibleChange={handleMenuVisibilityChange}
          >
            <div
              className={twMerge(
                "w-5 h-5 flex items-center justify-center group-hover:flex hidden border-transparent rounded-sm hover:border-ever-base-400 hover:bg-ever-base-300",
                dsFieldConfig.moreOptionVisible
                  ? "flex border-ever-base-400 bg-ever-base-300"
                  : ""
              )}
              onClick={handleMoreOptionsClick}
            >
              <DotsVerticalIcon className="text-ever-base-content w-3 h-3" />
            </div>
          </Dropdown>
        )}
      </div>
    );
  };

  useEffect(() => {
    if (updateCalculatedFieldDetails) setUpdateFormula();
  }, [updateCalculatedFieldDetails]);

  if (dsFieldConfig.showRenameInput) {
    return renderRenameInput();
  }

  return (
    <div
      className={`w-full group flex justify-between border border-solid h-9 px-2 py-1 cursor-pointer group rounded-md items-center ${fieldStyle()}`}
    >
      {renderFieldContent()}
      {renderFieldOptions()}
    </div>
  );
}
