import React, { useEffect, useState } from "react";

import {
  EverButton,
  EverHotToastBanner,
  EverPopover,
  EverTg,
} from "~/v2/components";

export function GlobalAdjustmentWarning({ datasheetDetails }) {
  const [showPopover, setShowPopover] = useState(false);

  useEffect(() => {
    return () => {
      setShowPopover(false);
    };
  }, []);
  return (
    <div className="h-full items-end flex ml-8">
      <EverHotToastBanner
        type="info"
        description={
          <EverTg.Caption className="w-full text-center">
            This adjustment will be copied to
            {
              <EverPopover
                className="max-h-[500px] max-w-[500px] overflow-auto !p-4"
                open={showPopover}
                onOpenChange={(isOpen) => setShowPopover(isOpen)}
                defaultOpen={false}
                showCloseIcon={false}
                content={
                  <div className="flex flex-col gap-2">
                    {datasheetDetails.downstream_sheet_details.map((ds) => {
                      return (
                        <a
                          rel="noopener noreferrer"
                          className={`text-xs mr-2 hover:underline font-medium text-ever-primary hover:text-ever-primary`}
                          href={ds.url}
                          target="_blank"
                          key={ds.datasheet_id}
                        >
                          {ds.name}
                        </a>
                      );
                    })}
                  </div>
                }
                align="center"
              >
                <EverButton
                  size="small"
                  type="link"
                  onClick={() => setShowPopover((prev) => !prev)}
                  className="inline !px-1 text-xs font-normal hover:font-semibold underline"
                  hideLoader={true}
                >
                  {datasheetDetails.downstream_sheet_details.length} dependent{" "}
                  {datasheetDetails.downstream_sheet_details.length > 1
                    ? "sheets"
                    : "sheet"}
                </EverButton>
              </EverPopover>
            }
          </EverTg.Caption>
        }
        className="w-max"
      />
    </div>
  );
}
