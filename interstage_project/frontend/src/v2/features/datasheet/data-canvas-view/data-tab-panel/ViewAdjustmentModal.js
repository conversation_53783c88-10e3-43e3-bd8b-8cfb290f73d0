import { InfoCircleIcon } from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { Row, Col } from "antd";
import { format, isValid, parse } from "date-fns";
import { observer } from "mobx-react";
import React, { useContext } from "react";
import { useMutation } from "react-query";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverModal,
  EverButton,
  EverTg,
  EverLabel,
  message,
} from "~/v2/components";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";

import { DatasheetContext } from "../../DatasheetStore";
import { revertAdjustment } from "../../restApi";
import { ADJUSTMENT_TYPE_ENUMS } from "../../utils";

// Grid configuration constants
const GRID_CONFIG = {
  ROW_HEIGHT: 32,
  HEADER_HEIGHT: 32,
  PAGINATION_PAGE_SIZE: 9999,
  CACHE_BLOCK_SIZE: 9999,
  PRIMARY_RECORDS_PAGINATION_PAGE_SIZE: 1,
  PRIMARY_RECORDS_CACHE_BLOCK_SIZE: 1,
};

/**
 * Retrieves variable details based on the system name.
 * @param {Array} variables - Array of variables.
 * @param {String} systemName - System name of the variable.
 * @returns {Object} - The variable details.
 */
function getVariableDetails(variables, systemName) {
  const selectedVar = variables.find((ele) => ele.system_name === systemName);
  return selectedVar;
}

/**
 * Formats date string to 'dd MMM yyyy' format.
 * @param {String} dateString - The date string to format.
 * @returns {String} - The formatted date string.
 */
function formatDate(dateString) {
  if (dateString === "NaT" || dateString === "null" || !dateString) return "";

  const formats = ["yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd"];
  let date;

  for (const fmt of formats) {
    date = parse(dateString, fmt, new Date());
    if (isValid(date)) break;
  }

  if (!isValid(date)) {
    return dateString;
  }

  return format(date, "dd MMM yyyy");
}

/**
 * Converts adjustment type to a human-readable format.
 * @param {String} type - The adjustment type.
 * @returns {String} - The human-readable adjustment type.
 */
function typeName(type) {
  switch (type) {
    case "IGNORE": {
      return "Ignored";
    }
    case "UPDATE": {
      return "Update";
    }
    case "SPLIT": {
      return "Insert";
    }
  }
  return null;
}

/**
 * AdjustmentData Component - Displays adjustment data in a table.
 */
export const AdjustmentData = observer((props) => {
  const {
    //    datasheetName,
    primaryRecords,
    adjustmentScope,
    adjustmentType,
    comments,
    selectedRecord,
    variables,
    appliedBy,
  } = props;

  const type = typeName(adjustmentType);
  const { dataTypesById } = useVariableStore();

  /**
   * Updates the description container with header and values.
   * @param {Array} headers - The header definitions.
   * @param {Object} values - The values to display.
   * @returns {JSX.Element} - The updated description container.
   */
  function updateDescriptionContainer(headers, values) {
    const originalValues = values.original_values;
    const dataSource = Object.keys(originalValues).map((key) => {
      const result = {};
      const variableDetails = getVariableDetails(variables, key);
      const dataType = dataTypesById[variableDetails?.data_type_id];
      result.column = key;
      result.originalValue =
        dataType == "Date"
          ? formatDate(originalValues[key])
          : originalValues[key];
      result.newValue =
        dataType == "Date" ? formatDate(values[key]) : values[key];
      return result;
    });
    return (
      <div className="ag-theme-material no-border zebra-grid w-full h-full datasheet-table datasheet-v2-table border-t-0 border-r border-b-0 border-l border-solid border-ever-base-400 no-min-height">
        <AgGridReact
          {...getDefaultOptions({ type: "md" })}
          columnDefs={headers}
          rowData={dataSource}
          rowHeight={GRID_CONFIG.ROW_HEIGHT}
          headerHeight={GRID_CONFIG.HEADER_HEIGHT}
          paginationPageSize={GRID_CONFIG.PAGINATION_PAGE_SIZE}
          cacheBlockSize={GRID_CONFIG.CACHE_BLOCK_SIZE}
          domLayout="autoHeight"
          getContextMenuItems={() => {
            return ["autoSizeAll"];
          }}
        />
      </div>
    );
  }

  /**
   * Creates a container for split description.
   * @param {Object} selectRecord - The selected record data.
   * @returns {JSX.Element} - The split description container.
   */
  const splitDescriptionContainer = (selectRecord) => {
    const columns = [
      {
        field: "column",
        headerName: "Column Name",
        suppressColumnsToolPanel: true,
        pinned: "left",
        lockPinned: true,
        valueGetter: (params) => {
          const variable = getVariableDetails(variables, params.data.column);
          return variable?.display_name || "";
        },
      },
      {
        field: "originalValue",
        headerName: "Original Value",
        suppressColumnsToolPanel: true,
        pinned: "left",
        lockPinned: true,
        valueGetter: (params) => {
          return params.data.originalValue ?? "-";
        },
      },
    ];
    const originalValue = selectRecord.original_values;
    const splitValues = selectRecord.adjustment_details.split_values;
    const originalColumns = new Set();
    Object.keys(splitValues).map((item) => {
      Object.keys(splitValues[item]).map((key) => {
        originalColumns.add(key);
      });
    });

    const data = [];

    for (const [index] of Object.entries(splitValues)) {
      columns.push({
        field: `record${index}`,
        headerName: `Record ${index}`,
        suppressColumnsToolPanel: true,
      });
    }

    for (const key of originalColumns) {
      const variableDetails = getVariableDetails(variables, key);
      const dataType = dataTypesById[variableDetails.data_type_id];
      const result =
        typeof originalValue[key] === "boolean"
          ? originalValue[key].toString()
          : dataType === "Date"
          ? formatDate(originalValue[key])
          : originalValue[key];
      data.push({
        column: key,
        originalValue: result,
      });
    }

    for (const item of data) {
      const variableDetails = getVariableDetails(variables, item.column);
      const dataType = dataTypesById[variableDetails.data_type_id];
      for (const [index] of Object.entries(splitValues)) {
        const name = `record${index}`;
        const splitValue = splitValues[index];

        let value = "-";
        if (splitValue && item.column in splitValue) {
          value =
            splitValue[item.column] === null ? "" : splitValue[item.column];
        }

        item[name] = String(
          typeof value === "boolean"
            ? value.toString()
            : dataType === "Date"
            ? formatDate(value)
            : value
        );
      }
    }

    return (
      <>
        <div className="flex items-center gap-1 mb-2">
          <InfoCircleIcon className="w-3 h-3 text-ever-base-content" />
          <EverTg.Text className="text-ever-base-content font-medium text-xs">
            Records with &ldquo;–&rdquo; indicate the original value has been
            retained.
          </EverTg.Text>
        </div>
        <div className="ag-theme-material no-border zebra-grid w-full h-full datasheet-table datasheet-v2-table border-t-0 border-r border-b-0 border-l border-solid border-ever-base-400 no-min-height">
          <AgGridReact
            {...getDefaultOptions({ type: "md" })}
            columnDefs={columns}
            rowData={data}
            rowHeight={GRID_CONFIG.ROW_HEIGHT}
            headerHeight={GRID_CONFIG.HEADER_HEIGHT}
            paginationPageSize={GRID_CONFIG.PAGINATION_PAGE_SIZE}
            cacheBlockSize={GRID_CONFIG.CACHE_BLOCK_SIZE}
            domLayout="autoHeight"
            getContextMenuItems={() => {
              return ["autoSizeAll"];
            }}
          />
        </div>
      </>
    );
  };
  /**
   * Creates a container for primary records.
   * @param {Array} records - Array of primary records.
   * @returns {JSX.Element} - The primary records container.
   */
  const primaryRecordsContainer = (records) => {
    const columns = [
      {
        field: "name",
        headerName: "Primary Key",
        suppressColumnsToolPanel: true,
        pinned: "left",
        lockPinned: true,
        valueGetter: (params) => {
          return params.data.name;
        },
      },
      {
        field: "value",
        headerName: "Value",
        suppressColumnsToolPanel: true,
        pinned: "left",
        lockPinned: true,
        valueGetter: (params) => {
          const dataType = dataTypesById[params.data.datatypeId];
          const value = params.data.value;
          const result =
            typeof dataType === "boolean"
              ? value.toString()
              : dataType === "Date"
              ? formatDate(value)
              : value;

          return result;
        },
      },
    ];
    const dataSource = [];
    for (const record of records.filter((record) => record && record)) {
      dataSource.push({
        name: record?.display_name,
        value: selectedRecord[record.system_name],
        datatypeId: record.data_type_id,
      });
    }

    return (
      <div className="ag-theme-material no-border zebra-grid w-full h-full datasheet-table datasheet-v2-table border-t-0 border-r border-b-0 border-l border-solid border-ever-base-400 no-min-height">
        <AgGridReact
          {...getDefaultOptions({ type: "md" })}
          columnDefs={columns}
          rowData={dataSource}
          rowHeight={GRID_CONFIG.ROW_HEIGHT}
          headerHeight={GRID_CONFIG.HEADER_HEIGHT}
          paginationPageSize={GRID_CONFIG.PRIMARY_RECORDS_PAGINATION_PAGE_SIZE}
          cacheBlockSize={GRID_CONFIG.PRIMARY_RECORDS_CACHE_BLOCK_SIZE}
          domLayout="autoHeight"
          getContextMenuItems={() => {
            return ["autoSizeAll"];
          }}
        />
      </div>
    );
  };
  /**
   * Displays primary records container.
   * @returns {JSX.Element} - The primary records container.
   */
  const primaryRecord = () => {
    return primaryRecordsContainer(primaryRecords);
  };

  /**
   * Displays adjusted records container.
   * @returns {JSX.Element} - The adjusted records container.
   */
  const adjustedRecords = () => {
    const columns = [
      {
        field: "column",
        headerName: "Column Name",
        suppressColumnsToolPanel: true,
        pinned: "left",
        lockPinned: true,
        minWidth: 140,
        valueGetter: (params) => {
          const variable = getVariableDetails(variables, params.data.column);
          return variable?.display_name || "";
        },
      },
      {
        field: "originalValue",
        headerName: "Original Value",
        suppressColumnsToolPanel: true,
        minWidth: 140,
        valueGetter: (params) => {
          return params.data.originalValue ?? "-";
        },
      },
      {
        field: "newValue",
        headerName: "Updated To",
        suppressColumnsToolPanel: true,
        minWidth: 140,
        cellRenderer: (params) => {
          return <>{params.value?.toString() ?? "-"}</>;
        },
      },
    ];
    const values = selectedRecord;
    return updateDescriptionContainer(columns, values);
  };

  return (
    <div className="w-full h-full">
      <Row>
        {/* <EverTg.Heading3 className="text-ever-base-content font-semibold mr-1">
          Datasheet:
        </EverTg.Heading3>
        <EverTg.Heading3 className="text-ever-base-content font-normal">
          {datasheetName}
        </EverTg.Heading3> */}
      </Row>
      {type !== "Ignored" && (
        <Row className="adjustmentContainer">
          {type == "Update"
            ? adjustedRecords()
            : splitDescriptionContainer(selectedRecord)}
        </Row>
      )}
      <Row className="mt-5">
        <Col span={24} className="flex">
          <EverLabel>Applied By: </EverLabel>
          <EverTg.Text>{appliedBy}</EverTg.Text>
        </Col>
      </Row>
      <Row className="mt-5">
        <Col span={24} className="flex">
          <EverLabel>Adjustment Type: </EverLabel>
          <EverTg.Text>{type}</EverTg.Text>
        </Col>
      </Row>
      <Row className="mt-5">
        <Col span={24} className="flex">
          <EverLabel>Adjustment Scope: </EverLabel>
          <EverTg.Text> {adjustmentScope}</EverTg.Text>
        </Col>
      </Row>
      <Row className="mt-5">
        <Col span={24} className="flex">
          <EverLabel> Comments: </EverLabel>
          <EverTg.Text> {comments}</EverTg.Text>
        </Col>
      </Row>
      <Row className="mt-5">{primaryRecord()}</Row>
    </div>
  );
});
const AdjustmentModal = observer((props) => {
  const {
    visible,
    handleVisible,
    title,
    selectedRecord,
    isRevert,
    // saveSessionKd,
  } = props;
  const { accessToken } = useAuthStore();
  const { adjustment_details } = selectedRecord;
  const datasheetStore = useContext(DatasheetContext);

  const adjustmentScope = adjustment_details?.is_global ? "Global" : "Local";
  const type = adjustment_details?.type || "";
  const comments = adjustment_details?.comments || "";
  const handleCancel = () => {
    handleVisible("");
  };

  const primaryRecords = datasheetStore.datasheetDetails?.variables
    .map((variable) => {
      if (
        variable.system_name === "effective_start_date" ||
        variable.system_name === "effective_end_date" ||
        variable.system_name === "employee_id"
      ) {
        return variable;
      }
      if (variable.is_primary) {
        return variable;
      }
    })
    .filter((record) => record !== undefined);

  const revertAdjustmentRequest = useMutation(
    (data) =>
      revertAdjustment(
        accessToken,
        datasheetStore.datasheetId,
        data.id,
        data.data
      ),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.toString());
      },
      onSuccess: async () => {
        message.success(`Adjustment reverted successfully.`);
        handleCancel();
        datasheetStore.adjustmentTableDataRefetch();
        datasheetStore.refetchDatasheetDetails();
      },
    }
  );

  return (
    <div>
      <EverModal
        title={title}
        visible={visible}
        onCancel={handleCancel}
        width={type === ADJUSTMENT_TYPE_ENUMS.SPLIT ? 820 : 520}
        footer={
          isRevert && (
            <Row>
              <Col span={24} align="right" className="flex">
                <EverButton onClick={handleCancel} type="outlined" color="base">
                  Cancel
                </EverButton>

                <EverButton
                  type="filled"
                  color="primary"
                  onClick={async () => {
                    const adjustmentDetails = adjustment_details;
                    const bodyData = {
                      rowKey: adjustmentDetails.row_key,
                      originalRowKey: adjustmentDetails.row_key,
                      adjustmentNumber: adjustmentDetails.adjustment_number,
                      type: adjustmentDetails.type,
                      subAdjustmentNumber:
                        adjustmentDetails.sub_adjustment_number,
                    };
                    await revertAdjustmentRequest.mutate({
                      id: adjustmentDetails.adjustment_id,
                      data: bodyData,
                    });
                  }}
                >
                  Revert
                </EverButton>
              </Col>
            </Row>
          )
        }
        maskClosable={false}
        destroyOnClose
      >
        <AdjustmentData
          adjustedData={adjustment_details}
          appliedBy={adjustment_details?.applied_by || ""}
          adjustmentType={type}
          comments={comments}
          adjustmentScope={adjustmentScope}
          primaryRecords={primaryRecords}
          selectedRecord={selectedRecord}
          datasheetName={datasheetStore.currentDatasheetName}
          variables={datasheetStore.datasheetDetails?.variables}
        />
      </EverModal>
    </div>
  );
});
export const AdjustmentDetail = observer((props) => {
  return <AdjustmentModal {...props} />;
});
