import { cloneDeep, debounce } from "lodash";
import { observer } from "mobx-react";
import React, {
  useState,
  useContext,
  useRef,
  useEffect,
  useCallback,
} from "react";
import { useMutation, useQuery } from "react-query";
import { twMerge } from "tailwind-merge";

import { DATASHEET_VIEW_ID } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  PageNotFound,
  toast,
  EverHotToastMessage,
  //EverHotToastAlert,
  EverTg,
  EverHotToastNotification,
} from "~/v2/components";
import { lostAstronaut } from "~/v2/images";

import { DataTabPanel } from "./data-tab-panel";
import { HeaderPanel } from "./header-panel";
import { VersionToggleButton } from "./VersionToggleButton";
import { DatasheetContext } from "../DatasheetStore";
import { getDatasheetAccess } from "../restApi";
import useFetchApiWithAuth from "../useFetchApiWithAuth";

// polling frequency - 8s
const DATASHEET_POLLING_RATE = 8 * 1000;
// max time datasheet sync can run before warning
// message is displayed - 60 mins
const DATASHEET_SYNC_THRESHOLD = 60 * 60;

export const DataCanvasView = observer(
  ({
    showGenerateSheetModal,
    setShowGenerateSheetModal,
    hasFlushApiAttemptedRef,
    sendMessage,
    refetchForceSkippedDatasheetIds,
  }) => {
    const datasheetStore = useContext(DatasheetContext);
    const { accessToken } = useAuthStore();

    const syncStatusTimeoutRef = useRef(null);
    const [isPolling, setIsPolling] = useState(false);
    const [isDatasheetNotAvailable, setIsDatasheetNotAvailable] =
      useState(false);
    const [objectRestrictedMessage, setObjectRestrictedMessage] = useState("");
    const [
      datasheetRestrictedAccessBanner,
      setDatasheetRestrictedAccessBanner,
    ] = useState(false);
    const [activeKey, setActiveKey] = useState(DATASHEET_VIEW_ID.ALL_DATA);
    const [pivotConfig, setPivotConfig] = useState({});
    const [pivotConfigPayload, setPivotConfigPayload] = useState({});

    const { fetchData } = useFetchApiWithAuth();
    const [isDatasheetGenerated, setIsDatasheetGenerated] = useState(true);
    const [additionalSourceVariables, setAdditionalSourceVariables] = useState(
      []
    );
    const [transformationSpec, setTransformationSpec] = useState({});
    const canvasRef = useRef();

    const { refetch } = useQuery(
      ["getDatasheetAccess"],
      () => {
        return getDatasheetAccess(
          accessToken,
          datasheetStore.datasheetId,
          datasheetStore.datasheetDetails?.databook_id
        );
      },
      {
        cacheTime: 0,
        refetchOnWindowFocus: false,
        enabled: datasheetStore.datasheetDetails?.databook_id ? true : false,
        onSettled: (data, error) => {
          if (error) {
            console.log("Error occurred:", error);
          } else if (data) {
            setDatasheetRestrictedAccessBanner(
              !data.customObjectPermission || data.datasheetRestrictedAccess
            );
          }
        },
      }
    );

    // additional variables have been updated when ever canvas details api triggered
    useEffect(() => {
      setAdditionalSourceVariables(
        datasheetStore.datasheetDetails?.additional_source_variables || []
      );
      setTransformationSpec(
        datasheetStore.datasheetDetails?.transformation_spec || {}
      );
    }, [datasheetStore.datasheetDetails]);

    // additional variables have been updated when ever edit details api triggered.
    useEffect(() => {
      setAdditionalSourceVariables(
        datasheetStore.datasheetDetailsForEdit?.additional_source_variables ||
          []
      );
      setTransformationSpec(
        datasheetStore.datasheetDetailsForEdit?.transformation_spec || {}
      );
    }, [datasheetStore.datasheetDetailsForEdit]);

    useEffect(() => {
      // Clear the timeout for the sync status if it exists when the datasheet ID changes
      if (syncStatusTimeoutRef.current != null) {
        clearTimeout(syncStatusTimeoutRef.current);
      }
    }, [datasheetStore.datasheetId]);

    useEffect(() => {
      // This refetches to check if the user has restricted access to the current datasheet
      if (datasheetStore.datasheetDetails?.databook_id) {
        refetch();
      }
    }, [
      datasheetStore.datasheetId,
      datasheetStore.datasheetDetails?.databook_id,
    ]);

    useEffect(() => {
      setIsDatasheetGenerated(
        datasheetStore.datasheetDetails.is_datasheet_generated
      );
    }, [datasheetStore.datasheetDetails?.is_datasheet_generated]);

    useEffect(() => {
      setIsDatasheetNotAvailable(false);
    }, [datasheetStore.datasheetId]);

    useEffect(() => {
      // Check if the active sync is present and sync is not already in progress
      if (datasheetStore.datasheetDetails?.is_active_sync_present === true) {
        if (!datasheetStore.isSyncInProgress) {
          // Set the sync in progress state to true
          datasheetStore.setIsSyncInProgress(true);
          // Enable polling to fetch data periodically
          setTimeout(() => {
            setIsPolling(true);
          }, DATASHEET_POLLING_RATE);
        }
      } else {
        datasheetStore.setIsSyncInProgress(false);
        setIsPolling(false);
      }
    }, [
      datasheetStore.datasheetDetails, // Watch for changes in is_active_sync_present
      // Watch for changes in isSyncInProgress
      datasheetStore.datasheetId,
      datasheetStore.databookId,
    ]);

    useQuery(
      ["getDatasheetSyncStatus"],
      () => {
        return fetchData(
          `/datasheets/${datasheetStore.datasheetId}/sync_status`,
          "GET"
        );
      },
      {
        refetchInterval: isPolling ? DATASHEET_POLLING_RATE : false,
        enabled: isPolling,
        onSettled: (data, error) => {
          if (error) return console.log("Error occurred:", error);
          const { sync_start_time, sync_status } = data;
          // when the sync is triggered for the first time
          // and the sync is not yet started
          if (data.sync_start_time !== null) {
            const startDate = new Date(sync_start_time);
            const endDate = new Date();
            const seconds = (endDate.getTime() - startDate.getTime()) / 1000;
            const showWarningMsg = seconds > DATASHEET_SYNC_THRESHOLD;
            // when component renders initially
            if (
              data.sync_end_time === "None" &&
              !datasheetStore.isSyncInProgress
            ) {
              datasheetStore.setIsSyncInProgress(true);
              if (showWarningMsg) {
                toast.remove("timeLimitWarning");
                toast.custom(
                  () => (
                    <EverHotToastMessage
                      type="warning"
                      description="Datasheet sync has been running for a long time. Please contact the support team"
                    />
                  ),
                  {
                    position: "top-center",
                    duration: Infinity,
                    id: "timeLimitWarning",
                  }
                );
                setIsPolling(false);
              } else {
                setIsPolling(true);
              }
            }

            // sync is running currently, but running for > threshold time
            if (datasheetStore.isSyncInProgress && showWarningMsg) {
              setIsPolling(false);
              toast.remove("timeLimitWarning");
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="warning"
                    description="Datasheet sync has been running for a long time. Please contact the support team"
                  />
                ),
                {
                  position: "top-center",
                  duration: Infinity,
                  id: "timeLimitWarning",
                }
              );
            }

            // if sync has completed and the inprogress state
            // is true in the component stop pollng and loading
            if (
              data.sync_end_time !== "None" &&
              datasheetStore.isSyncInProgress
            ) {
              setIsPolling(false);

              // if too long banner is being displayed, stop it
              // since sync has finished running
              toast.remove("timeLimitWarning");

              if (sync_status === "complete") {
                if (
                  datasheetStore.datasheetDetails.is_stale ||
                  !datasheetStore.datasheetDetails_is_datasheet_generated
                ) {
                  const viewByData = cloneDeep(datasheetStore.viewByData);
                  let found = false;
                  // find the datasheet and update the last generated at time
                  for (const ele of viewByData) {
                    if (ele.id === data.databook_id) {
                      for (const sheet of ele.datasheets) {
                        if (sheet.datasheet_id === data.datasheet_id) {
                          sheet.last_generated_at = data.sync_start_time;
                          found = true;
                          break;
                        }
                      }
                      if (found) break;
                    }
                  }
                  datasheetStore.setViewByData(viewByData);
                }
                setIsDatasheetGenerated(true);
                datasheetStore.refetchDatasheetDetails();
                canvasRef.current?.dsDataRefetch();
                datasheetStore.adjustmentTableDataRefetch();
                setShowGenerateSheetModal(false);
              } else if (
                sync_status === "failed" ||
                sync_status === "partially_failed"
              ) {
                if (data.error_info == "row_limit_exceeded") {
                  const defaultMessage =
                    "The datasheet generation has failed because the number of rows produced in certain datasheets has exceeded the threshold limit. To increase the limit, please contact Everstage support.";
                  toast.custom(
                    () => (
                      <EverHotToastMessage
                        type="error"
                        description={
                          data.error_details?.message || defaultMessage
                        }
                      />
                    ),
                    { position: "top-center", duration: 4000 }
                  );
                } else {
                  toast.custom(
                    () => (
                      <EverHotToastMessage
                        type="error"
                        description={
                          "Error in generating datasheet. Please contact the support team for help"
                        }
                      />
                    ),
                    { position: "top-center", duration: 4000 }
                  );
                }
              } else if (sync_status == "lock_failed") {
                toast.custom(
                  () => (
                    <EverHotToastMessage
                      type="error"
                      description={
                        "Another process is trying to update the datasheet. Please try again later"
                      }
                    />
                  ),
                  { position: "top-center", duration: 4000 }
                );
              } else {
                toast.custom(
                  () => (
                    <EverHotToastMessage
                      type="error"
                      description={"Datasheet sync failed"}
                    />
                  ),
                  { position: "top-center", duration: 4000 }
                );
              }
              datasheetStore.setIsSyncInProgress(false);
            }
          }
        },
      }
    );

    const refreshDatasheetRequest = useMutation(
      (data) =>
        fetchData(
          `/datasheets/${datasheetStore.datasheetId}/refresh`,
          "POST",
          data
        ),
      {
        onError: (error) => {
          setIsPolling(false);
          datasheetStore.setIsSyncInProgress(false);

          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={
                  error.message ||
                  "Error in generating datasheet. Please contact the support team for help"
                }
              />
            ),
            { position: "top-center" }
          );
        },
        onSuccess: () => {
          datasheetStore.setIsSyncInProgress(true);
          // wait for 5 seconds before polling unless backend is process the previous sync request
          const details = cloneDeep(datasheetStore.datasheetDetails);
          details.is_active_sync_present = true;
          datasheetStore.setDatasheetDetails(details);
          syncStatusTimeoutRef.current = setTimeout(() => {
            setIsPolling(true);
          }, DATASHEET_POLLING_RATE);
        },
      }
    );

    const handleRefreshDatasheet = useCallback(
      debounce(async (isForceInvalidate = false) => {
        const reqParams = {
          isReportDataStale: datasheetStore.datasheetDetails.is_stale,
          isForceInvalidate: isForceInvalidate,
          additionalSourceVariables: additionalSourceVariables,
          transformationSpec: transformationSpec,
        };

        await refreshDatasheetRequest.mutate(reqParams);
      }, 200), // Debounce time in milliseconds
      [
        refreshDatasheetRequest.mutate,
        datasheetStore.datasheetDetails.is_stale,
        additionalSourceVariables,
        transformationSpec,
      ]
    );

    // async function handleGenerateDatasheet(isForceInvalidate = false) {
    //   const reqParams = {
    //     isReportDataStale: datasheetStore.datasheetDetails.is_stale,
    //     isForceInvalidate: isForceInvalidate,
    //   };
    //   await refreshDatasheetRequest.mutate(reqParams);
    // }

    return (
      <div
        className={twMerge("flex flex-col flex-auto gap-1 relative")}
        id="datasheet-canvas-view"
      >
        {isDatasheetNotAvailable ? (
          objectRestrictedMessage ? (
            <PageNotFound
              title={objectRestrictedMessage}
              imgSrc={lostAstronaut}
              subTitle="Please reach out to your Everstage admin"
            />
          ) : (
            <PageNotFound
              title="404"
              imgSrc={lostAstronaut}
              subTitle="Sorry, the datasheet you visited does not exist."
              showButton
              redirectUrl={"/"}
              buttonLabel="Back Home"
            />
          )
        ) : datasheetStore.showErrorPage ? (
          <div className="w-full h-full">
            <div className="flex w-full justify-end">
              <VersionToggleButton sendMessage={sendMessage} />
            </div>
            <div className="w-full h-full flex flex-col gap-2 items-center justify-center">
              <img src={lostAstronaut} />{" "}
              <EverTg.Heading2>
                There was an error while fetching details of this datasheet.
              </EverTg.Heading2>
              <EverTg.Description>
                Please try again or contact Everstage support.
              </EverTg.Description>
            </div>
          </div>
        ) : (
          <>
            <HeaderPanel
              onUpdateDatasheet={handleRefreshDatasheet}
              showGenerateSheetModal={showGenerateSheetModal}
              setShowGenerateSheetModal={setShowGenerateSheetModal}
              setIsDatasheetNotAvailable={setIsDatasheetNotAvailable}
              isRefreshRequestLoading={refreshDatasheetRequest.isLoading}
              hasFlushApiAttemptedRef={hasFlushApiAttemptedRef}
              sendMessage={sendMessage}
              activeKey={activeKey}
              setObjectRestrictedMessage={setObjectRestrictedMessage}
              pivotConfig={pivotConfig}
              refetchForceSkippedDatasheetIds={refetchForceSkippedDatasheetIds}
            />
            <DataTabPanel
              onGenerateDatasheet={handleRefreshDatasheet}
              canvasRef={canvasRef}
              isDatasheetGenerated={isDatasheetGenerated}
              setShowGenerateSheetModal={setShowGenerateSheetModal}
              showGenerateSheetModal={showGenerateSheetModal}
              isRefreshRequestLoading={refreshDatasheetRequest.isLoading}
              activeKey={activeKey}
              setActiveKey={setActiveKey}
              pivotConfig={pivotConfig}
              setPivotConfig={setPivotConfig}
              pivotConfigPayload={pivotConfigPayload}
              setPivotConfigPayload={setPivotConfigPayload}
            />
          </>
        )}
        {datasheetRestrictedAccessBanner && (
          <LimitedVisibilityMessage
            setDatasheetRestrictedAccessBanner={
              setDatasheetRestrictedAccessBanner
            }
          />
        )}
      </div>
    );
  }
);

function LimitedVisibilityMessage({ setDatasheetRestrictedAccessBanner }) {
  return (
    <div className="absolute top-0 right-0 w-auto h-auto">
      <EverHotToastNotification
        type="info"
        title="You have limited access to data in this sheet"
        description="Please reach out to your administrator for more details."
        onClose={() => {
          setDatasheetRestrictedAccessBanner(false);
        }}
      />
    </div>
  );
}
