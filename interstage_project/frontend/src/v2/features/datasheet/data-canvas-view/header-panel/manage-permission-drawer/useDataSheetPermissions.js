import { cloneDeep, isEmpty, isNumber } from "lodash";
import React, { useState, useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";

import { useAuthStore } from "~/GlobalStores/AuthStore";

import {
  getDataSheetPermissions,
  updateDataSheetPermissions,
} from "../../../restApi";

export const UPDATE_USERS = "UPDATE_USERS";
export const UPDATE_USER_GROUPS = "UPDATE_USER_GROUPS";
const INITIAL_PERMISSION_SET_NAME = "Create New Permission Set";
const DEFAULT_FILTER_OBJECT = {
  /** filterId needs to be updated when used */
  filterId: uuidv4(),
  type: "FILTER",
  isValid: false,
  colName: null,
  colDisplayName: null,
  dataType: null,
  operator: null,
  needsOperand: false,
  multiValued: false,
  valueCategory: null,
  value: null,
};

const isFilterValid = (filter) => {
  return filter.isValid;
};
const areFiltersValid = (filters) => {
  for (const filter of filters) {
    const filterValid = isFilterValid(filter);
    if (!filterValid) return false;
  }

  return true;
};

export const useDataSheetPermissions = ({
  databookId,
  datasheetId,
  updatedVariables,
}) => {
  const [
    isDataSheetPermissionFetchLoading,
    setIsDataSheetPermissionFetchLoading,
  ] = useState(false);
  const [
    isDataSheetPermissionSaveLoading,
    setIsDataSheetPermissionSaveLoading,
  ] = useState(false);
  const [savedPermissions, setSavedPermissions] = useState([]);
  const [currentPermissions, setCurrentPermissions] = useState([]);

  const [activePermissions, setActivePermissions] = useState([]);

  const isDataModified = useRef(false);

  const { accessToken } = useAuthStore();

  useEffect(() => {
    setIsDataSheetPermissionFetchLoading(true);
    getDataSheetPermissions(databookId, datasheetId, accessToken)
      .then((datasheetPermissionResponse) => {
        let permissions = datasheetPermissionResponse.permissionSet;
        if (isEmpty(permissions)) {
          permissions = [
            {
              permissionSetId: uuidv4(),
              permissionSetName: INITIAL_PERMISSION_SET_NAME,
              users: [],
              userGroups: [],
              columnsToBeHidden: [],
              filterList: [],
            },
          ];
        }
        setSavedPermissions(permissions);
        setCurrentPermissions(cloneDeep(permissions));
        setActivePermissions([permissions[0].permissionSetId]);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setIsDataSheetPermissionFetchLoading(false);
      });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const resetPermissions = () => {
    setCurrentPermissions(cloneDeep(savedPermissions));
    isDataModified.current = false;
  };

  const getNewPermissionName = (initialPermissionName) => {
    const permissionNames = new Set(
      currentPermissions.map((permission) => permission.permissionSetName)
    );

    let newPermissionName = initialPermissionName;
    let index = 1;
    while (permissionNames.has(newPermissionName)) {
      newPermissionName = `${initialPermissionName} (${index})`;
      index++;
    }

    return newPermissionName;
  };

  const onAddNewPermission = () => {
    const newPermission = {
      permissionSetId: uuidv4(),
      permissionSetName: getNewPermissionName(INITIAL_PERMISSION_SET_NAME),
      users: [],
      userGroups: [],
      columnsToBeHidden: [],
      filterList: [],
    };

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions, newPermission]);
  };

  const onPermissionNameChange = (permissionSetId, value) => {
    const clonedCurrentPermissions = cloneDeep(currentPermissions);
    // Find the permission set being updated
    const existingPermission = clonedCurrentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );

    if (existingPermission) {
      // Normalize the new permission name for case-insensitive comparison
      const normalizedNewName = value?.trim()?.toLowerCase();

      // Check if the new permission name is unique across all permissions
      const isPermissionNameUnique = clonedCurrentPermissions.every(
        (permission) => {
          // Skip the permission being updated
          if (permission.permissionSetId === permissionSetId) {
            return true;
          }
          return (
            permission.permissionSetName?.trim()?.toLowerCase() !==
            normalizedNewName
          );
        }
      );
      const normalizedExistingName = existingPermission.permissionSetName
        ?.trim()
        ?.toLowerCase();
      // Reset duplicate flags on the first permission that was previously marked as duplicates
      // of the permission being updated
      clonedCurrentPermissions.every((permission) => {
        if (
          permission.permissionSetId !== existingPermission.permissionSetId &&
          permission.permissionSetName?.trim()?.toLowerCase() ===
            normalizedExistingName
        ) {
          permission.isDuplicate = false;
          return false;
        }
        return true;
      });

      // Update the permission name and set duplicate flag if name is not unique
      existingPermission.permissionSetName = value;
      existingPermission.isDuplicate = !isPermissionNameUnique;
      isDataModified.current = true;
      setCurrentPermissions(clonedCurrentPermissions);
    }
  };

  const onClonePermission = (permissionSetId) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );

    const clonedPermission = cloneDeep(existingPermission);
    clonedPermission.permissionSetId = uuidv4();
    clonedPermission.permissionSetName = getNewPermissionName(
      existingPermission.permissionSetName
    );
    for (const filter of clonedPermission.filterList) {
      filter.filterId = uuidv4();
    }

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions, clonedPermission]);
  };

  const onDeletePermission = (permissionSetId) => {
    const filteredPermissions = currentPermissions.filter(
      (permission) => permission.permissionSetId !== permissionSetId
    );

    isDataModified.current = true;
    setCurrentPermissions(filteredPermissions);
  };

  const onHideColumnsChange = (permissionSetId, columns) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    existingPermission.columnsToBeHidden = columns;

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onAddRowPermission = (permissionSetId) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    existingPermission.filterList = [
      ...existingPermission.filterList,
      { ...DEFAULT_FILTER_OBJECT, filterId: uuidv4() },
    ];

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onFilterColumnChange = (permissionSetId, filterId, option) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    const filter = existingPermission.filterList.find(
      (filter) => filter.filterId === filterId
    );

    filter.isValid = false;
    filter.colName = option ? option.value : null;
    filter.colDisplayName = option ? option.label : null;
    filter.dataType = option ? option.dataType : null;
    filter.operator = null;
    filter.needsOperand = false;
    filter.multiValued = false;
    filter.valueCategory = null;
    filter.value = null;

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onFilterOperatorChange = (permissionSetId, filterId, option) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    const filter = existingPermission.filterList.find(
      (filter) => filter.filterId === filterId
    );
    filter.operator = option ? option.value : null;
    filter.needsOperand = option ? option.needsOperand : false;
    filter.multiValued = option ? option.multiValued : false;
    filter.isValid = option ? !option.needsOperand : false;
    filter.value = null;
    filter.valueCategory = filter.needsOperand
      ? filter.multiValued
        ? "both"
        : filter.operator === "BELONGS_TO"
        ? "custom_value"
        : null
      : null;

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onFilterCategoryChange = (permissionSetId, filterId, valueCategory) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    const filter = existingPermission.filterList.find(
      (filter) => filter.filterId === filterId
    );
    filter.isValid = false;
    filter.valueCategory = valueCategory;
    filter.value = null;

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onFilterValueChange = (permissionSetId, filterId, value) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    const filter = existingPermission.filterList.find(
      (filter) => filter.filterId === filterId
    );
    filter.value = value;

    if (filter.multiValued) filter.isValid = !isEmpty(value);
    else {
      if (value.userSystemField) {
        filter.isValid = !isEmpty(value.filterValue);
      } else {
        filter.isValid =
          filter.dataType === "Integer" || filter.dataType === "Percentage"
            ? isNumber(value.filterValue)
            : !isEmpty(value.filterValue);
      }
    }

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onRemoveRowPermission = (permissionSetId, filterId) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    existingPermission.filterList = existingPermission.filterList.filter(
      (filter) => filter.filterId !== filterId
    );

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onToggleUserGroupSelection = (permissionSetId, userGroup) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    const existingUserGroupIds = existingPermission.userGroups.map(
      (userGroup) => userGroup.userGroupId
    );
    if (existingUserGroupIds.includes(userGroup.userGroupId)) {
      existingPermission.userGroups = existingPermission.userGroups.filter(
        (existingUserGroup) =>
          existingUserGroup.userGroupId !== userGroup.userGroupId
      );
    } else {
      existingPermission.userGroups.push({
        userGroupId: userGroup.userGroupId,
        userGroupName: userGroup.userGroupName,
      });
    }

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onToggleUserSelection = (permissionSetId, user) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    const existingUserIds = existingPermission.users.map(
      (user) => user.employeeEmailId
    );
    if (existingUserIds.includes(user.employeeEmailId)) {
      existingPermission.users = existingPermission.users.filter(
        (existingUser) => existingUser.employeeEmailId !== user.employeeEmailId
      );
    } else {
      existingPermission.users.push({
        employeeEmailId: user.employeeEmailId,
        fullName: user.fullName,
      });
    }

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const onUpdateSelection = (permissionSetId, objects) => {
    const existingPermission = currentPermissions.find(
      (permission) => permission.permissionSetId === permissionSetId
    );
    for (const [updateKey, selectedValues] of Object.entries(objects)) {
      if (updateKey === UPDATE_USERS) {
        existingPermission.users = selectedValues;
      } else if (updateKey === UPDATE_USER_GROUPS) {
        existingPermission.userGroups = selectedValues;
      }
    }

    isDataModified.current = true;
    setCurrentPermissions([...currentPermissions]);
  };

  const arePermissionsValid = () => {
    const arePermissionNamesAvailable = currentPermissions
      .map((permission) => permission.permissionSetName)
      .every((permissionSetName) => !isEmpty(permissionSetName));
    if (!arePermissionNamesAvailable)
      return {
        isValid: false,
        message: "Please provide valid permission names",
      };

    const permissionNames = currentPermissions.map((permission) =>
      permission.permissionSetName.trim()
    );
    const uniquePermissionNames = new Set(permissionNames);
    if (uniquePermissionNames.size !== permissionNames.length)
      return {
        isValid: false,
        message: "Please provide valid and unique permission names",
      };

    for (const permission of currentPermissions) {
      const selectedValues = [...permission.users, ...permission.userGroups];
      if (isEmpty(selectedValues))
        return {
          isValid: false,
          message:
            "Please select any one of the user or groups to apply restriction",
        };
    }

    for (const permission of currentPermissions) {
      const filtersValid = areFiltersValid(permission.filterList);

      if (!filtersValid) {
        return {
          isValid: false,
          message: "Please provide valid row permission filters",
        };
      }
      if (
        isEmpty(permission.filterList) &&
        isEmpty(permission.columnsToBeHidden)
      ) {
        return {
          isValid: false,
          message:
            "Please provide valid row permission filters or valid columns to hide",
        };
      }
    }

    return { isValid: true };
  };

  const onSavePermissions = async () => {
    setIsDataSheetPermissionSaveLoading(true);

    const formattedPermissions = [];

    for (const permission of currentPermissions) {
      const columnsUsed = [];
      let variableIdMap = {};

      columnsUsed.push(...permission.columnsToBeHidden);
      permission.filterList.forEach((filter) => {
        columnsUsed.push(filter.colName);
      });

      [...new Set(columnsUsed)].forEach((column) => {
        const variable = updatedVariables.find(
          (variable) => variable.system_name === column
        );
        if (variable) {
          variableIdMap[variable.system_name] = variable.variable_id;
        }
      });

      formattedPermissions.push({
        permissionSetId: permission.permissionSetId,
        permissionSetName: permission.permissionSetName,
        filterList: permission.filterList,
        columnsToBeHidden: permission.columnsToBeHidden,
        users: permission.users.map((user) => user.employeeEmailId),
        userGroups: permission.userGroups.map(
          (userGroup) => userGroup.userGroupId
        ),
        variableIdMap,
      });
    }

    return updateDataSheetPermissions(
      databookId,
      datasheetId,
      formattedPermissions,
      accessToken
    )
      .catch((error) => {
        throw new Error(
          error.errorMessage ?? "Error in saving datasheet permissions"
        );
      })
      .finally(() => {
        setIsDataSheetPermissionSaveLoading(false);
      });
  };

  return {
    isDataModified: isDataModified.current,
    isDataSheetPermissionFetchLoading,
    isDataSheetPermissionSaveLoading,
    datasheetPermissions: currentPermissions,
    activePermissions,
    onAddNewPermission,
    onPermissionNameChange,
    onClonePermission,
    onDeletePermission,
    onHideColumnsChange,
    onAddRowPermission,
    onFilterColumnChange,
    onFilterOperatorChange,
    onFilterCategoryChange,
    onFilterValueChange,
    onRemoveRowPermission,
    onToggleUserGroupSelection,
    onToggleUserSelection,
    onUpdateSelection,
    arePermissionsValid,
    setActivePermissions,
    resetPermissions,
    onSavePermissions,
  };
};
