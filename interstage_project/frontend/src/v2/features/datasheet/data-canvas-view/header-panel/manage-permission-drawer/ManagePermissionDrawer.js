import {
  CheckIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  CopyIcon,
  DotsHorizontalIcon,
  EditPencilIcon,
  PlusCircleIcon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import { InfoCircleIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { isEmpty, sortBy } from "lodash";
import React, { useState } from "react";
import { useQuery } from "react-query";
import { twMerge } from "tailwind-merge";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverButton,
  EverCollapse,
  EverDrawer,
  EverHotToastMessage,
  EverInput,
  EverLoader,
  EverModal,
  EverTg,
  EverTooltip,
  toast,
} from "~/v2/components";

import { DataSheetPermissionInnerLayout } from "./datasheet-permission-inner-layout";
import { useDataSheetPermissions } from "./useDataSheetPermissions";
import { getUserAssignedProperties } from "../../../restApi";
import useFetchApiWithAuth from "../../../useFetchApiWithAuth";
import { convertKeysToPascalCase } from "../../../utils";

function handleTextClick(evt) {
  evt.stopPropagation();
}

function DataSheetPermissionHeader({
  headerName,
  isActive,
  onPermissionNameChange,
  permission = {},
}) {
  const [typeStatus, setTypeStatus] = useState("cancel");

  const isInEditMode = isActive && typeStatus === "edit";
  const isDuplicate = permission?.isDuplicate || false;

  function handleTextChange(evt) {
    const value = evt.target.value;
    onPermissionNameChange(value);
  }

  function handleEditClick(evt) {
    evt.stopPropagation();
    setTypeStatus("edit");
  }

  function handleApplyClick(evt) {
    evt.stopPropagation();
    setTypeStatus("cancel");
  }

  const renderDuplicate = () => {
    if (isDuplicate) {
      return (
        <div className="flex items-center gap-2">
          <InfoCircleIcon className="w-5 h-5 text-ever-error" />
          <EverTg.Text className="text-ever-error-lite-content">
            This Permission set name already exists. Please enter a different
            name.
          </EverTg.Text>
        </div>
      );
    }
  };

  return (
    <>
      {isInEditMode ? (
        <div className="flex items-center gap-2">
          <EverInput
            bordered={true}
            value={headerName}
            className={twMerge("w-80", isDuplicate && "!border-ever-error")}
            onClick={handleTextClick}
            onChange={handleTextChange}
          />
        </div>
      ) : (
        <div className="flex items-center gap-4">
          <EverTg.SubHeading4 className="text-ever-base-content ml-2">
            {headerName}
          </EverTg.SubHeading4>
          {renderDuplicate()}
        </div>
      )}
      {isActive && (
        <div className="flex items-center gap-4">
          {typeStatus === "edit" ? (
            <div
              className="ml-2 flex   items-center justify-center cursor-pointer"
              onClick={handleApplyClick}
            >
              <CheckIcon
                className={`w-6 h-6 ${
                  isDuplicate
                    ? "text-ever-base-content-low"
                    : "text-ever-success"
                }`}
              />
            </div>
          ) : (
            <div
              className="ml-2 flex items-center justify-center cursor-pointer"
              onClick={handleEditClick}
            >
              <EditPencilIcon className="w-4 h-4 text-ever-base-content-mid" />
            </div>
          )}
          {renderDuplicate()}
        </div>
      )}
    </>
  );
}

export function ManagePermissionDrawer({
  visible,
  datasheetStore,
  setDrawerOpen,
}) {
  const {
    isDataModified,
    isDataSheetPermissionFetchLoading,
    isDataSheetPermissionSaveLoading,
    datasheetPermissions,
    resetPermissions,
    onAddNewPermission,
    onPermissionNameChange,
    onHideColumnsChange,
    onAddRowPermission,
    onFilterColumnChange,
    onFilterOperatorChange,
    onFilterCategoryChange,
    onFilterValueChange,
    onRemoveRowPermission,
    onToggleUserGroupSelection,
    onToggleUserSelection,
    onUpdateSelection,
    onClonePermission,
    onDeletePermission,
    onSavePermissions,
    arePermissionsValid,
    activePermissions,
    setActivePermissions,
  } = useDataSheetPermissions(datasheetStore);
  const { fetchData } = useFetchApiWithAuth();
  const { dataTypesById } = useVariableStore();
  const { accessToken } = useAuthStore();

  const [userGroups, setUserGroups] = useState([]);
  const [filtersOperators, setFiltersOperators] = useState([]);

  const [userAssignedProperties, setUserAssignedProperties] = useState([]);
  const [isModalVisible, setModalVisible] = useState(false);

  const permissionsValidity = arePermissionsValid();
  const isSaveDisabled = !permissionsValidity.isValid;
  const validityErrorText = permissionsValidity.message ?? "";

  const { isLoading } = useQuery(
    ["getUserGroups"],
    () => {
      return fetchData("spm/user-groups-v2", "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSettled: (data, error) => {
        if (error) {
          console.log("Error", error);
        } else {
          setUserGroups(data);
        }
      },
    }
  );

  useQuery(
    ["getPermissionFilter"],
    () => {
      return fetchData("datasheets/filter-operators", "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSettled: (data, error) => {
        if (error) {
          console.log("Error:", error);
        } else {
          setFiltersOperators(convertKeysToPascalCase(data));
        }
      },
    }
  );

  useQuery(
    ["getUserAssignedProperties"],
    () => {
      return getUserAssignedProperties(accessToken);
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSettled: (data, error) => {
        if (error) {
          console.log("Error", error);
        } else {
          setUserAssignedProperties(data);
        }
      },
    }
  );

  function handleCloseInternal() {
    setModalVisible(false);
    resetPermissions();
    setDrawerOpen(false);
  }

  function onSavePermissionsWrapper() {
    const saveLoading = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Saving the datasheet permissions..."}
        />
      ),
      { position: "top-center", duration: Number.POSITIVE_INFINITY }
    );
    onSavePermissions()
      .then(() => {
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description={"Datasheet permission saved successfully"}
            />
          ),
          { position: "top-center" }
        );
        datasheetStore.refetchDatasheetDetails();
        // saveAllSessionKd(currentUTCTimeMsInString());
        setDrawerOpen(false);
      })
      .catch((error) => {
        toast.custom(
          () => (
            <EverHotToastMessage type="error" description={error.message} />
          ),
          { position: "top-center" }
        );
        //message.error(error.message);
      })
      .finally(() => {
        toast.remove(saveLoading);
      });
  }

  function extraItem(permission) {
    return (
      <div className="ml-auto" onClick={(event) => event.stopPropagation()}>
        <Dropdown
          overlay={
            <Menu>
              <Menu.Item
                onClick={() => onClonePermission(permission.permissionSetId)}
                key="clone"
              >
                <div className="flex gap-3">
                  <CopyIcon className="w-5 h-5 text-ever-base-content-mid" />
                  <EverTg.Caption className="text-ever-base-content">
                    Clone
                  </EverTg.Caption>
                </div>
              </Menu.Item>
              <Menu.Item
                onClick={() => onDeletePermission(permission.permissionSetId)}
                key="delete"
                className="hover:!bg-ever-error-lite"
              >
                <div className="flex gap-3">
                  <Trash03Icon className="w-5 h-5 text-ever-error" />
                  <EverTg.Caption className="text-ever-error-lite-content">
                    Delete
                  </EverTg.Caption>
                </div>
              </Menu.Item>
            </Menu>
          }
          trigger={["click"]}
        >
          <DotsHorizontalIcon className="w-5 h-5 text-ever-base-content" />
        </Dropdown>
      </div>
    );
  }

  function handleCancel() {
    if (isDataModified) {
      setModalVisible(true);
    } else {
      handleCloseInternal();
      return;
    }

    return;
  }

  function handleCollapseChange(keys) {
    setActivePermissions(keys);
  }

  function getDropDownOperators() {
    const operatorsMap = {};
    // Create operatorsMap for all columns's datatype
    for (const value of Object.values(dataTypesById)) {
      operatorsMap[value] = [];
    }

    // Add the avaiable operators for datatypes
    const dsFilterOperators = filtersOperators || [];
    for (const dsFilterOperator of dsFilterOperators) {
      const operandTypeIds = dsFilterOperator.operandTypeIds;
      for (const operandTypeId of operandTypeIds.filter(
        (operandTypeId) => operatorsMap[dataTypesById[operandTypeId]]
      )) {
        const datatype = dataTypesById[operandTypeId];
        const operatorOptions = {
          multiValued: dsFilterOperator.multiValued,
          needsOperand: dsFilterOperator.needsOperand,
          label: dsFilterOperator.altName,
          value: dsFilterOperator.name,
        };
        operatorsMap[datatype].push(operatorOptions);
      }
    }
    if (!isEmpty(operatorsMap["Email"])) {
      operatorsMap["Email"] = [
        ...operatorsMap["Email"],
        {
          multiValued: false,
          needsOperand: true,
          label: "Belongs To",
          value: "BELONGS_TO",
        },
      ];
    }

    // Sort operators based on label
    for (const [dataType, operators] of Object.entries(operatorsMap)) {
      operatorsMap[dataType] = sortBy(operators, ["label"]);
    }

    return operatorsMap;
  }

  return (
    <>
      <EverModal.Confirm
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => {
              setModalVisible(false);
            }}
            type="ghost"
          >
            No, Cancel
          </EverButton>,
          <EverButton
            type="filled"
            color="primary"
            key="accept"
            onClick={handleCloseInternal}
          >
            Yes, Close
          </EverButton>,
        ]}
        subtitle="Are you sure you want to leave this page?"
        title="Confirm!"
        visible={isModalVisible}
      ></EverModal.Confirm>
      <EverDrawer
        key={`${datasheetStore.databookId} ${datasheetStore.datasheetId}`} /** the drawer gets re-mounted for every databookId and datasheetId change  */
        title={<EverTg.Heading3>Manage Permissions</EverTg.Heading3>}
        height="calc(100% - 36px)"
        placement="top"
        closable={true}
        destroyOnClose={
          false
        } /** children unmounts and mounts on drawer open and clone. value has to be false */
        visible={visible}
        onClose={handleCancel}
        footer={
          <div className="flex gap-3 justify-end">
            <EverButton
              color="base"
              type="outlined"
              key="cancel"
              onClick={handleCancel}
            >
              Cancel
            </EverButton>
            <EverTooltip title={validityErrorText}>
              <EverButton
                key="submit"
                type="filled"
                color="primary"
                disabled={isSaveDisabled || isDataSheetPermissionSaveLoading}
                loading={isDataSheetPermissionSaveLoading}
                onClick={onSavePermissionsWrapper}
              >
                Save Permissions
              </EverButton>
            </EverTooltip>
          </div>
        }
      >
        <div className="w-full h-full">
          <div className="overflow-y-auto h-full px-14">
            {isDataSheetPermissionFetchLoading ? (
              <div className="w-full h-full flex items-center justify-center">
                <EverLoader.SpinnerLottie className="w-20 h-20" />
              </div>
            ) : (
              <>
                <EverTg.Heading3 className="mb-2">Permissions</EverTg.Heading3>
                <br />
                <EverTg.Text className="text-ever-base-content-mid mb-6">
                  Helps you control the visibility of rows and columns to users
                  who access this sheet. These restrictions won&rsquo;t apply to
                  users who can edit datasheets.
                </EverTg.Text>

                <EverCollapse
                  activeKey={activePermissions}
                  bordered={false}
                  className="mt-6 bg-ever-base-25 header-even-padding datasheet-permissions flex flex-col gap-4"
                  onChange={handleCollapseChange}
                  expandIcon={() => null}
                >
                  {datasheetPermissions.map((permission) => (
                    <EverCollapse.Panel
                      key={permission.permissionSetId}
                      header={
                        <div className="flex items-center">
                          {activePermissions.includes(
                            permission.permissionSetId
                          ) ? (
                            <ChevronDownIcon className="w-5 h-5 text-ever-base-content" />
                          ) : (
                            <ChevronRightIcon className="w-5 h-5 text-ever-base-content" />
                          )}
                          <DataSheetPermissionHeader
                            headerName={permission.permissionSetName}
                            isActive={activePermissions.includes(
                              permission.permissionSetId
                            )}
                            onPermissionNameChange={(value) =>
                              onPermissionNameChange(
                                permission.permissionSetId,
                                value
                              )
                            }
                            permission={permission}
                          />
                          {extraItem(permission)}
                        </div>
                      }
                      className="dataSheetPermissionsHeader border border-solid border-ever-base-400"
                    >
                      <DataSheetPermissionInnerLayout
                        permission={permission}
                        dropDownOperators={getDropDownOperators()}
                        userAssignedProperties={userAssignedProperties}
                        allUserGroups={userGroups}
                        allUserGroupsLoading={isLoading}
                        datasheetAllColumns={datasheetStore.updatedVariables.filter(
                          (variable) => variable.is_selected
                        )}
                        onHideColumnsChange={(columns) =>
                          onHideColumnsChange(
                            permission.permissionSetId,
                            columns
                          )
                        }
                        onAddRowPermission={() =>
                          onAddRowPermission(permission.permissionSetId)
                        }
                        onFilterColumnChange={(filterId, option) => {
                          onFilterColumnChange(
                            permission.permissionSetId,
                            filterId,
                            option
                          );
                        }}
                        onFilterOperatorChange={(filterId, option) =>
                          onFilterOperatorChange(
                            permission.permissionSetId,
                            filterId,
                            option
                          )
                        }
                        onFilterCategoryChange={(filterId, valueCategory) =>
                          onFilterCategoryChange(
                            permission.permissionSetId,
                            filterId,
                            valueCategory
                          )
                        }
                        onFilterValueChange={(filterId, value) =>
                          onFilterValueChange(
                            permission.permissionSetId,
                            filterId,
                            value
                          )
                        }
                        onRemoveRowPermission={(filterId) => {
                          onRemoveRowPermission(
                            permission.permissionSetId,
                            filterId
                          );
                        }}
                        onToggleUserGroupSelection={(userGroup) =>
                          onToggleUserGroupSelection(
                            permission.permissionSetId,
                            userGroup
                          )
                        }
                        onToggleUserSelection={(user) =>
                          onToggleUserSelection(
                            permission.permissionSetId,
                            user
                          )
                        }
                        onUpdateSelection={(objects) => {
                          onUpdateSelection(
                            permission.permissionSetId,
                            objects
                          );
                        }}
                      />
                    </EverCollapse.Panel>
                  ))}
                </EverCollapse>

                <div className="mt-3">
                  <EverButton
                    onClick={onAddNewPermission}
                    type="ghost"
                    color="base"
                    prependIcon={
                      <PlusCircleIcon className="w-5 h-5 text-ever-primary" />
                    }
                  >
                    <span className="align-middle">Add New Permission Set</span>
                  </EverButton>
                </div>
              </>
            )}
          </div>
        </div>
      </EverDrawer>
    </>
  );
}
