import React from "react";

import { useAuthStore } from "~/GlobalStores/AuthStore";

const useFetchApiWithAuth = () => {
  // Retrieve the access token from the authentication store
  const { accessToken } = useAuthStore();

  // Define the fetchData function for making API requests
  const fetchData = async (url, method = "GET", body = null, signal = null) => {
    try {
      // Prepare the request options, including the access token in the Authorization header
      const options = {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: body ? JSON.stringify(body) : null,
      };
      if (signal) {
        options.signal = signal;
      }

      // Make the API request using fetch
      const response = await fetch(url, options);

      // Parse the response JSON
      const data = await response.json();

      // Check if the response is ok (status code in the 2xx range)
      if (!response.ok) {
        // If there's an error in the response data, extract the error message
        const errorMessage =
          data?.error?.message || data?.message || "Something went wrong";

        // Throw an Error object with the error message
        throw new Error(errorMessage);
      }

      // If the response is successful, return the data
      return data;
    } catch (error) {
      // Log the error to the console for debugging
      console.error("Error fetching data:", error);

      // Re-throw the error to propagate it to the caller
      throw error;
    }
  };

  // Return the fetchData function for use in components
  return { fetchData };
};

export default useFetchApiWithAuth;
