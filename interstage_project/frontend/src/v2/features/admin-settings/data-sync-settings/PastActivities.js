/* eslint-disable no-unused-vars */
import { gql, useQuery, useLazyQuery } from "@apollo/client";
import {
  AlertTriangleIcon,
  AlertOctagonIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment-timezone";
import React, {
  useCallback,
  useMemo,
  useState,
  useRef,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverButton,
  EverModal,
  EverTooltip,
  EverBadge,
  EverFormatter,
} from "~/v2/components";
import {
  everAgGridCallbacks,
  Pagination,
  everAgGridOptions,
} from "~/v2/components/ag-grid";

import DetailedActivityView from "./DetailedActivityView";
import { GET_ETL_STATUS_HISTORY, GET_SYNC_RUN_LOGS } from "./graphql";
import { PeriodAffected } from "./PeriodAffected";
import { groupSyncRunLog } from "./utils";

const GET_CLIENT_CONFIG = gql`
  query MyClientConfig {
    myClient {
      clientFeatures {
        enableViewSyncDetailsPermission
      }
    }
  }
`;

const DEFAULT_PAGE_SIZE = 20;

export const PastActivities = observer((props) => {
  const gridRef = useRef();
  const [displayPagination, setDisplayPagination] = useState(false);
  const [pageData, setPageData] = useState({});
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [gridApi, setGridApi] = useState(null);
  const [period, setPeriod] = useState(null);
  const [multiPeriodData, setMultiPeriodData] = useState({});
  const { getDefaultOptions } = everAgGridOptions;
  const [enableViewSyncDetailsPermission, setEnableViewSyncDetailsPermission] =
    useState(true);

  const setPaginationValues = (params) => {
    setPageData(everAgGridCallbacks.onPaginationChangedDefault(params));
  };

  const doOnFirstRender = (params) => {
    setDisplayPagination(true);
    everAgGridCallbacks.adjustColumnWidth(params);
  };

  const { refetch: etlHistoryRefetch } = useQuery(GET_ETL_STATUS_HISTORY, {
    variables: { pageNo: 1, pageSize: DEFAULT_PAGE_SIZE },
    fetchPolicy: "stand-by",
    notifyOnNetworkStatusChange: true,
  });

  const { data: clientData, refetch } = useQuery(GET_CLIENT_CONFIG, {
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (clientData) {
      if (clientData.myClient) {
        const { clientFeatures } = clientData.myClient;
        if (clientFeatures) {
          let clientFeaturesObj = clientFeatures;
          setEnableViewSyncDetailsPermission(
            clientFeaturesObj.enableViewSyncDetailsPermission
          );
        }
      }
    }
  }, [clientData]);

  const [getSyncRunLogs] = useLazyQuery(GET_SYNC_RUN_LOGS);

  const { userTimeZone } = props;
  const timeZone = moment.tz.zone(userTimeZone) ? userTimeZone : "UTC";

  const [showModal, setShowModal] = useState(false);
  const [selectedDetailedActivity, setSelectedDetailedActivity] =
    useState(undefined);

  const { hasPermissions } = useUserPermissionStore();
  const { t } = useTranslation();

  const durationFormatter = (rowData) => {
    const data = rowData.data;
    if (data?.syncEndTime && data?.syncStartTime) {
      const duration = moment.duration(
        moment(data.syncEndTime)
          .tz(timeZone)
          .diff(moment(data.syncStartTime).tz(timeZone))
      );
      let secs = Math.floor(duration.asSeconds());
      let mins = 0;
      let hrs = 0;
      if (secs > 59) {
        mins = Math.floor(secs / 60);
        secs = secs % 60;
        if (mins > 59) {
          hrs = Math.floor(mins / 60);
          mins = mins % 60;
        }
      }

      const hours = (hrs && `${hrs}h `) || "";
      const mintus = (mins && `${mins}m `) || "";
      return `${hours}${mintus}${secs}s`;
    } else {
      return "-";
    }
  };

  const columnDefs = [
    {
      field: "e2eSyncRunId",
      headerName: t("ID"),
    },
    {
      field: "task",
      headerName: t("ACTIVITY"),
      cellRenderer: (rowData) => {
        if (rowData) {
          const value = rowData.value;
          if (value === "Commission calculation") {
            return t("CALCULATE_COMMISSIONS");
          }
          return value;
        }
        return null;
      },
    },
    {
      field: "syncPeriod",
      headerName: t("PERIOD"),
      cellRenderer: (rowData) => {
        const { value, data } = rowData;
        // const periodData = props.multiPeriodSyncs[data.e2eSyncRunId];

        if (!value) return "-";

        if (value.includes(" - ")) {
          const [start, end] = value.split(" - ");
          const dateElement = (
            <>
              <EverFormatter.Date date={start.trim()} className="font-normal" />
              <span className="px-1 leading-none">-</span>
              <EverFormatter.Date date={end.trim()} className="font-normal" />
            </>
          );

          // Commented the code to avoid showing the period affected modal
          // if (periodData) {
          //   return (
          //     <EverTooltip title="View affected periods" placement="bottom">
          //       <div
          //         className="border-b border-ever-primary flex items-center [&_*]:text-ever-primary"
          //         onClick={() => {
          //           setPeriod(value);
          //           setMultiPeriodData(periodData);
          //         }}
          //       >
          //         {dateElement}
          //       </div>
          //     </EverTooltip>
          //   );
          // }

          return dateElement;
        }

        return (
          <EverFormatter.Date date={value.trim()} className="font-normal" />
        );
      },
    },
    {
      field: "syncStatus",
      headerName: t("STATUS"),
      cellRenderer: (rowData) => {
        const value = rowData.value;
        let showToolTip = false;
        if (value) {
          const status = value.toString().toLowerCase().replace(" ", "");
          const colorCode = statusColorCode[status];
          if (status === "partiallyfailed" || status === "failed") {
            showToolTip = true;
          }
          const result = (
            <div className={`flex mr-10 mb-0 gap-1 ${colorCode}`}>
              <EverBadge
                title={
                  value == "completed"
                    ? t("COMPLETED")
                    : value === "failed"
                    ? t("FAILED")
                    : value === "skipped"
                    ? t("SKIPPED")
                    : t("PARTIALLY_FAILED")
                }
                icon={renderLogo(value)}
                type={
                  value == "completed"
                    ? "success"
                    : value === "failed"
                    ? "error"
                    : value === "skipped"
                    ? "info"
                    : "warning"
                }
                className="rounded-full"
              />
            </div>
          );
          return showToolTip ? (
            <EverTooltip
              mouseEnterDelay={0.5}
              title={t("REACH_EVERSTAGE_SUPPORT_FOR_ASSISTANCE")}
            >
              {result}
            </EverTooltip>
          ) : (
            result
          );
        }
        return "-";
      },
    },
    {
      field: "syncStartTime",
      headerName: t("START_TIME"),
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (value) {
          return (
            <EverFormatter.DateTime
              className="font-normal"
              date={value}
              targetTimeZone={timeZone}
              type="shortWithZone"
            />
          );
        }
        return "-";
      },
    },
    {
      field: "syncEndTime",
      headerName: t("END_TIME"),
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (value) {
          return (
            <EverFormatter.DateTime
              className="font-normal"
              date={value}
              targetTimeZone={timeZone}
              type="shortWithZone"
            />
          );
        }
        return "-";
      },
    },
    {
      field: "",
      headerName: t("DURATION"),
      cellRenderer: durationFormatter,
      valueFormatter: durationFormatter,
    },
    {
      field: "updatedBy",
      headerName: t("INITIATED_BY"),
      cellRenderer: (rowData) => {
        const data = rowData.data;
        return (
          <EverTooltip mouseEnterDelay={0.5} title={rowData.value}>
            <span>{data.userName}</span>
          </EverTooltip>
        );
      },
    },
    {
      field: "",
      headerName: "",
      align: "right",
      pinned:
        (enableViewSyncDetailsPermission &&
          hasPermissions(RBAC_ROLES.VIEW_SYNC_DETAILS)) ||
        (!enableViewSyncDetailsPermission &&
          hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)) ||
        hasPermissions(RBAC_ROLES.MANAGE_CPQ_SETTINGS)
          ? "right"
          : null,
      cellRenderer: (rowData) => {
        if (
          (enableViewSyncDetailsPermission &&
            hasPermissions(RBAC_ROLES.VIEW_SYNC_DETAILS)) ||
          (!enableViewSyncDetailsPermission &&
            hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)) ||
          hasPermissions(RBAC_ROLES.MANAGE_CPQ_SETTINGS)
        ) {
          return (
            <EverButton
              size="small"
              className="ml-2.5"
              type="link"
              onClick={() => {
                getSyncRunLogs({
                  variables: {
                    e2eSyncRunId: rowData.data.e2eSyncRunId,
                    task: rowData.data.task,
                  },
                }).then(async (res) => {
                  setSelectedDetailedActivity(
                    JSON.parse(JSON.parse(res.data.syncRunLogs))
                  );
                  setShowModal(true);
                });
              }}
            >
              {t("VIEW_DETAILS")}
            </EverButton>
          );
        } else {
          return null;
        }
      },
    },
  ];

  const defaultColDef = useMemo(() => ({
    sortable: false,
  }));

  const renderLogo = (status) => {
    switch (status) {
      case "partially failed":
        return <AlertTriangleIcon className="h-3 w-3" />;
      case "failed":
        return <AlertOctagonIcon className="h-3 w-3" />;
      case "skipped":
        return <XCircleIcon className="h-3 w-3" />;
      case "completed":
        return <CheckCircleIcon className="h-3 w-3" />;
      default:
        return "";
    }
  };

  const statusColorCode = {
    completed: "text-ever-success-content-lite",
    partiallyfailed: "text-ever-warning-content-lite",
    failed: "text-ever-error-content-lite",
  };

  const [gridLoading, setGridLoading] = useState(false);

  useEffect(() => {
    if (gridApi) {
      const datasource = {
        async getRows(params) {
          const { endRow } = params.request;
          setGridLoading(true);
          const result = await etlHistoryRefetch({
            pageNo: endRow / pageSize,
            pageSize: pageSize,
          });
          setGridLoading(false);
          if (result?.data?.etlStatusHistory.length > 0) {
            params.success({
              rowData: result?.data?.etlStatusHistory,
              rowCount: result?.data?.etlStatusCount,
            });
          } else {
            params.success({ rowData: [], rowCount: 0 });
            params.api.showNoRowsOverlay();
          }
        },
      };
      gridApi.setGridOption("serverSideDatasource", datasource);
    }
  }, [gridApi, pageSize]);

  const onGridReady = useCallback((params) => {
    setGridApi(params.api);
  }, []);

  const customLoadingCellRenderer = () => {
    return "";
  };

  const loadingCellRenderer = useMemo(() => {
    return customLoadingCellRenderer;
  }, []);
  const loadingCellRendererParams = useMemo(() => {
    return {
      loadingMessage: t("ONE_MOMENT_PLEASE"),
    };
  }, [t]);

  return (
    <>
      <div className="ag-theme-material zebra-grid w-full h-[82vh]">
        <AgGridReact
          {...getDefaultOptions({ type: "sm" })}
          ref={gridRef}
          loading={gridLoading}
          onFirstDataRendered={doOnFirstRender}
          onPaginationChanged={setPaginationValues}
          loadingCellRenderer={loadingCellRenderer}
          loadingCellRendererParams={loadingCellRendererParams}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowModelType={"serverSide"}
          pagination={true}
          paginationPageSize={pageSize}
          cacheBlockSize={pageSize}
          animateRows={true}
          onGridReady={onGridReady}
        />
        {displayPagination && (
          <Pagination
            pageData={pageData}
            setPageSize={setPageSize}
            control={gridRef}
          />
        )}
      </div>
      <EverModal
        visible={showModal}
        width="1200px"
        zIndex={2000}
        destroyOnClose
        title={t("SYNC_SUMMARY")}
        closable={false}
        footer={
          <EverButton
            onClick={() => {
              setShowModal(false);
              setSelectedDetailedActivity(undefined);
            }}
          >
            {t("CLOSE")}
          </EverButton>
        }
      >
        <DetailedActivityView
          groupedSyncRunLog={groupSyncRunLog(
            selectedDetailedActivity ? selectedDetailedActivity : [],
            t
          )}
          userTimezone={userTimeZone}
        />
      </EverModal>
      {/* Commented the period affected modal to avoid showing the modal */}
      {/* <PeriodAffected
        showModal={!isEmpty(period)}
        period={period}
        multiPeriodData={multiPeriodData}
        onCancel={() => {
          setPeriod(null);
          setMultiPeriodData({});
        }}
      /> */}
    </>
  );
});
