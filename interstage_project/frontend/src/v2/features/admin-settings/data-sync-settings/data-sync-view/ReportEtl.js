import { useQuery } from "@apollo/client";
import { isNil } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverDatePicker,
  EverRadio,
  EverSelect,
  EverSwitch,
  Space,
  LazySelect,
  filterAndFormatOptions,
  useAbortiveLazyQuery,
} from "~/v2/components";

import {
  GET_ALL_REPORT_OBJECTS,
  DETAILED_SYNC_STATUS,
  GET_PAYEES,
} from "../graphql";
import { runReportETL } from "../restApi";

export const ReportEtl = observer((props) => {
  const { store, renderPreparingText, isValidUser, startPolling, handleError } =
    props;
  const {
    reportObjectId,
    setReportObjectId,
    reportEtlButtonLoading,
    setStatusBarVisible,
    setReportEtlButtonLoading,
    setTaskStatus,
    taskStatus,
    setStartSync,
    currentJob,
    supportUserEmail,
    setIsModalVisible,
    setCurrentJob,
    setDetailedStatus,
    taskDetail,
    reportEtlMode,
    setReportEtlMode,
    selectedRoEtlPayeeList,
    setSelectedRoEtlPayeeList,
    roEtlPayeeOption,
    setRoEtlPayeeOption,
    reportEtlDate,
    setReportEtlDate,
    enableReportEtlButton,
    setEnableReportEtlButton,
    isDateRequired,
    setIsDateRequired,
  } = store;
  const { accessToken, email } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const [reportObjects, setReportObjects] = useState([]);
  const [disableModeSwitch, setDisableModeSwitch] = useState(false);

  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const resetForm = () => {
    setReportObjectId(null);
    setIsDateRequired(false);
    setReportEtlMode("custom");
    setRoEtlPayeeOption("all-payees");
    setSelectedRoEtlPayeeList([]);
    setReportEtlDate(null);
    setEnableReportEtlButton(false);
  };

  const { data: reportObjectData } = useQuery(GET_ALL_REPORT_OBJECTS, {
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (
      taskDetail?.task?.includes("Report ETL") &&
      hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)
    ) {
      reportEtlStartPolling(5000);
    }
  }, [taskDetail, hasPermissions]);

  useEffect(() => {
    if (supportUserEmail !== null) {
      if (currentJob === "report-etl") {
        handleReportEtl();
      }
    }
  }, [supportUserEmail]);

  const {
    startPolling: reportEtlStartPolling,
    stopPolling: reportEtlStopPolling,
  } = useQuery(DETAILED_SYNC_STATUS, {
    fetchPolicy: "no-cache",
    notifyOnNetworkStatusChange: true,
    skip:
      !hasPermissions([
        RBAC_ROLES.MANAGE_ALL_ADMINS,
        RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      ]) ||
      currentJob === "" ||
      currentJob !== "report-etl",
    onCompleted: (res) => {
      if (res?.detailedSyncStatus && currentJob === "report-etl") {
        const status = JSON.parse(res.detailedSyncStatus);
        console.log("### status", status);
        setDetailedStatus(status);
        if (status.sync_end_time !== "None") {
          reportEtlStopPolling();
        }
      } else {
        reportEtlStopPolling();
      }
    },
    onError: (error) => {
      console.log(error);
    },
  });

  useEffect(() => {
    if (reportObjectData && reportObjectData.allReportObjects) {
      let report_options = [];
      reportObjectData.allReportObjects.forEach((report) => {
        report_options.push({
          label: report.name,
          value: report.id,
        });
      });
      setReportObjects(report_options);
    }
  }, [reportObjectData]);

  const handleReportEtl = async () => {
    setStatusBarVisible(false);
    setCurrentJob("report-etl");
    if (isValidUser() && supportUserEmail === null) {
      setIsModalVisible(true);
    } else {
      setReportEtlButtonLoading(true);
      const response = await runReportETL(accessToken, {
        mode: reportEtlMode,
        reportObjectId: reportObjectId,
        email: isValidUser() ? supportUserEmail : email,
        payeeList: reportEtlMode === "custom" ? selectedRoEtlPayeeList : null,
        reportEtlPeriod:
          reportEtlMode === "custom" && isDateRequired
            ? moment(reportEtlDate).format("MM/01/YYYY")
            : null,
      });
      setTaskStatus(true);

      if (response.ok) {
        startPolling(5000);
        setStartSync(true);
        resetForm();
        if (hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS))
          reportEtlStartPolling(5000);
      } else {
        setTaskStatus(false);
        setReportEtlButtonLoading(false);
        handleError(response, "Report Etl task submission failed");
      }
    }
  };

  const filterOptions = (data) => !data.canUserManageAdmins;

  const [getPayee, { variables, abort }] = useAbortiveLazyQuery(GET_PAYEES, {
    fetchPolicy: "network-only",
    onCompleted: (data) => {
      const response = data?.employeeRoleDetails;
      if (!isNil(response)) {
        const formattedResponse = filterAndFormatOptions(
          response,
          { label: "fullName", value: "employeeEmailId" },
          filterOptions
        );
        variables.successCbk(formattedResponse, response);
      } else {
        variables.failureCbk();
      }
    },
    onError: () => {
      variables.failureCbk();
    },
  });

  const payeeLazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { searchTerm, offset, limit } = params;
      await getPayee({
        ...params,
        userStatus: "All",
        searchTerm,
        offsetValue: offset,
        limitValue: limit,
      });
    },
  };

  const commissionBasedEverObjects = [
    "settlement",
    "commission",
    "quota_attainment",
    "inter_commission",
    "inter_quota_attainment",
    "forecast_commission",
    "forecast_quota_attainment",
    "inter_forecast_commission",
    "inter_forecast_quota_attainment",
    "payout",
  ];

  const allModeObjects = [
    "commission_summary",
    "fx_rate",
    "statement_approvals",
    "quote",
    "quote_line_item",
    "quote_line_item_tier",
  ];

  const customCalendarEnabled = clientFeatures.customCalendar ?? false;

  return (
    <div className="pl-5 pt-3">
      <Space direction="vertical" size="middle">
        <EverSelect
          showSearch
          options={reportObjects}
          //suffixIcon={SelectArrow}
          onChange={(value) => {
            setReportObjectId(value);
            if (allModeObjects.includes(value) || customCalendarEnabled) {
              setDisableModeSwitch(true);
              setReportEtlMode("all");
              setEnableReportEtlButton(true);
            } else {
              setDisableModeSwitch(false);
              setIsDateRequired(commissionBasedEverObjects.includes(value));
              setReportEtlMode("custom");
              setRoEtlPayeeOption("all-payees");
              setSelectedRoEtlPayeeList([]);
              setReportEtlDate(null);
              setEnableReportEtlButton(false);
            }
          }}
          value={reportObjectId}
          placeholder="Choose report object"
          className="w-52"
          getPopupContainer={(trigger) => trigger.parentNode}
        />
        <EverSwitch
          size="small"
          defaultChecked={false}
          checked={reportEtlMode === "all"}
          disabled={disableModeSwitch}
          onChange={(status) => {
            setReportEtlMode(status ? "all" : "custom");
            setRoEtlPayeeOption("all-payees");
            setSelectedRoEtlPayeeList([]);
            setReportEtlDate(null);
            setEnableReportEtlButton(status || !isDateRequired);
          }}
          label={`Run for all payees and periods`}
        />
        {!isNil(reportObjectId) &&
          reportEtlMode == "custom" &&
          isDateRequired && (
            <Space direction="vertical" size="middle">
              <EverRadio.Group
                onChange={(event) => {
                  setRoEtlPayeeOption(event.target.value);
                  setSelectedRoEtlPayeeList([]);
                  setReportEtlDate(null);
                  setEnableReportEtlButton(
                    !isDateRequired && event.target.value === "all-payees"
                  );
                }}
                value={roEtlPayeeOption}
                className="flex gap-3 !mb-0"
              >
                <EverRadio value={"all-payees"} label="All Payees" />
                <EverRadio value={"selected-payees"} label="Selected Payees" />
              </EverRadio.Group>
              {roEtlPayeeOption == "selected-payees" && (
                <LazySelect
                  mode="multiple"
                  maxTagCount={3}
                  placeholder="Select payees"
                  value={selectedRoEtlPayeeList}
                  onChange={(values) => {
                    setSelectedRoEtlPayeeList(values);
                    setEnableReportEtlButton(
                      (!isDateRequired || !isNil(reportEtlDate)) &&
                        values.length > 0
                    );
                  }}
                  {...payeeLazyLoadProps}
                />
              )}
              {isDateRequired && (
                <EverDatePicker.Legacy
                  className="w-6/12"
                  picker="month"
                  format={"01/MM/YYYY"}
                  value={reportEtlDate}
                  onChange={(date) => {
                    setReportEtlDate(date);
                    setEnableReportEtlButton(
                      reportEtlMode === "all" ||
                        (!isNil(date) &&
                          (roEtlPayeeOption === "all-payees" ||
                            selectedRoEtlPayeeList.length > 0))
                    );
                  }}
                />
              )}
            </Space>
          )}
        {!isNil(reportObjectId) &&
          reportEtlMode == "custom" &&
          !isDateRequired && (
            <LazySelect
              mode="multiple"
              maxTagCount={3}
              placeholder="Select payees"
              value={selectedRoEtlPayeeList}
              onChange={(values) => {
                setSelectedRoEtlPayeeList(values);
                setEnableReportEtlButton(values.length > 0);
              }}
              className="w-full"
              {...payeeLazyLoadProps}
            />
          )}
        <div className="flex">
          <EverButton
            onClick={() => {
              handleReportEtl();
            }}
            //loading={reportEtlButtonLoading}
            disabled={
              taskStatus
                ? true
                : !enableReportEtlButton || isNil(reportObjectId)
            }
          >
            Run
          </EverButton>
          {reportEtlButtonLoading && renderPreparingText()}
        </div>
      </Space>
    </div>
  );
});
