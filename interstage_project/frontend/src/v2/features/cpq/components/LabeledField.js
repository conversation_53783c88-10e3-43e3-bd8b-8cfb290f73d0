import { InfoCircleIcon } from "@everstage/evericons/outlined";
import { AnimatePresence } from "framer-motion";
import { twMerge } from "tailwind-merge";

import { EverLabel, EverTooltip } from "~/v2/components";
import {
  AnimatedWrapper,
  ANIMATION_TYPES,
  ANIMATION_STATES,
} from "~/v2/features/cpq/components";

import { LABEL_CLASS } from "./constants";

function LabelWithHelpText({
  className = "",
  label,
  required,
  helpText,
  extraText,
}) {
  return (
    <div className="flex items-center">
      <EverLabel
        className={twMerge("mr-0 leading-4", className)}
        required={required}
      >
        {label}
      </EverLabel>
      {helpText && (
        <EverTooltip title={helpText}>
          <InfoCircleIcon className="w-4 h-4 ml-2 text-ever-base-content-low" />
        </EverTooltip>
      )}
      {extraText}
    </div>
  );
}

export function LabeledField({
  label,
  labelPlacement = "top",
  required = false,
  wrapperClassName = "",
  className = "",
  labelClassName = "",
  helpText = "",
  extraText = null,
  error = null,
  children,
}) {
  return (
    <div className={twMerge("flex flex-col gap-1.5", wrapperClassName)}>
      <div
        className={twMerge(
          "flex gap-3",
          LABEL_CLASS[labelPlacement],
          className
        )}
      >
        {label && (
          <LabelWithHelpText
            className={labelClassName}
            label={label}
            required={required}
            helpText={helpText}
            extraText={extraText}
          />
        )}
        {children}
      </div>
      <AnimatePresence initial={false}>
        {error?.message && (
          <AnimatedWrapper.Motion
            className="h-full"
            initial={ANIMATION_STATES.COLLAPSE}
            type={ANIMATION_TYPES.EXPAND_COLLAPSE}
          >
            <EverLabel className="text-ever-error-lite-content">
              {error.message}
            </EverLabel>
          </AnimatedWrapper.Motion>
        )}
      </AnimatePresence>
    </div>
  );
}
