import { debounce } from "lodash";
import { useRef, useState, useLayoutEffect } from "react";
import { twMerge } from "tailwind-merge";

import { EverTooltip, useCurrentTheme } from "~/v2/components";

const getLineClampClass = (lines) => {
  // Define all possible line-clamp classes that might be used
  const lineClampClasses = {
    1: "line-clamp-1",
    2: "line-clamp-2",
    3: "line-clamp-3",
    4: "line-clamp-4",
    5: "line-clamp-5",
    6: "line-clamp-6",
  };

  return lineClampClasses[lines] || "line-clamp-1";
};

export const TruncatedText = ({
  text,
  className = "",
  placement = "",
  children,
  theme = "dark",
  lines = 1,
}) => {
  const colors = useCurrentTheme();
  const textRef = useRef(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useLayoutEffect(() => {
    const element = textRef.current;
    if (!element) return;

    const checkIfTruncated = () => {
      if (lines === 1) {
        if (element.offsetWidth !== 0 && element.scrollWidth !== 0) {
          if (element.offsetWidth < element.scrollWidth) {
            setIsTruncated(true);
          } else {
            setIsTruncated(false);
          }
        }
      } else {
        if (element.offsetHeight !== 0 && element.scrollHeight !== 0) {
          if (element.offsetHeight < element.scrollHeight) {
            setIsTruncated(true);
          } else {
            setIsTruncated(false);
          }
        }
      }
    };

    const debouncedCheck = debounce(checkIfTruncated, 200);

    const resizeObserver = new ResizeObserver(debouncedCheck);
    resizeObserver.observe(element);

    checkIfTruncated();

    return () => {
      resizeObserver.unobserve(element);
      resizeObserver.disconnect();
      debouncedCheck.cancel();
    };
  }, [text]);

  const element = (
    <div
      ref={textRef}
      className={twMerge(
        lines === 1
          ? "whitespace-nowrap leading-normal truncate"
          : `${getLineClampClass(lines)} leading-normal`,
        className
      )}
    >
      {children}
    </div>
  );

  return isTruncated ? (
    <EverTooltip
      {...(theme === "light" && {
        color: colors.base["DEFAULT"],
        overlayInnerStyle: {
          color: colors.base["content"]["DEFAULT"],
        },
      })}
      title={text}
      {...(placement && { placement })}
    >
      {element}
    </EverTooltip>
  ) : (
    element
  );
};
