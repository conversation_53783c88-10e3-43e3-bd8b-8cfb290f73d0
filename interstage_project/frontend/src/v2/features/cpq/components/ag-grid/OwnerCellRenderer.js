import { EverGroupAvatar, EverTg } from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";

const OwnerCellRenderer = ({ ownerId, src }) => {
  if (ownerId === undefined) return null;
  return (
    <div className="flex items-center gap-3 overflow-hidden">
      <EverGroupAvatar
        avatars={[
          {
            name: ownerId,
            image: src,
            className: "w-8 h-8",
          },
        ]}
        groupMaxCount={1}
        limitInPopover={1}
      />
      <TruncatedText text={ownerId}>
        <EverTg.Text>{ownerId}</EverTg.Text>
      </TruncatedText>
    </div>
  );
};

export default OwnerCellRenderer;
