import { useFetch<PERSON>piWithAuth } from "~/v2/hooks";

import { validatePayload, formDataToObject } from "../utils";

const useFetchApi = (endPoints) => {
  const { fetchData } = useFetchApiWithAuth();

  const fetchEndpoint = (endpointKey, options = {}, dynamicParams) => {
    let endpoint = endPoints[endpointKey];
    if (!endpoint) {
      throw new Error(`Endpoint ${endpointKey} not defined`);
    }

    const { body = null, validateSchema, ...remainingOptions } = options;

    // Validate payload if validator is provided
    if (validateSchema && body) {
      const validation = validatePayload(
        validateSchema,
        body instanceof FormData ? formDataToObject(body) : body
      );
      if (!validation.success) {
        throw new Error(`Validation failed: ${validation.error}`);
      }
      // If validation passes, continue with the original body
    }

    // If the endpoint is a function, call it with dynamic parameters
    if (typeof endpoint === "function") {
      endpoint = endpoint(dynamicParams);
    }

    return fetchData(endpoint.url, endpoint.method, body, remainingOptions);
  };

  return { fetchEndpoint };
};

export default useFetchApi;
