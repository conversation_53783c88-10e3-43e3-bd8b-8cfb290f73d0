import { debounce } from "lodash";
import { useState, useRef, useMemo } from "react";

import { SEARCH_BOX } from "~/Enums";

const useDebouncedSearch = (
  searchCallback,
  debounceTime = SEARCH_BOX.DEBOUNCE_TIME
) => {
  const [searchState, setSearchState] = useState({
    userInput: "",
    searchQuery: "",
  });
  const tempSearchValue = useRef("");

  const debouncedSearch = useMemo(
    () =>
      debounce((searchTerm) => {
        setSearchState((prev) => ({
          ...prev,
          searchQuery: searchTerm,
        }));
        searchCallback({ searchTerm });
      }, debounceTime),
    [searchCallback, debounceTime]
  );

  const onSearch = (event) => {
    const value = event.target.value.trim();
    const searchQuery = value.length >= SEARCH_BOX.MINIMUM_CHARS ? value : "";
    if (
      (value.length >= SEARCH_BOX.MINIMUM_CHARS ||
        (tempSearchValue.current.length >= SEARCH_BOX.MINIMUM_CHARS &&
          value.length < SEARCH_BOX.MINIMUM_CHARS)) &&
      tempSearchValue.current !== value
    ) {
      debouncedSearch(searchQuery);
    }
    tempSearchValue.current = value;
    setSearchState((prev) => ({
      ...prev,
      userInput: event.target.value,
    }));
  };

  const onReset = () => {
    setSearchState({
      userInput: "",
      searchQuery: "",
    });
  };

  return { searchState, onSearch, onReset };
};

export default useDebouncedSearch;
