# Deal Room Components Development Guide

## Overview

This document provides comprehensive, step-by-step guidance for **creating new Deal Room components from scratch**. It covers the complete development process, from initial setup to production deployment.

**Target Audience:** Developers who want to create new components for the Deal Room system.

## Quick Start

If you're new to component development, follow this guide in order:

1. **[Naming Conventions](#naming-conventions)** - Understand the naming standards
2. **[File Structure](#file-structure-guidance)** - Know where to place files
3. **[Step-by-Step Creation](#step-by-step-component-creation)** - Complete tutorial with examples
4. **[Registry Integration](#registry-integration-examples)** - Add to the system
5. **[Form Field Integration](#form-field-integration-examples)** - Create properties panel

## Naming Conventions

Before starting, follow these naming conventions:

```
Component Type: CamelCase (e.g., CallToAction, PricingTable)
File Names: CamelCase.js (e.g., CallToAction.js, PricingTable.js)
Constants: UPPER_SNAKE_CASE (e.g., CALL_TO_ACTION, PRICING_TABLE)
Props: camelCase (e.g., buttonText, backgroundColor)
CSS Classes: kebab-case (e.g., cta-button, pricing-card)
```

## File Structure Guidance

### Where to Place Files

```
deal-room/
├── components/
│   └── builder-components/
│       └── YourNewComponent.js          # ← New component here
├── deal-room-builder/
│   └── block-fields/
│       └── YourNewComponentFields.js    # ← Form fields here
├── constants.js                         # ← Add component config here
```

### Component Organization Pattern

Each component follows a consistent internal structure:

```
feature-folder/
├── index.js                    # Main component export
├── Component.js                # Individual components
├── constants.js                # Feature-specific constants
├── hooks/                      # Custom React hooks
├── components/                 # Sub-components
└── utils.js                    # Utility functions
```

## Step-by-Step Component Creation

Let's create a **"Call To Action"** component as a complete example.

### Step 1: Create the Component File

**Location:** `components/builder-components/CallToAction.js`

```javascript
import React from "react";
import { LayoutContainer } from "../LayoutContainer";
import { Typography } from "../Typography";
import { getTextStyles, getBackgroundStyles } from "../../utils/styleUtils";

const CallToAction = ({ data, navigateInternal, buyersLogoUrl }) => {
  const {
    title = "Ready to Get Started?",
    description = "Take the next step with our solution.",
    button_text = "Contact Us",
    button_url = "#",
    button_style = "primary",
    background = {},
    text = {},
  } = data;

  // Generate styles based on configuration
  const textStyles = getTextStyles(text, background);
  const backgroundStyles = getBackgroundStyles(background);
  const buttonStyles = getButtonStyles(button_style, background);

  const handleButtonClick = (e) => {
    e.preventDefault();

    if (button_url.startsWith("#")) {
      // Internal navigation
      const sectionId = button_url.substring(1);
      navigateInternal && navigateInternal(null, sectionId);
    } else {
      // External link
      window.open(button_url, "_blank", "noopener,noreferrer");
    }
  };

  return (
    <LayoutContainer
      background={background}
      padding="large"
      className="call-to-action-section"
    >
      <div className="flex flex-col items-center text-center space-y-6">
        {/* Title */}
        <Typography.Heading2 style={textStyles} className="max-w-3xl">
          {title}
        </Typography.Heading2>

        {/* Description */}
        {description && (
          <Typography.Body
            style={{ ...textStyles, fontSize: "1.1rem" }}
            className="max-w-2xl text-gray-600"
          >
            {description}
          </Typography.Body>
        )}

        {/* Button */}
        <button
          onClick={handleButtonClick}
          className={`
            px-8 py-4 rounded-lg font-semibold text-lg 
            transition-all duration-200 hover:scale-105
            ${buttonStyles.className}
          `}
          style={buttonStyles.style}
        >
          {button_text}
        </button>
      </div>
    </LayoutContainer>
  );
};

// Utility function for button styling
const getButtonStyles = (buttonStyle, background) => {
  const styles = {
    primary: {
      className: "bg-blue-600 text-white hover:bg-blue-700",
      style: {},
    },
    secondary: {
      className: "bg-gray-200 text-gray-800 hover:bg-gray-300",
      style: {},
    },
    outline: {
      className:
        "border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white",
      style: {},
    },
  };

  return styles[buttonStyle] || styles.primary;
};

export default CallToAction;
```

### Step 2: Add Component to Registry

**Location:** `constants.js`

```javascript
// Import your new component
import CallToAction from "./components/builder-components/CallToAction";

// Add to BUILDER_COMPONENTS registry
export const BUILDER_COMPONENTS = {
  hero: Hero,
  feature: Feature,
  metrics: Metrics,
  members: Members,
  resources: Resources,
  faqs: FAQ,
  testimonial: Testimonial,
  logo: Logo,
  callToAction: CallToAction, // ← Add your component here
};

// Add component configuration
export const CALL_TO_ACTION = "callToAction";

export const COMPONENT_TYPES = [
  // ... existing types
  {
    type: CALL_TO_ACTION,
    label: "Call To Action",
    icon: "target", // Icon name for sidebar
    category: "conversion",
  },
];

// Add default data structure
export const DEFAULT_COMPONENT_DATA = {
  // ... existing defaults
  [CALL_TO_ACTION]: {
    title: "Ready to Get Started?",
    description: "Take the next step with our solution.",
    button_text: "Contact Us",
    button_url: "#contact",
    button_style: "primary",
    background: {
      background_type: "color",
      background_color: "#ffffff",
    },
    text: {
      text_color: "#333333",
      text_alignment: "center",
    },
  },
};
```

### Step 3: Create Form Fields Component

**Location:** `deal-room-builder/block-fields/CallToActionFields.js`

```javascript
import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import { TextInput } from "./TextInput";
import { TextArea } from "./TextArea";
import { RadioGroup } from "./RadioGroup";
import { ColorPicker } from "./ColorPicker";
import { TabComponent } from "./TabComponent";

const CallToActionFields = ({ blockId }) => {
  const { control, watch } = useFormContext();

  // Watch current values for dynamic updates
  const watchedData = watch(`pages.0.sections.${blockId}.data`);

  const buttonStyleOptions = [
    { value: "primary", label: "Primary" },
    { value: "secondary", label: "Secondary" },
    { value: "outline", label: "Outline" },
  ];

  const alignmentOptions = [
    { value: "left", label: "Left" },
    { value: "center", label: "Center" },
    { value: "right", label: "Right" },
  ];

  return (
    <div className="space-y-4">
      <TabComponent
        tabs={[
          {
            label: "Content",
            content: (
              <div className="space-y-4">
                {/* Title Field */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.title`}
                  control={control}
                  rules={{ required: "Title is required" }}
                  render={({ field, fieldState }) => (
                    <TextInput
                      label="Title"
                      placeholder="Enter call-to-action title"
                      {...field}
                      error={fieldState.error?.message}
                      required
                    />
                  )}
                />

                {/* Description Field */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.description`}
                  control={control}
                  render={({ field, fieldState }) => (
                    <TextArea
                      label="Description"
                      placeholder="Enter supporting description"
                      rows={3}
                      {...field}
                      error={fieldState.error?.message}
                    />
                  )}
                />

                {/* Button Text Field */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.button_text`}
                  control={control}
                  rules={{ required: "Button text is required" }}
                  render={({ field, fieldState }) => (
                    <TextInput
                      label="Button Text"
                      placeholder="Enter button text"
                      {...field}
                      error={fieldState.error?.message}
                      required
                    />
                  )}
                />

                {/* Button URL Field */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.button_url`}
                  control={control}
                  rules={{
                    required: "Button URL is required",
                    pattern: {
                      value: /^(https?:\/\/|#|mailto:)/,
                      message:
                        "URL must start with http://, https://, #, or mailto:",
                    },
                  }}
                  render={({ field, fieldState }) => (
                    <TextInput
                      label="Button URL"
                      placeholder="https://example.com or #section"
                      {...field}
                      error={fieldState.error?.message}
                      helpText="Use # for internal navigation (e.g., #contact)"
                      required
                    />
                  )}
                />

                {/* Button Style Selection */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.button_style`}
                  control={control}
                  render={({ field }) => (
                    <RadioGroup
                      label="Button Style"
                      options={buttonStyleOptions}
                      {...field}
                    />
                  )}
                />
              </div>
            ),
          },
          {
            label: "Styling",
            content: (
              <div className="space-y-4">
                {/* Text Alignment */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.text.text_alignment`}
                  control={control}
                  render={({ field }) => (
                    <RadioGroup
                      label="Text Alignment"
                      options={alignmentOptions}
                      {...field}
                    />
                  )}
                />

                {/* Text Color */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.text.text_color`}
                  control={control}
                  render={({ field }) => (
                    <ColorPicker label="Text Color" {...field} />
                  )}
                />

                {/* Background Color */}
                <Controller
                  name={`pages.0.sections.${blockId}.data.background.background_color`}
                  control={control}
                  render={({ field }) => (
                    <ColorPicker label="Background Color" {...field} />
                  )}
                />
              </div>
            ),
          },
        ]}
      />
    </div>
  );
};

export default CallToActionFields;
```

### Step 4: Register Form Fields

**Location:** `deal-room-builder/block-fields/index.js`

```javascript
// Import your new form fields component
import CallToActionFields from "./CallToActionFields";

// Add to the form fields registry
export const BLOCK_FIELD_COMPONENTS = {
  hero: HeroFields,
  feature: FeatureFields,
  metrics: MetricsFields,
  members: MembersFields,
  resources: ResourcesFields,
  faqs: FAQFields,
  testimonial: TestimonialFields,
  logo: LogoFields,
  callToAction: CallToActionFields, // ← Add your form fields here
};
```

### Step 5: Add Component to Builder Sidebar

**Location:** `deal-room-builder/components/SectionSidebar.js`

```javascript
import { CALL_TO_ACTION } from "../../constants";

const componentCategories = {
  content: [
    { type: "hero", label: "Hero Section", icon: "hero" },
    { type: "feature", label: "Feature", icon: "feature" },
    // ... other content components
  ],
  conversion: [
    { type: CALL_TO_ACTION, label: "Call To Action", icon: "target" }, // ← Add here
  ],
  social: [
    { type: "testimonial", label: "Testimonials", icon: "quote" },
    // ... other social components
  ],
};
```

## Registry Integration Examples

### Adding to BUILDER_COMPONENTS

```javascript
// constants.js
export const BUILDER_COMPONENTS = {
  // Existing components...
  hero: Hero,
  feature: Feature,

  // Your new component
  callToAction: CallToAction,
  pricingTable: PricingTable, // Another example
  contactForm: ContactForm, // Another example
};

// Component type constants
export const CALL_TO_ACTION = "callToAction";
export const PRICING_TABLE = "pricingTable";
export const CONTACT_FORM = "contactForm";
```

### Adding to Sidebar Categories

```javascript
// SectionSidebar.js
const componentCategories = {
  content: ["hero", "feature", "metrics"],
  conversion: ["callToAction", "contactForm"], // ← Conversion components
  commerce: ["pricingTable"], // ← Commerce components
  social: ["testimonial", "members"],
};
```

## Form Field Integration Examples

### Complex Form Field with Dynamic Options

```javascript
const AdvancedFields = ({ blockId }) => {
  const { control, watch } = useFormContext();

  // Watch for changes to show/hide fields dynamically
  const showAdvanced = watch(`pages.0.sections.${blockId}.data.advanced_mode`);

  return (
    <div className="space-y-4">
      {/* Toggle for advanced options */}
      <Controller
        name={`pages.0.sections.${blockId}.data.advanced_mode`}
        control={control}
        render={({ field }) => (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...field}
              checked={field.value || false}
              onChange={(e) => field.onChange(e.target.checked)}
            />
            <label>Enable Advanced Options</label>
          </div>
        )}
      />

      {/* Conditionally show advanced fields */}
      {showAdvanced && (
        <div className="border-t pt-4 space-y-4">
          <Controller
            name={`pages.0.sections.${blockId}.data.custom_css`}
            control={control}
            render={({ field }) => (
              <TextArea
                label="Custom CSS"
                placeholder="Enter custom styles"
                rows={5}
                {...field}
              />
            )}
          />
        </div>
      )}
    </div>
  );
};
```

## Quick Reference Checklist

When creating a new component, use this checklist:

- [ ] Created component file in `components/builder-components/`
- [ ] Added component to `BUILDER_COMPONENTS` registry
- [ ] Added component type to `COMPONENT_TYPES` array
- [ ] Added default data to `DEFAULT_COMPONENT_DATA`
- [ ] Created form fields component in `deal-room-builder/block-fields/`
- [ ] Added form fields to `BLOCK_FIELD_COMPONENTS` registry
- [ ] Added component to sidebar categories
- [ ] Implemented proper error handling and defaults
- [ ] Used consistent styling utilities
- [ ] Added proper TypeScript interfaces
- [ ] Written unit tests
- [ ] Written integration tests
- [ ] Updated documentation
- [ ] Tested in builder interface
- [ ] Tested in preview mode

## Best Practices Summary

1. **Consistent Interface**: All components accept `data` prop with standardized structure
2. **Theme Integration**: Use theme colors and spacing variables
3. **Performance**: Use React.memo and useMemo for expensive operations
4. **Error Handling**: Graceful degradation with helpful error messages
5. **Testing**: Comprehensive unit and integration tests
6. **Documentation**: Keep documentation updated with examples

This development guide provides everything needed to create new Deal Room components from scratch. For information on using existing components, see the [Components Reference Guide](./ComponentsReference.md).
