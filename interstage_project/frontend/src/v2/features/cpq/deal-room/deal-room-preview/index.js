import { get } from "lodash";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";

import { EverLoader, message } from "~/v2/components";
import { replaceUrl } from "~/v2/features/cpq/utils";

import PresentationMode from "./PresentationMode";
import PreviewMode from "./PreviewMode";

/**
 * DealRoomPreview is the main preview component for the deal room, handling both preview and presentation modes.
 * @param {Object} props
 * @param {Object} props.data - Deal room data.
 * @param {Object} props.clientInfo - Client info.
 * @param {string} props.buyersLogoUrl - Buyer's logo URL.
 * @param {Function} props.onClose - Handler for closing the preview.
 * @returns {JSX.Element}
 */
const DealRoomPreview = ({
  data,
  clientInfo,
  buyersLogoUrl,
  onClose = null,
}) => {
  const query = new URLSearchParams(window.location.search);

  const [activePage, setActivePage] = useState("");
  const [activeSection, setActiveSection] = useState("");
  const [viewMode, setViewMode] = useState(
    () => query.get("view") ?? "preview"
  );

  /**
   * On mount and when data/viewMode changes, set the active page and section based on URL or first available block.
   */
  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const page = query.get("page");
    const blocks_order = get(
      data,
      `deal_room_page_spec.pages.${page}.blocks_order`,
      []
    );
    if (page && blocks_order.length > 0) {
      const hash = window.location.hash;
      let sectionId = blocks_order[0];
      if (hash) {
        sectionId = hash.split("#")[1];
      }
      setActivePage(page);
      setActiveSection(sectionId);
      replaceUrl({ view: viewMode, page: page }, sectionId);
    } else {
      let index = 0;
      const { pages_order, pages } = get(data, "deal_room_page_spec");
      while (
        get(pages, `${pages_order[index]}.blocks_order.length`) === 0 &&
        index < pages_order.length
      ) {
        index++;
      }
      if (get(pages, `${pages_order[index]}.blocks_order.length`) > 0) {
        const sectionId = pages[pages_order[index]].blocks_order[0];
        setActivePage(pages_order[index]);
        setActiveSection(sectionId);
        replaceUrl({ view: viewMode, page: pages_order[index] }, sectionId);
      } else {
        message.error("Add at least one block to the page");
      }
    }
  }, [data, viewMode]);

  if (!activePage || !activeSection) {
    return <EverLoader />;
  }

  return (
    <div className="relative w-full h-full flex flex-col items-center">
      {/* Preview Mode */}
      {viewMode === "preview" && (
        <PreviewMode
          data={data}
          clientInfo={clientInfo}
          buyersLogoUrl={buyersLogoUrl}
          activePage={activePage}
          activeSection={activeSection}
          setViewMode={setViewMode}
          setActivePage={setActivePage}
          setActiveSection={setActiveSection}
          onClose={onClose}
        />
      )}
      {/* Presentation Mode */}
      {viewMode === "presentation" && (
        <PresentationMode
          data={data}
          clientInfo={clientInfo}
          buyersLogoUrl={buyersLogoUrl}
          activePage={activePage}
          setActivePage={setActivePage}
          setActiveSection={setActiveSection}
          onClose={() => {
            setViewMode("preview");
            replaceUrl({ view: "preview", page: activePage }, activeSection);
          }}
        />
      )}
    </div>
  );
};

DealRoomPreview.propTypes = {
  data: PropTypes.object.isRequired,
  clientInfo: PropTypes.object,
  buyersLogoUrl: PropTypes.string,
  onClose: PropTypes.func,
};

DealRoomPreview.defaultProps = {
  clientInfo: {},
  buyersLogoUrl: "",
  onClose: null,
};

export default DealRoomPreview;
