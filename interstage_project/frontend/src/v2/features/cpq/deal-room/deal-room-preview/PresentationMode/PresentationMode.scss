.presentation-carousel {
  margin: 0;
  overflow-y: auto;
  width: 100% !important;

  .slick-list {
    height: 100% !important;
  }

  .slick-track {
    height: 100% !important;
  }

  .slick-slide {
    > div {
      height: 100% !important;
    }

    &.slick-active {
      height: 100% !important;
    }
  }

  .slick-dots {
    position: fixed !important;
    padding-bottom: 12px !important;
    bottom: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    align-items: center !important;

    li {
      width: 100% !important;
      height: 8px !important; // h-2 = 8px

      &.slick-active {
        width: 150% !important;
        height: 12px !important; // h-3 = 12px
      }

      button {
        width: 100% !important;
        height: 8px !important; // h-2 = 8px
        border-radius: 0 !important;
      }
    }
  }
}

// Shake animations for navigation buttons
@keyframes shake-left {
  0%,
  100% {
    transform: translateX(0);
  }
  16.66% {
    transform: translateX(-12px);
  }
  33.33% {
    transform: translateX(8px);
  }
  50% {
    transform: translateX(-8px);
  }
  66.66% {
    transform: translateX(4px);
  }
  83.33% {
    transform: translateX(-4px);
  }
}

@keyframes shake-right {
  0%,
  100% {
    transform: translateX(0);
  }
  16.66% {
    transform: translateX(12px);
  }
  33.33% {
    transform: translateX(-8px);
  }
  50% {
    transform: translateX(8px);
  }
  66.66% {
    transform: translateX(-4px);
  }
  83.33% {
    transform: translateX(4px);
  }
}

.animate-shake-left {
  animation: shake-left 0.5s ease-in-out;
}

.animate-shake-right {
  animation: shake-right 0.5s ease-in-out;
}
