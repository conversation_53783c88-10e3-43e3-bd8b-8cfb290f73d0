import { PlayIcon, LogOutCircleIcon } from "@everstage/evericons/outlined";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useEffect, useRef, useCallback } from "react";
import { twMerge } from "tailwind-merge";

import { EverButton } from "~/v2/components";
import {
  Layout<PERSON>ontainer,
  HeaderComponent,
  BUILDER_COMPONENTS,
} from "~/v2/features/cpq/deal-room/components";
import { PAGE_TYPES } from "~/v2/features/cpq/deal-room/constants";
import {
  replaceUrl,
  replaceUrlHash,
  getTextColor,
} from "~/v2/features/cpq/utils";
import theme from "~/v2/themes/blueBias23";

/**
 * PreviewMode displays the preview of the deal room, allowing navigation between pages and sections.
 * @param {Object} props
 * @param {Object} props.data - Deal room data.
 * @param {Object} props.clientInfo - Client info.
 * @param {string} props.buyersLogoUrl - Buyer's logo URL.
 * @param {string} props.activePage - Currently active page ID.
 * @param {string} props.activeSection - Currently active section ID.
 * @param {Function} props.setViewMode - Setter for view mode (preview/presentation).
 * @param {Function} props.setActivePage - Setter for active page.
 * @param {Function} props.setActiveSection - Setter for active section.
 * @param {Function} props.onClose - Handler for closing the preview.
 * @returns {JSX.Element}
 */
const PreviewMode = ({
  data,
  clientInfo,
  buyersLogoUrl,
  activePage,
  activeSection,
  setViewMode,
  setActivePage,
  setActiveSection,
  onClose,
}) => {
  const pages = get(data, "deal_room_page_spec.pages", {});
  const pagesOrder = get(data, "deal_room_page_spec.pages_order", []);
  const page = pages[activePage];

  const initialLoadRef = useRef(true);
  const blockRefs = useRef({});

  /**
   * Scrolls to the section with the given sectionId.
   * @param {string} sectionId
   */
  const scrollToSection = useCallback((sectionId) => {
    if (sectionId && blockRefs.current[sectionId]) {
      const element = blockRefs.current[sectionId];
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "start",
      });
    }
  }, []);

  /**
   * On mount, scroll to the section based on hash if needed.
   */
  useEffect(() => {
    if (initialLoadRef.current) {
      const firstSection = pages[activePage].blocks_order[0];
      if (firstSection !== activeSection) {
        scrollToSection(activeSection);
      }
      setTimeout(() => {
        initialLoadRef.current = false;
      }, 1000);
    }
  }, [activeSection, scrollToSection, activePage, pages]);

  /**
   * Adds a manual scroll listener as a fallback to update the active section based on scroll position.
   */
  useEffect(() => {
    const handleScroll = () => {
      const elements = Object.values(blockRefs.current).filter(Boolean);
      if (elements.length === 0) return;

      // Find the element closest to the top of the viewport
      let closestElement = null;
      let closestDistance = Number.POSITIVE_INFINITY;

      for (const element of elements) {
        const rect = element.getBoundingClientRect();
        const distance = Math.abs(rect.top);
        if (distance < closestDistance) {
          closestDistance = distance;
          closestElement = element;
        }
      }

      if (closestElement) {
        const sectionId = closestElement.dataset.sectionId;
        if (
          !initialLoadRef.current &&
          sectionId &&
          sectionId !== activeSection
        ) {
          // Update active section and URL hash when scrolling
          setActiveSection(sectionId);
          replaceUrlHash(sectionId);
        }
      }
    };

    // Add scroll listener to the drawer body
    const scrollContainer =
      document.querySelector(".ant-drawer-body") || document.documentElement;
    scrollContainer.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
    };
  }, [activeSection, setActiveSection]);

  const { background = {} } = pages[PAGE_TYPES.HEADER];
  const textColor = getTextColor(background.background_color);

  return (
    <>
      <div className="absolute top-5 right-10 flex gap-3 z-10">
        <EverButton
          size="small"
          type="ghost"
          color="base"
          className={twMerge(
            "!bg-transparent",
            textColor === theme.colors.dealroom.base.content.DEFAULT
              ? "!text-dealroom-base-content !border-ever-base-400"
              : "!text-dealroom-base !border-dealroom-base"
          )}
          prependIcon={<PlayIcon style={{ color: textColor }} />}
          onClick={() => {
            setViewMode("presentation");
            replaceUrl(
              { view: "presentation", page: activePage },
              activeSection
            );
          }}
        >
          Present
        </EverButton>
        {onClose && (
          <EverButton
            size="small"
            type="ghost"
            color="base"
            className={twMerge(
              "!bg-transparent",
              textColor === theme.colors.dealroom.base.content.DEFAULT
                ? "!text-dealroom-base-content !border-ever-base-400"
                : "!text-dealroom-base !border-dealroom-base"
            )}
            prependIcon={<LogOutCircleIcon style={{ color: textColor }} />}
            onClick={() => onClose(activePage, activeSection)}
          >
            Exit
          </EverButton>
        )}
      </div>
      {pages[PAGE_TYPES.HEADER] && (
        <LayoutContainer data={pages[PAGE_TYPES.HEADER]} isPreview>
          <HeaderComponent
            data={pages[PAGE_TYPES.HEADER]}
            pages={pages}
            pagesOrder={pagesOrder}
            activePage={activePage}
            onPageChange={(pageId, sectionId) => {
              replaceUrl({ view: "preview", page: pageId }, sectionId);
              setActivePage(pageId);
              setActiveSection(sectionId);
            }}
            isPreview
          />
        </LayoutContainer>
      )}
      <div className="flex flex-col w-full overflow-y-auto">
        {page.blocks_order?.map((blockId) => {
          const block = page.blocks[blockId];
          const Component = BUILDER_COMPONENTS[block.type];
          if (!Component) {
            return null;
          }
          return (
            <div
              key={blockId}
              ref={(el) => {
                if (el) {
                  blockRefs.current[blockId] = el;
                }
              }}
              data-section-id={blockId}
              className="w-full"
            >
              <div className="mx-auto">
                <LayoutContainer data={block} isPreview>
                  <Component
                    data={block}
                    clientInfo={clientInfo}
                    buyersLogoUrl={buyersLogoUrl}
                    navigateInternal={(pageId, sectionId) => {
                      replaceUrl({ view: "preview", page: pageId }, sectionId);
                      setActivePage(pageId);
                      setActiveSection(sectionId);
                      scrollToSection(sectionId);
                    }}
                  />
                </LayoutContainer>
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

PreviewMode.propTypes = {
  data: PropTypes.object.isRequired,
  clientInfo: PropTypes.object,
  buyersLogoUrl: PropTypes.string,
  activePage: PropTypes.string.isRequired,
  activeSection: PropTypes.string.isRequired,
  setViewMode: PropTypes.func.isRequired,
  setActivePage: PropTypes.func.isRequired,
  setActiveSection: PropTypes.func.isRequired,
  onClose: PropTypes.func,
};

PreviewMode.defaultProps = {
  clientInfo: {},
  buyersLogoUrl: "",
  onClose: null,
};

export default PreviewMode;
