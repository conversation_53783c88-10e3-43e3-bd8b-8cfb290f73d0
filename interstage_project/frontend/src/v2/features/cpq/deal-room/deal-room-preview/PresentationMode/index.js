import { Carousel } from "antd";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { twMerge } from "tailwind-merge";

import { EverTg } from "~/v2/components";
import {
  BUILDER_COMPONENTS,
  LayoutContainer,
} from "~/v2/features/cpq/deal-room/components";
import { replaceUrl, replaceUrlHash } from "~/v2/features/cpq/utils";

import NavigationOverlay from "./NavigationOverlay";
import SlidePreview from "./SlidePreview";
import "./PresentationMode.scss";

/**
 * PresentationMode displays the presentation view of the deal room, with slide navigation and overlays.
 * @param {Object} props
 * @param {Object} props.data - Deal room data.
 * @param {Object} props.clientInfo - Client info.
 * @param {string} props.buyersLogoUrl - Buyer's logo URL.
 * @param {string} props.activePage - Currently active page ID.
 * @param {Function} props.setActivePage - Setter for active page.
 * @param {Function} props.setActiveSection - Setter for active section.
 * @param {Function} props.onClose - Handler for closing the presentation mode.
 * @returns {JSX.Element}
 */
const PresentationMode = ({
  data,
  clientInfo,
  buyersLogoUrl,
  activePage,
  setActivePage,
  setActiveSection,
  onClose,
}) => {
  const carouselRef = useRef(null);
  const hoveredElementRef = useRef(null);

  // Add timeout management refs
  const carouselTimeoutRef = useRef(null);
  const shakeTimeoutRef = useRef(null);

  const pages = get(data, "deal_room_page_spec.pages", {});
  const page = pages[activePage];

  // Initialize currentSlide based on URL hash
  const [currentSlide, setCurrentSlide] = useState(() => {
    const hash = window.location.hash;
    if (hash) {
      const blockId = hash.split("#")[1];
      const slideIndex = page.blocks_order.indexOf(blockId);
      return slideIndex >= 0 ? slideIndex : 0;
    }
    return 0;
  });
  const [visitedSlides, setVisitedSlides] = useState(new Set());
  const [hoveredDotIndex, setHoveredDotIndex] = useState(null);
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });
  const [shakeDirection, setShakeDirection] = useState(null); // 'left' or 'right'

  // Memoize slides array to prevent unnecessary re-renders
  const slides = useMemo(() => {
    const slidesArray = [];

    // Add page blocks as slides
    let index = 0;
    for (const blockId of page.blocks_order) {
      const block = page.blocks[blockId];

      if (BUILDER_COMPONENTS[block.type]) {
        slidesArray.push({
          index,
          block,
        });
        index++;
      }
    }

    return slidesArray;
  }, [page.blocks_order, page.blocks]);

  // Update currentSlide when page changes or URL hash changes
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const blockId = hash.split("#")[1];
      const slideIndex = slides.findIndex(
        (slide) => slide.block.block_id === blockId
      );
      if (slideIndex >= 0) {
        setCurrentSlide(slideIndex);
        if (carouselRef.current) {
          carouselRef.current.goTo(slideIndex);
        }
      } else {
        setCurrentSlide(0);
        replaceUrlHash(get(slides, "0.block.block_id", null));
      }
    } else {
      setCurrentSlide(0);
      replaceUrlHash(get(slides, "0.block.block_id", null));
    }
  }, [slides]);

  // Add cleanup useEffect
  useEffect(() => {
    return () => {
      if (carouselTimeoutRef.current) {
        clearTimeout(carouselTimeoutRef.current);
      }
      if (shakeTimeoutRef.current) {
        clearTimeout(shakeTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Renders the content for a slide.
   * @param {Object} param0
   * @returns {JSX.Element}
   */
  const slideContent = ({ block, index, isSlidePreview = false }) => {
    const Component = BUILDER_COMPONENTS[block.type];

    return (
      <div className="w-full h-full">
        <div className="mx-auto h-full">
          <LayoutContainer
            data={block}
            isSlidePreview={isSlidePreview}
            isPreview
          >
            <Component
              data={block}
              clientInfo={clientInfo}
              buyersLogoUrl={buyersLogoUrl}
              navigateInternal={(pageId, sectionId) => {
                if (!pageId || !pages[pageId]) {
                  return;
                }
                const currentSlideIndex =
                  pages[pageId].blocks_order.indexOf(sectionId);
                carouselRef.current.goTo(currentSlideIndex);
                setCurrentSlide(currentSlideIndex);
                setActivePage(pageId);
                setActiveSection(sectionId);
                replaceUrl({ view: "presentation", page: pageId }, sectionId);
              }}
              // Needed when spotlight feature is implemented
              isPresentationMode={true}
              isActiveSlide={index === currentSlide}
              isVisitedSlide={visitedSlides.has(block.block_id)}
            />
          </LayoutContainer>
        </div>
      </div>
    );
  };

  /**
   * Handles mouse enter on a dot to show slide preview.
   * @param {number} index
   * @param {HTMLElement} element
   */
  const handleDotMouseEnter = (index, element) => {
    setHoveredDotIndex(index);
    hoveredElementRef.current = element;

    // Calculate position for preview
    const rect = element.getBoundingClientRect();
    setPreviewPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10,
    });
  };

  /**
   * Handles mouse leave on a dot to hide slide preview.
   */
  const handleDotMouseLeave = () => {
    setHoveredDotIndex(null);
    hoveredElementRef.current = null;
  };

  /**
   * Handles navigation to the previous slide or previous page.
   */
  const handlePreviousSlide = useCallback(() => {
    if (carouselRef.current && currentSlide > 0) {
      carouselRef.current.prev();
    } else {
      // Check if there's a previous page
      const pagesOrder = get(data, "deal_room_page_spec.pages_order", []);
      const filteredPagesOrder = pagesOrder.filter(
        (pageId) => pages[pageId].blocks_order.length > 0
      );
      const currentPageIndex = filteredPagesOrder.indexOf(activePage);

      if (currentPageIndex > 0) {
        // Navigate to previous page
        const prevPageId = filteredPagesOrder[currentPageIndex - 1];
        const prevPage = pages[prevPageId];
        const lastSlideIndex = prevPage.blocks_order.length - 1;
        const lastSectionId = prevPage.blocks_order[lastSlideIndex];

        setActivePage(prevPageId);
        setActiveSection(lastSectionId);
        setCurrentSlide(lastSlideIndex);

        // Update URL
        replaceUrl({ view: "presentation", page: prevPageId }, lastSectionId);

        // Clear any existing carousel timeout before setting new one
        if (carouselTimeoutRef.current) {
          clearTimeout(carouselTimeoutRef.current);
        }

        // Navigate carousel to last slide AFTER component re-renders
        carouselTimeoutRef.current = setTimeout(() => {
          if (carouselRef.current) {
            carouselRef.current.goTo(lastSlideIndex);
          }
          carouselTimeoutRef.current = null;
        }, 100);
      } else {
        // Show shake effect for left button
        setShakeDirection("left");

        // Clear any existing shake timeout before setting new one
        if (shakeTimeoutRef.current) {
          clearTimeout(shakeTimeoutRef.current);
        }

        shakeTimeoutRef.current = setTimeout(() => {
          setShakeDirection(null);
          shakeTimeoutRef.current = null;
        }, 500);
      }
    }
  }, [
    currentSlide,
    data,
    pages,
    activePage,
    setActivePage,
    setActiveSection,
    setCurrentSlide,
    setShakeDirection,
  ]);

  /**
   * Handles navigation to the next slide or next page.
   */
  const handleNextSlide = useCallback(() => {
    if (carouselRef.current && currentSlide < slides.length - 1) {
      carouselRef.current.next();
    } else {
      // Check if there's a next page
      const pagesOrder = get(data, "deal_room_page_spec.pages_order", []);
      const filteredPagesOrder = pagesOrder.filter(
        (pageId) => pages[pageId].blocks_order.length > 0
      );
      const currentPageIndex = filteredPagesOrder.indexOf(activePage);

      if (currentPageIndex < filteredPagesOrder.length - 1) {
        // Navigate to next page
        const nextPageId = filteredPagesOrder[currentPageIndex + 1];
        const nextPage = pages[nextPageId];
        const firstSectionId = nextPage.blocks_order[0];

        setActivePage(nextPageId);
        setActiveSection(firstSectionId);
        setCurrentSlide(0);

        // Update URL
        replaceUrl({ view: "presentation", page: nextPageId }, firstSectionId);

        // Clear any existing carousel timeout before setting new one
        if (carouselTimeoutRef.current) {
          clearTimeout(carouselTimeoutRef.current);
        }

        // Navigate carousel to first slide AFTER component re-renders
        carouselTimeoutRef.current = setTimeout(() => {
          if (carouselRef.current) {
            carouselRef.current.goTo(0);
          }
          carouselTimeoutRef.current = null;
        }, 100);
      } else {
        // Show shake effect for right button
        setShakeDirection("right");

        // Clear any existing shake timeout before setting new one
        if (shakeTimeoutRef.current) {
          clearTimeout(shakeTimeoutRef.current);
        }

        shakeTimeoutRef.current = setTimeout(() => {
          setShakeDirection(null);
          shakeTimeoutRef.current = null;
        }, 500);
      }
    }
  }, [
    currentSlide,
    slides.length,
    data,
    pages,
    activePage,
    setActivePage,
    setActiveSection,
    setCurrentSlide,
    setShakeDirection,
  ]);

  /**
   * Handles carousel slide change event.
   * @param {number} current
   * @param {number} next
   */
  const handleCarouselChange = (current, next) => {
    setCurrentSlide(next);
    if (next > current) {
      setVisitedSlides(
        (prev) => new Set([...prev, slides[current].block.block_id])
      );
    }
    if (next !== current) {
      const blockId = get(slides, `${next}.block.block_id`, null);
      if (blockId) {
        replaceUrlHash(blockId);
        setActiveSection(blockId);
      }
    }
  };

  if (!slides || slides.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <EverTg.Text className="text-ever-base-content-mid">
          No content available for presentation
        </EverTg.Text>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full flex flex-col">
      <NavigationOverlay
        data={data}
        activePage={activePage}
        setActivePage={setActivePage}
        setActiveSection={setActiveSection}
        shakeDirection={shakeDirection}
        handlePreviousSlide={handlePreviousSlide}
        handleNextSlide={handleNextSlide}
        onClose={onClose}
      />
      {/* Carousel Container */}
      <div className="w-full h-full overflow-y-auto [&_.ant-carousel]:h-full [&_.ant-carousel]:flex">
        <Carousel
          ref={carouselRef}
          infinite={false}
          className="presentation-carousel"
          beforeChange={handleCarouselChange}
          effect="fade"
          autoplay={false}
          accessibility={false}
          customPaging={(i) => (
            <div
              className={twMerge(
                "w-full h-full cursor-pointer rounded-xs transition-all duration-300 hover:scale-101",
                i < currentSlide
                  ? "bg-dealroom-chartColors-10"
                  : i === currentSlide
                  ? "bg-ever-info"
                  : "bg-ever-base-400 hover:bg-ever-base-content-low"
              )}
              onMouseEnter={(e) => handleDotMouseEnter(i, e.currentTarget)}
              onMouseLeave={() => handleDotMouseLeave()}
            />
          )}
        >
          {slides.map((slide, index) => (
            <div
              key={index}
              className={twMerge(
                "w-full h-full",
                shakeDirection === "left" && "animate-shake-left",
                shakeDirection === "right" && "animate-shake-right"
              )}
            >
              {slideContent({ ...slide })}
            </div>
          ))}
        </Carousel>
      </div>
      {/* Preview overlay */}
      <SlidePreview
        clientInfo={clientInfo}
        buyersLogoUrl={buyersLogoUrl}
        hoveredDotIndex={hoveredDotIndex}
        slides={slides}
        page={page}
        previewPosition={previewPosition}
      />
    </div>
  );
};

PresentationMode.propTypes = {
  data: PropTypes.object.isRequired,
  clientInfo: PropTypes.object,
  buyersLogoUrl: PropTypes.string,
  activePage: PropTypes.string.isRequired,
  setActivePage: PropTypes.func.isRequired,
  setActiveSection: PropTypes.func.isRequired,
  onClose: PropTypes.func,
};

PresentationMode.defaultProps = {
  clientInfo: {},
  buyersLogoUrl: "",
  onClose: null,
};

export default PresentationMode;
