import {
  ChevronLeftIcon,
  ChevronRightIcon,
  LogOutCircleIcon,
} from "@everstage/evericons/outlined";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useState, useRef, useEffect, useCallback } from "react";
import { twMerge } from "tailwind-merge";

import { EverButton, EverTg, EverSelect, IconButton } from "~/v2/components";
import { replaceUrl } from "~/v2/features/cpq/utils";

/**
 * NavigationOverlay displays navigation controls and keyboard hints for presentation mode.
 * @param {Object} props
 * @param {Object} props.data - Deal room data.
 * @param {string} props.activePage - Currently active page ID.
 * @param {Function} props.setActivePage - Setter for active page.
 * @param {Function} props.setActiveSection - Setter for active section.
 * @param {string} props.shakeDirection - Direction for shake animation.
 * @param {Function} props.handlePreviousSlide - Handler for previous slide.
 * @param {Function} props.handleNextSlide - Handler for next slide.
 * @param {Function} props.onClose - Handler for closing presentation mode.
 * @returns {JSX.Element}
 */
const NavigationOverlay = ({
  data,
  activePage,
  setActivePage,
  setActiveSection,
  shakeDirection,
  handlePreviousSlide,
  handleNextSlide,
  onClose,
}) => {
  const timeout = useRef(null);
  const initialLoad = useRef(true);
  const lastActionTime = useRef(0); // Track last navigation action time
  const THROTTLE_DELAY = 200; // 200ms throttle delay

  const pages = get(data, "deal_room_page_spec.pages", {});
  const pagesOrder = get(data, "deal_room_page_spec.pages_order", []);
  const filteredPagesOrder = pagesOrder.filter(
    (pageId) => pages[pageId].blocks_order.length > 0
  );

  const [onHover, setOnHover] = useState(true);
  const [showKeyboardHint, setShowKeyboardHint] = useState(true);
  const [showControls, setShowControls] = useState(false);

  const onMouseEnter = () => {
    clearTimeout(timeout.current);
    setOnHover(true);
  };

  const onMouseLeave = () => {
    if (initialLoad.current) return;
    clearTimeout(timeout.current);
    timeout.current = setTimeout(() => {
      setOnHover(false);
    }, 500);
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      setOnHover(false);
      setShowKeyboardHint(false);
      initialLoad.current = false;
    }, 5000);
    return () => {
      clearTimeout(timeout);
    };
  }, []);

  /**
   * Throttled handler for previous slide navigation.
   */
  const throttledHandlePreviousSlide = useCallback(() => {
    const now = Date.now();
    if (now - lastActionTime.current < THROTTLE_DELAY) {
      return; // Ignore if within throttle delay
    }
    lastActionTime.current = now;
    handlePreviousSlide();
  }, [handlePreviousSlide]);

  /**
   * Throttled handler for next slide navigation.
   */
  const throttledHandleNextSlide = useCallback(() => {
    const now = Date.now();
    if (now - lastActionTime.current < THROTTLE_DELAY) {
      return; // Ignore if within throttle delay
    }
    lastActionTime.current = now;
    handleNextSlide();
  }, [handleNextSlide]);

  /**
   * Handles keyboard navigation and control toggling.
   * @param {KeyboardEvent} event
   */
  const handleKeyDown = useCallback(
    (event) => {
      // Don't handle if modifier keys are pressed
      if (event.ctrlKey || event.altKey || event.metaKey || event.shiftKey) {
        return;
      }

      // Check if the event is coming from an input field
      const activeElement = document.activeElement;
      const isInputField =
        activeElement &&
        (activeElement.tagName === "INPUT" ||
          activeElement.tagName === "TEXTAREA" ||
          activeElement.contentEditable === "true");

      if (isInputField) {
        return; // Don't handle if focus is on an input field
      }

      switch (event.key) {
        case "ArrowLeft": {
          event.preventDefault();
          event.stopPropagation();
          throttledHandlePreviousSlide();
          break;
        }
        case "ArrowRight": {
          event.preventDefault();
          event.stopPropagation();
          throttledHandleNextSlide();
          break;
        }
        case "Escape": {
          event.preventDefault();
          event.stopPropagation();
          onClose();
          break;
        }
        case " ": {
          event.preventDefault();
          event.stopPropagation();
          setShowControls(!showControls);
          break;
        }
        default: {
          break;
        }
      }
    },
    [
      throttledHandlePreviousSlide,
      throttledHandleNextSlide,
      onClose,
      showControls,
    ]
  );

  // Add keyboard event listener with highest priority
  useEffect(() => {
    // Add event listener with capture phase to intercept before other handlers
    document.addEventListener("keydown", handleKeyDown, true);

    return () => {
      document.removeEventListener("keydown", handleKeyDown, true);
    };
  }, [handleKeyDown]);

  return (
    <>
      {/* Keyboard navigation hint */}
      <div
        className={twMerge(
          "absolute left-1/2 transform -translate-x-1/2 transition-all duration-700 ease-in-out z-30 bg-ever-base border border-ever-base-300 rounded-lg px-4 py-2 shadow-lg",
          showKeyboardHint
            ? "translate-y-0 opacity-100 top-20"
            : "-translate-y-full opacity-0 top-0"
        )}
      >
        <EverTg.Text className="text-sm text-ever-base-content-mid">
          <span className="font-medium">Use ← → to navigate</span>
          <span className="mx-1 font-bold"> | </span>
          <span className="font-medium">SPACE to show controls</span>
          <span className="mx-1 font-bold"> | </span>
          <span className="font-medium">ESC to exit</span>
        </EverTg.Text>
      </div>
      <div
        className="absolute w-full flex z-20 overflow-hidden"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div
          className={twMerge(
            "flex w-full py-5 m-auto bg-ever-base border-b border-ever-base-300 shadow-sm transition-all duration-700 ease-in-out",
            onHover || showControls ? "translate-y-0" : "-translate-y-full"
          )}
        >
          <EverSelect
            size="small"
            value={activePage}
            options={filteredPagesOrder.map((pageId) => ({
              label: pages[pageId].name,
              value: pageId,
            }))}
            onChange={(value) => {
              const sectionId = pages[value].blocks_order[0];
              replaceUrl({ view: "presentation", page: value }, sectionId);
              setActivePage(value);
              setActiveSection(sectionId);
            }}
            className="w-fit m-auto [&_.ant-select-selector]:!border-none [&_.ant-select-arrow]:!right-0 [&_.ant-select-selector]:!shadow-none"
          />
          <EverButton
            size="small"
            type="ghost"
            color="base"
            className="absolute right-10 z-20"
            prependIcon={<LogOutCircleIcon />}
            onClick={onClose}
          >
            Exit
          </EverButton>
        </div>
      </div>
      <div
        className="flex items-center absolute h-full z-20 overflow-hidden"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div
          className={twMerge(
            "px-8 transition-all duration-700 ease-in-out",
            onHover || showControls ? "translate-x-0" : "-translate-x-full"
          )}
        >
          <IconButton
            size="small"
            type="ghost"
            color="base"
            className={twMerge(
              "!h-14 !w-14",
              shakeDirection === "left" && "animate-shake-left"
            )}
            icon={
              <ChevronLeftIcon className="!h-10 !w-10 shrink-0 text-ever-base-content-low !stroke-2" />
            }
            onClick={throttledHandlePreviousSlide}
          />
        </div>
      </div>
      <div
        className="flex items-center absolute right-0 h-full z-10 overflow-hidden"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div
          className={twMerge(
            "px-8 transition-all duration-700 ease-in-out",
            onHover || showControls ? "translate-x-0" : "translate-x-full"
          )}
        >
          <IconButton
            size="small"
            type="ghost"
            color="base"
            className={twMerge(
              "!h-14 !w-14",
              shakeDirection === "right" && "animate-shake-right"
            )}
            icon={
              <ChevronRightIcon className="!h-10 !w-10 shrink-0 text-ever-base-content-low !stroke-2" />
            }
            onClick={throttledHandleNextSlide}
          />
        </div>
      </div>
    </>
  );
};

NavigationOverlay.propTypes = {
  data: PropTypes.object.isRequired,
  activePage: PropTypes.string.isRequired,
  setActivePage: PropTypes.func.isRequired,
  setActiveSection: PropTypes.func.isRequired,
  shakeDirection: PropTypes.string,
  handlePreviousSlide: PropTypes.func.isRequired,
  handleNextSlide: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};

NavigationOverlay.defaultProps = {
  shakeDirection: null,
};

export default NavigationOverlay;
