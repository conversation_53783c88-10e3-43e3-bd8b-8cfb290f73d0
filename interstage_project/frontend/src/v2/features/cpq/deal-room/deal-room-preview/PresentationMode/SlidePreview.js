import PropTypes from "prop-types";

import { BUILDER_COMPONENTS } from "~/v2/features/cpq/deal-room/components";
import AutoScaleContainer from "~/v2/features/cpq/deal-room/deal-room-builder/builder/AutoScaleContainer";

/**
 * SlidePreview displays a small preview of a slide when hovering over a navigation dot.
 * @param {Object} props
 * @param {Object} props.clientInfo - Client info.
 * @param {string} props.buyersLogoUrl - Buyer's logo URL.
 * @param {number|null} props.hoveredDotIndex - Index of the hovered dot.
 * @param {Array} props.slides - Array of slides.
 * @param {Object} props.page - Current page object.
 * @param {Object} props.previewPosition - Position for the preview overlay.
 * @returns {JSX.Element|null}
 */
const SlidePreview = ({
  clientInfo,
  buyersLogoUrl,
  hoveredDotIndex,
  slides,
  page,
  previewPosition,
}) => {
  // Only show preview if a dot is hovered
  if (hoveredDotIndex === null) return null;

  const slide = slides[hoveredDotIndex];
  const currentBlock = page.blocks[slide.block.block_id];
  const Component = BUILDER_COMPONENTS[currentBlock.type];

  return (
    <div
      className="w-56 h-32 fixed z-50 bg-ever-base border border-ever-base-300 rounded-lg shadow-lg overflow-hidden -translate-x-1/2 -translate-y-full"
      style={{
        left: `${previewPosition.x}px`,
        top: `${previewPosition.y}px`,
      }}
    >
      <div
        id="slide-preview-container"
        className="flex items-center h-full overflow-hidden rounded"
      >
        <AutoScaleContainer
          parentId="slide-preview-container"
          isSlidePreview
          isPreview
        >
          <Component
            data={currentBlock}
            clientInfo={clientInfo}
            buyersLogoUrl={buyersLogoUrl}
          />
        </AutoScaleContainer>
      </div>
    </div>
  );
};

SlidePreview.propTypes = {
  clientInfo: PropTypes.object,
  buyersLogoUrl: PropTypes.string,
  hoveredDotIndex: PropTypes.number,
  slides: PropTypes.array.isRequired,
  page: PropTypes.object.isRequired,
  previewPosition: PropTypes.object.isRequired,
};

SlidePreview.defaultProps = {
  clientInfo: {},
  buyersLogoUrl: "",
  hoveredDotIndex: null,
};

export default SlidePreview;
