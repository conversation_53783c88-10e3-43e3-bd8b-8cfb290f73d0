import PropTypes from "prop-types";

import { EverInput } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

/**
 * TextArea renders a labeled textarea input field with validation and error display.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the textarea field.
 * @param {string} props.value - Value of the textarea.
 * @param {function} props.onChange - Callback when the value changes.
 * @param {boolean} props.required - Whether the field is required.
 * @param {Object} props.error - Error object for validation.
 * @param {string} props.placeholder - Placeholder text for the textarea.
 */
const TextArea = ({
  label,
  value,
  onChange,
  required = false,
  error,
  placeholder = "Type here",
  ...rest
}) => (
  <LabeledField label={label} required={required} error={error}>
    <EverInput.TextArea
      value={value}
      placeholder={placeholder}
      onChange={(e) => onChange(e.target.value)}
      autoSize={{ minRows: 3 }}
      {...rest}
    />
  </LabeledField>
);

TextArea.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  error: PropTypes.object,
  placeholder: PropTypes.string,
};

TextArea.defaultProps = {
  label: "",
  value: "",
  required: false,
  error: {},
  placeholder: "Type here",
};

export default TextArea;
