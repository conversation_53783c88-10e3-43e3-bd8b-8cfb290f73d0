.ant-tabs.ant-tabs-card {
  width: 100%;
  display: flex;
  gap: 1rem;
  padding-top: 0.125rem;

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-nav {
    height: 2rem;

    &::before {
      border: 0 !important;
      border-bottom: 1px solid theme("colors.ever.base.300") !important;
    }

    &-wrap {
      height: 2rem;
    }

    &-more {
      display: flex;
      align-items: center;
    }
  }

  .ant-tabs-tab {
    padding: 5px 0.75rem !important;
    margin-right: 0.25rem !important;
    border: 1px solid transparent !important;
    background-color: theme("colors.ever.base.100") !important;

    &.ant-tabs-tab-active {
      z-index: 30 !important;
      background-color: theme("colors.ever.base.DEFAULT") !important;
      border: 1px solid theme("colors.ever.base.300") !important;
      border-style: solid !important;
      border-bottom-color: theme("colors.ever.base.DEFAULT") !important;
    }
  }
}
