import { cloneDeep, isArray } from "lodash";

import { message } from "~/v2/components";
import { BLOCK_TYPES } from "~/v2/features/cpq/deal-room/constants";

import { CATEGORIES_KEYS, CATEGORIES, blockSchema } from "./constants";

/**
 * Returns a modified categories object with pages and their blocks (sections) for sidebar navigation.
 * Used to structure the sidebar for quick access to pages and branding sections.
 * @param {Object} data - The deal room page spec data.
 * @returns {Object} Modified categories object for sidebar.
 */
export const getCategories = (data) => {
  const modifiedCategories = cloneDeep(CATEGORIES);
  if (data?.pages_order?.length) {
    // Map each page to a sidebar entry with its blocks as sections
    modifiedCategories[CATEGORIES_KEYS.PAGES].pages = data.pages_order.map(
      (pageId) => {
        const page = data.pages[pageId];
        // For each block in the page, get its label (custom or from schema)
        const blocks = page.blocks_order.map((blockId) => ({
          block_id: blockId,
          label:
            page.blocks[blockId].title ||
            blockSchema[page.blocks[blockId].type].label,
        }));

        return {
          page_id: pageId,
          label: page.name,
          blocks, // Blocks act as sections
        };
      }
    );
  }

  return modifiedCategories;
};

/**
 * Extracts file objects from block data and appends them to formData for upload.
 * Cleans the block data by removing file_url and file references for backend compatibility.
 * Used before submitting block data to the backend to ensure files are uploaded and not sent as base64 or URLs.
 * @param {Object} block - The block data to process.
 * @param {FormData} formData - The FormData object to append files to.
 * @returns {Object} The cleaned block data.
 */
export const processBlockFiles = (block, formData) => {
  const blockType = block.type;
  const blockId = block.block_id;
  const cleanedBlock = cloneDeep(block);

  // Handle logo block file upload
  if (blockType === BLOCK_TYPES.LOGO) {
    if (cleanedBlock.background?.background_image?.file) {
      formData.append(blockId, cleanedBlock.background.background_image.file);
      // Remove file_url and file from payload for backend
      cleanedBlock.background.background_image = {
        ...cleanedBlock.background.background_image,
        file_url: "",
      };
    }
    // Handle hero and feature block file upload
  } else if (
    blockType === BLOCK_TYPES.HERO ||
    blockType === BLOCK_TYPES.FEATURE
  ) {
    if (cleanedBlock.file?.file) {
      formData.append(blockId, cleanedBlock.file.file);
      cleanedBlock.file = {
        ...cleanedBlock.file,
        file_url: "",
      };
    }
    // Handle members block: upload each member's file
  } else if (
    blockType === BLOCK_TYPES.MEMBERS &&
    isArray(cleanedBlock.members)
  ) {
    let index = 0;
    for (const member of cleanedBlock.members) {
      if (member.file?.file) {
        formData.append(member.key, member.file.file);
        cleanedBlock.members[index].file = {
          ...member.file,
          file_url: "",
        };
      }
      index++;
    }
    // Handle testimonials block: upload each testimonial's file
  } else if (
    blockType === BLOCK_TYPES.TESTIMONIALS &&
    isArray(cleanedBlock.testimonials)
  ) {
    let index = 0;
    for (const testimonial of cleanedBlock.testimonials) {
      if (testimonial.file?.file) {
        formData.append(testimonial.key, testimonial.file.file);
        cleanedBlock.testimonials[index].file = {
          ...testimonial.file,
          file_url: "",
        };
      }
      index++;
    }
    // Handle resources block: upload each resource's file
  } else if (
    blockType === BLOCK_TYPES.RESOURCES &&
    isArray(cleanedBlock.resources)
  ) {
    let index = 0;
    for (const resource of cleanedBlock.resources) {
      if (resource.file?.file) {
        formData.append(resource.key, resource.file.file);
        cleanedBlock.resources[index].file = {
          ...resource.file,
          file_url: "",
        };
      }
      index++;
    }
  }

  return { cleanedBlock };
};

/**
 * Displays an error message using the UI message component.
 * Shows the error's message if available, otherwise shows a default message.
 * Used to provide user feedback on failed API or form actions.
 * @param {Object} error - The error object (may have a message property).
 * @param {string} defaultMessage - The fallback message to show if error.message is not present.
 */
export const showErrorMessage = (error, defaultMessage) => {
  // Prefer error.message if available, otherwise show the fallback
  if (error?.message) {
    message.error(error?.message);
  } else {
    message.error(defaultMessage);
  }
};
