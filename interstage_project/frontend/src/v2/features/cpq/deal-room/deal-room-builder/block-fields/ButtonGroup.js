import { get } from "lodash";
import PropTypes from "prop-types";
import { Fragment } from "react";
import { useForm<PERSON>ontex<PERSON>, Controller } from "react-hook-form";

import {
  EverInput,
  EverButtonGroup,
  EverButton,
  EverSelect,
} from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

import TextInput from "./TextInput";

/**
 * ButtonGroup renders a list of button configuration fields for a block.
 *
 * @param {Object} props
 * @param {Array<Object>} props.value - Array of button config objects.
 * @param {string} props.name - Field name prefix for form fields.
 * @param {Object} props.pageSpec - Page specification object for page/section options.
 */
const ButtonGroup = ({ value, name, pageSpec }) => {
  return (
    <div className="flex flex-col gap-4 items-start">
      {value.map((button, idx) => (
        <Fragment key={idx}>
          {idx !== 0 && <div className="w-full h-px bg-ever-base-300" />}
          <Button button={button} name={`${name}.${idx}`} pageSpec={pageSpec} />
        </Fragment>
      ))}
    </div>
  );
};

ButtonGroup.propTypes = {
  value: PropTypes.arrayOf(PropTypes.object),
  name: PropTypes.string.isRequired,
  pageSpec: PropTypes.object.isRequired,
};

ButtonGroup.defaultProps = {
  value: [],
};

/**
 * Button renders the form fields for a single button configuration.
 *
 * @param {Object} props
 * @param {Object} props.button - Button config object.
 * @param {string} props.name - Field name prefix for form fields.
 * @param {Object} props.pageSpec - Page specification object for page/section options.
 */
export const Button = ({ button, name, pageSpec }) => {
  const { control, getValues, setValue, clearErrors } = useFormContext();
  const {
    ctaLabel = "",
    ctaPlaceholder = "Type here",
    linkLabel = "",
    linkPlaceholder = "Paste link",
    ctaRequired = false,
    linkRequired = false,
    isBothRequired = false,
  } = button || {};

  const pageOptions = get(pageSpec, "pages_order", []).map((page) => ({
    label: pageSpec.pages[page].name,
    value: page,
  }));

  const getSectionOptions = (linkId) => {
    // Add null check for linkId
    if (!linkId || typeof linkId !== "string") {
      return [];
    }
    const pageId = linkId.split("#")[0];
    const page = pageSpec.pages[pageId];

    // If the page is not found, return an empty array
    if (!page) {
      return [];
    }

    const hash = window.location.hash;
    let activeBlockId = "";
    if (hash) {
      activeBlockId = hash.split("#")[1];
    }

    const sectionOptions = get(page, "blocks_order", [])
      .map((block) => {
        if (block !== activeBlockId) {
          return {
            label: page.blocks[block].title,
            value: block,
          };
        }
        return null;
      })
      .filter(Boolean);
    return sectionOptions;
  };

  return (
    <div className="w-full flex flex-col gap-4">
      <Controller
        name={`${name}.ctaText`}
        control={control}
        rules={{
          required: ctaRequired ? `${ctaLabel} is required` : false,
          validate: (value) => {
            if (!isBothRequired) return true;
            const link = getValues(`${name}.link`);
            if (!value && link) {
              return "Both CTA text and link are required if either is filled";
            }
            if ((value && link) || (!value && !link)) {
              clearErrors(`${name}.link`);
            }
            return true;
          },
        }}
        render={({ field, fieldState }) => (
          <TextInput
            label={ctaLabel}
            placeholder={ctaPlaceholder}
            value={field.value}
            onChange={(value) => {
              field.onChange(value);
            }}
            required={ctaRequired}
            error={fieldState.error}
          />
        )}
      />
      <LabeledField label={linkLabel} required={linkRequired}>
        <Controller
          name={`${name}.navigationType`}
          control={control}
          render={({ field }) => (
            <EverButtonGroup
              className="bg-ever-base-200 w-full h-9"
              activeBtnType="text"
              activeBtnColor="primary"
              defActiveBtnIndex={field.value === "external" ? 0 : 1}
            >
              <EverButton
                className="w-1/2 h-7 text-xs font-medium"
                onClick={() => {
                  field.onChange("external");
                  setValue(`${name}.link`, "");
                }}
              >
                External URL
              </EverButton>
              <EverButton
                className="w-1/2 h-7 text-xs font-medium"
                onClick={() => {
                  field.onChange("internal");
                  setValue(`${name}.link`, "");
                }}
              >
                Deal Room Page
              </EverButton>
            </EverButtonGroup>
          )}
        />
        <Controller
          name={`${name}.link`}
          control={control}
          rules={{
            required: linkRequired ? `${linkLabel} is required` : false,
            validate: (value) => {
              if (!isBothRequired) return true;
              const ctaText = getValues(`${name}.ctaText`);
              if (!value && ctaText) {
                return "Both CTA text and link are required if either is filled";
              }
              if ((value && ctaText) || (!value && !ctaText)) {
                clearErrors(`${name}.ctaText`);
              }
              return true;
            },
          }}
          render={({ field, fieldState }) => (
            <LabeledField label="" error={fieldState.error}>
              {getValues(`${name}.navigationType`) === "external" ? (
                <EverInput
                  {...field}
                  value={field.value}
                  placeholder={linkPlaceholder}
                  required={linkRequired}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              ) : (
                <div className="flex flex-col gap-2">
                  <EverSelect
                    onChange={(value) => field.onChange(value)}
                    value={
                      field.value && typeof field.value === "string"
                        ? field.value.split("#")[0]
                        : null
                    }
                    options={pageOptions}
                    placeholder="Select page"
                  />
                  {field.value && (
                    <EverSelect
                      onChange={(value) =>
                        field.onChange(field.value + "#" + value)
                      }
                      value={
                        field.value && typeof field.value === "string"
                          ? field.value.split("#")[1] ?? null
                          : null
                      }
                      options={getSectionOptions(field.value)}
                      placeholder="Select section"
                    />
                  )}
                </div>
              )}
            </LabeledField>
          )}
        />
      </LabeledField>
    </div>
  );
};

Button.propTypes = {
  button: PropTypes.object,
  name: PropTypes.string.isRequired,
  pageSpec: PropTypes.object.isRequired,
};

Button.defaultProps = {
  button: {},
};

export default ButtonGroup;
