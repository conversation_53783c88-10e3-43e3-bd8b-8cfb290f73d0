import { isEmpty, get, startsWith, cloneDeep, isNil } from "lodash";
import { useState, useRef, useCallback } from "react";
import { useMutation } from "react-query";
import { useParams, useLocation } from "react-router-dom";
import { useSetRecoilState } from "recoil";
import { v4 as uuidv4 } from "uuid";

import { breadcrumbAtom } from "~/GlobalStores/atoms";
import { showToastMessage } from "~/v2/components";
import { createHookProvider } from "~/v2/features/cpq/components";
import {
  PAGE_TYPES,
  STATUS_OPTIONS,
} from "~/v2/features/cpq/deal-room/constants";
import { useF<PERSON>ch<PERSON><PERSON>, useNavigationWithBack } from "~/v2/features/cpq/hooks";
import { fetchBrandData, replaceUrl } from "~/v2/features/cpq/utils";
import theme from "~/v2/themes/blueBias23";

import {
  BLOCK_ATTRIBUTES_BY_TYPE,
  API,
  TEMPLATE_API_ENDPOINTS,
  DEALROOM_API_ENDPOINTS,
} from "../constants";
import { getCategories, processBlockFiles, showErrorMessage } from "../utils";
import { dealRoomSchema, dealRoomTemplateSchema } from "../validation";

/**
 * Returns the correct ID key and value for deal room or template.
 * @param {boolean} isTemplate
 * @param {string} dealRoomId
 * @returns {{idKey: string, idValue: string}}
 */
const getIdConfig = (isTemplate, dealRoomId) => {
  const idKey = isTemplate ? "deal_room_template_id" : "deal_room_id";
  return { idKey, idValue: dealRoomId };
};

/**
 * Returns the default data structure for a deal room or template.
 * @param {boolean} isTemplate
 * @param {string} dealRoomId
 * @returns {Object}
 */
const getDefaultData = (isTemplate, dealRoomId) => {
  const { idKey, idValue } = getIdConfig(isTemplate, dealRoomId);

  if (isTemplate) {
    return {
      [idKey]: idValue,
      deal_room_template_name: "",
      deal_room_template_description: "",
      deal_room_page_spec: {},
      deal_room_template_status: STATUS_OPTIONS.INACTIVE,
    };
  }

  return {
    [idKey]: idValue,
    deal_room_name: "",
    deal_room_description: "",
    deal_room_page_spec: {},
    deal_room_status: STATUS_OPTIONS.DRAFT,
  };
};

/**
 * Fetches company data from Brandfetch API for logo enrichment.
 * @param {Object} param0
 * @returns {Promise<Array>}
 */
const getCompanies = async ({ searchTerm }) => {
  try {
    const { response, data } = await fetchBrandData(searchTerm);

    if (!response.ok) {
      return []; // Return empty array on error
    }

    return data;
  } catch (error) {
    console.error("Error loading company data:", error);
    return []; // Return empty array on error
  }
};

/**
 * Shows a toast error message for property save failures.
 * @param {any} error
 */
const onSavePropertiesError = (error) => {
  showErrorMessage(error, "Failed to update section!");
};

/**
 * Main builder hook for deal room and template editing.
 * Handles all state, mutations, and actions for the builder UI.
 * @param {Object} param0
 * @returns {Object} Builder context
 */
const useBuilderHook = ({ isTemplate }) => {
  // Setup API endpoints and validation schema based on mode
  const { fetchEndpoint } = useFetchApi(
    isTemplate ? TEMPLATE_API_ENDPOINTS : DEALROOM_API_ENDPOINTS
  );
  const validationSchema = useRef(
    isTemplate ? dealRoomTemplateSchema : dealRoomSchema
  );
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  const { navigate, navigateBack } = useNavigationWithBack();
  const { dealRoomId } = useParams();
  const { search, hash } = useLocation();
  const formMethods = useRef(null);
  const query = new URLSearchParams(search);

  const expandedPagesRef = useRef([]);
  // State for deal room data, categories, active page/section, etc.
  const [dealRoomData, setDealRoomData] = useState(() =>
    getDefaultData(isTemplate, dealRoomId)
  );
  const [categories, setCategories] = useState({});
  const [activePage, setActivePage] = useState(() => {
    const pageId = query.get("page");
    return pageId ?? null;
  });
  const [activeSection, setActiveSection] = useState(() =>
    hash ? hash.split("#")[1] : null
  );
  const [previewMode, setPreviewMode] = useState(() => {
    return query.get("view") ?? null;
  });
  const [expandedPages, setExpandedPages] = useState(() =>
    activePage && ![PAGE_TYPES.HEADER, PAGE_TYPES.WELCOME].includes(activePage)
      ? new Set([activePage])
      : new Set()
  );
  const [buyersLogoUrl, setBuyersLogoUrl] = useState("");
  const {
    getSchema,
    updateSchema,
    updatePagesSchema,
    updatePageSchema,
    updateBlockSchema,
    cloneSchema,
    deleteSchema,
    syncSchema,
  } = validationSchema.current;

  const setFormMethods = useCallback((methods) => {
    formMethods.current = methods;
  }, []);

  /**
   * Helper to build request body with correct ID for API calls.
   * @param {Object} additionalData
   * @returns {Object}
   */
  const buildRequestBody = useCallback(
    (additionalData = {}) => {
      const { idKey, idValue } = getIdConfig(isTemplate, dealRoomId);
      return {
        [idKey]: idValue,
        ...additionalData,
      };
    },
    [isTemplate, dealRoomId]
  );

  /**
   * Sets the active page to the welcome page and clears section.
   */
  const setActivePageToWelcome = () => {
    setActivePage(PAGE_TYPES.WELCOME);
    setActiveSection(null);
    replaceUrl({ page: PAGE_TYPES.WELCOME }, null);
  };

  /**
   * Sets the buyer's logo URL from customer data, using Brandfetch if needed.
   * @param {Object} data
   */
  const setBuyersLogo = async (data) => {
    const customerLogo = get(data, "customer_logo", {});
    const { type, url } = customerLogo;
    if (url) {
      if (type === "brand_fetch") {
        try {
          const companies = await getCompanies({ searchTerm: url });
          if (Array.isArray(companies) && companies.length > 0) {
            setBuyersLogoUrl(companies[0]?.icon);
          }
        } catch (error) {
          console.error("Error loading company data:", error);
        }
      } else {
        setBuyersLogoUrl(url);
      }
    }
  };

  /**
   * Updates local state and breadcrumbs after deal room/template fetch or update.
   * @param {Object} data
   */
  const onDealRoomUpdate = (data) => {
    setDealRoomData(data);
    setCategories(getCategories(data.deal_room_page_spec));
    if (isTemplate) {
      setBreadcrumbName([
        {
          index: 1,
          title: `Deal Room Template - ${data.deal_room_template_name}`,
          name: "Edit Template",
        },
      ]);
    } else {
      setBreadcrumbName([
        {
          index: 1,
          title: `Deal Room - ${data.deal_room_name}`,
          name: "Edit Deal Room",
        },
      ]);
    }
  };

  // --- Mutations ---

  /**
   * Fetches the deal room/template data from the backend.
   */
  const getDealRoom = useMutation(
    () =>
      fetchEndpoint(API.GET_DEALROOM, {
        body: buildRequestBody(),
        validateSchema: getSchema,
      }),
    {
      onSuccess: (data) => {
        onDealRoomUpdate(data);
        if (!isTemplate) {
          setBuyersLogo(data);
        }

        // If active page is set but no active section, set the first section of the page
        if (
          activePage &&
          !activeSection &&
          data.deal_room_page_spec.pages[activePage]
        ) {
          const page = data.deal_room_page_spec.pages[activePage];
          updateActiveState(activePage, page?.blocks_order?.[0] ?? null);
        }
        if (!activePage || !data.deal_room_page_spec.pages[activePage]) {
          // If no active page or page missing, go to welcome
          setActivePageToWelcome();
        }
        // If no expanded pages, expand the first one
        if (
          expandedPages.size === 0 &&
          data.deal_room_page_spec.pages_order.length > 0
        ) {
          setExpandedPages(new Set([data.deal_room_page_spec.pages_order[0]]));
        }
      },
      onError: () => {
        showErrorMessage({}, "Something went wrong! Please try again later.");
      },
    }
  );

  /**
   * Updates the deal room/template data (main mutation for save).
   */
  const updateDealRoom = useMutation(
    (body) =>
      fetchEndpoint(API.UPDATE_DEALROOM, {
        body,
        validateSchema: updateSchema,
      }),
    {
      onSuccess: (_, variables) => {
        const { reset, getValues } = formMethods.current;
        // Merge updated variables into form state
        const data = {
          ...getValues(),
          ...variables,
        };
        onDealRoomUpdate(data);
        reset(data, {
          keepDirty: true,
          keepErrors: true,
          keepDirtyValues: true,
        });
      },
      onError: (error) => {
        showErrorMessage(error, "Failed to update template!");
      },
    }
  );

  /**
   * Updates the deal room pages (add, delete, reorder, etc).
   */
  const updateDealRoomPages = useMutation(
    (body) =>
      fetchEndpoint(API.UPDATE_DEALROOM_PAGES, {
        body,
        validateSchema: updatePagesSchema,
      }),
    {
      onSuccess: (_, variables) => {
        const { reset, getValues } = formMethods.current;

        // Merge updated variables into form state
        const data = {
          ...getValues(),
          ...variables,
        };
        onDealRoomUpdate(data);

        // If a page was deleted, go to welcome page
        if (variables?.type === "delete") {
          updateActiveState(PAGE_TYPES.WELCOME, null);
          reset(data, { keepDirty: false });
        } else {
          reset(data, {
            keepDirty: true,
            keepErrors: true,
            keepDirtyValues: true,
          });
        }
      },
      onError: (error) => {
        showErrorMessage(error, "Failed to update template pages!");
      },
    }
  );

  /**
   * Updates a single block within a deal room/template.
   */
  const updateBlock = useMutation(
    (body) =>
      fetchEndpoint(API.UPDATE_DEALROOM_BLOCK, {
        body,
        validateSchema: updateBlockSchema,
      }),
    {
      onError: (error) => {
        showErrorMessage(error, "Failed to update section!");
      },
    }
  );

  /**
   * Updates a single page within a deal room/template.
   */
  const updatePage = useMutation(
    (body) =>
      fetchEndpoint(API.UPDATE_DEALROOM_PAGE, {
        body,
        validateSchema: updatePageSchema,
      }),
    {
      onError: (error) => {
        showErrorMessage(error, "Failed to update page!");
      },
    }
  );

  /**
   * Clones a deal room/template.
   */
  const cloneDealRoom = useMutation(
    (body) =>
      fetchEndpoint(API.CLONE_DEALROOM, {
        body,
        validateSchema: cloneSchema,
      }),
    {
      onSuccess: (data) => {
        if (isTemplate) {
          navigate(
            `/cpq/settings/deal-room-templates/${data.deal_room_template_id}`
          );
        } else {
          navigate(`/cpq/deal-rooms/${data.deal_room_id}`);
        }
      },
    }
  );

  /**
   * Deletes a deal room/template.
   */
  const deleteDealRoom = useMutation(
    (body) =>
      fetchEndpoint(API.DELETE_DEALROOM, {
        body,
        validateSchema: deleteSchema,
      }),
    { onSuccess: () => navigateBack() }
  );

  /**
   * Syncs a deal room with its template.
   */
  const syncWithTemplate = useMutation(
    (body) =>
      fetchEndpoint(API.SYNC_DEALROOM_WITH_TEMPLATE, {
        body,
        validateSchema: syncSchema,
      }),
    {
      onMutate: () => {
        setExpandedPages(new Set());
        setActivePageToWelcome();
      },
      onSuccess: () => {
        getDealRoom.mutate(null, {
          onSuccess: (data) => {
            const { reset } = formMethods.current;
            reset(data, { keepDirty: false });
          },
        });
      },
    }
  );

  /**
   * Sets the active page and section.
   * @param {string} pageId
   * @param {string|null} sectionId
   */
  const updateActiveState = (pageId, sectionId) => {
    setActivePage(pageId);
    setActiveSection(sectionId);
    replaceUrl({ page: pageId }, sectionId);
  };

  /**
   * Expands the set of expanded pages.
   * @param {Set<string>} pageIds
   */
  const onExpandPage = (pageIds) => {
    setExpandedPages(new Set(pageIds));
  };

  /**
   * Generates a new page name.
   * @param {string|null} pageId
   * @returns {string}
   */
  const getNewPageName = (pageId = null) => {
    const pagesOrder = get(dealRoomData, "deal_room_page_spec.pages_order", []);

    let newPageName = "";
    if (isEmpty(pageId)) {
      // If no page id is provided, add a new page flow.
      // This is used when the user clicks the "Add Page" button.
      const highestUntitledPageNumber =
        pagesOrder
          .map((pageId) => {
            const page = dealRoomData.deal_room_page_spec.pages[pageId];
            if (
              startsWith(page.name, "Page") &&
              !isEmpty(page.name.split("Page ")[1]) &&
              !Number.isNaN(Number(page.name.split("Page ")[1]))
            ) {
              return Number(page.name.split("Page ")[1]);
            }
            return 0;
          })
          .sort((a, b) => b - a)[0] ?? 0;

      newPageName = `Page ${highestUntitledPageNumber + 1}`;
    } else {
      // If a page id is provided, clone the page.
      // This is used when the user clicks the "Clone Page" button.
      const page = dealRoomData.deal_room_page_spec.pages[pageId];
      const pageNames = new Set(
        pagesOrder.map((pageId) => {
          const page = dealRoomData.deal_room_page_spec.pages[pageId];
          return page.name;
        })
      );
      newPageName = `${page.name} (Copy)`;
      let copyIndex = 1;
      while (pageNames.has(newPageName)) {
        newPageName = `${page.name} (Copy ${copyIndex})`;
        copyIndex += 1;
      }
    }

    return newPageName;
  };

  /**
   * Adds a new page to the deal room/template.
   */
  const onAddPage = () => {
    const tempData = cloneDeep(formMethods.current.getValues());
    const pagesOrder = get(tempData, "deal_room_page_spec.pages_order", []);
    const newPageId = `page_${uuidv4()}`;
    pagesOrder.push(newPageId);
    tempData.deal_room_page_spec.pages_order = pagesOrder;
    tempData.deal_room_page_spec.pages = {
      ...tempData.deal_room_page_spec.pages,
      [newPageId]: {
        page_id: newPageId,
        name: getNewPageName(),
        blocks: {},
        blocks_order: [],
      },
    };

    // Call mutation to update pages in backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({ deal_room_page_spec: tempData.deal_room_page_spec })
    );
  };

  /**
   * Adds a new section to a page.
   * @param {string} pageId
   * @param {string} sectionType
   * @param {number|null} index
   */
  const onAddSection = (pageId, sectionType, index = null) => {
    const tempData = cloneDeep(formMethods.current.getValues());
    const page = tempData.deal_room_page_spec.pages[pageId];

    const newBlockId = `block_${uuidv4()}`;
    if (index === null) {
      page.blocks_order.push(newBlockId);
    } else {
      page.blocks_order.splice(index + 1, 0, newBlockId);
    }
    const isEvenBlock = page.blocks_order.length % 2 === 0;
    const blockProperties = BLOCK_ATTRIBUTES_BY_TYPE[sectionType];
    page.blocks[newBlockId] = {
      block_id: newBlockId,
      type: sectionType,
      ...blockProperties,
      background: {
        ...blockProperties.background,
        background_color: isEvenBlock
          ? theme.colors.dealroom.chartColors[12]
          : theme.colors.dealroom.base.DEFAULT,
      },
    };
    tempData.deal_room_page_spec.pages[pageId] = page;

    // Call mutation to update pages in backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({ deal_room_page_spec: tempData.deal_room_page_spec }),
      {
        onSuccess: () => {
          updateActiveState(pageId, newBlockId);
        },
      }
    );
  };

  /**
   * Updates the name of a page.
   * @param {string} pageId
   * @param {Object} pageSpec
   */
  const onUpdatePageName = (pageId, pageSpec) => {
    const payload = isTemplate
      ? {
          deal_room_template_page_id: pageId,
          deal_room_template_page_spec: pageSpec,
        }
      : {
          deal_room_page_id: pageId,
          deal_room_page_spec: pageSpec,
        };

    updatePage.mutate(buildRequestBody(payload));
  };

  /**
   * Clones a page.
   * @param {string} pageId
   */
  const onClonePage = (pageId) => {
    const tempData = cloneDeep(formMethods.current.getValues());
    const pagesOrder = tempData.deal_room_page_spec.pages_order;
    const page = tempData.deal_room_page_spec.pages[pageId];
    const index = pagesOrder.indexOf(pageId);

    const newPageId = `page_${uuidv4()}`;

    // Clone blocks with new IDs
    const oldBlocks = page.blocks;
    const oldBlocksOrder = page.blocks_order;
    const newBlocks = {};
    const newBlocksOrder = [];

    for (const oldBlockId of oldBlocksOrder) {
      const newBlockId = `block_${uuidv4()}`;
      newBlocksOrder.push(newBlockId);
      newBlocks[newBlockId] = {
        ...oldBlocks[oldBlockId],
        block_id: newBlockId,
      };
    }

    pagesOrder.splice(index + 1, 0, newPageId);
    tempData.deal_room_page_spec.pages_order = pagesOrder;
    tempData.deal_room_page_spec.pages = {
      ...tempData.deal_room_page_spec.pages,
      [newPageId]: {
        ...page,
        page_id: newPageId,
        name: getNewPageName(pageId),
        blocks: newBlocks,
        blocks_order: newBlocksOrder,
      },
    };

    // Call mutation to update pages in backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({ deal_room_page_spec: tempData.deal_room_page_spec })
    );
  };

  /**
   * Deletes a page.
   * @param {string} pageId
   */
  const onDeletePage = (pageId) => {
    const tempData = cloneDeep(formMethods.current.getValues());
    const pagesOrder = tempData.deal_room_page_spec.pages_order;
    const index = pagesOrder.indexOf(pageId);
    pagesOrder.splice(index, 1);
    tempData.deal_room_page_spec.pages_order = pagesOrder;
    delete tempData.deal_room_page_spec.pages[pageId];

    // Call mutation to update pages in backend and state, with type 'delete' to trigger special handling
    updateDealRoomPages.mutate(
      buildRequestBody({
        deal_room_page_spec: tempData.deal_room_page_spec,
        type: "delete",
      })
    );
  };

  /**
   * Handles page drag end.
   * @param {Object} result
   */
  const handlePageDragEnd = (result) => {
    const { source, destination } = result;
    if (!destination || source.index === destination.index) return;

    const tempData = cloneDeep(formMethods.current.getValues());
    const pagesOrder = tempData.deal_room_page_spec.pages_order;
    const [pageToMove] = pagesOrder.splice(source.index, 1);
    pagesOrder.splice(destination.index, 0, pageToMove);
    tempData.deal_room_page_spec.pages_order = pagesOrder;

    // Call mutation to update pages in backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({ deal_room_page_spec: tempData.deal_room_page_spec })
    );
  };

  /**
   * Handles section drag end.
   * @param {Object} result
   */
  const handleSectionDragEnd = (result) => {
    const { source, destination } = result;
    if (!destination) return;

    const tempData = cloneDeep(formMethods.current.getValues());
    let sectionToMove = null;

    // If dragged section is dropped on the same page, update the blocks order
    if (source.droppableId === destination.droppableId) {
      const blocksOrder =
        tempData.deal_room_page_spec.pages[source.droppableId].blocks_order;
      sectionToMove = blocksOrder.splice(source.index, 1)[0];
      blocksOrder.splice(destination.index, 0, sectionToMove);
      tempData.deal_room_page_spec.pages[source.droppableId].blocks_order =
        blocksOrder;
    } else {
      // If dragged section is dropped on a different page, add the block to the destination page and remove the block from the source page
      const sourcePage = tempData.deal_room_page_spec.pages[source.droppableId];
      const destinationPage =
        tempData.deal_room_page_spec.pages[destination.droppableId];
      sectionToMove = sourcePage.blocks_order.splice(source.index, 1)[0];
      const blockToMove = sourcePage.blocks[sectionToMove];
      delete sourcePage.blocks[sectionToMove];
      destinationPage.blocks_order.splice(destination.index, 0, sectionToMove);
      destinationPage.blocks[sectionToMove] = blockToMove;
    }

    // Call mutation to update pages in backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({ deal_room_page_spec: tempData.deal_room_page_spec }),
      {
        onSuccess: () => {
          // If the moved section is the active section, update active state
          if (sectionToMove === activeSection) {
            updateActiveState(destination.droppableId, sectionToMove);
          }
          // Expand the destination page after move
          setExpandedPages(
            new Set([...expandedPagesRef.current, destination.droppableId])
          );
          expandedPagesRef.current = [];
        },
      }
    );
  };

  /**
   * Handles drag end for pages or sections.
   * @param {Object} result
   */
  const onDragEnd = (result) => {
    if (result.type === "PAGE") {
      handlePageDragEnd(result);
    } else if (result.type === "SECTION") {
      handleSectionDragEnd(result);
    }
  };

  // This is used to expand the page when the user is dragging a page over it
  const onPageDragOver = (draggedPageId) => {
    if (expandedPagesRef.current.length === 0) {
      expandedPagesRef.current = [...expandedPages];
    }
    if (!expandedPages.has(draggedPageId)) {
      setExpandedPages(new Set([...expandedPages, draggedPageId]));
    }
  };

  /**
   * Changes the order of sections within a page.
   * @param {string} pageId
   * @param {string} sectionId
   * @param {string} direction
   */
  const onSectionOrderChange = (pageId, sectionId, direction) => {
    const tempPageSpec = cloneDeep(
      formMethods.current.getValues("deal_room_page_spec")
    );
    const page = tempPageSpec.pages[pageId];
    const sectionOrder = page.blocks_order;
    const index = sectionOrder.indexOf(sectionId);
    const newSectionOrder = [...sectionOrder];

    if (direction === "up") {
      if (index === 0) {
        return;
      }
      // Swap with previous
      [newSectionOrder[index - 1], newSectionOrder[index]] = [
        newSectionOrder[index],
        newSectionOrder[index - 1],
      ];
    } else if (direction === "down") {
      if (index === sectionOrder.length - 1) {
        return;
      }
      // Swap with next
      [newSectionOrder[index], newSectionOrder[index + 1]] = [
        newSectionOrder[index + 1],
        newSectionOrder[index],
      ];
    }

    // Call mutation to update section order in backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({
        deal_room_page_spec: {
          ...tempPageSpec,
          pages: {
            ...tempPageSpec.pages,
            [pageId]: { ...page, blocks_order: newSectionOrder },
          },
        },
      })
    );
  };

  /**
   * Deletes a section from a page.
   * @param {string} pageId
   * @param {string} sectionId
   */
  const onSectionDelete = (pageId, sectionId) => {
    const { reset, getValues } = formMethods.current;
    const tempPageSpec = cloneDeep(
      formMethods.current.getValues("deal_room_page_spec")
    );
    const page = tempPageSpec.pages[pageId];
    const sectionOrder = page.blocks_order;
    const index = sectionOrder.indexOf(sectionId);
    delete page.blocks[sectionId];
    sectionOrder.splice(index, 1);

    // If the deleted section was active, update active section
    if (activeSection === sectionId) {
      const newActiveIndex = index === 0 ? index : index - 1;
      updateActiveState(pageId, sectionOrder[newActiveIndex] ?? null);
      reset(getValues(), { keepDirty: false });
    }

    // Call mutation to update backend and state
    updateDealRoomPages.mutate(
      buildRequestBody({
        deal_room_page_spec: {
          ...tempPageSpec,
          pages: {
            ...tempPageSpec.pages,
            [pageId]: { ...page, blocks_order: sectionOrder },
          },
        },
      })
    );
  };

  /**
   * Saves block properties.
   * @param {string} blockType
   * @param {Object} data
   */
  const onSaveProperties = (blockType, data) => {
    let pageData = {};
    const page = data.deal_room_page_spec.pages[activePage];
    const { reset } = formMethods.current;

    const handleSuccess = () => {
      onDealRoomUpdate(data);
      reset(data, { keepDirty: false });
    };

    if (isNil(activeSection)) {
      // If editing a page (not a block)
      pageData = isTemplate
        ? {
            deal_room_template_id: dealRoomId,
            deal_room_template_page_id: page.page_id,
            deal_room_template_page_spec: page,
          }
        : {
            deal_room_id: dealRoomId,
            deal_room_page_id: page.page_id,
            deal_room_page_spec: page,
          };

      // Call mutation to update page in backend and state
      updatePage.mutate(pageData, { onSuccess: handleSuccess });
    } else {
      // If editing a block
      const block = get(page, `blocks.${activeSection}`);
      const formData = new FormData();

      if (isTemplate) {
        formData.append("deal_room_template_id", dealRoomId);
        formData.append("deal_room_template_page_id", activePage);
        formData.append("deal_room_template_page_block_id", activeSection);
        formData.append(
          "deal_room_template_page_block_order",
          JSON.stringify(get(page, "blocks_order"))
        );
      } else {
        formData.append("deal_room_id", dealRoomId);
        formData.append("deal_room_page_id", activePage);
        formData.append("deal_room_page_block_id", activeSection);
        formData.append(
          "deal_room_page_block_order",
          JSON.stringify(get(page, "blocks_order"))
        );
      }

      // Attach block data and files
      if (!isNil(activeSection) && blockType) {
        const { cleanedBlock } = processBlockFiles(block, formData);

        if (isTemplate) {
          formData.append(
            "deal_room_template_page_block",
            JSON.stringify(cleanedBlock)
          );
        } else {
          formData.append("deal_room_page_block", JSON.stringify(cleanedBlock));
        }
      }

      // Call mutation to update block in backend and state
      updateBlock.mutate(formData, { onSuccess: handleSuccess });
    }
  };

  /**
   * Updates the template.
   * @param {Object} fieldsToUpdate
   * @param {Object} options
   */
  const onUpdateTemplate = (fieldsToUpdate, options = {}) => {
    updateDealRoom.mutate(buildRequestBody(fieldsToUpdate), options);
  };

  /**
   * Updates the deal room.
   * @param {FormData} formData
   * @param {Object} options
   */
  const onUpdateDealRoom = (formData, options = {}) => {
    formData.append("deal_room_id", dealRoomId);
    updateDealRoom.mutate(formData, options);
  };

  /**
   * Clones the deal room/template.
   * @param {Object} messages
   */
  const onClone = (messages) => {
    const clonePromise = cloneDealRoom.mutateAsync(buildRequestBody());
    showToastMessage(clonePromise, { messages });
  };

  /**
   * Deletes the deal room/template.
   * @param {Object} messages
   */
  const onDelete = (messages) => {
    const deletePromise = deleteDealRoom.mutateAsync(buildRequestBody());
    showToastMessage(deletePromise, { messages });
  };

  /**
   * Syncs the deal room with its template.
   * @param {Object} messages
   */
  const onSyncWithTemplate = (messages) => {
    const syncPromise = syncWithTemplate.mutateAsync(buildRequestBody());
    showToastMessage(syncPromise, { messages });
  };

  return {
    dealRoomId,
    isTemplate,
    isLoading:
      getDealRoom.isLoading ||
      updateDealRoom.isLoading ||
      updateDealRoomPages.isLoading ||
      updateBlock.isLoading ||
      updatePage.isLoading ||
      cloneDealRoom.isLoading ||
      deleteDealRoom.isLoading ||
      syncWithTemplate.isLoading,
    dealRoomData,
    categories,
    activePage,
    activeSection,
    previewMode,
    buyersLogoUrl,
    updateActiveState,
    setPreviewMode,
    setFormMethods,

    // Sidebar
    expandedPages,
    onAddPage,
    onDeletePage,
    onClonePage,
    onExpandPage,
    onDragEnd,
    onPageDragOver,
    onAddSection,
    onSectionOrderChange,
    onSectionDelete,
    onSaveProperties,
    onSavePropertiesError,
    onUpdateTemplate,
    onUpdateDealRoom,
    onUpdatePageName,

    // Mutations
    getDealRoom,
    onClone,
    onDelete,
    onSyncWithTemplate,
  };
};

/**
 * useBuilder is a custom hook that provides all state and actions for the Deal Room builder and template builder.
 *
 * @returns {Object} Builder context with state, actions, and mutation handlers for the builder UI.
 * @property {string} dealRoomId - The current deal room or template ID.
 * @property {boolean} isTemplate - Whether the builder is in template mode.
 * @property {boolean} isLoading - Loading state for all builder mutations.
 * @property {Object} dealRoomData - The current deal room/template data.
 * @property {Object} categories - Categories for the builder.
 * @property {string} activePage - The currently active page ID.
 * @property {string} activeSection - The currently active section ID.
 * @property {string|null} previewMode - The current preview mode.
 * @property {string} buyersLogoUrl - The buyer's logo URL.
 * @property {Function} updateActiveState - Set the active page and section.
 * @property {Function} setPreviewMode - Set the preview mode.
 * @property {Function} setFormMethods - Set the form methods ref.
 * @property {Set<string>} expandedPages - Set of expanded page IDs.
 * @property {Function} onAddPage - Add a new page.
 * @property {Function} onDeletePage - Delete a page.
 * @property {Function} onClonePage - Clone a page.
 * @property {Function} onExpandPage - Expand a page.
 * @property {Function} onDragEnd - Handle drag end for pages/sections.
 * @property {Function} onPageDragOver - Handle drag over for pages.
 * @property {Function} onAddSection - Add a new section to a page.
 * @property {Function} onSectionOrderChange - Change the order of sections.
 * @property {Function} onSectionDelete - Delete a section from a page.
 * @property {Function} onSaveProperties - Save block properties.
 * @property {Function} onSavePropertiesError - Error handler for saving properties.
 * @property {Function} onUpdateTemplate - Update the template.
 * @property {Function} onUpdateDealRoom - Update the deal room.
 * @property {Object} getDealRoom - Mutation for fetching the deal room.
 * @property {Function} onClone - Clone the deal room/template.
 * @property {Function} onDelete - Delete the deal room/template.
 * @property {Function} onSyncWithTemplate - Sync the deal room with its template.
 * @property {Function} onUpdatePageName - Update the name of a page.
 */
const { Provider: BuilderProvider, useHookContext: useBuilder } =
  createHookProvider(useBuilderHook);

export { BuilderProvider, useBuilder };
