import PropTypes from "prop-types";
import { Fragment } from "react";
import { useFormContext, useFieldA<PERSON><PERSON>, Controller } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import TabComponent from "./TabComponent";
import TextInput from "./TextInput";

const defaultMetric = () => ({
  key: uuidv4(),
  metric_data: "",
  metric_description: "",
});

/**
 * MetricsList renders a list of metric items, each with data and description fields, using TabComponent.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the metrics section.
 * @param {string} props.name - Field name prefix for form fields.
 */
const MetricsList = ({ label = "", name }) => {
  const { control } = useFormContext();

  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
  });

  const handleUpdate = (type, ...args) => {
    switch (type) {
      case "move": {
        const [sourceIndex, destinationIndex] = args;
        move(sourceIndex, destinationIndex);
        break;
      }
      case "add": {
        append(args[0]);
        break;
      }
      case "remove": {
        remove(args[0]);
        break;
      }
      default: {
        break;
      }
    }
  };

  return (
    <TabComponent
      items={fields}
      label={label}
      tabLabel="M"
      onUpdate={handleUpdate}
      getDefaultItem={defaultMetric}
    >
      {(item, index) => (
        <Fragment key={item.key}>
          <Controller
            name={`${name}.${index}.metric_data`}
            control={control}
            rules={{
              required: "Metric data is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Metric data"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.metric_description`}
            control={control}
            rules={{
              required: "Description is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Description"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
        </Fragment>
      )}
    </TabComponent>
  );
};

MetricsList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
};

MetricsList.defaultProps = {
  label: "",
};

export default MetricsList;
