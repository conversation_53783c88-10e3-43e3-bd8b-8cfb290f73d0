import { useState, useEffect, useCallback, useRef } from "react";
import { twMerge } from "tailwind-merge";

/*
Sample Example:

const { data, ...remainingProps } = props
const elements = [
    { type: "image", condition: !!data?.file?.file_url },
    { type: "tagline", condition: !!data?.tagline },
    { type: "title", condition: !!data?.title },
    { type: "description", condition: !!data?.description },
    { type: "buttons", condition: data?.buttons?.length > 0 },
  ].filter((el) => el.condition);

  const { presentationProps } = usePresentationMode({
    ...remainingProps,
    elements,
  });

  return (
    <div {...presentationProps("image", { className: "w-full h-full" })}>
      <img src={data?.file?.file_url} alt={data?.file?.file_name} />
    </div>
  );
*/

/**
 * usePresentationMode is a custom hook for handling presentation mode element highlighting and navigation.
 *
 * @param {Object} options - Configuration options
 * @param {boolean} options.isPresentationMode - Whether presentation mode is active
 * @param {boolean} options.isActiveSlide - Whether this slide is currently active
 * @param {boolean} options.isVisitedSlide - Whether this slide has been visited
 * @param {Array} options.elements - Array of element configurations with types
 * @returns {Object} Presentation mode utilities
 * @property {Function} presentationProps - Returns props for a given element type for presentation mode
 */
export const usePresentationMode = ({
  isPresentationMode = false,
  isActiveSlide = false,
  isVisitedSlide = false,
  elements = [],
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [activeElementIndex, setActiveElementIndex] = useState(-1);
  const [hoveredElementType, setHoveredElementType] = useState(null);
  const eventListenerRef = useRef(null);

  /**
   * Triggers animation when the active element changes.
   */
  useEffect(() => {
    if (isPresentationMode && isActiveSlide && activeElementIndex >= 0) {
      setIsAnimating(true);
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isPresentationMode, isActiveSlide, activeElementIndex]);

  /**
   * Go to next element in the sequence.
   */
  const handleNextElement = useCallback(() => {
    if (activeElementIndex < elements.length - 1) {
      setActiveElementIndex((prev) => prev + 1);
    }
  }, [activeElementIndex, elements.length]);

  /**
   * Go to previous element, or reset if at the start.
   */
  const handlePreviousElement = useCallback(() => {
    if (activeElementIndex > 0) {
      setActiveElementIndex((prev) => prev - 1);
    } else if (activeElementIndex !== -1) {
      setActiveElementIndex(-1);
    }
  }, [activeElementIndex]);

  /**
   * Keyboard navigation: up/down arrows to move between elements.
   */
  useEffect(() => {
    if (!isPresentationMode || !isActiveSlide || isVisitedSlide) {
      // Clean up existing listener if not in presentation mode
      if (eventListenerRef.current) {
        document.removeEventListener("keydown", eventListenerRef.current);
        eventListenerRef.current = null;
      }
      return;
    }

    /**
     * Handles keydown events for navigation.
     * @param {KeyboardEvent} event
     */
    const handleKeyDown = (event) => {
      switch (event.key) {
        case "ArrowUp": {
          event.preventDefault();
          handlePreviousElement(); // Move to previous element
          break;
        }
        case "ArrowDown": {
          event.preventDefault();
          handleNextElement(); // Move to next element
          break;
        }
        default: {
          break;
        }
      }
    };

    // Store reference for cleanup
    eventListenerRef.current = handleKeyDown;
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      if (eventListenerRef.current) {
        document.removeEventListener("keydown", eventListenerRef.current);
        eventListenerRef.current = null;
      }
    };
  }, [
    isPresentationMode,
    isActiveSlide,
    isVisitedSlide,
    handleNextElement,
    handlePreviousElement,
  ]);

  /**
   * Returns true if the element is currently active.
   * @param {string} elementType
   * @returns {boolean}
   */
  const isElementActive = (elementType) => {
    const elementIndex = elements.findIndex((el) => el.type === elementType);
    return (
      isPresentationMode && isActiveSlide && activeElementIndex === elementIndex
    );
  };

  /**
   * Returns true if the element has already been visited.
   * @param {string} elementType
   * @returns {boolean}
   */
  const isElementVisited = (elementType) => {
    const elementIndex = elements.findIndex((el) => el.type === elementType);
    return (
      isPresentationMode && isActiveSlide && activeElementIndex > elementIndex
    );
  };

  /**
   * Returns true if the element is currently hovered.
   * @param {string} elementType
   * @returns {boolean}
   */
  const isElementHovered = (elementType) => {
    return hoveredElementType === elementType;
  };

  /**
   * Computes classes for an element based on its state (active, visited, hovered).
   * @param {string} elementType
   * @returns {string}
   */
  const getElementClasses = (elementType) => {
    if (!isPresentationMode || !isActiveSlide || isVisitedSlide) return "";

    const elementIndex = elements.findIndex((el) => el.type === elementType);

    if (elementIndex === -1) return ""; // Element not found

    // Check if the last element is active
    const isLastElementActive = activeElementIndex === elements.length - 1;

    if (isElementActive(elementType) || isElementHovered(elementType)) {
      return isAnimating
        ? "blur-0 scale-105 transition-all duration-700 ease-out animate-pulse"
        : "blur-0 scale-105 transition-all duration-700 ease-out";
    } else if (isElementVisited(elementType)) {
      // Remove blur effect when last element is active
      return isLastElementActive
        ? "blur-0 scale-100 transition-all duration-300 ease-in-out"
        : "blur-[1px] scale-100 transition-all duration-300 ease-in-out";
    } else {
      return "transition-all duration-500 ease-in-out blur-md scale-100";
    }
  };

  /**
   * Mouse event handler for entering an element (sets hover state).
   * @param {string} elementType
   */
  const onMouseEnter = useCallback((elementType) => {
    setHoveredElementType(elementType);
  }, []);

  /**
   * Mouse event handler for leaving an element (clears hover state).
   */
  const onMouseLeave = useCallback(() => {
    setHoveredElementType(null);
  }, []);

  /**
   * Returns props for a given element type for presentation mode.
   * @param {string} key
   * @param {Object} param1
   * @returns {Object}
   */
  const presentationProps = (key, { className = "" }) => {
    return {
      className: twMerge(className, getElementClasses(key)),
      onMouseEnter: () => onMouseEnter(key),
      onMouseLeave: () => onMouseLeave(),
    };
  };

  return {
    presentationProps,
  };
};
