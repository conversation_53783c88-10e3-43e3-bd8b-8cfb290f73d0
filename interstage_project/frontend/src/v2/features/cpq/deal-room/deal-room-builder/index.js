import { isEmpty } from "lodash";
import { useEffect } from "react";
import { useForm, FormProvider, useFormContext } from "react-hook-form";
import ReactRouterPrompt from "react-router-prompt";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { EverLoader } from "~/v2/components";
import { UnsavedChangesModal } from "~/v2/features/cpq/deal-room/components";
import {
  UnsavedChangesProvider,
  useUnsavedChanges,
} from "~/v2/features/cpq/deal-room/deal-room-builder/hooks";

import Builder from "./builder";
import Header from "./header";
import { BuilderProvider, useBuilder } from "./hooks";
import PropertiesPanel from "./PropertiesPanel";
import SectionSidebar from "./section-side-bar";

const DealRoom = ({ isTemplate = false }) => {
  return (
    <BuilderProvider isTemplate={isTemplate}>
      <DealRoomRender />
    </BuilderProvider>
  );
};

const DealRoomRender = () => {
  const clientInfo = useRecoilValue(myClientAtom);
  const { getDealRoom, isLoading, setFormMethods, activeSection, activePage } =
    useBuilder();

  const methods = useForm({
    mode: "onChange",
    defaultValues: async () => {
      const data = await getDealRoom.mutateAsync();
      return data;
    },
  });

  useEffect(() => {
    if (methods && !methods.formState.isLoading) {
      setFormMethods(methods);
    }
  }, [methods, methods.formState.isLoading, setFormMethods]);

  if (methods?.formState.isLoading || getDealRoom.isLoading) {
    return (
      <EverLoader
        indicatorType="spinner"
        className="flex h-full"
        wrapperClassName="z-20"
        spinning
      />
    );
  } else if (!getDealRoom.isLoading) {
    if (getDealRoom?.status !== "success") {
      return <div>Something went wrong! Please try again later.</div>;
    } else if (isEmpty(methods.watch())) {
      return null;
    }
  }

  return (
    <FormProvider {...methods}>
      <UnsavedChangesProvider>
        <DealRoomContent
          clientInfo={clientInfo}
          isLoading={isLoading}
          activeSection={activeSection}
          activePage={activePage}
        />
      </UnsavedChangesProvider>
    </FormProvider>
  );
};

const DealRoomContent = ({
  clientInfo,
  isLoading,
  activeSection,
  activePage,
}) => {
  const { formState } = useFormContext();
  const { showUnsavedChangesModal, handleConfirmAction, handleCancelAction } =
    useUnsavedChanges();

  return (
    <div className="flex flex-col h-full">
      <EverLoader
        indicatorType="spinner"
        className="flex flex-col"
        wrapperClassName="z-50"
        spinning={isLoading}
      >
        <>
          <Header clientInfo={clientInfo} />
          <div className="flex h-full gap-5 px-6 py-5 overflow-y-auto bg-[radial-gradient(#D9D9D9_1px,#F5F7FD_1px)] bg-[length:8px_8px]">
            <ReactRouterPrompt when={formState.isDirty}>
              {({ isActive, onConfirm, onCancel }) => (
                <UnsavedChangesModal
                  visible={isActive}
                  onCancel={onCancel}
                  onConfirm={onConfirm}
                />
              )}
            </ReactRouterPrompt>
            <SectionSidebar />
            <Builder clientInfo={clientInfo} />
            <PropertiesPanel key={activeSection || activePage} />
          </div>
        </>
      </EverLoader>

      {/*
        - Centralized unsaved changes modal for section change
        - As we replace the url, React Router Prompt will not work in section change.
        - So we need to handle the unsaved changes manually.
      */}
      <UnsavedChangesModal
        visible={showUnsavedChangesModal}
        onCancel={handleCancelAction}
        onConfirm={handleConfirmAction}
      />
    </div>
  );
};

export default DealRoom;
