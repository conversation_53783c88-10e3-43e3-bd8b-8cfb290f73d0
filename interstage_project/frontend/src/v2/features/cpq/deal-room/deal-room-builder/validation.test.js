/**
 * Tests for Deal Room Builder validation schemas.
 * Demonstrates how Valibot validation works for builder operations.
 */

import { safeParse } from "valibot";

import {
  getDealRoomSchema,
  updateDealRoomSchema,
  updateDealRoomPagesSchema,
  updateDealRoomPageSchema,
  updateDealRoomBlockSchema,
  cloneDealRoomSchema,
  deleteDealRoomSchema,
  syncDealRoomWithTemplateSchema,
  blockDataSchema,
  pageDataSchema,
  getDealRoomTemplateSchema,
  updateDealRoomTemplateSchema,
  updateDealRoomTemplatePagesSchema,
  updateDealRoomTemplatePageSchema,
  updateDealRoomTemplateBlockSchema,
  cloneDealRoomTemplateSchema,
  deleteDealRoomTemplateSchema,
} from "./validation";

describe("Deal Room Builder Validation", () => {
  describe("getDealRoomSchema", () => {
    it("validates deal_room_id", () => {
      const payload = { deal_room_id: "dr_123" };
      const result = safeParse(getDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_id", () => {
      const result = safeParse(getDealRoomSchema, { deal_room_id: "" });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
    it("rejects invalid deal_room_id format", () => {
      const result = safeParse(getDealRoomSchema, { deal_room_id: "dr@123" });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "ID must contain only letters, numbers, hyphens, and underscores"
      );
    });
  });

  describe("updateDealRoomSchema", () => {
    it("validates minimal update", () => {
      const payload = { deal_room_id: "dr_123" };
      const result = safeParse(updateDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("validates all fields", () => {
      const payload = {
        deal_room_id: "dr_123",
        deal_room_template_id: "tpl_1",
        deal_room_name: "Room",
        deal_room_opportunity_id: "opp_1",
        deal_room_opportunity_name: "Opp Name",
        account_id: "acc_1",
        deal_room_status: "draft",
        deal_room_owner: "user_1",
        customer_logo: { type: "main", name: "logo", url: "url" },
      };
      const result = safeParse(updateDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects invalid status", () => {
      const result = safeParse(updateDealRoomSchema, {
        deal_room_id: "dr_1",
        deal_room_status: "foo",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Status must be either 'draft' or 'published'"
      );
    });
    it("rejects empty deal_room_name", () => {
      const result = safeParse(updateDealRoomSchema, {
        deal_room_id: "dr_1",
        deal_room_name: "",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Deal room name is required");
    });
  });

  describe("blockDataSchema", () => {
    it("validates hero block", () => {
      const payload = { block_id: "hero_1", type: "hero", title: "Welcome" };
      const result = safeParse(blockDataSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects invalid block type", () => {
      const result = safeParse(blockDataSchema, {
        block_id: "b1",
        type: "foo",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Invalid block type");
    });
    it("rejects empty block_id", () => {
      const result = safeParse(blockDataSchema, { block_id: "", type: "hero" });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Block ID is required");
    });
    it("rejects invalid block_id format", () => {
      const result = safeParse(blockDataSchema, {
        block_id: "b@1",
        type: "hero",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Block ID must contain only letters, numbers, hyphens, and underscores"
      );
    });
  });

  describe("pageDataSchema", () => {
    it("validates page with blocks", () => {
      const payload = {
        blocks_order: ["hero_1"],
        blocks: {
          hero_1: { block_id: "hero_1", type: "hero", title: "Welcome" },
        },
      };
      const result = safeParse(pageDataSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects invalid block ID in order", () => {
      const result = safeParse(pageDataSchema, {
        blocks_order: ["b@1"],
        blocks: {},
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Block ID must contain only letters, numbers, hyphens, and underscores"
      );
    });
    it("should pass when name is a non-empty string", () => {
      const result = safeParse(pageDataSchema, { name: "My Page" });
      expect(result.success).toBe(true);
    });

    it("should fail when name is an empty string", () => {
      const result = safeParse(pageDataSchema, { name: "" });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Page name cannot be empty");
    });

    it("should pass when name is not provided", () => {
      const result = safeParse(pageDataSchema, {});
      expect(result.success).toBe(true);
    });
  });

  describe("updateDealRoomPagesSchema", () => {
    it("validates add page operation", () => {
      const payload = {
        deal_room_id: "dr_1",
        deal_room_page_spec: {
          pages_order: ["page_1"],
          pages: {
            page_1: {
              blocks_order: ["hero_1"],
              blocks: {
                hero_1: { block_id: "hero_1", type: "hero", title: "Welcome" },
              },
            },
          },
        },
      };
      const result = safeParse(updateDealRoomPagesSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects invalid page ID in order", () => {
      const payload = {
        deal_room_id: "dr_1",
        deal_room_page_spec: {
          pages_order: ["page@1"],
          pages: {},
        },
      };
      const result = safeParse(updateDealRoomPagesSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Page ID must contain only letters, numbers, hyphens, and underscores"
      );
    });
  });

  describe("updateDealRoomPageSchema", () => {
    it("validates page update", () => {
      const payload = {
        deal_room_id: "dr_1",
        deal_room_page_id: "page_1",
        deal_room_page_spec: {
          blocks_order: ["hero_1"],
          blocks: {
            hero_1: { block_id: "hero_1", type: "hero", title: "Welcome" },
          },
        },
      };
      const result = safeParse(updateDealRoomPageSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty page ID", () => {
      const payload = {
        deal_room_id: "dr_1",
        deal_room_page_id: "",
        deal_room_page_spec: { blocks_order: [], blocks: {} },
      };
      const result = safeParse(updateDealRoomPageSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Page ID is required");
    });
  });

  describe("updateDealRoomBlockSchema", () => {
    it("validates block update", () => {
      const payload = {
        deal_room_id: "dr_1",
        deal_room_page_id: "page_1",
        deal_room_page_block_id: "hero_1",
        deal_room_page_block: {
          block_id: "hero_1",
          type: "hero",
          title: "Welcome",
        },
      };
      const result = safeParse(updateDealRoomBlockSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects missing required fields", () => {
      const result = safeParse(updateDealRoomBlockSchema, {
        deal_room_id: "dr_1",
      });
      expect(result.success).toBe(false);
    });
  });

  describe("cloneDealRoomSchema", () => {
    it("validates deal room clone", () => {
      const payload = { deal_room_id: "dr_1" };
      const result = safeParse(cloneDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_id", () => {
      const result = safeParse(cloneDealRoomSchema, { deal_room_id: "" });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
  });

  describe("deleteDealRoomSchema", () => {
    it("validates deal room delete", () => {
      const payload = { deal_room_id: "dr_1" };
      const result = safeParse(deleteDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_id", () => {
      const result = safeParse(deleteDealRoomSchema, { deal_room_id: "" });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
  });

  describe("syncDealRoomWithTemplateSchema", () => {
    it("validates sync operation", () => {
      const payload = { deal_room_id: "dr_1" };
      const result = safeParse(syncDealRoomWithTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_id", () => {
      const result = safeParse(syncDealRoomWithTemplateSchema, {
        deal_room_id: "",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
  });
});

describe("Deal Room Template Validation", () => {
  describe("getDealRoomTemplateSchema", () => {
    it("validates deal_room_template_id", () => {
      const payload = { deal_room_template_id: "tpl_123" };
      const result = safeParse(getDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_template_id", () => {
      const result = safeParse(getDealRoomTemplateSchema, {
        deal_room_template_id: "",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
    it("rejects invalid deal_room_template_id format", () => {
      const result = safeParse(getDealRoomTemplateSchema, {
        deal_room_template_id: "tpl@123",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "ID must contain only letters, numbers, hyphens, and underscores"
      );
    });
  });

  describe("updateDealRoomTemplateSchema", () => {
    it("validates minimal update", () => {
      const payload = { deal_room_template_id: "tpl_123" };
      const result = safeParse(updateDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("validates all fields", () => {
      const payload = {
        deal_room_template_id: "tpl_123",
        deal_room_template_name: "Template",
        deal_room_template_description: "Desc",
        deal_room_template_status: "active",
      };
      const result = safeParse(updateDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects invalid status", () => {
      const result = safeParse(updateDealRoomTemplateSchema, {
        deal_room_template_id: "tpl_1",
        deal_room_template_status: "foo",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Status must be either 'active' or 'inactive'"
      );
    });
    it("rejects empty template name", () => {
      const result = safeParse(updateDealRoomTemplateSchema, {
        deal_room_template_id: "tpl_1",
        deal_room_template_name: "",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Template name cannot be empty");
    });
  });

  describe("updateDealRoomTemplatePagesSchema", () => {
    it("validates add page operation", () => {
      const payload = {
        deal_room_template_id: "tpl_1",
        deal_room_page_spec: {
          pages_order: ["page_1"],
          pages: {
            page_1: {
              blocks_order: ["hero_1"],
              blocks: {
                hero_1: { block_id: "hero_1", type: "hero", title: "Welcome" },
              },
            },
          },
        },
      };
      const result = safeParse(updateDealRoomTemplatePagesSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects invalid page ID in order", () => {
      const payload = {
        deal_room_template_id: "tpl_1",
        deal_room_page_spec: {
          pages_order: ["page@1"],
          pages: {},
        },
      };
      const result = safeParse(updateDealRoomTemplatePagesSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Page ID must contain only letters, numbers, hyphens, and underscores"
      );
    });
  });

  describe("updateDealRoomTemplatePageSchema", () => {
    it("validates page update", () => {
      const payload = {
        deal_room_template_id: "tpl_1",
        deal_room_template_page_id: "page_1",
        deal_room_template_page_spec: {
          blocks_order: ["hero_1"],
          blocks: {
            hero_1: { block_id: "hero_1", type: "hero", title: "Welcome" },
          },
        },
      };
      const result = safeParse(updateDealRoomTemplatePageSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty page ID", () => {
      const payload = {
        deal_room_template_id: "tpl_1",
        deal_room_template_page_id: "",
        deal_room_template_page_spec: { blocks_order: [], blocks: {} },
      };
      const result = safeParse(updateDealRoomTemplatePageSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Page ID is required");
    });
  });

  describe("updateDealRoomTemplateBlockSchema", () => {
    it("validates block update", () => {
      const payload = {
        deal_room_template_id: "tpl_1",
        deal_room_template_page_id: "page_1",
        deal_room_template_page_block_id: "hero_1",
        deal_room_template_page_block: {
          block_id: "hero_1",
          type: "hero",
          title: "Welcome",
        },
      };
      const result = safeParse(updateDealRoomTemplateBlockSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects missing required fields", () => {
      const result = safeParse(updateDealRoomTemplateBlockSchema, {
        deal_room_template_id: "tpl_1",
      });
      expect(result.success).toBe(false);
    });
  });

  describe("cloneDealRoomTemplateSchema", () => {
    it("validates template clone", () => {
      const payload = { deal_room_template_id: "tpl_1" };
      const result = safeParse(cloneDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_template_id", () => {
      const result = safeParse(cloneDealRoomTemplateSchema, {
        deal_room_template_id: "",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
  });

  describe("deleteDealRoomTemplateSchema", () => {
    it("validates template delete", () => {
      const payload = { deal_room_template_id: "tpl_1" };
      const result = safeParse(deleteDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
    it("rejects empty deal_room_template_id", () => {
      const result = safeParse(deleteDealRoomTemplateSchema, {
        deal_room_template_id: "",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("ID is required");
    });
  });
});
