import PropTypes from "prop-types";
import { Fragment } from "react";
import { useForm<PERSON>ontext, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";

import { EverTg, EverSelect } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

import FileUpload from "./FileUpload";

/**
 * HeroList renders a list of feature items, each with an image upload and image placement option.
 *
 * @param {Object} props
 * @param {string} props.name - Field name prefix for form fields.
 */
const HeroList = ({ name }) => {
  const { control, setValue } = useFormContext();

  const { fields } = useFieldArray({
    control,
    name,
  });

  return (
    <div className="flex flex-col gap-5">
      <div className="flex items-center gap-4">
        <EverTg.Text className="text-xs font-medium whitespace-nowrap">
          FILE
        </EverTg.Text>
        <div className="w-full h-px bg-ever-base-400" />
      </div>
      {fields.map((field, index) => (
        <Fragment key={field.id}>
          <Controller
            name={`${name}.${index}.file.file_type`}
            control={control}
            render={({ field }) => (
              <LabeledField label="File Type">
                <EverSelect
                  value={field.value}
                  options={[
                    {
                      label: "Image",
                      value: "image",
                    },
                    {
                      label: "Video",
                      value: "video",
                    },
                    {
                      label: "Document",
                      value: "document",
                    },
                  ]}
                  onChange={(value) => {
                    field.onChange(value);
                    setValue(`${name}.${index}.file.file_url`, "");
                    setValue(`${name}.${index}.file.file_name`, "");
                  }}
                  placeholder="Select file type"
                />
              </LabeledField>
            )}
          />
          <Controller
            name={`${name}.${index}.file`}
            control={control}
            render={({ field }) => (
              <LabeledField label="Upload">
                <FileUpload
                  value={field.value}
                  file_type={field.value?.file_type ?? "image"}
                  onChange={(file) => field.onChange(file)}
                />
              </LabeledField>
            )}
          />
        </Fragment>
      ))}
    </div>
  );
};

HeroList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
};

HeroList.defaultProps = {
  label: "",
};

export default HeroList;
