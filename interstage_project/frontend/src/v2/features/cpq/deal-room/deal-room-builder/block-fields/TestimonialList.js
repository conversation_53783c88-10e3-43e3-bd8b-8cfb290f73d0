import PropTypes from "prop-types";
import { Fragment } from "react";
import { useFormContext, useFieldArray, Controller } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import { LabeledField } from "~/v2/features/cpq/components";

import FileUpload from "./FileUpload";
import TabComponent from "./TabComponent";
import TextArea from "./TextArea";
import TextInput from "./TextInput";

const defaultTestimonial = () => ({
  key: uuidv4(),
  name: "",
  title: "",
  message: "",
  tagline: "",
  file: {
    file_url: "",
    file_name: "",
    file_type: "image",
  },
});

/**
 * TestimonialList renders a list of testimonial items, each with image, tagline, message, name, and title fields, using TabComponent.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the testimonial section.
 * @param {string} props.name - Field name prefix for form fields.
 */
const TestimonialList = ({ label = "", name }) => {
  const { control } = useFormContext();

  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
  });

  const handleUpdate = (type, ...args) => {
    switch (type) {
      case "move": {
        const [sourceIndex, destinationIndex] = args;
        move(sourceIndex, destinationIndex);
        break;
      }
      case "add": {
        append(args[0]);
        break;
      }
      case "remove": {
        remove(args[0]);
        break;
      }
      default: {
        break;
      }
    }
  };

  return (
    <TabComponent
      items={fields}
      label={label}
      tabLabel="T"
      onUpdate={handleUpdate}
      getDefaultItem={defaultTestimonial}
    >
      {(item, index) => (
        <Fragment key={item.key}>
          <Controller
            name={`${name}.${index}.file`}
            control={control}
            render={({ field }) => (
              <LabeledField label="Image">
                <FileUpload
                  value={field.value}
                  onChange={(img) => field.onChange(img)}
                />
              </LabeledField>
            )}
          />
          <Controller
            name={`${name}.${index}.tagline`}
            control={control}
            render={({ field, fieldState }) => (
              <TextInput
                label="Tagline"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
              />
            )}
          />
          <Controller
            name={`${name}.${index}.message`}
            control={control}
            rules={{
              required: "Message is required",
            }}
            render={({ field, fieldState }) => (
              <TextArea
                label="Message"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.name`}
            control={control}
            rules={{
              required: "Person name is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Person Name"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.title`}
            control={control}
            rules={{
              required: "Job title is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Job Title"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
        </Fragment>
      )}
    </TabComponent>
  );
};

TestimonialList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
};

TestimonialList.defaultProps = {
  label: "",
};

export default TestimonialList;
