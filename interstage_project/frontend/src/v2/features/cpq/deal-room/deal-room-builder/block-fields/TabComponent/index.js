import {
  PlusIcon,
  DotsVerticalIcon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import PropTypes from "prop-types";
import { useState, useMemo } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { twMerge } from "tailwind-merge";

import { EverTabs, EverTg, IconButton } from "~/v2/components";

import "./tabStyles.scss";

/**
 * TabComponent renders a draggable, addable, and removable set of tabs for block fields.
 *
 * @param {Object} props
 * @param {function} props.children - Render function for tab content, receives (item, index).
 * @param {Array<{key: string, [x: string]: any}>} props.items - Array of tab items, each with a unique key.
 * @param {string} props.label - Label prefix for each tab's content section.
 * @param {string} props.tabLabel - Label prefix for each tab's tab label.
 * @param {function} props.onUpdate - Callback for add, remove, or move actions.
 * @param {function} props.getDefaultItem - Function to generate a new default item for adding tabs.
 */
const TabComponent = ({
  children,
  items,
  label,
  tabLabel,
  onUpdate,
  getDefaultItem,
}) => {
  const [activeKey, setActiveKey] = useState(items[0].key);

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    onUpdate("move", result.source.index, result.destination.index);
  };

  const handleTabChange = (key) => {
    setActiveKey(key);
  };

  const handleAdd = () => {
    const newItem = getDefaultItem();
    setActiveKey(newItem.key);
    onUpdate("add", newItem);
  };

  const handleRemove = (key) => {
    const removeIndex = items.findIndex((v) => v.key === key);
    const newActiveIndex =
      removeIndex === 0 ? removeIndex + 1 : removeIndex - 1;
    setActiveKey(items[newActiveIndex].key);
    onUpdate("remove", removeIndex);
  };

  const tabItems = useMemo(() => {
    return items.map((v, i) => ({
      ...v,
      label: `${tabLabel}${i + 1}`,
    }));
  }, [items, tabLabel]);

  const renderTab = (item) => {
    const isActive = activeKey === item.key;
    return (
      <div className="flex items-center gap-1.5">
        <div className="flex items-center justify-center w-3 h-3 rotate-90 overflow-hidden cursor-grab">
          <DotsVerticalIcon
            className={twMerge(
              "w-4 h-4 mt-1",
              isActive ? "text-ever-primary" : "text-ever-base-content-mid"
            )}
          />
          <DotsVerticalIcon
            className={twMerge(
              "w-4 h-4 mt-1",
              isActive ? "text-ever-primary" : "text-ever-base-content-mid"
            )}
          />
        </div>
        <EverTg.Caption
          className={twMerge(
            "font-medium",
            isActive ? "!text-ever-primary" : "!text-ever-base-content"
          )}
        >
          {item.label}
        </EverTg.Caption>
      </div>
    );
  };

  const renderClone = (provided, _, rubric) => {
    const item = tabItems[rubric.source.index];
    return (
      <div
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
      >
        {renderTab(item)}
      </div>
    );
  };

  const renderTabBar = (props, DefaultTabBar) => (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable
        droppableId="tabs"
        direction="horizontal"
        renderClone={renderClone}
      >
        {(provided) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className="h-8"
          >
            <DefaultTabBar {...props}>
              {(node) => {
                const index = items.findIndex((item) => item.key === node.key);
                return (
                  <Draggable
                    key={node.key}
                    draggableId={node.key}
                    index={index}
                  >
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        {node}
                      </div>
                    )}
                  </Draggable>
                );
              }}
            </DefaultTabBar>
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );

  return (
    <div className="flex items-center gap-2">
      <EverTabs
        type="card"
        activeKey={activeKey}
        onChange={handleTabChange}
        renderTabBar={renderTabBar}
        tabBarExtraContent={
          <div
            className="flex items-center h-8 w-8 ml-auto cursor-pointer justify-center rounded-tr-md rounded-tl-md border border-solid border-ever-base-300 shadow-[-1.6px_-1px_1.6px_0px_rgba(0,0,0,0.08)]"
            onClick={handleAdd}
          >
            <PlusIcon className="w-4 h-4 text-ever-primary" />
          </div>
        }
      >
        {tabItems.map((item, index) => {
          return (
            <EverTabs.TabPane
              key={item.key}
              tab={renderTab(item)}
              index={index}
            >
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between">
                  <EverTg.SubHeading4 className="text-ever-base-content">
                    {`${label} ${index + 1}`}
                  </EverTg.SubHeading4>
                  {items.length > 1 && (
                    <IconButton
                      icon={<Trash03Icon className="w-4 h-4 text-ever-error" />}
                      type="text"
                      size="small"
                      onClick={() => handleRemove(item.key)}
                    />
                  )}
                </div>
                <div className="flex flex-col gap-4">
                  {children(item, index)}
                </div>
              </div>
            </EverTabs.TabPane>
          );
        })}
      </EverTabs>
    </div>
  );
};

TabComponent.propTypes = {
  children: PropTypes.func.isRequired,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
    })
  ).isRequired,
  label: PropTypes.string,
  tabLabel: PropTypes.string,
  onUpdate: PropTypes.func.isRequired,
  getDefaultItem: PropTypes.func.isRequired,
};

TabComponent.defaultProps = {
  label: "Tab",
  tabLabel: "Tab ",
};

export default TabComponent;
