import * as Popover from "@radix-ui/react-popover";
import PropTypes from "prop-types";
import { useState } from "react";

import { useUnsavedChanges } from "~/v2/features/cpq/deal-room/deal-room-builder/hooks";

import { blockSchema } from "./constants";

/**
 * AddSectionPopover renders a popover with a list of section types to add.
 *
 * @param {Object} props
 * @param {function} props.onAddSection - <PERSON><PERSON> called with the section type key when a section is selected.
 * @param {React.ReactNode} props.children - The trigger element for the popover.
 */
const AddSectionPopover = ({ onAddSection, children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { executeWithUnsavedChangesCheck } = useUnsavedChanges();

  const handleSectionClick = (sectionType) => {
    const addSectionAction = () => {
      setIsOpen(false);
      onAddSection(sectionType);
    };

    executeWithUnsavedChangesCheck(addSectionAction, "add a section");
  };

  const content = (
    <div className="flex flex-col overflow-y-auto">
      {Object.entries(blockSchema).map(([key, block]) => {
        return (
          <div
            key={key}
            className="flex items-center gap-2 h-8 px-3 py-2 rounded-md cursor-pointer hover:bg-ever-base-100"
            onClick={() => handleSectionClick(key)}
          >
            {block.label}
          </div>
        );
      })}
    </div>
  );

  return (
    <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>{children}</Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          align="center"
          className="w-48 mx-px p-2 shadow-xl my-1.5 border border-solid border-ever-base-400 rounded-lg bg-ever-base"
        >
          {content}
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

AddSectionPopover.propTypes = {
  onAddSection: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
};

AddSectionPopover.defaultProps = {
  onAddSection: () => {},
};

export default AddSectionPopover;
