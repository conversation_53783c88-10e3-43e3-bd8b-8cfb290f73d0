import {
  EyeIcon,
  CheckIcon,
  ChevronDownIcon,
  EditPencilAltIcon,
  DotsVerticalIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

import {
  EverTg,
  EverButton,
  EverBadge,
  EverDrawer,
  message,
  IconButton,
} from "~/v2/components";
import {
  RemoveConfirmationModal,
  TruncatedText,
} from "~/v2/features/cpq/components";
import { CreateEditTemplateModal } from "~/v2/features/cpq/deal-room/components";
import {
  ACTIVE_STATUS_MENU_ITEMS,
  STATUS_OPTIONS,
  MORE_MENU_ITEMS,
  MORE_OPTIONS,
} from "~/v2/features/cpq/deal-room/constants";
import {
  REMOVE_CONFIRMATION_MESSAGES,
  templateToastMessage,
} from "~/v2/features/cpq/deal-room/deal-room-template-list/constants";
import { replaceUrl } from "~/v2/features/cpq/utils";

import DealRoomPreview from "../../deal-room-preview";
import { useBuilder } from "../hooks";

/**
 * DealRoomTemplateHeader renders the header for the Deal Room template builder, including actions, status, and preview.
 *
 * @param {Object} props
 * @param {Object} props.clientInfo - Client info for the deal room template.
 */
const DealRoomTemplateHeader = ({ clientInfo }) => {
  const { watch } = useFormContext();
  const data = watch();
  const {
    isLoading,
    activePage,
    activeSection,
    previewMode,
    buyersLogoUrl,
    setPreviewMode,
    onUpdateTemplate,
    onClone,
    onDelete,
  } = useBuilder();
  const {
    deal_room_template_name,
    deal_room_template_description,
    deal_room_template_status,
  } = data;

  const [isEditTemplateDetails, setIsEditTemplateDetails] = useState(false);
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);

  const handleStatusChange = (event) => {
    event.domEvent.stopPropagation();
    if (event.key === deal_room_template_status) {
      return;
    }

    onUpdateTemplate({ deal_room_template_status: event.key });
  };

  const statusMenu = (
    <Menu
      className="flex items-start !p-2"
      selectedKeys={[deal_room_template_status]}
      onClick={handleStatusChange}
    >
      {Object.values(ACTIVE_STATUS_MENU_ITEMS).map((item) => (
        <Menu.Item
          key={item.value}
          className={twMerge(
            "rounded-lg w-full !h-9",
            deal_room_template_status === item.value &&
              "font-medium bg-ever-base text-ever-primary"
          )}
        >
          <div className="flex gap-2 items-center">
            <EverTg.Caption>{item.label}</EverTg.Caption>
            {deal_room_template_status === item.value && (
              <CheckIcon className="h-4 w-4" />
            )}
          </div>
        </Menu.Item>
      ))}
    </Menu>
  );

  const handleSave = (fieldsToUpdate) => {
    setIsEditTemplateDetails(false);
    onUpdateTemplate(fieldsToUpdate);
  };

  const handleDelete = () => {
    onDelete(templateToastMessage.delete);
  };

  const handleMoreOptionsClick = (item) => {
    if (item === MORE_OPTIONS.CLONE) {
      onClone(templateToastMessage.clone);
    } else if (item === MORE_OPTIONS.DELETE) {
      setShowRemoveConfirmModal(true);
    }
  };

  const moreOptionsMenu = (
    <Menu
      className="flex items-start !p-2"
      onClick={(event) => {
        event.domEvent.stopPropagation();
      }}
    >
      {MORE_MENU_ITEMS.map((item, key) => (
        <Menu.Item
          className="!px-0 w-full hover:!bg-transparent !h-9"
          key={key}
        >
          <EverButton
            type="text"
            color={item.type === MORE_OPTIONS.DELETE ? "error" : "base"}
            size="small"
            className="w-full justify-start"
            prependIcon={item.icon}
            onClick={() => handleMoreOptionsClick(item.type)}
          >
            <EverTg.Caption
              className={twMerge(
                "font-normal",
                item.type === MORE_OPTIONS.DELETE
                  ? "text-ever-error-lite-content"
                  : ""
              )}
            >
              {item.label}
            </EverTg.Caption>
          </EverButton>
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div className="flex justify-between items-end px-8 py-4 border-b border-ever-base-400 bg-ever-base-50">
      <div className="flex flex-col gap-1.5 my-auto">
        <div className="flex items-center gap-1.5">
          <div className="flex items-center gap-2">
            <TruncatedText className="text-xl font-semibold">
              {deal_room_template_name}
            </TruncatedText>
            <EditPencilAltIcon
              className="w-5 h-5 shrink-0 text-ever-base-content-low cursor-pointer"
              onClick={() => {
                setIsEditTemplateDetails(true);
              }}
            />
          </div>
        </div>
        {deal_room_template_description && (
          <EverTg.Text>{deal_room_template_description}</EverTg.Text>
        )}
      </div>
      <div className="flex items-center gap-3">
        <Dropdown
          overlay={statusMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
          className="cursor-pointer"
        >
          <EverBadge
            className="!h-8 gap-1.5 text-sm font-medium rounded-3xl"
            {...{
              type: ACTIVE_STATUS_MENU_ITEMS[deal_room_template_status].type,
              icon: ACTIVE_STATUS_MENU_ITEMS[deal_room_template_status].icon,
              title: (
                <div className="flex items-center gap-1">
                  <span>
                    {ACTIVE_STATUS_MENU_ITEMS[deal_room_template_status].label}
                  </span>
                  <ChevronDownIcon
                    className={twMerge(
                      "w-4 h-4",
                      ACTIVE_STATUS_MENU_ITEMS[deal_room_template_status]
                        .value === STATUS_OPTIONS.ACTIVE
                        ? "text-ever-success-lite-content"
                        : "text-ever-base-content-mid"
                    )}
                  />
                </div>
              ),
            }}
          />
        </Dropdown>
        <EverButton
          type="outlined"
          color="base"
          size="small"
          icon={<EyeIcon className="w-5 h-5 text-ever-base-content-mid" />}
          className="rounded-lg [&>div]:gap-1.5"
          onClick={() => {
            const { pages_order, pages } = get(data, "deal_room_page_spec");
            const hasBlocks = pages_order.some(
              (pageId) => get(pages, `${pageId}.blocks_order.length`) > 0
            );
            if (hasBlocks) {
              setPreviewMode("preview");
            } else {
              message.error("Add at least one block to the page");
            }
          }}
        >
          Preview
        </EverButton>
        <Dropdown
          overlay={moreOptionsMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <IconButton
            type="outlined"
            color="base"
            size="small"
            className="!px-2 ml-auto"
            icon={
              <DotsVerticalIcon className="w-4 h-4 text-ever-base-content-mid" />
            }
          />
        </Dropdown>
      </div>
      <CreateEditTemplateModal
        type="edit"
        data={data}
        visible={isEditTemplateDetails}
        onClose={() => setIsEditTemplateDetails(false)}
        onSubmit={handleSave}
        loading={isLoading}
      />
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title={REMOVE_CONFIRMATION_MESSAGES.title}
        subtitle={REMOVE_CONFIRMATION_MESSAGES.subtitle}
        onConfirm={handleDelete}
        onCancel={() => setShowRemoveConfirmModal(false)}
        loading={isLoading}
      />
      <EverDrawer
        visible={!!previewMode}
        bodyStyle={{ padding: 0 }}
        closable={false}
        destroyOnClose
        height="100%"
        push={false}
      >
        {!!previewMode && (
          <DealRoomPreview
            data={data}
            clientInfo={clientInfo}
            buyersLogoUrl={buyersLogoUrl}
            onClose={() => {
              setPreviewMode(null);
              replaceUrl({ page: activePage }, activeSection);
            }}
          />
        )}
      </EverDrawer>
    </div>
  );
};

DealRoomTemplateHeader.propTypes = {
  clientInfo: PropTypes.object,
};

DealRoomTemplateHeader.defaultProps = {
  clientInfo: {},
};

export default DealRoomTemplateHeader;
