import PropTypes from "prop-types";
import { twMerge } from "tailwind-merge";

import { EverTg } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

/**
 * RadioGroup renders a group of selectable radio options with optional icons.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the radio group field.
 * @param {string} props.value - Selected value.
 * @param {Array<{label: string, value: string, icon?: React.ReactNode}>} props.options - Array of radio options.
 * @param {boolean} props.required - Whether the field is required.
 * @param {Object} props.error - Error object for validation.
 * @param {function} props.onChange - Callback when the value changes.
 */
const RadioGroup = ({
  label = "",
  value = "",
  options = [],
  required = false,
  error = {},
  onChange,
}) => {
  return (
    <LabeledField label={label} required={required} error={error}>
      <div className="flex gap-2">
        {options.map((option) => (
          <div
            key={option.value}
            className={twMerge(
              "flex items-center gap-2 w-36 h-8 border border-ever-base-400 rounded-lg py-1.5 px-3 cursor-pointer",
              value === option.value &&
                "border-ever-primary ring ring-ever-primary-ring"
            )}
            onClick={() => onChange(option.value)}
          >
            {option.icon}
            <EverTg.SubHeading4 className="text-ever-base-content">
              {option.label}
            </EverTg.SubHeading4>
          </div>
        ))}
      </div>
    </LabeledField>
  );
};

RadioGroup.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
      icon: PropTypes.node,
    })
  ),
  required: PropTypes.bool,
  error: PropTypes.object,
  onChange: PropTypes.func.isRequired,
};

RadioGroup.defaultProps = {
  label: "",
  value: "",
  options: [],
  required: false,
  error: {},
};

export default RadioGroup;
