import { Trash03Icon } from "@everstage/evericons/outlined";
import { FilePlusIcon, File06Icon } from "@everstage/evericons/solid";
import { Upload } from "antd";
import PropTypes from "prop-types";
import { useState, useEffect, useRef } from "react";

import { EverTg, IconButton } from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";

const { Dragger } = Upload;

const IMAGE_EXTENSIONS = ["png", "jpg", "jpeg", "gif", "webp", "svg"];
const VIDEO_EXTENSIONS = ["mp4", "avi", "mov", "wmv", "flv", "webm"];
const DOCUMENT_EXTENSIONS = [
  "pdf",
  "doc",
  "docx",
  "txt",
  "csv",
  "xlsx",
  "xls",
  "ppt",
  "pptx",
];

const FILE_TYPE_CONFIG = {
  image: {
    accept: `.${IMAGE_EXTENSIONS.join(", .")}`,
    label: "image",
  },
  video: {
    accept: `.${VIDEO_EXTENSIONS.join(", .")}`,
    label: "video",
  },
  document: {
    accept: `.${DOCUMENT_EXTENSIONS.join(", .")}`,
    label: "document",
  },
  all: {
    accept: `.${[
      ...IMAGE_EXTENSIONS,
      ...VIDEO_EXTENSIONS,
      ...DOCUMENT_EXTENSIONS,
    ].join(", .")}`,
    label: "all",
  },
};

/**
 * FilePreview renders a preview of the uploaded file with a remove button.
 *
 * @param {Object} props
 * @param {string} props.previewUrl - The preview URL for the file.
 * @param {Object} props.value - The file object.
 * @param {function} props.handleRemove - Callback to remove the file.
 */
const FilePreview = ({ previewUrl, value, handleRemove }) => {
  const [isError, setIsError] = useState(false);

  const getFileType = () => {
    if (value?.file_type) return value.file_type;

    const extension = value?.file_extension?.toLowerCase();
    if (IMAGE_EXTENSIONS.includes(extension)) return "image";
    if (VIDEO_EXTENSIONS.includes(extension)) return "video";
    return "document";
  };

  const renderFileIcon = () => (
    <div className="w-5 h-5 rounded flex items-center justify-center">
      <File06Icon className="w-5 h-5 text-ever-base-content-mid" />
    </div>
  );

  const renderPreview = () => {
    if (isError) {
      return renderFileIcon();
    }

    const fileType = getFileType();

    switch (fileType) {
      case "image": {
        return (
          <img
            src={previewUrl}
            alt="Uploaded"
            className="w-5 h-5 object-cover rounded"
            onError={() => setIsError(true)}
          />
        );
      }
      case "video": {
        return (
          <video
            src={previewUrl}
            className="w-5 h-5 object-cover rounded"
            loop
            onError={() => setIsError(true)}
          />
        );
      }
      default: {
        return renderFileIcon();
      }
    }
  };

  return (
    <div className="h-10 flex items-center gap-2 border border-ever-base-300 rounded-lg py-2 px-3">
      {renderPreview()}
      <TruncatedText text={value?.file_name} className="text-ever-base-content">
        {value?.file_name}
      </TruncatedText>
      <IconButton
        type="text"
        color="base"
        className="!w-5 !h-5 ml-auto rounded-sm cursor-pointer"
        icon={<Trash03Icon className="ml-auto text-ever-error-hover" />}
        onClick={handleRemove}
      />
    </div>
  );
};

FilePreview.propTypes = {
  previewUrl: PropTypes.string,
  value: PropTypes.object,
  handleRemove: PropTypes.func.isRequired,
};

FilePreview.defaultProps = {
  previewUrl: "",
  value: {},
  handleRemove: () => {},
};

/**
 * FileUpload renders a file upload field with preview and removal for images, videos, and documents.
 *
 * @param {Object} props
 * @param {Object|null} props.value - The uploaded file object or null.
 * @param {function} props.onChange - Callback when the file changes or is removed.
 * @param {string} props.file_type - The type of file to accept (image, video, document, all).
 */
const FileUpload = ({ value = null, onChange, file_type = "image" }) => {
  const tempFileType = useRef(file_type);
  const [previewUrl, setPreviewUrl] = useState(value?.file_url ?? null);

  const config = FILE_TYPE_CONFIG[file_type] || FILE_TYPE_CONFIG.image;

  const handleRemove = () => {
    // Only revoke if it's a blob URL (starts with "blob:")
    if (previewUrl && previewUrl.startsWith("blob:")) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    onChange({
      ...value,
      file_url: "",
      file_name: "",
      file_type,
    });
  };

  const draggerProps = {
    name: "file",
    accept: config.accept,
    multiple: false,
    showUploadList: false,
    onChange(info) {
      const { status } = info.file;
      if (status !== "removed") {
        let reader = new FileReader();
        reader.readAsDataURL(info.file);
        reader.addEventListener("load", function () {
          const fileUrl = reader.result;
          setPreviewUrl(fileUrl);
          onChange({
            ...value,
            file: info.file,
            file_url: fileUrl,
            file_name: info.file.name,
            file_extension: info.file.type.split("/")[1],
            file_type,
          });
        });
      }
    },
    beforeUpload() {
      return false;
    },
    onRemove: handleRemove,
  };

  useEffect(() => {
    if (value?.file_url) {
      setPreviewUrl(value?.file_url);
    }
  }, [value?.file_url]);

  useEffect(() => {
    if (tempFileType.current !== file_type) {
      setPreviewUrl(null);
      if (previewUrl && previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
      tempFileType.current = file_type;
    }
  }, [file_type, previewUrl]);

  useEffect(() => {
    return () => {
      // Only revoke if it's a blob URL (starts with "blob:")
      if (previewUrl && previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  return (
    <>
      {previewUrl && (
        <FilePreview
          previewUrl={previewUrl}
          value={value}
          handleRemove={handleRemove}
        />
      )}
      {!previewUrl && (
        <div className="bg-ever-base-400 rounded-lg h-10">
          <Dragger
            {...draggerProps}
            className="!rounded-lg [&_.ant-upload]:!p-0"
          >
            <div className="flex align-center gap-2 px-3">
              <FilePlusIcon className="text-ever-primary w-5 h-5 my-auto" />
              <div className="flex flex-col">
                <div className="flex justify-center align-center">
                  <EverTg.Text className="self-center mr-1.5 !text-[12px]">
                    Drag and drop {config.label} file here, or
                  </EverTg.Text>
                  <a className="self-center !text-[12px] text-ever-primary hover:text-ever-primary-hover">
                    Browse
                  </a>
                </div>
              </div>
            </div>
          </Dragger>
        </div>
      )}
    </>
  );
};

FileUpload.propTypes = {
  value: PropTypes.object,
  onChange: PropTypes.func.isRequired,
  file_type: PropTypes.oneOf(["image", "video", "document", "all"]),
};

FileUpload.defaultProps = {
  value: null,
  file_type: "image",
};

export default FileUpload;
