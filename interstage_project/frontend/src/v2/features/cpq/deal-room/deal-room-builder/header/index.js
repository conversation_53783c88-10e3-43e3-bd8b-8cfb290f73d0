import PropTypes from "prop-types";

import DealRoomHeader from "./DealRoomHeader";
import DealRoomTemplateHeader from "./DealRoomTemplateHeader";
import { useBuilder } from "../hooks";

/**
 * Header component that conditionally renders DealRoomHeader or DealRoomTemplateHeader based on builder mode.
 *
 * @param {Object} props
 * @param {Object} props.clientInfo - Client info for the header.
 */
const Header = ({ clientInfo }) => {
  const { isTemplate } = useBuilder();

  return isTemplate ? (
    <DealRoomTemplateHeader clientInfo={clientInfo} />
  ) : (
    <DealRoomHeader clientInfo={clientInfo} />
  );
};

Header.propTypes = {
  clientInfo: PropTypes.object,
};

Header.defaultProps = {
  clientInfo: {},
};

export default Header;
