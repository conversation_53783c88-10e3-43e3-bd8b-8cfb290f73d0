import PropTypes from "prop-types";
import React, { useRef, useState, useLayoutEffect } from "react";

import { LayoutContainer } from "~/v2/features/cpq/deal-room/components";

/**
 * AutoScaleContainer automatically scales and positions its children to fit within a parent container.
 *
 * @param {Object} props
 * @param {Object} props.data - Data for the layout container, including background.
 * @param {string} props.parentId - The DOM id of the parent container to fit into.
 * @param {React.ReactNode} props.children - The content to scale and display.
 * @param {function} props.onClick - Optional click handler for the container.
 * @param {boolean} props.isSlidePreview - Whether to use slide preview scaling.
 * @param {boolean} props.isPreview - Whether to use preview mode.
 */
const AutoScaleContainer = ({
  data = { background: {} },
  parentId = "deal-room-builder-container",
  children,
  onClick = null,
  isSlidePreview = false,
  isPreview = false,
}) => {
  const containerRef = useRef(null);
  const contentRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [contentDimensions, setContentDimensions] = useState({
    width: 0,
    height: 0,
  });

  useLayoutEffect(() => {
    const updateScale = () => {
      const parentContainer = document.querySelector(`#${parentId}`);
      if (!containerRef.current || !contentRef.current || !parentContainer) {
        return;
      }

      // Get the natural dimensions of the content
      const contentWidth = contentRef.current.offsetWidth;
      const contentHeight = contentRef.current.offsetHeight;

      // Store content dimensions
      setContentDimensions({ width: contentWidth, height: contentHeight });

      // Get container width (we'll use content height for container height)
      const containerWidth =
        parentContainer instanceof HTMLElement
          ? parentContainer.offsetWidth
          : 0;
      const containerHeight =
        parentContainer instanceof HTMLElement
          ? parentContainer.offsetHeight
          : 0;

      // Calculate scale based on container dimensions
      const widthScale = containerWidth / contentWidth;
      const heightScale = containerHeight / contentHeight;

      // Use width scale to ensure content fits horizontally
      const newScale = isSlidePreview
        ? Math.min(widthScale, heightScale)
        : widthScale;
      const offsetX = (contentWidth * newScale - contentWidth) / 2;
      const offsetY = (contentHeight * newScale - contentHeight) / 2;

      // Apply negative positioning to compensate
      setPosition({ x: offsetX, y: offsetY });
      setScale(newScale);
    };

    // Create a ResizeObserver to watch for content size changes
    const resizeObserver = new ResizeObserver(updateScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current);
    }

    // Initial update - call immediately for better UX
    updateScale();

    return () => {
      resizeObserver.disconnect();
    };
  }, [isSlidePreview, parentId]);

  return (
    <div
      ref={containerRef}
      className="relative overflow-hidden w-full"
      style={{
        height: contentDimensions.height
          ? `${contentDimensions.height * scale}px`
          : "auto",
      }}
      onClick={onClick}
    >
      <div
        ref={contentRef}
        className="origin-center"
        style={{
          transform: `scale(${scale})`,
          position: `absolute`,
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `max-content`,
        }}
      >
        <LayoutContainer
          data={data}
          isSlidePreview={isSlidePreview}
          isPreview={isPreview}
        >
          {children}
        </LayoutContainer>
      </div>
    </div>
  );
};

AutoScaleContainer.propTypes = {
  data: PropTypes.object,
  parentId: PropTypes.string,
  children: PropTypes.node,
  onClick: PropTypes.func,
  isSlidePreview: PropTypes.bool,
  isPreview: PropTypes.bool,
};

AutoScaleContainer.defaultProps = {
  data: { background: {} },
  parentId: "deal-room-builder-container",
  onClick: null,
  isSlidePreview: false,
  isPreview: false,
};

export default AutoScaleContainer;
