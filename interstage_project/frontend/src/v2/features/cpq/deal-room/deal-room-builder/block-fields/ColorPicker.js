import { EditIcon } from "@everstage/evericons/outlined";
import * as Popover from "@radix-ui/react-popover";
import PropTypes from "prop-types";
import { useState } from "react";
import { HexColorPicker } from "react-colorful";
import { twMerge } from "tailwind-merge";

import { LabeledField } from "~/v2/features/cpq/components";
import { Typography } from "~/v2/features/cpq/deal-room/components";
import theme from "~/v2/themes/blueBias23";

const DEFAULT_OPTIONS = [
  theme.colors.dealroom.chartColors[1],
  theme.colors.dealroom.chartColors[2],
  theme.colors.dealroom.chartColors[3],
  theme.colors.dealroom.chartColors[4],
  theme.colors.dealroom.chartColors[5],
  theme.colors.dealroom.base.DEFAULT,
  theme.colors.dealroom.base.heavy.DEFAULT,
  theme.colors.dealroom.chartColors[6],
  theme.colors.dealroom.chartColors[7],
  theme.colors.dealroom.chartColors[8],
];

/**
 * ColorPicker renders a color selection field with preset and custom color options.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the color picker field.
 * @param {string} props.value - Selected color value.
 * @param {boolean} props.required - Whether the field is required.
 * @param {Object} props.error - Error object for validation.
 * @param {function} props.onChange - Callback when the color changes.
 */
const ColorPicker = ({
  label = "",
  value = "",
  required = false,
  error = {},
  onChange,
}) => {
  const [isOpen, toggle] = useState(false);

  const handleColorChange = (color) => {
    onChange(color);
  };

  return (
    <LabeledField label={label} required={required} error={error}>
      <div className="flex flex-wrap items-center gap-2">
        {DEFAULT_OPTIONS.map((color) => (
          <div
            key={color}
            className={twMerge(
              "h-8 w-8 border border-ever-base-300 rounded-lg cursor-pointer",
              value === color &&
                "border-ever-primary ring ring-ever-primary-ring"
            )}
            style={{ background: color }}
            onClick={() => handleColorChange(color)}
          />
        ))}
        <div className="h-px w-3 bg-ever-base-400" />
        <Popover.Root open={isOpen} onOpenChange={toggle}>
          <Popover.Trigger asChild>
            <div
              className={twMerge(
                "flex w-[132px] gap-2 border rounded-lg py-1.5 px-2 cursor-pointer",
                isOpen
                  ? "border-ever-primary ring ring-ever-primary-ring"
                  : "border-ever-base-400"
              )}
            >
              <div
                className="h-5 min-w-5 border border-ever-base-400 rounded-sm"
                style={{ backgroundColor: value }}
              />
              <Typography.TextSMMedium>{value}</Typography.TextSMMedium>
              <div className="ml-auto">
                <EditIcon className="w-5 h-5 p-px text-ever-base-content-low" />
              </div>
            </div>
          </Popover.Trigger>
          <Popover.Portal>
            <Popover.Content
              align="end"
              className="mx-px p-2 shadow-xl my-1.5 border border-solid border-ever-base-400 rounded-lg bg-ever-base"
            >
              <HexColorPicker color={value} onChange={handleColorChange} />
            </Popover.Content>
          </Popover.Portal>
        </Popover.Root>
      </div>
    </LabeledField>
  );
};

ColorPicker.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  required: PropTypes.bool,
  error: PropTypes.object,
  onChange: PropTypes.func.isRequired,
};

ColorPicker.defaultProps = {
  label: "",
  value: "",
  required: false,
  error: {},
};

export default ColorPicker;
