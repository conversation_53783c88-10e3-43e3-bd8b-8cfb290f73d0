import PropTypes from "prop-types";
import { Fragment } from "react";
import { useFormContext, useFieldA<PERSON><PERSON>, Controller } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import { Button } from "./ButtonGroup";
import TabComponent from "./TabComponent";
import TextArea from "./TextArea";
import TextInput from "./TextInput";

const defaultFaq = () => ({
  key: uuidv4(),
  question: "",
  answer: "",
  button: {
    ctaText: "",
    link: "",
    navigationType: "external",
  },
});

/**
 * FAQList renders a list of FAQ items, each with question, answer, and button fields, using TabComponent.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the FAQ section.
 * @param {string} props.name - Field name prefix for form fields.
 * @param {Object} props.pageSpec - Page specification object for page/section options.
 */
const FAQList = ({ label = "", name, pageSpec }) => {
  const { control } = useFormContext();

  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
  });

  const handleUpdate = (type, ...args) => {
    switch (type) {
      case "move": {
        const [sourceIndex, destinationIndex] = args;
        move(sourceIndex, destinationIndex);
        break;
      }
      case "add": {
        append(args[0]);
        break;
      }
      case "remove": {
        remove(args[0]);
        break;
      }
      default: {
        break;
      }
    }
  };

  return (
    <TabComponent
      items={fields}
      label={label}
      tabLabel="FAQ"
      onUpdate={handleUpdate}
      getDefaultItem={defaultFaq}
    >
      {(item, index) => (
        <Fragment key={item.key}>
          <Controller
            name={`${name}.${index}.question`}
            control={control}
            rules={{
              required: "Question is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Question"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.answer`}
            control={control}
            rules={{
              required: "Answer is required",
            }}
            render={({ field, fieldState }) => (
              <TextArea
                label="Answer"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Button
            button={{
              ...item.button,
              ctaLabel: "CTA",
              linkLabel: "Link",
            }}
            name={`${name}.${index}.button`}
            pageSpec={pageSpec}
          />
        </Fragment>
      )}
    </TabComponent>
  );
};

FAQList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  pageSpec: PropTypes.object.isRequired,
};

FAQList.defaultProps = {
  label: "",
};

export default FAQList;
