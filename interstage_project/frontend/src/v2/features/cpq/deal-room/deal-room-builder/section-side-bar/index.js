import { FilePlus03Icon } from "@everstage/evericons/outlined";
import * as Accordion from "@radix-ui/react-accordion";
import { cloneDeep } from "lodash";
import { Fragment } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

import { EverDiv<PERSON>, EverTg, IconButton } from "~/v2/components";
import { useUnsavedChanges } from "~/v2/features/cpq/deal-room/deal-room-builder/hooks";

import Page, { SidebarItem } from "./Page";
import { CATEGORIES_KEYS } from "../constants";
import { useBuilder } from "../hooks";
/**
 * SectionSidebar renders the sidebar with pages and branding categories, supporting drag-and-drop and expansion.
 */
const SectionSidebar = () => {
  const {
    activePage,
    categories,
    expandedPages,
    onAddPage,
    onExpandPage,
    onDragEnd,
    onPageDragOver,
    updateActiveState,
  } = useBuilder();
  const { executeWithUnsavedChangesCheck } = useUnsavedChanges();

  const onSectionChange = (pageId, sectionId) => {
    const sectionChangeAction = () => {
      updateActiveState(pageId, sectionId);
    };

    executeWithUnsavedChangesCheck(sectionChangeAction, "change section");
  };

  const renderPages = (category) => {
    const { key, pages } = category;
    if (pages?.length === 0) {
      return null;
    }

    if (key === CATEGORIES_KEYS.PAGES) {
      const pageNames = new Set(pages.map((page) => page.label));

      return (
        <Droppable type="PAGE" droppableId={key}>
          {(provided) => (
            <div
              ref={provided.innerRef}
              className="pt-1"
              {...provided.droppableProps}
            >
              <Accordion.Root
                type="multiple"
                value={[...expandedPages]}
                onValueChange={onExpandPage}
              >
                {pages.map((page, pageIndex) => {
                  const tempPageNames = cloneDeep(pageNames);
                  tempPageNames.delete(page.label);

                  return (
                    <Draggable
                      key={page.page_id}
                      draggableId={page.page_id}
                      index={pageIndex}
                      isDragDisabled={expandedPages.has(page.page_id)}
                    >
                      {(prov) => (
                        <div
                          ref={prov.innerRef}
                          {...prov.draggableProps}
                          {...prov.dragHandleProps}
                        >
                          <Page
                            page={page}
                            isOpen={expandedPages.has(page.page_id)}
                            pageNames={tempPageNames}
                            onPageDragOver={onPageDragOver}
                            onSectionChange={onSectionChange}
                          />
                        </div>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </Accordion.Root>
            </div>
          )}
        </Droppable>
      );
    } else if (key === CATEGORIES_KEYS.BRANDING) {
      return (
        <>
          {pages.map((page) => (
            <SidebarItem
              key={page.page_id}
              label={page.label}
              isActive={page.page_id === activePage}
              onClick={() => onSectionChange(page.page_id, null)}
            />
          ))}
        </>
      );
    }
  };

  return (
    <div className="flex flex-col min-w-60 max-w-60 h-full py-4 border border-ever-base-300 bg-ever-base-50 rounded-lg">
      <DragDropContext onDragEnd={onDragEnd}>
        <div className="flex flex-col h-full gap-2 px-4 overflow-y-auto">
          {Object.entries(categories).map(([key, category], categoryIndex) => (
            <Fragment key={key}>
              {categoryIndex !== 0 && <EverDivider className="my-1 min-h-px" />}
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2 h-8 sticky top-0 bg-ever-base-50 z-30">
                  {category.icon}
                  <EverTg.Heading4>{category.label}</EverTg.Heading4>
                  {key === CATEGORIES_KEYS.PAGES &&
                    category.pages.length < 5 && (
                      <IconButton
                        type="text"
                        size="medium"
                        className="!w-8 !h-8 ml-auto"
                        icon={<FilePlus03Icon className="w-5 h-5 p-px" />}
                        onClick={onAddPage}
                      />
                    )}
                </div>
                <div>{renderPages(category)}</div>
              </div>
            </Fragment>
          ))}
        </div>
      </DragDropContext>
    </div>
  );
};

export default SectionSidebar;
