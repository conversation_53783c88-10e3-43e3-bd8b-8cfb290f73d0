import {
  DotsVerticalIcon,
  PlusCircleIcon,
  ChevronDownIcon,
} from "@everstage/evericons/outlined";
import { ChevronDownArrowIcon } from "@everstage/evericons/solid";
import * as Accordion from "@radix-ui/react-accordion";
import { Dropdown, Menu } from "antd";
import { motion, AnimatePresence } from "framer-motion";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useState, useRef } from "react";
import { Droppable, Draggable } from "react-beautiful-dnd";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

import { EverTg, EverButton, IconButton, message } from "~/v2/components";
import {
  TruncatedText,
  RemoveConfirmationModal,
} from "~/v2/features/cpq/components";
import {
  MORE_OPTIONS,
  MORE_MENU_ITEMS,
} from "~/v2/features/cpq/deal-room/constants";

import AddSectionPopover from "../AddSectionPopover";
import { useBuilder } from "../hooks";

/**
 * SidebarItem renders a clickable sidebar item for a block or page.
 *
 * @param {Object} props
 * @param {string} props.label - The label to display.
 * @param {boolean} props.isActive - Whether the item is active.
 * @param {function} props.onClick - Click handler for the item.
 */
export const SidebarItem = ({ label, isActive, onClick }) => {
  return (
    <div
      className={twMerge(
        "flex mb-2 flex-col py-1 pl-3 pr-1 relative z-10 border border-transparent rounded-md cursor-pointer hover:bg-ever-base-100 transition-all duration-200",
        isActive && "bg-ever-primary-lite border-ever-primary"
      )}
      onClick={onClick}
    >
      <TruncatedText text={label} placement="right">
        <EverTg.Text className="py-px">{label}</EverTg.Text>
      </TruncatedText>
    </div>
  );
};

SidebarItem.propTypes = {
  label: PropTypes.string.isRequired,
  isActive: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
};

SidebarItem.defaultProps = {
  isActive: false,
};

/**
 * Page renders a sidebar accordion item for a page, including its sections and actions.
 *
 * @param {Object} props
 * @param {Object} props.page - The page object.
 * @param {boolean} props.isOpen - Whether the page accordion is open.
 * @param {Set} props.pageNames - Set of page names.
 * @param {function} props.onPageDragOver - Handler for drag over event.
 * @param {function} props.onSectionChange - Handler for section change.
 */
const Page = ({ page, isOpen, pageNames, onPageDragOver, onSectionChange }) => {
  const {
    dealRoomData,
    activeSection,
    onClonePage,
    onDeletePage,
    onAddSection,
    onUpdatePageName,
  } = useBuilder();
  const nameRef = useRef(null);
  const { watch } = useFormContext();
  const pagesOrder = watch("deal_room_page_spec.pages_order");

  const [pageName, setPageName] = useState(page.label);
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);
  const [isEditPageName, setIsEditPageName] = useState(false);
  const [nameWidth, setNameWidth] = useState(0);

  const moreOptionsMenu = (
    <Menu
      className="flex items-start !p-2"
      onClick={(event) => {
        event.domEvent.stopPropagation();
        if (event.key === "clone") {
          onClonePage(page.page_id);
        } else if (event.key === "delete") {
          setShowRemoveConfirmModal(true);
        }
      }}
    >
      {MORE_MENU_ITEMS.map((item) => {
        if (item.type === MORE_OPTIONS.CLONE && pagesOrder.length === 5) {
          return null;
        }

        return (
          <Menu.Item
            className="!px-0 w-full hover:!bg-transparent !h-9"
            key={item.type}
          >
            <EverButton
              type="text"
              color="base"
              size="small"
              className="w-full justify-start"
              prependIcon={item.icon}
            >
              <EverTg.Caption className="font-normal">
                {item.label}
              </EverTg.Caption>
            </EverButton>
          </Menu.Item>
        );
      })}
    </Menu>
  );

  const handlePageNameChange = (e) => {
    if (e.target.value.length <= 50) {
      setPageName(e.target.value);
      setTimeout(() => {
        setNameWidth(nameRef.current?.offsetWidth ?? 0);
      }, 10);
    }
  };

  const handlePageNameBlur = () => {
    const trimmedName = pageName.trim();
    // Create a Set of trimmed page names for accurate duplicate detection
    const trimmedPageNames = new Set(
      Array.from(pageNames, (name) => name.trim())
    );

    if (trimmedName.length === 0) {
      setPageName(page.label);
      setIsEditPageName(false);
      return;
    }
    if (trimmedPageNames.has(trimmedName)) {
      message.error("Page name already exists");
      setPageName(page.label);
      setIsEditPageName(false);
      return;
    }
    if (trimmedName !== page.label.trim()) {
      const pageSpec = get(
        dealRoomData,
        `deal_room_page_spec.pages.${page.page_id}`
      );
      onUpdatePageName(page.page_id, {
        ...pageSpec,
        name: trimmedName,
      });
    }
    setIsEditPageName(false);
  };

  return (
    <>
      <Accordion.Item value={page.page_id} className="border-0">
        <Droppable key={page.page_id} droppableId={page.page_id} type="SECTION">
          {(provided, snapshot) => {
            // If this is the droppable being dragged over, ensure it's open
            if (snapshot.isDraggingOver) {
              onPageDragOver(page.page_id);
            }
            return (
              <div ref={provided.innerRef} {...provided.droppableProps}>
                <Accordion.Header
                  className={twMerge(
                    "mb-3 h-8 border border-ever-base-300 bg-ever-base-200 rounded-sm py-1 px-1.5 flex gap-2 items-center w-full hover:shadow-sm",
                    isOpen && "sticky top-8 z-20"
                  )}
                >
                  <Accordion.Trigger className="group">
                    <div className="w-4 h-4 flex items-center justify-center bg-ever-base-300 rounded-sm">
                      <ChevronDownArrowIcon className="w-5 h-5 text-ever-base-content-mid group-data-[state=open]:rotate-0 group-data-[state=closed]:-rotate-90 transition-transform duration-200" />
                    </div>
                  </Accordion.Trigger>
                  <div className="relative overflow-hidden w-full">
                    {isEditPageName && (
                      <input
                        autoFocus
                        className={twMerge(
                          "min-w-20 h-6 font-bold border-0 border-b border-ever-primary bg-transparent outline-0 transition-all text-sm",
                          pageName.length === 0 ? "border-ever-error" : ""
                        )}
                        style={{
                          width: nameWidth + 8 + "px",
                        }}
                        value={pageName}
                        onChange={handlePageNameChange}
                        onBlur={handlePageNameBlur}
                        onKeyUp={(e) => {
                          if (e.key === "Enter") {
                            handlePageNameBlur();
                          }
                        }}
                      />
                    )}
                    <div
                      ref={nameRef}
                      className={twMerge(
                        "flex items-center gap-1.5 min-w-24",
                        isEditPageName ? "absolute invisible" : ""
                      )}
                    >
                      <div
                        className="flex items-center gap-1 cursor-pointer w-full"
                        onClick={() => {
                          setIsEditPageName(true);
                          setNameWidth(nameRef.current?.offsetWidth ?? 0);
                        }}
                      >
                        <EverTg.Text>{pageName}</EverTg.Text>
                      </div>
                    </div>
                  </div>
                  <Dropdown
                    overlay={moreOptionsMenu}
                    trigger={["click"]}
                    onClick={(event) => {
                      event.stopPropagation();
                    }}
                    className="cursor-pointer rounded-sm ml-auto"
                  >
                    <IconButton
                      type="text"
                      color="base"
                      size="small"
                      className="!w-6 !h-6"
                      icon={
                        <DotsVerticalIcon className="w-4 h-4 text-ever-base-content-mid" />
                      }
                    />
                  </Dropdown>
                </Accordion.Header>
                <AnimatePresence initial={false}>
                  <motion.div
                    animate={
                      isOpen
                        ? {
                            height: "auto",
                            opacity: 1,
                            marginBottom: 16,
                            overflow: "visible",
                          }
                        : {
                            height: 0,
                            opacity: 0,
                            marginBottom: 0,
                            overflow: "hidden",
                          }
                    }
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    {page.blocks.map((block, idx) => (
                      <Draggable
                        key={`${block.block_id}_${page.page_id}`}
                        draggableId={`${block.block_id}_${page.page_id}`}
                        index={idx}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            <SidebarItem
                              label={block.label}
                              isActive={block.block_id === activeSection}
                              onClick={() =>
                                onSectionChange(page.page_id, block.block_id)
                              }
                            />
                            {provided.placeholder}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    <Draggable
                      key={`add-section-${page.page_id}`}
                      draggableId={`add-section-${page.page_id}`}
                      index={page.blocks.length}
                      isDragDisabled={true}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                        >
                          <AddSectionPopover
                            onAddSection={(sectionType) =>
                              onAddSection(page.page_id, sectionType)
                            }
                          >
                            <EverButton
                              type="dashed"
                              size="small"
                              className="w-full mt-1"
                              prependIcon={
                                <PlusCircleIcon className="w-4 h-4" />
                              }
                              appendIcon={
                                <ChevronDownIcon className="w-4 h-4" />
                              }
                            >
                              Add Section
                            </EverButton>
                          </AddSectionPopover>
                          {provided.placeholder}
                        </div>
                      )}
                    </Draggable>
                  </motion.div>
                </AnimatePresence>
                {provided.placeholder}
              </div>
            );
          }}
        </Droppable>
      </Accordion.Item>
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title="Delete this page?"
        subtitle="This action cannot be undone."
        onConfirm={() => onDeletePage(page.page_id)}
        onCancel={() => setShowRemoveConfirmModal(false)}
      />
    </>
  );
};

Page.propTypes = {
  page: PropTypes.object.isRequired,
  isOpen: PropTypes.bool,
  onPageDragOver: PropTypes.func.isRequired,
  onSectionChange: PropTypes.func.isRequired,
};

Page.defaultProps = {
  isOpen: false,
};

export default Page;
