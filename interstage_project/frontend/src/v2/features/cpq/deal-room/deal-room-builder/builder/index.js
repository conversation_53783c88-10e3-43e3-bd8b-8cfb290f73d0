import {
  PlusCircleIcon,
  ArrowCircleBrokenUpIcon,
  ArrowCircleBrokenDownIcon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import { PlusCircleIcon as PlusCircleIconSolid } from "@everstage/evericons/solid";
import PropTypes from "prop-types";
import { useState, useRef, useCallback, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

import { RemoveConfirmationModal } from "~/v2/features/cpq/components";
import {
  DealRoomThemeProvider,
  Typography,
  WelcomePageComponent,
  HeaderComponent,
  BUILDER_COMPONENTS,
} from "~/v2/features/cpq/deal-room/components";
import { PAGE_TYPES } from "~/v2/features/cpq/deal-room/constants";

import AutoScaleContainer from "./AutoScaleContainer";
import AddSectionPopover from "../AddSectionPopover";
import { useBuilder } from "../hooks";

/**
 * BlockComponent renders a single block using the appropriate builder component.
 *
 * @param {Object} props
 * @param {string} props.type - The block type key for BUILDER_COMPONENTS.
 * @param {Object} props.data - The block data.
 * @param {Object} props.clientInfo - Client info for the block.
 * @param {string} [props.pageId] - The page ID.
 * @param {string} [props.blockId] - The block ID.
 * @param {number} [props.index] - The block index.
 * @param {boolean} [props.isFirst] - Whether this is the first block.
 * @param {boolean} [props.isLast] - Whether this is the last block.
 * @param {boolean} [props.isPage] - Whether this is a page block.
 */
const BlockComponent = ({
  type,
  data,
  clientInfo,
  pageId = null,
  blockId = null,
  index = null,
  isFirst = false,
  isLast = false,
  isPage = false,
}) => {
  const blockRefs = useRef({});
  const {
    activeSection,
    onAddSection,
    onSectionOrderChange,
    onSectionDelete,
    updateActiveState,
    buyersLogoUrl,
  } = useBuilder();
  const [isHovering, setIsHovering] = useState(false);
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);

  const Component = BUILDER_COMPONENTS[type];

  const scrollToSection = useCallback((sectionId) => {
    if (sectionId && blockRefs.current[sectionId]) {
      const element = blockRefs.current[sectionId];
      setTimeout(() => {
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "start",
        });
      }, 0);
    }
  }, []);

  useEffect(() => {
    scrollToSection(activeSection);
  }, [activeSection, scrollToSection]);

  if (!Component) return null;

  return (
    <div
      ref={(el) => {
        if (el) {
          blockRefs.current[blockId] = el;
        }
      }}
      className="relative transition-all duration-300"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onMouseOver={() => setIsHovering(true)}
    >
      {isPage && (
        <div
          className={twMerge(
            "relative transition-all duration-300 opacity-0 z-50",
            isHovering && "opacity-100"
          )}
        >
          <div className="absolute top-3 right-3 w-fit h-8 flex gap-2 px-2 py-1.5 items-center justify-center z-50 bg-ever-base-50 border border-solid border-ever-base-100 rounded-md shadow-lg">
            <ArrowCircleBrokenUpIcon
              className={twMerge(
                "w-6 h-6 p-1 text-ever-base-content-mid",
                isFirst ? "cursor-not-allowed opacity-50" : "cursor-pointer"
              )}
              onClick={() => onSectionOrderChange(pageId, data.block_id, "up")}
            />
            <ArrowCircleBrokenDownIcon
              className={twMerge(
                "w-6 h-6 p-1 text-ever-base-content-mid",
                isLast ? "cursor-not-allowed opacity-50" : "cursor-pointer"
              )}
              onClick={() =>
                onSectionOrderChange(pageId, data.block_id, "down")
              }
            />
            <Trash03Icon
              className="w-6 h-6 p-1 text-ever-error cursor-pointer"
              onClick={() => setShowRemoveConfirmModal(true)}
            />
          </div>
        </div>
      )}
      <AutoScaleContainer
        key={type}
        data={data}
        onClick={() => updateActiveState(pageId, data.block_id)}
      >
        <Component
          data={data}
          clientInfo={clientInfo}
          buyersLogoUrl={buyersLogoUrl}
          navigateInternal={(pageId, sectionId) =>
            updateActiveState(pageId, sectionId)
          }
        />
      </AutoScaleContainer>
      {isPage && (
        <div
          className={twMerge(
            "relative transition-all duration-300 opacity-0 z-50",
            isHovering && "opacity-100"
          )}
        >
          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center z-50">
            <div className="w-full border-2 border-solid border-ever-primary" />
            <div className="absolute w-6 border-8 rounded-lg border-solid border-ever-base" />
            <AddSectionPopover
              onAddSection={(sectionType) =>
                onAddSection(pageId, sectionType, index)
              }
            >
              <PlusCircleIconSolid className="absolute cursor-pointer w-7 h-7 text-ever-primary z-50" />
            </AddSectionPopover>
          </div>
        </div>
      )}
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title="Delete this section?"
        subtitle="This action cannot be undone."
        onConfirm={() => onSectionDelete(pageId, data.block_id)}
        onCancel={() => setShowRemoveConfirmModal(false)}
      />
    </div>
  );
};

BlockComponent.propTypes = {
  type: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  clientInfo: PropTypes.object,
  pageId: PropTypes.string,
  blockId: PropTypes.string,
  index: PropTypes.number,
  isFirst: PropTypes.bool,
  isLast: PropTypes.bool,
  isPage: PropTypes.bool,
};

BlockComponent.defaultProps = {
  clientInfo: {},
  pageId: null,
  blockId: null,
  index: null,
  isFirst: false,
  isLast: false,
  isPage: false,
};

/**
 * EmptyBlocksPlaceholder renders a placeholder UI when no blocks are present on a page.
 *
 * @param {Object} props
 * @param {string} props.pageId - The page ID for adding new sections.
 */
const EmptyBlocksPlaceholder = ({ pageId }) => {
  const { onAddSection } = useBuilder();

  return (
    <AutoScaleContainer key="empty-blocks">
      <div className="h-full flex items-center justify-center px-28 py-20">
        <div className="flex flex-col items-center gap-6 text-center">
          <AddSectionPopover
            onAddSection={(sectionType) => onAddSection(pageId, sectionType)}
          >
            <div className="w-16 h-16 rounded-full flex items-center justify-center cursor-pointer text-ever-base-content-low hover:text-ever-base-content-mid hover:bg-ever-base-100">
              <PlusCircleIcon className="min-w-20 min-h-20 [&_*]:stroke-[4px]" />
            </div>
          </AddSectionPopover>
          <div className="flex flex-col gap-2">
            <Typography.Heading3 className="text-ever-base-content">
              No blocks added yet
            </Typography.Heading3>
            <Typography.TextLGRegular className="text-ever-base-content-mid">
              Add blocks from the sidebar to build your page
            </Typography.TextLGRegular>
          </div>
        </div>
      </div>
    </AutoScaleContainer>
  );
};

EmptyBlocksPlaceholder.propTypes = {
  pageId: PropTypes.string.isRequired,
};

/**
 * BlocksBuilder is the main builder component for the Deal Room, rendering all blocks for the active page.
 *
 * @param {Object} props
 * @param {Object} props.clientInfo - Client info for the builder.
 */
const BlocksBuilder = ({ clientInfo }) => {
  const { watch } = useFormContext();
  const { activePage, updateActiveState, onExpandPage } = useBuilder();

  const pageSpec = watch(`deal_room_page_spec`);
  const page = pageSpec.pages[activePage];

  if (!page) return null;

  const renderBlocks = () => {
    const headerPage = pageSpec.pages[PAGE_TYPES.HEADER];
    return (
      <>
        <AutoScaleContainer
          key={PAGE_TYPES.HEADER}
          data={headerPage}
          onClick={() => updateActiveState(PAGE_TYPES.HEADER, null)}
        >
          <HeaderComponent
            data={headerPage}
            pages={pageSpec.pages}
            pagesOrder={pageSpec.pages_order}
            activePage={activePage}
            onPageChange={(pageId, sectionId) => {
              updateActiveState(pageId, sectionId);
              onExpandPage([pageId]);
            }}
          />
        </AutoScaleContainer>
        <div className="flex flex-col w-full h-full overflow-y-auto">
          {page.blocks_order.length > 0 ? (
            page.blocks_order.map((blockId, index) => {
              const block = page.blocks[blockId];
              return (
                <BlockComponent
                  key={blockId}
                  type={block.type}
                  data={block}
                  clientInfo={clientInfo}
                  pageId={page.page_id}
                  blockId={blockId}
                  index={index}
                  isFirst={page.blocks_order[0] === blockId}
                  isLast={page.blocks_order.at(-1) === blockId}
                  isPage
                />
              );
            })
          ) : (
            <EmptyBlocksPlaceholder pageId={page.page_id} />
          )}
        </div>
      </>
    );
  };

  return (
    <DealRoomThemeProvider>
      <div
        id="deal-room-builder-container"
        className={twMerge(
          "w-full flex flex-col overflow-y-auto",
          PAGE_TYPES.HEADER === activePage
            ? "h-fit rounded-lg"
            : "h-full rounded-xl"
        )}
        style={{
          filter: `drop-shadow(0px 12.074px 16.099px rgba(0, 23, 128, 0.08)) drop-shadow(0px 4.025px 6.037px rgba(0, 23, 128, 0.03))`,
        }}
      >
        {activePage === PAGE_TYPES.WELCOME ? (
          <AutoScaleContainer key={PAGE_TYPES.WELCOME} data={page}>
            <WelcomePageComponent data={page} clientInfo={clientInfo} />
          </AutoScaleContainer>
        ) : activePage === PAGE_TYPES.HEADER ? (
          <AutoScaleContainer key={PAGE_TYPES.HEADER} data={page}>
            <HeaderComponent
              data={page}
              pages={pageSpec.pages}
              pagesOrder={pageSpec.pages_order}
              onPageChange={(pageId, sectionId) => {
                updateActiveState(pageId, sectionId);
                onExpandPage([pageId]);
              }}
            />
          </AutoScaleContainer>
        ) : (
          renderBlocks()
        )}
      </div>
    </DealRoomThemeProvider>
  );
};

BlocksBuilder.propTypes = {
  clientInfo: PropTypes.object,
};

BlocksBuilder.defaultProps = {
  clientInfo: {},
};

export default BlocksBuilder;
