import {
  Brush01Icon,
  LayersThree02Icon,
  AlignLeftIcon,
  AlignJustifyIcon,
} from "@everstage/evericons/solid";
import { v4 as uuidv4 } from "uuid";

import { PAGE_TYPES, BLOCK_TYPES } from "~/v2/features/cpq/deal-room/constants";
import {
  sampleFemale1,
  sampleFemale2,
  sampleFemale4,
  sampleMale1,
  sampleTeam1,
  sampleTeamVideo1,
  logoSectionBackground,
} from "~/v2/images";
import theme from "~/v2/themes/blueBias23";

import {
  TextInput,
  TextArea,
  RadioGroup,
  ColorPicker,
  FileUpload,
  ButtonGroup,
  FAQList,
  MetricsList,
  MembersList,
  ResourceList,
  TestimonialList,
  FeatureList,
  HeroList,
} from "./block-fields";

const CATEGORIES_KEYS = {
  BRANDING: "branding",
  PAGES: "pages",
};

const SECTION_TYPES = {
  CONTENT: "content",
  BACKGROUND: "background",
  BUTTONS: "buttons",
  FAQ: "faq",
  METRICS: "metrics",
  MEMBERS: "members",
  RESOURCES: "resources",
  TESTIMONIAL: "testimonial",
};

const CATEGORIES = {
  [CATEGORIES_KEYS.BRANDING]: {
    key: CATEGORIES_KEYS.BRANDING,
    label: "BRANDING",
    icon: <Brush01Icon className="w-4 h-4 text-ever-info" />,
    pages: [
      {
        page_id: PAGE_TYPES.WELCOME,
        label: "Welcome Page",
      },
      {
        page_id: PAGE_TYPES.HEADER,
        label: "Header",
      },
    ],
  },
  [CATEGORIES_KEYS.PAGES]: {
    key: CATEGORIES_KEYS.PAGES,
    label: "PAGES",
    icon: <LayersThree02Icon className="w-4 h-4 text-ever-info" />,
    pages: [],
  },
};

const BLOCK_PROPERTIES = {
  TITLE: {
    key: "title",
    label: "Title",
    component: TextInput,
    section: SECTION_TYPES.CONTENT,
  },
  TAGLINE: {
    key: "tagline",
    label: "Tagline",
    component: TextInput,
    section: SECTION_TYPES.CONTENT,
  },
  DESCRIPTION: {
    key: "description",
    label: "Description",
    component: TextArea,
    section: SECTION_TYPES.CONTENT,
  },
  ALIGNMENT: {
    key: "text.text_alignment",
    label: "Content alignment",
    component: RadioGroup,
    options: [
      {
        label: "Left align",
        value: "left",
        icon: <AlignLeftIcon className="w-4 h-4 text-ever-base-content-mid" />,
      },
      {
        label: "Center align",
        value: "center",
        icon: (
          <AlignJustifyIcon className="w-4 h-4 text-ever-base-content-mid" />
        ),
      },
    ],
    section: SECTION_TYPES.CONTENT,
  },
  TEXT_COLOR: {
    key: "text.text_color",
    label: "Text Colour",
    component: ColorPicker,
    section: SECTION_TYPES.CONTENT,
  },
  BACKGROUND_COLOR: {
    key: "background.background_color",
    label: "Background Colour",
    component: ColorPicker,
    section: SECTION_TYPES.BACKGROUND,
  },
  BACKGROUND_IMAGE: {
    key: "background.background_image",
    label: "Background Image",
    component: FileUpload,
    section: SECTION_TYPES.BACKGROUND,
  },
  BUTTONS: {
    key: "buttons",
    label: "Button",
    component: ButtonGroup,
    section: SECTION_TYPES.BUTTONS,
  },
  HERO: {
    key: "hero",
    label: "Hero",
    component: HeroList,
    section: "",
  },
  FEATURE: {
    key: "feature",
    label: "Feature",
    component: FeatureList,
    section: "",
  },
  FAQS: {
    key: "faqs",
    label: "FAQ",
    component: FAQList,
    section: SECTION_TYPES.FAQ,
  },
  METRICS: {
    key: "metrics",
    label: "Metric",
    component: MetricsList,
    section: SECTION_TYPES.METRICS,
  },
  MEMBERS: {
    key: "members",
    label: "Member",
    component: MembersList,
    section: SECTION_TYPES.MEMBERS,
  },
  RESOURCES: {
    key: "resources",
    label: "Resource",
    component: ResourceList,
    section: SECTION_TYPES.RESOURCES,
  },
  TESTIMONIAL: {
    key: "testimonial",
    label: "Testimonial",
    component: TestimonialList,
    section: SECTION_TYPES.TESTIMONIAL,
  },
};

const pageSchema = {
  [PAGE_TYPES.WELCOME]: {
    label: "Welcome Page",
    properties: [
      {
        ...BLOCK_PROPERTIES.TITLE,
        required: true,
      },
      BLOCK_PROPERTIES.DESCRIPTION,
    ],
  },
  [PAGE_TYPES.HEADER]: {
    label: "Header",
    properties: [BLOCK_PROPERTIES.BACKGROUND_COLOR],
  },
};

const blockSchema = {
  [BLOCK_TYPES.LOGO]: {
    label: "Logo",
    properties: [
      BLOCK_PROPERTIES.TITLE,
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.TEXT_COLOR,
      BLOCK_PROPERTIES.BACKGROUND_IMAGE,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
    ],
  },
  [BLOCK_TYPES.HERO]: {
    label: "Hero",
    properties: [
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.ALIGNMENT,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.HERO,
    ],
  },
  [BLOCK_TYPES.FEATURE]: {
    label: "Feature",
    properties: [
      BLOCK_PROPERTIES.TAGLINE,
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.BUTTONS,
      BLOCK_PROPERTIES.FEATURE,
    ],
  },
  [BLOCK_TYPES.METRICS]: {
    label: "Metrics",
    properties: [
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.ALIGNMENT,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.METRICS,
    ],
  },
  [BLOCK_TYPES.FAQS]: {
    label: "FAQs",
    properties: [
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.FAQS,
    ],
  },
  [BLOCK_TYPES.MEMBERS]: {
    label: "Team members",
    properties: [
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.MEMBERS,
    ],
  },
  [BLOCK_TYPES.RESOURCES]: {
    label: "Resources",
    properties: [
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.RESOURCES,
    ],
  },
  [BLOCK_TYPES.TESTIMONIAL]: {
    label: "Testimonial",
    properties: [
      { ...BLOCK_PROPERTIES.TITLE, required: true },
      BLOCK_PROPERTIES.DESCRIPTION,
      BLOCK_PROPERTIES.BACKGROUND_COLOR,
      BLOCK_PROPERTIES.TESTIMONIAL,
    ],
  },
};

const BLOCK_ATTRIBUTES_BY_TYPE = {
  logo: {
    title: "Product or Service Name",
    description:
      "Write a one line brief about the product or service you offer.",
    text: {
      text_color: theme.colors.dealroom.base.DEFAULT,
    },
    background: {
      background_image: {
        file_url: logoSectionBackground,
        file_name: "logo-section-background.png",
        file_type: "image",
      },
      background_color: theme.colors.dealroom.base.DEFAULT,
      overlay_color: `${theme.colors.dealroom.chartColors[11]}66`,
    },
  },
  hero: {
    title: "Hello!",
    description:
      "This is an intro block for you to set the context on what information your customers can find here.",
    text: {
      text_alignment: "center",
    },
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    hero: [
      {
        file: {
          file_url: sampleTeamVideo1,
          file_name: "sample-team-video-1.mp4",
          file_type: "video",
        },
      },
    ],
  },
  feature: {
    tagline: "What makes this matter",
    title: "Feature or value you want to highlight",
    description:
      "Showcase your product’s key strengths, customer benefits, or impact. You can also use this space to link a short story or stat that proves why it matters.",
    feature: [
      {
        file: {
          file_url: sampleTeam1,
          file_name: "sample-team-1.jpg",
          file_type: "image",
          file_placement: "left",
        },
      },
    ],
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    buttons: [
      {
        ctaText: "Primary CTA",
        ctaLabel: "Primary CTA Name",
        link: "",
        linkLabel: "Primary CTA Link",
        type: "filled",
        color: "primary",
        ctaRequired: true,
        linkRequired: true,
        navigationType: "external",
      },
      {
        ctaText: "Secondary CTA",
        ctaLabel: "Secondary CTA Name",
        link: "",
        linkLabel: "Secondary CTA Link",
        type: "ghost",
        color: "primary",
        navigationType: "external",
        isBothRequired: true,
      },
    ],
  },
  resources: {
    title: "Resources",
    description:
      "Add content that strengthens your pitch - like demos, pricing, case studies, or FAQs - to inform and build trust with buyers.",
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    resources: [
      {
        key: uuidv4(),
        title: "Product Demo Video",
        description:
          "Give buyers a quick look at how your product works and how it solves their problems.",
        resource_type: "video",
        file_action_text: "",
        file: {
          file_url: "",
          file_name: "",
          file_type: "video",
        },
        button: {
          ctaText: "",
          link: "",
          navigationType: "external",
        },
      },
      {
        key: uuidv4(),
        title: "Proposal PDF",
        description:
          "Share detailed pricing, contract terms, or onboarding plans in a downloadable format.",
        resource_type: "file",
        file_action_text: "",
        file: {
          file_url: "",
          file_name: "",
          file_type: "file",
        },
        button: {
          ctaText: "",
          link: "",
          navigationType: "external",
        },
      },
      {
        key: uuidv4(),
        title: "Customer Case Study",
        description:
          "Highlight real-world impact and success stories to build confidence in your solution.",
        resource_type: "web",
        file_action_text: "",
        file: {
          file_url: "",
          file_name: "",
          file_type: "file",
        },
        button: {
          ctaText: "",
          link: "",
          navigationType: "external",
        },
      },
    ],
  },
  metrics: {
    title: "Metrics that matter",
    description:
      "Highlight your impact with numbers - whether it's customer growth, uptime, ROI, or anything else buyers care about.",
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    text: {
      text_color: "",
      text_alignment: "left",
    },
    metrics: [
      {
        key: uuidv4(),
        metric_data: "10,000+",
        metric_description: "Happy customers worldwide",
      },
      {
        key: uuidv4(),
        metric_data: "99.90%",
        metric_description: "System uptime",
      },
      {
        key: uuidv4(),
        metric_data: "3x",
        metric_description: "Faster go-live than industry average",
      },
    ],
  },
  faqs: {
    title: "Frequently asked questions",
    description:
      "Answer common questions buyers or stakeholders might have about your product, pricing, implementation, or support. Use this space to reduce objections and build clarity.",
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    faqs: [
      {
        key: uuidv4(),
        question: "What does your product do?",
        answer:
          "Give a high-level overview of your solution—what problems it solves and who it's for.",
        button: {
          ctaText: "",
          link: "",
          navigationType: "external",
        },
      },
      {
        key: uuidv4(),
        question: "How long does it take to implement?",
        answer:
          "Share typical timelines and what’s involved so buyers know what to expect.",
        button: {
          ctaText: "",
          link: "",
          navigationType: "external",
        },
      },
      {
        key: uuidv4(),
        question: "What kind of support do you offer?",
        answer:
          "Mention onboarding help, SLAs, support channels, and anything else that reassures buyers.",
        button: {
          ctaText: "",
          link: "",
          navigationType: "external",
        },
      },
    ],
  },
  testimonial: {
    title: "Our Solutions That Fit Your Needs",
    description:
      "Share direct feedback from real users. Highlight quotes that speak to your product’s value, ease of use, support experience, or business impact.",
    variant: "carousel",
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    testimonial: [
      {
        key: uuidv4(),
        name: "Maria Lopez",
        tagline: "On Solving Complexity with Simplicity",
        message:
          "We evaluated several tools, but this was the only one that struck the right balance between power and usability. It just works, and our team loves using it.",
        title: "Director of Sales Operations - Acme Systems",
        file: {
          file_url: sampleFemale4,
          file_name: "sample-female-4.jpg",
          file_type: "image",
        },
      },
    ],
  },
  members: {
    title: "Team Members",
    description:
      "Introduce key people who’ll support or work with the customer - like onboarding specialists, account managers, or solution consultants.",
    background: {
      background_color: theme.colors.dealroom.base.DEFAULT,
    },
    members: [
      {
        key: uuidv4(),
        name: "Alex Rivera",
        role: "Customer Success Manager",
        linkedin: "",
        email: "",
        phone: "",
        file: {
          file_url: sampleFemale1,
          file_name: "sample-female-1.jpg",
          file_type: "image",
        },
      },
      {
        key: uuidv4(),
        name: "Maria Alvarez",
        role: "Implementation Specialist",
        linkedin: "",
        email: "",
        phone: "",
        file: {
          file_url: sampleFemale2,
          file_name: "sample-female-2.jpg",
          file_type: "image",
        },
      },
      {
        key: uuidv4(),
        name: "Lucas Gladwell",
        role: "Account Executive",
        linkedin: "",
        email: "",
        phone: "",
        file: {
          file_url: sampleMale1,
          file_name: "sample-male-1.jpg",
          file_type: "image",
        },
      },
    ],
  },
};

const API = {
  UPDATE_DEALROOM: "updateDealRoom",
  GET_DEALROOM: "getDealRoom",
  UPDATE_DEALROOM_PAGES: "updateDealRoomPages",
  UPDATE_DEALROOM_PAGE: "updateDealRoomPage",
  UPDATE_DEALROOM_BLOCK: "updateDealRoomBlock",
  CLONE_DEALROOM: "cloneDealRoom",
  DELETE_DEALROOM: "deleteDealRoom",
  SYNC_DEALROOM_WITH_TEMPLATE: "syncDealRoomWithTemplate",
};

const TEMPLATE_API_ENDPOINTS = {
  [API.UPDATE_DEALROOM]: {
    url: "/ninja/dealroom/manage_templates/update_template",
    method: "POST",
  },
  [API.GET_DEALROOM]: {
    url: "/ninja/dealroom/manage_templates/get_template",
    method: "POST",
  },
  [API.CLONE_DEALROOM]: {
    url: "/ninja/dealroom/manage_templates/clone_template",
    method: "POST",
  },
  [API.DELETE_DEALROOM]: {
    url: "/ninja/dealroom/manage_templates/delete_template",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_PAGES]: {
    url: "/ninja/dealroom/manage_templates/update_pages",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_PAGE]: {
    url: "/ninja/dealroom/manage_templates/update_page",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_BLOCK]: {
    url: "/ninja/dealroom/manage_templates/update_block",
    method: "POST",
  },
};

const DEALROOM_API_ENDPOINTS = {
  [API.UPDATE_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/update_dealroom",
    method: "POST",
  },
  [API.GET_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/get_dealroom",
    method: "POST",
  },
  [API.CLONE_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/clone_dealroom",
    method: "POST",
  },
  [API.DELETE_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/delete_dealroom",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_PAGES]: {
    url: "/ninja/dealroom/manage_rooms/update_pages",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_PAGE]: {
    url: "/ninja/dealroom/manage_rooms/update_page",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_BLOCK]: {
    url: "/ninja/dealroom/manage_rooms/update_block",
    method: "POST",
  },
  [API.SYNC_DEALROOM_WITH_TEMPLATE]: {
    url: "/ninja/dealroom/manage_rooms/sync_dealroom_with_template",
    method: "POST",
  },
};

export {
  CATEGORIES_KEYS,
  CATEGORIES,
  BLOCK_ATTRIBUTES_BY_TYPE,
  pageSchema,
  blockSchema,
  API,
  TEMPLATE_API_ENDPOINTS,
  DEALROOM_API_ENDPOINTS,
};
