import { ImageLeftIcon, ImageRightIcon } from "@everstage/evericons/duotone";
import PropTypes from "prop-types";
import { Fragment } from "react";
import { useFormContext, useFieldArray, Controller } from "react-hook-form";

import { EverTg } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

import FileUpload from "./FileUpload";
import RadioGroup from "./RadioGroup";

/**
 * FeatureList renders a list of feature items, each with an image upload and image placement option.
 *
 * @param {Object} props
 * @param {string} props.name - Field name prefix for form fields.
 */
const FeatureList = ({ name }) => {
  const { control } = useFormContext();

  const { fields } = useFieldArray({
    control,
    name,
  });

  return (
    <div className="flex flex-col gap-5">
      <div className="flex items-center gap-4">
        <EverTg.Text className="text-xs font-medium whitespace-nowrap">
          IMAGE
        </EverTg.Text>
        <div className="w-full h-px bg-ever-base-400" />
      </div>
      {fields.map((field, index) => (
        <Fragment key={field.id}>
          <Controller
            name={`${name}.${index}.file`}
            control={control}
            render={({ field }) => (
              <LabeledField label="Upload">
                <FileUpload
                  value={field.value}
                  file_type="image"
                  onChange={(file) => field.onChange(file)}
                />
              </LabeledField>
            )}
          />
          <Controller
            name={`${name}.${index}.file.file_placement`}
            control={control}
            render={({ field }) => (
              <LabeledField label="Image Placement">
                <RadioGroup
                  value={field.value}
                  options={[
                    {
                      label: "Left",
                      value: "left",
                      icon: (
                        <ImageLeftIcon className="w-4 h-4 text-ever-base-content-mid" />
                      ),
                    },
                    {
                      label: "Right",
                      value: "right",
                      icon: (
                        <ImageRightIcon className="w-4 h-4 text-ever-base-content-mid" />
                      ),
                    },
                  ]}
                  onChange={(value) => field.onChange(value)}
                />
              </LabeledField>
            )}
          />
        </Fragment>
      ))}
    </div>
  );
};

FeatureList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
};

FeatureList.defaultProps = {
  label: "",
};

export default FeatureList;
