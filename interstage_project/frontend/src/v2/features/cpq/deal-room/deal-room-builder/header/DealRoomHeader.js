import {
  EyeIcon,
  EditPencilAltIcon,
  SyncRefreshIcon,
  ShareIcon,
  DotsVerticalIcon,
  CheckCircleBrokenIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { useQuery } from "react-query";
import { twMerge } from "tailwind-merge";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverTg,
  EverBadge,
  EverDrawer,
  IconButton,
  EverButton,
  message,
  EverModal,
  PublicLinkShareModal,
  createPublicLink,
  EverHotToastMessage,
  toast,
} from "~/v2/components";
import {
  RemoveConfirmationModal,
  TruncatedText,
} from "~/v2/features/cpq/components";
import { CreateEditDealRoomModal } from "~/v2/features/cpq/deal-room/components";
import {
  PUBLISH_STATUS_MENU_ITEMS,
  STATUS_OPTIONS,
  MORE_MENU_ITEMS,
  MORE_OPTIONS,
} from "~/v2/features/cpq/deal-room/constants";
import {
  REMOVE_CONFIRMATION_MESSAGES,
  dealRoomToastMessage,
} from "~/v2/features/cpq/deal-room/deal-room-list/constants";
import { replaceUrl } from "~/v2/features/cpq/utils";

import DealRoomPreview from "../../deal-room-preview";
import { useBuilder } from "../hooks";

/**
 * DealRoomHeader renders the header for the Deal Room builder, including actions, status, and preview.
 *
 * @param {Object} props
 * @param {Object} props.clientInfo - Client info for the deal room.
 */
const DealRoomHeader = ({ clientInfo }) => {
  const { watch } = useFormContext();
  const data = watch();
  const {
    dealRoomId,
    isLoading,
    activePage,
    activeSection,
    previewMode,
    buyersLogoUrl,
    setPreviewMode,
    onUpdateDealRoom,
    onDelete,
    onClone,
    onSyncWithTemplate,
  } = useBuilder();
  const { deal_room_name, deal_room_status } = data;
  const { accessToken } = useAuthStore();
  const [isEditTemplateDetails, setIsEditTemplateDetails] = useState(false);
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);
  const [showSyncConfirmModal, setShowSyncConfirmModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showPublishSuccessModal, setShowPublishSuccessModal] = useState(false);

  const {
    data: publicLinkData,
    isLoading: isPublicLinkLoading,
    refetch: refetchPublicLink,
  } = useQuery(
    ["createPublicLink", dealRoomId],
    () =>
      createPublicLink(
        { entity_id: dealRoomId, entity: "dealroom" },
        accessToken
      ),
    {
      enabled: false, // Only run on demand
      retry: false, // Prevent automatic retries
      onSuccess: () => {
        setShowShareModal(true);
      },
      onError: () => {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description={
              "Unable to share the deal room. Please try again later."
            }
          />
        ));
      },
    }
  );

  const getBadgeProps = () => {
    const statusObj = PUBLISH_STATUS_MENU_ITEMS[deal_room_status.toLowerCase()];

    return {
      type: statusObj.type,
      icon: statusObj.icon,
      title: statusObj.label,
    };
  };

  const handleSave = (fieldsToUpdate) => {
    setIsEditTemplateDetails(false);
    onUpdateDealRoom(fieldsToUpdate);
  };

  const handleDelete = () => {
    onDelete(dealRoomToastMessage.delete);
  };

  const handleSyncWithTemplate = async () => {
    await onSyncWithTemplate(dealRoomToastMessage.syncWithTemplate);
    setShowSyncConfirmModal(false);
  };

  const handlePublish = () => {
    const formData = new FormData();
    formData.append("deal_room_status", STATUS_OPTIONS.PUBLISHED);
    onUpdateDealRoom(formData, {
      onSuccess: () => setShowPublishSuccessModal(true),
    });
  };

  const handleShowShareOpen = () => {
    refetchPublicLink();
  };

  const handleMoreOptionsClick = (item) => {
    switch (item) {
      case "sync": {
        setShowSyncConfirmModal(true);
        break;
      }
      case MORE_OPTIONS.CLONE: {
        onClone(dealRoomToastMessage.clone);
        break;
      }
      case MORE_OPTIONS.DELETE: {
        setShowRemoveConfirmModal(true);
        break;
      }
      default: {
        break;
      }
    }
  };

  const moreOptionsMenu = () => {
    const options = [
      {
        icon: (
          <SyncRefreshIcon className="w-4 h-4 text-ever-base-content-mid shrink-0" />
        ),
        type: "sync",
        label: "Sync from template",
      },
      ...MORE_MENU_ITEMS,
    ];

    return (
      <Menu
        className="flex items-start !p-2"
        onClick={(event) => {
          event.domEvent.stopPropagation();
        }}
      >
        {options.map((item, key) => (
          <Menu.Item
            className="!px-0 w-full hover:!bg-transparent !h-9"
            key={key}
          >
            <EverButton
              type="text"
              color={item.type === MORE_OPTIONS.DELETE ? "error" : "base"}
              size="small"
              className="w-full justify-start"
              prependIcon={item.icon}
              onClick={() => handleMoreOptionsClick(item.type)}
            >
              <EverTg.Caption
                className={twMerge(
                  "font-normal",
                  item.type === MORE_OPTIONS.DELETE
                    ? "text-ever-error-lite-content"
                    : ""
                )}
              >
                {item.label}
              </EverTg.Caption>
            </EverButton>
          </Menu.Item>
        ))}
      </Menu>
    );
  };

  return (
    <div className="flex justify-between items-end px-8 py-4 border-b border-ever-base-400 bg-ever-base-50">
      <div className="flex flex-col gap-1.5 my-auto">
        <div className="flex items-center gap-2.5">
          <TruncatedText className="text-xl font-semibold">
            {deal_room_name}
          </TruncatedText>
          <IconButton
            icon={
              <EditPencilAltIcon className="shrink-0 text-ever-base-content-low" />
            }
            type="outlined"
            color="base"
            size="small"
            className="!h-6 !w-6 !rounded-md"
            onClick={() => setIsEditTemplateDetails(true)}
          />
          <EverBadge
            className="gap-1 text-xs font-medium"
            {...getBadgeProps()}
          />
        </div>
      </div>
      <div className="flex gap-3">
        <EverButton
          type="outlined"
          color="base"
          size="small"
          icon={<EyeIcon className="w-5 h-5 text-ever-base-content-mid" />}
          className="rounded-lg [&>div]:gap-1.5"
          onClick={() => {
            const { pages_order, pages } = get(data, "deal_room_page_spec");
            const hasBlocks = pages_order.some(
              (pageId) => get(pages, `${pageId}.blocks_order.length`) > 0
            );
            if (hasBlocks) {
              setPreviewMode("preview");
            } else {
              message.error("Add at least one block to the page");
            }
          }}
        >
          Preview
        </EverButton>
        {deal_room_status === STATUS_OPTIONS.PUBLISHED ? (
          <EverButton
            type="outlined"
            color="base"
            size="small"
            prependIcon={
              <ShareIcon className="w-4 h-4 shrink-0 text-ever-base-content-mid" />
            }
            onClick={handleShowShareOpen}
          >
            Share
          </EverButton>
        ) : (
          <EverButton size="small" onClick={handlePublish}>
            Publish deal room
          </EverButton>
        )}
        <Dropdown
          overlay={moreOptionsMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <IconButton
            type="outlined"
            color="base"
            size="small"
            className="!px-2 ml-auto"
            icon={
              <DotsVerticalIcon className="w-4 h-4 text-ever-base-content-mid" />
            }
          />
        </Dropdown>
      </div>
      <CreateEditDealRoomModal
        type="edit"
        data={data}
        buyersLogoUrl={buyersLogoUrl}
        visible={isEditTemplateDetails}
        onClose={() => setIsEditTemplateDetails(false)}
        onSubmit={handleSave}
        loading={isLoading}
      />
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title={REMOVE_CONFIRMATION_MESSAGES.title}
        subtitle={REMOVE_CONFIRMATION_MESSAGES.subtitle}
        onConfirm={handleDelete}
        onCancel={() => setShowRemoveConfirmModal(false)}
        loading={isLoading}
      />
      <PublicLinkShareModal
        visible={showShareModal}
        onCancel={() => setShowShareModal(false)}
        entity="dealroom"
        publicLink={publicLinkData?.public_link}
        isLoading={isPublicLinkLoading}
        publicLinkId={publicLinkData?.public_link_id}
        sharedUsers={publicLinkData?.shared_users}
      />
      <EverModal.Confirm
        type="warning"
        width={600}
        destroyOnClose
        title="Sync latest changes from template?"
        subtitle="Warning: This will replace existing contents of the deal room."
        visible={showSyncConfirmModal}
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => setShowSyncConfirmModal(false)}
            disabled={isLoading}
          >
            Cancel
          </EverButton>,
          <EverButton
            color="warning"
            key="delete"
            onClick={handleSyncWithTemplate}
            loading={isLoading}
          >
            Yes, proceed
          </EverButton>,
        ]}
      />
      <EverModal.Confirm
        type="success"
        width={600}
        destroyOnClose
        icon={
          <div className="flex items-center justify-center p-5 bg-ever-success-lite rounded-full">
            <CheckCircleBrokenIcon className="w-6 h-6 text-ever-success" />
          </div>
        }
        title={`${deal_room_name}'s deal room successfully published`}
        visible={showPublishSuccessModal}
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            type="outlined"
            onClick={() => setShowPublishSuccessModal(false)}
            disabled={isLoading}
          >
            Close
          </EverButton>,
          <EverButton
            color="primary"
            key="delete"
            onClick={() => {
              setShowPublishSuccessModal(false);
              handleShowShareOpen();
            }}
            loading={isLoading}
            icon={<ShareIcon className="w-4 h-4 shrink-0 text-ever-base" />}
          >
            Share externally
          </EverButton>,
        ]}
      />
      <EverDrawer
        visible={!!previewMode}
        bodyStyle={{ padding: 0 }}
        closable={false}
        destroyOnClose
        height="100%"
        push={false}
      >
        {!!previewMode && (
          <DealRoomPreview
            data={data}
            clientInfo={clientInfo}
            buyersLogoUrl={buyersLogoUrl}
            onClose={() => {
              setPreviewMode(null);
              replaceUrl({ page: activePage }, activeSection);
            }}
          />
        )}
      </EverDrawer>
    </div>
  );
};

DealRoomHeader.propTypes = {
  clientInfo: PropTypes.object,
};

DealRoomHeader.defaultProps = {
  clientInfo: {},
};

export default DealRoomHeader;
