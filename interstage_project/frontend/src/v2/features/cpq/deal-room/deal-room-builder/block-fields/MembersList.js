import PropTypes from "prop-types";
import { Fragment } from "react";
import { useFormContext, useFieldA<PERSON>y, Controller } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import { LabeledField } from "~/v2/features/cpq/components";

import FileUpload from "./FileUpload";
import TabComponent from "./TabComponent";
import TextInput from "./TextInput";

const defaultMember = () => ({
  key: uuidv4(),
  name: "",
  role: "",
  linkedin: "",
  email: "",
  phone: "",
  file: {
    file_url: "",
    file_name: "",
    file_type: "image",
  },
});

/**
 * MembersList renders a list of member items, each with image, name, title, and contact fields, using TabComponent.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the members section.
 * @param {string} props.name - Field name prefix for form fields.
 */
const MembersList = ({ label = "", name }) => {
  const { control } = useFormContext();

  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
  });

  const handleUpdate = (type, ...args) => {
    switch (type) {
      case "move": {
        const [sourceIndex, destinationIndex] = args;
        move(sourceIndex, destinationIndex);
        break;
      }
      case "add": {
        append(args[0]);
        break;
      }
      case "remove": {
        remove(args[0]);
        break;
      }
      default: {
        break;
      }
    }
  };

  return (
    <TabComponent
      items={fields}
      label={label}
      tabLabel="M"
      onUpdate={handleUpdate}
      getDefaultItem={defaultMember}
    >
      {(item, index) => (
        <Fragment key={item.key}>
          <Controller
            name={`${name}.${index}.file`}
            control={control}
            render={({ field }) => (
              <LabeledField label="Image">
                <FileUpload
                  value={field.value}
                  file_type="image"
                  onChange={(file) => field.onChange(file)}
                />
              </LabeledField>
            )}
          />
          <Controller
            name={`${name}.${index}.name`}
            control={control}
            rules={{
              required: "Name is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Name"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.role`}
            control={control}
            rules={{
              required: "Title is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Title"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.linkedin`}
            control={control}
            render={({ field, fieldState }) => (
              <TextInput
                label="LinkedIn"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
              />
            )}
          />
          <Controller
            name={`${name}.${index}.email`}
            control={control}
            render={({ field, fieldState }) => (
              <TextInput
                label="Email"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
              />
            )}
          />
          <Controller
            name={`${name}.${index}.phone`}
            control={control}
            render={({ field, fieldState }) => (
              <TextInput
                label="Phone"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
              />
            )}
          />
        </Fragment>
      )}
    </TabComponent>
  );
};

MembersList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
};

MembersList.defaultProps = {
  label: "",
};

export default MembersList;
