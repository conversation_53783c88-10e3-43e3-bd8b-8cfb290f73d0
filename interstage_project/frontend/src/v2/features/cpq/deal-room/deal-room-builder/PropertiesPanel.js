import { motion } from "framer-motion";
import { get } from "lodash";
import PropTypes from "prop-types";
import { useState, useEffect, useRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

import { EverTg, EverButton } from "~/v2/components";
import { PAGE_TYPES, BLOCK_TYPES } from "~/v2/features/cpq/deal-room/constants";

import { pageSchema, blockSchema } from "./constants";
import { useBuilder } from "./hooks";

/**
 * Properties renders a list of property fields for a block or page.
 *
 * @param {Object} props
 * @param {string} props.name - The field name prefix for form fields.
 * @param {Array} props.properties - The property definitions to render.
 */
const Properties = ({ name, properties }) => {
  const { control } = useFormContext();
  const { dealRoomData } = useBuilder();

  return (
    <>
      {properties.map((property, index) => {
        const { component: Component, ...rest } = property;

        return (
          <motion.div
            key={property.key}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.3,
              delay: index * 0.05,
              ease: "easeOut",
            }}
          >
            <Controller
              name={`${name}.${property.key}`}
              control={control}
              rules={{
                required: property.required
                  ? `${property.label} is required`
                  : false,
              }}
              render={({ field, fieldState }) => (
                <Component
                  {...rest}
                  {...field}
                  label={property.label}
                  error={fieldState.error}
                  required={property.required}
                  pageSpec={get(dealRoomData, "deal_room_page_spec", {})}
                />
              )}
            />
          </motion.div>
        );
      })}
    </>
  );
};

Properties.propTypes = {
  name: PropTypes.string.isRequired,
  properties: PropTypes.arrayOf(PropTypes.object).isRequired,
};

/**
 * PropertiesPanel renders the right-side panel for editing block/page properties in the builder.
 */
const PropertiesPanel = () => {
  const { watch, handleSubmit } = useFormContext();
  const { activePage, activeSection, onSaveProperties, onSavePropertiesError } =
    useBuilder();
  const page = watch(`deal_room_page_spec.pages.${activePage}`);

  // State for scroll shadow
  const [showFooterShadow, setShowFooterShadow] = useState(false);
  const scrollContainerRef = useRef(null);

  // Handle scroll events to show/hide footer shadow
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const isScrollable = scrollHeight > clientHeight;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1; // 1px tolerance

      setShowFooterShadow(isScrollable && !isAtBottom);
    };

    // Initial check
    handleScroll();

    // Add scroll listener
    scrollContainer.addEventListener("scroll", handleScroll);

    // Add resize listener to handle content changes
    const resizeObserver = new ResizeObserver(handleScroll);
    resizeObserver.observe(scrollContainer);

    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
      resizeObserver.disconnect();
    };
  }, [activePage, activeSection]); // Re-run when content changes

  const getBlockInfo = () => {
    // Handle special page types (WELCOME, HEADER)
    if ([PAGE_TYPES.WELCOME, PAGE_TYPES.HEADER].includes(activePage)) {
      return {
        block: pageSchema[activePage],
        data: page,
        name: `deal_room_page_spec.pages.${activePage}`,
        blockType: null,
      };
    }

    // Handle regular blocks within pages
    const blockType = get(page, `blocks.${activeSection}.type`, null);
    return {
      block: blockSchema[blockType],
      data: page.blocks[activeSection],
      name: `deal_room_page_spec.pages.${activePage}.blocks.${activeSection}`,
      blockType,
    };
  };

  const { block, data, name, blockType } = getBlockInfo();

  if (!block) {
    return null;
  }

  const getFilteredProperties = () => {
    let filteredProperties = block.properties;
    if (blockType === BLOCK_TYPES.LOGO) {
      const hasBackgroundImage = !!get(
        data,
        "background.background_image.file_url",
        ""
      );

      filteredProperties = block.properties.filter((property) => {
        if (property.key === "background.background_color") {
          // Show background color only when there's no background image
          return !hasBackgroundImage;
        }
        // Show all other properties
        return true;
      });
    }

    return filteredProperties;
  };

  const grouped = {};
  for (const prop of getFilteredProperties()) {
    if (!grouped[prop.section]) {
      grouped[prop.section] = [];
    }
    grouped[prop.section].push(prop);
  }

  return (
    <motion.div
      className="flex flex-col justify-between h-full min-w-[342px] max-w-[342px] bg-ever-base-50 border border-ever-base-300 rounded-lg"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
    >
      <div
        ref={scrollContainerRef}
        className="flex flex-col gap-6 p-4 h-full overflow-y-auto"
      >
        <motion.div
          className="flex flex-col gap-1"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <EverTg.Heading4>{block.label}</EverTg.Heading4>
          <EverTg.Text className="text-ever-base-content-mid">
            Style the section to your match your requirements
          </EverTg.Text>
        </motion.div>
        <motion.div
          key={`${activePage}-${activeSection}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="flex flex-col gap-6"
        >
          {Object.entries(grouped).map(
            ([section, properties], sectionIndex) => (
              <motion.div
                key={section}
                className="flex flex-col gap-5"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.3,
                  delay: sectionIndex * 0.1 + 0.2,
                  ease: "easeOut",
                }}
              >
                {section && (
                  <div className="flex items-center gap-4">
                    <EverTg.Text className="text-xs font-medium whitespace-nowrap">
                      {section.toUpperCase()}
                    </EverTg.Text>
                    <div className="w-full h-px bg-ever-base-400" />
                  </div>
                )}
                <Properties
                  key={`${activePage}-${section}`}
                  name={name}
                  properties={properties}
                />
              </motion.div>
            )
          )}
        </motion.div>
      </div>
      <motion.div
        className={twMerge(
          "flex items-center justify-center border-t border-solid border-ever-base-300 px-4 py-2 transition-shadow duration-300 ease-in-out",
          showFooterShadow &&
            "shadow-[0_-4px_4px_-2px_rgba(0,23,128,0.06),0_-2px_8px_-2px_rgba(0,23,128,0.10)]"
        )}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <EverButton
          size="medium"
          className="w-fit ml-auto"
          onClick={handleSubmit(
            (data) => onSaveProperties(blockType, data),
            onSavePropertiesError
          )}
        >
          Save changes
        </EverButton>
      </motion.div>
    </motion.div>
  );
};

export default PropertiesPanel;
