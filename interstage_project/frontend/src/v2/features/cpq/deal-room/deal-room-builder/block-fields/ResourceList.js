import PropTypes from "prop-types";
import { Fragment } from "react";
import { useFormContext, useF<PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import { EverSelect } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

import { Button } from "./ButtonGroup";
import FileUpload from "./FileUpload";
import TabComponent from "./TabComponent";
import TextArea from "./TextArea";
import TextInput from "./TextInput";

const RESOURCE_TYPE_OPTIONS = [
  { value: "web", label: "Web Page" },
  { value: "video", label: "Video" },
  { value: "file", label: "File" },
];

const defaultResource = () => ({
  key: uuidv4(),
  title: "",
  description: "",
  resource_type: "",
  file_action_text: "",
  file: {
    file_url: "",
    file_name: "",
    file_type: "all",
  },
  button: {
    ctaText: "",
    link: "",
    navigationType: "external",
  },
});

/**
 * ResourceList renders a list of resource items, each with type, file, title, description, and button fields, using TabComponent.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the resource section.
 * @param {string} props.name - Field name prefix for form fields.
 * @param {Object} props.pageSpec - Page specification object for page/section options.
 */
const ResourceList = ({ label = "", name, pageSpec }) => {
  const { control, getValues, clearErrors } = useFormContext();

  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
  });

  const handleUpdate = (type, ...args) => {
    switch (type) {
      case "move": {
        const [sourceIndex, destinationIndex] = args;
        move(sourceIndex, destinationIndex);
        break;
      }
      case "add": {
        append(args[0]);
        break;
      }
      case "remove": {
        remove(args[0]);
        break;
      }
      default: {
        break;
      }
    }
  };

  return (
    <TabComponent
      items={fields}
      label={label}
      tabLabel="R"
      onUpdate={handleUpdate}
      getDefaultItem={defaultResource}
    >
      {(item, index) => (
        <Fragment key={item.key}>
          <Controller
            name={`${name}.${index}.resource_type`}
            control={control}
            rules={{
              required: "Resource type is required",
            }}
            render={({ field, fieldState }) => (
              <LabeledField
                label="Resource type"
                required
                error={fieldState.error}
              >
                <EverSelect
                  value={field.value}
                  options={RESOURCE_TYPE_OPTIONS}
                  onChange={(val) => {
                    field.onChange(val);
                    if (
                      getValues(`${name}.${index}.resource_type`) === "file"
                    ) {
                      clearErrors(`${name}.${index}.button.ctaText`);
                      clearErrors(`${name}.${index}.button.link`);
                    } else {
                      clearErrors(`${name}.${index}.file`);
                      clearErrors(`${name}.${index}.file_action_text`);
                    }
                  }}
                  placeholder="Select resource type"
                />
              </LabeledField>
            )}
          />
          {getValues(`${name}.${index}.resource_type`) === "file" && (
            <Controller
              name={`${name}.${index}.file`}
              control={control}
              rules={{
                required: "File is required",
              }}
              render={({ field, fieldState }) => (
                <LabeledField label="File" error={fieldState.error}>
                  <FileUpload
                    value={field.value}
                    file_type="all"
                    onChange={(file) => field.onChange(file)}
                  />
                </LabeledField>
              )}
            />
          )}
          <Controller
            name={`${name}.${index}.title`}
            control={control}
            rules={{
              required: "Title is required",
            }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Title"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          <Controller
            name={`${name}.${index}.description`}
            control={control}
            rules={{
              required: "Description is required",
            }}
            render={({ field, fieldState }) => (
              <TextArea
                label="Description"
                value={field.value}
                onChange={(value) => field.onChange(value)}
                error={fieldState.error}
                required
              />
            )}
          />
          {getValues(`${name}.${index}.resource_type`) === "file" ? (
            <Controller
              name={`${name}.${index}.file_action_text`}
              control={control}
              rules={{
                required: "CTA is required",
              }}
              render={({ field, fieldState }) => (
                <TextInput
                  label="CTA"
                  value={field.value}
                  onChange={(value) => field.onChange(value)}
                  error={fieldState.error}
                  required
                />
              )}
            />
          ) : (
            <Button
              button={{
                ...item.button,
                ctaLabel: "CTA",
                linkLabel: "Link",
              }}
              name={`${name}.${index}.button`}
              pageSpec={pageSpec}
            />
          )}
        </Fragment>
      )}
    </TabComponent>
  );
};

ResourceList.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  pageSpec: PropTypes.object.isRequired,
};

ResourceList.defaultProps = {
  label: "",
};

export default ResourceList;
