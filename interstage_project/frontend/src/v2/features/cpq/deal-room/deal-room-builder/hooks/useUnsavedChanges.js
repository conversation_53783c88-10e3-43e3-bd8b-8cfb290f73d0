import { createContext, useContext, useState } from "react";
import { useFormContext } from "react-hook-form";

const UnsavedChangesContext = createContext(null);

/**
 * Provider component that manages unsaved changes state and provides methods
 * to handle form navigation with unsaved changes protection.
 *
 * This provider wraps components that need unsaved changes functionality and
 * provides a context for managing modal state and pending actions.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to be wrapped
 * @returns {JSX.Element} Provider component with context value
 *
 * @example
 * ```jsx
 * <FormProvider {...methods}>
 *   <UnsavedChangesProvider>
 *     <YourFormComponent />
 *   </UnsavedChangesProvider>
 * </FormProvider>
 * ```
 */
export const UnsavedChangesProvider = ({ children }) => {
  const [showUnsavedChangesModal, setShowUnsavedChangesModal] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);
  const { formState, reset } = useFormContext();

  /**
   * Executes an action with unsaved changes protection.
   *
   * If the form has unsaved changes (isDirty), this function will show a confirmation
   * modal before executing the action. If there are no unsaved changes, the action
   * is executed immediately.
   *
   * @param {Function} action - The function to execute after confirmation
   * @param {string} [actionName="perform this action"] - Human-readable name of the action for the modal
   * @returns {void}
   */
  const executeWithUnsavedChangesCheck = (
    action,
    actionName = "perform this action"
  ) => {
    if (formState.isDirty) {
      setPendingAction({ action, actionName });
      setShowUnsavedChangesModal(true);
    } else {
      action();
    }
  };

  /**
   * Handles confirmation of the pending action when user confirms in the modal.
   *
   * This function resets the form state to clear the dirty flag and then
   * executes the pending action. It also closes the modal and clears the
   * pending action state.
   */
  const handleConfirmAction = () => {
    if (pendingAction) {
      // Reset form to clear dirty state
      reset(undefined, {
        keepDirty: false,
        keepErrors: false,
        keepTouched: false,
        keepIsSubmitted: false,
        keepIsValid: false,
      });

      // Execute the pending action
      pendingAction.action();
      setPendingAction(null);
    }
    setShowUnsavedChangesModal(false);
  };

  /**
   * Handles cancellation of the pending action when user cancels in the modal.
   *
   * This function closes the modal and clears the pending action state without
   * executing the action or resetting the form.

   */
  const handleCancelAction = () => {
    setPendingAction(null);
    setShowUnsavedChangesModal(false);
  };

  return (
    <UnsavedChangesContext.Provider
      value={{
        showUnsavedChangesModal,
        executeWithUnsavedChangesCheck,
        handleConfirmAction,
        handleCancelAction,
      }}
    >
      {children}
    </UnsavedChangesContext.Provider>
  );
};

/**
 * Custom hook that provides access to unsaved changes functionality.
 *
 * This hook must be used within an UnsavedChangesProvider component. It provides
 * methods to handle form navigation with unsaved changes protection and modal state.
 */
export const useUnsavedChanges = () => {
  const context = useContext(UnsavedChangesContext);
  if (!context) {
    throw new Error(
      "useUnsavedChanges must be used within an UnsavedChangesProvider"
    );
  }
  return context;
};
