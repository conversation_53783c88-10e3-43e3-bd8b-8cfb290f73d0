/**
 * Validation schemas for Deal Room Builder operations using Valibot.
 * Provides type-safe validation for API payloads before making requests.
 */

import {
  object,
  string,
  pipe,
  minLength,
  optional,
  regex,
  array,
  record,
  any,
  picklist,
  union,
  number,
  transform,
} from "valibot";

// Common validation patterns
const idValidation = pipe(
  string(),
  minLength(1, "ID is required"),
  regex(
    /^[\w-]+$/,
    "ID must contain only letters, numbers, hyphens, and underscores"
  )
);

const pageIdValidation = pipe(
  string(),
  minLength(1, "Page ID is required"),
  regex(
    /^[\w-]+$/,
    "Page ID must contain only letters, numbers, hyphens, and underscores"
  )
);

const blockIdValidation = pipe(
  string(),
  minLength(1, "Block ID is required"),
  regex(
    /^[\w-]+$/,
    "Block ID must contain only letters, numbers, hyphens, and underscores"
  )
);

/**
 * Schema for block data structure.
 * Validates individual block properties.
 */
export const blockDataSchema = object({
  block_id: blockIdValidation,
  type: pipe(
    string(),
    minLength(1, "Block type is required"),
    picklist(
      [
        "hero",
        "feature",
        "metrics",
        "members",
        "resources",
        "faqs",
        "testimonial",
        "logo",
      ],
      "Invalid block type"
    )
  ),
  variant: optional(string()),
  title: optional(string()),
  description: optional(string()),
  file: optional(
    object({
      file_url: optional(string()),
      file_name: optional(string()),
      file_type: optional(string()),
      file_placement: optional(string()),
    })
  ),
  buttons: optional(array(any())),
  background: optional(
    object({
      background_color: optional(string()),
      background_image: optional(
        object({
          file_url: optional(string()),
          file_name: optional(string()),
          file_type: optional(string()),
        })
      ),
      overlay_color: optional(string()),
    })
  ),
  text: optional(
    object({
      text_color: optional(string()),
      text_alignment: optional(string()),
    })
  ),
  // Additional block-specific properties
  members: optional(array(any())),
  features: optional(array(any())),
  metrics: optional(array(any())),
  resources: optional(array(any())),
  faqs: optional(array(any())),
  testimonials: optional(array(any())),
  logo: optional(any()),
});

/**
 * Schema for page data structure.
 * Validates page configuration including blocks.
 */
export const pageDataSchema = object({
  name: optional(pipe(string(), minLength(1, "Page name cannot be empty"))),
  blocks_order: optional(
    array(
      pipe(
        string(),
        minLength(1, "Block ID cannot be empty"),
        regex(
          /^[\w-]+$/,
          "Block ID must contain only letters, numbers, hyphens, and underscores"
        )
      )
    )
  ),
  blocks: optional(record(string(), blockDataSchema)),
});

// ===== DEAL ROOM SCHEMAS =====

/**
 * Schema for getting a deal room.
 * Validates that deal_room_id is a non-empty string.
 */
export const getDealRoomSchema = object({
  deal_room_id: idValidation,
});

/**
 * Schema for updating a deal room.
 * Validates basic deal room properties.
 */
export const updateDealRoomSchema = object({
  deal_room_id: idValidation,
  deal_room_template_id: optional(idValidation),
  deal_room_name: optional(
    pipe(
      union([string(), number()]),
      transform((v) => v.toString()),
      minLength(1, "Deal room name is required")
    )
  ),
  deal_room_opportunity_id: optional(
    pipe(
      union([string(), number()]),
      transform((v) => v.toString()),
      minLength(1, "Opportunity ID is required")
    )
  ),
  deal_room_opportunity_name: optional(
    pipe(
      union([string(), number()]),
      transform((v) => v.toString()),
      minLength(1, "Opportunity name is required")
    )
  ),
  account_id: optional(
    pipe(
      union([string(), number()]),
      transform((v) => v.toString()),
      minLength(1, "Account ID is required")
    )
  ),
  deal_room_status: optional(
    picklist(
      ["draft", "published"],
      "Status must be either 'draft' or 'published'"
    )
  ),
  deal_room_owner: optional(
    pipe(string(), minLength(1, "Deal room owner is required"))
  ),
  customer_logo: optional(
    object({
      type: pipe(string(), minLength(1, "Logo type is required")),
      name: optional(string()),
      url: optional(string()),
    })
  ),
});

/**
 * Schema for updating deal room pages.
 * Validates page structure updates.
 */
export const updateDealRoomPagesSchema = object({
  deal_room_id: idValidation,
  deal_room_page_spec: object({
    pages_order: array(
      pipe(
        string(),
        minLength(1, "Page ID cannot be empty"),
        regex(
          /^[\w-]+$/,
          "Page ID must contain only letters, numbers, hyphens, and underscores"
        )
      )
    ),
    pages: record(string(), pageDataSchema),
  }),
});

/**
 * Schema for updating a single deal room page.
 * Validates page-specific updates.
 */
export const updateDealRoomPageSchema = object({
  deal_room_id: idValidation,
  deal_room_page_id: pageIdValidation,
  deal_room_page_spec: pageDataSchema,
});

/**
 * Schema for updating a single deal room block.
 * Validates block-specific updates.
 */
export const updateDealRoomBlockSchema = object({
  deal_room_id: idValidation,
  deal_room_page_id: pageIdValidation,
  deal_room_page_block_id: blockIdValidation,
  deal_room_page_block_order: optional(array(string())),
  deal_room_page_block: blockDataSchema,
});

/**
 * Schema for cloning a deal room.
 * Validates that deal_room_id is a non-empty string.
 */
export const cloneDealRoomSchema = object({
  deal_room_id: idValidation,
});

/**
 * Schema for deleting a deal room.
 * Validates that deal_room_id is a non-empty string.
 */
export const deleteDealRoomSchema = object({
  deal_room_id: idValidation,
});

/**
 * Schema for syncing deal room with template.
 * Validates that deal_room_id is a non-empty string.
 */
export const syncDealRoomWithTemplateSchema = object({
  deal_room_id: idValidation,
});

// ===== TEMPLATE SCHEMAS =====

/**
 * Schema for getting a deal room template.
 * Validates that deal_room_template_id is a non-empty string.
 */
export const getDealRoomTemplateSchema = object({
  deal_room_template_id: idValidation,
});

/**
 * Schema for updating a deal room template.
 * Validates basic template properties.
 */
export const updateDealRoomTemplateSchema = object({
  deal_room_template_id: idValidation,
  deal_room_template_name: optional(
    pipe(string(), minLength(1, "Template name cannot be empty"))
  ),
  deal_room_template_description: optional(string()),
  deal_room_template_status: optional(
    picklist(
      ["active", "inactive"],
      "Status must be either 'active' or 'inactive'"
    )
  ),
});

/**
 * Schema for updating deal room template pages.
 * Validates page structure updates.
 */
export const updateDealRoomTemplatePagesSchema = object({
  deal_room_template_id: idValidation,
  deal_room_page_spec: object({
    pages_order: array(
      pipe(
        string(),
        minLength(1, "Page ID cannot be empty"),
        regex(
          /^[\w-]+$/,
          "Page ID must contain only letters, numbers, hyphens, and underscores"
        )
      )
    ),
    pages: record(string(), pageDataSchema),
  }),
});

/**
 * Schema for updating a single deal room template page.
 * Validates page-specific updates.
 */
export const updateDealRoomTemplatePageSchema = object({
  deal_room_template_id: idValidation,
  deal_room_template_page_id: pageIdValidation,
  deal_room_template_page_spec: pageDataSchema,
});

/**
 * Schema for updating a single deal room template block.
 * Validates block-specific updates.
 */
export const updateDealRoomTemplateBlockSchema = object({
  deal_room_template_id: idValidation,
  deal_room_template_page_id: pageIdValidation,
  deal_room_template_page_block_id: blockIdValidation,
  deal_room_template_page_block_order: optional(array(string())),
  deal_room_template_page_block: blockDataSchema,
});

/**
 * Schema for cloning a deal room template.
 * Validates that deal_room_template_id is a non-empty string.
 */
export const cloneDealRoomTemplateSchema = object({
  deal_room_template_id: idValidation,
});

/**
 * Schema for deleting a deal room template.
 * Validates that deal_room_template_id is a non-empty string.
 */
export const deleteDealRoomTemplateSchema = object({
  deal_room_template_id: idValidation,
});

export const dealRoomSchema = {
  getSchema: getDealRoomSchema,
  updateSchema: updateDealRoomSchema,
  updatePagesSchema: updateDealRoomPagesSchema,
  updatePageSchema: updateDealRoomPageSchema,
  updateBlockSchema: updateDealRoomBlockSchema,
  cloneSchema: cloneDealRoomSchema,
  deleteSchema: deleteDealRoomSchema,
  syncSchema: syncDealRoomWithTemplateSchema,
};

export const dealRoomTemplateSchema = {
  getSchema: getDealRoomTemplateSchema,
  updateSchema: updateDealRoomTemplateSchema,
  updatePagesSchema: updateDealRoomTemplatePagesSchema,
  updatePageSchema: updateDealRoomTemplatePageSchema,
  updateBlockSchema: updateDealRoomTemplateBlockSchema,
  cloneSchema: cloneDealRoomTemplateSchema,
  deleteSchema: deleteDealRoomTemplateSchema,
};
