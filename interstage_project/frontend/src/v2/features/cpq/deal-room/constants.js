import { CopyIcon, Trash03Icon } from "@everstage/evericons/outlined";
import {
  CheckCircleIcon,
  PauseCircleIcon,
  EditPencilIcon,
} from "@everstage/evericons/solid";

const PAGE_TYPES = {
  WELCOME: "welcome_page",
  HEADER: "header",
};

const BLOCK_TYPES = {
  LOGO: "logo",
  HERO: "hero",
  METRICS: "metrics",
  FEATURE: "feature",
  FAQS: "faqs",
  MEMBERS: "members",
  RESOURCES: "resources",
  TESTIMONIAL: "testimonial",
};

const STATUS_OPTIONS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
  DRAFT: "draft",
  PUBLISHED: "published",
};

const ACTIVE_STATUS_MENU_ITEMS = {
  [STATUS_OPTIONS.ACTIVE]: {
    type: "success",
    icon: <CheckCircleIcon className="h-4 w-4 text-ever-success shrink-0" />,
    value: STATUS_OPTIONS.ACTIVE,
    label: "Active",
  },
  [STATUS_OPTIONS.INACTIVE]: {
    type: "base",
    icon: (
      <PauseCircleIcon className="h-4 w-4 text-ever-base-content-mid shrink-0" />
    ),
    value: STATUS_OPTIONS.INACTIVE,
    label: "Inactive",
  },
};

const PUBLISH_STATUS_MENU_ITEMS = {
  [STATUS_OPTIONS.DRAFT]: {
    type: "warning",
    icon: <EditPencilIcon className="h-4 w-4 p-px text-ever-chartColors-29" />,
    value: STATUS_OPTIONS.DRAFT,
    label: "Draft",
  },
  [STATUS_OPTIONS.PUBLISHED]: {
    type: "success",
    icon: <CheckCircleIcon className="h-4 w-4 p-px text-ever-success" />,
    value: STATUS_OPTIONS.PUBLISHED,
    label: "Published",
  },
};

const MORE_OPTIONS = {
  CLONE: "clone",
  DELETE: "delete",
};

const MORE_MENU_ITEMS = [
  {
    icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid shrink-0" />,
    type: MORE_OPTIONS.CLONE,
    label: "Clone",
  },
  {
    icon: <Trash03Icon className="w-4 h-4 text-ever-error shrink-0" />,
    type: MORE_OPTIONS.DELETE,
    label: "Delete",
  },
];

export {
  PAGE_TYPES,
  BLOCK_TYPES,
  STATUS_OPTIONS,
  ACTIVE_STATUS_MENU_ITEMS,
  PUBLISH_STATUS_MENU_ITEMS,
  MORE_OPTIONS,
  MORE_MENU_ITEMS,
};
