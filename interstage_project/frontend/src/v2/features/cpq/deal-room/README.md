# Deal Room Module

## Overview

The Deal Room module is a powerful, customizable digital presentation platform designed to help sales teams create branded, interactive spaces for engaging with prospects. It enables sales representatives to showcase their products, services, and value propositions through a comprehensive set of components including hero sections, features, metrics, team introductions, resources, and testimonials.

Deal Rooms bridge the gap between traditional sales presentations and modern digital experiences by providing:

- **Personalized Brand Experience**: Customizable themes, colors, and company branding
- **Interactive Components**: Engaging blocks like videos, metrics, FAQs, and team member profiles
- **Template System**: Reusable templates for consistent company messaging
- **Multi-mode Viewing**: Both standard preview and full-screen presentation modes
- **CRM Integration**: Direct linking with opportunity records and account data
- **Public Sharing**: Secure, shareable links for prospect access

## Key Features

- **Visual Builder Interface**: Drag-and-drop page and section management
- **Component Library**: 8+ pre-built components (Hero, Feature, Metrics, Members, Resources, FAQs, Testimonials, Logo)
- **Template Management**: Create and manage reusable deal room templates
- **Brand Integration**: Automated company logo fetching via BrandFetch API
- **File Management**: Support for images, videos, documents, and presentations
- **Responsive Design**: Optimized for desktop and mobile viewing
- **Real-time Preview**: Live preview with instant updates during editing

## Folder Structure

```
deal-room/
├── README.md                     # Main documentation (this file)
├── Architecture.md               # Technical architecture and design patterns
├── UserFlow.md                   # User journey and interaction flows
├── APIReference.md              # API endpoints and data contracts
├── Components.md                # Detailed component documentation
├── constants.js                 # Global constants and enums
│
├── components/                  # Shared components across deal room features
│   ├── index.js                # Component exports
│   ├── CreateEditDealRoomModal.js      # Modal for creating/editing deal rooms
│   ├── CreateEditTemplateModal.js      # Modal for template management
│   ├── LayoutContainer.js              # Layout wrapper component
│   ├── Typography.js                   # Typography system
│   ├── DealRoomThemeProvider.js        # Theme context provider
│   │
│   └── builder-components/             # Individual block components
│       ├── index.js            # Builder component exports
│       ├── Hero.js             # Hero section component
│       ├── Feature.js          # Feature highlight component
│       ├── Metrics.js          # Statistics/metrics component
│       ├── Member.js           # Team member profile component
│       ├── Resources.js        # Resources/documents component
│       ├── FAQ.js              # Frequently asked questions component
│       ├── Logo.js             # Logo/branding component
│       ├── Header.js           # Header component
│       ├── WelcomePage.js      # Welcome page component
│       │
│       └── Testimonial/        # Testimonial component with variants
│           └── ...
│
├── deal-room-list/             # Deal room listing and management
│   ├── index.js               # Main list component
│   ├── DealRoomCard.js        # Individual deal room card
│   └── constants.js           # List-specific constants
│
├── deal-room-template-list/    # Template listing and management
│   ├── index.js               # Main template list component
│   ├── TemplateCard.js        # Individual template card
│   └── constants.js           # Template-specific constants
│
├── deal-room-builder/          # Core builder interface
│   ├── index.js               # Main builder entry point
│   ├── PropertiesPanel.js     # Properties configuration panel
│   ├── AddSectionPopover.js   # Section addition interface
│   ├── constants.js           # Builder-specific constants
│   ├── utils.js               # Builder utility functions
│   │
│   ├── hooks/                 # Builder state management
│   │   ├── index.js           # Hook exports
│   │   ├── useBuilder.js      # Main builder logic and state
│   │   └── usePresentationMode.js  # Presentation mode functionality
│   │
│   ├── builder/               # Visual builder canvas
│   │   ├── index.js           # Builder canvas component
│   │   └── AutoScaleContainer.js   # Auto-scaling container
│   │
│   ├── header/                # Builder header components
│   │   ├── index.js           # Header exports
│   │   ├── DealRoomHeader.js  # Deal room specific header
│   │   └── DealRoomTemplateHeader.js  # Template specific header
│   │
│   ├── section-side-bar/      # Sidebar navigation and management
│   │   ├── index.js           # Sidebar main component
│   │   └── Page.js            # Page management component
│   │
│   └── block-fields/          # Form field components for block properties
│       ├── index.js           # Field exports
│       ├── TextInput.js       # Text input field
│       ├── TextArea.js        # Multi-line text field
│       ├── ColorPicker.js     # Color selection field
│       ├── FileUpload.js      # File upload field
│       ├── ButtonGroup.js     # Button configuration field
│       ├── RadioGroup.js      # Radio button group field
│       ├── FAQList.js         # FAQ management field
│       ├── MetricsList.js     # Metrics configuration field
│       ├── MembersList.js     # Team member management field
│       ├── ResourceList.js    # Resource management field
│       ├── TestimonialList.js # Testimonial management field
│       ├── FileUploadWithOptions.js  # Advanced file upload
│       │
│       └── TabComponent/      # Tabbed interface components
│           └── ...
│
├── deal-room-preview/          # Preview and presentation modes
│   ├── index.js               # Main preview component
│   ├── PreviewMode.js         # Standard preview mode
│   │
│   └── PresentationMode/      # Full-screen presentation mode
│       ├── index.js           # Presentation mode component
│       ├── NavigationOverlay.js    # Presentation navigation
│       ├── SlidePreview.js    # Slide preview functionality
│       └── PresentationMode.scss   # Presentation-specific styles
│
└── docs/                      # Additional documentation
    └── (additional docs as needed)
```

## Quick Start

### Creating a Deal Room

1. Navigate to `/cpq/deal-rooms`
2. Click "Create Deal Room"
3. Fill in deal room details (name, template, CRM opportunity)
4. Add customer logo (optional)
5. Assign deal room owner

### Building Content

1. Access the builder interface
2. Use the sidebar to add pages and sections
3. Configure each section's properties in the Properties Panel
4. Preview changes in real-time
5. Switch between Preview and Presentation modes

### Template Management

1. Navigate to `/cpq/settings/deal-room-templates`
2. Create templates for reusable content structures
3. Manage template status (Active/Inactive)
4. Sync deal rooms with updated templates

## Documentation Structure

- **[ComponentsReference.md](./ComponentsReference.md)** - Complete component reference guide with architecture, usage, and detailed documentation
- **[ComponentsDevelopment.md](./ComponentsDevelopment.md)** - Step-by-step guide for creating new components
- **[NavigationFlows.md](./NavigationFlows.md)** - Detailed navigation flow diagrams for all major components
- **[APIReference.md](./APIReference.md)** - API endpoints, request/response formats, and integration details

## Technology Stack

- **Frontend Framework**: React with Hooks
- **State Management**: React Hook Form, React Query, Recoil
- **Styling**: Tailwind CSS, SCSS
- **File Upload**: Antd Upload component
- **Drag & Drop**: Custom implementation
- **Theme System**: Custom theme provider with CSS variables
- **Brand Integration**: BrandFetch API for logo fetching

## Browser Support

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

## Performance Considerations

- Lazy loading for large file assets
- Optimized image rendering and caching
- Efficient re-renders with React.memo and useMemo
- Debounced API calls for search functionality

## Security Features

- Public link access control with email validation
- File upload restrictions and validation
- XSS protection in user-generated content
- Secure API endpoints with authentication

---

For specific implementation details, API documentation, and component usage, please refer to the linked documentation files above.
