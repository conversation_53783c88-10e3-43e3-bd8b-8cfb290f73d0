import PropTypes from "prop-types";

import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getBackgroundStyles, getTextStyles } from "~/v2/features/cpq/utils";

/**
 * Hero block component for displaying a hero section.
 * @param {Object} props
 * @param {Object} props.data - Hero block data (title, description, file, background, text).
 */
const Hero = ({ data }) => {
  if (!data) return null;

  const { title, description, hero = [], background = {}, text = {} } = data;
  const textStyles = getTextStyles(text, background);
  const backgroundStyles = getBackgroundStyles(background);

  const renderMedia = (file) => {
    if (!file?.file_url) return null;

    if (file.file_type === "video") {
      return (
        <div className="flex flex-col w-full">
          <video
            src={file.file_url}
            className="object-contain w-full rounded-lg"
            controls
            loop
          >
            Your browser does not support the video tag.
          </video>
        </div>
      );
    }

    // Default to image
    return (
      <div className="flex flex-col">
        <img
          src={file.file_url}
          alt={file.file_name}
          className="object-contain max-w-[1200px] max-h-[620px] rounded-lg"
        />
      </div>
    );
  };

  return (
    <div
      className="flex flex-col items-center justify-center gap-12"
      style={{
        ...backgroundStyles,
        ...textStyles,
        alignItems: text.text_alignment === "left" ? "flex-start" : "center",
      }}
    >
      {/* Content */}
      <div className="flex flex-col gap-2">
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>
      {hero.map((item) => renderMedia(item.file))}
    </div>
  );
};

Hero.propTypes = {
  data: PropTypes.object,
};

export default Hero;
