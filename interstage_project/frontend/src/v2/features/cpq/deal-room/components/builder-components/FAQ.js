import { XCloseIcon, ChevronRightIcon } from "@everstage/evericons/outlined";
import PropTypes from "prop-types";
import { useState } from "react";

import { EverButton } from "~/v2/components";
import { AnimatedWrapper, ANIMATION_TYPES } from "~/v2/features/cpq/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles } from "~/v2/features/cpq/utils";

/**
 * FAQ block component for displaying frequently asked questions.
 * @param {Object} props
 * @param {Object} props.data - FAQ block data (title, description, faqs, background, text).
 * @param {Function} [props.navigateInternal] - Optional navigation handler for internal links.
 */
const FAQ = ({ data, navigateInternal = null }) => {
  const [openIndexes, setOpenIndexes] = useState(new Set());

  if (!data) return null;

  const { title, description, faqs = [], background = {}, text = {} } = data;
  const textStyles = getTextStyles(text, background);

  const handleToggle = (idx) => {
    setOpenIndexes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(idx)) {
        newSet.delete(idx);
      } else {
        newSet.add(idx);
      }
      return newSet;
    });
  };

  return (
    <div className="flex flex-col items-center justify-center gap-12">
      {/* Content */}
      <div className="flex flex-col gap-2">
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>

      <div className="flex flex-col w-full">
        {faqs.map((faq, idx) => {
          const { question, answer, button = {} } = faq;
          return (
            <>
              <div key={idx} className="py-6 flex flex-col gap-6">
                <div
                  className="flex items-center justify-between cursor-pointer gap-4"
                  onClick={() => handleToggle(idx)}
                >
                  <Typography.SubHeading5
                    className={`transition-all duration-300 ${
                      openIndexes.has(idx) && "font-semibold !text-dealroom-xl"
                    }`}
                    style={textStyles}
                  >
                    {question}
                  </Typography.SubHeading5>
                  <span className="ml-4">
                    <XCloseIcon
                      className={`w-5 h-5 text-ever-base-content-mid transition-transform duration-300 ${
                        openIndexes.has(idx) ? "rotate-0" : "-rotate-45"
                      }`}
                    />
                  </span>
                </div>
                <AnimatedWrapper
                  isVisible={openIndexes.has(idx)}
                  keyName={`faq-${idx}`}
                  initial={false}
                  motionProps={{
                    type: ANIMATION_TYPES.EXPAND_COLLAPSE,
                    transition: {
                      duration: 0.5,
                    },
                  }}
                >
                  <div className="flex flex-col gap-6 h-full w-full">
                    <Typography.TextBaseRegular
                      className="w-4/5"
                      style={textStyles}
                    >
                      {answer}
                    </Typography.TextBaseRegular>
                    {button.ctaText && button.link && (
                      <EverButton
                        size="large"
                        className="w-fit"
                        appendIcon={<ChevronRightIcon className="w-5 h-5" />}
                        onClick={() => {
                          if (button.navigationType === "internal") {
                            if (
                              button.link &&
                              typeof button.link === "string"
                            ) {
                              const linkParts = button.link.split("#");
                              const pageId = linkParts[0] || "";
                              const sectionId = linkParts[1] || undefined;

                              if (typeof navigateInternal === "function") {
                                navigateInternal(pageId, sectionId);
                              }
                            }
                          } else {
                            window.open(
                              button.link,
                              "_blank",
                              "noopener,noreferrer"
                            );
                          }
                        }}
                      >
                        {button.ctaText}
                      </EverButton>
                    )}
                  </div>
                </AnimatedWrapper>
              </div>
              <div className="h-px w-full bg-ever-base-content-low" />
            </>
          );
        })}
      </div>
    </div>
  );
};

FAQ.propTypes = {
  data: PropTypes.object,
  navigateInternal: PropTypes.func,
};

FAQ.defaultProps = {
  navigateInternal: null,
};

export default FAQ;
