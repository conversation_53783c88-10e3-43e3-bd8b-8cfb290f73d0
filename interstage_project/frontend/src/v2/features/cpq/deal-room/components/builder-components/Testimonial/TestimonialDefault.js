import PropTypes from "prop-types";
import { Fragment } from "react";

import { EverGroupAvatar, EverDiv<PERSON> } from "~/v2/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles } from "~/v2/features/cpq/utils";

/**
 * Default (non-carousel) testimonial block component for displaying testimonials in the Deal Room.
 *
 * @param {Object} props
 * @param {Object} props.data - Testimonial block data (title, description, testimonial array, background, text).
 *   @param {string} [props.data.title] - Section title.
 *   @param {string} [props.data.description] - Section description.
 *   @param {Array} [props.data.testimonial] - Array of testimonial objects.
 *   @param {Object} [props.data.background] - Background style object.
 *   @param {Object} [props.data.text] - Text style object.
 */
const TestimonialDefault = ({ data }) => {
  if (!data) return null;

  const {
    title,
    description,
    testimonial = [],
    background = {},
    text = {},
  } = data;
  const textStyles = getTextStyles(text, background);

  // Default (non-carousel) testimonial rendering
  return (
    <div className="flex flex-col items-center justify-center gap-12">
      <div className="flex flex-col gap-2">
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>
      <div className="flex flex-col gap-8 w-full">
        {testimonial.map((_testimonial, idx) => {
          const {
            name,
            title,
            message,
            tagline = "",
            file = {},
          } = _testimonial;

          return (
            <Fragment key={idx}>
              {idx !== 0 && <EverDivider className="my-1" />}
              <div className="flex flex-row gap-6 items-start">
                <EverGroupAvatar
                  avatars={[
                    {
                      firstName: name,
                      lastName: "",
                      image: file.file_url,
                      shape: file.file_url ? "square" : "round",
                      className: "text-5xl !rounded-2xl",
                    },
                  ]}
                  maxStyle={{
                    width: 100,
                    height: 100,
                  }}
                />
                <div className="flex flex-col gap-4">
                  {tagline && (
                    <Typography.TextLGMedium style={textStyles}>
                      {tagline}
                    </Typography.TextLGMedium>
                  )}
                  <Typography.SubHeading4 style={textStyles}>
                    “{message}”
                  </Typography.SubHeading4>
                  <div className="flex flex-row items-center text-2xl gap-3">
                    <Typography.TextLGMedium style={textStyles}>
                      {name}
                    </Typography.TextLGMedium>
                    {title && (
                      <>
                        <Typography.TextLGRegular style={textStyles}>
                          |
                        </Typography.TextLGRegular>
                        <Typography.TextLGRegular style={textStyles}>
                          {title}
                        </Typography.TextLGRegular>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </Fragment>
          );
        })}
      </div>
    </div>
  );
};

TestimonialDefault.propTypes = {
  data: PropTypes.shape({
    title: PropTypes.string,
    description: PropTypes.string,
    testimonial: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        title: PropTypes.string,
        message: PropTypes.string,
        tagline: PropTypes.string,
        file: PropTypes.object,
      })
    ),
    background: PropTypes.object,
    text: PropTypes.object,
  }),
};

TestimonialDefault.defaultProps = {
  data: {
    title: "",
    description: "",
    testimonial: [],
    background: {},
    text: {},
  },
};

export default TestimonialDefault;
