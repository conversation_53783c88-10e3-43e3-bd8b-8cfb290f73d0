import PropTypes from "prop-types";

import { EverModal, EverButton } from "~/v2/components";

/**
 * UnsavedChangesModal renders a confirmation modal for unsaved changes when switching sections.
 *
 * @param {Object} props
 * @param {boolean} props.visible - Whether the modal is visible.
 * @param {function} props.onCancel - Handler for cancel action.
 * @param {function} props.onConfirm - Handler for confirm action.
 */
const UnsavedChangesModal = ({ visible, onCancel, onConfirm }) => {
  return (
    <EverModal.Confirm
      visible={visible}
      title="You have unsaved changes!"
      subtitle="Are you sure you want to switch sections? Your changes will be lost."
      type="warning"
      confirmationButtons={[
        <EverButton key="cancel" color="base" onClick={onCancel}>
          Cancel
        </EverButton>,
        <EverButton color="primary" key="confirm" onClick={onConfirm}>
          Leave
        </EverButton>,
      ]}
      destroyOnClose
    />
  );
};

UnsavedChangesModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
};

UnsavedChangesModal.defaultProps = {
  visible: false,
  onCancel: () => {},
  onConfirm: () => {},
};

export default UnsavedChangesModal;
