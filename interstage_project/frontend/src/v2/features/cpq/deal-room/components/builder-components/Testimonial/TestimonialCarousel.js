import { UserCircleIcon } from "@everstage/evericons/solid";
import { Carousel } from "antd";
import PropTypes from "prop-types";

import { EverGroupAvatar } from "~/v2/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles } from "~/v2/features/cpq/utils";

/**
 * Carousel component for displaying testimonials in the Deal Room.
 *
 * @param {Object} props
 * @param {Object} props.data - Testimonial block data (title, description, testimonial array, background, text).
 *   @param {string} [props.data.title] - Section title.
 *   @param {string} [props.data.description] - Section description.
 *   @param {Array} [props.data.testimonial] - Array of testimonial objects.
 *   @param {Object} [props.data.background] - Background style object.
 *   @param {Object} [props.data.text] - Text style object.
 */
const TestimonialCarousel = ({ data }) => {
  if (!data) return null;

  const {
    title,
    description,
    testimonial = [],
    background = {},
    text = {},
  } = data;
  const textStyles = getTextStyles(text, background);

  return (
    <div className="flex flex-col items-center justify-center gap-16">
      <div className="flex flex-col gap-2 text-center">
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>
      <Carousel className="w-full max-w-4xl" infinite dots>
        {testimonial.map((_testimonial, idx) => {
          const {
            name,
            title,
            message,
            tagline = "",
            file = {},
          } = _testimonial;

          return (
            <div
              key={idx}
              className="flex flex-col items-center justify-center mb-20"
            >
              <div className="flex items-center w-full justify-between gap-8 mb-6">
                <hr className="flex-1 border-t border-ever-base-400" />
                <EverGroupAvatar
                  avatars={[
                    {
                      firstName: name || "User",
                      lastName: "",
                      image: file.file_url,
                      ...(!name &&
                        !file.file_url && {
                          fallBack: <UserCircleIcon className="w-10 h-10" />,
                        }),
                      shape: file.file_url ? "square" : "round",
                      className: "text-5xl !rounded-2xl",
                    },
                  ]}
                  maxStyle={{
                    width: 100,
                    height: 100,
                  }}
                />
                <hr className="flex-1 border-t border-ever-base-400" />
              </div>
              <div className="flex flex-col gap-4">
                {tagline && (
                  <Typography.TextLGRegular
                    className="text-center italic"
                    style={textStyles}
                  >
                    {tagline}
                  </Typography.TextLGRegular>
                )}
                {message && (
                  <Typography.SubHeading4
                    className="text-center"
                    style={textStyles}
                  >
                    “{message}”
                  </Typography.SubHeading4>
                )}
              </div>
              <div className="flex flex-col gap-2 mt-8 text-center">
                <Typography.TextMDMedium style={textStyles}>
                  {name}
                </Typography.TextMDMedium>
                {title && (
                  <Typography.TextMDRegular style={textStyles}>
                    {title}
                  </Typography.TextMDRegular>
                )}
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};

TestimonialCarousel.propTypes = {
  data: PropTypes.shape({
    title: PropTypes.string,
    description: PropTypes.string,
    testimonial: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        title: PropTypes.string,
        message: PropTypes.string,
        tagline: PropTypes.string,
        file: PropTypes.object,
      })
    ),
    background: PropTypes.object,
    text: PropTypes.object,
  }),
};

TestimonialCarousel.defaultProps = {
  data: {
    title: "",
    description: "",
    testimonial: [],
    background: {},
    text: {},
  },
};

export default TestimonialCarousel;
