/**
 * Layout container component for Deal Room pages.
 * Provides consistent layout structure with background styling and responsive behavior.
 *
 * @example
 * ```
 * <LayoutContainer
 *   data={{ background: { background_color: '#ffffff' }, page_id: 'content' }}
 *   isPreview={false}
 * >
 *   <Typography.Heading1>Page Content</Typography.Heading1>
 * </LayoutContainer>
 * ```
 */

import PropTypes from "prop-types";
import React from "react";
import { twMerge } from "tailwind-merge";

import { PAGE_TYPES } from "~/v2/features/cpq/deal-room/constants";
import { getBackgroundStyles } from "~/v2/features/cpq/utils";

/**
 * Renders a layout container with background styling and responsive behavior.
 *
 * @param {Object} props - Component props.
 * @param {Object} props.data - Page data containing background and page_id.
 * @param {boolean} props.isPreview - Whether the component is in preview mode.
 * @param {boolean} props.isSlidePreview - Whether the component is in slide preview mode.
 * @param {React.ReactNode} props.children - The content to render inside the container.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the layout container component.
 */
const LayoutContainer = ({
  data,
  isPreview,
  isSlidePreview,
  children,
  className,
}) => {
  const { background = {}, page_id } = data;
  const backgroundStyles = getBackgroundStyles(background);

  // TODO: Refactor
  return (
    <div
      className={twMerge(
        "bg-ever-base w-full h-full flex flex-col items-center justify-center relative",
        isSlidePreview
          ? "px-10 py-8"
          : page_id === PAGE_TYPES.HEADER
          ? "px-6 py-5 h-auto"
          : page_id === PAGE_TYPES.WELCOME
          ? "p-0"
          : "px-28 py-20",
        className
      )}
      style={backgroundStyles}
    >
      <div
        className={twMerge(
          "w-full h-full flex flex-col",
          isSlidePreview
            ? "max-w-full"
            : isPreview
            ? "max-w-[1440px] w-full h-full justify-center"
            : "w-[1440px]"
        )}
      >
        {children}
      </div>
    </div>
  );
};

LayoutContainer.defaultProps = {
  data: { background: {}, page_id: "" },
  isPreview: false,
  isSlidePreview: false,
  children: null,
  className: "",
};

LayoutContainer.propTypes = {
  data: PropTypes.shape({
    background: PropTypes.object,
    page_id: PropTypes.string,
  }),
  isPreview: PropTypes.bool,
  isSlidePreview: PropTypes.bool,
  children: PropTypes.node,
  className: PropTypes.string,
};

export default LayoutContainer;
