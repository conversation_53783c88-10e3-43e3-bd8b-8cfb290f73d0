import {
  LinkedinIcon,
  Mail01Icon,
  PhoneIcon,
  User01Icon,
} from "@everstage/evericons/solid";
import PropTypes from "prop-types";
import { useState, useRef, useEffect } from "react";

import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles } from "~/v2/features/cpq/utils";

const RenderImage = ({ file }) => {
  const imageRef = useRef(null);
  const [isError, setIsError] = useState(false);
  const [imageHeight, setImageHeight] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      if (imageRef.current) {
        setImageHeight(imageRef.current.clientWidth);
      }
    };

    handleResize();

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [file, isError]);

  if (!file || isError)
    return (
      <div
        ref={imageRef}
        className="w-full object-cover rounded-2xl bg-ever-primary-lite flex items-center justify-center"
        style={{ height: imageHeight ? `${imageHeight}px` : "100%" }}
      >
        <User01Icon className="w-72 h-80 text-ever-base-400" />
      </div>
    );

  return (
    <img
      ref={imageRef}
      src={file.file_url}
      alt={file.file_name}
      className="w-full object-cover rounded-2xl"
      style={{ height: imageHeight ? `${imageHeight}px` : "100%" }}
      onError={() => setIsError(true)}
      onLoad={() => {
        if (imageRef.current) {
          setImageHeight(imageRef.current.clientWidth);
        }
      }}
    />
  );
};

/**
 * Member block component for displaying team members.
 * @param {Object} props
 * @param {Object} props.data - Member block data (title, description, members, background, text).
 */
const Member = ({ data }) => {
  if (!data) return null;

  const { title, description, members = [], background = {}, text = {} } = data;
  const textStyles = getTextStyles(text, background);

  return (
    <div className="flex flex-col justify-center gap-12">
      {/* Content */}
      <div className="flex flex-col gap-6">
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 w-full">
        {members.map((member, idx) => {
          const { name, role, file = {}, linkedin, email, phone } = member;
          return (
            <div
              key={idx}
              className="m-auto w-full flex flex-col items-center gap-4"
            >
              <RenderImage key={file.file_url} file={file} />
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col gap-2">
                  <Typography.Heading4 style={textStyles}>
                    {name}
                  </Typography.Heading4>
                  <Typography.TextBaseRegular style={textStyles}>
                    {role?.toUpperCase()}
                  </Typography.TextBaseRegular>
                </div>
                <div className="flex flex-row gap-5">
                  {linkedin && (
                    <a
                      href={linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <LinkedinIcon className="w-6 h-6 text-ever-base-content-low" />
                    </a>
                  )}
                  {email && (
                    <a href={`mailto:${email}`}>
                      <Mail01Icon className="w-6 h-6 text-ever-base-content-low" />
                    </a>
                  )}
                  {phone && (
                    <a href={`tel:${phone}`}>
                      <PhoneIcon className="w-6 h-6 text-ever-base-content-low" />
                    </a>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

Member.propTypes = {
  data: PropTypes.object,
};

export default Member;
