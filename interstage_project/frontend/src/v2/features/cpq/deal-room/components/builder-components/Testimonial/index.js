import PropTypes from "prop-types";

import TestimonialCarousel from "./TestimonialCarousel";
import TestimonialDefault from "./TestimonialDefault";

/**
 * Testimonial block component that renders either a carousel or default testimonial layout
 * based on the `variant` property in the data prop.
 *
 * @param {Object} props
 * @param {Object} props.data - Testimonial block data (should include a `variant` property).
 */
const Testimonial = (props) => {
  const { data } = props;

  switch (data?.variant) {
    case "carousel": {
      return <TestimonialCarousel {...props} />;
    }
    default: {
      return <TestimonialDefault {...props} />;
    }
  }
};

Testimonial.propTypes = {
  data: PropTypes.shape({
    variant: PropTypes.string,
  }),
};

Testimonial.defaultProps = {
  data: {},
};

export default Testimonial;
