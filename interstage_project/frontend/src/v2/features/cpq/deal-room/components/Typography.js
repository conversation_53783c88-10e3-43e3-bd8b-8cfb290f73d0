/**
 * Typography component system for Deal Room.
 * Provides consistent typography variants with proper styling and theming.
 *
 * @example
 * ```
 * import { Typography } from "./Typography";
 *
 * <Typography.Heading1>Main Title</Typography.Heading1>
 * <Typography.Heading2>Section Title</Typography.Heading2>
 * <Typography.TextBaseRegular>Body text content</Typography.TextBaseRegular>
 * ```
 */

import PropTypes from "prop-types";
import React from "react";
import { twMerge } from "tailwind-merge";

/**
 * Renders a level 1 heading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const Heading1 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-semibold !text-dealroom-4xl text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a level 2 heading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const Heading2 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-semibold !text-dealroom-3xl text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a level 3 heading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const Heading3 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-semibold !text-dealroom-2xl text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a level 4 heading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const Heading4 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-semibold !text-dealroom-xl text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a level 5 heading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const Heading5 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-semibold !text-dealroom-lg text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a level 4 subheading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const SubHeading4 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-medium !text-dealroom-xl text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a level 5 subheading with Lora font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the heading.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the heading component.
 */
const SubHeading5 = ({ children, className = "", ...props }) => (
  <div
    className={twMerge(
      "!font-dealroom-heading font-dealroom-medium !text-dealroom-lg text-dealroom-base-content m-0",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

/**
 * Renders a small regular text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextSMRegular = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-regular !text-base text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a small medium text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextSMMedium = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-medium !text-base text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a base regular text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextBaseRegular = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-regular !text-lg text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a base medium text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextBaseMedium = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-medium !text-lg text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a medium regular text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextMDRegular = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-regular !text-xl text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a medium medium text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextMDMedium = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-medium !text-xl text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a large regular text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextLGRegular = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-regular !text-2xl text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a large medium text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextLGMedium = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-medium !text-2xl text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a extra large regular text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextXLRegular = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-regular !text-3xl text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

/**
 * Renders a extra large medium text with IBM Plex Sans font and dealroom theme styling.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to display in the text.
 * @param {string} props.className - Additional CSS classes to apply.
 * @returns {React.ReactNode} The JSX for the text component.
 */
const TextXLMedium = ({ children, className = "", ...props }) => (
  <span
    className={twMerge(
      "!font-dealroom-body font-dealroom-medium !text-3xl text-dealroom-base-content-mid",
      className
    )}
    {...props}
  >
    {children}
  </span>
);

const typographyPropTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
};

const typographyDefaultProps = {
  className: "",
};

Heading1.propTypes = typographyPropTypes;
Heading2.propTypes = typographyPropTypes;
Heading3.propTypes = typographyPropTypes;
Heading4.propTypes = typographyPropTypes;
Heading5.propTypes = typographyPropTypes;
SubHeading4.propTypes = typographyPropTypes;
SubHeading5.propTypes = typographyPropTypes;
TextSMRegular.propTypes = typographyPropTypes;
TextSMMedium.propTypes = typographyPropTypes;
TextBaseRegular.propTypes = typographyPropTypes;
TextBaseMedium.propTypes = typographyPropTypes;
TextMDRegular.propTypes = typographyPropTypes;
TextMDMedium.propTypes = typographyPropTypes;
TextLGRegular.propTypes = typographyPropTypes;
TextLGMedium.propTypes = typographyPropTypes;
TextXLRegular.propTypes = typographyPropTypes;
TextXLMedium.propTypes = typographyPropTypes;

Heading1.defaultProps = typographyDefaultProps;
Heading2.defaultProps = typographyDefaultProps;
Heading3.defaultProps = typographyDefaultProps;
Heading4.defaultProps = typographyDefaultProps;
Heading5.defaultProps = typographyDefaultProps;
SubHeading4.defaultProps = typographyDefaultProps;
SubHeading5.defaultProps = typographyDefaultProps;
TextSMRegular.defaultProps = typographyDefaultProps;
TextSMMedium.defaultProps = typographyDefaultProps;
TextBaseRegular.defaultProps = typographyDefaultProps;
TextBaseMedium.defaultProps = typographyDefaultProps;
TextMDRegular.defaultProps = typographyDefaultProps;
TextMDMedium.defaultProps = typographyDefaultProps;
TextLGRegular.defaultProps = typographyDefaultProps;
TextLGMedium.defaultProps = typographyDefaultProps;
TextXLRegular.defaultProps = typographyDefaultProps;
TextXLMedium.defaultProps = typographyDefaultProps;

const Typography = {};
Typography.Heading1 = Heading1;
Typography.Heading2 = Heading2;
Typography.Heading3 = Heading3;
Typography.Heading4 = Heading4;
Typography.Heading5 = Heading5;
Typography.SubHeading4 = SubHeading4;
Typography.SubHeading5 = SubHeading5;
Typography.TextSMRegular = TextSMRegular;
Typography.TextSMMedium = TextSMMedium;
Typography.TextBaseRegular = TextBaseRegular;
Typography.TextBaseMedium = TextBaseMedium;
Typography.TextMDRegular = TextMDRegular;
Typography.TextMDMedium = TextMDMedium;
Typography.TextLGRegular = TextLGRegular;
Typography.TextLGMedium = TextLGMedium;
Typography.TextXLRegular = TextXLRegular;
Typography.TextXLMedium = TextXLMedium;

export { Typography };
export default Typography;
