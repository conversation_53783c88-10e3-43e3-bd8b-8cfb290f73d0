/**
 * Theme provider component for Deal Room styling.
 * Applies dealroom theme classes to the document body and manages theme lifecycle.
 *
 * @example
 * ```
 * <DealRoomThemeProvider>
 *   <LayoutContainer>
 *     <Typography.Heading1>Deal Room Content</Typography.Heading1>
 *   </LayoutContainer>
 * </DealRoomThemeProvider>
 * ```
 */

import PropTypes from "prop-types";
import React, { useEffect } from "react";

/**
 * Renders a theme provider that applies dealroom styling to the document body.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to render with theme applied.
 * @returns {React.ReactNode} The JSX for the theme provider component.
 */
const DealRoomThemeProvider = ({ children }) => {
  useEffect(() => {
    // Apply dealroom theme to body
    document.body.classList.add("dealroom");

    // Cleanup function to remove theme when component unmounts
    return () => {
      document.body.classList.remove("dealroom");
    };
  }, []);

  return <>{children}</>;
};

DealRoomThemeProvider.propTypes = {
  children: PropTypes.node,
};

export default DealRoomThemeProvider;
