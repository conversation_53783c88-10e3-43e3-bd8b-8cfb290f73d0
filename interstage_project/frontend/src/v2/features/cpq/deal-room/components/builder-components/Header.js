import PropTypes from "prop-types";
import { twMerge } from "tailwind-merge";

import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getBackgroundStyles, getTextStyles } from "~/v2/features/cpq/utils";

/**
 * Header component for displaying page navigation tabs in the Deal Room.
 *
 * @param {Object} props
 * @param {Object} props.data - Header block data (background, text).
 * @param {Object} props.pages - Pages object, keyed by page ID.
 * @param {Array} props.pagesOrder - Array of page IDs in order.
 * @param {string} [props.activePage] - Currently active page ID.
 * @param {boolean} [props.isPreview] - If true, show preview mode (hide empty pages).
 * @param {Function} [props.onPageChange] - Handler for page change. Receives (pageId, sectionId).
 */
const Header = ({
  data,
  pages,
  pagesOrder,
  activePage = "",
  isPreview = false,
  onPageChange = null,
}) => {
  const { background = {}, text = {} } = data;
  const textStyles = getTextStyles(text, background);
  const backgroundStyles = getBackgroundStyles(background);

  return (
    <div
      className="h-fit flex gap-6 items-center justify-center py-0.5"
      style={backgroundStyles}
    >
      {pagesOrder.map((pageId) => {
        const page = pages[pageId];
        if (isPreview && page.blocks_order.length === 0) {
          return null;
        }

        return (
          <Typography.TextBaseRegular
            key={pageId}
            style={textStyles}
            className={twMerge(
              "cursor-pointer",
              activePage === pageId ? "!font-medium" : ""
            )}
            onClick={(event) => {
              event.stopPropagation();
              const sectionId = page.blocks_order[0];
              onPageChange(pageId, sectionId ?? null);
            }}
          >
            {page.name}
          </Typography.TextBaseRegular>
        );
      })}
    </div>
  );
};

Header.propTypes = {
  data: PropTypes.object.isRequired,
  pages: PropTypes.object.isRequired,
  pagesOrder: PropTypes.array.isRequired,
  activePage: PropTypes.string,
  isPreview: PropTypes.bool,
  onPageChange: PropTypes.func,
};

Header.defaultProps = {
  activePage: "",
  isPreview: false,
  onPageChange: null,
};

export default Header;
