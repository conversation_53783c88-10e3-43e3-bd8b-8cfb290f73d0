import PropTypes from "prop-types";
import { twMerge } from "tailwind-merge";

import { EverButton } from "~/v2/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getBackgroundStyles, getTextStyles } from "~/v2/features/cpq/utils";

/**
 * Feature block component for displaying a feature section.
 * @param {Object} props
 * @param {Object} props.data - Feature block data (tagline, title, description, file, buttons, background, text).
 * @param {Function} [props.navigateInternal] - Optional navigation handler for internal links.
 */
const Feature = ({ data, navigateInternal = null }) => {
  if (!data) return null;

  const {
    tagline,
    title,
    description,
    feature = [],
    buttons = [],
    background = {},
    text = {},
  } = data;
  const textStyles = getTextStyles(text, background);
  const backgroundStyles = getBackgroundStyles(background);

  return (
    <div
      className="grid grid-cols-2 items-center justify-center gap-14"
      style={{
        ...backgroundStyles,
      }}
    >
      {/* Image Element */}
      {feature[0].file?.file_url && (
        <div
          className={twMerge(
            "flex flex-col items-center justify-center w-full h-full",
            feature[0].file.file_placement === "right"
              ? "order-last"
              : "order-first"
          )}
        >
          <img
            src={feature[0].file.file_url}
            alt={feature[0].file.file_name}
            className="object-contain w-full rounded-lg"
          />
        </div>
      )}

      <div className="flex flex-col items-start justify-center w-full gap-10">
        <div className="flex flex-col items-start justify-center gap-6">
          {/* Tagline Element */}
          {tagline && (
            <Typography.TextLGMedium style={textStyles}>
              {tagline.toUpperCase()}
            </Typography.TextLGMedium>
          )}

          <div className="flex flex-col items-start justify-center gap-4">
            {/* Title Element */}
            <Typography.Heading3 style={textStyles}>
              {title}
            </Typography.Heading3>

            {/* Description Element */}
            {description && (
              <Typography.TextLGRegular style={textStyles}>
                {description}
              </Typography.TextLGRegular>
            )}
          </div>
        </div>

        {/* Buttons Element */}
        <div className="flex flex-row gap-6 w-full">
          {buttons.map((button, idx) => (
            <>
              {button?.ctaText ? (
                <EverButton
                  key={idx}
                  type={button.type}
                  color={button.color}
                  size="large"
                  onClick={() => {
                    if (button.navigationType === "internal") {
                      if (button.link && typeof button.link === "string") {
                        const linkParts = button.link.split("#");
                        const pageId = linkParts[0] || "";
                        const sectionId = linkParts[1] || undefined;

                        if (typeof navigateInternal === "function") {
                          navigateInternal(pageId, sectionId);
                        }
                      }
                    } else {
                      window.open(button.link, "_blank", "noopener,noreferrer");
                    }
                  }}
                  href={button?.link}
                  className="!py-1"
                >
                  {button.ctaText}
                </EverButton>
              ) : null}
            </>
          ))}
        </div>
      </div>
    </div>
  );
};

Feature.propTypes = {
  data: PropTypes.object,
  navigateInternal: PropTypes.func,
};

Feature.defaultProps = {
  navigateInternal: null,
};

export default Feature;
