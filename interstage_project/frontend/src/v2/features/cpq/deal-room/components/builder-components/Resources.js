import { Globe02Icon, File06Icon, VideoIcon } from "@everstage/evericons/solid";
import PropTypes from "prop-types";
import { useState, useEffect } from "react";
import { useMutation } from "react-query";
import { twMerge } from "tailwind-merge";

import { EverModal } from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles } from "~/v2/features/cpq/utils";
import { useFetchApiWithAuth } from "~/v2/hooks";

// Utility function to convert video URLs to embed format
const getVideoEmbedUrl = (url) => {
  if (!url) return null;

  try {
    // For YouTube videos
    if (url.includes("youtube.com") || url.includes("youtu.be")) {
      const videoId = url.match(
        /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^\n#&?]+)/
      )?.[1];
      if (videoId) {
        return `https://www.youtube.com/embed/${videoId}`;
      }
    }
    // For Vimeo videos
    else if (url.includes("vimeo.com")) {
      const videoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
      if (videoId) {
        return `https://player.vimeo.com/video/${videoId}`;
      }
    }
    // For Loom videos
    else if (url.includes("loom.com")) {
      const videoId = url.match(/loom\.com\/share\/([^\n#&?]+)/)?.[1];
      if (videoId) {
        return `https://www.loom.com/embed/${videoId}`;
      }
    }
    // For direct video files, return as is
    else if (/\.(mp4|avi|mov|wmv|flv|webm)$/i.test(url)) {
      return url;
    }
  } catch (error) {
    console.error("Error converting video URL to embed format:", error);
  }

  return null;
};

// Component to fetch and display link previews
const LinkPreview = ({ url, title }) => {
  const { fetchData } = useFetchApiWithAuth();
  const [previewData, setPreviewData] = useState(null);
  const [isError, setIsError] = useState(false);

  // Extract domain from URL for display
  let domain = title;
  try {
    const urlObj = url ? new URL(url) : null;
    domain = urlObj?.hostname.replace("www.", "") ?? title;
  } catch (error) {
    console.error("Error extracting domain from URL:", error);
  }

  const { mutate: getLinkPreview, isLoading: isLinkPreviewLoading } =
    useMutation(
      (body) =>
        fetchData(`/ninja/dealroom/manage_rooms/link_preview`, "POST", body),
      {
        onSuccess: (data) => {
          setPreviewData({
            title: data?.title || domain,
            image: data?.image,
          });
        },
        onError: (error) => {
          console.error("Error fetching link preview:", error);
          setPreviewData({
            title: domain,
            image: null,
          });
        },
      }
    );

  useEffect(() => {
    if (url) {
      getLinkPreview({ url });
      setIsError(false);
    }
  }, [url, getLinkPreview]);

  return (
    <div
      className={twMerge(
        "w-full min-h-48 max-h-48 flex items-center justify-center border border-ever-base-300 rounded-lg overflow-hidden",
        previewData?.image && !isError ? "bg-white" : "bg-ever-base-200"
      )}
    >
      {isLinkPreviewLoading ? (
        <div className="animate-pulse text-ever-base-content-mid">
          Loading preview...
        </div>
      ) : previewData?.image && !isError ? (
        <img
          src={previewData.image}
          alt={previewData.title || title}
          className="w-full h-full object-contain"
          onError={() => setIsError(true)}
        />
      ) : (
        <Globe02Icon className="w-16 h-16 text-dealroom-chartColors-9/50" />
      )}
    </div>
  );
};

// Component to handle video previews
const VideoPreview = ({ url, title }) => {
  const [thumbnailUrl, setThumbnailUrl] = useState(null);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const getVideoThumbnail = async () => {
      if (!url) {
        setThumbnailUrl(null);
        return;
      }

      try {
        // For YouTube videos
        if (url.includes("youtube.com") || url.includes("youtu.be")) {
          const videoId = url.match(
            /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^\n#&?]+)/
          )?.[1];
          if (videoId) {
            setThumbnailUrl(
              `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
            );
          }
        }
        // For Vimeo videos
        else if (url.includes("vimeo.com")) {
          const videoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
          if (videoId) {
            const response = await fetch(
              `https://vimeo.com/api/oembed.json?url=https://vimeo.com/${videoId}`
            );

            if (!response.ok) {
              console.error(
                "Vimeo oembed API error:",
                response.status,
                response.statusText
              );
              setThumbnailUrl(url); // Fallback to original URL
              return;
            }

            const data = await response.json();
            setThumbnailUrl(data.thumbnail_url);
          }
        }
        // For Loom videos
        else if (url.includes("loom.com")) {
          const response = await fetch(
            `https://www.loom.com/v1/oembed?url=${encodeURIComponent(url)}`
          );

          if (!response.ok) {
            console.error(
              "Loom oembed API error:",
              response.status,
              response.statusText
            );
            setThumbnailUrl(url); // Fallback to original URL
            return;
          }

          const data = await response.json();
          setThumbnailUrl(data.thumbnail_url);
        } else {
          setThumbnailUrl(url);
        }
      } catch (error) {
        console.error("Error fetching video thumbnail:", error);
        setThumbnailUrl(url); // Fallback to original URL
        setIsError(true);
      }
    };

    setIsError(false);
    getVideoThumbnail();
  }, [url]);

  return (
    <div
      className={twMerge(
        "w-full min-h-48 max-h-48 flex items-center justify-center border border-ever-base-300 rounded-lg overflow-hidden relative",
        thumbnailUrl && !isError ? "bg-white" : "bg-ever-base-200"
      )}
    >
      {thumbnailUrl && !isError && (
        <img
          src={thumbnailUrl}
          alt={title}
          className="w-full h-full object-cover"
          onError={() => setIsError(true)}
        />
      )}
      <div className="absolute inset-0 flex items-center justify-center">
        <VideoIcon
          className={twMerge(
            "w-16 h-16",
            thumbnailUrl && !isError
              ? "text-ever-base-200"
              : "text-dealroom-chartColors-9/50"
          )}
        />
      </div>
    </div>
  );
};

const IMAGE_EXTENSIONS = new Set(["png", "jpg", "jpeg", "gif", "webp", "svg"]);
const VIDEO_EXTENSIONS = new Set(["mp4", "avi", "mov", "wmv", "flv", "webm"]);
const DOCUMENT_EXTENSIONS = new Set([
  "pdf",
  "doc",
  "docx",
  "txt",
  "csv",
  "xlsx",
  "xls",
  "ppt",
  "pptx",
]);

// Component to handle file previews
const FilePreview = ({ file, title }) => {
  const [previewUrl, setPreviewUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  const fileExtension =
    file?.file_extension || file?.file_name?.split(".").pop()?.toLowerCase();

  useEffect(() => {
    const generatePreview = async () => {
      if (!file?.file_url) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setIsError(false);

        // Handle different file types
        switch (true) {
          case IMAGE_EXTENSIONS.has(fileExtension): {
            // Image files can be displayed directly
            setPreviewUrl(file.file_url);
            break;
          }
          case VIDEO_EXTENSIONS.has(fileExtension): {
            // Video files can be displayed directly
            setPreviewUrl(file.file_url);
            break;
          }

          case fileExtension === "pdf": {
            // For PDFs, we can show them directly in iframe
            setPreviewUrl(file.file_url);
            break;
          }

          case DOCUMENT_EXTENSIONS.has(fileExtension): {
            // For Word documents, we'll show a download link in preview
            setPreviewUrl(null);
            break;
          }

          default: {
            // For other file types, show the file icon
            setPreviewUrl(null);
            break;
          }
        }
      } catch (error) {
        console.error("Error generating file preview:", error);
        setPreviewUrl(null);
        setIsError(true);
      } finally {
        setLoading(false);
      }
    };

    generatePreview();
  }, [file, fileExtension]);

  const renderPreview = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center w-full h-full bg-ever-base-200">
          <div className="animate-pulse text-ever-base-content-mid">
            Loading preview...
          </div>
        </div>
      );
    } else if (isError) {
      return (
        <div className="flex items-center justify-center w-full h-full bg-ever-base-200">
          <File06Icon className="w-16 h-16 text-dealroom-chartColors-9/50" />
        </div>
      );
    } else if (IMAGE_EXTENSIONS.has(fileExtension)) {
      return (
        <img
          src={previewUrl}
          alt={title}
          className="w-full h-full object-cover"
          onError={() => setIsError(true)}
        />
      );
    } else if (VIDEO_EXTENSIONS.has(fileExtension)) {
      return (
        <div className="relative w-full h-full">
          <video
            src={previewUrl}
            className="w-full h-full object-cover"
            controls={false}
            autoPlay={false}
            loop
            preload="metadata"
            onError={() => setIsError(true)}
          />
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <VideoIcon className="w-16 h-16 text-ever-base-content-low" />
          </div>
        </div>
      );
    } else if (fileExtension === "txt") {
      // For text files, use a simple iframe
      return (
        <iframe
          src={previewUrl}
          title={title}
          className="w-full h-full"
          onError={() => setIsError(true)}
        />
      );
    } else if (fileExtension === "pdf") {
      // For PDF files, use iframe
      return (
        <iframe
          src={previewUrl}
          title={title}
          className="w-full h-full"
          onError={() => setIsError(true)}
        />
      );
    } else {
      // For other file types (doc, xlsx, csv, ppt, etc.), show file icon
      return (
        <div className="flex items-center justify-center w-full h-full bg-ever-base-200">
          <File06Icon className="w-16 h-16 text-dealroom-chartColors-9/50" />
        </div>
      );
    }
  };

  // For other document types, show a preview card
  return (
    <div className="min-h-48 max-h-48 w-full h-full bg-white flex items-center justify-center border border-ever-base-300 rounded-lg overflow-hidden">
      {renderPreview()}
    </div>
  );
};

const ResourcePreview = ({ resource }) => {
  const { title, resource_type, file, button } = resource;

  // For different resource types, show appropriate previews
  switch (resource_type) {
    case "video": {
      return <VideoPreview url={button?.link} title={title} />;
    }
    case "web": {
      return <LinkPreview url={button?.link} title={title} />;
    }
    case "file": {
      return <FilePreview file={file} title={title} />;
    }
    default: {
      // Default fallback for unknown types
      return (
        <div className="min-w-80 min-h-48 max-h-48 bg-ever-base-200 flex items-center justify-center border border-ever-base-300 rounded-lg overflow-hidden">
          <Globe02Icon className="w-16 h-16 text-dealroom-chartColors-9/50" />
        </div>
      );
    }
  }
};

/**
 * Resources block component for displaying resource links, files, and videos.
 * @param {Object} props
 * @param {Object} props.data - Resources block data.
 * @param {Function} [props.navigateInternal] - Optional navigation handler for internal links.
 */
const Resources = ({ data, navigateInternal = null }) => {
  // State for modal management
  const [modalVisible, setModalVisible] = useState(false);
  const [modalContent, setModalContent] = useState(null);
  const [modalTitle, setModalTitle] = useState("");

  if (!data) return null;

  const {
    title,
    description,
    resources = [],
    background = {},
    text = {},
  } = data;
  const textStyles = getTextStyles(text, background);

  // Handle resource click based on type
  const handleResourceClick = (resource) => {
    const { resource_type, button, file, title } = resource;

    switch (resource_type) {
      case "web": {
        // Open link in new tab
        if (button?.link) {
          if (button.navigationType === "internal") {
            if (button.link && typeof button.link === "string") {
              const linkParts = button.link.split("#");
              const pageId = linkParts[0] || "";
              const sectionId = linkParts[1] || undefined;

              if (typeof navigateInternal === "function") {
                navigateInternal(pageId, sectionId);
              }
            }
          } else {
            window.open(button.link, "_blank", "noopener,noreferrer");
          }
        }
        break;
      }
      case "video": {
        // Open video in modal with iframe using embed URL
        if (button?.link) {
          const embedUrl = getVideoEmbedUrl(button.link);
          if (embedUrl) {
            setModalTitle(title || "Video");
            setModalContent(
              <iframe
                src={embedUrl}
                title={title || "Video"}
                className="w-full h-full min-h-[400px]"
                allowFullScreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              />
            );
            setModalVisible(true);
          } else {
            // Fallback: open in new tab if embed URL couldn't be generated
            window.open(button.link, "_blank", "noopener,noreferrer");
          }
        }
        break;
      }

      case "file": {
        if (file?.file_url) {
          const fileExtension =
            file?.file_extension ||
            file?.file_name?.split(".").pop()?.toLowerCase();

          if (IMAGE_EXTENSIONS.has(fileExtension)) {
            // Open image in modal with proper sizing
            setModalTitle(title || "Image Preview");
            setModalContent(
              <div className="w-full h-full flex items-center justify-center bg-ever-base-50 rounded-lg">
                <img
                  src={file.file_url}
                  alt={title || "Image"}
                  className="w-auto h-auto max-w-full max-h-full object-contain rounded-lg shadow-lg"
                />
              </div>
            );
            setModalVisible(true);
          } else if (VIDEO_EXTENSIONS.has(fileExtension)) {
            // Open video files in modal with video player
            setModalTitle(title || "Video Preview");
            setModalContent(
              <div className="w-full h-full flex items-center justify-center bg-ever-base-50 rounded-lg">
                <video
                  src={file.file_url}
                  className="w-auto h-auto max-w-full max-h-full object-contain rounded-lg shadow-lg"
                  controls
                  autoPlay={false}
                  loop
                />
              </div>
            );
            setModalVisible(true);
          } else if (fileExtension === "pdf") {
            // For PDF files, use iframe
            setModalTitle(title || "PDF Preview");
            setModalContent(
              <iframe
                src={file.file_url}
                title={title || "PDF"}
                className="w-full h-full min-h-[400px]"
              />
            );
            setModalVisible(true);
          } else if (fileExtension === "txt") {
            // For text files, use iframe
            setModalTitle(title || "Text Preview");
            setModalContent(
              <iframe
                src={file.file_url}
                title={title || "Text"}
                className="w-full h-full min-h-[400px]"
              />
            );
            setModalVisible(true);
          } else {
            // For other file types (doc, xlsx, csv, ppt, etc.), trigger download
            setModalTitle(title || "File Download");
            setModalContent(
              <div className="w-full h-full flex flex-col items-center justify-center bg-ever-base-50 rounded-lg gap-4">
                <File06Icon className="w-24 h-24 text-dealroom-chartColors-9/50" />
                <div className="text-center">
                  <p className="text-ever-base-content-mid mb-4">
                    This file type cannot be previewed. Click the button below
                    to download.
                  </p>
                  <a
                    href={file.file_url}
                    download={file.file_name}
                    className="inline-block px-6 py-3 bg-ever-primary text-white rounded-lg hover:bg-ever-primary-dark transition-colors"
                  >
                    Download {file.file_name}
                  </a>
                </div>
              </div>
            );
            setModalVisible(true);
          }
        }
        break;
      }
      default: {
        break;
      }
    }
  };

  return (
    <div className="flex flex-col justify-center gap-12">
      {/* Content */}
      <div className="flex flex-col gap-2">
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 w-full">
        {resources.map((resource, idx) => {
          const {
            title,
            description,
            resource_type = "",
            file_action_text = "",
            button = {},
          } = resource;

          return (
            <div
              key={idx}
              className="max-h-[448px] p-6 flex flex-col gap-5 bg-white rounded-2xl border border-ever-base-400 overflow-hidden transition-all duration-300 ease-in-out transform-gpu hover:scale-105 hover:shadow-lg"
            >
              <div
                className="cursor-pointer"
                onClick={() => handleResourceClick(resource)}
              >
                <ResourcePreview resource={resource} />
              </div>
              <div className="w-full flex flex-col items-start gap-4">
                <div className="flex flex-col items-start gap-3">
                  <Typography.Heading4>{title}</Typography.Heading4>
                  {description && (
                    <Typography.TextBaseRegular>
                      <TruncatedText
                        text={description}
                        className="text-lg w-full"
                        lines={4}
                      >
                        {description}
                      </TruncatedText>
                    </Typography.TextBaseRegular>
                  )}
                </div>
                {((resource_type === "file" && file_action_text) ||
                  (button && button.ctaText && button.link)) && (
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      handleResourceClick(resource);
                    }}
                    className="text-ever-primary text-2xl text-dealroom-body mt-auto"
                  >
                    {resource_type === "file"
                      ? file_action_text
                      : button.ctaText}
                  </a>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Modal for video and file previews */}
      <EverModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        title={modalTitle}
        width="90%"
        height="90%"
        destroyOnClose={true}
        bodyStyle={{
          height: `calc(100vh - 54px)`,
          maxHeight: "100%",
        }}
        className="p-0 [&_.ant-modal-content]:h-full [&_.ant-modal-content]:flex [&_.ant-modal-content]:flex-col"
      >
        <div className="w-full h-full">{modalContent}</div>
      </EverModal>
    </div>
  );
};

Resources.propTypes = {
  data: PropTypes.object,
  navigateInternal: PropTypes.func,
};

Resources.defaultProps = {
  navigateInternal: null,
};

export default Resources;
