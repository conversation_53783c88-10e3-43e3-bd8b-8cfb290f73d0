/**
 * Modal component for creating and editing deal room templates.
 * Provides a form interface for template name and description management.
 *
 * @example
 * ```
 * <CreateEditTemplateModal
 *   visible={isModalOpen}
 *   onClose={() => setIsModalOpen(false)}
 *   onSubmit={handleSaveTemplate}
 *   type="create"
 *   data={{ deal_room_template_name: "", deal_room_template_description: "" }}
 * />
 * ```
 */

import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import PropTypes from "prop-types";
import React from "react";
import { useForm, useWatch, Controller } from "react-hook-form";

import { EverModal, EverButton, EverTg, EverInput } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

/**
 * Renders a modal for creating or editing deal room templates.
 *
 * @param {Object} props - Component props.
 * @param {boolean} props.visible - Whether the modal is visible.
 * @param {Function} props.onClose - Function to call when modal is closed.
 * @param {Function} props.onSubmit - Function to call when form is submitted.
 * @param {boolean} props.loading - Whether the form is in loading state.
 * @param {string} props.type - Modal type: "create" or "edit".
 * @param {Object} props.data - Initial form data.
 * @returns {React.ReactNode} The JSX for the modal component.
 */
const CreateEditTemplateModal = ({
  visible,
  onClose,
  onSubmit,
  loading,
  type,
  data,
}) => {
  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      deal_room_template_name: data.deal_room_template_name,
      deal_room_template_description: data.deal_room_template_description,
    },
  });
  const formValues = useWatch({
    control,
    name: "deal_room_template_name",
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleSaveTemplate = ({
    deal_room_template_name,
    deal_room_template_description,
  }) => {
    onSubmit({
      deal_room_template_name,
      deal_room_template_description,
    });
  };

  return (
    <EverModal
      visible={visible}
      onCancel={!loading && handleClose}
      destroyOnClose
      width={640}
      bodyStyle={{ height: "554px", maxHeight: "80%" }}
    >
      <div className="flex flex-col items-center h-full py-9 px-24 gap-12 w-full">
        <div className="flex flex-col w-full gap-3 text-center">
          <EverTg.Heading1>
            {type === "create" ? "Create New Template" : "Edit Template"}
          </EverTg.Heading1>
        </div>
        <div className="flex flex-col gap-8 justify-between w-full h-full">
          <div className="flex flex-col gap-4">
            <LabeledField
              label="Name"
              className="gap-2"
              labelClassName="text-xs"
              error={errors.deal_room_template_name}
              required
            >
              <Controller
                control={control}
                name="deal_room_template_name"
                rules={{
                  required: "Template name is required",
                }}
                render={({ field }) => (
                  <EverInput {...field} placeholder="Enter template name" />
                )}
              />
            </LabeledField>
            <LabeledField
              label="Description"
              className="gap-2"
              labelClassName="text-xs"
            >
              <Controller
                control={control}
                name="deal_room_template_description"
                render={({ field }) => (
                  <EverInput.TextArea
                    {...field}
                    placeholder="Enter description"
                    autoSize={{ minRows: 2, maxRows: 2 }}
                  />
                )}
              />
            </LabeledField>
          </div>
          <EverButton
            appendIcon={<ArrowCircleRightIcon className="w-5 h-5" />}
            onClick={handleSubmit(handleSaveTemplate)}
            disabled={isEmpty(formValues)}
            loading={loading}
            className="mt-auto [&_.ant-btn-loading-icon]: flex [&_.ant-btn-loading-icon]: items-center"
          >
            {type === "create" ? "Create template" : "Save changes"}
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
};

CreateEditTemplateModal.defaultProps = {
  visible: false,
  onClose: () => {},
  onSubmit: () => {},
  loading: false,
  type: "create",
  data: {
    deal_room_template_name: "",
    deal_room_template_description: "",
  },
};

CreateEditTemplateModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  type: PropTypes.oneOf(["create", "edit"]),
  data: PropTypes.shape({
    deal_room_template_name: PropTypes.string,
    deal_room_template_description: PropTypes.string,
  }),
};

export default CreateEditTemplateModal;
