import PropTypes from "prop-types";

import { EverCard } from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles } from "~/v2/features/cpq/utils";

/**
 * Metrics block component for displaying metrics.
 * @param {Object} props
 * @param {Object} props.data - Metrics block data (title, description, metrics, background, text).
 */
const Metrics = ({ data }) => {
  if (!data) return null;

  const { title, description, metrics = [], background = {}, text = {} } = data;
  const textStyles = getTextStyles(text, background);

  return (
    <div className="flex flex-col justify-center gap-12">
      {/* Content */}
      <div className="flex flex-col gap-2 z-10" style={textStyles}>
        <Typography.Heading3 style={textStyles}>{title}</Typography.Heading3>
        {description && (
          <Typography.TextLGRegular style={textStyles}>
            {description}
          </Typography.TextLGRegular>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-10 w-full z-10">
        {metrics.map((metric, idx) => (
          <div key={idx}>
            <EverCard
              className="min-h-40 bg-ever-base px-8 py-3 flex flex-col gap-1 justify-center border border-ever-base-200 shadow-lg hover:scale-105"
              style={{ textAlign: text.text_alignment }}
            >
              <Typography.Heading2>{metric.metric_data}</Typography.Heading2>
              {metric.metric_description && (
                <div>
                  <Typography.TextLGMedium>
                    <TruncatedText
                      text={metric.metric_description}
                      className="w-full"
                      lines={2}
                    >
                      {metric.metric_description}
                    </TruncatedText>
                  </Typography.TextLGMedium>
                </div>
              )}
            </EverCard>
          </div>
        ))}
      </div>
    </div>
  );
};

Metrics.propTypes = {
  data: PropTypes.object,
};

export default Metrics;
