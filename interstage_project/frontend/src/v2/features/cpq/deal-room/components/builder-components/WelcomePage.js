import { isEmpty } from "lodash";
import PropTypes from "prop-types";
import { useEffect, useRef } from "react";

import { EverInput, EverButton, EverGroupAvatar } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles, getModifiedLogoUrl } from "~/v2/features/cpq/utils";

/**
 * Welcome page component for displaying the welcome screen.
 * @param {Object} props
 * @param {Object} props.data - Welcome page data (title, description, text).
 * @param {Object} props.clientInfo - Client info (logoUrl, name, metaInfo).
 * @param {Object} [props.inputProps] - Props for the input field.
 * @param {Object} [props.buttonProps] - Props for the button.
 */
const WelcomePage = ({
  data,
  clientInfo,
  inputProps = {},
  buttonProps = {},
}) => {
  const { logoUrl, name, metaInfo } = clientInfo;
  const modifiedLogoUrl = getModifiedLogoUrl(logoUrl, metaInfo);
  const inputRef = useRef(null);

  useEffect(() => {
    if (inputRef.current && !isEmpty(inputProps?.error)) {
      inputRef.current.focus();
    }
  }, [inputProps?.error]);

  if (!data) return null;

  const { title, description, text = {} } = data;
  const textStyles = getTextStyles(text);

  const handleEnterKey = async (e) => {
    if (
      e.key === "Enter" &&
      buttonProps &&
      typeof buttonProps["onClick"] === "function" &&
      !buttonProps["disabled"]
    ) {
      buttonProps["onClick"]();
    }
  };

  return (
    <div className="bg-ever-base h-full">
      <div className="relative bg-ever-base-50 h-full">
        <div className="flex flex-col gap-12 items-center justify-center h-full py-20 px-44">
          <EverGroupAvatar
            avatars={[
              {
                firstName: name,
                lastName: "",
                image: modifiedLogoUrl,
                shape: "square",
                className: "bg-transparent text-5xl !rounded-2xl",
              },
            ]}
            maxStyle={{ width: 100, height: 100 }}
          />
          <div className="flex flex-col gap-2 items-center w-11/12">
            <Typography.Heading3 className="text-center" style={textStyles}>
              {title}
            </Typography.Heading3>
            {description && (
              <Typography.TextLGRegular
                className="text-center text-ever-base-content-mid"
                style={textStyles}
              >
                {description}
              </Typography.TextLGRegular>
            )}
          </div>
          <div className="flex flex-col items-center gap-10">
            <div className="flex flex-col items-center gap-3">
              <Typography.TextLGRegular style={textStyles}>
                Enter your email to proceed
              </Typography.TextLGRegular>
              <div className="flex flex-col">
                <LabeledField label="" error={inputProps?.error}>
                  <EverInput
                    ref={inputRef}
                    placeholder="Enter your email"
                    className="w-96 !h-12 !text-lg"
                    onKeyDown={handleEnterKey}
                    {...inputProps}
                  />
                </LabeledField>
              </div>
            </div>
            <EverButton
              className="w-40 h-12 text-lg"
              size="large"
              {...buttonProps}
            >
              Proceed
            </EverButton>
          </div>
        </div>
      </div>
    </div>
  );
};

WelcomePage.propTypes = {
  data: PropTypes.object,
  clientInfo: PropTypes.object,
  inputProps: PropTypes.object,
  buttonProps: PropTypes.object,
};

WelcomePage.defaultProps = {
  inputProps: {},
  buttonProps: {},
};

export default WelcomePage;
