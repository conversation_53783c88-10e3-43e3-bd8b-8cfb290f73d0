/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import {
  ArrowCircleRightIcon,
  UploadCloudIcon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import { Upload, Dropdown, Menu } from "antd";
import { isEmpty } from "lodash";
import PropTypes from "prop-types";
import { useRef, useState, useEffect, useCallback } from "react";
import { useForm, useWatch, Controller } from "react-hook-form";
import { useMutation, useQuery } from "react-query";

import {
  EverModal,
  EverButton,
  EverTg,
  EverInput,
  EverSelect,
  LazySelect,
  IconButton,
  ClickBoundary,
} from "~/v2/components";
import { LabeledField, TruncatedText } from "~/v2/features/cpq/components";
import { useFetchApi, useDebouncedSearch } from "~/v2/features/cpq/hooks";
import { fetchBrandData } from "~/v2/features/cpq/utils";

import { API, API_ENDPOINTS } from "../deal-room-list/constants";

// Utility to check if any required field is empty or falsy
const isAnyRequiredFieldEmpty = (values) => {
  // Only check the first 4 fields (customer_logo is not required)
  const valueToValidate = values.slice(0, 4);
  return valueToValidate.some(
    (value) =>
      value === undefined ||
      value === null ||
      value === "" ||
      (typeof value === "object" && Object.keys(value).length === 0)
  );
};

/**
 * CreateEditDealRoomModal Component
 *
 * A modal component for creating new deal rooms or editing existing ones.
 * Provides form fields for deal room details including:
 * - Customer name
 * - Deal room template selection
 * - CRM opportunity linking
 * - Owner assignment
 * - Customer logo upload/search
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {Function} props.onClose - Callback function when modal is closed
 * @param {Function} props.onSubmit - Callback function when form is submitted
 * @param {boolean} props.loading - Whether the form is in loading state
 * @param {string} props.type - Modal type: "create" or "edit"
 * @param {string} props.buyersLogoUrl - URL of existing buyer's logo (for edit mode)
 * @param {Object} props.data - Initial form data (for edit mode)
 * @returns {JSX.Element} The modal component
 */
const CreateEditDealRoomModal = ({
  visible,
  onClose,
  onSubmit,
  loading,
  type,
  buyersLogoUrl,
  data,
}) => {
  // Refs for managing async operations
  const abortController = useRef(null); // For cancelling ongoing API requests
  const isSelectedOptionPushed = useRef(false); // Tracks if selected opportunity was manually added to options

  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  // State for managing form field behaviors
  const [disableDealRoomOpportunity, setDisableDealRoomOpportunity] =
    useState(false); // Disable opportunity field when only one option
  const [opportunityInitialLoad, setOpportunityInitialLoad] = useState(true); // Show loading state during initial opportunity fetch
  const [dealRoomTemplates, setDealRoomTemplates] = useState([]); // Available deal room templates
  const [disableDealRoomTemplate, setDisableDealRoomTemplate] = useState(false); // Disable template field when only one option
  const [accountDetails, setAccountDetails] = useState([]); // Available account owners

  // State for company search functionality
  const [isFocused, setIsFocused] = useState(false); // Controls visibility of company search dropdown
  const [companies, setCompanies] = useState([]); // Search results from BrandFetch API
  const [isCompanyLoading, setIsCompanyLoading] = useState(false); // Loading state for company search
  const [previewUrl, setPreviewUrl] = useState(buyersLogoUrl); // URL for logo preview

  /**
   * Fetches company data from BrandFetch API
   * Used for searching and displaying company logos
   * @param {Object} params - Search parameters
   * @param {string} params.searchTerm - The search term to query
   * @returns {Promise<Array>} Array of company objects with logo and details
   */
  const getCompanies = useCallback(async ({ searchTerm }) => {
    setIsCompanyLoading(true);
    try {
      const { response, data } = await fetchBrandData(searchTerm);

      if (!response.ok) {
        setCompanies([]);
        return [];
      }

      setCompanies(data);
      return data;
    } catch (error) {
      console.error("Error loading company data:", error);
      setCompanies([]);
      return [];
    } finally {
      setIsCompanyLoading(false);
    }
  }, []);

  // Debounced search hook for company search to avoid excessive API calls
  const {
    searchState: { userInput, searchQuery },
    onSearch,
    onReset,
  } = useDebouncedSearch(getCompanies);

  // React Hook Form setup with validation rules
  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
    setValue,
    getValues,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      deal_room_template_id: data.deal_room_template_id,
      deal_room_name: data.deal_room_name,
      deal_room_opportunity_id: data.deal_room_opportunity_id,
      deal_room_opportunity_name: data.deal_room_opportunity_name,
      account_id: data.deal_room_account_id,
      deal_room_owner: data.deal_room_owner,
      customer_logo: data.customer_logo ?? {},
    },
  });

  // Watch specific form fields to control button state and UI updates
  const formValues = useWatch({
    control,
    name: [
      "deal_room_name",
      "deal_room_template_id",
      "deal_room_opportunity_id",
      "deal_room_owner",
      "customer_logo",
    ],
  });

  /**
   * Handles removal of customer logo
   * Cleans up blob URLs to prevent memory leaks and resets form field
   */
  const handleRemove = () => {
    // Only revoke if it's a blob URL (starts with "blob:")
    if (previewUrl && previewUrl.startsWith("blob:")) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setValue("customer_logo", {
      type: "",
      name: null,
      url: null,
    });
  };

  /**
   * Configuration object for Ant Design Upload component
   * Handles file upload, validation, and preview generation
   */
  const uploadProps = {
    name: "file",
    accept: "image/*", // Only allow image files
    multiple: false, // Single file upload only
    showUploadList: false, // Hide default upload list, we handle preview manually
    onChange(info) {
      const { status } = info.file;
      if (status !== "removed") {
        // Convert file to base64 for preview
        let reader = new FileReader();
        reader.readAsDataURL(info.file);
        reader.addEventListener("load", function () {
          const fileUrl = reader.result;
          if (typeof fileUrl === "string") {
            setPreviewUrl(fileUrl);
            setValue("customer_logo", {
              type: "upload",
              name: info.file.name,
              file: info.file,
              url: fileUrl,
            });
          }
        });
      }
    },
    beforeUpload() {
      return false; // Prevent automatic upload, we handle it manually
    },
    onRemove: handleRemove,
  };

  /**
   * Handles modal close event
   * Resets form state and calls parent close handler
   */
  const handleClose = () => {
    if (previewUrl && previewUrl.startsWith("blob:")) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    reset();
    onClose();
  };

  /**
   * Handles form submission
   * Converts form data to FormData format for multipart upload
   * @param {Object} formData - Form data from react-hook-form
   */
  const handleSaveDealRoom = ({
    deal_room_name,
    deal_room_template_id,
    deal_room_opportunity_id,
    deal_room_opportunity_name,
    account_id,
    deal_room_owner,
    customer_logo,
  }) => {
    const formData = new FormData();

    // Handle customer logo data - separate file from metadata
    if (!isEmpty(customer_logo)) {
      const { file, ...rest } = customer_logo;
      formData.append("customer_logo", JSON.stringify(rest));
      if (file) {
        formData.append("FILES", file);
      }
    }

    // Append other form fields
    formData.append("deal_room_name", deal_room_name);
    formData.append("deal_room_template_id", deal_room_template_id);
    formData.append("deal_room_opportunity_id", deal_room_opportunity_id);
    formData.append("deal_room_opportunity_name", deal_room_opportunity_name);
    formData.append("account_id", account_id);
    formData.append("deal_room_owner", deal_room_owner);

    onSubmit(formData);
  };

  // Fetch deal room templates on component mount
  const { mutate: getAllTemplates, isLoading: getAllTemplatesLoading } =
    useMutation(
      () =>
        fetchEndpoint(API.GET_ALL_DEALROOM_TEMPLATES, {
          body: { sort_by: "name" },
        }),
      {
        onSuccess: (data) => {
          // Transform API response to select options format
          const options = data.deal_room_templates.map((item) => ({
            label: item.deal_room_template_name,
            value: item.deal_room_template_id,
            key: `${item.deal_room_template_name}##::##${item.deal_room_template_id}`,
          }));
          setDealRoomTemplates(options);

          // Auto-select if only one template available
          if (data.deal_room_templates.length === 1) {
            setValue("deal_room_template_id", options[0].value);
            setDisableDealRoomTemplate(true);
          } else {
            setDisableDealRoomTemplate(false);
          }
        },
        onError: () => {
          setDealRoomTemplates([]);
        },
      }
    );

  // Fetch account details for owner selection
  const { isLoading: getAccountDetailsLoading } = useQuery(
    [API.GET_ACCOUNT_DETAILS],
    () => fetchEndpoint(API.GET_ACCOUNT_DETAILS),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        // Transform employee data to select options format
        setAccountDetails(
          Object.entries(data.employee_details).map(([key, value]) => ({
            label: value.full_name,
            value: key,
            key: `${key}##::##${value.full_name}`,
          }))
        );
      },
      onError: () => {
        setAccountDetails([]);
      },
    }
  );

  /**
   * Fetches CRM opportunities for the lazy-loaded select component
   * Handles pagination, search, and maintains selected option visibility
   * @param {Object} params - Fetch parameters
   * @param {string} params.searchTerm - Search term for filtering opportunities
   * @param {number} params.offset - Pagination offset
   * @param {number} params.limit - Number of items to fetch
   * @param {Function} params.successCbk - Success callback
   * @param {Function} params.failureCbk - Failure callback
   */
  const fetchQuoteForms = async (params) => {
    const { searchTerm = null, offset, limit, successCbk, failureCbk } = params;

    // Cancel any ongoing requests
    abortController.current = new AbortController();
    const signal = abortController.current
      ? abortController.current.signal
      : undefined;

    const body = {
      limit_value: limit,
      search_term: searchTerm || "",
      offset_value: offset,
    };

    try {
      const res = await fetchEndpoint(API.GET_DEALROOM_OPPORTUNITY, {
        body,
        signal,
      });

      const {
        deal_room_opportunity_name,
        deal_room_opportunity_id,
        account_id,
      } = getValues();

      // Helper function to create option objects
      const createOptionObject = (name, id, account_id) => ({
        account_id,
        label: name,
        value: id,
        key: `${name}##::##${id}`,
      });

      const data = [];
      let selectedOppFound = false;

      // Process all opportunities from the API response
      for (const item of res.data) {
        // Add the item if it's not the selected option, OR if it's the selected option but we haven't added it manually yet
        if (
          item.opportunity_id !== deal_room_opportunity_id ||
          (item.opportunity_id === deal_room_opportunity_id &&
            !isSelectedOptionPushed.current)
        ) {
          data.push(
            createOptionObject(
              item.opportunity_name,
              item.opportunity_id,
              item.account_id
            )
          );
        }

        // Track if we found the selected option in the API response
        if (item.opportunity_id === deal_room_opportunity_id) {
          selectedOppFound = true;
          isSelectedOptionPushed.current = true;
        }
      }

      // Add the selected opportunity manually if it wasn't found in the current response
      // This ensures the selected option remains visible even if it's not in the current page
      if (
        !selectedOppFound &&
        !isSelectedOptionPushed.current &&
        deal_room_opportunity_id &&
        deal_room_opportunity_name
      ) {
        data.push(
          createOptionObject(
            deal_room_opportunity_name,
            deal_room_opportunity_id,
            account_id
          )
        );
        isSelectedOptionPushed.current = true;
      }

      // Auto-select if only one opportunity available (initial load, no search)
      if (offset === 0 && isEmpty(searchTerm) && data.length === 1) {
        setValue("deal_room_opportunity_id", data[0].value);
        setValue("deal_room_opportunity_name", data[0].label);
        setValue("account_id", data[0].account_id);
        setDisableDealRoomOpportunity(true);
        // Mark that the selected option has been processed to prevent duplicates
        isSelectedOptionPushed.current = true;
      } else {
        setDisableDealRoomOpportunity(false);
      }

      setOpportunityInitialLoad(false);
      successCbk(data, res.data);
    } catch {
      failureCbk();
    }
  };

  // Props for the LazySelect component
  const lazyLoadProps = {
    abort: () => {
      if (
        abortController.current &&
        typeof abortController.current.abort === "function"
      ) {
        abortController.current.abort();
      }
    },
    getOptions: async (params) => {
      await fetchQuoteForms(params);
    },
  };

  // Fetch templates when component mounts
  useEffect(() => {
    getAllTemplates();
  }, [getAllTemplates]);

  // Set initial preview URL when editing existing deal room
  useEffect(() => {
    if (visible && buyersLogoUrl) {
      setPreviewUrl(buyersLogoUrl);
    }
  }, [visible, buyersLogoUrl]);

  // Cleanup blob URLs on component unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      // Only revoke if it's a blob URL (starts with "blob:")
      if (previewUrl && previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Reset selected option tracking when modal closes
  useEffect(() => {
    if (!visible) {
      isSelectedOptionPushed.current = false;
    }
  }, [visible]);

  /**
   * Handles clearing of company search state
   * Resets focus, search results, and search input
   */
  const handleClearCompanySearch = () => {
    setIsFocused(false);
    setCompanies([]);
    onReset();
  };

  /**
   * Renders the company search dropdown menu
   * Shows loading state, search results, or no results message
   * @returns {JSX.Element} The dropdown menu component
   */
  const companySearchMenu = () => {
    // Don't show dropdown until minimum search length is met
    if (searchQuery.length < 3) {
      return <></>;
    }

    return (
      <Menu
        className="shadow-md bg-ever-base rounded-lg max-h-60 overflow-y-auto py-2"
        onClick={(event) => {
          event.domEvent.stopPropagation();
        }}
      >
        {isCompanyLoading ? (
          <Menu.Item key="loading">
            <EverTg.Text className="text-ever-base-content-mid flex items-center gap-2">
              <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
              Loading...
            </EverTg.Text>
          </Menu.Item>
        ) : companies.length > 0 ? (
          companies.map((company) => (
            <Menu.Item
              key={company.brandId}
              className="flex items-center gap-2 !h-14 !p-2 hover:bg-ever-base-200 rounded-md cursor-pointer"
              onClick={() => {
                // Set company logo and metadata
                setPreviewUrl(company.icon);
                setValue("customer_logo", {
                  type: "brand_fetch",
                  name: company.name,
                  url: company.domain,
                });
                handleClearCompanySearch();
              }}
            >
              <img
                className="w-10 h-10 rounded-md"
                src={company.icon}
                alt={company.name}
              />
              <div className="flex flex-col">
                <EverTg.Text>{company.name}</EverTg.Text>
                <EverTg.Caption className="text-ever-base-content-mid">
                  {company.domain}
                </EverTg.Caption>
              </div>
            </Menu.Item>
          ))
        ) : (
          <Menu.Item key="no-results">
            <EverTg.Text className="text-ever-base-content-mid">
              No results found
            </EverTg.Text>
          </Menu.Item>
        )}
      </Menu>
    );
  };

  return (
    <EverModal
      visible={visible}
      onCancel={loading ? null : handleClose}
      width={800}
      bodyStyle={{ maxHeight: "80%" }}
    >
      <div className="flex flex-col items-center h-full py-9 px-24 gap-12 w-full">
        {/* Modal Header */}
        <div className="flex flex-col w-full gap-3 text-center">
          <EverTg.Heading1>
            {type === "create" ? "Create New Deal Room" : "Edit Deal Room"}
          </EverTg.Heading1>
          <EverTg.Text className="text-ever-base-content-mid">
            Fill in the details below to get started.
          </EverTg.Text>
        </div>

        <div className="flex flex-col gap-8 justify-between w-full h-full">
          {/* Basic Details Section */}
          <div className="flex flex-col gap-6">
            <div className="flex items-center gap-4">
              <div className="w-full h-px bg-ever-base-400" />
              <EverTg.Text className="text-ever-base-content-mid font-medium whitespace-nowrap">
                BASIC DETAILS
              </EverTg.Text>
              <div className="w-full h-px bg-ever-base-400" />
            </div>

            {/* Form Fields Grid */}
            <div className="grid grid-cols-2 gap-4">
              {/* Customer Name Field */}
              <LabeledField
                label="Customer Name"
                className="gap-2"
                labelClassName="text-xs"
                error={errors.deal_room_name}
                required
              >
                <Controller
                  control={control}
                  name="deal_room_name"
                  rules={{
                    required: "Customer name is required",
                  }}
                  render={({ field }) => (
                    <EverInput {...field} placeholder="Enter customer name" />
                  )}
                />
              </LabeledField>

              {/* Deal Room Template Field */}
              <LabeledField
                label="Deal Room Template"
                className="gap-2"
                labelClassName="text-xs"
                required
                error={errors.deal_room_template_id}
              >
                <Controller
                  control={control}
                  name="deal_room_template_id"
                  rules={{
                    required: "Deal room template is required",
                  }}
                  render={({ field }) => (
                    <EverSelect
                      {...field}
                      placeholder="Select deal room template"
                      disabled={disableDealRoomTemplate || type === "edit"}
                      options={dealRoomTemplates}
                      {...(getAllTemplatesLoading && {
                        suffixIcon: (
                          <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
                        ),
                      })}
                    />
                  )}
                />
              </LabeledField>

              {/* CRM Opportunity Field */}
              <LabeledField
                label="CRM Opportunity"
                className="gap-2"
                labelClassName="text-xs"
                required
                error={errors.deal_room_opportunity_id}
              >
                <Controller
                  control={control}
                  name="deal_room_opportunity_id"
                  rules={{
                    required: "CRM opportunity is required",
                  }}
                  render={({ field }) => (
                    <LazySelect
                      {...field}
                      onChange={(value, option) => {
                        field.onChange(value);
                        // Also update the opportunity name and account ID
                        setValue("deal_room_opportunity_name", option.label);
                        setValue("account_id", option.account_id);
                      }}
                      value={opportunityInitialLoad ? null : field.value}
                      showSearch
                      limit={25}
                      size="medium"
                      className="w-full"
                      placeholder="Select a quote form"
                      disabled={disableDealRoomOpportunity}
                      {...(opportunityInitialLoad && {
                        suffixIcon: (
                          <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
                        ),
                      })}
                      {...lazyLoadProps}
                    />
                  )}
                />
              </LabeledField>

              {/* Owner Field */}
              <LabeledField
                label="Owner"
                className="gap-2"
                labelClassName="text-xs"
                required
                error={errors.deal_room_owner}
              >
                <Controller
                  control={control}
                  name="deal_room_owner"
                  rules={{
                    required: "Owner is required",
                  }}
                  render={({ field }) => (
                    <EverSelect
                      {...field}
                      placeholder="Select owner"
                      options={accountDetails}
                      {...(getAccountDetailsLoading && {
                        suffixIcon: (
                          <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
                        ),
                      })}
                    />
                  )}
                />
              </LabeledField>
            </div>
          </div>

          {/* Customer Logo Section */}
          <div className="flex flex-col gap-6">
            <div className="flex items-center gap-4">
              <div className="w-full h-px bg-ever-base-400" />
              <EverTg.Text className="text-ever-base-content-mid font-medium whitespace-nowrap">
                CUSTOMER LOGO
              </EverTg.Text>
              <div className="w-full h-px bg-ever-base-400" />
            </div>

            {/* Show logo preview if available, otherwise show upload/search options */}
            {previewUrl ? (
              <div className="h-10 flex items-center gap-2 border border-ever-base-300 rounded-lg py-2 px-3">
                <img
                  src={previewUrl}
                  alt="Uploaded"
                  className="w-5 h-5 object-cover"
                />
                <TruncatedText
                  text={formValues[4]?.name || ""}
                  className="text-ever-base-content"
                >
                  {formValues[4]?.name || ""}
                </TruncatedText>
                <IconButton
                  type="text"
                  color="base"
                  className="!w-5 !h-5 ml-auto rounded-sm cursor-pointer"
                  icon={
                    <Trash03Icon className="ml-auto text-ever-error-hover" />
                  }
                  onClick={handleRemove}
                />
              </div>
            ) : (
              <div className="flex items-center gap-4">
                {/* Company Search Option */}
                <div className="w-full">
                  <LabeledField label="Search buyer's company">
                    <div className="relative">
                      <ClickBoundary onClickOutside={handleClearCompanySearch}>
                        <Dropdown
                          visible={isFocused}
                          placement="topLeft"
                          trigger={[]}
                          overlay={companySearchMenu}
                          getPopupContainer={(trigger) => trigger.parentNode}
                        >
                          <div>
                            <EverInput.Search
                              placeholder="Search buyer's company"
                              value={userInput}
                              onChange={onSearch}
                              onFocus={() => setIsFocused(true)}
                            />
                          </div>
                        </Dropdown>
                      </ClickBoundary>
                    </div>
                  </LabeledField>
                </div>

                <EverTg.Heading4 className="text-ever-base-content-mid mt-5">
                  OR
                </EverTg.Heading4>

                {/* File Upload Option */}
                <div className="w-full">
                  <LabeledField label="Upload buyer's logo">
                    <Upload
                      {...uploadProps}
                      className="w-full [&_.ant-upload]:w-full"
                    >
                      <div className="flex items-center h-10 w-full gap-2 justify-center bg-ever-info-lite/80 border border-dashed border-ever-info rounded-lg px-3 py-1.5 cursor-pointer">
                        <UploadCloudIcon className="w-5 h-5 p-px text-ever-info" />
                        <EverTg.SubHeading4 className="text-ever-info">
                          Choose buyer&apos;s logo
                        </EverTg.SubHeading4>
                      </div>
                    </Upload>
                  </LabeledField>
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <EverButton
            appendIcon={<ArrowCircleRightIcon className="w-5 h-5" />}
            onClick={handleSubmit(handleSaveDealRoom)}
            disabled={isAnyRequiredFieldEmpty(formValues)} // Disable if any required field is empty
            loading={loading}
            className="mt-4 [&_.ant-btn-loading-icon]: flex [&_.ant-btn-loading-icon]: items-center"
          >
            {type === "create" ? "Create Deal Room" : "Save changes"}
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
};

CreateEditDealRoomModal.defaultProps = {
  loading: false,
  type: "create",
  buyersLogoUrl: "",
  data: {
    deal_room_template_id: null,
    deal_room_name: "",
    deal_room_opportunity_id: "",
    deal_room_opportunity_name: "",
    account_id: "",
    deal_room_owner: "",
    customer_logo: {
      type: "",
      name: "",
      file: null,
      url: "",
    },
  },
};

CreateEditDealRoomModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  type: PropTypes.oneOf(["create", "edit"]),
  buyersLogoUrl: PropTypes.string,
  data: PropTypes.shape({
    deal_room_template_id: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
    deal_room_name: PropTypes.string,
    deal_room_opportunity_id: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
    deal_room_opportunity_name: PropTypes.string,
    account_id: PropTypes.string,
    deal_room_owner: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    customer_logo: PropTypes.shape({
      type: PropTypes.string,
      name: PropTypes.string,
      file: PropTypes.object,
      url: PropTypes.string,
    }),
  }),
};

export default CreateEditDealRoomModal;
