import { BLOCK_TYPES } from "~/v2/features/cpq/deal-room/constants";

import FAQ from "./FAQ";
import Feature from "./Feature";
import Hero from "./Hero";
import Logo from "./Logo";
import Member from "./Member";
import Metrics from "./Metrics";
import Resources from "./Resources";
import Testimonial from "./Testimonial";

export { default as HeaderComponent } from "./Header";
export { default as WelcomePageComponent } from "./WelcomePage";
export const BUILDER_COMPONENTS = {
  [BLOCK_TYPES.LOGO]: Logo,
  [BLOCK_TYPES.HERO]: Hero,
  [BLOCK_TYPES.FEATURE]: Feature,
  [BLOCK_TYPES.FAQS]: FAQ,
  [BLOCK_TYPES.MEMBERS]: Member,
  [BLOCK_TYPES.METRICS]: Metrics,
  [BLOCK_TYPES.RESOURCES]: Resources,
  [BLOCK_TYPES.TESTIMONIAL]: Testimonial,
};
