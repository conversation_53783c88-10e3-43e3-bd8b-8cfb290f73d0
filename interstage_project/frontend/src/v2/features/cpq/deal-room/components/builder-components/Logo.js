import { get } from "lodash";
import PropTypes from "prop-types";
import { useState } from "react";

import { EverGroupAvatar } from "~/v2/components";
import { Typography } from "~/v2/features/cpq/deal-room/components/Typography";
import { getTextStyles, getModifiedLogoUrl } from "~/v2/features/cpq/utils";
import { handShake, logoPlaceholder } from "~/v2/images";

const BackgroundOverlay = ({ overlayColor }) => {
  return (
    <div
      className="absolute top-0 left-0 w-full h-full bg-dealroom-base/40 pointer-events-none z-10"
      style={{ backgroundColor: overlayColor }}
    />
  );
};

const BackgroundImage = ({ imageUrl }) => {
  const [onError, setOnError] = useState(false);

  if (onError) return null;

  return (
    <div className="absolute top-0 left-0 w-full h-full pointer-events-none z-10 blur-sm">
      <img
        src={imageUrl}
        alt=""
        className="object-cover bg-center w-full h-full"
        onError={() => setOnError(true)}
      />
    </div>
  );
};

/**
 * Logo block component for displaying client and partner logos.
 * @param {Object} props
 * @param {Object} props.data - Logo block data (title, description, background, text).
 * @param {Object} props.clientInfo - Client info (logoUrl, name, metaInfo).
 * @param {string} [props.buyersLogoUrl] - Optional buyer's logo URL.
 */
const Logo = ({ data, clientInfo, buyersLogoUrl }) => {
  const { logoUrl, name, metaInfo } = clientInfo;
  const modifiedLogoUrl = getModifiedLogoUrl(logoUrl, metaInfo);

  if (!data) return null;

  const { title, description, background = {}, text = {} } = data;
  const textStyles = getTextStyles(text, background);
  const imageUrl = get(background, "background_image.file_url", "");

  return (
    <>
      {imageUrl && (
        <>
          <BackgroundImage imageUrl={imageUrl} />
          <BackgroundOverlay overlayColor={background.overlay_color} />
        </>
      )}
      {/* Content */}
      <div className="flex flex-col items-center justify-center gap-12">
        {(title || description) && (
          <div className="flex flex-col gap-4 z-10 w-11/12">
            {/* Title Element */}
            {title && (
              <Typography.Heading1 className="text-center" style={textStyles}>
                {title}
              </Typography.Heading1>
            )}

            {/* Description Element */}
            {description && (
              <Typography.TextXLMedium
                className="text-center"
                style={textStyles}
              >
                {description}
              </Typography.TextXLMedium>
            )}
          </div>
        )}

        <div className="flex flex-row items-center justify-center gap-8 z-10">
          {/* Client Avatar Element */}
          <div>
            <EverGroupAvatar
              avatars={[
                {
                  firstName: name,
                  lastName: "",
                  image: modifiedLogoUrl,
                  shape: "square",
                  className: "bg-transparent text-5xl !rounded-2xl",
                },
              ]}
              maxStyle={{ width: 100, height: 100 }}
            />
          </div>

          {/* Handshake Icon */}
          <img src={handShake} alt="Handshake" className="w-16" />

          {/* Partner Avatar Element */}
          <div>
            <EverGroupAvatar
              avatars={[
                {
                  firstName: "Buyer's",
                  lastName: "Logo",
                  image: buyersLogoUrl || logoPlaceholder,
                  shape: "square",
                  className: "!border-0 text-5xl shadow-lg !rounded-2xl",
                },
              ]}
              maxStyle={{ width: 100, height: 100 }}
            />
          </div>
        </div>
      </div>
    </>
  );
};

Logo.propTypes = {
  data: PropTypes.object,
  clientInfo: PropTypes.object,
  buyersLogoUrl: PropTypes.string,
};

Logo.defaultProps = {
  buyersLogoUrl: "",
};

export default Logo;
