/**
 * Tests for Deal Room validation schemas.
 * Demonstrates how Valibot validation works for clone and delete operations.
 */

import { safeParse } from "valibot";

import {
  createDealRoomSchema,
  customerLogoSchema,
  cloneDealRoomSchema,
  deleteDealRoomSchema,
} from "./validation";

describe("Deal Room Validation", () => {
  describe("customerLogoSchema", () => {
    it("validates all fields present", () => {
      const payload = {
        type: "brand_fetch",
        name: "Logo",
        file: { some: "file" },
        url: "https://example.com/logo.png",
      };
      const result = safeParse(customerLogoSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    it("validates only required field", () => {
      const payload = { type: "upload" };
      const result = safeParse(customerLogoSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual({ type: "upload" });
    });

    it("accepts null for nullable fields", () => {
      const payload = {
        type: "brand_fetch",
        name: null,
        file: null,
        url: null,
      };
      const result = safeParse(customerLogoSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    it("rejects missing type", () => {
      const result = safeParse(customerLogoSchema, {});
      expect(result.success).toBe(false);
    });
  });

  describe("createDealRoomSchema", () => {
    it("validates minimal required fields", () => {
      const payload = {
        deal_room_name: "Room 1",
        deal_room_template_id: "tpl_1",
      };
      const result = safeParse(createDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual({
        ...payload,
        deal_room_opportunity_id: undefined,
        deal_room_opportunity_name: undefined,
        account_id: undefined,
        deal_room_owner: undefined,
        customer_logo: undefined,
      });
    });

    it("validates all fields present", () => {
      const payload = {
        deal_room_name: "Room 1",
        deal_room_template_id: "tpl_1",
        deal_room_opportunity_id: "opp_1",
        deal_room_opportunity_name: "Opportunity",
        account_id: "acc_1",
        deal_room_owner: "owner_1",
        customer_logo: {
          type: "main",
          name: "Logo",
          file: null,
          url: "https://example.com/logo.png",
        },
      };
      const result = safeParse(createDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    it("accepts nullable and optional fields as null", () => {
      const payload = {
        deal_room_name: "Room 1",
        deal_room_template_id: "tpl_1",
        deal_room_opportunity_id: null,
        deal_room_owner: null,
        customer_logo: {
          type: "main",
          name: null,
          file: null,
          url: null,
        },
      };
      const result = safeParse(createDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual({
        deal_room_name: "Room 1",
        deal_room_template_id: "tpl_1",
        deal_room_opportunity_id: null,
        deal_room_opportunity_name: undefined,
        account_id: undefined,
        deal_room_owner: null,
        customer_logo: {
          type: "main",
          name: null,
          file: null,
          url: null,
        },
      });
    });

    it("rejects missing deal_room_name", () => {
      const result = safeParse(createDealRoomSchema, {
        deal_room_template_id: "tpl_1",
      });
      expect(result.success).toBe(false);
      // The error message may vary depending on your schema, so check for the expected error
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });

    it("rejects empty deal_room_name", () => {
      const payload = { deal_room_name: "", deal_room_template_id: "tpl_1" };
      const result = safeParse(createDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Deal room name is required");
    });

    it("rejects missing deal_room_template_id", () => {
      const result = safeParse(createDealRoomSchema, {
        deal_room_name: "Room",
      });
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });

    it("rejects empty deal_room_template_id", () => {
      const payload = { deal_room_name: "Room 1", deal_room_template_id: "" };
      const result = safeParse(createDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Deal room template is required");
    });
  });
  describe("Clone Deal Room Validation", () => {
    test("should validate correct payload", () => {
      const payload = { deal_room_id: "35f8ba64-d0c0-4c25-8ee2-3e7acc1669a7" };
      const result = safeParse(cloneDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should reject empty deal_room_id", () => {
      const payload = { deal_room_id: "" };
      const result = safeParse(cloneDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Deal room ID is required");
    });

    test("should reject invalid deal_room_id format", () => {
      const payload = { deal_room_id: "dr@123" };
      const result = safeParse(cloneDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room ID must contain only letters, numbers, hyphens, and underscores"
      );
    });

    test("should reject missing deal_room_id", () => {
      const payload = {};
      const result = safeParse(cloneDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });
  });

  describe("Delete Deal Room Validation", () => {
    test("should validate correct payload", () => {
      const payload = { deal_room_id: "dr_456" };
      const result = safeParse(deleteDealRoomSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should reject empty deal_room_id", () => {
      const payload = { deal_room_id: "" };
      const result = safeParse(deleteDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Deal room ID is required");
    });

    test("should reject invalid deal_room_id format", () => {
      const payload = { deal_room_id: "dr#456" };
      const result = safeParse(deleteDealRoomSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room ID must contain only letters, numbers, hyphens, and underscores"
      );
    });

    it("should reject missing deal_room_id", () => {
      const result = safeParse(deleteDealRoomSchema, {});
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });
  });
});
