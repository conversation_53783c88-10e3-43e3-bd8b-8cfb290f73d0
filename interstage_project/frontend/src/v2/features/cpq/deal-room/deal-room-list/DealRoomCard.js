import { DotsVerticalIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { format } from "date-fns";
import PropTypes from "prop-types";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import {
  EverCard,
  EverTg,
  IconButton,
  EverButton,
  EverBadge,
  showToastMessage,
  EverGroupAvatar,
} from "~/v2/components";
import {
  RemoveConfirmationModal,
  TruncatedText,
} from "~/v2/features/cpq/components";
import {
  PUBLISH_STATUS_MENU_ITEMS,
  MORE_OPTIONS,
  MORE_MENU_ITEMS,
} from "~/v2/features/cpq/deal-room/constants";
import { fetchBrandData } from "~/v2/features/cpq/utils";
import { logoPlaceholder } from "~/v2/images";

import {
  dealRoomToastMessage,
  REMOVE_CONFIRMATION_MESSAGES,
} from "./constants";

/**
 * Fetches and displays the buyer's logo, using Brandfetch if needed.
 * @param {Object} props
 * @param {Object} props.customerLogo - The customer logo object with type and url.
 */
const RenderImage = ({ customerLogo }) => {
  const { type, url } = customerLogo;
  const [buyersLogoUrl, setBuyersLogoUrl] = useState("");

  const updateBrandFetchLogo = async ({ searchTerm }) => {
    try {
      const { response, data } = await fetchBrandData(searchTerm);

      if (!response.ok) {
        return; // Fail silently to not break the UI
      }

      if (Array.isArray(data) && data.length > 0) {
        setBuyersLogoUrl(data[0]?.icon);
      }
    } catch (error) {
      console.error("Error loading company logo:", error);
      // Fail silently to not break the UI - logo will remain as placeholder
    }
  };

  useEffect(() => {
    if (type === "brand_fetch") {
      updateBrandFetchLogo({ searchTerm: url });
    } else {
      setBuyersLogoUrl(url);
    }
  }, [type, url]);

  return (
    <EverGroupAvatar
      avatars={[
        {
          firstName: "Buyer's",
          lastName: "Logo",
          image: buyersLogoUrl || logoPlaceholder,
          shape: "square",
          className: "border-0 text-xl shadow !rounded-md",
        },
      ]}
      maxStyle={{ width: 40, height: 40 }}
    />
  );
};

RenderImage.propTypes = {
  customerLogo: PropTypes.object.isRequired,
};

RenderImage.defaultProps = {
  customerLogo: {},
};

/**
 * DealRoomCard displays a card for a single deal room, with actions and status.
 * @param {Object} props
 * @param {Object} props.data - Deal room data.
 * @param {Function} props.onClone - Handler for clone action.
 * @param {Function} props.onDelete - Handler for delete action.
 * @param {boolean} props.isLoading - Loading state for actions.
 */
const DealRoomCard = ({ data, onClone, onDelete, isLoading }) => {
  const navigate = useNavigate();
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);
  const status = data.deal_room_status.toLowerCase();

  /**
   * Returns badge props for the current deal room status.
   * @returns {Object}
   */
  const getBadgeProps = () => {
    const statusObj = PUBLISH_STATUS_MENU_ITEMS[status];

    return {
      type: statusObj.type,
      icon: statusObj.icon,
      title: statusObj.label,
    };
  };

  /**
   * Handles clicks on the more options menu (clone/delete).
   * @param {string} item
   */
  const handleMoreOptionsClick = (item) => {
    if (item === MORE_OPTIONS.CLONE) {
      const clonePromise = onClone({
        deal_room_id: data.deal_room_id,
      });
      showToastMessage(clonePromise, { messages: dealRoomToastMessage.clone });
    } else if (item === MORE_OPTIONS.DELETE) {
      setShowRemoveConfirmModal(true);
    }
  };

  /**
   * Handles the delete action for a deal room.
   */
  const handleDelete = () => {
    const deletePromise = onDelete({ deal_room_id: data.deal_room_id });
    showToastMessage(deletePromise, { messages: dealRoomToastMessage.delete });
  };

  const moreOptionsMenu = (
    <Menu
      className="flex items-start !p-2"
      onClick={(event) => {
        event.domEvent.stopPropagation();
      }}
    >
      {MORE_MENU_ITEMS.map((item, key) => (
        <Menu.Item
          className="!px-0 w-full hover:!bg-transparent !h-9"
          key={key}
        >
          <EverButton
            type="text"
            color={item.type === MORE_OPTIONS.DELETE ? "error" : "base"}
            size="small"
            className="w-full justify-start"
            prependIcon={item.icon}
            onClick={() => handleMoreOptionsClick(item.type)}
          >
            <EverTg.Caption
              className={twMerge(
                "font-normal",
                item.type === MORE_OPTIONS.DELETE
                  ? "text-ever-error-lite-content"
                  : ""
              )}
            >
              {item.label}
            </EverTg.Caption>
          </EverButton>
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <React.Fragment key={data.deal_room_id}>
      <EverCard
        className="flex flex-col gap-4 justify-between p-4 h-36"
        onClick={() => {
          navigate(`/cpq/deal-rooms/${data.deal_room_id}`);
        }}
        interactive
      >
        <div className="flex gap-3">
          <RenderImage customerLogo={data.customer_logo} />
          <div className="flex flex-col gap-[3px]">
            <TruncatedText
              text={data.deal_room_name}
              className="!text-lg font-medium"
            >
              {data.deal_room_name}
            </TruncatedText>
            {data.last_updated_at && (
              <EverTg.Text className="text-ever-base-content-mid">
                Last edited on{" "}
                {format(new Date(data.last_updated_at), "MMM dd, yyyy")}
              </EverTg.Text>
            )}
          </div>
          <Dropdown
            overlay={moreOptionsMenu}
            trigger={["click"]}
            onClick={(event) => {
              event.stopPropagation();
            }}
          >
            <IconButton
              type="text"
              color="base"
              size="small"
              className="!px-2 ml-auto"
              icon={
                <DotsVerticalIcon className="w-4 h-4 text-ever-base-content-mid" />
              }
            />
          </Dropdown>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <EverGroupAvatar
              avatars={[
                {
                  firstName: data.deal_room_owner_name,
                  lastName: "Logo",
                  image: data.deal_room_owner_profile_pic,
                },
              ]}
              maxStyle={{ width: 28, height: 28 }}
            />
            <EverTg.Text>{data.deal_room_owner_name}</EverTg.Text>
          </div>
          <EverBadge
            className="gap-1 text-xs font-medium"
            {...getBadgeProps()}
          />
        </div>
      </EverCard>
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title={REMOVE_CONFIRMATION_MESSAGES.title}
        subtitle={REMOVE_CONFIRMATION_MESSAGES.subtitle}
        onConfirm={handleDelete}
        onCancel={() => setShowRemoveConfirmModal(false)}
        loading={isLoading}
      />
    </React.Fragment>
  );
};

DealRoomCard.propTypes = {
  data: PropTypes.object.isRequired,
  onClone: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
};

DealRoomCard.defaultProps = {
  isLoading: false,
};

export default DealRoomCard;
