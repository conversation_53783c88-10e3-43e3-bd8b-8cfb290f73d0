const REMOVE_CONFIRMATION_MESSAGES = {
  title: "Are you sure to remove this deal room?",
  subtitle: "This action cannot be undone",
};

const dealRoomToastMessage = {
  create: {
    loading: "Creating deal room",
    error: (errorMsg) => errorMsg || "Unable to create deal room",
  },
  clone: {
    loading: "Cloning deal room",
    error: (errorMsg) => errorMsg || "Unable to clone deal room",
  },
  delete: {
    loading: "Deleting deal room",
    success: "Deal room has been deleted",
    error: (errorMsg) => errorMsg || "Unable to delete deal room",
  },
  syncWithTemplate: {
    loading: "Sync with template loading",
    success: "Deal room synced with template",
    error: (errorMsg) => errorMsg || "Unable to sync with template",
  },
};

const API = {
  GET_ALL_DEALROOM_TEMPLATES: "getAllDealRoomTemplates",
  GET_ALL_DEALROOMS: "getAllDealRooms",
  CREATE_DEALROOM: "createDealRoom",
  CLONE_DEALROOM: "cloneDealRoom",
  DELETE_DEALROOM: "deleteDealRoom",
  GET_DEALROOM_OPPORTUNITY: "getDealRoomOpportunity",
  GET_ACCOUNT_DETAILS: "getAccountDetails",
};

const API_ENDPOINTS = {
  [API.GET_ALL_DEALROOM_TEMPLATES]: {
    url: "/ninja/dealroom/manage_templates/get_all_templates",
    method: "POST",
  },
  [API.GET_DEALROOM_OPPORTUNITY]: {
    url: "/ninja/dealroom/manage_rooms/get_dealroom_opportunity",
    method: "POST",
  },
  [API.GET_ALL_DEALROOMS]: {
    url: "/ninja/dealroom/manage_rooms/get_all_dealrooms",
    method: "POST",
  },
  [API.CREATE_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/create_dealroom",
    method: "POST",
  },
  [API.CLONE_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/clone_dealroom",
    method: "POST",
  },
  [API.DELETE_DEALROOM]: {
    url: "/ninja/dealroom/manage_rooms/delete_dealroom",
    method: "POST",
  },
  [API.GET_ACCOUNT_DETAILS]: {
    url: "/ninja/cpq/quotes/account-details",
    method: "GET",
  },
};

export {
  dealRoomToastMessage,
  REMOVE_CONFIRMATION_MESSAGES,
  API,
  API_ENDPOINTS,
};
