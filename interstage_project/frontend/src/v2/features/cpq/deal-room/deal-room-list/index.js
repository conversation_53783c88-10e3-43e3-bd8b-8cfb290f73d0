import { Server01Icon } from "@everstage/evericons/duotone";
import {
  PlusCircleIcon,
  SearchIcon,
  ChevronDownIcon,
} from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { useMutation } from "react-query";
import { useNavigate } from "react-router-dom";

import {
  EverTg,
  EverButton,
  EmptyPagePlaceholder,
  EverLoader,
  message,
  EverInput,
  showToastMessage,
} from "~/v2/components";
import { CustomDropDown } from "~/v2/features/cpq/components";
import { CreateEditDealRoomModal } from "~/v2/features/cpq/deal-room/components";
import { PUBLISH_STATUS_MENU_ITEMS } from "~/v2/features/cpq/deal-room/constants";
import { useFetchApi, useDebouncedSearch } from "~/v2/features/cpq/hooks";
import { createQuoteRule, emptyBg, noQuotes } from "~/v2/images";

import { API_ENDPOINTS, API, dealRoomToastMessage } from "./constants";
import DealRoomCard from "./DealRoomCard";
import {
  cloneDealRoomSchema,
  deleteDealRoomSchema,
  createDealRoomSchema,
} from "./validation";

/**
 * DealRoomList displays a list of all deal rooms, with search, create, clone, and delete functionality.
 * @returns {JSX.Element}
 */
const DealRoomList = () => {
  const initialLoading = useRef(true);
  const navigate = useNavigate();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const [isCreateDealRoomModalOpen, setIsCreateDealRoomModalOpen] =
    useState(false);
  const [dealRooms, setDealRooms] = useState([]);
  const [statusFilter, setStatusFilter] = useState([]);

  const {
    data: { data = [] } = { data: [] },
    mutate: getAllDealRooms,
    isLoading: getDealRoomsLoading,
  } = useMutation(
    () =>
      fetchEndpoint(API.GET_ALL_DEALROOMS, {
        body: { sort_by: "name" },
      }),
    {
      onError: (error) => {
        setDealRooms([]);
        message.error(error.message);
      },
      onSettled: () => {
        initialLoading.current = false;
      },
    }
  );

  /**
   * Filters deal rooms based on the search term.
   * @param {Object} param0
   * @param {Object[]} param0.data
   * @param {string} param0.searchQuery
   * @param {string[]} param0.statusFilter
   */
  const getFilteredDealRooms = useCallback(
    ({ data, searchQuery = "", statusFilter = [] }) => {
      let filtered = data;

      // Filter by status
      if (statusFilter.length > 0) {
        filtered = filtered.filter((room) =>
          statusFilter.includes(room.deal_room_status?.toLowerCase())
        );
      }

      // Filter by search
      if (searchQuery.trim() !== "") {
        const lowerSearch = searchQuery.toLowerCase();
        filtered = filtered.filter(
          (room) =>
            room.deal_room_name?.toLowerCase().includes(lowerSearch) ||
            room.deal_room_opportunity_name
              ?.toLowerCase()
              .includes(lowerSearch) ||
            room.deal_room_owner?.toLowerCase().includes(lowerSearch)
        );
      }

      return filtered;
    },
    []
  );

  const {
    searchState: { userInput, searchQuery },
    onSearch,
  } = useDebouncedSearch();

  const handleStatusFilter = (selectedStatuses) => {
    setStatusFilter(selectedStatuses);
  };

  /**
   * Creates a new deal room.
   * @param {Object} body
   */
  const createDealRoom = useMutation(
    (body) =>
      fetchEndpoint(API.CREATE_DEALROOM, {
        body,
        validateSchema: createDealRoomSchema,
      }),
    {
      onSuccess: (data) => {
        setIsCreateDealRoomModalOpen(false);
        navigate(`/cpq/deal-rooms/${data.deal_room_id}`);
      },
    }
  );

  /**
   * Clones a deal room.
   * @param {Object} body
   */
  const cloneDealRoom = useMutation(
    (body) =>
      fetchEndpoint(API.CLONE_DEALROOM, {
        body,
        validateSchema: cloneDealRoomSchema,
      }),
    {
      onSuccess: (data) => {
        navigate(`/cpq/deal-rooms/${data.deal_room_id}`);
      },
    }
  );

  /**
   * Deletes a deal room.
   * @param {Object} body
   */
  const deleteDealRoom = useMutation(
    (body) =>
      fetchEndpoint(API.DELETE_DEALROOM, {
        body,
        validateSchema: deleteDealRoomSchema,
      }),
    { onSuccess: () => getAllDealRooms() }
  );

  /**
   * Returns the status options for the deal room.
   * @returns {Object[]}
   */
  const dealRoomStatusOptions = useMemo(() => {
    const statusCount = { draft: 0, published: 0 };
    if (data)
      for (const dealRoom of data) {
        const status = dealRoom.deal_room_status.toLowerCase();
        statusCount[status]++;
      }

    return Object.entries(statusCount).map(([status, count]) => {
      return {
        label: PUBLISH_STATUS_MENU_ITEMS[status].label,
        value: status,
        count: count,
      };
    });
  }, [data]);

  /**
   * Handles the creation of a new deal room.
   * @param {Object} formData
   */
  const handleCreateDealRoom = (formData) => {
    const createPromise = createDealRoom.mutateAsync(formData);
    showToastMessage(createPromise, { messages: dealRoomToastMessage.create });
  };

  useEffect(() => {
    getAllDealRooms();
  }, [getAllDealRooms]);

  useEffect(() => {
    if (data) {
      setDealRooms(getFilteredDealRooms({ data, searchQuery, statusFilter }));
    } else {
      setDealRooms([]);
    }
  }, [getFilteredDealRooms, data, searchQuery, statusFilter]);

  /**
   * Renders the main content of the deal room list, including loading, empty, and results states.
   * @returns {JSX.Element}
   */
  const renderContent = () => {
    if (initialLoading.current) {
      return (
        <EverLoader
          indicatorType="spinner"
          className="flex h-full"
          wrapperClassName="z-20"
          spinning
        />
      );
    }

    if (!getDealRoomsLoading && isEmpty(searchQuery) && isEmpty(dealRooms)) {
      return (
        <div className="h-full px-6 pt-3 pb-4">
          <EmptyPagePlaceholder
            title="It's time to create first deal room for your customer ⚡️"
            description="Create a personalised deal room - show case what services you offer, what others have to say about your services, etc that helps you win deals."
            image={createQuoteRule}
            buttonsList={[
              <EverButton
                size="small"
                type="outlined"
                color="primary"
                prependIcon={<PlusCircleIcon className="text-ever-primary" />}
                key="createNewDealRoom"
                onClick={() => setIsCreateDealRoomModalOpen(true)}
                className="!text-ever-primary"
              >
                Create Deal Room
              </EverButton>,
            ]}
          />
        </div>
      );
    }

    return (
      <EverLoader
        indicatorType="spinner"
        className="flex flex-col overflow-auto"
        wrapperClassName="z-20"
        spinning={
          getDealRoomsLoading ||
          createDealRoom.isLoading ||
          cloneDealRoom.isLoading ||
          deleteDealRoom.isLoading
        }
      >
        <div className="flex gap-2 justify-between border-b border-solid border-ever-base-300 px-6 pb-4">
          <EverInput.Search
            allowClear
            className="w-80 [&_.ant-input-affix-wrapper]:!px-3"
            placeholder="Search"
            value={userInput}
            onChange={onSearch}
            size="small"
            disabled={
              getDealRoomsLoading ||
              createDealRoom.isLoading ||
              cloneDealRoom.isLoading ||
              deleteDealRoom.isLoading
            }
            {...(isEmpty(userInput)
              ? {
                  enterButton: (
                    <SearchIcon className="w-4 h-4 text-ever-base-content-low" />
                  ),
                }
              : {
                  enterButton: null,
                })}
          />
          <div className="ml-auto">
            <CustomDropDown
              multi
              statusCount
              setHandlerState={handleStatusFilter}
              options={dealRoomStatusOptions}
            >
              <EverButton
                size="small"
                type="ghost"
                color="base"
                prependIcon={
                  <Server01Icon className="w-4 h-4 text-ever-base-content-mid shrink-0" />
                }
                appendIcon={
                  <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                }
              >
                <EverTg.Text className="text-base-content font-normal">
                  Status
                </EverTg.Text>
              </EverButton>
            </CustomDropDown>
          </div>
          <EverButton
            size="small"
            prependIcon={<PlusCircleIcon className="w-4 h-4" />}
            onClick={() => setIsCreateDealRoomModalOpen(true)}
            disabled={
              getDealRoomsLoading ||
              createDealRoom.isLoading ||
              cloneDealRoom.isLoading ||
              deleteDealRoom.isLoading
            }
          >
            Create Deal Room
          </EverButton>
        </div>
        {!isEmpty(searchQuery) && dealRooms.length === 0 ? (
          <div className="relative h-[calc(100vh-140px)] flex flex-col justify-center items-center my-4 mx-6">
            <img src={emptyBg} className="w-full absolute top-0 left-0" />
            <div className="flex flex-col items-center">
              <img src={noQuotes} className="mb-8" />
              <div className="flex flex-col gap-2 mb-5">
                <EverTg.Heading2 className="text-center">
                  No deal room found!
                </EverTg.Heading2>
                <EverTg.Text className="text-center">
                  We couldn’t find any deal room matching your search. Try
                  adjusting your filters or search terms.
                </EverTg.Text>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-[repeat(auto-fill,minmax(320px,_auto))] gap-4 px-6 py-4 overflow-auto">
            {dealRooms.map((dealRoom) => (
              <DealRoomCard
                key={dealRoom.deal_room_id}
                data={dealRoom}
                onClone={cloneDealRoom.mutateAsync}
                onDelete={deleteDealRoom.mutateAsync}
                isLoading={
                  getDealRoomsLoading ||
                  createDealRoom.isLoading ||
                  cloneDealRoom.isLoading ||
                  deleteDealRoom.isLoading
                }
              />
            ))}
          </div>
        )}
      </EverLoader>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="w-full h-full">{renderContent()}</div>
      <CreateEditDealRoomModal
        visible={isCreateDealRoomModalOpen}
        onClose={() => setIsCreateDealRoomModalOpen(false)}
        onSubmit={handleCreateDealRoom}
        loading={createDealRoom.isLoading}
      />
    </div>
  );
};

export default DealRoomList;
