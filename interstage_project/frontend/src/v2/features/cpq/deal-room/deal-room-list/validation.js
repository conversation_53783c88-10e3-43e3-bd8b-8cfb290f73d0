/**
 * Validation schemas for Deal Room operations using Valibot.
 * Provides type-safe validation for API payloads before making requests.
 */

import {
  object,
  string,
  pipe,
  minLength,
  optional,
  nullable,
  union,
  any,
  regex,
  number,
} from "valibot";

/**
 * Schema for cloning a deal room.
 * Validates that deal_room_id is a non-empty string.
 */
export const cloneDealRoomSchema = object({
  deal_room_id: pipe(
    string(),
    minLength(1, "Deal room ID is required"),
    regex(
      /^[\w-]+$/,
      "Deal room ID must contain only letters, numbers, hyphens, and underscores"
    )
  ),
});

/**
 * Schema for deleting a deal room.
 * Validates that deal_room_id is a non-empty string.
 */
export const deleteDealRoomSchema = object({
  deal_room_id: pipe(
    string(),
    minLength(1, "Deal room ID is required"),
    regex(
      /^[\w-]+$/,
      "Deal room ID must contain only letters, numbers, hyphens, and underscores"
    )
  ),
});

// Schema for customer logo object
export const customerLogoSchema = object({
  type: string(),
  name: optional(nullable(string())),
  file: optional(nullable(any())),
  url: optional(nullable(string())),
});

// Schema for creating a deal room
export const createDealRoomSchema = object({
  deal_room_name: pipe(string(), minLength(1, "Deal room name is required")),
  deal_room_template_id: pipe(
    union([string(), nullable(string())]),
    minLength(1, "Deal room template is required")
  ),
  deal_room_opportunity_id: optional(nullable(string())),
  deal_room_opportunity_name: optional(string()),
  account_id: optional(string()),
  deal_room_owner: optional(nullable(union([string(), number()]))),
  customer_logo: optional(customerLogoSchema),
});
