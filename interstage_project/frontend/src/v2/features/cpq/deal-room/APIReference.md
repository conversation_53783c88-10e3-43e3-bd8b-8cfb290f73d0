# Deal Room API Reference

## Overview

The Deal Room API provides RESTful endpoints for managing deal rooms, templates, and related resources. All endpoints require authentication via <PERSON><PERSON> token and return JSON responses with standardized error handling.

## Base Configuration

```javascript
const BASE_URL = "/ninja/dealroom";
const TEMPLATE_BASE = "/manage_templates";
const DEALROOM_BASE = "/manage_rooms";
```

## Authentication

All API calls require a valid authentication token in the Authorization header:

```http
Authorization: Bearer {access_token}
```

## Deal Room Management APIs

### Create Deal Room

Creates a new deal room instance.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/create_dealroom`

**Request Format:** `multipart/form-data`

```javascript
// Form Data Fields
deal_room_name: string; // Required
deal_room_template_id: string; // Required
deal_room_opportunity_id: string; // Optional
deal_room_opportunity_name: string; // Optional
account_id: string; // Optional
deal_room_owner: string; // Required
customer_logo: string; // JSON stringified object
FILES: File; // Optional - customer logo file
```

**Request Example:**

```javascript
const formData = new FormData();
formData.append("deal_room_name", "Acme Corp Deal Room");
formData.append("deal_room_template_id", "template_123");
formData.append("deal_room_owner", "user_456");
formData.append(
  "customer_logo",
  JSON.stringify({
    type: "file",
    name: "acme-logo.png",
    url: null,
  })
);
formData.append("FILES", logoFile);
```

**Response:**

```json
{
  "status": "success",
  "deal_room_id": "dr_789",
  "message": "Deal room created successfully"
}
```

### Get Deal Room

Retrieves complete deal room data including page specifications.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/get_dealroom`

**Request:**

```json
{
  "deal_room_id": "dr_789"
}
```

**Response:**

```json
{
  "deal_room_id": "dr_789",
  "deal_room_name": "Acme Corp Deal Room",
  "deal_room_status": "draft",
  "deal_room_template_id": "template_123",
  "deal_room_opportunity_id": "opp_456",
  "deal_room_opportunity_name": "Acme Q1 Deal",
  "deal_room_owner": "John Smith",
  "customer_logo": {
    "type": "file",
    "name": "acme-logo.png",
    "url": "https://cdn.example.com/logos/acme-logo.png"
  },
  "deal_room_page_spec": {
    "pages_order": ["welcome_page", "page_001"],
    "pages": {
      "welcome_page": {
        "blocks_order": ["logo_001", "hero_001"],
        "blocks": {
          "logo_001": {
            "type": "logo",
            "title": "Welcome to Acme Corp",
            "background": {
              "background_color": "#ffffff"
            }
          }
        }
      }
    }
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T14:22:00Z"
}
```

### Update Deal Room

Updates deal room properties or metadata.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/update_dealroom`

**Request Format:** `multipart/form-data` or `application/json`

```javascript
// For basic updates (JSON)
{
  "deal_room_id": "dr_789",
  "deal_room_name": "Updated Deal Room Name",
  "deal_room_status": "published"
}

// For file uploads (FormData)
const formData = new FormData();
formData.append('deal_room_id', 'dr_789');
formData.append('customer_logo', JSON.stringify(logoData));
formData.append('FILES', newLogoFile);
```

### Get All Deal Rooms

Retrieves list of deal rooms with sorting and filtering options.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/get_all_dealrooms`

**Request:**

```json
{
  "sort_by": "name",
  "filter": {
    "status": "published",
    "owner": "user_456"
  },
  "limit": 50,
  "offset": 0
}
```

**Response:**

```json
{
  "data": [
    {
      "deal_room_id": "dr_789",
      "deal_room_name": "Acme Corp Deal Room",
      "deal_room_status": "published",
      "deal_room_owner": "John Smith",
      "deal_room_opportunity_name": "Acme Q1 Deal",
      "customer_logo": {
        "type": "file",
        "url": "https://cdn.example.com/logos/acme-logo.png"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "last_updated_at": "2024-01-15T14:22:00Z"
    }
  ],
  "total_count": 25,
  "has_more": false
}
```

### Clone Deal Room

Creates a copy of an existing deal room.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/clone_dealroom`

**Request:**

```json
{
  "deal_room_id": "dr_789"
}
```

**Response:**

```json
{
  "status": "success",
  "deal_room_id": "dr_890",
  "message": "Deal room cloned successfully"
}
```

### Delete Deal Room

Permanently deletes a deal room and all associated data.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/delete_dealroom`

**Request:**

```json
{
  "deal_room_id": "dr_789"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Deal room deleted successfully"
}
```

## Page and Block Management APIs

### Update Deal Room Pages

Updates the overall page structure and ordering.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/update_pages`

**Request:**

```json
{
  "deal_room_id": "dr_789",
  "type": "add_page",
  "deal_room_page_spec": {
    "pages_order": ["welcome_page", "page_001", "page_002"],
    "pages": {
      "page_002": {
        "blocks_order": [],
        "blocks": {}
      }
    }
  }
}
```

### Update Single Page

Updates a specific page's configuration.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/update_page`

**Request:**

```json
{
  "deal_room_id": "dr_789",
  "page_id": "page_001",
  "page_data": {
    "blocks_order": ["hero_001", "feature_001"],
    "blocks": {
      "feature_001": {
        "type": "feature",
        "title": "Key Feature",
        "description": "Feature description"
      }
    }
  }
}
```

### Update Block

Updates a specific block's properties.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/update_block`

**Request Format:** `multipart/form-data`

```javascript
const formData = new FormData();
formData.append("deal_room_id", "dr_789");
formData.append("page_id", "page_001");
formData.append("block_id", "hero_001");
formData.append(
  "block_data",
  JSON.stringify({
    type: "hero",
    title: "Welcome to Our Solution",
    description: "Discover how we can help your business grow",
  })
);
formData.append("FILES", heroImageFile);
```

### Sync with Template

Synchronizes a deal room with its template to inherit latest changes.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/sync_dealroom_with_template`

**Request:**

```json
{
  "deal_room_id": "dr_789"
}
```

## Template Management APIs

### Create Template

Creates a new deal room template.

**Endpoint:** `POST /ninja/dealroom/manage_templates/create_template`

**Request:**

```json
{
  "deal_room_template_name": "Standard Sales Template",
  "deal_room_template_description": "Template for standard sales presentations",
  "status": "active"
}
```

### Get Template

Retrieves template data and structure.

**Endpoint:** `POST /ninja/dealroom/manage_templates/get_template`

**Request:**

```json
{
  "deal_room_template_id": "template_123"
}
```

### Get All Templates

Lists all available templates with metadata.

**Endpoint:** `POST /ninja/dealroom/manage_templates/get_all_templates`

**Request:**

```json
{
  "sort_by": "name",
  "status": "active"
}
```

**Response:**

```json
{
  "deal_room_templates": [
    {
      "deal_room_template_id": "template_123",
      "deal_room_template_name": "Standard Sales Template",
      "deal_room_template_description": "Template description",
      "status": "active",
      "created_at": "2024-01-10T09:00:00Z",
      "updated_at": "2024-01-12T11:30:00Z"
    }
  ]
}
```

## CRM Integration APIs

### Get Deal Room Opportunity

Searches CRM opportunities for deal room linkage.

**Endpoint:** `POST /ninja/dealroom/manage_rooms/get_dealroom_opportunity`

**Request:**

```json
{
  "search_term": "Acme",
  "limit_value": 20,
  "offset_value": 0
}
```

**Response:**

```json
{
  "data": [
    {
      "opportunity_id": "opp_456",
      "opportunity_name": "Acme Corp Q1 Deal",
      "account_id": "acc_789",
      "account_name": "Acme Corporation",
      "owner": "John Smith",
      "stage": "Proposal",
      "value": 50000
    }
  ],
  "total_count": 15,
  "has_more": true
}
```

### Get Account Details

Retrieves account owner information for deal room assignment.

**Endpoint:** `GET /ninja/cpq/quotes/account-details`

**Response:**

```json
{
  "employee_details": {
    "user_456": {
      "full_name": "John Smith",
      "email": "<EMAIL>",
      "role": "Senior Sales Rep"
    },
    "user_789": {
      "full_name": "Jane Doe",
      "email": "<EMAIL>",
      "role": "Account Manager"
    }
  }
}
```

## Public Link APIs

### Get Deal Room Data (Public)

Retrieves deal room content for public access.

**Endpoint:** `POST /public/get_dealroom`

**Headers:**

```http
Authorization: Bearer {public_access_token}
```

**Response:**

```json
{
  "deal_room_id": "dr_789",
  "deal_room_name": "Acme Corp Presentation",
  "customer_logo": {
    "type": "brand_fetch",
    "url": "acme-corp"
  },
  "deal_room_page_spec": {
    // Full page specification
  },
  "public_access": true
}
```

### Get Welcome Page (Public)

Retrieves welcome page content for public landing.

**Endpoint:** `POST /public/get_dealroom_welcome_page`

**Headers:**

```http
Authorization: Bearer {public_access_token}
```

### Validate Public Access

Validates email access for public deal room.

**Endpoint:** `POST /public/validate_access`

**Request:**

```json
{
  "public_token": "pub_token_123",
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "isValid": true,
  "message": "Access granted"
}
```

## File Upload APIs

### Upload Files

Handles file uploads for deal room assets.

**Endpoint:** `POST /ninja/dealroom/upload`

**Request Format:** `multipart/form-data`

```javascript
const formData = new FormData();
formData.append("file", fileObject);
formData.append("file_type", "image"); // image, video, document
formData.append("context", "deal_room");
```

**Response:**

```json
{
  "file_url": "https://cdn.example.com/uploads/file_123.jpg",
  "file_name": "uploaded_image.jpg",
  "file_type": "image",
  "file_size": 1024000,
  "file_id": "file_123"
}
```

## External Integration APIs

### BrandFetch Integration

The system integrates with BrandFetch API for automatic logo fetching:

**Endpoint:** `https://api.brandfetch.io/v2/search/{company_name}`

**Query Parameters:**

```
?c=1idce0oQGHTnX120CcV  // API key
```

**Response:**

```json
[
  {
    "name": "Acme Corporation",
    "domain": "acme.com",
    "icon": "https://asset.brandfetch.io/logo.svg",
    "brandName": "Acme Corp"
  }
]
```

## Error Handling

### Standard Error Response

All API endpoints return consistent error responses:

```json
{
  "status": "error",
  "error_code": "VALIDATION_FAILED",
  "message": "Deal room name is required",
  "details": {
    "field": "deal_room_name",
    "validation_rule": "required"
  }
}
```

### Common Error Codes

| Code                    | Description               | HTTP Status |
| ----------------------- | ------------------------- | ----------- |
| `VALIDATION_FAILED`     | Request validation error  | 400         |
| `UNAUTHORIZED`          | Authentication failed     | 401         |
| `FORBIDDEN`             | Insufficient permissions  | 403         |
| `NOT_FOUND`             | Resource not found        | 404         |
| `FILE_TOO_LARGE`        | Upload file size exceeded | 413         |
| `UNSUPPORTED_FILE_TYPE` | Invalid file format       | 415         |
| `RATE_LIMITED`          | Too many requests         | 429         |
| `INTERNAL_ERROR`        | Server error              | 500         |

### Error Handling Best Practices

1. **Retry Logic**: Implement exponential backoff for temporary failures
2. **Validation**: Validate inputs client-side before API calls
3. **User Feedback**: Provide clear error messages to users
4. **Logging**: Log errors for debugging and monitoring

## Rate Limiting

API endpoints have rate limits to prevent abuse:

- **Deal Room Operations**: 100 requests per minute
- **File Uploads**: 20 uploads per minute
- **Search Operations**: 50 requests per minute
- **Public Access**: 200 requests per minute per IP

## Caching Strategy

### Client-Side Caching

- **Deal Room Data**: Cache for 5 minutes
- **Template List**: Cache for 15 minutes
- **Account Details**: Cache for 30 minutes
- **File URLs**: Cache indefinitely (immutable)

### Cache Headers

```http
Cache-Control: private, max-age=300
ETag: "deal_room_version_123"
Last-Modified: Tue, 15 Jan 2024 14:22:00 GMT
```

## API Versioning

Future API versions will maintain backward compatibility:

```http
Accept: application/vnd.everstage.v2+json
```

## Development and Testing

### Postman Collection

A Postman collection is available with sample requests for all endpoints:

```bash
# Import collection
curl -o dealroom-api.postman_collection.json \
  https://api.example.com/docs/postman/dealroom-collection
```

### Mock Data

Development environments include mock data generators:

```javascript
// Generate test deal room
POST /dev/mock/deal-room
{
  "template": "standard",
  "blocks": ["hero", "feature", "members"]
}
```

---

This API reference provides comprehensive coverage of all deal room endpoints with examples and error handling guidelines for robust integration.
