import {
  DotsVerticalIcon,
  ChevronDownIcon,
  CheckIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import PropTypes from "prop-types";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import {
  EverCard,
  EverTg,
  IconButton,
  EverTooltip,
  EverBadge,
  EverButton,
  showToastMessage,
} from "~/v2/components";
import {
  RemoveConfirmationModal,
  TruncatedText,
} from "~/v2/features/cpq/components";
import {
  STATUS_OPTIONS,
  ACTIVE_STATUS_MENU_ITEMS,
  MORE_OPTIONS,
  MORE_MENU_ITEMS,
} from "~/v2/features/cpq/deal-room/constants";

import {
  templateToastMessage,
  REMOVE_CONFIRMATION_MESSAGES,
} from "./constants";

/**
 * TemplateCard displays a card for a single deal room template, with actions for status, clone, and delete.
 * @param {Object} props
 * @param {Object} props.data - Template data.
 * @param {Function} props.onClone - Handler for clone action.
 * @param {Function} props.onUpdate - Handler for status update action.
 * @param {Function} props.onDelete - Handler for delete action.
 * @param {boolean} props.isLoading - Loading state for actions.
 * @returns {JSX.Element}
 */
const TemplateCard = ({ data, onClone, onUpdate, onDelete, isLoading }) => {
  const navigate = useNavigate();
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);
  const status = data.deal_room_template_status.toLowerCase();

  /**
   * Returns badge props for the current template status.
   * @returns {Object}
   */
  const getBadgeProps = () => {
    const statusObj = ACTIVE_STATUS_MENU_ITEMS[status];
    return {
      type: statusObj.type,
      icon: statusObj.icon,
      title: (
        <div className="flex items-center gap-1">
          <span>{statusObj.label}</span>
          <ChevronDownIcon
            className={twMerge(
              "w-4 h-4",
              statusObj.value === STATUS_OPTIONS.ACTIVE
                ? "text-ever-success-lite-content"
                : "text-ever-base-content-mid"
            )}
          />
        </div>
      ),
    };
  };

  /**
   * Handles status change from the dropdown menu.
   * @param {Object} event - The Ant Design menu event.
   */
  const handleStatusChange = (event) => {
    event.domEvent.stopPropagation();
    if (event.key === status) {
      return;
    }
    // Call update and show toast for status change
    const updatePromise = onUpdate({
      deal_room_template_id: data.deal_room_template_id,
      deal_room_template_status: event.key,
    });
    showToastMessage(updatePromise, {
      messages: templateToastMessage.update({ status: event.key }),
    });
  };

  /**
   * Handles clicks on the more options menu (clone/delete).
   * @param {string} item
   */
  const handleMoreOptionsClick = (item) => {
    if (item === MORE_OPTIONS.CLONE) {
      // Clone the template and show toast
      const clonePromise = onClone({
        deal_room_template_id: data.deal_room_template_id,
      });
      showToastMessage(clonePromise, { messages: templateToastMessage.clone });
    } else if (item === MORE_OPTIONS.DELETE) {
      setShowRemoveConfirmModal(true);
    }
  };

  /**
   * Handles the delete action for a template.
   */
  const handleDelete = () => {
    // Delete the template and show toast
    const deletePromise = onDelete({
      deal_room_template_id: data.deal_room_template_id,
    });
    showToastMessage(deletePromise, { messages: templateToastMessage.delete });
  };

  const statusMenu = (
    <Menu
      className="flex items-start !p-2"
      selectedKeys={[status]}
      onClick={handleStatusChange}
    >
      {Object.values(ACTIVE_STATUS_MENU_ITEMS).map((item) => (
        <Menu.Item
          key={item.value}
          className={twMerge(
            "rounded-lg w-full !h-9",
            status === item.value &&
              "font-medium bg-ever-base text-ever-primary"
          )}
        >
          <div className="flex gap-2 items-center">
            <EverTg.Caption>{item.label}</EverTg.Caption>
            {status === item.value && <CheckIcon className="h-4 w-4" />}
          </div>
        </Menu.Item>
      ))}
    </Menu>
  );

  const moreOptionsMenu = (
    <Menu
      className="flex items-start !p-2"
      onClick={(event) => {
        event.domEvent.stopPropagation();
      }}
    >
      {MORE_MENU_ITEMS.map((item, key) => (
        <Menu.Item
          className="!px-0 w-full hover:!bg-transparent !h-9"
          key={key}
        >
          <EverButton
            type="text"
            color={item.type === MORE_OPTIONS.DELETE ? "error" : "base"}
            size="small"
            className="w-full justify-start"
            prependIcon={item.icon}
            onClick={() => handleMoreOptionsClick(item.type)}
          >
            <EverTg.Caption
              className={twMerge(
                "font-normal",
                item.type === MORE_OPTIONS.DELETE
                  ? "text-ever-error-lite-content"
                  : ""
              )}
            >
              {item.label}
            </EverTg.Caption>
          </EverButton>
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <React.Fragment key={data.deal_room_template_id}>
      <EverCard
        className="flex gap-6 items-center justify-between py-3"
        onClick={() => {
          navigate(
            `/cpq/settings/deal-room-templates/${data.deal_room_template_id}`
          );
        }}
        interactive
      >
        <div className="w-full">
          <div className="flex flex-col gap-1.5 w-11/12">
            <div className="flex gap-2 items-center">
              <TruncatedText
                text={data.deal_room_template_name}
                className="max-w-96 font-medium"
              >
                <EverTg.Text>{data.deal_room_template_name}</EverTg.Text>
              </TruncatedText>
            </div>
            {data.deal_room_template_description && (
              <EverTooltip title={data.deal_room_template_description}>
                <div className="w-fit text-sm text-ever-base-content-mid line-clamp-2">
                  {data.deal_room_template_description}
                </div>
              </EverTooltip>
            )}
          </div>
        </div>
        <Dropdown
          overlay={statusMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <EverBadge
            className="!h-8 gap-1.5 text-sm font-medium"
            {...getBadgeProps()}
          />
        </Dropdown>
        <Dropdown
          overlay={moreOptionsMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <IconButton
            type="text"
            color="base"
            size="small"
            className="!px-2"
            icon={
              <DotsVerticalIcon className="w-4 h-4 text-ever-base-content-mid" />
            }
          />
        </Dropdown>
      </EverCard>
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title={REMOVE_CONFIRMATION_MESSAGES.title}
        subtitle={REMOVE_CONFIRMATION_MESSAGES.subtitle}
        onConfirm={handleDelete}
        onCancel={() => setShowRemoveConfirmModal(false)}
        loading={isLoading}
      />
    </React.Fragment>
  );
};

TemplateCard.propTypes = {
  data: PropTypes.object.isRequired,
  onClone: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
};

TemplateCard.defaultProps = {
  isLoading: false,
};

export default TemplateCard;
