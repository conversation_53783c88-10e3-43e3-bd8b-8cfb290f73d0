/**
 * Validation schemas for Deal Room Template operations using Valibot.
 * Provides type-safe validation for API payloads before making requests.
 */

import {
  object,
  string,
  pipe,
  minLength,
  optional,
  regex,
  picklist,
} from "valibot";

/**
 * Schema for cloning a deal room template.
 * Validates that deal_room_template_id is a non-empty string.
 */
export const cloneDealRoomTemplateSchema = object({
  deal_room_template_id: pipe(
    string(),
    minLength(1, "Deal room template ID is required"),
    regex(
      /^[\w-]+$/,
      "Deal room template ID must contain only letters, numbers, hyphens, and underscores"
    )
  ),
});

/**
 * Schema for deleting a deal room template.
 * Validates that deal_room_template_id is a non-empty string.
 */
export const deleteDealRoomTemplateSchema = object({
  deal_room_template_id: pipe(
    string(),
    minLength(1, "Deal room template ID is required"),
    regex(
      /^[\w-]+$/,
      "Deal room template ID must contain only letters, numbers, hyphens, and underscores"
    )
  ),
});

/**
 * Schema for updating a deal room template.
 * Validates template ID and status updates.
 */
export const updateDealRoomTemplateSchema = object({
  deal_room_template_id: pipe(
    string(),
    minLength(1, "Deal room template ID is required"),
    regex(
      /^[\w-]+$/,
      "Deal room template ID must contain only letters, numbers, hyphens, and underscores"
    )
  ),
  deal_room_template_status: optional(
    picklist(
      ["active", "inactive"],
      "Status must be either 'active' or 'inactive'"
    )
  ),
  deal_room_template_name: optional(string()),
  deal_room_template_description: optional(string()),
});

/**
 * Schema for creating a deal room template.
 * Validates required template name and optional description.
 */
export const createDealRoomTemplateSchema = object({
  deal_room_template_name: pipe(
    string(),
    minLength(1, "Template name is required")
  ),
  deal_room_template_description: optional(string()),
});
