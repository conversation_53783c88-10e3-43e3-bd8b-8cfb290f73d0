/**
 * Tests for Deal Room Template validation schemas.
 * Demonstrates how Valibot validation works for template operations.
 */

import { safeParse } from "valibot";

import {
  cloneDealRoomTemplateSchema,
  deleteDealRoomTemplateSchema,
  createDealRoomTemplateSchema,
  updateDealRoomTemplateSchema,
} from "./validation";

describe("Deal Room Template Validation", () => {
  describe("Clone Deal Room Template Validation", () => {
    test("should validate correct payload", () => {
      const payload = { deal_room_template_id: "template_123" };
      const result = safeParse(cloneDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should reject empty deal_room_template_id", () => {
      const payload = { deal_room_template_id: "" };
      const result = safeParse(cloneDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room template ID is required"
      );
    });

    test("should reject invalid deal_room_template_id format", () => {
      const payload = { deal_room_template_id: "template@123" };
      const result = safeParse(cloneDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room template ID must contain only letters, numbers, hyphens, and underscores"
      );
    });

    test("should reject missing deal_room_template_id", () => {
      const payload = {};
      const result = safeParse(cloneDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });

    test("should accept valid template ID with hyphens and underscores", () => {
      const payload = { deal_room_template_id: "template-123_abc" };
      const result = safeParse(cloneDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
  });

  describe("Delete Deal Room Template Validation", () => {
    test("should validate correct payload", () => {
      const payload = { deal_room_template_id: "template_456" };
      const result = safeParse(deleteDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should reject empty deal_room_template_id", () => {
      const payload = { deal_room_template_id: "" };
      const result = safeParse(deleteDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room template ID is required"
      );
    });

    test("should reject invalid deal_room_template_id format", () => {
      const payload = { deal_room_template_id: "template#456" };
      const result = safeParse(deleteDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room template ID must contain only letters, numbers, hyphens, and underscores"
      );
    });

    test("should reject missing deal_room_template_id", () => {
      const payload = {};
      const result = safeParse(deleteDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });
  });

  describe("Create Deal Room Template Validation", () => {
    test("should validate correct payload with required fields only", () => {
      const payload = { deal_room_template_name: "Standard Template" };
      const result = safeParse(createDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should validate correct payload with all fields", () => {
      const payload = {
        deal_room_template_name: "Premium Template",
        deal_room_template_description:
          "A comprehensive template for premium deals",
      };
      const result = safeParse(createDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should reject empty template name", () => {
      const payload = { deal_room_template_name: "" };
      const result = safeParse(createDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe("Template name is required");
    });

    test("should reject missing template name", () => {
      const payload = {};
      const result = safeParse(createDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });

    test("should accept template with only whitespace in description", () => {
      const payload = {
        deal_room_template_name: "Test Template",
        deal_room_template_description: "   ",
      };
      const result = safeParse(createDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });
  });

  describe("Update Deal Room Template Validation", () => {
    test("should validate status update payload", () => {
      const payload = {
        deal_room_template_id: "template_789",
        deal_room_template_status: "active",
      };
      const result = safeParse(updateDealRoomTemplateSchema, payload);
      expect(result.success).toBe(true);
      expect(result.output).toEqual(payload);
    });

    test("should reject invalid status value", () => {
      const payload = {
        deal_room_template_id: "template_789",
        deal_room_template_status: "invalid_status",
      };
      const result = safeParse(updateDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Status must be either 'active' or 'inactive'"
      );
    });

    test("should reject invalid template ID format", () => {
      const payload = {
        deal_room_template_id: "template@789",
        deal_room_template_status: "active",
      };
      const result = safeParse(updateDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toBe(
        "Deal room template ID must contain only letters, numbers, hyphens, and underscores"
      );
    });

    test("should reject missing template ID", () => {
      const payload = {
        deal_room_template_status: "active",
      };
      const result = safeParse(updateDealRoomTemplateSchema, payload);
      expect(result.success).toBe(false);
      expect(result.issues[0].message).toMatch(/Invalid type: Expected string/);
    });
  });
});
