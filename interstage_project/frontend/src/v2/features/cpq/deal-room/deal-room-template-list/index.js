import { PlusCircleIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useState, useRef, useEffect } from "react";
import { useMutation } from "react-query";
import { useNavigate } from "react-router-dom";

import {
  EverTg,
  EverButton,
  EmptyPagePlaceholder,
  EverLoader,
  message,
  showToastMessage,
} from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";
import { CreateEditTemplateModal } from "~/v2/features/cpq/deal-room/components";
import { useFetchApi } from "~/v2/features/cpq/hooks";
import { createQuoteRule } from "~/v2/images";

import { templateToastMessage, API_ENDPOINTS, API } from "./constants";
import TemplateCard from "./TemplateCard";
import {
  cloneDealRoomTemplateSchema,
  deleteDealRoomTemplateSchema,
  createDealRoomTemplateSchema,
  updateDealRoomTemplateSchema,
} from "./validation";

/**
 * DealRoomTemplateList displays a list of all deal room templates, with create, clone, update, and delete functionality.
 * @returns {JSX.Element}
 */
const DealRoomTemplateList = () => {
  const initialLoading = useRef(true);
  const navigate = useNavigate();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const [isCreateTemplateModalOpen, setIsCreateTemplateModalOpen] =
    useState(false);
  const [templates, setTemplates] = useState([]);

  /**
   * Fetches all templates from the backend and updates state.
   */
  const { mutate: getAllTemplates, isLoading: getTemplatesLoading } =
    useMutation(
      () =>
        fetchEndpoint(API.GET_ALL_DEALROOM_TEMPLATES, {
          body: { sort_by: "name" },
        }),
      {
        onSuccess: (data) => {
          setTemplates(data.deal_room_templates);
        },
        onError: (error) => {
          setTemplates([]);
          // Show error message if fetch fails
          message.error(error.message);
        },
        onSettled: () => {
          initialLoading.current = false;
        },
      }
    );

  /**
   * Creates a new template and navigates to its page on success.
   */
  const createTemplate = useMutation(
    (body) =>
      fetchEndpoint(API.CREATE_DEALROOM_TEMPLATE, {
        body,
        validateSchema: createDealRoomTemplateSchema,
      }),
    {
      onSuccess: (data) => {
        setIsCreateTemplateModalOpen(false);
        navigate(
          `/cpq/settings/deal-room-templates/${data.deal_room_template_id}`
        );
      },
    }
  );

  /**
   * Clones a template and navigates to the new template on success.
   */
  const cloneTemplate = useMutation(
    (body) =>
      fetchEndpoint(API.CLONE_DEALROOM_TEMPLATE, {
        body,
        validateSchema: cloneDealRoomTemplateSchema,
      }),
    {
      onSuccess: (data) => {
        navigate(
          `/cpq/settings/deal-room-templates/${data.deal_room_template_id}`
        );
      },
    }
  );

  /**
   * Updates a template and refreshes the list on success.
   */
  const updateTemplate = useMutation(
    (body) =>
      fetchEndpoint(API.UPDATE_DEALROOM_TEMPLATE, {
        body,
        validateSchema: updateDealRoomTemplateSchema,
      }),
    { onSuccess: () => getAllTemplates() }
  );

  /**
   * Deletes a template and refreshes the list on success.
   */
  const deleteTemplate = useMutation(
    (body) =>
      fetchEndpoint(API.DELETE_DEALROOM_TEMPLATE, {
        body,
        validateSchema: deleteDealRoomTemplateSchema,
      }),
    { onSuccess: () => getAllTemplates() }
  );

  /**
   * Handles the creation of a new template, showing a toast message.
   * @param {Object} data
   */
  const handleCreateTemplate = (data) => {
    const createPromise = createTemplate.mutateAsync(data);
    showToastMessage(createPromise, { messages: templateToastMessage.create });
  };

  useEffect(() => {
    getAllTemplates();
  }, [getAllTemplates]);

  /**
   * Renders the main content of the template list, including loading, empty, and results states.
   * @returns {JSX.Element}
   */
  const renderContent = () => {
    if (initialLoading.current) {
      return (
        <EverLoader
          indicatorType="spinner"
          className="flex h-full"
          wrapperClassName="z-20"
          spinning
        />
      );
    }

    // Show empty state if no templates
    if (!getTemplatesLoading && isEmpty(templates)) {
      return (
        <div className="h-full px-8">
          <EmptyPagePlaceholder
            title="Manage your templates"
            description="Build customized templates to streamline your organization's selling process."
            image={createQuoteRule}
            buttonsList={[
              <EverButton
                size="small"
                type="outlined"
                color="primary"
                prependIcon={<PlusCircleIcon className="text-ever-primary" />}
                key="createNewTemplate"
                onClick={() => setIsCreateTemplateModalOpen(true)}
                className="!text-ever-primary"
              >
                Create New Template
              </EverButton>,
            ]}
          />
        </div>
      );
    }

    return (
      <EverLoader
        indicatorType="spinner"
        className="flex flex-col overflow-auto"
        wrapperClassName="z-20"
        spinning={
          getTemplatesLoading ||
          createTemplate.isLoading ||
          cloneTemplate.isLoading ||
          updateTemplate.isLoading ||
          deleteTemplate.isLoading
        }
      >
        <div className="flex flex-col gap-4 h-full px-6 py-2 mb-4 overflow-auto">
          {templates.map((template) => (
            <TemplateCard
              key={template.deal_room_template_id}
              data={template}
              onClone={cloneTemplate.mutateAsync}
              onUpdate={updateTemplate.mutateAsync}
              onDelete={deleteTemplate.mutateAsync}
              isLoading={
                getTemplatesLoading ||
                createTemplate.isLoading ||
                cloneTemplate.isLoading ||
                updateTemplate.isLoading ||
                deleteTemplate.isLoading
              }
            />
          ))}
        </div>
      </EverLoader>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-end gap-5 px-8 py-4 border-b border-ever-base-400 bg-ever-base-50">
        <div className="flex flex-col gap-1.5 w-[calc(100%-200px)]">
          <EverTg.Heading2>Templates</EverTg.Heading2>
          <TruncatedText
            text="Configure templates to manage exceptions like excessive discounts,
          invalid product configurations, and other special cases."
          >
            <EverTg.Text>
              Configure templates to manage exceptions like excessive discounts,
              invalid product configurations, and other special cases.
            </EverTg.Text>
          </TruncatedText>
        </div>
        <EverButton
          className="ml-auto"
          prependIcon={<PlusCircleIcon />}
          size="small"
          onClick={() => setIsCreateTemplateModalOpen(true)}
        >
          Create New Template
        </EverButton>
      </div>
      <div className="h-full py-5">{renderContent()}</div>
      <CreateEditTemplateModal
        visible={isCreateTemplateModalOpen}
        onClose={() => setIsCreateTemplateModalOpen(false)}
        onSubmit={handleCreateTemplate}
        loading={createTemplate.isLoading}
      />
    </div>
  );
};

export default DealRoomTemplateList;
