import { STATUS_OPTIONS } from "~/v2/features/cpq/deal-room/constants";

const REMOVE_CONFIRMATION_MESSAGES = {
  title: "Are you sure to remove this template?",
  subtitle: "This action cannot be undone",
};

const templateToastMessage = {
  create: {
    loading: "Creating deal room template",
    error: (errorMsg) => errorMsg || "Unable to create deal room template",
  },
  clone: {
    loading: "Cloning deal room template",
    error: (errorMsg) => errorMsg || "Unable to clone deal room template",
  },
  delete: {
    loading: "Deleting deal room template",
    success: "Template has been deleted",
    error: (errorMsg) => errorMsg || "Unable to delete deal room template",
  },
  update: (data) => ({
    loading: "Updating deal room template",
    success:
      data?.status === STATUS_OPTIONS.ACTIVE
        ? "Template is now active"
        : "Template is now inactive",
    error: (errorMsg) => errorMsg || "Unable to update deal room template",
  }),
};

const API = {
  GET_ALL_DEALROOM_TEMPLATES: "getAllDealRoomTemplates",
  CREATE_DEALROOM_TEMPLATE: "createDealRoomTemplate",
  CLONE_DEALROOM_TEMPLATE: "cloneDealRoomTemplate",
  UPDATE_DEALROOM_TEMPLATE: "updateDealRoomTemplate",
  DELETE_DEALROOM_TEMPLATE: "deleteDealRoomTemplate",
};

const API_ENDPOINTS = {
  [API.GET_ALL_DEALROOM_TEMPLATES]: {
    url: "/ninja/dealroom/manage_templates/get_all_templates",
    method: "POST",
  },
  [API.CREATE_DEALROOM_TEMPLATE]: {
    url: "/ninja/dealroom/manage_templates/create_template",
    method: "POST",
  },
  [API.CLONE_DEALROOM_TEMPLATE]: {
    url: "/ninja/dealroom/manage_templates/clone_template",
    method: "POST",
  },
  [API.UPDATE_DEALROOM_TEMPLATE]: {
    url: "/ninja/dealroom/manage_templates/update_template",
    method: "POST",
  },
  [API.DELETE_DEALROOM_TEMPLATE]: {
    url: "/ninja/dealroom/manage_templates/delete_template",
    method: "POST",
  },
};

export {
  templateToastMessage,
  REMOVE_CONFIRMATION_MESSAGES,
  API,
  API_ENDPOINTS,
};
