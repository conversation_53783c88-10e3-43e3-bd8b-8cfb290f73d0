# Deal Room Navigation Flows

## Overview

This document provides detailed navigation flow diagrams for each major component of the Deal Room system, including folder structure relationships and user interaction flows.

## Deal Room List Navigation Flow

### Folder Structure Flow

```mermaid
graph TD
    A[deal-room-list/] --> B[index.js]
    A --> C[DealRoomCard.js]
    A --> D[constants.js]

    B --> E[DealRoomList Component]
    E --> F[Create Deal Room Modal]
    E --> G[Search & Filter]
    E --> H[Deal Room Grid]

    H --> I[DealRoomCard Components]
    I --> J[Deal Room Actions]
    J --> K[Navigate to Builder]
    J --> L[Clone Deal Room]
    J --> M[Delete Deal Room]

    F --> N[components/CreateEditDealRoomModal.js]
    N --> O[Template Selection]
    N --> P[CRM Integration]
    N --> Q[Customer Logo Upload]

    style E fill:#e3f2fd
    style F fill:#fff3e0
    style I fill:#f3e5f5
```

### User Navigation Flow

```mermaid
flowchart TD
    A["/cpq/deal-rooms"] --> B{Deal Rooms Exist?}

    B -->|No| C[Empty State]
    B -->|Yes| D[Deal Room Grid]

    C --> E[Create Deal Room Button]
    D --> F[Search Bar]
    D --> G[Deal Room Cards]
    D --> E

    E --> H[Create Deal Room Modal]
    H --> I[Fill Deal Room Details]
    I --> J[Select Template]
    J --> K[Link CRM Opportunity]
    K --> L[Add Customer Logo]
    L --> M[Submit Form]
    M --> N[Navigate to Builder]
    N --> O["/cpq/deal-rooms/{id}"]

    G --> P{Card Action}
    P -->|Click Card| Q[Open Builder]
    P -->|More Menu| R[Action Menu]

    R --> S[Clone Deal Room]
    R --> T[Delete Deal Room]

    Q --> O
    S --> U[Clone Created]
    U --> O
    T --> V[Confirmation Modal]
    V --> W[Delete & Refresh List]

    F --> X[Filter Results]
    X --> D

    style H fill:#e8f5e8
    style O fill:#e1f5fe
    style V fill:#ffebee
```

## Deal Room Builder Navigation Flow

### Folder Structure Flow

```mermaid
graph TD
    A[deal-room-builder/] --> B[index.js]
    A --> C[PropertiesPanel.js]
    A --> D[AddSectionPopover.js]
    A --> E[constants.js]
    A --> F[utils.js]

    A --> G[hooks/]
    G --> H[useBuilder.js]
    G --> I[usePresentationMode.js]

    A --> J[builder/]
    J --> K[index.js - Builder Canvas]
    J --> L[AutoScaleContainer.js]

    A --> M[header/]
    M --> N[DealRoomHeader.js]
    M --> O[DealRoomTemplateHeader.js]

    A --> P[section-side-bar/]
    P --> Q[index.js - Sidebar]
    P --> R[Page.js]

    A --> S[block-fields/]
    S --> T[TextInput.js]
    S --> U[ColorPicker.js]
    S --> V[FileUpload.js]
    S --> W[ButtonGroup.js]
    S --> X[List Components...]

    B --> Y[Deal Room Builder Component]
    Y --> Z[Builder Context Provider]
    Z --> AA[Form Provider]
    AA --> BB[Three Panel Layout]

    BB --> CC[Section Sidebar]
    BB --> DD[Builder Canvas]
    BB --> EE[Properties Panel]

    CC --> Q
    DD --> K
    EE --> C

    style Y fill:#e1f5fe
    style H fill:#fff3e0
    style K fill:#f3e5f5
    style C fill:#e8f5e8
```

### User Navigation Flow

```mermaid
flowchart TD
    A["/cpq/deal-rooms/{id}"] --> B[Load Deal Room Data]
    B --> C[Builder Interface]

    C --> D[Sidebar Navigation]
    C --> E[Builder Canvas]
    C --> F[Properties Panel]
    C --> G[Header Actions]

    D --> H[Welcome Page]
    D --> I[Header Section]
    D --> J[Custom Pages]
    D --> K[Add New Page]

    J --> L{Page Actions}
    L -->|Click Page| M[Navigate to Page]
    L -->|+ Button| N[Add Section Popover]
    L -->|More Menu| O[Page Context Menu]

    N --> P[Select Block Type]
    P --> Q[Hero Block]
    P --> R[Feature Block]
    P --> S[Metrics Block]
    P --> T[Members Block]
    P --> U[Resources Block]
    P --> V[FAQ Block]
    P --> W[Testimonials Block]
    P --> X[Logo Block]

    Q --> Y[Add Block to Canvas]
    R --> Y
    S --> Y
    T --> Y
    U --> Y
    V --> Y
    W --> Y
    X --> Y

    Y --> Z[Update Canvas Display]
    Z --> AA[Show in Properties Panel]

    E --> BB{Canvas Action}
    BB -->|Click Block| CC[Select Block]
    BB -->|Drag Block| DD[Reorder Blocks]

    CC --> F
    F --> EE[Block Configuration]
    EE --> FF[Update Block Properties]
    FF --> GG[Real-time Preview]

    G --> HH[Preview Mode]
    G --> II[Presentation Mode]
    G --> JJ[Share Deal Room]
    G --> KK[Save & Publish]

    HH --> LL["?view=preview"]
    II --> MM["?view=presentation"]
    JJ --> NN[Public Link Generation]
    KK --> OO[Update Deal Room Status]

    O --> PP[Clone Page]
    O --> QQ[Delete Page]
    O --> RR[Rename Page]

    style C fill:#e1f5fe
    style N fill:#fff3e0
    style F fill:#e8f5e8
    style LL fill:#f3e5f5
    style MM fill:#f3e5f5
```

## Deal Room Template List Navigation Flow

### Folder Structure Flow

```mermaid
graph TD
    A[deal-room-template-list/] --> B[index.js]
    A --> C[TemplateCard.js]
    A --> D[constants.js]

    B --> E[DealRoomTemplateList Component]
    E --> F[Create Template Modal]
    E --> G[Template Grid]

    G --> H[TemplateCard Components]
    H --> I[Template Actions]
    I --> J[Navigate to Template Builder]
    I --> K[Clone Template]
    I --> L[Delete Template]
    I --> M[Update Status]

    F --> N[components/CreateEditTemplateModal.js]
    N --> O[Template Details Form]

    style E fill:#e3f2fd
    style F fill:#fff3e0
    style H fill:#f3e5f5
```

### User Navigation Flow

```mermaid
flowchart TD
    A["/cpq/settings/deal-room-templates"] --> B{Templates Exist?}

    B -->|No| C[Empty State]
    B -->|Yes| D[Template Grid]

    C --> E[Create Template Button]
    D --> F[Template Cards]
    D --> E

    E --> G[Create Template Modal]
    G --> H[Fill Template Details]
    H --> I[Template Name]
    H --> J[Description]
    H --> K[Initial Status]
    K --> L[Submit Form]
    L --> M[Navigate to Template Builder]
    M --> N["/cpq/settings/deal-room-templates/{id}"]

    F --> O{Card Action}
    O -->|Click Card| P[Open Template Builder]
    O -->|Status Toggle| Q[Update Template Status]
    O -->|More Menu| R[Action Menu]

    R --> S[Clone Template]
    R --> T[Delete Template]

    P --> N
    Q --> U[Active/Inactive Toggle]
    U --> V[Update Status & Refresh]

    S --> W[Clone Created]
    W --> N

    T --> X[Confirmation Modal]
    X --> Y[Delete & Refresh List]

    style G fill:#e8f5e8
    style N fill:#e1f5fe
    style X fill:#ffebee
    style U fill:#fff3e0
```

## Deal Room Template Builder Navigation Flow

### Folder Structure Flow

```mermaid
graph TD
    A[deal-room-builder/] --> B[index.js]
    A --> C[Same Structure as Deal Room Builder]

    B --> D[DealRoom Component]
    D --> E{isTemplate = true}
    E --> F[BuilderProvider]
    F --> G[Template Builder Context]

    G --> H[Template Header]
    G --> I[Same Three Panel Layout]

    H --> J[header/DealRoomTemplateHeader.js]
    I --> K[Sidebar + Canvas + Properties]

    K --> L[Template-specific Actions]
    L --> M[Save Template]
    L --> N[Publish Template]
    L --> O[Clone Template]
    L --> P[Delete Template]

    style D fill:#e1f5fe
    style G fill:#fff3e0
    style H fill:#f3e5f5
```

### User Navigation Flow

```mermaid
flowchart TD
    A["/cpq/settings/deal-room-templates/{id}"] --> B[Load Template Data]
    B --> C[Template Builder Interface]

    C --> D[Template Header]
    C --> E[Same Builder Layout]

    D --> F[Template Name & Status]
    D --> G[Template Actions]

    G --> H[Save Template]
    G --> I[Publish Template]
    G --> J[Clone Template]
    G --> K[Delete Template]
    G --> L[Preview Template]

    E --> M[Page Management]
    E --> N[Block Configuration]
    E --> O[Properties Panel]

    M --> P{Page Actions}
    P -->|Add Page| Q[Create Template Page]
    P -->|Configure| R[Template Page Setup]

    N --> S{Block Actions}
    S -->|Add Block| T[Template Block Creation]
    S -->|Configure| U[Template Block Properties]

    T --> V[Set Default Properties]
    V --> W[Template Block Schema]

    H --> X[Save Template Changes]
    I --> Y[Update Template Status]
    Y --> Z[Make Available for Deal Rooms]

    J --> AA[Create Template Copy]
    AA --> BB[Navigate to New Template]

    K --> CC[Confirmation Modal]
    CC --> DD[Delete Template]
    DD --> EE[Navigate Back to Template List]
    EE --> FF["/cpq/settings/deal-room-templates"]

    L --> GG[Template Preview Mode]
    GG --> HH["?view=preview"]

    style C fill:#e1f5fe
    style G fill:#fff3e0
    style V fill:#e8f5e8
    style CC fill:#ffebee
    style GG fill:#f3e5f5
```

## Cross-Component Navigation Relationships

### Component Interaction Flow

```mermaid
graph TD
    A[Deal Room List] --> B[Deal Room Builder]
    C[Template List] --> D[Template Builder]
    D --> E[Template Used in Deal Room Creation]
    E --> B

    B --> F[Share Deal Room]
    F --> G[Public Link]
    G --> H[Prospect Access]

    B --> I[Sync with Template]
    I --> J[Update from Template]
    J --> B

    C --> K[Template Status Management]
    K --> L[Available in Deal Room Creation]

    style B fill:#e1f5fe
    style D fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#f3e5f5
```

### URL Structure Navigation

```mermaid
graph TD
    A["/cpq/deal-rooms"] --> B["Deal Room List"]
    A --> C["/cpq/deal-rooms/{id}"]
    C --> D["Deal Room Builder"]
    C --> E["?view=preview"]
    C --> F["?view=presentation"]
    C --> G["?page={pageId}"]
    C --> H["#{sectionId}"]

    I["/cpq/settings/deal-room-templates"] --> J["Template List"]
    I --> K["/cpq/settings/deal-room-templates/{id}"]
    K --> L["Template Builder"]

    M["/public/deal-room/{token}"] --> N["Public Deal Room Access"]
    N --> O["Email Validation"]
    O --> P["Deal Room Content"]

    style D fill:#e1f5fe
    style L fill:#fff3e0
    style P fill:#e8f5e8
```

## File Dependencies and Data Flow

### Data Flow Between Components

```mermaid
graph TD
    A[constants.js] --> B[All Components]
    C[deal-room-list/constants.js] --> D[Deal Room List]
    E[deal-room-template-list/constants.js] --> F[Template List]
    G[deal-room-builder/constants.js] --> H[Builder Components]

    I[useBuilder Hook] --> J[Builder State Management]
    J --> K[Form Data]
    J --> L[API Calls]
    J --> M[Real-time Updates]

    N[Builder Components] --> O[Block Fields]
    O --> P[Properties Panel]
    P --> Q[Form Updates]
    Q --> R[Canvas Re-render]

    S[API Services] --> T[CRUD Operations]
    T --> U[Deal Rooms]
    T --> V[Templates]
    T --> W[Pages & Blocks]

    style J fill:#e1f5fe
    style T fill:#fff3e0
    style R fill:#e8f5e8
```

---

These navigation flows provide a comprehensive view of how users move through the Deal Room system and how the code is organized to support those user journeys. Each diagram shows both the structural organization and the user interaction patterns for maximum clarity.
