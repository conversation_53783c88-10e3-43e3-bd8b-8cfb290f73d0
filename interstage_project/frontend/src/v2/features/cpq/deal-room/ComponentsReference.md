# Deal Room Components Reference

## Overview

This document provides a comprehensive reference guide for **using existing Deal Room components**. It covers all available components, their interfaces, usage patterns, and examples.

**Target Audience:** Developers who want to understand, use, or modify existing components.

## Component Categories

### 🎨 Builder Components (Visual Blocks)

These are the core visual elements that users can add to their deal rooms:

- **Hero** - Primary section with title, description, and media
- **Feature** - Highlight key features with text, buttons, and images
- **Metrics** - Display key statistics and achievements
- **Members** - Showcase team members with photos and contact info
- **Resources** - Share files, videos, and links with prospects
- **FAQ** - Display frequently asked questions in collapsible format
- **Testimonials** - Display customer testimonials and success stories
- **Logo** - Display company branding and welcome message

### 📝 Form Field Components (Properties Panel)

Input controls used in the properties panel for configuring blocks:

- **TextInput** - Single-line text input for titles, names, etc.
- **TextArea** - Multi-line text input for descriptions
- **ColorPicker** - Color selection for backgrounds and text
- **FileUpload** - File upload with drag-and-drop and preview
- **FileUploadWithOptions** - Advanced file upload with type options
- **ButtonGroup** - Configure action buttons with links
- **RadioGroup** - Radio button group for single selections
- **FAQList** - Manage FAQ items with add/remove/reorder
- **MetricsList** - Manage metric items with values and labels
- **MembersList** - Manage team members with contact information
- **ResourceList** - Manage resource items with different types
- **TestimonialList** - Manage customer testimonials
- **TabComponent** - Tabbed interface for organizing form fields

### 🏗️ Layout Components (Structure & Containers)

Structural elements and containers that provide consistent layouts:

- **LayoutContainer** - Wrapper for builder components with consistent spacing
- **Typography** - Consistent typography system with multiple variants
- **DealRoomThemeProvider** - Theme context provider for styling

### ⚙️ Utility Components (Modals & Helpers)

Helper components for common functionality:

- **CreateEditDealRoomModal** - Modal for creating/editing deal rooms
- **CreateEditTemplateModal** - Modal for template management

---

# Detailed Component Documentation

## Builder Components Documentation

### Hero Component

**Purpose:** Primary hero section that introduces the deal room with a compelling title, description, and supporting media (image or video).

**Location:** `components/builder-components/Hero.js`

**Interface:**

```typescript
interface HeroProps {
  data: {
    title: string; // Main headline text
    description?: string; // Supporting description text
    file?: {
      // Optional media file
      file_url: string;
      file_name: string;
      file_type: "image" | "video";
    };
    background?: BackgroundConfig; // Background styling
    text?: TextConfig; // Text styling and alignment
  };
}
```

**Usage Example:**

```javascript
<Hero
  data={{
    title: "Welcome to Our Solution",
    description: "Discover how we can transform your business operations",
    file: {
      file_url: "/assets/hero-video.mp4",
      file_name: "hero-video.mp4",
      file_type: "video",
    },
    background: {
      background_color: "#f8f9fa",
    },
    text: {
      text_alignment: "center",
      text_color: "#333333",
    },
  }}
  navigateInternal={handleInternalNavigation}
  buyersLogoUrl="/assets/logo.png"
/>
```

**Key Features:**

- Supports both image and video media
- Responsive design with mobile optimization
- Internal navigation support for button links
- Customizable background and text styling
- Accessibility features for screen readers

### Feature Component

**Purpose:** Highlight key features with text, buttons, and supporting images.

**Location:** `components/builder-components/Feature.js`

**Interface:**

```typescript
interface FeatureProps {
  data: {
    title: string;
    description?: string;
    file?: {
      file_url: string;
      file_name: string;
      file_type: "image" | "video";
    };
    buttons?: Array<{
      text: string;
      url: string;
      style: "primary" | "secondary" | "outline";
    }>;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<Feature
  data={{
    title: "Powerful Analytics",
    description:
      "Get deep insights into your performance with our advanced analytics dashboard.",
    file: {
      file_url: "/assets/analytics-demo.png",
      file_name: "analytics-demo.png",
      file_type: "image",
    },
    buttons: [
      {
        text: "Learn More",
        url: "#analytics",
        style: "primary",
      },
      {
        text: "Watch Demo",
        url: "https://example.com/demo",
        style: "secondary",
      },
    ],
    background: {
      background_color: "#ffffff",
    },
    text: {
      text_alignment: "left",
      text_color: "#333333",
    },
  }}
  navigateInternal={handleInternalNavigation}
/>
```

**Key Features:**

- Multiple button support with different styles
- Image/video integration
- Flexible text alignment
- Responsive layout with image positioning

### Metrics Component

**Purpose:** Display key statistics and achievements in an engaging format.

**Location:** `components/builder-components/Metrics.js`

**Interface:**

```typescript
interface MetricsProps {
  data: {
    title?: string;
    metrics: Array<{
      value: string;
      label: string;
      icon?: string;
    }>;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<Metrics
  data={{
    title: "Our Impact",
    metrics: [
      {
        value: "500+",
        label: "Happy Clients",
        icon: "users",
      },
      {
        value: "99.9%",
        label: "Uptime",
        icon: "shield",
      },
      {
        value: "24/7",
        label: "Support",
        icon: "clock",
      },
    ],
    background: {
      background_color: "#f8f9fa",
    },
    text: {
      text_alignment: "center",
      text_color: "#333333",
    },
  }}
/>
```

**Key Features:**

- Icon support for each metric
- Responsive grid layout
- Animated number counting (optional)
- Customizable styling

### Members Component

**Purpose:** Showcase team members with photos, names, titles, and contact information.

**Location:** `components/builder-components/Members.js`

**Interface:**

```typescript
interface MembersProps {
  data: {
    title?: string;
    members: Array<{
      name: string;
      title: string;
      photo?: {
        file_url: string;
        file_name: string;
      };
      email?: string;
      phone?: string;
      linkedin?: string;
    }>;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<Members
  data={{
    title: "Meet Our Team",
    members: [
      {
        name: "John Doe",
        title: "CEO & Founder",
        photo: {
          file_url: "/assets/john-doe.jpg",
          file_name: "john-doe.jpg",
        },
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/johndoe",
      },
      {
        name: "Jane Smith",
        title: "CTO",
        photo: {
          file_url: "/assets/jane-smith.jpg",
          file_name: "jane-smith.jpg",
        },
        email: "<EMAIL>",
        phone: "+****************",
      },
    ],
    background: {
      background_color: "#ffffff",
    },
    text: {
      text_alignment: "center",
      text_color: "#333333",
    },
  }}
/>
```

**Key Features:**

- Contact information integration
- Social media links
- Responsive grid layout
- Hover effects for member cards

### Resources Component

**Purpose:** Share files, videos, and links with prospects in an organized format.

**Location:** `components/builder-components/Resources.js`

**Interface:**

```typescript
interface ResourcesProps {
  data: {
    title?: string;
    resources: Array<{
      title: string;
      description?: string;
      type: "file" | "video" | "link";
      url: string;
      file_name?: string;
      file_size?: string;
    }>;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<Resources
  data={{
    title: "Helpful Resources",
    resources: [
      {
        title: "Product Brochure",
        description: "Comprehensive overview of our solutions",
        type: "file",
        url: "/assets/brochure.pdf",
        file_name: "product-brochure.pdf",
        file_size: "2.5 MB",
      },
      {
        title: "Demo Video",
        description: "See our platform in action",
        type: "video",
        url: "https://youtube.com/watch?v=example",
      },
      {
        title: "Case Study",
        description: "Success story with major client",
        type: "link",
        url: "https://example.com/case-study",
      },
    ],
    background: {
      background_color: "#f8f9fa",
    },
    text: {
      text_alignment: "left",
      text_color: "#333333",
    },
  }}
/>
```

**Key Features:**

- Multiple resource types (file, video, link)
- File size display for downloads
- External link handling
- Organized card layout

### FAQ Component

**Purpose:** Display frequently asked questions in an interactive, collapsible format.

**Location:** `components/builder-components/FAQ.js`

**Interface:**

```typescript
interface FAQProps {
  data: {
    title?: string;
    faqs: Array<{
      question: string;
      answer: string;
    }>;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<FAQ
  data={{
    title: "Frequently Asked Questions",
    faqs: [
      {
        question: "How long does implementation take?",
        answer:
          "Implementation typically takes 2-4 weeks depending on your requirements and team size.",
      },
      {
        question: "What kind of support do you provide?",
        answer:
          "We provide 24/7 technical support, dedicated account management, and comprehensive training.",
      },
      {
        question: "Can I customize the platform?",
        answer:
          "Yes, our platform is highly customizable. You can modify themes, workflows, and integrations.",
      },
    ],
    background: {
      background_color: "#ffffff",
    },
    text: {
      text_alignment: "left",
      text_color: "#333333",
    },
  }}
/>
```

**Key Features:**

- Accordion-style interaction
- Smooth animations
- Keyboard navigation support
- Accessible ARIA attributes

### Testimonials Component

**Purpose:** Display customer testimonials and success stories to build credibility.

**Location:** `components/builder-components/Testimonials.js`

**Interface:**

```typescript
interface TestimonialsProps {
  data: {
    title?: string;
    testimonials: Array<{
      quote: string;
      author: string;
      title?: string;
      company?: string;
      photo?: {
        file_url: string;
        file_name: string;
      };
    }>;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<Testimonials
  data={{
    title: "What Our Clients Say",
    testimonials: [
      {
        quote:
          "This platform transformed how we manage our sales process. The results have been incredible.",
        author: "Sarah Johnson",
        title: "VP of Sales",
        company: "TechCorp Inc.",
        photo: {
          file_url: "/assets/sarah-johnson.jpg",
          file_name: "sarah-johnson.jpg",
        },
      },
      {
        quote:
          "The ROI we've seen in just 6 months is remarkable. Highly recommended!",
        author: "Mike Chen",
        title: "CEO",
        company: "StartupXYZ",
        photo: {
          file_url: "/assets/mike-chen.jpg",
          file_name: "mike-chen.jpg",
        },
      },
    ],
    background: {
      background_color: "#f8f9fa",
    },
    text: {
      text_alignment: "center",
      text_color: "#333333",
    },
  }}
/>
```

**Key Features:**

- Carousel/slider functionality
- Author photos and details
- Quote styling
- Responsive design

### Logo Component

**Purpose:** Display company branding and welcome message.

**Location:** `components/builder-components/Logo.js`

**Interface:**

```typescript
interface LogoProps {
  data: {
    logo?: {
      file_url: string;
      file_name: string;
    };
    welcome_message?: string;
    background?: BackgroundConfig;
    text?: TextConfig;
  };
}
```

**Usage Example:**

```javascript
<Logo
  data={{
    logo: {
      file_url: "/assets/company-logo.png",
      file_name: "company-logo.png",
    },
    welcome_message: "Welcome to our exclusive deal room",
    background: {
      background_color: "#ffffff",
    },
    text: {
      text_alignment: "center",
      text_color: "#333333",
    },
  }}
  buyersLogoUrl="/assets/client-logo.png"
/>
```

**Key Features:**

- Company logo display
- Welcome message customization
- Client logo integration
- Brand-consistent styling

## Form Field Components Documentation

### TextInput Component

**Purpose:** Single-line text input for titles, names, and short text content.

**Location:** `deal-room-builder/block-fields/TextInput.js`

**Interface:**

```typescript
interface TextInputProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  helpText?: string;
}
```

**Usage Example:**

```javascript
<Controller
  name="title"
  control={control}
  rules={{ required: "Title is required" }}
  render={({ field, fieldState }) => (
    <TextInput
      label="Title"
      placeholder="Enter section title"
      {...field}
      error={fieldState.error?.message}
      required
      helpText="This will be the main heading for this section"
    />
  )}
/>
```

### TextArea Component

**Purpose:** Multi-line text input for descriptions and longer content.

**Location:** `deal-room-builder/block-fields/TextArea.js`

**Interface:**

```typescript
interface TextAreaProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  rows?: number;
  helpText?: string;
}
```

**Usage Example:**

```javascript
<Controller
  name="description"
  control={control}
  render={({ field, fieldState }) => (
    <TextArea
      label="Description"
      placeholder="Enter detailed description"
      rows={4}
      {...field}
      error={fieldState.error?.message}
      helpText="Provide a compelling description of your offering"
    />
  )}
/>
```

### ColorPicker Component

**Purpose:** Color selection for backgrounds and text styling.

**Location:** `deal-room-builder/block-fields/ColorPicker.js`

**Interface:**

```typescript
interface ColorPickerProps {
  label: string;
  value: string;
  onChange: (color: string) => void;
  presetColors?: string[];
}
```

**Usage Example:**

```javascript
<Controller
  name="background.background_color"
  control={control}
  render={({ field }) => (
    <ColorPicker
      label="Background Color"
      {...field}
      presetColors={["#ffffff", "#f8f9fa", "#e9ecef", "#dee2e6"]}
    />
  )}
/>
```

### FileUpload Component

**Purpose:** File upload with drag-and-drop functionality and preview.

**Location:** `deal-room-builder/block-fields/FileUpload.js`

**Interface:**

```typescript
interface FileUploadProps {
  label: string;
  value?: {
    file_url: string;
    file_name: string;
    file_type: string;
  };
  onChange: (file: FileUploadValue) => void;
  accept?: string;
  maxSize?: number;
  error?: string;
}
```

**Usage Example:**

```javascript
<Controller
  name="file"
  control={control}
  render={({ field, fieldState }) => (
    <FileUpload
      label="Upload Media"
      accept="image/*,video/*"
      maxSize={10 * 1024 * 1024} // 10MB
      {...field}
      error={fieldState.error?.message}
    />
  )}
/>
```

### ButtonGroup Component

**Purpose:** Configure action buttons with links and styling options.

**Location:** `deal-room-builder/block-fields/ButtonGroup.js`

**Interface:**

```typescript
interface ButtonGroupProps {
  label: string;
  value: Array<{
    text: string;
    url: string;
    style: "primary" | "secondary" | "outline";
  }>;
  onChange: (buttons: ButtonConfig[]) => void;
  maxButtons?: number;
}
```

**Usage Example:**

```javascript
<Controller
  name="buttons"
  control={control}
  render={({ field }) => (
    <ButtonGroup label="Action Buttons" maxButtons={3} {...field} />
  )}
/>
```

### RadioGroup Component

**Purpose:** Radio button group for single selections.

**Location:** `deal-room-builder/block-fields/RadioGroup.js`

**Interface:**

```typescript
interface RadioGroupProps {
  label: string;
  options: Array<{
    value: string;
    label: string;
  }>;
  value: string;
  onChange: (value: string) => void;
}
```

**Usage Example:**

```javascript
<Controller
  name="text.text_alignment"
  control={control}
  render={({ field }) => (
    <RadioGroup
      label="Text Alignment"
      options={[
        { value: "left", label: "Left" },
        { value: "center", label: "Center" },
        { value: "right", label: "Right" },
      ]}
      {...field}
    />
  )}
/>
```

## Layout Components Documentation

### LayoutContainer Component

**Purpose:** Wrapper for builder components with consistent spacing and background handling.

**Location:** `components/LayoutContainer.js`

**Interface:**

```typescript
interface LayoutContainerProps {
  background?: BackgroundConfig;
  padding?: "small" | "medium" | "large";
  children: React.ReactNode;
  className?: string;
}
```

**Usage Example:**

```javascript
<LayoutContainer
  background={{
    background_type: "color",
    background_color: "#f8f9fa",
  }}
  padding="large"
  className="custom-section"
>
  <Typography.Heading2>Section Content</Typography.Heading2>
  <BuilderComponent data={componentData} />
</LayoutContainer>
```

### Typography Component

**Purpose:** Consistent typography system with multiple variants.

**Location:** `components/Typography.js`

**Interface:**

```typescript
interface TypographyProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
}

interface TypographyVariants {
  Heading1: React.FC<TypographyProps>;
  Heading2: React.FC<TypographyProps>;
  Heading3: React.FC<TypographyProps>;
  Body: React.FC<TypographyProps>;
  Caption: React.FC<TypographyProps>;
}
```

**Usage Example:**

```javascript
<Typography.Heading1 style={textStyles}>
  Main Title
</Typography.Heading1>

<Typography.Body className="text-gray-600">
  Supporting text content
</Typography.Body>
```

### DealRoomThemeProvider Component

**Purpose:** Theme context provider for consistent styling across components.

**Location:** `components/DealRoomThemeProvider.js`

**Interface:**

```typescript
interface DealRoomThemeProviderProps {
  theme: DealRoomTheme;
  children: React.ReactNode;
}
```

**Usage Example:**

```javascript
<DealRoomThemeProvider theme={dealRoomTheme}>
  <LayoutContainer>
    <BuilderComponent data={componentData} />
  </LayoutContainer>
</DealRoomThemeProvider>
```

## Utility Components Documentation

### CreateEditDealRoomModal Component

**Purpose:** Modal for creating and editing deal rooms with form validation.

**Location:** `components/CreateEditDealRoomModal.js`

**Interface:**

```typescript
interface CreateEditDealRoomModalProps {
  isOpen: boolean;
  onClose: () => void;
  dealRoom?: DealRoomData;
  onSave: (data: DealRoomFormData) => Promise<void>;
}
```

**Usage Example:**

```javascript
<CreateEditDealRoomModal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  dealRoom={existingDealRoom}
  onSave={handleSaveDealRoom}
/>
```

**Key Features:**

- Form validation with React Hook Form
- File upload for deal room assets
- Template selection
- Real-time preview
- Error handling and loading states

### CreateEditTemplateModal Component

**Purpose:** Modal for creating and editing deal room templates.

**Location:** `components/CreateEditTemplateModal.js`

**Interface:**

```typescript
interface CreateEditTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  template?: TemplateData;
  onSave: (data: TemplateFormData) => Promise<void>;
}
```

**Usage Example:**

```javascript
<CreateEditTemplateModal
  isOpen={isTemplateModalOpen}
  onClose={() => setIsTemplateModalOpen(false)}
  template={existingTemplate}
  onSave={handleSaveTemplate}
/>
```

**Key Features:**

- Template configuration
- Component selection
- Default data setup
- Category organization

---

This reference guide provides comprehensive documentation for all existing Deal Room components, their interfaces, and usage patterns. For information on creating new components, see the [Components Development Guide](./ComponentsDevelopment.md).
