import {
  CheckCircleIcon,
  UsersCheckIcon,
  MinusCircleIcon,
  EditIcon,
  EditPencilIcon as EditPencilSolidIcon,
  XCircleIcon,
  HourglassIcon,
  CalendarMinus02Icon,
  ThumbsDownIcon,
  Edit04Icon,
  PauseCircleIcon,
} from "@everstage/evericons/solid";
import { getUnixTime } from "date-fns";
import { isEmpty, isNil, isNumber } from "lodash";
import { parse } from "valibot";

import { DATATYPE } from "~/Enums";
import theme from "~/v2/themes/blueBias23";

import {
  DEFAULT_DECIMAL_SEPARATOR,
  DURATION_TYPES,
  DURATION_LABELS,
} from "./constants";

/**
 * Extracts the group and decimal separators for a given locale by formatting a sample number.
 *
 * @param {string} locale - The locale identifier.
 * @returns {Object} An object containing group and decimal separators.
 */
const getSeparators = (locale = "en-US") => {
  const formattedNumber = new Intl.NumberFormat([locale, "en-US"], {
    style: "decimal",
    minimumFractionDigits: 1,
  }).format(12_345.6);
  const separators = formattedNumber.match(/\D+/g); // Matches non-digit characters

  return { groupSeparator: separators[0], decimalSeparator: separators[1] };
};

const numberFormatter = (
  value,
  {
    locale = "en-US",
    skipSeparatorFormat = false,
    innerPrefix = "",
    innerSuffix = "",
    precision = null,
  }
) => {
  const { decimalSeparator } = getSeparators(locale);
  // Check if the value is invalid or empty and return as is
  if (Number.isNaN(Number(value)) || value === "") {
    return `${innerPrefix}${value || ""}${innerSuffix}`;
  }

  const precisedValue = isNil(precision)
    ? value
    : formatNumberByPrecision(value, precision);
  // Split the value into integer and decimal parts
  const [integerPart, decimalPart] = String(precisedValue).split(
    DEFAULT_DECIMAL_SEPARATOR
  );
  // Format the integer part using the specified locale
  let formattedValue = skipSeparatorFormat
    ? Number(integerPart)
    : new Intl.NumberFormat([locale, "en-US"]).format(Number(integerPart));
  // Append the decimal part if it exists
  if (decimalPart !== undefined) {
    formattedValue += `${decimalSeparator}${decimalPart}`;
  }

  // Return the formatted value with the percentage sign if the field is a percentage
  return `${innerPrefix}${formattedValue}${innerSuffix}`;
};

function roundTo(num, precision) {
  const factor = 10 ** precision;
  return Math.round(num * factor + Number.EPSILON) / factor;
}

const formatNumberByPrecision = (value, precision) => {
  const numericValue = Number.parseFloat(value);
  if (!(Number.isNaN(numericValue) || isNil(value) || value === "")) {
    // Format value to specified precision and remove trailing zeros
    const formattedValue = String(
      roundTo(numericValue, precision || 0)
    ).replace(/(\.\d*[1-9])0+|\.0*$/, "$1");
    const isNumberType = isNumber(Number.parseFloat(formattedValue));
    const finalValue = isNumberType
      ? Number.parseFloat(formattedValue)
      : formattedValue;

    return finalValue;
  }

  return value;
};

function getDurationLabel({ duration_type, duration_value }) {
  if (duration_type === DURATION_TYPES.FOREVER) {
    return DURATION_LABELS[duration_type].default;
  }
  const label = Number(duration_value) === 1 ? "singular" : "plural";
  return `${duration_value} ${DURATION_LABELS[duration_type][label]}`;
}

const getStatusBadgeProps = (status) => {
  switch (status) {
    case "Published": {
      return {
        type: "success",
        icon: <CheckCircleIcon className="h-3.5 w-3.5 text-ever-success" />,
      };
    }

    case "Active": {
      return {
        type: "success",
        icon: <CheckCircleIcon className="h-3.5 w-3.5 text-ever-success" />,
      };
    }

    case "Inactive": {
      return {
        type: "info",
        icon: <PauseCircleIcon className="h-3.5 w-3.5 text-ever-info" />,
      };
    }

    case "Draft": {
      return {
        type: "warning",
        icon: (
          <EditPencilSolidIcon className="h-3.5 w-3.5 text-ever-chartColors-22" />
        ),
      };
    }

    case "Pending Approval": {
      return {
        type: "info",
        icon: <HourglassIcon className="h-3.5 w-3.5 text-ever-info" />,
      };
    }

    case "Needs Attention": {
      return {
        type: "warning",
        icon: <HourglassIcon className="h-3.5 w-3.5" />,
      };
    }

    case "Approved": {
      return {
        type: "success",
        icon: <UsersCheckIcon className="h-3.5 w-3.5 text-ever-success" />,
      };
    }

    case "Rejected": {
      return {
        type: "error",
        icon: <MinusCircleIcon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Pending Signature": {
      return {
        type: "info",
        icon: <EditIcon className="h-3.5 w-3.5 text-ever-info" />,
      };
    }

    case "Lost": {
      return {
        type: "error",
        icon: <ThumbsDownIcon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Cancelled": {
      return {
        type: "error",
        icon: <XCircleIcon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Expired": {
      return {
        type: "error",
        icon: <CalendarMinus02Icon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Signed Offline": {
      return {
        type: "warning",
        icon: <Edit04Icon className="h-3.5 w-3.5 text-ever-chartColors-22" />,
      };
    }

    case "Won": {
      return {
        type: "custom",
        render: (
          <div className="status-badge-wrapper">
            <div className="status-badge">
              <span className="status-badge-text">{status}</span>
              <span>🎉</span>
            </div>
          </div>
        ),
      };
    }

    default: {
      return null;
    }
  }
};

const compareIfIntegerOrPercentage = (dataTypeNameA, dataTypeNameB) => {
  if (!dataTypeNameA || !dataTypeNameB) {
    return false;
  }

  let dataTypeAIsIntegerOrPercentage =
    dataTypeNameA.toLowerCase() === DATATYPE.INTEGER.toLowerCase() ||
    dataTypeNameA.toLowerCase() === DATATYPE.NUMBER.toLowerCase() ||
    dataTypeNameA.toLowerCase() === DATATYPE.PERCENTAGE.toLowerCase();
  let dataTypeBIsIntegerOrPercentage =
    dataTypeNameB.toLowerCase() === DATATYPE.INTEGER.toLowerCase() ||
    dataTypeNameB.toLowerCase() === DATATYPE.NUMBER.toLowerCase() ||
    dataTypeNameB.toLowerCase() === DATATYPE.PERCENTAGE.toLowerCase();
  return dataTypeAIsIntegerOrPercentage && dataTypeBIsIntegerOrPercentage;
};

const replaceUrl = (params, sectionId) => {
  const url = new URL(window.location.href);
  const newParams = new URLSearchParams({ ...params });
  url.search = newParams.toString();
  url.hash = sectionId ? `#${sectionId}` : "";
  window.history.replaceState({}, "", url.toString());
};

const replaceUrlHash = (sectionId) => {
  const currentUrl = new URL(window.location.href);
  if (sectionId) {
    currentUrl.hash = `#${sectionId}`;
  } else {
    currentUrl.hash = "";
  }
  window.history.replaceState(null, "", currentUrl.toString());
};

// Styling utils
function getTextColor(bgColor) {
  bgColor = bgColor ?? theme.colors.dealroom.base.DEFAULT;
  const r = parseInt(bgColor.slice(1, 3), 16);
  const g = parseInt(bgColor.slice(3, 5), 16);
  const b = parseInt(bgColor.slice(5, 7), 16);

  // Relative luminance formula (WCAG)
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // If luminance is high, return black text, else white
  return luminance > 0.5
    ? theme.colors.dealroom.base.content.DEFAULT
    : theme.colors.dealroom.base.DEFAULT;
}

const getBackgroundStyles = (background) => {
  const styles = {};

  if (!isEmpty(background?.background_color)) {
    styles.backgroundColor = background.background_color;
  }

  return styles;
};

const getTextStyles = (text, background = {}) => {
  const styles = {};

  if (!isEmpty(text?.text_color)) {
    styles.color = text.text_color;
  } else if (!isEmpty(background?.background_color)) {
    // Auto-determine text color based on background color if no explicit text color is provided
    styles.color = getTextColor(background.background_color);
  }

  if (!isEmpty(text?.text_alignment)) {
    styles.textAlign = text.text_alignment;
  }

  return styles;
};

const getModifiedLogoUrl = (logoUrl, metaInfo) => {
  if (isEmpty(metaInfo?.updatedAt)) {
    return logoUrl;
  }
  return `${logoUrl}?ts=${getUnixTime(new Date(metaInfo.updatedAt))}`;
};

/**
 * Validates a payload against a given schema.
 *
 * @param {Object} schema - The Valibot schema to validate against
 * @param {Object} payload - The payload to validate
 * @returns {Object} - Validation result with success status and data/error
 */
const validatePayload = (schema, payload) => {
  try {
    const validatedData = parse(schema, payload);
    return {
      success: true,
      data: validatedData,
      error: null,
    };
  } catch (error) {
    // Extract the first validation error message
    const firstIssue = error.issues?.[0];
    const errorMessage = firstIssue?.message || "Validation failed";

    return {
      success: false,
      data: null,
      error: errorMessage,
    };
  }
};

// Helper function to convert FormData to object
const formDataToObject = (formData) => {
  const obj = {};
  for (let [key, value] of formData.entries()) {
    // Try to parse as JSON for any field that might be serialized
    try {
      obj[key] = JSON.parse(value);
    } catch {
      // If parsing fails, use the original value
      obj[key] = value;
    }
  }
  return obj;
};

/**
 * Fetches brand data from BrandFetch API.
 * Centralized utility to avoid hardcoding the endpoint and key in multiple places.
 * @param {string} searchTerm - The company/domain to search for.
 * @returns {Promise<Object>} The BrandFetch API response.
 */
export async function fetchBrandData(searchTerm) {
  const response = await fetch(
    `https://api.brandfetch.io/v2/search/${encodeURIComponent(searchTerm)}?c=${
      process.env.BRANDFETCH_CLIENT_ID
    }`
  );
  if (!response.ok) console.error("BrandFetch API error");

  return {
    response,
    data: await response.json(),
  };
}

export {
  compareIfIntegerOrPercentage,
  getSeparators,
  numberFormatter,
  formatNumberByPrecision,
  getDurationLabel,
  getStatusBadgeProps,
  replaceUrl,
  replaceUrlHash,
  getTextColor,
  getBackgroundStyles,
  getTextStyles,
  getModifiedLogoUrl,
  validatePayload,
  formDataToObject,
};
