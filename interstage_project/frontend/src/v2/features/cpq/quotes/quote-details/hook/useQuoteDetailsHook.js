import { useQuery as useApolloQuery } from "@apollo/client";
import React, { useCallback, useMemo, useState } from "react";
import { useMutation, useQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { useSetRecoilState } from "recoil";

import { approveRequest, rejectRequest } from "~/Api/ApprovalWorkflowService";
import { breadcrumbAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { createPublicLink, message } from "~/v2/components";

import { useFetchApi } from "../../../hooks";
import {
  downloadPdf,
  fetchApprovalBannerData,
  getPdfTemplate,
  getQuoteLineItemsForPdf,
  saveQuoteDetailsService,
  voidEnvelope,
} from "../../build-quote/services/restApi";
import { onExportAsPdf } from "../../build-quote/utils";
import { STATUS_TYPES } from "../../quotes-list/utils";
import { API, API_ENDPOINTS, GET_All_COUNTRIES } from "../constants";

const IGNORED_SECTIONS = new Set(["Approval Matrix", "Select Product"]);

export function useQuoteDetailsHook(quoteId) {
  const { accessToken } = useAuthStore();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  const { userEmail } = useEmployeeStore();
  const { hasPermissions } = useUserPermissionStore();
  const navigate = useNavigate();

  const [formDataMap, setFormDataMap] = useState([]);
  const [fieldMap, setFieldMap] = useState({});
  const [accountName, setAccountName] = useState("");
  const [opportunityId, setOpportunityId] = useState("");
  const [formValues, setFormValues] = useState({});
  const [templateFileList, setTemplateFileList] = useState([]);
  const [currencyCodeSymbolMap, setCurrencyCodeSymbolMap] = useState({});

  const replaceFieldValues = useCallback(
    (object) => {
      if (!object) return [];
      let jsonString = JSON.stringify(object);

      Object.keys(fieldMap).map((key) => {
        jsonString = jsonString.replaceAll(
          new RegExp(`{{${fieldMap[key]}}}`, "g"),
          formValues?.[key] ?? ""
        );
      });

      return JSON.parse(jsonString);
    },
    [fieldMap, formValues]
  );

  const {
    data: quoteData,
    isFetching: isQuoteDataLoading,
    refetch: refetchQuoteDetails,
  } = useQuery(
    [API.GET_QUOTE_DETAILS, quoteId],
    () => fetchEndpoint(API.GET_QUOTE_DETAILS, {}, { quoteId }),
    {
      enabled: !!quoteId,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        if (data.quote_status === STATUS_TYPES.DRAFT) {
          navigate(`/cpq/quotes/${quoteId}`, {
            state: {
              isEditMode: true,
            },
          });
        }
        setBreadcrumbName([
          {
            index: 1,
            title: data.quote_display_id,
            name: data.quote_display_id,
          },
        ]);
        constructFormData(data);
      },
    }
  );

  const {
    data: publicLinkData,
    isLoading: isPublicLinkLoading,
    refetch: refetchPublicLink,
  } = useQuery(
    ["createPublicLink", quoteId],
    () => createPublicLink({ quote_id: quoteId, entity: "quote" }, accessToken),
    {
      enabled: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const { data: approverInstanceData, refetch: fetchApproverInstanceData } =
    useQuery(
      ["approverInstanceData"],
      () => fetchApprovalBannerData({ quote_id: quoteId }, accessToken),
      {
        enabled: quoteId && quoteData?.quote_status !== STATUS_TYPES.DRAFT,
        retry: false,
        cacheTime: 0,
        refetchOnWindowFocus: false,
      }
    );

  const { data: quoteTerms, isFetching: isQuoteTermsLoading } = useQuery(
    [API.GET_QUOTE_TERMS, quoteId],
    () => fetchEndpoint(API.GET_QUOTE_TERMS, {}, quoteId),
    {
      enabled: !!quoteId,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const {
    data: multiApprovalDetails,
    isFetching: isApprovalDetailsLoading,
    refetch: refetchMultiApprovalDetails,
  } = useQuery(
    [API.GET_MULTI_APPROVAL_DETAILS, quoteId],
    () => fetchEndpoint(API.GET_MULTI_APPROVAL_DETAILS, {}, { quoteId }),
    {
      enabled: !!quoteId,
    }
  );

  const { data: templateData } = useQuery(
    ["getPdfTemplate"],
    () => getPdfTemplate({ quote_id: quoteId }, accessToken),
    {
      enabled: !!quoteId,
    }
  );

  const { data: tableData, isFetching: isTableDataLoading } = useQuery(
    ["getQuoteLineItemsForPdf"],
    () =>
      getQuoteLineItemsForPdf(
        {
          quote_id: quoteId,
          is_edit: false,
        },
        accessToken
      ),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: !!quoteId,
    }
  );

  const {
    data: templateDetails,
    refetch: refetchTemplateDetails,
    isFetching: isTemplateDetailsLoading,
  } = useQuery(
    [API.GET_TEMPLATE_DETAILS, quoteId],
    () =>
      fetchEndpoint(API.GET_TEMPLATE_DETAILS, {
        body: {
          email_id: userEmail,
          quote_id: quoteId,
        },
      }),
    {
      enabled:
        quoteData?.quote_status === STATUS_TYPES.PENDING_SIGNATURE ||
        quoteData?.quote_status === STATUS_TYPES.WON,
      onError: (error) => {
        console.log("error", error);
        if (error && typeof error === "object" && "message" in error) {
          message.error(error.message || "Fetching template details failed");
        } else {
          message.error("Fetching template details failed");
        }
      },
      onSuccess: (data) => {
        if (data.sharing_type === "docusign") {
          setTemplateFileList(data.files_with_urls || []);
        }
        if (!data.sharing_type) {
          setTemplateFileList([]);
        }
      },
    }
  );

  useApolloQuery(GET_All_COUNTRIES, {
    onCompleted: (data) => {
      if (data) {
        let currencySymbolMap = {};
        for (const country of data.allActiveCountries) {
          currencySymbolMap[country.currencyCode] = country.currencySymbol;
        }
        setCurrencyCodeSymbolMap(currencySymbolMap);
      }
    },
  });

  const generatePdfData = useCallback(() => {
    if (
      !templateData?.template ||
      !tableData ||
      !formValues ||
      Object.keys(formValues).length === 0
    ) {
      console.log("Missing required data for PDF generation:", {
        hasTemplateData: !!templateData?.template,
        hasTableData: !!tableData,
        hasFormValues: !!formValues && Object.keys(formValues).length > 0,
      });
      return null;
    }

    const newTemplateData = templateData?.template;
    let tempFormValues = { ...formValues };

    if (tableData) {
      tempFormValues.start_date = tableData?.start_date;
      tempFormValues.end_date = tableData?.end_date;
      tempFormValues.duration_label = tableData?.duration_label;
      tempFormValues.currency = tableData?.currency;
      tempFormValues.quote_total = tableData?.quote_total;
      newTemplateData.tableData = tableData?.table_data;
    }

    Object.keys(tempFormValues).map((key) => {
      if (typeof tempFormValues[key] === "string") {
        if (/^\d{4}-\d{2}-\d{2}$/.test(tempFormValues[key])) {
          const date = new Date(tempFormValues[key]);
          tempFormValues[key] = date.toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
          });
        }
        tempFormValues[key] = tempFormValues[key]?.replace(/\\/g, "\\/");
        tempFormValues[key] = tempFormValues[key]?.replace(/"/g, '\\"');
      }
    });

    let jsonString = JSON.stringify(newTemplateData);
    Object.keys(tempFormValues).map((key) => {
      jsonString = jsonString.replaceAll(
        new RegExp(`{{${key}}}`, "g"),
        tempFormValues?.[key] ?? ""
      );
    });

    const newPdfData = JSON.parse(jsonString);
    newPdfData.termsAndConditions = quoteTerms?.terms;
    return newPdfData;
  }, [templateData, tableData, formValues, quoteTerms]);

  const pdfData = useMemo(() => {
    return generatePdfData();
  }, [generatePdfData]);

  const { data: backendFile, isFetching: isFetchingBackendFile } = useQuery(
    ["fetchBackendFile", quoteId, pdfData],
    async () => {
      const response = await downloadPdf(
        { pdf_data: { ...pdfData, final: true } },
        accessToken
      );
      if (!response.ok) {
        throw new Error("Request failed");
      }
      const blobby = await response.blob();
      const file = new File(
        [blobby],
        quoteData?.quote_name ? `${quoteData?.quote_name}.pdf` : "quote.pdf",
        {
          type: "application/pdf",
        }
      );
      return file;
    },
    {
      enabled: !!pdfData,
      onError: (error) => {
        message.error("Failed to fetch backend PDF");
        console.error("PDF download error:", error);
      },
    }
  );

  const voidEnvelopeMutation = useMutation(
    async ({ reason, envelopeId, userEmail, quoteId }) => {
      return voidEnvelope(
        {
          email_id: userEmail,
          void_reason: reason,
          envelope_id: envelopeId,
          quote_id: quoteId,
        },
        accessToken
      );
    },
    {
      onSuccess: (res, { handleClose }) => {
        message.success("Envelope voided successfully");
        handleClose?.();
        refetchTemplateDetails();
        refetchQuoteDetails();
      },
      onError: (error) => {
        message.error(error?.message || "Failed to void envelope");
        console.error("Envelope voiding error:", error);
      },
    }
  );

  const saveQuoteDetails = useMutation(
    async (variables) => {
      const { payload } = variables || {};
      return await saveQuoteDetailsService(
        "/ninja/cpq/docusign/save/quote_data",
        payload,
        accessToken
      );
    },
    {
      onSuccess: (res, variables) => {
        const { callback } = variables || {};
        if (callback && typeof callback === "function") {
          callback();
        }
      },
      onError: (error) => {
        console.error("Quote details save error:", error);
      },
    }
  );

  const approveRequestMutate = useMutation((payload) =>
    approveRequest(payload, accessToken).then((res) => {
      if (res?.status === "SUCCESS") {
        message.success("Request approved successfully!");
        fetchApproverInstanceData();
        refetchQuoteDetails();
        refetchMultiApprovalDetails();
      } else {
        message.error("Error in approving request. Please try again later.");
      }
    })
  );

  const rejectRequestMutate = useMutation((payload) =>
    rejectRequest(payload, accessToken).then((res) => {
      if (res?.status === "SUCCESS") {
        fetchApproverInstanceData();
        refetchQuoteDetails();
        refetchMultiApprovalDetails();
      } else {
        message.error("Error in rejecting request. Please try again later.");
      }
      return res;
    })
  );

  const retractQuoteRequest = useMutation(
    () => {
      return fetchEndpoint(API.RETRACT_QUOTE, {}, quoteId);
    },
    {
      onSuccess: (res, { cbk }) => {
        message.success("Quote retracted successfully.");
        cbk?.();
        refetchQuoteDetails();
        refetchTemplateDetails();
      },
      onError: () => {
        console.error("Something went wrong");
      },
    }
  );

  const rejectApprovalRequest = (requestData) => {
    const rejectPromise = rejectRequestMutate.mutateAsync(requestData);
    return rejectPromise;
  };

  const handleApproveRequest = (requestId) => {
    const approvePromise = approveRequestMutate.mutateAsync({
      requestId: [requestId],
      entityType: "quote",
    });
    return approvePromise;
  };

  function handleExportPdf(final = false) {
    onExportAsPdf(
      {
        pdf_data: {
          ...pdfData,
          final: final,
          isDraft:
            quoteData?.quote_status === STATUS_TYPES.DRAFT ||
            quoteData?.quote_status === STATUS_TYPES.PENDING_APPROVAL ||
            quoteData?.quote_status === STATUS_TYPES.CANCELLED ||
            quoteData?.quote_status === STATUS_TYPES.REJECTED,
        },
      },
      accessToken,
      quoteData?.quote_name,
      () => {},
      refetchesAfterExport
    );
  }

  const downloadQuotePDF = ({ reason }) => {
    saveQuoteDetails.mutate(
      {
        payload: {
          quote_id: quoteId,
          type: "offline",
          reason,
        },
      },
      {
        onSettled: (data, error) => {
          if (!error) {
            handleExportPdf(true);
          }
        },
      }
    );
  };

  function constructFormData(quoteData) {
    const formSpec = quoteData.form_spec;
    const map = [];
    const newFormValues = {};
    const fieldMap = {};

    for (const key of Object.keys(formSpec.sections)) {
      const section = formSpec.sections[key];
      const simplifiedSection = {};
      const needToAddSection = IGNORED_SECTIONS.has(section.label);
      if (!needToAddSection) {
        simplifiedSection.label = section.label;
      }
      for (const fieldKey of Object.keys(section.fields)) {
        const field = section.fields[fieldKey];
        newFormValues[fieldKey] = field.value;
        fieldMap[fieldKey] = field.label;

        if (fieldKey === "field5") {
          setAccountName(field.value);
        }
        if (fieldKey === "field4") {
          setOpportunityId(field.value);
        }
        if (field.properties?.is_hidden) {
          continue;
        }
        if (!needToAddSection) {
          simplifiedSection[fieldKey] = {
            value: field.value,
            label: field.label,
          };
        }
      }

      if (!needToAddSection) {
        map.push(simplifiedSection);
      }
    }
    setFormValues(newFormValues);
    setFormDataMap(map);
    setFieldMap(fieldMap);
  }

  function handleOnVoidEnvelope(reason, handleClose) {
    voidEnvelopeMutation.mutate({
      reason,
      envelopeId: templateDetails?.envelope_id,
      userEmail: userEmail,
      quoteId: quoteData.quote_id,
      handleClose,
    });
  }

  function refetchesAfterExport() {
    refetchQuoteDetails();
    refetchTemplateDetails();
  }

  return {
    quoteData,
    isQuoteDataLoading,
    accessToken,
    saveQuoteDetails,
    templateDetails,
    formDataMap,
    voidEnvelopeMutation,
    accountName,
    opportunityId,
    pdfData,
    tableData,
    isTableDataLoading,
    backendFile,
    isFetchingBackendFile,
    templateFileList,
    isTemplateDetailsLoading,
    currencyCodeSymbolMap,
    multiApprovalDetails,
    isApprovalDetailsLoading,
    approverInstanceData,
    isApprovalRequestLoading: approveRequestMutate.isLoading,
    quoteTerms: replaceFieldValues(quoteTerms?.terms),
    isQuoteTermsLoading,
    publicLinkData,
    isPublicLinkLoading,
    hasPermissions,
    retractQuoteRequest,

    refetchPublicLink,
    handleApproveRequest,
    rejectApprovalRequest,
    fetchApproverInstanceData,
    refetchTemplateDetails,
    refetchQuoteDetails,
    handleOnVoidEnvelope,
    downloadQuotePDF,
    handleExportPdf,
  };
}
