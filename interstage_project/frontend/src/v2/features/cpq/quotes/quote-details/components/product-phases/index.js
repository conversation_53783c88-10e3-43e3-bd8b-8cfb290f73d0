import { AnimatePresence } from "framer-motion";
import React from "react";

import { EverNumberBadge, EverTg } from "~/v2/components";
import {
  AnimatedWrapper,
  ANIMATION_STATES,
  ANIMATION_TYPES,
} from "~/v2/features/cpq/components";
import { AgGridLoaderTable } from "~/v2/features/cpq/components/ag-grid/AgGridLoaderTable";

import { PhaseDetail } from "./PhaseDetail";
import { useQuoteDetails } from "../../hook/useQuoteDetails";
import { PRODUCT_TABLE_SKELETON_COLUMN_DEFS } from "../../utils";

export function ProductPhases() {
  const { tableData, quoteData, isTableDataLoading } = useQuoteDetails();
  return (
    <div className="space-y-4 pl-1">
      <div className="flex gap-2 items-center">
        <EverTg.Heading4>PRODUCTS</EverTg.Heading4>
        <EverNumberBadge
          count={quoteData?.product_count || 0}
          className="text-ever-primary bg-ever-primary/10 border-ever-primary/30 border border-solid"
        />
      </div>
      <AnimatePresence initial={false}>
        {isTableDataLoading ? (
          <AgGridLoaderTable
            columnDefs={PRODUCT_TABLE_SKELETON_COLUMN_DEFS}
            rowCount={6}
            agGridProps={{
              rowHeight: 32,
              headerHeight: 32,
            }}
          />
        ) : (
          tableData?.phases.map((phase, index) => (
            <AnimatedWrapper.Motion
              key={phase.phase_id}
              className={tableData?.phases.length === 0 ? "!h-full" : ""}
              initial={ANIMATION_STATES.EXPAND}
              type={ANIMATION_TYPES.EXPAND_COLLAPSE}
              transition={{ duration: 0.6 }}
            >
              <PhaseDetail
                isMultiPhase={tableData?.phases.length > 1}
                phase={phase}
                phaseIndex={index}
                defaultOpen={index === 0}
              />
            </AnimatedWrapper.Motion>
          ))
        )}
      </AnimatePresence>
    </div>
  );
}
