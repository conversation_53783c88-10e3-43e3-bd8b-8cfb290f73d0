import {
  AlertTriangleIcon,
  MessageTextSquareIcon,
} from "@everstage/evericons/duotone";
import { CheckIcon, XCloseIcon, FileIcon } from "@everstage/evericons/outlined";
import { format } from "date-fns";
import { motion, AnimatePresence } from "framer-motion";
import { debounce } from "lodash";
import React, { useState } from "react";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { APPROVAL_ENTITY_TYPES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverDivider,
  EverGroupAvatar,
  EverTg,
  EverTooltip,
} from "~/v2/components";
import RejectApprovalModal from "~/v2/features/approvals/approval-requests/payout-approvals/approval-action-modals/RejectModal";

import { TruncatedText } from "../../../components";
import { STATUS_TYPES } from "../../quotes-list/utils";
import {
  STATUS_CLASS_NAMES,
  COMMENT_DIVIDER_BACKGROUND_COLOR,
  APPROVAL_STATUS_BADGE_ITEMS,
  containerVariants,
  itemVariants,
} from "../constants";
import { useQuoteDetails } from "../hook/useQuoteDetails";

// CONDITION TO SHOW APPROVAL ACTION COMPONENT
// 1. IF QUOTE STATUS IS NOT APPROVED OR REJECTED
// 2. IF APPROVAL REQUESTS ARE NOT EMPTY (ie: LOGGED IN USER IS APPROVER)

export function ApprovalAction() {
  const {
    approverInstanceData,
    currencyCodeSymbolMap,
    isApprovalRequestLoading,
    handleApproveRequest,
    rejectApprovalRequest,
    fetchApproverInstanceData,
    quoteData,
  } = useQuoteDetails();
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  if (
    (quoteData?.quote_status !== STATUS_TYPES.PENDING_APPROVAL &&
      quoteData?.quote_status !== STATUS_TYPES.REJECTED) ||
    !approverInstanceData?.approval_requests ||
    approverInstanceData?.approval_requests?.length === 0
  ) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className="space-y-4"
      >
        {approverInstanceData.approval_requests.map((item) => (
          <motion.div
            key={item.approval_request_id}
            variants={itemVariants}
            layout
          >
            <ActionBox
              item={item}
              clientFeatures={clientFeatures}
              currencyCodeSymbolMap={currencyCodeSymbolMap}
              isApprovalRequestLoading={isApprovalRequestLoading}
              handleApproveRequest={handleApproveRequest}
              rejectApprovalRequest={rejectApprovalRequest}
              fetchApproverInstanceData={fetchApproverInstanceData}
            />
          </motion.div>
        ))}
      </motion.div>
    </AnimatePresence>
  );
}

function ActionBox({
  item,
  clientFeatures,
  currencyCodeSymbolMap,
  isApprovalRequestLoading,
  handleApproveRequest,
  rejectApprovalRequest,
  fetchApproverInstanceData,
}) {
  const { isLoggedInAsUser, name } = useAuthStore();
  const [rejectModalVisibility, setRejectModalVisibility] = useState(false);

  const debouncedApproveRequest = debounce((approvalRequestId) => {
    handleApproveRequest(approvalRequestId);
  }, 300);

  const getApprovalDate = (status) => {
    if (status === "requested") {
      return item.due_date;
    }
    return item?.completed_time
      ? format(new Date(item.completed_time), "MMM dd, yyyy")
      : "";
  };

  return (
    <>
      <div
        className={twMerge(
          STATUS_CLASS_NAMES.common,
          STATUS_CLASS_NAMES[item.status]
        )}
      >
        <div className="flex items-center gap-2">
          <TruncatedText text={item.rule_group_name} className="max-w-[160px]">
            <EverTg.Heading4>{item.rule_group_name}</EverTg.Heading4>
          </TruncatedText>
          <EverDivider type="vertical" />
          <EverTg.Text>
            {item.status === "requested"
              ? `Due on ${getApprovalDate(item.status)}`
              : item.status === "approved"
              ? `Approved on ${getApprovalDate(item.status)}`
              : `Rejected on ${getApprovalDate(item.status)}`}
          </EverTg.Text>
        </div>
        {item.exception_name && item.exception_name !== "Always include" && (
          <div className="space-y-1">
            <div className="flex gap-1.5 items-center">
              <AlertTriangleIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.SubHeading4 className="text-ever-base-content">
                Exception to Review:
              </EverTg.SubHeading4>
            </div>
            <ul className="list-disc pl-5 mb-0">
              <li>
                <EverTg.Text className="break-word">
                  {item.exception_name}
                </EverTg.Text>
              </li>
            </ul>
          </div>
        )}
        {item.approval_message && (
          <div className="space-y-1.5">
            <div className="flex gap-1.5 items-center">
              <MessageTextSquareIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.SubHeading4 className="text-ever-base-content">
                Note:
              </EverTg.SubHeading4>
            </div>
            <div className="flex gap-1.5 pl-1">
              <div
                className={twMerge(
                  "!w-1 rounded-md flex-shrink-0",
                  COMMENT_DIVIDER_BACKGROUND_COLOR[item.status]
                )}
              />
              <EverTooltip
                title={
                  item.approval_message.length > 200
                    ? item.approval_message
                    : ""
                }
              >
                <EverTg.Text className="break-words whitespace-normal line-clamp-4">
                  {item.approval_message}
                </EverTg.Text>
              </EverTooltip>
            </div>
          </div>
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <EverGroupAvatar
              avatars={[
                {
                  name: item.quote_owner_name,
                  image: item.quote_owner_profile_picture,
                  className: "w-6 h-6",
                },
              ]}
              groupMaxCount={1}
              limitInPopover={1}
            />
            <EverTg.SubHeading4 className="text-ever-base-content">
              {item.quote_owner_name}
            </EverTg.SubHeading4>
          </div>
          {item.status === "requested" ? (
            <div className="flex gap-2">
              <EverButton
                type="filled"
                color="error"
                size="small"
                className="!bg-ever-error-ring !text-ever-error-lite-content"
                prependIcon={<XCloseIcon />}
                disabled={isLoggedInAsUser || isApprovalRequestLoading}
                tooltipTitle={
                  isLoggedInAsUser
                    ? `You cannot respond on behalf of  ${name}`
                    : ""
                }
                onClick={() => setRejectModalVisibility(true)}
              >
                Reject
              </EverButton>
              <EverButton
                type="filled"
                color="success"
                size="small"
                className="!bg-ever-success-ring !text-ever-success-lite-content"
                prependIcon={<CheckIcon />}
                disabled={isLoggedInAsUser || isApprovalRequestLoading}
                tooltipTitle={
                  isLoggedInAsUser
                    ? `You cannot respond on behalf of  ${name}`
                    : ""
                }
                onClick={() =>
                  debouncedApproveRequest(item.approval_request_id)
                }
              >
                Approve
              </EverButton>
            </div>
          ) : (
            <EverTooltip
              title={item.status === "rejected" ? item.rejection_reason : ""}
            >
              <div
                className={twMerge(
                  "flex gap-2 px-3 py-1.5 rounded-lg shadow-sm items-center",
                  APPROVAL_STATUS_BADGE_ITEMS[item.status]?.containerClassName
                )}
              >
                {APPROVAL_STATUS_BADGE_ITEMS[item.status].icon}
                <EverTg.SubHeading4
                  className={
                    APPROVAL_STATUS_BADGE_ITEMS[item.status].textClassName
                  }
                >
                  {APPROVAL_STATUS_BADGE_ITEMS[item.status].text}
                </EverTg.SubHeading4>
                {item.status === "rejected" && (
                  <FileIcon className="text-ever-error w-5 h-5" />
                )}
              </div>
            </EverTooltip>
          )}
        </div>
      </div>
      {rejectModalVisibility && (
        <RejectApprovalModal
          rejectModalData={{
            approvalRequestId: item.approval_request_id,
            profilePicture: null,
            fullName: item.quote_owner_name,
            employeeEmailId: item.quote_owner_email,
            currency: item.currency,
            payoutAmount: item.total_quote_value,
            period: item.requested_time,
          }}
          modalVisibility={rejectModalVisibility}
          closeModal={() => {
            setRejectModalVisibility(false);
          }}
          customCalendar={clientFeatures?.customCalendar}
          store={{
            currencyCodeSymbolMap: currencyCodeSymbolMap,
            rejectApprovalRequest,
          }}
          refetchData={fetchApproverInstanceData}
          entityType={APPROVAL_ENTITY_TYPES.QUOTE}
        />
      )}
    </>
  );
}
