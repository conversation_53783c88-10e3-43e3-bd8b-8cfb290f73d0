import { PencilLineIcon } from "@everstage/evericons/duotone";
import {
  ChevronDownIcon,
  CopyIcon,
  SlashCircleIcon,
  StarIcon,
  // ThumbsDownIcon,
  ThumbsUpIcon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import { DotsVerticalIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { RBAC_ROLES } from "~/Enums";
import {
  EverBadge,
  EverButton,
  EverLoader,
  EverModal,
  EverTg,
  EverTooltip,
  message,
} from "~/v2/components";

import { ShareQuoteWrapper } from "./ShareQuoteWrapper";
import { StatusProgressBar } from "./StatusProgressBar";
import { MarkAsWonModal } from "../../build-quote/components";
import { VoidEnvelopeModal } from "../../build-quote/components/VoidEnvelopModal";
import WonCelebrationModal, {
  handleConfetti,
} from "../../build-quote/components/WonCelebrationModal";
import {
  createQuote,
  deleteQuote,
  markQuoteAsPrimary,
} from "../../quotes-list/services/restApi";
import {
  REMOVE_CONFIRMATION_MESSAGES,
  RETRACT_QUOTE_CONFIRMATION_MESSAGES,
  RETRACT_QUOTE_TOOLTIPS,
  STATUS_TYPES,
} from "../../quotes-list/utils";
import { useQuoteDetails } from "../hook/useQuoteDetails";
import { getStatusBadgeProps } from "../utils";

const QUOTE_OPTION_VALUES = {
  MARK_AS_PRIMARY: "markAsPrimary",
  MARK_AS_LOST: "markAsLost",
  CLONE: "clone",
  DELETE: "delete",
};

const commonProps = {
  color: "primary",
  type: "filled",
  size: "small",
};

export function Header() {
  const {
    quoteData,
    accessToken,
    saveQuoteDetails,
    refetchQuoteDetails,
    templateDetails,
    accountName,
    isQuoteDataLoading,
    handleExportPdf,
    hasPermissions,
    retractQuoteRequest,
    handleOnVoidEnvelope,
    voidEnvelopeMutation,
  } = useQuoteDetails();
  const navigate = useNavigate();

  const [showMarkAsWonFormModal, setShowMarkAsWonFormModal] = useState(false);
  const [showMarkAsWonModal, setShowMarkAsWonModal] = useState(false);
  const [showVoidEnvelopeModal, setShowVoidEnvelopeModal] = useState(false);

  const dropdownOptions = [
    {
      label: "Mark as Primary",
      value: QUOTE_OPTION_VALUES.MARK_AS_PRIMARY,
      icon: <StarIcon className="w-4 h-4 text-ever-base-content-mid" />,
      permission: RBAC_ROLES.EDIT_QUOTES,
    },
    // {
    //     label: "Mark as Lost",
    //     value: QUOTE_OPTION_VALUES.MARK_AS_LOST,
    //     icon: <ThumbsDownIcon className="w-4 h-4 text-ever-base-content-mid" />,
    //     permission: RBAC_ROLES.EDIT_QUOTES,
    // },
    {
      label: "Clone",
      value: QUOTE_OPTION_VALUES.CLONE,
      icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />,
      permission: RBAC_ROLES.CREATE_QUOTES,
    },
    {
      label: "Delete",
      value: QUOTE_OPTION_VALUES.DELETE,
      icon: <Trash03Icon className="w-4 h-4 text-ever-error" />,
      permission: RBAC_ROLES.DELETE_QUOTES,
    },
  ];

  const handleMenuItemClick = async (item, quoteId) => {
    try {
      switch (item) {
        case QUOTE_OPTION_VALUES.MARK_AS_PRIMARY: {
          await markQuoteAsPrimary(accessToken, { quote_id: quoteId });
          message.success("Quote marked as primary successfully");
          refetchQuoteDetails();
          break;
        }
        case QUOTE_OPTION_VALUES.CLONE: {
          const payload = {
            quote_id: quoteId,
            request_type: "clone",
            form_builder_id: quoteData?.form_builder_id,
          };
          await createQuote(accessToken, payload).then((res) => {
            if (res.new_quote_id) {
              message.success("Quote cloned successfully");
              navigate(`/cpq/quotes/${res.new_quote_id}`);
            }
          });
          break;
        }
        case QUOTE_OPTION_VALUES.DELETE: {
          const modal = EverModal.error({
            title: REMOVE_CONFIRMATION_MESSAGES.title,
            subtitle: REMOVE_CONFIRMATION_MESSAGES.subtitle,
            confirmationButtons: [
              <EverButton
                key="cancel"
                type="filled"
                color="base"
                onClick={() => closeModal()}
              >
                Cancel
              </EverButton>,
              <EverButton
                key="confirm"
                onClick={async () => {
                  await deleteQuote(accessToken, { quote_id: quoteId });
                  message.success("Quote deleted successfully");
                  closeModal();
                  navigate("/cpq/quotes");
                }}
                type="filled"
                color="error"
              >
                Yes, delete
              </EverButton>,
            ],
          });
          const closeModal = () => {
            modal.destroy();
          };

          break;
        }
        default: {
          break;
        }
      }
    } catch (error) {
      let errorMessage = "An error occurred. Please try again later.";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      message.error(errorMessage);
    }
  };

  const dropdownMenu = () => {
    return (
      <Menu>
        {dropdownOptions.map((option) => {
          if (!hasPermissions(option.permission)) return null;
          return (
            <Menu.Item
              key={option.value}
              className={
                option.value === "delete"
                  ? "hover:!bg-ever-error-lite !p-0"
                  : "!p-0"
              }
              onClick={() => {
                handleMenuItemClick(option.value, quoteData?.quote_id);
              }}
              disabled={
                option.value === QUOTE_OPTION_VALUES.MARK_AS_PRIMARY &&
                quoteData?.is_primary
              }
            >
              <EverTooltip
                title={
                  option.value === QUOTE_OPTION_VALUES.MARK_AS_PRIMARY &&
                  quoteData?.is_primary
                    ? "Quote is already marked as primary."
                    : ""
                }
              >
                <div className="flex items-center gap-2 h-full px-4">
                  {option.icon}
                  <EverTg.Caption
                    className={
                      option.value === "delete" ? "text-ever-error" : ""
                    }
                  >
                    {option.label}
                  </EverTg.Caption>
                </div>
              </EverTooltip>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  };

  const handleMarkAsWonSubmit = (file) => {
    const formData = new FormData();
    formData.append("quote_id", quoteData?.quote_id);
    formData.append("type", "offline");
    formData.append("reason", templateDetails?.sharing_reason || "");
    formData.append(file.name, file);

    // Send both the file and other data to the API
    saveQuoteDetails.mutate({
      payload: formData,
      callback: () => {
        setShowMarkAsWonFormModal(false);
        setShowMarkAsWonModal(true);
        handleConfetti();
        refetchQuoteDetails();
      },
    });
  };

  function showEditButton() {
    if (!hasPermissions(RBAC_ROLES.EDIT_QUOTES)) return false;
    if (
      quoteData?.quote_status === STATUS_TYPES.WON ||
      quoteData?.quote_status === STATUS_TYPES.PENDING_SIGNATURE
    )
      return false;

    return true;
  }

  function handleRetractQuote() {
    if (templateDetails?.sharing_type === "docusign") {
      handleOpenVoidEnvelopeModal();
    } else if (templateDetails?.sharing_type === "offline") {
      const modal = EverModal.error({
        title: RETRACT_QUOTE_CONFIRMATION_MESSAGES.title,
        subtitle: RETRACT_QUOTE_CONFIRMATION_MESSAGES.subtitle,
        confirmationButtons: [
          <EverButton
            key="cancel"
            type="filled"
            color="base"
            onClick={() => closeModal()}
            disabled={retractQuoteRequest.loading}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="confirm"
            onClick={() => {
              retractQuoteRequest.mutate({ cbk: () => closeModal() });
            }}
            type="filled"
            color="error"
            disabled={retractQuoteRequest.loading}
            loading={retractQuoteRequest.loading}
          >
            Yes, retract
          </EverButton>,
        ],
      });
      const closeModal = () => {
        modal.destroy();
      };
    }
  }

  const handleOpenVoidEnvelopeModal = () => {
    setShowVoidEnvelopeModal(true);
  };

  const handleCloseVoidEnvelopeModal = () => {
    setShowVoidEnvelopeModal(false);
  };

  return (
    <>
      <div className="sticky top-0 z-10">
        <div className="w-full flex justify-between px-8 pt-3 pb-4 bg-ever-base-50 border-t-0 border-b border-ever-base-400">
          <div className="flex flex-col gap-1.5">
            <div className="flex items-center gap-2.5">
              {isQuoteDataLoading ? (
                <EverLoader.Skeleton config={[7]} className="h-5 my-1 w-20" />
              ) : (
                <EverTg.Heading2>{quoteData?.quote_name}</EverTg.Heading2>
              )}
              {isQuoteDataLoading ? (
                <EverLoader.Skeleton config={[7]} className="h-5 my-1 w-20" />
              ) : quoteData?.quote_status === STATUS_TYPES.WON ? (
                getStatusBadgeProps(quoteData?.quote_status).render
              ) : (
                <div className="flex gap-2">
                  {(quoteData?.quote_status ===
                    STATUS_TYPES.PENDING_SIGNATURE &&
                    templateDetails?.sharing_type) === "offline" && (
                    <EverBadge {...getStatusBadgeProps("Offline")} />
                  )}
                  <EverBadge
                    {...getStatusBadgeProps(quoteData?.quote_status)}
                  />
                </div>
              )}
            </div>
            <EverTg.Text className="text-ever-base-content">
              This document outlines the quote provided to Acme Corp, including
              detailed pricing and service descriptions.
            </EverTg.Text>
          </div>

          <div className="flex items-center gap-3">
            {hasPermissions(RBAC_ROLES.EDIT_QUOTES) &&
              quoteData?.quote_status === STATUS_TYPES.PENDING_SIGNATURE && (
                <EverButton
                  size="small"
                  color="base"
                  type="ghost"
                  prependIcon={
                    <SlashCircleIcon className="text-ever-base-content-mid" />
                  }
                  onClick={handleRetractQuote}
                  disabled={!quoteData?.quote_id}
                  tooltipPlacement="left"
                  tooltipTitle={
                    RETRACT_QUOTE_TOOLTIPS[templateDetails?.sharing_type]
                  }
                >
                  Retract Quote
                </EverButton>
              )}
            {showEditButton() && (
              <EverButton
                size="small"
                color="base"
                type="ghost"
                prependIcon={
                  <PencilLineIcon className="text-ever-base-content-mid" />
                }
                onClick={() => {
                  navigate(`/cpq/quotes/${quoteData?.quote_id}`, {
                    state: {
                      fromQuoteDetails: true,
                      isEditMode: true,
                    },
                  });
                }}
                disabled={!quoteData?.quote_id || isQuoteDataLoading}
                tooltipPlacement="left"
              >
                Edit Quote
              </EverButton>
            )}
            {quoteData?.quote_status === STATUS_TYPES.DRAFT && (
              <EverButton {...commonProps}>Publish</EverButton>
            )}
            {hasPermissions(RBAC_ROLES.EDIT_QUOTES) &&
              (quoteData?.quote_status === STATUS_TYPES.PENDING_APPROVAL ||
                quoteData?.quote_status === STATUS_TYPES.APPROVED) && (
                <ShareQuoteWrapper
                  triggerComponent={
                    <EverTooltip
                      title={
                        quoteData?.quote_status ===
                          STATUS_TYPES.PENDING_APPROVAL &&
                        "Quote is not yet approved."
                      }
                    >
                      <div
                        className={twMerge(
                          "flex px-3 items-center gap-2 bg-ever-primary rounded-md h-8 hover:cursor-pointer text-ever-base-25",
                          quoteData?.quote_status ===
                            STATUS_TYPES.PENDING_APPROVAL &&
                            "bg-transparent border border-ever-base-200 text-ever-base-content-mid/70"
                        )}
                      >
                        <EverTg.SubHeading4 className="text-inherit">
                          Send to Customer
                        </EverTg.SubHeading4>
                        <ChevronDownIcon className="w-4 h-4 text-inherit" />
                      </div>
                    </EverTooltip>
                  }
                  downloadPdf={handleExportPdf}
                  dropdownDisabled={
                    quoteData?.quote_status === STATUS_TYPES.PENDING_APPROVAL
                  }
                />
              )}
            {hasPermissions(RBAC_ROLES.EDIT_QUOTES) &&
              quoteData?.quote_status === STATUS_TYPES.PENDING_SIGNATURE &&
              templateDetails?.sharing_type === "offline" && (
                <EverButton
                  {...commonProps}
                  prependIcon={<ThumbsUpIcon />}
                  onClick={() => setShowMarkAsWonFormModal(true)}
                >
                  Mark as Won
                </EverButton>
              )}
            {hasPermissions([
              RBAC_ROLES.EDIT_QUOTES,
              RBAC_ROLES.CREATE_QUOTES,
              RBAC_ROLES.DELETE_QUOTES,
            ]) && (
              <Dropdown trigger={["click"]} overlay={dropdownMenu()}>
                <EverButton.Icon
                  size="small"
                  color="base"
                  type="filled"
                  icon={<DotsVerticalIcon />}
                />
              </Dropdown>
            )}
          </div>
        </div>
        <StatusProgressBar />
      </div>
      {showMarkAsWonFormModal && (
        <MarkAsWonModal
          visible={showMarkAsWonFormModal}
          onCancel={() => setShowMarkAsWonFormModal(false)}
          templateDetails={{
            sharing_reason: templateDetails?.sharing_reason,
          }}
          onSubmit={handleMarkAsWonSubmit}
          buttonLoading={saveQuoteDetails.isLoading}
        />
      )}
      {showVoidEnvelopeModal && (
        <VoidEnvelopeModal
          visible={showVoidEnvelopeModal}
          handleClose={handleCloseVoidEnvelopeModal}
          handleOnVoidEnvelope={handleOnVoidEnvelope}
          isLoading={voidEnvelopeMutation.isLoading}
        />
      )}
      {showMarkAsWonModal && (
        <WonCelebrationModal
          showMarkAsWonModal={showMarkAsWonModal}
          setShowMarkAsWonModal={setShowMarkAsWonModal}
          basicDetails={{
            ownerName: quoteData?.quote_owner,
            quoteTotal: quoteData?.quote_total,
            ownerId: quoteData?.quote_owner_id,
            accountName: accountName,
          }}
        />
      )}
    </>
  );
}
