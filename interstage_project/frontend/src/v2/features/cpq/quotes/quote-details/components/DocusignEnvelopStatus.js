import React from "react";

import { EnvelopStatus } from "../../build-quote/components/EnvelopStatus";
import { STATUS_TYPES } from "../../quotes-list/utils";
import { useQuoteDetails } from "../hook/useQuoteDetails";

export function DocusignEnvelopStatus() {
  const { quoteData, templateDetails } = useQuoteDetails();

  return (
    <>
      {quoteData?.quote_status === STATUS_TYPES.PENDING_SIGNATURE &&
        templateDetails?.sharing_type === "docusign" && (
          <EnvelopStatus
            containerClassName="w-full bg-ever-base-25 border border-solid border-ever-base-400 px-4 pt-4 pb-5"
            title="Pending Signature"
            pendingDays={templateDetails?.days_left}
            actionButton={<></>}
            timeline={(templateDetails?.template_recipients || []).map(
              (item) => ({
                name: item.name,
                email: item.email,
                signingPreference: item.signingPreference,
                id: item.id,
              })
            )}
          />
        )}
    </>
  );
}
