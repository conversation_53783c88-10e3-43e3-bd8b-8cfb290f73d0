import React, { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";

import {
  AnimatedWrapper,
  ANIMATION_STATES,
  ANIMATION_TYPES,
} from "~/v2/features/cpq/components";

import { PhaseHeader } from "./PhaseHeader";
import { PhaseTable } from "./PhaseTable";

export function PhaseDetail({ isMultiPhase, phase, phaseIndex, defaultOpen }) {
  const [expand, setExpand] = useState(defaultOpen);

  useEffect(() => {
    setExpand(defaultOpen || !isMultiPhase);
  }, [isMultiPhase]);

  return (
    <div
      className={twMerge(
        "h-full transition-all duration-300",
        isMultiPhase
          ? "px-3 py-2 border border-solid border-ever-base-400 rounded-lg bg-ever-base-50/70 cursor-pointer min-h-10 hover:bg-ever-base-100 hover:shadow-md"
          : "",
        expand && "pt-3 space-y-1",
        isMultiPhase && expand && "bg-ever-base-100"
      )}
      onClick={() => {
        isMultiPhase && setExpand(!expand);
      }}
    >
      <AnimatedWrapper
        isVisible={isMultiPhase}
        motionProps={{
          type: ANIMATION_TYPES.EXPAND_COLLAPSE,
        }}
      >
        <PhaseHeader phase={phase} expand={expand} />
      </AnimatedWrapper>
      <AnimatedWrapper
        isVisible
        motionProps={{
          type: ANIMATION_TYPES.EXPAND_COLLAPSE,
          initial: ANIMATION_STATES.EXPAND,
          animate: expand ? ANIMATION_STATES.EXPAND : ANIMATION_STATES.COLLAPSE,
          className: "flex flex-col gap-2",
        }}
      >
        <div className={isMultiPhase ? "mt-2" : ""}>
          <PhaseTable phaseIndex={phaseIndex} />
        </div>
      </AnimatedWrapper>
    </div>
  );
}
