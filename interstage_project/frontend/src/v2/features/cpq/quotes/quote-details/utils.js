import {
  CalendarMinus02Icon,
  CheckCircleIcon,
  Edit04Icon,
  EditIcon,
  EditPencilIcon,
  HourglassIcon,
  MinusCircleIcon,
  ThumbsDownIcon,
  UsersCheckIcon,
  XCircleIcon,
  EditBoxIcon,
} from "@everstage/evericons/solid";
import React from "react";
import { twMerge } from "tailwind-merge";

import { EverLoader } from "~/v2/components";

import {
  SingleSkeletonCell,
  SkeletonCellHeader,
} from "../../components/ag-grid/AgGridLoaderTable";
import { STATUS_TYPES } from "../quotes-list/utils";

const commonBadgeClassNames = "rounded-3xl h-[26px]";

export const badgeClassNames = {
  successTone: twMerge(
    "text-ever-success-lite-content border-ever-success/30 bg-ever-success-lite",
    commonBadgeClassNames
  ),
  errorTone: twMerge(
    "text-ever-error-lite-content border-ever-error/30 bg-ever-error-lite",
    commonBadgeClassNames
  ),
  infoTone: twMerge(
    "text-ever-info-lite-content border-ever-info/30 bg-ever-info-lite",
    commonBadgeClassNames
  ),
  warningTone: twMerge(
    "text-ever-warning-lite-content border-ever-warning/30 bg-ever-warning-lite",
    commonBadgeClassNames
  ),
};

export function getStatusBadgeProps(status) {
  switch (status) {
    case STATUS_TYPES.DRAFT: {
      return {
        title: STATUS_TYPES.DRAFT,
        icon: <EditPencilIcon className="w-4 h-4 text-ever-chartColors-22" />,
        className: twMerge(
          "text-ever-chartColors-29 bg-ever-chartColors-22/20 border-ever-chartColors-29/20",
          commonBadgeClassNames
        ),
      };
    }
    case STATUS_TYPES.PUBLISHED: {
      return {
        title: STATUS_TYPES.PUBLISHED,
        icon: <CheckCircleIcon className="w-4 h-4 text-ever-success" />,
        className: badgeClassNames.successTone,
      };
    }
    case STATUS_TYPES.PENDING_APPROVAL: {
      return {
        title: STATUS_TYPES.PENDING_APPROVAL,
        icon: <HourglassIcon className="w-4 h-4 text-ever-info" />,
        className: badgeClassNames.infoTone,
      };
    }
    case STATUS_TYPES.NEED_ATTENTION: {
      return {
        title: STATUS_TYPES.NEED_ATTENTION,
        icon: <HourglassIcon className="w-4 h-4 text-ever-warning" />,
        className: badgeClassNames.warningTone,
      };
    }
    case STATUS_TYPES.APPROVED: {
      return {
        title: STATUS_TYPES.APPROVED,
        icon: <UsersCheckIcon className="w-4 h-4 text-ever-success" />,
        className: badgeClassNames.successTone,
      };
    }
    case STATUS_TYPES.REJECTED: {
      return {
        title: STATUS_TYPES.REJECTED,
        icon: <MinusCircleIcon className="w-4 h-4 text-ever-error" />,
        className: badgeClassNames.errorTone,
      };
    }
    case STATUS_TYPES.PENDING_SIGNATURE: {
      return {
        title: STATUS_TYPES.PENDING_SIGNATURE,
        icon: <EditIcon className="w-4 h-4 text-ever-info" />,
        className: badgeClassNames.infoTone,
      };
    }
    case "Won": {
      return {
        type: "custom",
        render: (
          <div className="status-badge-wrapper">
            <div className="status-badge">
              <span className="status-badge-text">{status}</span>
              <span>🎉</span>
            </div>
          </div>
        ),
      };
    }
    case STATUS_TYPES.LOST: {
      return {
        title: STATUS_TYPES.LOST,
        icon: <ThumbsDownIcon className="w-4 h-4 text-ever-error" />,
        className: badgeClassNames.errorTone,
      };
    }
    case STATUS_TYPES.CANCELLED: {
      return {
        title: STATUS_TYPES.CANCELLED,
        icon: <XCircleIcon className="w-4 h-4 text-ever-error" />,
        className: badgeClassNames.errorTone,
      };
    }
    case STATUS_TYPES.EXPIRED: {
      return {
        title: STATUS_TYPES.EXPIRED,
        icon: <CalendarMinus02Icon className="w-4 h-4 text-ever-error" />,
        className: badgeClassNames.errorTone,
      };
    }
    case STATUS_TYPES.SIGNED_OFFLINE: {
      return {
        title: STATUS_TYPES.SIGNED_OFFLINE,
        icon: <Edit04Icon className="w-4 h-4 text-ever-info" />,
        className: twMerge(
          "text-ever-chartColors-20 border-ever-chartColors-20/30 bg-ever-chartColors-20/10",
          commonBadgeClassNames
        ),
      };
    }
    case "Offline": {
      return {
        title: "Offline",
        icon: <EditBoxIcon className="w-4 h-4 text-ever-chartColors-20" />,
        className: twMerge(
          "text-ever-chartColors-20 border-ever-chartColors-20/30 bg-ever-chartColors-20/10",
          commonBadgeClassNames
        ),
      };
    }
    default: {
      return {
        title: status,
        icon: <EditPencilIcon className="w-4 h-4 text-ever-info" />,
        className: badgeClassNames.infoTone,
      };
    }
  }
}

export const PRODUCT_TABLE_SKELETON_COLUMN_DEFS = [
  {
    field: "0",
    minWidth: 320,
    headerComponent: SkeletonCellHeader,
    cellRenderer: () => {
      return (
        <div className="flex flex-col gap-2">
          <EverLoader.Skeleton
            config={[2]}
            className="h-3.5 !p-0 !pl-1 !w-52"
          />
        </div>
      );
    },
  },
  {
    field: "1",
    headerComponent: SkeletonCellHeader,
    minWidth: 216,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "5",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "3",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "4",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
];
