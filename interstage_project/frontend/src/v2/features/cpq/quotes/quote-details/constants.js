import { gql } from "@apollo/client";
import { CheckCircleIcon, XCircleIcon } from "@everstage/evericons/solid";

export const API = {
  GET_QUOTE_DETAILS: "GET_QUOTE_DETAILS",
  GET_TEMPLATE_DETAILS: "GET_TEMPLATE_DETAILS",
  GET_MULTI_APPROVAL_DETAILS: "GET_MULTI_APPROVAL_DETAILS",
  GET_QUOTE_TERMS: "GET_QUOTE_TERMS",
  RETRACT_QUOTE: "RETRACT_QUOTE",
};

export const API_ENDPOINTS = {
  [API.GET_QUOTE_DETAILS]: ({ quoteId }) => ({
    url: `/ninja/cpq/quotes/details/${quoteId}`,
    method: "GET",
  }),
  [API.GET_TEMPLATE_DETAILS]: {
    url: `/ninja/cpq/docusign/quote/template_details`,
    method: "POST",
  },
  [API.GET_MULTI_APPROVAL_DETAILS]: ({ quoteId }) => ({
    url: `/ninja/cpq/approvals/get_multi_approval_details/${quoteId}`,
    method: "GET",
  }),
  [API.GET_QUOTE_TERMS]: (quoteId) => ({
    url: `/ninja/cpq/quotes/terms/${quoteId}`,
    method: "GET",
  }),
  [API.RETRACT_QUOTE]: (quoteId) => ({
    url: `/ninja/cpq/quotes/retract/${quoteId}`,
    method: "POST",
  }),
};

export const PROGRESS_STATE = {
  COMPLETED: "completed",
  IN_PROGRESS: "in_progress",
  NOT_STARTED: "not_started",
  FAILED: "failed",
  WARNING: "warning",
};

export const PROGRESS_STATE_LABELS = {
  QUOTE_STATUS_IN_PROGRESS: "Draft",
  QUOTE_STATUS_COMPLETE: "Published",
  APPROVAL_STATUS_IN_PROGRESS: "Approval",
  APPROVAL_STATUS_COMPLETED: "Approved",
  CUSTOMER_STATUS_IN_PROGRESS: "Sent to Customer",
  CUSTOMER_STATUS_COMPLETED: "Sent to Customer",
  WON_STATUS_IN_PROGRESS: "Won 🎉",
  WON_STATUS_COMPLETED: "Won 🎉",
};

export const APPROVAL_STATUS_TYPES = {
  APPROVED: "approved",
  REJECTED: "rejected",
  REQUESTED: "requested",
};

export const GET_All_COUNTRIES = gql`
  {
    allActiveCountries {
      currencyCode
      currencySymbol
    }
  }
`;

export const APPROVAL_STATES = {
  REQUESTED: "requested",
  APPROVED: "approved",
  REJECTED: "rejected",
};

export const STATUS_CLASS_NAMES = {
  common: "border border-solid rounded-lg py-3 px-4 flex flex-col gap-4",
  [APPROVAL_STATES.REQUESTED]:
    "bg-gradient-to-r from-[#FFE2D3CC] via-[#FEFBFA99] to-[#FEF1EBCC] border-ever-chartColors-15/40",
  [APPROVAL_STATES.APPROVED]:
    "bg-gradient-to-r from-[#DDFFE9CC] via-[#F9FFFBB9] to-[#D0FFE1CC] border-ever-chartColors-51/80",
  [APPROVAL_STATES.REJECTED]:
    "bg-gradient-to-r from-[#FFCDCDCC] via-[#FFF2F299] to-[#FFCFCFCC] border-ever-error-hover/20",
};

export const COMMENT_DIVIDER_BACKGROUND_COLOR = {
  [APPROVAL_STATES.REQUESTED]: "bg-ever-warning",
  [APPROVAL_STATES.APPROVED]: "bg-ever-success",
  [APPROVAL_STATES.REJECTED]: "bg-ever-error",
};

export const APPROVAL_STATUS_BADGE_ITEMS = {
  [APPROVAL_STATES.REJECTED]: {
    icon: <XCircleIcon className="text-ever-error w-5 h-5" />,
    text: "Rejected",
    containerClassName: "bg-ever-error-lite",
    textClassName: "text-ever-error",
  },
  [APPROVAL_STATES.APPROVED]: {
    icon: <CheckCircleIcon className="text-ever-success w-5 h-5" />,
    text: "Approved",
    containerClassName: "bg-ever-success-lite",
    textClassName: "text-ever-success",
  },
};

export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    scale: 0.95,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
};
