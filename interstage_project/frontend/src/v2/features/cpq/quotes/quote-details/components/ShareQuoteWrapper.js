import {
  Download04Icon,
  EditIcon,
  LinkIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import React, { useState } from "react";

import { RBAC_ROLES } from "~/Enums";
import { EverDivider, EverTg, PublicLinkShareModal } from "~/v2/components";

import { SendToCustomerButton } from "../../build-quote/components/send-to-customer-button";
import { STATUS_TYPES } from "../../quotes-list/utils";
import { useQuoteDetails } from "../hook/useQuoteDetails";

const SHARE_OPTIONS_VALUES = {
  DOWNLOAD_PDF: "downloadPdf",
  PUBLIC_LINK: "publicLink",
  SEND_FOR_SIGNING: "sendForSigning",
};

const dropdownOptions = [
  {
    label: "🔎  For review",
    type: "header",
    key: "for-review",
  },
  {
    label: "Download PDF",
    value: SHARE_OPTIONS_VALUES.DOWNLOAD_PDF,
    icon: <Download04Icon className="w-4 h-4 text-ever-chartColors-7" />,
    type: "item",
    key: "download-pdf",
  },
  {
    label: "Share public link",
    value: SHARE_OPTIONS_VALUES.PUBLIC_LINK,
    icon: <LinkIcon className="w-4 h-4 text-ever-chartColors-7" />,
    type: "item",
    key: "share-public-link",
  },
  {
    type: "divider",
    key: "divider-1",
    permission: RBAC_ROLES.EDIT_QUOTES,
  },
  {
    label: "✍️  For signature",
    type: "header",
    key: "for-signature",
    permission: RBAC_ROLES.EDIT_QUOTES,
  },
  {
    label: "Send for signing",
    value: SHARE_OPTIONS_VALUES.SEND_FOR_SIGNING,
    icon: <EditIcon className="w-4 h-4 text-ever-chartColors-7" />,
    type: "item",
    key: "send-for-signing",
    permission: RBAC_ROLES.EDIT_QUOTES,
  },
];

export function ShareQuoteWrapper({
  triggerComponent,
  downloadPdf,
  dropdownDisabled,
}) {
  const {
    publicLinkData,
    isPublicLinkLoading,
    refetchPublicLink,
    quoteData,
    downloadQuotePDF,
    pdfData,
    refetchTemplateDetails,
    refetchQuoteDetails,
    accountName,
    hasPermissions,
  } = useQuoteDetails();

  const [showShareModal, setShowShareModal] = useState(false);

  function handleMenuItemClick(value) {
    switch (value) {
      case SHARE_OPTIONS_VALUES.DOWNLOAD_PDF: {
        downloadPdf();
        break;
      }
      case SHARE_OPTIONS_VALUES.PUBLIC_LINK: {
        refetchPublicLink();
        setShowShareModal(true);
        break;
      }
      case SHARE_OPTIONS_VALUES.SEND_FOR_SIGNING: {
        break;
      }
      default: {
        break;
      }
    }
  }

  const dropdownMenu = () => {
    return (
      <Menu>
        {dropdownOptions.map((option) => {
          if (option.permission && !hasPermissions(option.permission))
            return null;
          if (option.type === "divider") {
            return <EverDivider className="my-2" key={option.key} />;
          }
          if (option.type === "header") {
            return (
              <div className="flex items-center py-2 px-3" key={option.key}>
                <EverTg.SubHeading4 text="text-ever-base-content-mid">
                  {option.label}
                </EverTg.SubHeading4>
              </div>
            );
          }
          if (
            option.value === SHARE_OPTIONS_VALUES.SEND_FOR_SIGNING &&
            (quoteData?.quote_status === STATUS_TYPES.PENDING_APPROVAL ||
              quoteData?.quote_status === STATUS_TYPES.APPROVED)
          ) {
            return (
              <Menu.Item
                key={option.key}
                className="!p-0"
                disabled={
                  quoteData?.quote_status === STATUS_TYPES.PENDING_APPROVAL
                }
              >
                <SendToCustomerButton
                  isButtonDisabled={
                    quoteData?.quote_status === STATUS_TYPES.PENDING_APPROVAL
                  }
                  handleDownloadQuotePDF={downloadQuotePDF}
                  quoteStatus={quoteData?.quote_status}
                  quoteName={quoteData?.quote_name}
                  pdfData={pdfData}
                  quoteId={quoteData?.quote_id}
                  showDownloadInProgressModal={false}
                  refetchTemplateDetails={refetchTemplateDetails}
                  refreshQuoteDetails={refetchQuoteDetails}
                  quoteDetails={{ account: accountName }}
                />
              </Menu.Item>
            );
          }
          return (
            <Menu.Item
              key={option.key}
              onClick={() => {
                handleMenuItemClick(option.value);
              }}
            >
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 flex items-center justify-center rounded-md bg-ever-primary-lite">
                  {option.icon}
                </div>
                <EverTg.Caption>{option.label}</EverTg.Caption>
              </div>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  };
  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Dropdown
        trigger={["click"]}
        overlay={dropdownMenu()}
        disabled={dropdownDisabled}
      >
        {triggerComponent}
      </Dropdown>
      <PublicLinkShareModal
        visible={showShareModal}
        onCancel={() => setShowShareModal(false)}
        entity="quote"
        publicLink={publicLinkData?.public_link}
        isLoading={isPublicLinkLoading}
        publicLinkId={publicLinkData?.public_link_id}
        sharedUsers={publicLinkData?.shared_users}
      />
    </div>
  );
}
