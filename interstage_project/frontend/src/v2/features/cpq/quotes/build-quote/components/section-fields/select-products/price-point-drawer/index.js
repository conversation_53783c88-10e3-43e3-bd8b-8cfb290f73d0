import { LockIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useState, useEffect, useMemo } from "react";
import { useForm, useWatch, Controller } from "react-hook-form";

import {
  EverButton,
  EverTg,
  EverDrawer,
  EverSelect,
  EverSwitch,
  EverCheckbox,
  EverInput,
  message,
} from "~/v2/components";
import {
  LabeledField,
  TruncatedText,
  TierTable,
} from "~/v2/features/cpq/components";
import {
  EditCellRenderer,
  NumberCellEditor,
  RangeCellEditor,
} from "~/v2/features/cpq/components/ag-grid";
import {
  formatNumberByPrecision,
  numberFormatter,
} from "~/v2/features/cpq/utils";

import PriceDiscountInput from "./PriceDiscountInput";
import NumberField from "../../field-types/numberField";
import { usePhase } from "../hooks";
import { CustomCellRenderer } from "../hooks/useGrid";

const OutputSettings = ({ control, showFutureTiers = false }) => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center w-full gap-2">
        <span className="text-xs font-semibold whitespace-nowrap">
          OUTPUT SETTINGS
        </span>
        <span className="h-px w-full bg-ever-base-300" />
      </div>
      <div>
        <EverTg.Description>Note to Customer</EverTg.Description>
        <Controller
          control={control}
          name="ProductDescription"
          render={({ field }) => (
            <EverInput.TextArea
              value={field.value}
              maxLength={250}
              onChange={(e) => field.onChange(e.target.value)}
              className="w-full !mt-2"
              placeholder="Included as product description on quote document"
            />
          )}
        />
      </div>
      {showFutureTiers && (
        <div className="flex items-start gap-3">
          <Controller
            control={control}
            name="showFutureTiers"
            render={({ field }) => (
              <EverSwitch
                {...field}
                checked={field.value}
                className="items-center h-5"
              />
            )}
          />
          <div className="flex flex-col gap-0.5">
            <EverTg.Text>Show all tiers to customers</EverTg.Text>
            <EverTg.Text className="text-sm text-ever-base-content-mid">
              Only tiers matching the quantity are displayed by default
            </EverTg.Text>
          </div>
        </div>
      )}
    </div>
  );
};

const PricePointDrawer = () => {
  const editable = true;
  const [pricePointOptions, setPricePointOptions] = useState([]);
  const [showTierTable, setShowTierTable] = useState(false);

  const {
    phase,
    phases,
    skuPricePoint,
    selectedRowForPricePoint,
    onUpdateProductPricePoint,
    onClosePricePointModal,
    qliLoading,
  } = usePhase();
  const phaseIndex = phases.findIndex((p) => p.phase_id === phase.phase_id);
  const subSequentPhases = phases
    .slice(phaseIndex + 1)
    .map((p) => p.phase_name);
  const isVisible = !isEmpty(selectedRowForPricePoint);
  subSequentPhases.length > 0;
  const [copyFlag, setCopyFlag] = useState(isVisible);
  const [quantity, setQuantity] = useState(selectedRowForPricePoint.quantity);
  const [selectedPricePointDetails, setSelectedPricePointDetails] = useState(
    {}
  );
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    setCopyFlag(isVisible);
  }, [isVisible]);

  const {
    control,
    getValues,
    setValue,
    reset,
    handleSubmit,
    clearErrors,
    formState: { errors },
  } = useForm({
    mode: "onChange",
    defaultValues: {
      pricePoint: "",
      tierData: [],
      quantity: 1,
      listUnitPrice: null,
      netUnitPrice: null,
      discountPercent: 0,
      showFutureTiers: false,
      ProductDescription: "",
    },
  });
  const formValues = useWatch({
    control,
    name: [
      "listUnitPrice",
      "netUnitPrice",
      "discountPercent",
      "quantity",
      "pricePoint",
    ],
  });

  useEffect(() => {
    if (formValues[0] === "") {
      setValue("listUnitPrice", 0);
    }
    if (formValues[1] === "") {
      setValue("netUnitPrice", 0);
    }
    if (formValues[2] === "") {
      setValue("discountPercent", 0);
    }
  }, [formValues]);

  useEffect(() => {
    setQuantity(formValues[3] || 0);
  }, [formValues[3]]);

  const selectedPricePointList = useMemo(
    () => skuPricePoint[selectedRowForPricePoint.sku],
    [selectedRowForPricePoint, skuPricePoint]
  );

  const billingFrequency = useMemo(() => {
    return selectedPricePointDetails?.billing_frequency;
  }, [selectedPricePointDetails, formValues[4]]);

  function getFormListUnitPrice() {
    let price = getValues("listUnitPrice");
    if (price === undefined && isFieldDisabled("listUnitPrice")) {
      price = parseFloat(selectedPricePointDetails.list_price);
    }
    return price;
  }

  function getFormNetUnitPrice() {
    let price = getValues("netUnitPrice");
    if (price === undefined && isFieldDisabled("netUnitPrice")) {
      price = parseFloat(selectedPricePointDetails.list_price);
    }
    return price;
  }

  const tierTableColumnDefs = useMemo(
    () => [
      {
        field: "range",
        headerName: "RANGE",
        minWidth: 170,
        maxWidth: 170,
        cellRenderer: (params) => {
          if (params.node.lastChild) {
            return <div className="grid px-2">{params.displayValue}</div>;
          }
          return <EditCellRenderer {...params} />;
        },
        cellRendererParams: (params) => {
          const { lower_bound, upper_bound } = params.data;
          const remainingQuantities = quantity - (lower_bound - 1);
          const distributeQuantities =
            upper_bound === null
              ? remainingQuantities
              : Math.min(remainingQuantities, upper_bound - lower_bound + 1);
          const text = `${params.valueFormatted ?? ""}${
            distributeQuantities > 0 ? ` (${distributeQuantities})` : ""
          }`;

          return {
            displayValue: (
              <TruncatedText text={text}>
                <span>{params.valueFormatted ?? ""}</span>
                {distributeQuantities > 0 && (
                  <span className="ml-1 text-ever-info">
                    ({distributeQuantities})
                  </span>
                )}
              </TruncatedText>
            ),
          };
        },
        editable: (params) => !params.node.lastChild,
        singleClickEdit: true,
        cellEditor: RangeCellEditor,
        cellEditorParams: (params) => {
          const { lower_bound, upper_bound } = params.data;
          return {
            lowerBoundProperties: {
              value: lower_bound,
              skipSeparatorFormat: true,
              disabled: true,
            },
            upperBoundProperties: {
              value: upper_bound,
              step: 1,
              skipSeparatorFormat: true,
              disabled: !upper_bound,
              autoFocus: true,
            },
          };
        },
        valueFormatter: (params) => {
          const { lower_bound, upper_bound } = params.data;
          return params.node.lastChild
            ? `${lower_bound} and above`
            : `${lower_bound} - ${upper_bound}`;
        },
      },
      {
        field: "list_unit_price",
        headerName: "LIST UNIT PRICE",
        minWidth: 112,
        maxWidth: 112,
        headerClass: "!px-4",
        cellClass: "justify-end [&_input]:!text-right",
        type: "rightAligned",
        cellRenderer: EditCellRenderer,
        cellRendererParams: (params) => {
          const displayValue = (
            <CustomCellRenderer
              value={params.valueFormatted ?? ""}
              billingFrequency={billingFrequency}
            />
          );
          return {
            displayValue,
            isNumber: true,
            isDisabled: isFieldDisabled("listUnitPrice"),
            toolTipTitle: "Modification to list unit price is not allowed",
            toolTipPlacement: "top",
          };
        },
        editable: !isFieldDisabled("listUnitPrice"),
        singleClickEdit: true,
        cellEditor: NumberCellEditor,
        cellEditorParams: {
          step: 1,
          precision: 2, // TODO: make this dynamic
          innerPrefix: "$",
          min: 0,
        },
        valueFormatter: ({ value }) => {
          return numberFormatter(value ?? 0, {
            innerPrefix: "$",
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2,
            locale: "en-US",
          });
        },
        valueSetter: (params) => {
          const oldValue = parseFloat(params.data.original_list_unit_price);
          const newValue = parseFloat(params.newValue);
          const threshold = parseFloat(
            selectedPricePointDetails.modification_treshold || 0
          );
          const difference = Math.abs(oldValue - newValue);
          if (difference <= threshold) {
            params.data.list_unit_price = newValue;
            return true;
          } else {
            message.error(
              `List unit price has to be within +/- $${threshold} of original list price $${oldValue}`
            );
            return false;
          }
        },
      },
      {
        field: "unit_price",
        headerName: "NET UNIT PRICE",
        minWidth: 160,
        maxWidth: 160,
        headerClass: "!px-4",
        cellClass: "justify-end [&_input]:!text-right",
        type: "rightAligned",
        cellRenderer: EditCellRenderer,
        cellRendererParams: ({ data }) => {
          const netUnitPrice =
            numberFormatter(data.net_unit_price ?? 0, {
              innerPrefix: "$",
              // TODO: Make this locale dynamic based on the selected format locale
              precision: 2,
              locale: "en-US",
            }) || "";
          const discountPercent = numberFormatter(data.unit_price_discount, {
            innerSuffix: "%",
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2,
            locale: "en-US",
          });
          const displayValue = (
            <CustomCellRenderer
              value={netUnitPrice}
              billingFrequency={billingFrequency}
              discountPercent={discountPercent}
            />
          );
          let isDisabled = false;
          let toolTipTitle = "";
          if (data.list_unit_price === 0) {
            isDisabled = true;
            toolTipTitle = "List Unit Price has to be set first";
          } else if (isFieldDisabled("netUnitPrice")) {
            isDisabled = true;
            toolTipTitle = "Discounting list unit price is not allowed";
          }
          return {
            displayValue,
            isNumber: true,
            isDisabled,
            toolTipTitle,
            toolTipPlacement: "top",
          };
        },
        editable: ({ data }) => {
          return data.list_unit_price !== 0 && !isFieldDisabled("netUnitPrice");
        },
        singleClickEdit: true,
        cellEditor: PriceDiscountInput,
        cellEditorParams: (params) => {
          const { list_unit_price, net_unit_price, unit_price_discount } =
            params.data;
          return {
            basePrice: list_unit_price,
            priceProperties: {
              value: net_unit_price,
              precision: 2, // TODO: make this dynamic
              step: 1,
              innerPrefix: "$",
              autoFocus: true,
              min: 0,
              max: list_unit_price,
            },
            discountProperties: {
              value: unit_price_discount,
              precision: 2, // TODO: make this dynamic
              step: 1,
              innerSuffix: "%",
              min: 0,
              max: 100,
            },
          };
        },
        valueSetter: (params) => {
          const maxDiscount = parseFloat(
            selectedPricePointDetails.max_discount
          );

          if (params.newValue.discount <= maxDiscount) {
            params.data.net_unit_price = params.newValue.price;
            params.data.unit_price_discount = params.newValue.discount;
            return true;
          } else {
            message.error(`Discount cannot be greater than ${maxDiscount}%`);
            return false;
          }
        },
      },
      {
        field: "list_flat_price",
        headerName: "LIST FLAT PRICE",
        minWidth: 112,
        maxWidth: 112,
        headerClass: "!px-4",
        cellClass: "justify-end [&_input]:!text-right",
        type: "rightAligned",
        cellRenderer: EditCellRenderer,
        cellRendererParams: (params) => {
          return {
            displayValue: params.valueFormatted ?? "",
            isNumber: true,
            isDisabled: isFieldDisabled("listFlatPrice"),
            toolTipTitle: "Modification to list flat price is not allowed",
            toolTipPlacement: "top",
          };
        },
        editable: !isFieldDisabled("listFlatPrice"),
        singleClickEdit: true,
        cellEditor: NumberCellEditor,
        cellEditorParams: {
          step: 1,
          precision: 2, // TODO: make this dynamic
          innerPrefix: "$",
          min: 0,
        },
        valueFormatter: ({ value }) => {
          return numberFormatter(value ?? 0, {
            innerPrefix: "$",
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2,
            locale: "en-US",
          });
        },
        valueSetter: (params) => {
          const oldValue = parseFloat(params.data.original_list_flat_price);
          const newValue = parseFloat(params.newValue);
          const threshold = parseFloat(
            selectedPricePointDetails.flat_modification_treshold || 0
          );
          const difference = Math.abs(oldValue - newValue);

          if (difference <= threshold) {
            params.data.list_flat_price = newValue;
            return true;
          } else {
            message.error(
              `List flat fee has to be within +/- $${threshold} of original list flat price $${oldValue}`
            );
            return false;
          }
        },
      },
      {
        field: "flat_price",
        headerName: "NET FLAT PRICE",
        minWidth: 160,
        maxWidth: 160,
        headerClass: "!px-4",
        cellClass: "justify-end [&_input]:!text-right",
        type: "rightAligned",
        cellRenderer: EditCellRenderer,
        cellRendererParams: ({ data }) => {
          const netFlatPrice =
            numberFormatter(data.net_flat_price ?? 0, {
              innerPrefix: "$",
              // TODO: Make this locale dynamic based on the selected format locale
              precision: 2,
              locale: "en-US",
            }) || "";
          const discountPercent = numberFormatter(data.flat_price_discount, {
            innerSuffix: "%",
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2,
            locale: "en-US",
          });
          const displayValue = (
            <CustomCellRenderer
              value={netFlatPrice}
              discountPercent={discountPercent}
            />
          );
          let isDisabled = false;
          let toolTipTitle = "";
          if (data.list_flat_price === 0) {
            isDisabled = true;
            toolTipTitle = "List Flat Price has to be set first";
          } else if (isFieldDisabled("netFlatPrice")) {
            isDisabled = true;
            toolTipTitle = "Discounting list flat price is not allowed";
          }
          return {
            displayValue,
            isNumber: true,
            isDisabled,
            toolTipTitle,
            toolTipPlacement: "left",
          };
        },
        editable: ({ data }) => {
          return data.list_flat_price !== 0 && !isFieldDisabled("netFlatPrice");
        },
        singleClickEdit: true,
        cellEditor: PriceDiscountInput,
        cellEditorParams: (params) => {
          const { list_flat_price, net_flat_price, flat_price_discount } =
            params.data;
          return {
            basePrice: list_flat_price,
            priceProperties: {
              value: net_flat_price,
              precision: 2, // TODO: make this dynamic
              step: 1,
              innerPrefix: "$",
              autoFocus: true,
              min: 0,
              max: list_flat_price,
            },
            discountProperties: {
              value: flat_price_discount,
              precision: 2, // TODO: make this dynamic
              step: 1,
              innerSuffix: "%",
              min: 0,
              max: 100,
            },
          };
        },
        valueSetter: (params) => {
          const maxDiscount = parseFloat(
            selectedPricePointDetails.flat_max_discount || 0
          );

          if (params.newValue.discount <= maxDiscount) {
            params.data.net_flat_price = params.newValue.price;
            params.data.flat_price_discount = params.newValue.discount;
            return true;
          } else {
            message.error(`Discount cannot be greater than ${maxDiscount}%`);
            return false;
          }
        },
      },
    ],
    [quantity, billingFrequency]
  );

  useEffect(() => {
    if (isEmpty(selectedRowForPricePoint)) {
      reset();
      setPricePointOptions([]);
      setShowTierTable(false);
    }
  }, [selectedRowForPricePoint, reset]);

  useEffect(() => {
    if (
      !isEmpty(selectedPricePointList) ||
      selectedRowForPricePoint?.pricePointData?.row_key
    ) {
      let options = (selectedPricePointList || []).map((item) => ({
        label: item.label,
        value: item.row_key,
      }));

      if (selectedRowForPricePoint?.pricePointData?.row_key) {
        const rowKey = selectedRowForPricePoint.pricePointData.row_key;
        const exists = (selectedPricePointList || []).some(
          (item) => item.row_key === rowKey
        );

        if (!exists && selectedRowForPricePoint.pricePointData.label) {
          options.push({
            value: rowKey,
            label: `${selectedRowForPricePoint.pricePointData.label} (Deactivated)`,
            isDeactivated: true,
            disabled: true,
          });
        }
      }

      setPricePointOptions(options);
      const {
        listUnitPrice,
        netUnitPrice,
        discountPercent,
        pricePointData,
        quantity,
      } = selectedRowForPricePoint;
      const showTierTable = !isEmpty(pricePointData.tier_data);

      setShowTierTable(showTierTable);
      setValue("netUnitPrice", netUnitPrice ?? null);
      setValue("discountPercent", discountPercent ?? 0);
      setValue("pricePoint", pricePointData.row_key ?? "");
      setValue("showFutureTiers", pricePointData.show_future_tiers ?? false);
      setValue("ProductDescription", pricePointData.product_description ?? "");
      setValue("quantity", quantity ?? 1);

      if (showTierTable) {
        setValue("tierData", pricePointData.tier_data);
      } else {
        setValue("listUnitPrice", listUnitPrice);
      }

      if (pricePointData.row_key) {
        setSelectedPricePointDetails(pricePointData);
      }
    }
  }, [selectedPricePointList, selectedRowForPricePoint, setValue]);

  const handleUpdate = ({
    tierData,
    listUnitPrice,
    netUnitPrice,
    discountPercent,
    showFutureTiers,
    ProductDescription,
    quantity,
  }) => {
    if (!quantity) {
      message.error("Quantity cannot be empty");
      return;
    }
    if (listUnitPrice === undefined) {
      listUnitPrice = getFormListUnitPrice();
    }
    if (netUnitPrice === undefined) {
      netUnitPrice = getFormNetUnitPrice();
    }
    const updatedData = showTierTable
      ? {
          quantity,
          pricePointData: {
            ...selectedPricePointDetails,
            list_price: tierData[0].list_unit_price,
            tier_data: tierData.map((tier, index) => {
              const { isHovered, ...remainingTier } = tier;
              return {
                ...remainingTier,
                tier_name: `Tier ${index + 1}`,
              };
            }),
            show_future_tiers: showFutureTiers,
            product_description: ProductDescription,
          },
        }
      : {
          quantity,
          listUnitPrice: listUnitPrice ?? 0,
          netUnitPrice: netUnitPrice ?? 0,
          discountPercent: discountPercent ?? 0,
          pricePointData: {
            ...selectedPricePointDetails,
            product_description: ProductDescription,
            list_price: listUnitPrice ?? 0,
          },
        };
    onUpdateProductPricePoint(updatedData, copyFlag);
  };

  const handlePricePointChange = (value) => {
    const selectedPricePoint = selectedPricePointList.find(
      (item) => item.row_key === value
    );
    if (isEmpty(selectedPricePoint.tier_data)) {
      setValue("listUnitPrice", selectedPricePoint.list_price);
      setValue("netUnitPrice", selectedPricePoint.list_price);
      setValue("discountPercent", 0);
      setShowTierTable(false);
    } else {
      setValue("tierData", selectedPricePoint.tier_data);
      setShowTierTable(true);
    }
    setSelectedPricePointDetails(selectedPricePoint);
    setValue("showFutureTiers", false);
    setValue(
      "ProductDescription",
      selectedPricePoint.product_description ?? ""
    );
    clearErrors(["tierData", "listUnitPrice"]);

    setPricePointOptions((prev) =>
      prev.filter((option) => !option.isDeactivated || option.value === value)
    );
  };

  const handleListUnitPriceChange = (value) => {
    value = value || 0;
    let netUnitPrice = value * (1 - (getValues("discountPercent") || 0) / 100);
    if (value === 0 && netUnitPrice === 0) {
      setValue("discountPercent", 0);
    }
    netUnitPrice = formatNumberByPrecision(netUnitPrice, 2);
    setValue("netUnitPrice", Number.isNaN(netUnitPrice) ? 0 : netUnitPrice);
  };

  const handleNetUnitPriceChange = (value) => {
    let totalPrice = getFormListUnitPrice();
    let discountPercent = ((totalPrice - value) / totalPrice) * 100;
    if (discountPercent < 0 || Number.isNaN(discountPercent)) {
      discountPercent = 0;
    }
    setValue("discountPercent", formatNumberByPrecision(discountPercent, 2));
  };

  const handleDiscountPercentChange = (value) => {
    let totalPrice = getFormListUnitPrice();
    const netUnitPrice = totalPrice * (1 - (value || 0) / 100);
    setValue("netUnitPrice", formatNumberByPrecision(netUnitPrice, 2));
  };

  function getAdditionalProps() {
    const additionalProps = {};
    if (!editable) {
      additionalProps.extraText = (
        <div className="ml-auto flex items-center gap-1.5">
          <LockIcon className="mb-0.5 w-4 h-4 text-ever-info" />
          <EverTg.Caption className="text-ever-info leading-none">
            Price cannot be modified
          </EverTg.Caption>
        </div>
      );
    }
    return additionalProps;
  }

  function isFieldDisabled(field) {
    const pricingMethod = selectedPricePointDetails.pricing_method;
    if (field === "listUnitPrice") {
      let threshold;
      if (pricingMethod === "flatfee") {
        threshold = parseFloat(
          selectedPricePointDetails.flat_modification_treshold
        );
      } else {
        threshold = parseFloat(selectedPricePointDetails.modification_treshold);
      }
      if (threshold === 0) {
        return true;
      }
    }
    if (field === "listFlatPrice") {
      let threshold;
      threshold = parseFloat(
        selectedPricePointDetails.flat_modification_treshold
      );
      if (threshold === 0) {
        return true;
      }
    }
    if (field === "netUnitPrice") {
      let maxDiscount;
      if (pricingMethod === "flatfee") {
        maxDiscount = parseFloat(selectedPricePointDetails.flat_max_discount);
      } else {
        maxDiscount = parseFloat(selectedPricePointDetails.max_discount);
      }
      if (maxDiscount === 0) {
        return true;
      }
    }
    if (field === "netFlatPrice") {
      let maxDiscount;
      maxDiscount = parseFloat(selectedPricePointDetails.flat_max_discount);
      if (maxDiscount === 0) {
        return true;
      }
    }
    return false;
  }

  return (
    <EverDrawer
      className="price-point-drawer"
      visible={isVisible}
      placement="right"
      width={764}
      onClose={qliLoading ? null : onClosePricePointModal}
      title={
        <div className="max-w-xl">
          <EverTg.Heading3>
            Configure {selectedRowForPricePoint.productName}
          </EverTg.Heading3>
        </div>
      }
      footer={
        <div className="flex items-center justify-between">
          {subSequentPhases.length > 0 && (
            <TruncatedText
              className="w-96 [&_*]:w-full [&_.ant-checkbox]:w-auto"
              text={`Apply changes to ${subSequentPhases.join(", ")}`}
            >
              <EverCheckbox
                label={`Apply changes to ${subSequentPhases.join(", ")}`}
                checked={copyFlag}
                onChange={(value) => setCopyFlag(value.target.checked)}
              />
            </TruncatedText>
          )}
          <div className="flex items-center ml-auto gap-4 py-0.5">
            <EverButton
              onClick={onClosePricePointModal}
              color="base"
              disabled={qliLoading}
            >
              Cancel
            </EverButton>
            <EverButton
              onClick={handleSubmit(handleUpdate)}
              loading={qliLoading}
              disabled={isEditing}
            >
              Update
            </EverButton>
          </div>
        </div>
      }
      footerStyle={{
        padding: "10px 16px",
        boxShadow: "0px -4px 14px 0px rgba(0, 0, 0, 0.08)",
      }}
    >
      <div className="flex flex-col gap-7">
        <div>
          <LabeledField
            label="Quantity"
            className="section-fields__input-container"
            labelClassName="text-ever-base-content font-medium"
            required
            {...getAdditionalProps()}
          >
            <Controller
              control={control}
              name="quantity"
              rules={{
                required: "This is required",
                min: {
                  value: 1,
                  message: "Quantity cannot be less than 1",
                },
              }}
              render={({ field }) => (
                <NumberField
                  field={field}
                  properties={{
                    is_read_only: field.disabled,
                  }}
                />
              )}
            />
          </LabeledField>
          {errors.quantity && (
            <EverTg.Caption className="block mt-1 text-ever-error-lite-content">
              This is required
            </EverTg.Caption>
          )}
        </div>
        <div>
          <LabeledField
            label="Price Point"
            error={errors.pricePoint}
            helpText={
              pricePointOptions.length === 1
                ? "Product has only one price point"
                : ""
            }
            labelClassName="text-ever-base-content font-medium"
            required
          >
            <Controller
              control={control}
              name="pricePoint"
              rules={{ required: "This is required" }}
              render={({ field }) => (
                <EverSelect
                  {...field}
                  showSearch
                  value={field.value === "" ? null : field.value}
                  placeholder="Select"
                  options={pricePointOptions}
                  disabled={field.value && pricePointOptions.length === 1}
                  onChange={(value) => {
                    field.onChange(value);
                    handlePricePointChange(value);
                  }}
                />
              )}
            />
          </LabeledField>
        </div>
        {showTierTable ? (
          <>
            <LabeledField
              label={
                <span>
                  <span>Tiers - </span>
                  <span className="text-ever-info">
                    For{" "}
                    <strong>
                      {quantity} {selectedRowForPricePoint.unitOfMeasure}
                    </strong>
                  </span>
                </span>
              }
              className="section-fields__input-container"
              labelClassName="text-ever-base-content font-medium"
              {...getAdditionalProps()}
            >
              <TierTable
                tierData={getValues("tierData")}
                onValueChange={(value) => setValue("tierData", value)}
                columnDefs={tierTableColumnDefs}
                instance="quoteTable"
                onCellEditingStarted={() => setIsEditing(true)}
                onCellEditingStopped={() =>
                  setTimeout(() => setIsEditing(false), 500)
                }
              />
            </LabeledField>
            <OutputSettings control={control} showFutureTiers={true} />
          </>
        ) : (
          <>
            <div>
              <LabeledField
                label="List Unit Price"
                error={errors.listUnitPrice}
                helpText={
                  isEmpty(getValues("pricePoint"))
                    ? "Price point has to be set first"
                    : ""
                }
                className="section-fields__input-container"
                labelClassName="text-ever-base-content font-medium"
                {...getAdditionalProps()}
              >
                <Controller
                  control={control}
                  name="listUnitPrice"
                  rules={{
                    required: "This is required",
                    validate: (value) => {
                      const threshold = parseFloat(
                        selectedPricePointDetails.pricing_method === "flatfee"
                          ? selectedPricePointDetails.flat_modification_treshold ||
                              0
                          : selectedPricePointDetails.modification_treshold || 0
                      );
                      const oldValue = parseFloat(
                        selectedPricePointDetails.original_list_price
                      );
                      const difference = Math.abs(value - oldValue);
                      return (
                        difference <= threshold ||
                        `List unit price has to be within +/- $${threshold} of original list price $${oldValue}`
                      );
                    },
                  }}
                  disabled={
                    isEmpty(getValues("pricePoint")) ||
                    isFieldDisabled("listUnitPrice")
                  }
                  render={({ field }) => (
                    <NumberField
                      field={{
                        ...field,
                        onChange: (value) => {
                          handleListUnitPriceChange(value);
                          field.onChange(value);
                        },
                      }}
                      properties={{
                        prefix: "$",
                        is_read_only: field.disabled,
                        precision: 2,
                        min: 0,
                        hide_min_max: true,
                      }}
                    />
                  )}
                />
              </LabeledField>
            </div>
            <div className="flex items-start gap-4 [&>div]:w-full">
              <LabeledField
                label="Net Unit Price"
                error={errors?.netUnitPrice}
                helpText={
                  isEmpty(getValues("pricePoint")) ||
                  getFormListUnitPrice() === 0
                    ? "List Unit Price has to be set first"
                    : ""
                }
                className="section-fields__input-container"
                labelClassName="text-ever-base-content font-medium"
              >
                <Controller
                  control={control}
                  name="netUnitPrice"
                  rules={{
                    required: "This is required",
                    validate: (value) => {
                      let totalPrice = getFormListUnitPrice();
                      const maxDiscount = parseFloat(
                        selectedPricePointDetails.pricing_method === "flatfee"
                          ? selectedPricePointDetails.flat_max_discount || 0
                          : selectedPricePointDetails.max_discount || 0
                      );

                      const percentage =
                        ((totalPrice - value) / totalPrice) * 100;

                      return (
                        percentage <= maxDiscount ||
                        `Discount cannot be greater than ${maxDiscount}%`
                      );
                    },
                  }}
                  disabled={
                    isEmpty(getValues("pricePoint")) ||
                    getFormListUnitPrice() === 0 ||
                    isFieldDisabled("netUnitPrice")
                  }
                  render={({ field }) => (
                    <NumberField
                      field={{
                        ...field,
                        onChange: (value) => {
                          handleNetUnitPriceChange(value);
                          field.onChange(value);
                        },
                      }}
                      properties={{
                        prefix: "$",
                        is_read_only: field.disabled,
                        precision: 2,
                        min: 0,
                        max: getFormListUnitPrice(),
                        hide_min_max: true,
                      }}
                    />
                  )}
                />
              </LabeledField>
              <LabeledField
                label="Discount"
                error={errors?.discountPercent}
                helpText={
                  isEmpty(getValues("pricePoint")) ||
                  getFormListUnitPrice() === 0
                    ? "List Unit Price has to be set first"
                    : ""
                }
                className="section-fields__input-container"
                labelClassName="text-ever-base-content font-medium"
              >
                <Controller
                  control={control}
                  name="discountPercent"
                  rules={{
                    required: "This is required",
                    min: {
                      value: 0,
                      message: "Value cannot be negative",
                    },
                    max: {
                      value: 100,
                      message: "Value cannot be more than 100",
                    },
                    validate: (value) => {
                      const maxDiscount = parseFloat(
                        selectedPricePointDetails.pricing_method === "flatfee"
                          ? selectedPricePointDetails.flat_max_discount || 0
                          : selectedPricePointDetails.max_discount || 0
                      );
                      return (
                        value <= maxDiscount ||
                        `Discount cannot be greater than ${maxDiscount}%`
                      );
                    },
                  }}
                  disabled={
                    isEmpty(getValues("pricePoint")) ||
                    getFormListUnitPrice() === 0 ||
                    isFieldDisabled("netUnitPrice")
                  }
                  render={({ field }) => (
                    <NumberField
                      field={{
                        ...field,
                        onChange: (value) => {
                          handleDiscountPercentChange(value);
                          field.onChange(value);
                        },
                      }}
                      properties={{
                        prefix: "%",
                        is_read_only: field.disabled,
                        precision: 2,
                        min: 0,
                        max: 100,
                        hide_min_max: true,
                      }}
                    />
                  )}
                />
              </LabeledField>
            </div>
            <OutputSettings control={control} />
          </>
        )}
      </div>
    </EverDrawer>
  );
};

export default PricePointDrawer;
