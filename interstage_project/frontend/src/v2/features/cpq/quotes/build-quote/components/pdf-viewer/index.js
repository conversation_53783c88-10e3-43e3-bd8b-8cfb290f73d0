import {
  ChevronRightIcon,
  Download04Icon,
  ExpandHorizontalIcon,
} from "@everstage/evericons/outlined";
import { motion } from "framer-motion";
import { useEffect, useState, useRef } from "react";
import { useQuery } from "react-query";
import { twMerge } from "tailwind-merge";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverButton,
  EverModal,
  EverTg,
  PublicLinkShareModal,
  createPublicLink,
} from "~/v2/components";
import { EverHotToastMessage, toast } from "~/v2/components/ever-popups";
import { AnimatedWrapper, ANIMATION_TYPES } from "~/v2/features/cpq/components";
import { useBuildQuote } from "~/v2/features/cpq/quotes/build-quote/hooks";

import PDF from "./pdf";
import { STATUS_TYPES } from "../../../quotes-list/utils";
import { getPdfTemplate } from "../../services/restApi";
import { onExportAsPdf } from "../../utils";

const PDFViewer = ({ isSmallScreen }) => {
  const { accessToken } = useAuthStore();
  const {
    showPdfViewer,
    activeSectionKey,
    quoteStatus,
    basicDetails,
    pdfData,
    setPdfData,
    quoteId,
    tableData,
    isTableDataLoading,
    quoteTerms,
  } = useBuildQuote();

  const quoteName = basicDetails?.quoteName;

  const prevActiveSectionKeyRef = useRef(null);
  const prevIsSmallScreenRef = useRef(false);

  const [collapsed, setCollapsed] = useState(isSmallScreen);
  const [showShareModal, setShowShareModal] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const { data: templateData, isLoading: isTemplateDataLoading } = useQuery(
    ["getPdfTemplate"],
    () => getPdfTemplate({ quote_id: quoteId }, accessToken),
    { enabled: !!quoteId }
  );

  const { data: publicLinkData, isLoading: isPublicLinkLoading } = useQuery(
    ["createPublicLink", quoteId],
    () =>
      createPublicLink({ entity_id: quoteId, entity: "quote" }, accessToken),
    {
      enabled: false, // Only run on demand
      retry: false, // Prevent automatic retries
      onSuccess: () => {
        setShowShareModal(true);
      },
      onError: () => {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description={"Unable to share the quote. Please try again later."}
          />
        ));
      },
    }
  );

  useEffect(() => {
    // If the screen is small, and the screen size or active section has changed, collapse the PDF viewer
    if (
      isSmallScreen &&
      (isSmallScreen !== prevIsSmallScreenRef.current ||
        activeSectionKey !== prevActiveSectionKeyRef.current)
    ) {
      setCollapsed(true);
    } else if (!isSmallScreen) {
      setCollapsed(false);
    }
    prevActiveSectionKeyRef.current = activeSectionKey;
    prevIsSmallScreenRef.current = isSmallScreen;
  }, [isSmallScreen, activeSectionKey]);

  const containerAnimation = {
    width: !showPdfViewer
      ? 0
      : collapsed
      ? 72
      : Math.min(Math.max(window.innerHeight - 105, 600), 1166) / 1.3, // 1.3 is the aspect ratio of the original PDF
    height: !showPdfViewer
      ? 0
      : Math.min(Math.max(window.innerHeight - 105, 600), 1166), // Roughly 1166px is the height of the original PDF
  };

  const chevronAnimation = {
    left: collapsed ? 10 : -16,
    top: collapsed ? 16 : 42,
  };

  return (
    <motion.div
      className={twMerge(
        "shrink-0 bg-ever-base mr-5",
        isSmallScreen
          ? "absolute right-0 top-5 h-[calc(100%-40px)] z-[1010]"
          : "h-full relative"
      )}
      initial={false}
      animate={containerAnimation}
      transition={{ type: "tween", delay: 0.1, duration: 0.3 }}
    >
      <div
        className={twMerge(
          "overflow-hidden !h-full w-auto border border-solid border-ever-base-300 rounded-xl bg-ever-base-50 shadow-lg",
          collapsed && "cursor-pointer w-12"
        )}
        {...(collapsed && {
          onClick: () => setCollapsed(!collapsed),
        })}
      >
        {collapsed && (
          <EverTg.SubHeading4 className="flex text-ever-base-content-mid w-12 my-20 rotate-90">
            Preview
          </EverTg.SubHeading4>
        )}
        <AnimatedWrapper
          isVisible={true}
          motionProps={{
            className: "relative overflow-auto inset-0 bg-ever-base-50",
            type: ANIMATION_TYPES.FADE_IN_OUT,
            style: { height: containerAnimation.height },
          }}
        >
          <div
            className={
              "absolute opacity-15 hover:opacity-100 transition-opacity z-[1100] group " +
              ((collapsed || !showPdfViewer) && "!opacity-0 !z-0")
            }
            style={{ width: containerAnimation.width - 17 }}
          >
            <div className="absolute top-0 left-0 right-0 h-40 bg-gradient-to-b from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
            <EverButton
              size="small"
              className="!h-7 !px-2 top-4 right-20 !text-ever-base-50 !text-xs absolute !bg-ever-base-content !border-0"
              disabled={isTemplateDataLoading || isTableDataLoading}
              onClick={() =>
                onExportAsPdf(
                  {
                    pdf_data: {
                      ...pdfData,
                      isDraft:
                        quoteStatus === STATUS_TYPES.DRAFT ||
                        quoteStatus === STATUS_TYPES.PENDING_APPROVAL ||
                        quoteStatus === STATUS_TYPES.CANCELLED ||
                        quoteStatus === STATUS_TYPES.REJECTED,
                    },
                  },
                  accessToken,
                  quoteName
                )
              }
            >
              Download
              <Download04Icon className="w-4 h-4 stroke-[1.2]" />
            </EverButton>
            <EverButton
              size="small"
              className="!h-7 !px-2 top-4 right-10 !text-ever-base-50 !text-xs absolute !bg-ever-base-content !border-0"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <ExpandHorizontalIcon className="w-4 h-4 -rotate-45" />
            </EverButton>
          </div>
          <PDF
            pdfData={pdfData}
            setPdfData={setPdfData}
            templateData={templateData}
            isTemplateDataLoading={isTemplateDataLoading}
            styleData={containerAnimation}
            tableData={tableData}
            quoteTerms={quoteTerms}
            hide={!showPdfViewer || collapsed}
          />
          <PublicLinkShareModal
            visible={showShareModal}
            onCancel={() => setShowShareModal(false)}
            entity="quote"
            publicLink={publicLinkData?.public_link}
            isLoading={isPublicLinkLoading}
            publicLinkId={publicLinkData?.public_link_id}
            sharedUsers={publicLinkData?.shared_users}
          />
        </AnimatedWrapper>
        <AnimatedWrapper
          isVisible={isSmallScreen}
          motionProps={{ type: ANIMATION_TYPES.FADE_IN_OUT }}
        >
          <motion.div
            className="absolute top-10 flex items-center justify-center w-7 h-7 cursor-pointer bg-ever-base-25 border border-solid border-ever-base-200 shadow-sm rounded-full z-[1100]"
            initial={false}
            animate={chevronAnimation}
            transition={{ type: "tween" }}
            onClick={() => setCollapsed(!collapsed)}
          >
            <ChevronRightIcon
              className={twMerge(
                "w-5 h-5 p-px text-ever-base-content-mid transition-all duration-300",
                collapsed ? "rotate-180" : ""
              )}
            />
          </motion.div>
        </AnimatedWrapper>
      </div>
      <EverModal
        visible={isExpanded}
        onCancel={() => setIsExpanded(false)}
        className="!w-[1400px] !h-[90%]"
        bodyStyle={{
          paddingLeft: "100px",
          paddingRight: "100px",
          paddingTop: "32px",
          minHeight: "90vh",
        }}
        footer={null}
      >
        <div className="overflow-hidden !h-full w-auto border border-solid border-ever-base-300 rounded-xl bg-ever-base-50 shadow-lg">
          <PDF
            pdfData={pdfData}
            setPdfData={setPdfData}
            templateData={templateData}
            isTemplateDataLoading={isTemplateDataLoading}
            styleData={{
              width: 1121,
            }}
            tableData={tableData}
            quoteTerms={quoteTerms}
          />
        </div>
      </EverModal>
    </motion.div>
  );
};

export default PDFViewer;
