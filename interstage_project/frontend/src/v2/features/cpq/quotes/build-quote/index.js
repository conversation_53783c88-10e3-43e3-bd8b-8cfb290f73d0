/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import {
  LogOutCircleIcon,
  CheckCircleBrokenIcon,
  ClockFastForwardIcon,
  EditPencilAltIcon,
  ThumbsUpIcon,
} from "@everstage/evericons/outlined";
import {
  format,
  formatDistanceToNowStrict,
  differenceInDays,
  differenceInSeconds,
} from "date-fns";
import { isEmpty, get, isNil } from "lodash";
import { useState, useLayoutEffect, useEffect, useRef } from "react";
import { useForm, FormProvider, useFormContext } from "react-hook-form";
import { useMutation } from "react-query";
import { useLocation, useParams } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { safeParse } from "valibot";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverLoader, EverDrawer, EverButton, EverTg } from "~/v2/components";
import { useViewport } from "~/v2/components/custom-hooks";
import { StatusBadge, TruncatedText } from "~/v2/features/cpq/components";
import {
  Sections,
  SectionFields,
  PDFViewer,
  Approvals,
  MarkAsWonModal,
} from "~/v2/features/cpq/quotes/build-quote/components";
import {
  BuildQuoteProvider,
  useBuildQuote,
} from "~/v2/features/cpq/quotes/build-quote/hooks";
import { fetchQuoteDetails } from "~/v2/features/cpq/quotes/build-quote/services/restApi";
import { schema } from "~/v2/features/cpq/quotes/build-quote/services/validationSchema";
import { getFormValues } from "~/v2/features/cpq/quotes/build-quote/utils";
import { STATUS_TYPES } from "~/v2/features/cpq/quotes/quotes-list/utils";

import ApprovalsModal from "./components/approvals/ApprovalsModal";
import ExitConfirmationModal from "./components/ExitConfirmationModal";
import WonCelebrationModal, {
  handleConfetti,
} from "./components/WonCelebrationModal";
import "./styles.scss";
const formatDateBasedOnNow = (date, dateFormat = "MMM dd, yyyy, p") => {
  const now = new Date();

  if (differenceInSeconds(now, date) < 60) {
    return "Just now"; // If the difference is less than 1 minute, return "few seconds ago"
  } else if (differenceInDays(now, date) >= 1) {
    return format(date, dateFormat); // If the difference is more than 1 day, return the date in the provided format
  } else {
    return `${formatDistanceToNowStrict(date)} ago`; // return relative time
  }
};

const BuildQuote = () => {
  const lastSavedInterval = useRef(null);
  const { getValues, formState, handleSubmit } = useFormContext();
  const {
    quoteId,
    quoteStatus,
    formBuilderId,
    basicDetails,
    formSpec,
    isAutoSaveLoading,
    lastSavedTime,
    showPdfViewer,
    isEditMode,
    setIsEditMode,
    publishForm,
    validateAndPublishForm,
    exitBuildQuote,
    // changeQuoteStatusHandler,
    showApprovalsModal,
    setShowApprovalsModal,
    approvalCycles,
    setApprovalCycles,
    onCloseBuildQuote,
    onCloseBuildQuoteSuccess,
    visibleSectionOrder,
    pdfData,
    templateDetails,
    saveQuoteDetails,
    refreshQuoteDetails,
  } = useBuildQuote();
  const { hasPermissions } = useUserPermissionStore();
  const { width } = useViewport(0);
  const { email } = useAuthStore();
  const isSmallScreen = width < 1280; // Less than 1280px is considered small screen

  const [initialLoad, setInitialLoad] = useState(true);
  const [lastSavedOn, setLastSavedOn] = useState(null);
  const [showExitConfirmModal, setShowExitConfirmModal] = useState(false);
  const [showMarkAsWonModal, setShowMarkAsWonModal] = useState(false);
  const [showMarkAsWonFormModal, setShowMarkAsWonFormModal] = useState(false);

  const getWonModalKey = () => `quote_won_${quoteId}_${email}`;

  useEffect(() => {
    if (quoteStatus === STATUS_TYPES.WON) {
      const wonModalKey = getWonModalKey();
      const hasShownModal = document.cookie
        .split("; ")
        .find((row) => row.startsWith(wonModalKey + "="));

      if (
        !hasShownModal &&
        differenceInDays(new Date(), new Date(lastSavedTime)) <= 2
      ) {
        setShowMarkAsWonModal(true);

        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 2);
        document.cookie = `${wonModalKey}=true; expires=${expirationDate.toUTCString()}; path=/`;

        handleConfetti();
      }
    }
  }, [lastSavedTime, email, quoteId]);

  useEffect(() => {
    if (quoteStatus === STATUS_TYPES.WON) {
      setIsEditMode(false);
    }
  }, [quoteStatus]);

  const handleValidateAndPublish = () => {
    validateAndPublishForm.mutate({
      form_builder_id: formBuilderId,
      quote_id: quoteId,
      form_data: getValues(),
      form_spec: formSpec,
      pdf_data: pdfData,
      is_edit_mode: quoteStatus === STATUS_TYPES.DRAFT ? false : isEditMode,
    });
  };

  const handlePublish = (comments) => {
    if (isEmpty(formState.errors)) {
      setApprovalCycles([]);
      publishForm.mutate({
        quote_id: quoteId,
        form_data: getValues(),
        form_spec: formSpec,
        comments: comments,
        pdf_data: pdfData,
      });
      setShowApprovalsModal(false);
    }
  };

  const closeModal = () => {
    setShowApprovalsModal(false);
  };

  const handleMarkAsWon = () => {
    setShowMarkAsWonFormModal(true);
  };

  const handleMarkAsWonSubmit = (file) => {
    const formData = new FormData();
    formData.append("quote_id", quoteId);
    formData.append("type", "offline");
    formData.append("reason", templateDetails?.sharing_reason || "");
    formData.append(file.name, file);

    // Send both the file and other data to the API
    saveQuoteDetails.mutate({
      payload: formData,
      callback: () => {
        setShowMarkAsWonFormModal(false);
        setShowMarkAsWonModal(true);
        handleConfetti();
        refreshQuoteDetails();
      },
    });
  };

  const handleExit = () => {
    if (quoteStatus === STATUS_TYPES.DRAFT) {
      onCloseBuildQuote();
      return;
    }

    if (isEditMode) {
      setShowExitConfirmModal(true);
    } else {
      onCloseBuildQuoteSuccess();
    }
  };

  useLayoutEffect(() => {
    setInitialLoad(false);
  }, []);

  useEffect(() => {
    setLastSavedOn(formatDateBasedOnNow(new Date(lastSavedTime)));
    clearInterval(lastSavedInterval.current);
    lastSavedInterval.current = setInterval(() => {
      setLastSavedOn(formatDateBasedOnNow(new Date(lastSavedTime)));
    }, 10_000);

    return () => clearInterval(lastSavedInterval.current);
  }, [lastSavedTime]);

  const isLoading =
    validateAndPublishForm.isLoading ||
    publishForm.isLoading ||
    exitBuildQuote.isLoading;

  const renderSaveStatus = () => {
    if (quoteStatus !== STATUS_TYPES.DRAFT) return null;

    if (isAutoSaveLoading) {
      return (
        <div className="flex items-center text-right justify-end gap-2 mr-3">
          <LoadingOutlined className="text-ever-primary [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
          <EverTg.Description>Saving...</EverTg.Description>
        </div>
      );
    } else if (!isNil(lastSavedTime)) {
      return (
        <div className="flex items-center text-right justify-end gap-2 mr-3">
          <ClockFastForwardIcon className="w-5 h-5 text-ever-base-content-low shrink-0" />
          <EverTg.Description>{lastSavedOn}</EverTg.Description>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="grid grid-cols-2 items-center px-6 py-2 bg-ever-base-50 border-0 border-b border-solid border-ever-base-300">
        <div
          className={twMerge(
            "grid items-center gap-3 w-fit",
            isEmpty(basicDetails.account)
              ? "grid-cols-[auto_auto]"
              : "grid-cols-[auto_auto_auto_auto]"
          )}
        >
          <TruncatedText
            className="text-lg font-medium w-full max-w-96"
            text={basicDetails.quoteName}
          >
            {basicDetails.quoteName}
          </TruncatedText>
          <StatusBadge status={quoteStatus} />
        </div>
        <div className="flex items-center flex-wrap justify-end gap-3 py-0.5 ml-auto">
          {renderSaveStatus()}
          {isEditMode ? (
            <EverButton
              size="small"
              prependIcon={
                <CheckCircleBrokenIcon className="w-5 h-5 shrink-0" />
              }
              onClick={handleSubmit(handleValidateAndPublish)}
              disabled={
                isLoading ||
                isAutoSaveLoading ||
                quoteStatus === STATUS_TYPES.WON
              }
            >
              {quoteStatus === STATUS_TYPES.DRAFT
                ? "Publish"
                : "Publish changes"}
            </EverButton>
          ) : (
            hasPermissions(RBAC_ROLES.EDIT_QUOTES) && (
              <EverButton
                size="small"
                type="ghost"
                color="base"
                prependIcon={<EditPencilAltIcon className="w-5 h-5 shrink-0" />}
                onClick={() => setIsEditMode(true)}
                disabled={
                  isLoading ||
                  isAutoSaveLoading ||
                  quoteStatus === STATUS_TYPES.WON ||
                  (quoteStatus === STATUS_TYPES.PENDING_SIGNATURE &&
                    templateDetails?.sharing_type === "docusign")
                }
                tooltipTitle={
                  quoteStatus === STATUS_TYPES.WON
                    ? "Cannot modify quotes that are marked won"
                    : quoteStatus === STATUS_TYPES.PENDING_SIGNATURE &&
                      templateDetails?.sharing_type === "docusign"
                    ? "You can’t edit a quote that’s out for signature. Please void the envelope before you do this."
                    : ""
                }
              >
                Edit
              </EverButton>
            )
          )}
          {quoteStatus === STATUS_TYPES.PENDING_SIGNATURE &&
            !isEditMode &&
            templateDetails?.sharing_type === "offline" && (
              <EverButton
                size="small"
                prependIcon={<ThumbsUpIcon className="w-5 h-5 shrink-0" />}
                onClick={handleMarkAsWon}
                disabled={isLoading || isAutoSaveLoading}
              >
                Mark As Won
              </EverButton>
            )}
          <EverButton
            size="small"
            type="ghost"
            color="base"
            prependIcon={
              <LogOutCircleIcon className="w-5 h-5 !text-ever-base-content-mid shrink-0" />
            }
            onClick={handleExit}
            disabled={isLoading || isAutoSaveLoading}
          >
            Exit
          </EverButton>
          {quoteStatus !== STATUS_TYPES.DRAFT && showExitConfirmModal && (
            <ExitConfirmationModal
              showExitConfirmModal={showExitConfirmModal}
              onExit={() => {
                setShowExitConfirmModal(false);
                onCloseBuildQuote();
              }}
              onClose={() => setShowExitConfirmModal(false)}
            />
          )}
        </div>
      </div>
      <Approvals />
      {/* Max width of quote page is 1600px */}
      <EverLoader
        indicatorType="spinner"
        spinning={isLoading}
        className="z-[1020] overflow-hidden"
        wrapperClassName="z-[1020]"
      >
        <div className="flex w-full max-w-[1600px] h-full m-auto pt-6 pb-5 overflow-hidden relative">
          <Sections initialLoad={initialLoad} />
          <div
            className={twMerge(
              "w-full h-full",
              isSmallScreen && showPdfViewer ? "mr-[72px]" : ""
            )}
          >
            {visibleSectionOrder.map((sectionKey) => (
              <SectionFields
                key={sectionKey}
                data={formSpec.sections[sectionKey]}
              />
            ))}
          </div>
          <PDFViewer isSmallScreen={isSmallScreen} />
        </div>
      </EverLoader>
      {!isEmpty(approvalCycles) && showApprovalsModal && (
        <ApprovalsModal
          approversData={approvalCycles}
          closeModal={closeModal}
          toPublish={true}
          handlePublish={handlePublish}
        />
      )}
      {showMarkAsWonFormModal && (
        <MarkAsWonModal
          visible={showMarkAsWonFormModal}
          onCancel={() => setShowMarkAsWonFormModal(false)}
          templateDetails={templateDetails}
          onSubmit={handleMarkAsWonSubmit}
          buttonLoading={saveQuoteDetails.isLoading}
        />
      )}
      {showMarkAsWonModal && (
        <WonCelebrationModal
          showMarkAsWonModal={showMarkAsWonModal}
          setShowMarkAsWonModal={setShowMarkAsWonModal}
          basicDetails={basicDetails}
        />
      )}
    </div>
  );
};

const parseQuoteDetails = (data) => {
  const result = safeParse(schema, data);
  if (result.success) {
    return result.output;
  } else {
    // TODO: Show error in UI
    console.error("Error in parsing schema", result.issues);
    return {};
  }
};

const BuildQuoteWrapper = (props) => {
  const { quoteId } = useParams();
  const { state } = useLocation();
  const { accessToken } = useAuthStore();
  const getQuoteDetails = useMutation(fetchQuoteDetails);

  const [visible, setVisible] = useState(true);
  const [initialData, setInitialData] = useState({});

  const refreshQuoteDetails = async () => {
    const data = await getQuoteDetails.mutateAsync({
      accessToken,
      quoteId,
    });
    const parsedData = parseQuoteDetails(data);
    setInitialData(parsedData);
    return parsedData;
  };

  const methods = useForm({
    mode: "onChange",
    defaultValues: async () => {
      const parsedData = await refreshQuoteDetails();
      return getFormValues(get(parsedData, "form_spec", {}));
    },
  });

  return (
    // TODO: Handle drawer open/close animation
    <EverDrawer
      className={twMerge(
        "create-edit-quote-drawer !translate-y-0",
        !state?.fromQuoteList &&
          "[&>.ant-drawer-content-wrapper]:!transition-none"
      )}
      visible={visible}
      bodyStyle={{ padding: 0 }}
      closable={false}
      destroyOnClose
      height="100%"
    >
      {visible && (
        <>
          {methods?.formState.isLoading ? (
            <EverLoader spinning />
          ) : isEmpty(initialData) ? (
            <div>Something went wrong, please try again later</div>
          ) : (
            <FormProvider {...methods}>
              <BuildQuoteProvider
                quoteId={quoteId}
                initialData={initialData}
                refreshQuoteDetails={refreshQuoteDetails}
                closeDrawer={() => setVisible(false)}
              >
                <BuildQuote {...props} />
              </BuildQuoteProvider>
            </FormProvider>
          )}
        </>
      )}
    </EverDrawer>
  );
};

export default BuildQuoteWrapper;
