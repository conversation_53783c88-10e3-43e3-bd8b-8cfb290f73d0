import {
  CopyIcon,
  // EyeIcon,
  StarIcon,
  Trash03Icon,
  PlusSquareIcon,
  LinkExternalIcon,
} from "@everstage/evericons/outlined";

// TODO: Update Signed Offline - badge color

export const STATUS_TYPES = {
  DRAFT: "Draft",
  PUBLISHED: "Published",
  PENDING_APPROVAL: "Pending Approval",
  APPROVED: "Approved",
  REJECTED: "Rejected",
  PENDING_SIGNATURE: "Pending Signature",
  WON: "Won",
  LOST: "Lost",
  CANCELLED: "Cancelled",
  EXPIRED: "Expired",
  SIGNED_OFFLINE: "Signed Offline",
  NEED_ATTENTION: "Needs Attention",
};

export const STATUS_OPTIONS = [
  { label: "Draft", value: STATUS_TYPES.DRAFT },
  { label: "Published", value: STATUS_TYPES.PUBLISHED },
  { label: "Pending Approval", value: STATUS_TYPES.PENDING_APPROVAL },
  { label: "Approved", value: STATUS_TYPES.APPROVED },
  { label: "Rejected", value: STATUS_TYPES.REJECTED },
  { label: "Pending Signature", value: STATUS_TYPES.PENDING_SIGNATURE },
  { label: "Won", value: STATUS_TYPES.WON },
  { label: "Lost", value: STATUS_TYPES.LOST },
  { label: "Cancelled", value: STATUS_TYPES.CANCELLED },
  { label: "Expired", value: STATUS_TYPES.EXPIRED },
  { label: "Signed Offline", value: STATUS_TYPES.SIGNED_OFFLINE },
  { label: "Needs Attention", value: STATUS_TYPES.NEED_ATTENTION },
];

export const MENU_ITEMS = [
  {
    icon: (
      <LinkExternalIcon className="w-4 h-4 text-ever-base-content-mid shrink-0" />
    ),
    type: "Open",
  },
  {
    icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid shrink-0" />,
    type: "Clone",
  },
  {
    icon: <StarIcon className="w-4 h-4 text-ever-base-content-mid" />,
    type: "Mark primary",
  },
  {
    icon: <Trash03Icon className="w-4 h-4 text-ever-error shrink-0" />,
    type: "Delete",
  },
  // {
  //   icon: <EyeIcon className="w-4 h-4 text-ever-base-content-mid" />,
  //   type: "Deal Room",
  // },
];

export const CREATE_QUOTE_TYPES = [
  {
    icon: <PlusSquareIcon />,
    title: "New Quote ✨",
    subTitle: "Create a quote from scratch",
    type: "new",
  },
  {
    icon: <CopyIcon />,
    title: "Clone a Quote ⚡️",
    subTitle: "Start with an existing quote",
    type: "clone",
  },
];

export const REMOVE_CONFIRMATION_MESSAGES = {
  title: "Are you sure to remove this quote?",
  subtitle: "This action cannot be undone",
};

export const RETRACT_QUOTE_CONFIRMATION_MESSAGES = {
  title: "Retract this quote?",
  subtitle:
    "This puts the quote back on approved status. You can revise and resend anytime.",
};

export const RETRACT_QUOTE_TOOLTIPS = {
  offline: "Use this to edit a quote that’s out for signature.",
  docusign:
    "Use this to edit a quote that’s out for signature. Docusign envelope is voided when you do this.",
};

export const QUOTE_LIST_TOAST_MESSAGES = {
  NEW: {
    loading: "Creating Quote",
    success: "Quote has been created successfully",
    error: (errorMsg) => errorMsg || "Unable to create Quote",
  },
  CLONE: {
    loading: "Cloning Quote",
    success: "Quote has been cloned successfully",
    error: (errorMsg) => errorMsg || "Unable to clone Quote",
  },
  DELETE: {
    loading: "Deleting Quote",
    success: "Quote has been deleted successfully",
    error: (errorMsg) => errorMsg || "Unable to delete Quote",
  },
};
