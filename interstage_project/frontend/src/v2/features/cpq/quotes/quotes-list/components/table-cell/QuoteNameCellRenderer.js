import {
  CircleIcon,
  Star01Icon as Star01SolidIcon,
} from "@everstage/evericons/solid";
import { Link } from "react-router-dom";

import { EverLoader, EverTg, EverTooltip } from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";

import { STATUS_TYPES } from "../../utils";

// TODO: Update color in blueBias23.js for #D9D9D9

const QuoteNameCellRenderer = ({
  quoteName,
  quoteId,
  quoteDisplayId,
  productCount,
  quotePrimary,
  quoteStatus,
  data,
}) => {
  if (quoteId === undefined) {
    return <EverLoader.SpinnerLottie className="w-8 h-8" />;
  }
  if (!quoteName) {
    return null;
  }

  return (
    <div className="flex flex-col overflow-hidden gap-1">
      <div className="flex items-center gap-2">
        <EverTg.SubHeading4 className="truncate">
          <TruncatedText text={quoteName}>
            <Link
              to={
                quoteStatus === STATUS_TYPES.DRAFT
                  ? `/cpq/quotes/${quoteId}`
                  : `/cpq/quotes-details/${quoteId}`
              }
              state={
                quoteStatus !== STATUS_TYPES.DRAFT
                  ? { quoteData: data }
                  : undefined
              }
              className="text-ever-base-content hover:underline
            hover:text-ever-base-content"
            >
              {quoteName}
            </Link>
          </TruncatedText>
        </EverTg.SubHeading4>
        {quotePrimary && (
          <EverTooltip title="Primary Quote">
            <div className="flex items-center justify-center cursor-pointer bg-ever-chartColors-42 rounded-full p-1 shadow-md">
              <Star01SolidIcon className="w-2.5 h-2.5 text-ever-primary" />
            </div>
          </EverTooltip>
        )}
      </div>
      <div className="flex items-center gap-2.5">
        <EverTg.Caption.Medium className="text-ever-base-content-mid">
          {quoteDisplayId}
        </EverTg.Caption.Medium>
        <CircleIcon className="w-1.5 h-1.5 text-[#D9D9D9]" />
        <EverTg.Caption.Medium className="text-ever-base-content-mid">
          {productCount === 0
            ? "No products added"
            : productCount > 1
            ? `${productCount} products`
            : `${productCount} product`}
        </EverTg.Caption.Medium>
      </div>
    </div>
  );
};

export default QuoteNameCellRenderer;
