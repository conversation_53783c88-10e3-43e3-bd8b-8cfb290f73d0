import { DotsVerticalIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverButton, EverTg } from "~/v2/components";
import { RemoveConfirmationModal } from "~/v2/features/cpq/components";
import useFetchApiWithAuth from "~/v2/features/cpq/hooks/useFetchApiWithAuth";

import {
  MENU_ITEMS,
  REMOVE_CONFIRMATION_MESSAGES,
  STATUS_TYPES,
} from "../../utils";

const dropdownMenu = ({
  quoteId,
  formBuilderId,
  // opportunityId,
  createQuote,
  markQuoteAsPrimary,
  deleteQuote,
  navigate,
  showRemoveConfirmModal,
  setShowRemoveConfirmModal,
  setIsHovered,
  quotePrimary,
  hasPermissions,
  quoteStatus,
  // fetchData,
}) => {
  const { title, subtitle } = REMOVE_CONFIRMATION_MESSAGES;

  // Filter menu items based on permissions
  const filteredMenuItems = MENU_ITEMS.filter((item) => {
    if (item.type === "Clone" && !hasPermissions(RBAC_ROLES.CREATE_QUOTES)) {
      return false;
    }
    if (item.type === "Open" && !hasPermissions(RBAC_ROLES.VIEW_QUOTES)) {
      return false;
    }
    if (item.type === "Delete" && !hasPermissions(RBAC_ROLES.DELETE_QUOTES)) {
      return false;
    }
    return true;
  });

  const handleMenuItemClick = (item, quoteId) => {
    switch (item) {
      case "Open": {
        if (quoteStatus === STATUS_TYPES.DRAFT) {
          navigate(quoteId, { state: { isEditMode: true } });
        } else {
          navigate(`/cpq/quotes-details/${quoteId}`);
        }
        break;
      }
      case "Clone": {
        const payload = {
          quote_id: quoteId,
          request_type: "clone",
          form_builder_id: formBuilderId,
        };
        createQuote(payload);
        break;
      }
      case "Mark primary": {
        markQuoteAsPrimary(quoteId);
        break;
      }
      case "Delete": {
        deleteQuote(quoteId);
        break;
      }
      // case "Deal Room": {
      //   // Fetch deal room details using the API
      //   fetchData(`/ninja/dealroom/${opportunityId}/generate_preview`, "GET")
      //     .then((data) => {
      //       if (data.preview_url) {
      //         window.open(data.preview_url, "_blank");
      //       } else {
      //         console.error("Error fetching deal room:", data);
      //       }
      //     })
      //     .catch((error) => {
      //       console.error("Error fetching deal room:", error);
      //     });
      //   break;
      // }
      default: {
        break;
      }
    }
  };

  return (
    <Menu className="flex items-start !p-2">
      {filteredMenuItems.map((item, key) => {
        if (item.type === "Mark primary" && quotePrimary) {
          return null;
        }
        // else if (item.type === "Deal Room") {
        //   return null;
        // }
        return (
          <React.Fragment key={key}>
            <Menu.Item className="!px-0 w-full hover:!bg-transparent">
              <EverButton
                type="text"
                color={item.type === "Delete" ? "error" : "base"}
                size="small"
                className="w-full justify-start"
                prependIcon={item.icon}
                onClick={() => {
                  setIsHovered(false);
                  item.type === "Delete"
                    ? setShowRemoveConfirmModal(true)
                    : handleMenuItemClick(item.type, quoteId);
                }}
              >
                <EverTg.Caption
                  className={twMerge(
                    "font-normal",
                    item.type === "Delete" ? "text-ever-error-lite-content" : ""
                  )}
                >
                  {item.type}
                </EverTg.Caption>
              </EverButton>
            </Menu.Item>
            {item.type === "Delete" && (
              <RemoveConfirmationModal
                visible={showRemoveConfirmModal}
                title={title}
                subtitle={subtitle}
                onConfirm={() => {
                  handleMenuItemClick(item.type, quoteId);
                  setShowRemoveConfirmModal(false);
                }}
                onCancel={() => setShowRemoveConfirmModal(false)}
              />
            )}
          </React.Fragment>
        );
      })}
    </Menu>
  );
};

const MenuButton = ({
  quoteId,
  formBuilderId,
  opportunityId,
  createQuote,
  markQuoteAsPrimary,
  deleteQuote,
  quotePrimary,
  setIsHovered,
  quoteStatus,
}) => {
  const navigate = useNavigate();
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);
  const { fetchData } = useFetchApiWithAuth();
  const { hasPermissions } = useUserPermissionStore();

  if (quoteId === undefined) return null;

  return (
    <Dropdown
      trigger={["click"]}
      overlay={dropdownMenu({
        quoteId,
        formBuilderId,
        opportunityId,
        createQuote,
        markQuoteAsPrimary,
        deleteQuote,
        navigate,
        showRemoveConfirmModal,
        setShowRemoveConfirmModal,
        setIsHovered,
        quotePrimary,
        hasPermissions,
        fetchData,
        quoteStatus,
      })}
      onVisibleChange={(visible) => setIsHovered(visible)}
    >
      <EverButton.Icon
        size="small"
        type="ghost"
        color="base"
        className="!w-8 !h-8 !p-1.5"
        icon={
          <DotsVerticalIcon className="text-ever-base-content-mid !w-4 !h-4 shrink-0" />
        }
      />
    </Dropdown>
  );
};

export default MenuButton;
