import {
  DatabaseIcon,
  BellShakeIcon,
  CalculatorIcon,
  Certificate02Icon,
  DataflowIcon,
  Dataflow04Icon,
  Tool02Icon,
  DatabaseConnectionIcon,
  ClockCheckIcon,
  EditPencilAltIcon,
  LayoutAlt02Icon,
  FileShield03Icon,
  Certificate01Icon,
  DataIcon,
} from "@everstage/evericons/outlined";
import {
  ToolsIcon as ToolsIconSolid,
  BellShakeIcon as BellShakeIconSolid,
  CalculatorIcon as CalculatorIconSolid,
  Certificate02Icon as Certificate02IconSolid,
  DataflowIcon as DataflowIconSolid,
  ClockCheckIcon as ClockCheckIconSolid,
  DatabaseIcon as DatabaseIconSolid,
  Dataflow04Icon as Dataflow04IconSolid,
  DatabaseConnectionIcon as DatabaseConnectionIconSolid,
  EditPencilLineIcon as EditPencilLineIconSolid,
  LayoutAlt02Icon as LayoutAlt02IconSolid,
  FileShield03Icon as FileShield03IconSolid,
  Certificate01Icon as Certificate01IconSolid,
  DataIcon as DataSolidIcon,
} from "@everstage/evericons/solid";

import { RBAC_ROLES } from "~/Enums";

const iconClassName = "w-5 h-5 text-ever-base-content-low";
const activeIconClassName = "w-5 h-5 text-ever-primary-pressed";

export const settingsSidebarContent = [
  {
    key: "general",
    title: " GENERAL SETTINGS",
    items: [
      {
        path: "/cpq/settings/basic-settings",
        title: "Basic Settings",
        icon: <Tool02Icon className={iconClassName} />,
        activeIcon: <ToolsIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
      {
        path: "/cpq/settings/notifications",
        title: "Notifications",
        icon: <BellShakeIcon className={iconClassName} />,
        activeIcon: <BellShakeIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
    ],
  },
  {
    key: "quote",
    title: "QUOTE SETTINGS",
    items: [
      {
        path: "/cpq/settings/quote-forms",
        title: "Quote Forms",
        icon: <Certificate02Icon className={iconClassName} />,
        activeIcon: <Certificate02IconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
      {
        path: "/cpq/settings/quote-rules",
        title: "Quote Rules",
        icon: <CalculatorIcon className={iconClassName} />,
        activeIcon: <CalculatorIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
      {
        path: "/cpq/settings/approval-rules",
        title: "Approval Rules",
        icon: <DataflowIcon className={iconClassName} />,
        activeIcon: <DataflowIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
      {
        path: "/cpq/settings/terms",
        title: "Terms Library",
        icon: <FileShield03Icon className={iconClassName} />,
        activeIcon: <FileShield03IconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_DATASETTINGS,
      },
      {
        path: "/cpq/settings/pdf-settings",
        title: "PDF Settings",
        icon: <Certificate01Icon className={iconClassName} />,
        activeIcon: <Certificate01IconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_DATASETTINGS,
      },
    ],
  },
  {
    key: "dealRoom",
    title: "DEALROOM",
    items: [
      {
        path: "/cpq/settings/deal-room-templates",
        title: "Templates",
        icon: <LayoutAlt02Icon className={iconClassName} />,
        activeIcon: <LayoutAlt02IconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
    ],
  },
  {
    key: "integration",
    title: "INTEGRATION SETTINGS",
    items: [
      {
        path: "/cpq/settings/docusign-integration",
        title: "eSignature",
        icon: <EditPencilAltIcon className={iconClassName} />,
        activeIcon: <EditPencilLineIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
      {
        path: "/cpq/settings/map-crm-entities",
        title: "Map CRM Entities",
        icon: <DataIcon className={iconClassName} />,
        activeIcon: <DataSolidIcon className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_CPQ_SETTINGS,
      },
    ],
  },
  {
    key: "advanced",
    title: "ADVANCED SETTINGS",
    items: [
      {
        path: "/cpq/settings/activity-logs",
        title: "Data Upload Logs",
        icon: <ClockCheckIcon className={iconClassName} />,
        activeIcon: <ClockCheckIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_DATASETTINGS,
      },
      {
        path: "/cpq/settings/connectors",
        title: "Connectors",
        icon: <Dataflow04Icon className={iconClassName} />,
        activeIcon: <Dataflow04IconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_DATASETTINGS,
      },
      {
        path: "/cpq/settings/manage-data",
        title: "Manage Data",
        icon: <DatabaseIcon className={iconClassName} />,
        activeIcon: <DatabaseIconSolid className={activeIconClassName} />,
        permission: RBAC_ROLES.MANAGE_DATASETTINGS,
      },
      {
        path: "/cpq/settings/commissions-and-data-sync",
        title: "Sync Tools",
        icon: <DatabaseConnectionIcon className={iconClassName} />,
        activeIcon: (
          <DatabaseConnectionIconSolid className={activeIconClassName} />
        ),
        permission: RBAC_ROLES.MANAGE_DATASETTINGS,
      },
    ],
  },
];
