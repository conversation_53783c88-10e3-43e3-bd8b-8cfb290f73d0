import { CheckCircleIcon, KeyIcon } from "@everstage/evericons/outlined";
import { debounce } from "lodash";
import React, { useEffect, useMemo, useState } from "react";
import { useLocation, Link } from "react-router-dom";

import { EverButton, EverTg, EverModal, EverInput } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";
import { docusignLogo } from "~/v2/images";

import { DOCUSIGN_CONNECT_SIGNATURE_LINK } from "./utils";

const DOCUSIGN_CONNECTION_STATUS = {
  connected: "connected",
  disconnected: "disconnected",
};

export function IntegrationButton({
  connectDocusign,
  sendAuthCode,
  isDocuSignConnected,
  hasSecret,
  isCallbackLoading,
  validateDisconnectDocusign,
  handleSaveSecretKey,
}) {
  const { search } = useLocation();
  const query = useMemo(() => new URLSearchParams(search), [search]);
  const authCode = useMemo(() => query.get("code"), [query]);

  const [docusignConnectionStatus, setDocusignConnectionStatus] = useState(
    isDocuSignConnected
      ? DOCUSIGN_CONNECTION_STATUS.connected
      : DOCUSIGN_CONNECTION_STATUS.disconnected
  );
  const [secretKeyModalVisible, setSecretKeyModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [secretKey, setSecretKey] = useState("");

  const connectButtonProps = {
    [DOCUSIGN_CONNECTION_STATUS.connected]: {
      type: "filled",
      size: "small",
      color: "success",
      buttonText: "Connected",
      prependIcon: <CheckCircleIcon />,
      onClick: () => {},
    },
    [DOCUSIGN_CONNECTION_STATUS.disconnected]: {
      type: "ghost",
      size: "small",
      color: "primary",
      buttonText: "Connect",
      onClick: debounce(() => connectDocusign(), 300),
      disabled: isCallbackLoading,
      loading: isCallbackLoading,
    },
  };

  function debouncedValidateDisconnect() {
    debounce(() => {
      validateDisconnectDocusign();
    }, 300)();
  }
  const handleOnClose = () => {
    setSecretKeyModalVisible(false);
    setErrorMessage("");
    setSecretKey("");
  };
  function debouncedSaveSecretKey() {
    debounce(() => {
      handleSaveSecretKey({ secret_key: secretKey });
      setSecretKey("");
      setTimeout(() => {
        handleOnClose();
      }, 400);
    }, 300)();
  }
  useEffect(() => {
    if (authCode) sendAuthCode(authCode);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authCode]);

  useEffect(() => {
    setDocusignConnectionStatus(
      isDocuSignConnected
        ? DOCUSIGN_CONNECTION_STATUS.connected
        : DOCUSIGN_CONNECTION_STATUS.disconnected
    );
  }, [isDocuSignConnected]);

  const handleSecretKeyChange = (e) => {
    const value = e.target.value.trim();
    if (value === "") {
      setErrorMessage("Connect key is required");
      setSecretKey("");
    } else {
      setErrorMessage("");
      setSecretKey(value);
    }
  };

  return (
    <div className="flex flex-col gap-5">
      <div className="flex flex-col gap-1.5">
        <EverTg.Heading3>eSign Provider</EverTg.Heading3>
      </div>
      <div className="flex justify-between items-center py-3 px-4 rounded-lg border border-ever-base-400">
        <div className="flex items-center gap-4">
          <img src={docusignLogo} alt="Docusign Logo" />
          <div className="flex flex-col gap-1">
            <EverTg.SubHeading4 className="text-ever-base-content">
              DocuSign
            </EverTg.SubHeading4>
            <EverTg.Text className="text-ever-base-content-mid">
              Use your DocuSign account to send, track, and manage quote
              signatures directly from Everstage.
            </EverTg.Text>
          </div>
        </div>
        <div className="flex gap-2">
          {isDocuSignConnected && (
            <EverButton
              type="link"
              size="small"
              color="primary"
              onClick={() => {
                setSecretKeyModalVisible(true);
              }}
              prependIcon=<KeyIcon />
            >
              {hasSecret ? "Update Connect Key" : "Add Connect Key"}
            </EverButton>
          )}
          <EverButton
            className="!w-[120px]"
            {...connectButtonProps[docusignConnectionStatus]}
          >
            {connectButtonProps[docusignConnectionStatus].buttonText}
          </EverButton>
          {isDocuSignConnected && (
            <EverButton
              type="link"
              size="small"
              color="primary"
              onClick={debouncedValidateDisconnect}
            >
              Disconnect
            </EverButton>
          )}
        </div>
        <EverModal
          visible={secretKeyModalVisible}
          onCancel={handleOnClose}
          title="Add Docusign Connect Key"
          footer={[
            <EverButton
              key="cancel"
              color="base"
              onClick={handleOnClose}
              type="ghost"
            >
              Cancel
            </EverButton>,
            <EverButton
              key="save"
              disabled={!secretKey}
              onClick={debouncedSaveSecretKey}
            >
              Save
            </EverButton>,
          ]}
        >
          <LabeledField required label="Paste here" labelPlacement="top">
            <EverInput
              placeholder="Key"
              onChange={handleSecretKeyChange}
              value={secretKey}
            />
            {errorMessage && (
              <EverTg.Caption className="text-ever-error">
                {errorMessage}
              </EverTg.Caption>
            )}
            <Link
              to={DOCUSIGN_CONNECT_SIGNATURE_LINK}
              target="_blank"
              rel="noopener noreferrer"
            >
              Create a connect key from here
            </Link>
          </LabeledField>
        </EverModal>
      </div>
    </div>
  );
}
