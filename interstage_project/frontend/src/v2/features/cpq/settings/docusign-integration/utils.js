// Actions that can be performed on a recipient
export const RECIPIENT_ACTIONS = Object.freeze({
  ADD: "add",
  UPDATE: "update",
  DELETE: "delete",
  REORDER: "reorder",
});

export const SIGNING_PREFERENCES = Object.freeze({
  NEEDS_TO_SIGN: "Needs To Sign",
  RECEIVES_A_COPY: "Receives A Copy",
  NEEDS_TO_VIEW: "Needs To View",
});

export const SIGNING_PREFERENCES_OPTIONS = Object.freeze([
  {
    label: "Needs to sign",
    value: SIGNING_PREFERENCES.NEEDS_TO_SIGN,
  },
  { label: "Receives a copy", value: SIGNING_PREFERENCES.RECEIVES_A_COPY },
  { label: "Needs to view", value: SIGNING_PREFERENCES.NEEDS_TO_VIEW },
]);

export default function getCroppedImgFromUrl(imageSrc, pixelCrop) {
  return new Promise((resolve, reject) => {
    const image = new window.Image();
    image.src = imageSrc;
    image.addEventListener("load", () => {
      const canvas = document.createElement("canvas");
      canvas.width = pixelCrop.width;
      canvas.height = pixelCrop.height;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(
        image,
        pixelCrop.x,
        pixelCrop.y,
        pixelCrop.width,
        pixelCrop.height,
        0,
        0,
        pixelCrop.width,
        pixelCrop.height
      );
      resolve(canvas.toDataURL("image/png"));
    });
    image.addEventListener("error", reject);
  });
}

export function getPureBase64(dataUrl) {
  return dataUrl.split(",")[1];
}

export const DOCUSIGN_CONNECT_SIGNATURE_LINK =
  "https://apps-d.docusign.com/admin/connect-signatures";
