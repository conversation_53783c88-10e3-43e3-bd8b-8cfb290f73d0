import { TrashIcon } from "@everstage/evericons/outlined";
import { cloneDeep, debounce } from "lodash";
import React, { useEffect, useMemo, useState, useRef } from "react";
import { useMutation, useQuery } from "react-query";
import { v4 as uuidv4 } from "uuid";

import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { message, EverButton, EverModal } from "~/v2/components";
import { useFetchApiWithAuth } from "~/v2/hooks";

import { RECIPIENT_ACTIONS, SIGNING_PREFERENCES } from "./utils";
import { isValidEmail } from "../../quotes/build-quote/utils";

const DEFAULT_COMPANY_NAME = "everstage";

export function useESignature() {
  const { fetchData } = useFetchApiWithAuth();
  const { userEmail } = useEmployeeStore();

  const [disabledTooltip, setDisabledTooltip] = useState("");
  const [savedRecipients, setSavedRecipients] = useState([]);
  const [currentRecipients, setCurrentRecipients] = useState(null);
  const [isDocuSignConnected, setIsDocuSignConnected] = useState(false);
  const [hasSecret, setHasSecret] = useState(false);
  const [allowOfflineSigning, setAllowOfflineSigning] = useState(false);
  const [mandateOfflineSigning, setMandateOfflineSigning] = useState(false);
  const [companyName, setCompanyName] = useState(DEFAULT_COMPANY_NAME);
  const modalRef = useRef(null);
  const {
    isFetching,
    refetch: refetchSettings,
    data,
  } = useQuery(
    ["getClientSettings"],
    () => {
      return fetchData(`/ninja/cpq/docusign/account/settings`, "POST", {
        email_id: userEmail,
      });
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        const recipients = data.default_signer;
        setSavedRecipients(recipients);
        setCurrentRecipients(recipients);
        setIsDocuSignConnected(data.has_integration);
        setHasSecret(data?.has_secret);
        setAllowOfflineSigning(data.allow_offline_signing);
        setMandateOfflineSigning(data.mandate_offline_signing);
      },
    }
  );

  const updateAccountSettingsRequest = useMutation(
    (data) =>
      fetchData("/ninja/cpq/docusign/save/account_settings", "POST", data),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: async (data) => {
        message.success(
          data?.message || "Account settings updated successfully"
        );
        setSavedRecipients(currentRecipients);
      },
    }
  );

  // ENDPOINTS RELATED TO DOCUSIGN CONNECTION/DISCONNECTION

  const validateAuthCode = useMutation(
    (data) => fetchData("/ninja/cpq/docusign/oauth/callback", "POST", data),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: () => {
        refetchSettings();
        message.success("DocuSign connected successfully");
      },
    }
  );

  useQuery(
    ["getBasicSettings"],
    () => {
      return fetchData(`/ninja/cpq/basic_settings/get`, "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        if (data?.company_info?.company_name) {
          setCompanyName(
            data?.company_info?.company_name || DEFAULT_COMPANY_NAME
          );
        }
      },
    }
  );

  const { refetch: initiateDocusignOAuthDisconnect } = useQuery(
    ["docusignOAuthDisconnectCPQ"],
    () => fetchData("/ninja/cpq/docusign/logout/url", "GET"),
    {
      enabled: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (data) => {
        try {
          const url = data?.url?.trim();
          // window.open(url, "_blank");
          window.location.href = url;

          disconnectDocusign.mutate({
            email_id: userEmail,
          });
        } catch (error) {
          console.error("Error opening DocuSign OAuth URL:", error);
          message.error("Failed to open DocuSign authentication page");
        }
      },
    }
  );

  const disconnectDocusign = useMutation(
    (data) => fetchData("/ninja/cpq/docusign/oauth/disconnect", "POST", data),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: () => {
        message.success("DocuSign disconnected successfully");
      },
    }
  );

  const { refetch: initiateDocusignOAuth } = useQuery(
    ["docusignOAuthCPQ"],
    () => fetchData("/ninja/cpq/docusign/oauth/auth", "GET"),
    {
      enabled: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (data) => {
        try {
          const url = data.trim();
          window.open(url, "_blank")?.focus();
        } catch (error) {
          console.error("Error opening DocuSign OAuth URL:", error);
          message.error("Failed to open DocuSign authentication page");
        }
      },
    }
  );

  function handleDisconnectDocusign() {
    initiateDocusignOAuthDisconnect();
  }

  function connectDocusign() {
    initiateDocusignOAuth();
  }

  function sendAuthCode(authCode) {
    validateAuthCode.mutate({
      code: authCode,
      login_email_id: userEmail,
    });
  }

  function handleOfflineSigningChange(checked) {
    setAllowOfflineSigning(checked);
  }

  function handleMandateOfflineSigningChange(checked) {
    setMandateOfflineSigning(checked);
  }

  function handleRecipientChange({ action, payload }) {
    switch (action) {
      case RECIPIENT_ACTIONS.ADD: {
        setCurrentRecipients([...currentRecipients, payload]);
        break;
      }
      case RECIPIENT_ACTIONS.UPDATE: {
        setCurrentRecipients(
          currentRecipients.map((recipient) =>
            recipient.id === payload.id ? payload : recipient
          )
        );
        break;
      }
      case RECIPIENT_ACTIONS.DELETE: {
        setCurrentRecipients(
          currentRecipients.filter((recipient) => recipient.id !== payload.id)
        );
        break;
      }
      case RECIPIENT_ACTIONS.REORDER: {
        setCurrentRecipients(payload.recipients);
        break;
      }
      default: {
        // Handle invalid action type
        console.warn(`Unhandled recipient action type: ${action}`);
      }
    }
  }

  function handleAddNewRecipient() {
    setCurrentRecipients([
      ...currentRecipients,
      {
        id: uuidv4(),
        name: "",
        email: "",
        role: "",
        signingPreference: SIGNING_PREFERENCES.NEEDS_TO_SIGN,
        title: companyName,
        routingOrder: currentRecipients.length + 1,
      },
    ]);
  }

  function handleRemoveRecipient(id) {
    setCurrentRecipients(
      currentRecipients.filter((recipient) => recipient.id !== id)
    );
  }

  function handleSaveRecipients() {
    const payload = cloneDeep(currentRecipients);
    for (const recipient of payload) {
      recipient.name = recipient.name.trim();
      recipient.email = recipient.email.trim();
      recipient.role = recipient.role.trim();
      recipient.title = companyName;
    }

    updateAccountSettingsRequest.mutate({
      recipients: payload,
      allow_offline_signing: allowOfflineSigning,
      mandate_offline_signing: mandateOfflineSigning,
    });
  }

  function handleSaveSecretKey(data) {
    updateAccountSettingsRequest.mutate(data);
  }

  const saveButtonState = useMemo(() => {
    // If currentRecipients is null or undefined, disable the button
    if (!currentRecipients) {
      return { disabled: true, tooltip: "Loading recipients..." };
    }

    // Check if there are any changes compared to saved recipients
    if (
      JSON.stringify(savedRecipients) === JSON.stringify(currentRecipients) &&
      allowOfflineSigning === data?.allow_offline_signing &&
      mandateOfflineSigning === data?.mandate_offline_signing
    ) {
      return { disabled: true, tooltip: "No changes detected" };
    }

    // Validate each recipient
    for (const [i, recipient] of currentRecipients.entries()) {
      // Check for empty required fields
      if (!recipient.name.trim()) {
        return {
          disabled: true,
          tooltip: `Recipient ${i + 1}: Name is required`,
        };
      }

      if (!recipient.email.trim()) {
        return {
          disabled: true,
          tooltip: `Recipient ${i + 1}: Email is required`,
        };
      }

      if (!recipient.signingPreference) {
        return {
          disabled: true,
          tooltip: `Recipient ${i + 1}: Signing preference is required`,
        };
      }

      // Validate email format
      if (!isValidEmail(recipient.email.trim())) {
        return {
          disabled: true,
          tooltip: `Recipient ${i + 1}: Invalid email format`,
        };
      }
    }

    // Check for duplicate emails within recipients
    const emails = currentRecipients
      .map((r) => r.email?.trim()?.toLowerCase())
      .filter(Boolean);

    const emailSet = new Set();
    for (const email of emails) {
      if (emailSet.has(email)) {
        return {
          disabled: true,
          tooltip: `Duplicate email found: ${email}`,
        };
      }
      emailSet.add(email);
    }

    // All validations passed
    return { disabled: false, tooltip: "" };
  }, [
    savedRecipients,
    currentRecipients,
    allowOfflineSigning,
    data,
    mandateOfflineSigning,
  ]);

  function isSaveButtonDisabled() {
    return saveButtonState.disabled;
  }
  function debouncedHandleDisconnect() {
    debounce(() => {
      handleDisconnectDocusign();
      setTimeout(() => {
        modalRef.current.destroy();
      }, 500);
    }, 300)();
  }

  function handleDisconnect() {
    modalRef.current = EverModal.confirm({
      title: "Are you sure to disconnect Docusign?",
      subtitle: "This action cannot be undone",
      icon: <TrashIcon className="w-8 h-8 text-ever-error" />,
      iconContainerClasses: "bg-ever-error-lite",
      confirmationButtons: [
        <EverButton
          key="cancel"
          type="filled"
          color="base"
          onClick={() => closeModal()}
        >
          Cancel
        </EverButton>,
        <EverButton
          key="delete"
          onClick={debouncedHandleDisconnect}
          type="filled"
          color="error"
        >
          Yes, disconnect
        </EverButton>,
      ],
    });
    const closeModal = () => {
      modalRef.current.destroy();
    };
  }

  const validateDocusign = useMutation(
    (data) =>
      fetchData("/ninja/cpq/docusign/validate_disconnect", "POST", data),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (data) => {
        if (data?.status === "Valid") {
          handleDisconnect();
        } else if (data?.status === "Not Valid") {
          message.error(data?.message || "Something went wrong");
        }
      },
    }
  );

  function validateDisconnectDocusign() {
    validateDocusign.mutate({
      email_id: userEmail,
    });
  }

  useEffect(() => {
    setDisabledTooltip(saveButtonState.tooltip);
  }, [saveButtonState]);

  return {
    disabledTooltip,
    savedRecipients,
    isFetching,
    currentRecipients,
    isDocuSignConnected,
    hasSecret,
    isSaveInProgress: updateAccountSettingsRequest.isLoading,
    allowOfflineSigning,
    mandateOfflineSigning,
    isCallbackLoading: validateAuthCode.isLoading,
    isSaveButtonDisabled,
    handleRecipientChange,
    handleAddNewRecipient,
    handleRemoveRecipient,
    handleSaveRecipients,
    connectDocusign,
    sendAuthCode,
    handleOfflineSigningChange,
    handleMandateOfflineSigningChange,
    validateDisconnectDocusign,
    handleSaveSecretKey,
  };
}
