import { motion } from "framer-motion";
import React, { Suspense } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { EverTg, EverLoader } from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";

import { settingsSidebarContent } from "./SettingsSidebarContent";

const PADDED_ROUTES = new Set([
  "connectors",
  "commissions-and-data-sync",
  "manage-data",
  "activity-logs",
]);

const SettingsLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const renderNavItem = (item) => {
    const isActive = location.pathname.startsWith(item.path);
    // TODO: SAKSHAM: Add RBAC check to render the nav item
    return (
      <RBACProtectedComponent key={item.path} permissionId={item.permission}>
        <li>
          <div className="w-full h-full flex justify-between gap-3 group cursor-pointer">
            <div
              onClick={() => navigate(item.path)}
              className={twMerge(
                "flex items-center gap-2 px-3 py-2 rounded-lg w-full transition-all duration-200 ease-in-out",
                isActive
                  ? "bg-ever-info-pressed/10 text-ever-base-content"
                  : "text-ever-base-content-mid hover:bg-ever-sidebar-base hover:text-ever-base-content"
              )}
            >
              {isActive ? item.activeIcon : item.icon}
              <EverTg.Caption.Medium className="transition-colors duration-200">
                {item.title}
              </EverTg.Caption.Medium>
            </div>
            <div
              className={twMerge(
                "w-1 rounded-l-xl transition-all duration-200 ease-in-out",
                isActive
                  ? "bg-ever-primary-pressed scale-y-100"
                  : "group-hover:bg-ever-primary-pressed/20 scale-y-0 group-hover:scale-y-100"
              )}
            />
          </div>
        </li>
      </RBACProtectedComponent>
    );
  };

  return (
    <div className="flex w-full h-full">
      {/* Settings Sidebar - fixed width */}
      <div className="bg-ever-base-50 py-3 pl-7 fixed top-11 w-60 min-w-60 rounded-t-xl">
        <EverTg.Heading2>Settings</EverTg.Heading2>
      </div>
      <div className="w-64 min-w-64 border-r border-ever-base-400 overflow-y-auto bg-ever-base-50 pl-4 flex flex-col gap-4">
        {settingsSidebarContent.map((content, index) => {
          const { key, title, items } = content;
          return (
            <div
              key={key}
              className={twMerge("flex flex-col gap-2", index === 0 && "mt-12")}
            >
              <EverTg.Caption className="text-ever-base-content-mid px-3 py-2 font-medium">
                {title}
              </EverTg.Caption>
              <ul className="space-y-2">
                {items.map((item) => renderNavItem(item))}
              </ul>
            </div>
          );
        })}
      </div>

      {/* Content Area - Create a wrapper to enforce contained layout */}
      <div
        className={twMerge(
          "flex-1 relative overflow-y-auto h-full",
          PADDED_ROUTES.has(location.pathname.split("/").pop()) && "p-6"
        )}
      >
        <Suspense
          fallback={
            <div className="h-full flex items-center justify-center">
              <EverLoader
                indicatorType="spinner"
                className="flex"
                wrapperClassName="z-20"
                spinning
              />
            </div>
          }
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="h-full"
          >
            <Outlet />
          </motion.div>
        </Suspense>
      </div>
    </div>
  );
};

export default SettingsLayout;
