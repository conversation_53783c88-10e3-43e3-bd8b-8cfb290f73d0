import { message } from "antd";
import base64 from "base-64";
import { useState } from "react";
import { useMutation } from "react-query";

import { useFetchApi } from "~/v2/features/cpq/hooks";

import { API, API_ENDPOINTS } from "../constants";

const useTermContext = (formBuilderId) => {
  const [termsList, setTermsList] = useState(null);
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const getTermsDataMutation = useMutation(
    () => fetchEndpoint(API.GET_FORM_BUILDER_TERMS, {}, formBuilderId),
    {
      retry: false,
      onSuccess: (data) => {
        setTermsList(data.terms);
      },
      onError: () => {
        message.error("Something went wrong! Please try again later.");
      },
    }
  );

  const { isLoading: isLoadingTerms, mutate: getTermsData } =
    getTermsDataMutation;
  const updateTermData = useMutation(
    /**
     * @param {Object} data
     * @param {Array<Object>} data.terms
     */
    /** @type {(data: {terms: Array<{value: string, [key: string]: any}>}) => Promise<any>} */
    (data) =>
      fetchEndpoint(
        API.CREATE_UPDATE_FORM_BUILDER_TERMS,
        {
          body: {
            terms: (data?.terms || []).map((term) => ({
              ...term,
              value: base64.encode(term.value || ""),
            })),
          },
        },
        formBuilderId
      ),
    {
      retry: false,
    }
  );

  return {
    termsList,
    getTermsData,
    updateTermData,
    isLoadingTerms,
  };
};

export default useTermContext;
