/* eslint-disable no-restricted-imports */
import { PlusCircleIcon } from "@everstage/evericons/outlined";
import { useEffect, useRef, useState } from "react";
import { useMutation } from "react-query";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

import {
  EverButton,
  EverLoader,
  // EverSelect,
  EverTg,
  EmptyPagePlaceholder,
} from "~/v2/components";
import { useFetchApi } from "~/v2/features/cpq/hooks";
import { createQuotes } from "~/v2/images";

import { API_ENDPOINTS, API } from "./constants";
import CreateTermModal from "./CreateTermModal";
import useTermContext from "./hooks/useTermContext";
import TermsCard from "./TermsCard";

const TermsLibraryList = () => {
  const initialLoading = useRef(true);

  const [showEmptyScreen, setShowEmptyScreen] = useState(false);
  const [allFormData, setAllFormData] = useState([]);
  const [selectedFormBuilderId, setSelectedFormBuilderId] = useState(null);
  const [isCreateTermModalOpen, setIsCreateTermModalOpen] = useState(false);

  const navigate = useNavigate();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);
  const { termsList, getTermsData, updateTermData, isLoadingTerms } =
    useTermContext(selectedFormBuilderId);

  const { mutate: getAllFormData } = useMutation(
    (body) =>
      fetchEndpoint(API.GET_ALL_FORM_BUILDERS_BASIC_INFO, { body: body }),
    {
      onSuccess: (data) => {
        setAllFormData(data.forms);
      },
    }
  );

  const handleCloneTerm = (termId) => {
    const term = termsList.find((t) => t.term_id === termId);
    const newTerm = {
      ...term,
      term_id: uuidv4(),
      default: false,
      active: false,
    };
    updateTermData.mutate(
      {
        terms: [...termsList, newTerm],
      },
      {
        onSuccess: () => {
          getTermsData(selectedFormBuilderId);
        },
      }
    );
  };

  const handleDeleteTerm = (termId) => {
    const newTermsList = termsList.filter((t) => t.term_id !== termId);
    updateTermData.mutate(
      {
        terms: newTermsList,
      },
      {
        onSuccess: () => {
          getTermsData(selectedFormBuilderId);
        },
      }
    );
  };

  const handleUpdateTerm = (updatedTerm) => {
    updateTermData.mutate(
      {
        terms: termsList.map((t) =>
          t.term_id === updatedTerm.term_id ? { ...t, ...updatedTerm } : t
        ),
      },
      {
        onSuccess: () => {
          getTermsData(selectedFormBuilderId);
        },
      }
    );
  };

  const handleCreateTerm = (newTerm) => {
    newTerm.term_id = uuidv4();
    updateTermData.mutate(
      {
        terms: [...termsList, newTerm],
      },
      {
        onSuccess: () => {
          navigate(
            `/cpq/settings/terms/${selectedFormBuilderId}/${newTerm.term_id}`
          );
        },
      }
    );
  };

  useEffect(() => {
    if (selectedFormBuilderId) {
      getTermsData(selectedFormBuilderId);
    }
  }, [getTermsData, selectedFormBuilderId]);

  useEffect(() => {
    getAllFormData();
  }, [getAllFormData]);

  useEffect(() => {
    if (allFormData.length > 0) {
      setSelectedFormBuilderId(allFormData.at(-1).form_builder_id);
    }
  }, [allFormData]);

  useEffect(() => {
    setShowEmptyScreen(!isLoadingTerms && termsList && termsList.length === 0);
    initialLoading.current = isLoadingTerms;
  }, [termsList, isLoadingTerms]);

  const isLoading = !allFormData || isLoadingTerms;
  const anyLoaderActive = isLoading;

  const renderContent = () => {
    if (anyLoaderActive) {
      return (
        <EverLoader
          indicatorType="spinner"
          className="flex h-full"
          wrapperClassName="z-20"
          spinning={anyLoaderActive}
        />
      );
    }

    if (showEmptyScreen) {
      return (
        <div className="h-full">
          <EmptyPagePlaceholder
            title="Add terms to your library"
            description="Start with NDA clauses, payment terms, renewal language, and more."
            image={createQuotes}
            buttonsList={[
              <EverButton
                size="small"
                type="filled"
                color="primary"
                prependIcon={<PlusCircleIcon />}
                key="createNewRule"
                onClick={() => setIsCreateTermModalOpen(true)}
              >
                Create New Term
              </EverButton>,
            ]}
          />
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full w-full">
        <div className="flex justify-between items-center px-8 py-4 bg-ever-base-50 border-b border-ever-base-400 sticky top-0 z-10">
          <div className="flex flex-col gap-1.5">
            <EverTg.Heading2>Terms Library</EverTg.Heading2>
            <EverTg.Text>
              Standardize and manage reusable legal, commercial, and custom
              terms across your quotes.
            </EverTg.Text>
          </div>
          {termsList && termsList.length > 0 && (
            <EverButton
              prependIcon={<PlusCircleIcon />}
              type="filled"
              color="primary"
              size="small"
              onClick={() => setIsCreateTermModalOpen(true)}
              // disabled={disableControls}
            >
              Create New Term
            </EverButton>
          )}
        </div>
        <div
          className={twMerge(
            "flex items-center justify-end gap-4 px-6 pb-4 z-10"
            // isContentScrolled && "shadow-[0_16px_10px_-16px_#a5a5a5]"
          )}
        >
          {/* <EverSelect
            options={allFormData.map((form) => ({
              label: form.form_builder_name,
              value: form.form_builder_id,
            }))}
            onChange={(value) => setSelectedFormBuilderId(value)}
            value={selectedFormBuilderId}
            className="w-[200px]"
          /> */}
        </div>

        <EverLoader
          indicatorType="spinner"
          className="flex flex-col overflow-auto"
          wrapperClassName="z-20"
          spinning={anyLoaderActive}
        >
          {termsList && termsList.length > 0 && (
            <div className="flex flex-col gap-4 h-full px-8 py-2 mb-4 overflow-auto w-full">
              {termsList &&
                termsList.map((term) => (
                  <TermsCard
                    key={term.term_id + term.active.toString()}
                    term={{
                      ...term,
                      status: term.active ? "active" : "inactive",
                    }}
                    formBuilderId={selectedFormBuilderId}
                    onClone={() => handleCloneTerm(term.term_id)}
                    onUpdate={(updatedTerm) => handleUpdateTerm(updatedTerm)}
                    onDelete={() => handleDeleteTerm(term.term_id)}
                    // isLoading={createTerm.isLoading}
                  />
                ))}
            </div>
          )}
        </EverLoader>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full py-1">
      <CreateTermModal
        visible={isCreateTermModalOpen}
        onClose={() => setIsCreateTermModalOpen(false)}
        onSubmit={(newTerm) => handleCreateTerm(newTerm)}
        // loading={createTerm.isLoading}
      />
      <div className="flex flex-col h-full">{renderContent()}</div>
    </div>
  );
};

export default TermsLibraryList;
