import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useForm, useWatch, Controller } from "react-hook-form";

import {
  EverModal,
  EverButton,
  EverTg,
  EverInput,
  EverSwitch,
  MultiTabDropdownSelect,
} from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

import { FetchUsers } from "../common/commonComponents";

const CreateTermModal = ({
  visible,
  onClose,
  onSubmit,
  initialValues,
  loading = false,
}) => {
  const [isPermissions, setIsPermissions] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);

  const tabs = [
    {
      name: "Users",
      key: "users",
      isLazy: true,
      selectedValues: selectedUsers,
      renderLazyList: () => (
        <FetchUsers
          selectedUsers={selectedUsers}
          onClickItem={(object) => {
            const email = object.value;
            const isSelected = selectedUsers.find(
              (user) => user.value === email
            );
            if (isSelected) {
              setSelectedUsers(
                selectedUsers.filter((user) => user.value !== email)
              );
            } else {
              setSelectedUsers([...selectedUsers, object]);
            }
          }}
        />
      ),
    },
  ];

  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      name: initialValues?.name || "",
      description: initialValues?.description || "",
      default_value: initialValues?.default || false,
      permissions: initialValues?.permissions || [],
    },
  });
  const name = useWatch({ control, name: "name" });

  const handleClose = () => {
    setSelectedUsers([]);
    reset();
    onClose();
  };

  const onRemove = (objects) => {
    setSelectedUsers(objects);
  };

  const handleCreateQuoteForm = ({ name, description, default_value }) => {
    onSubmit({
      name: name,
      description: description,
      default: default_value,
      permissions: selectedUsers,
    });
  };

  useEffect(() => {
    setIsPermissions(
      initialValues?.permissions && initialValues?.permissions.length > 0
        ? true
        : false
    );
    setSelectedUsers(initialValues?.permissions || []);
  }, [initialValues, visible]);

  return (
    <EverModal
      visible={visible}
      onCancel={!loading && handleClose}
      destroyOnClose
      width={640}
    >
      <div className="flex flex-col items-center justify-between py-10 px-24 gap-12 w-full">
        <div className="flex flex-col w-full gap-3 text-center">
          <EverTg.Heading1>
            {initialValues ? "Edit Term" : "Create New Term"}
          </EverTg.Heading1>
        </div>
        <div className="flex flex-col gap-8 w-full">
          <div className="flex flex-col gap-4">
            <LabeledField
              label="Name"
              className="gap-2"
              labelClassName="text-xs"
              error={errors.name}
              required
            >
              <Controller
                control={control}
                name="name"
                rules={{
                  required: "Term name is required",
                  validate: (value) => {
                    if (value.trim() === "") {
                      return "Term name is required";
                    }
                    return true;
                  },
                }}
                render={({ field }) => (
                  <EverInput {...field} placeholder="Enter term name" />
                )}
              />
            </LabeledField>
            <LabeledField
              label="Description"
              className="gap-2"
              labelClassName="text-xs"
            >
              <Controller
                control={control}
                name="description"
                render={({ field }) => (
                  <EverInput.TextArea
                    {...field}
                    placeholder="Enter description"
                    autoSize={{ minRows: 2, maxRows: 2 }}
                  />
                )}
              />
            </LabeledField>
            <Controller
              control={control}
              name="default_value"
              render={({ field }) => (
                <EverSwitch
                  {...field}
                  checked={field.value}
                  label="Add this term to all quotes by default"
                />
              )}
            />
            <EverSwitch
              label="Configure who can modify this term on the quote"
              checked={isPermissions}
              onChange={(checked) => setIsPermissions(checked)}
            />
            {isPermissions && (
              <MultiTabDropdownSelect
                placeholder="Search Users"
                allowClear={false}
                selectClassName="w-full "
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                tabsClassName="h-96"
                tabs={tabs}
                maxTagCount={5}
                onRemove={(objects) => {
                  onRemove(objects);
                }}
              />
            )}
          </div>
          <EverButton
            appendIcon={<ArrowCircleRightIcon className="w-5 h-5" />}
            onClick={handleSubmit(handleCreateQuoteForm)}
            disabled={isEmpty(name.trim())}
            loading={loading}
          >
            {initialValues ? "Update term" : "Add term"}
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
};

export default CreateTermModal;
