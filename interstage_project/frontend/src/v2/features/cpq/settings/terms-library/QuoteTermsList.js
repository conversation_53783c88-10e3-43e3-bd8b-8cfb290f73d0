/* eslint-disable no-restricted-imports */
import { ChevronDownIcon, PlusCircleIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import base64 from "base-64";
import { useCallback, useEffect, useState } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useMutation } from "react-query";

import {
  EverButton,
  EverLoader,
  EverTg,
  EmptyPagePlaceholder,
  message,
} from "~/v2/components";
import { useFetchApi } from "~/v2/features/cpq/hooks";
import { createQuotes } from "~/v2/images";

import { API_ENDPOINTS, API } from "./constants";
import useTermContext from "./hooks/useTermContext";
import QuoteCard from "./QuoteCard";

const NoTermsFound = ({ isDropdown = false }) => {
  if (isDropdown) {
    return (
      <Menu.Item className="flex items-center justify-center w-full hover:!bg-transparent !h-9 px-2">
        <EverTg.Text className="text-ever-base-content-mid">
          No terms found
        </EverTg.Text>
      </Menu.Item>
    );
  }
  return (
    <EmptyPagePlaceholder
      title="Include relevant terms from the library"
      image={createQuotes}
      buttonsList={[]}
    />
  );
};

const termOptionsMenu = (termsList, quoteTerms, handleAddTerm) => {
  const validTerms =
    termsList && termsList.length > 0
      ? termsList.filter(
          (term) =>
            term.active &&
            (quoteTerms.length === 0 ||
              !quoteTerms.some((qt) => qt.term_id === term.term_id))
        )
      : [];
  return (
    <Menu
      className="flex m-auto !w-fit"
      onClick={(event) => {
        event.domEvent.stopPropagation();
      }}
    >
      {validTerms.length === 0 ? (
        <NoTermsFound isDropdown />
      ) : (
        validTerms.map((term, index) => (
          <Menu.Item
            className="!px-0 w-full hover:!bg-transparent !h-9"
            key={index}
          >
            <EverButton
              type="text"
              color="base"
              size="small"
              className="w-full justify-start"
              onClick={() => handleAddTerm(term)}
            >
              {term.name}
            </EverButton>
          </Menu.Item>
        ))
      )}
    </Menu>
  );
};

const QuoteTermsList = ({
  formBuilderId,
  quoteId,
  fieldValues,
  quoteTerms,
  setQuoteTerms,
}) => {
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const [fieldMap, setFieldMap] = useState([]);

  const { termsList, getTermsData } = useTermContext(formBuilderId);

  const replaceFieldValues = useCallback(
    (object) => {
      let jsonString = JSON.stringify(object);

      Object.keys(fieldMap).map((key) => {
        jsonString = jsonString.replaceAll(
          new RegExp(`{{${fieldMap[key]}}}`, "g"),
          fieldValues?.[key] ?? ""
        );
      });

      return JSON.parse(jsonString);
    },
    [fieldMap, fieldValues]
  );

  const { mutate: getFieldMap, isLoading: getFieldMapIsLoading } = useMutation(
    () =>
      fetchEndpoint(API.GET_FIELD_MAP_BY_FORM_BUILDER_ID, {}, formBuilderId),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setFieldMap(data.field_map);
      },
    }
  );

  const { mutate: getQuoteTerms, isLoading: getQuoteTermsIsLoading } =
    useMutation(() => fetchEndpoint(API.GET_QUOTE_TERMS, {}, quoteId), {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setQuoteTerms(replaceFieldValues(data.terms));
      },
    });

  const updateQuoteTerms = useMutation(
    /**
     * @param {Object} data
     * @param {Array<Object>} data.terms
     */
    /** @type {(data: {terms: Array<{value: string, [key: string]: any}>}) => Promise<any>} */
    (data) =>
      fetchEndpoint(
        API.UPDATE_QUOTE_TERMS,
        {
          body: {
            ...data,
            terms: data.terms.map((term) => ({
              ...term,
              value: base64.encode(term.value || ""),
            })),
          },
        },
        quoteId
      ),
    {
      retry: false,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setQuoteTerms(replaceFieldValues(data.terms));
      },
    }
  );

  const handleDeleteTerm = (termId) => {
    const newTermsList = quoteTerms.filter((t) => t.term_id !== termId);
    updateQuoteTerms.mutate({
      terms: newTermsList,
    });
  };

  const handleUpdateTerm = (updatedTerm) => {
    updateQuoteTerms.mutate({
      terms: quoteTerms.map((t) =>
        t.term_id === updatedTerm.term_id ? updatedTerm : t
      ),
    });
  };

  const handleAddTerm = (term) => {
    const newTermsList = [...quoteTerms, term];
    updateQuoteTerms.mutate({
      terms: newTermsList,
    });
  };

  const handleRefreshTerm = (termId) => {
    getTermsData(
      {},
      {
        onSuccess: (data) => {
          const newTerm = data?.terms?.find((t) => t.term_id === termId);
          if (!newTerm) {
            message.error("Term is no longer available in the library");
            return;
          }
          const newTermsList = quoteTerms.map((t) =>
            t.term_id === termId ? { ...t, ...newTerm } : t
          );
          updateQuoteTerms.mutate({
            terms: newTermsList,
          });
          message.success("Term refreshed successfully");
        },
      }
    );
  };

  useEffect(() => {
    getTermsData(formBuilderId);
  }, [getTermsData, formBuilderId]);

  useEffect(() => {
    if (formBuilderId && quoteId) {
      getQuoteTerms();
      getFieldMap();
    }
  }, [formBuilderId, quoteId, getFieldMap, getQuoteTerms]);

  // useEffect(() => {
  //   setQuoteTerms((prevQuoteTerms) => replaceFieldValues(prevQuoteTerms));
  // }, [setQuoteTerms, replaceFieldValues]);

  const isLoading = getQuoteTermsIsLoading || getFieldMapIsLoading;
  const anyLoaderActive = isLoading;

  const handleActiveFieldsOnDragEnd = (result) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    const updatedTerms = [...quoteTerms];
    const [movedTerm] = updatedTerms.splice(sourceIndex, 1);
    updatedTerms.splice(destinationIndex, 0, movedTerm);

    setQuoteTerms(updatedTerms);
    updateQuoteTerms.mutate({
      terms: updatedTerms,
    });
  };

  return (
    <>
      <EverLoader
        indicatorType="spinner"
        className="flex flex-col overflow-auto"
        wrapperClassName="z-20"
        spinning={anyLoaderActive}
      >
        <div className="flex items-center justify-end pb-6">
          <Dropdown
            overlay={termOptionsMenu(termsList, quoteTerms, handleAddTerm)}
            trigger={["click"]}
            onClick={(event) => {
              getTermsData(
                {},
                {
                  onSuccess: () => {
                    event.stopPropagation();
                  },
                }
              );
            }}
          >
            <EverButton
              type="dashed"
              color="info"
              className="w-full !text-ever-info [&>span]:flex [&>span]:items-center [&_svg]:w-4 [&_svg]:h-4"
            >
              <PlusCircleIcon className="w-4 h-4" />
              Add Term
              <ChevronDownIcon className="w-4 h-4" />
            </EverButton>
          </Dropdown>
        </div>
        {!quoteTerms || quoteTerms.length === 0 ? (
          <NoTermsFound />
        ) : (
          <div className="flex flex-col gap-4 h-full py-2 mb-4 overflow-auto w-full">
            <DragDropContext onDragEnd={handleActiveFieldsOnDragEnd}>
              <Droppable droppableId="dropId">
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="flex flex-col gap-4 h-full"
                  >
                    {quoteTerms &&
                      quoteTerms.map((term, index) => (
                        <Draggable
                          key={term.term_id}
                          draggableId={`draggable-${term.term_id}`}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <QuoteCard
                                isLoading={isLoading}
                                key={term.term_id}
                                term={{
                                  ...term,
                                  status: term.default ? "active" : "inactive",
                                }}
                                onRefresh={() =>
                                  handleRefreshTerm(term.term_id)
                                }
                                onUpdate={(updatedTerm) =>
                                  handleUpdateTerm(updatedTerm)
                                }
                                onDelete={() => handleDeleteTerm(term.term_id)}
                                quoteTerms={quoteTerms}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        )}
      </EverLoader>
    </>
  );
};

export default QuoteTermsList;
