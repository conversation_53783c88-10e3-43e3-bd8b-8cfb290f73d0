.terms-library {
  .ck.ck-button.ck-on {
    background-color: #e0f2fe !important;
  }

  .ck-powered-by-balloon {
    opacity: 0;
    pointer-events: none;
  }

  .ck-editor__editable_inline {
    /* min-height: 55vh !important;
  max-height: 55vh !important; */
    height: 55vh;
    min-width: 800px !important;
    line-height: normal !important;
    letter-spacing: normal !important;
  }

  .ck-content {
    padding: 16px !important;
    font-size: 14px !important;
  }

  .custom-item {
    padding: 7px !important;
    border-radius: 0px !important;
    justify-content: space-between !important;
    color: #1f2937 !important;
    font-weight: 500 !important;
  }

  .custom-label {
    font-weight: 600 !important;
    color: #6b7280 !important;
    margin-left: 60px !important;
  }

  .custom-item:hover {
    background-color: #f3f4f6 !important;
  }

  .custom-item.ck-on {
    background-color: #e0f2fe !important;
    color: #1f2937 !important;
    border-radius: 6px !important;
  }

  .custom-heading {
    font-weight: 600 !important;
    font-size: 12px !important;
    color: #1f2937 !important;
    border-radius: 6px 6px 0px 0px !important;
    pointer-events: none !important;
    padding: 8px !important;
    justify-content: space-between !important;
  }

  .custom-badge {
    font-family: "IBM Plex Sans" !important;
    font-size: 12px;
    font-weight: 500 !important;
    padding: 1px 8px !important;
    border-radius: 6px !important;
    background-color: #f3f4f6;
    color: #1f2937;
  }

  .ck-tooltip {
    padding: 8px !important;
    line-height: 1 !important;
  }

  .ck-content .table {
    margin: auto !important;
  }
}
