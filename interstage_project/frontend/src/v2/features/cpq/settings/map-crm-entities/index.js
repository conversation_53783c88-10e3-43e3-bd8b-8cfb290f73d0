import { cloneDeep, isEqual } from "lodash";
import { observer } from "mobx-react";
import { useState, useRef } from "react";
import { useQuery, useMutation } from "react-query";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { EverTg, EverButton, message } from "~/v2/components";

import { CrmSectionMapper } from "./components/CrmSectionMapper";
import { getMapCrmEntities, updateMapCrmEntities } from "./services/restApi";
import { CRM_MAPPING_CONFIG } from "./util";

const MapCrmEntities = () => {
  const { accessToken } = useAuthStore();
  const [scrolled, setScrolled] = useState(false);
  const [crmEntityMappings, setCrmEntityMappings] = useState({
    company: {
      databook_id: "",
      datasheet_id: "",
      account_name: "",
      account_id: "",
    },
    deal: {
      databook_id: "",
      datasheet_id: "",
      deal_name: "",
      deal_id: "",
      deal_account_id: "",
    },
    contact: {
      databook_id: "",
      datasheet_id: "",
      contact_name: "",
      contact_email: "",
      contact_id: "",
      contact_account_id: "",
    },
  });
  const [initialMapCrmData, setInitialMapCrmData] = useState(crmEntityMappings);
  const scrollTimeoutRef = useRef(null);
  const { activeDatabooks, getDatasheets, getDatasheetOrderedColumns } =
    useDatabookStore();
  const handleScroll = () => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    setScrolled(true);

    scrollTimeoutRef.current = setTimeout(() => {
      setScrolled(false);
    }, 2000);
  };
  const isEdited = () => {
    return isEqual(initialMapCrmData, crmEntityMappings);
  };
  const handleDiscard = () => {
    setCrmEntityMappings(initialMapCrmData);
  };
  const { refetch, isLoading, isFetching } = useQuery(
    ["getMapCrmEntities"],
    () => getMapCrmEntities(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        const cloneMappings = cloneDeep(crmEntityMappings);
        cloneMappings.company =
          data?.company_map_source || cloneMappings.company;
        cloneMappings.deal = data?.deal_map_source || cloneMappings.deal;
        cloneMappings.contact =
          data?.contact_map_source || cloneMappings.contact;
        setCrmEntityMappings(cloneMappings);
        setInitialMapCrmData(cloneMappings);
      },
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
    }
  );
  const updateMapCrmEntitiesData = useMutation(
    (payload) => updateMapCrmEntities(accessToken, payload),
    {
      onSuccess: () => {
        message.success("Map CRM Entities updated successfully");
        refetch();
      },
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
        refetch();
      },
    }
  );
  const handleSave = () => {
    const payload = {};
    const warningMessageSectionName = [];
    for (const sectionKey of Object.keys(crmEntityMappings || {})) {
      const current = crmEntityMappings[sectionKey];
      const initial = initialMapCrmData[sectionKey];
      if (!isEqual(current, initial)) {
        const emptyValue = Object.values(current).some((v) => !v);
        if (emptyValue) {
          warningMessageSectionName.push(
            sectionKey.charAt(0).toUpperCase() + sectionKey.slice(1)
          );
        } else {
          payload[`${sectionKey}_map_source`] = current;
        }
      }
    }
    if (warningMessageSectionName.length > 0) {
      message.error(
        `Please map all the fields in ${warningMessageSectionName.join(
          ", "
        )} section.`
      );
    } else if (Object.keys(payload).length > 0) {
      updateMapCrmEntitiesData.mutate(payload);
    }
  };
  return (
    <div
      onScroll={handleScroll}
      className={`flex flex-col items-center h-full overflow-y-auto ${
        isLoading || isFetching ? "pointer-events-none opacity-50" : ""
      }`}
    >
      <div
        className={`flex justify-between w-full px-8 py-4 bg-ever-base-50 border-b border-ever-base-300 rounded-b-lg sticky top-0 z-[1052] transition-all duration-1000 ease-in-out ${
          scrolled
            ? "shadow-[0px_1px_2px_0px_rgba(0,23,128,0.06),0px_1px_3px_0px_rgba(0,23,128,0.10)]"
            : ""
        }
        `}
      >
        <div>
          <EverTg.Heading2 className="block">Map CRM Entities</EverTg.Heading2>
          <EverTg.Description className="block mt-2">
            Configure data sources for each CRM model such as Accounts, Deals
            and Contacts.
          </EverTg.Description>
        </div>
      </div>
      <div className="flex-1 w-2/3 max-w-[800px] my-4">
        {Object.entries(CRM_MAPPING_CONFIG).length > 0 &&
          Object.entries(CRM_MAPPING_CONFIG).map(
            ([sectionKey, sectionBody]) => (
              <CrmSectionMapper
                key={sectionKey}
                sectionKey={sectionKey}
                sectionBody={sectionBody}
                activeDatabooks={activeDatabooks}
                getDatasheets={getDatasheets}
                getDatasheetOrderedColumns={getDatasheetOrderedColumns}
                crmEntityMappings={crmEntityMappings}
                setCrmEntityMappings={setCrmEntityMappings}
              />
            )
          )}
      </div>
      <div
        className={`w-full px-6 py-4 bg-ever-base-50 border-t border-ever-base-300 box-border-t sticky bottom-0 z-[1051] transition-all duration-1000 ease-in-out ${
          scrolled
            ? "shadow-[0px_-1px_2px_0px_rgba(0,23,128,0.06),0px_-1px_3px_0px_rgba(0,23,128,0.10)]"
            : ""
        }
        `}
      >
        <div className="flex flex-row-reverse gap-x-4">
          <EverButton
            color="primary"
            type="filled"
            disabled={
              isFetching || updateMapCrmEntitiesData.isLoading || isEdited()
            }
            loading={updateMapCrmEntitiesData.isLoading || isLoading}
            onClick={handleSave}
          >
            Save changes
          </EverButton>
          <EverButton
            color="base"
            type="filled"
            disabled={
              isFetching || updateMapCrmEntitiesData.isLoading || isEdited()
            }
            onClick={handleDiscard}
          >
            Discard changes
          </EverButton>
        </div>
      </div>
    </div>
  );
};

export default observer(MapCrmEntities);
