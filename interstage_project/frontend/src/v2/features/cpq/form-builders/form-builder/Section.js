import {
  DotsVerticalIcon,
  PlusCircleIcon,
  ArrowCircleRightIcon,
  EditPencilAltIcon,
  AlertCircleIcon,
} from "@everstage/evericons/outlined";
import * as Popover from "@radix-ui/react-popover";
import { Dropdown, Menu } from "antd";
import { get, intersection, startsWith, isEmpty } from "lodash";
import { useState, useRef, useEffect } from "react";
import { Droppable } from "react-beautiful-dnd";
import { twMerge } from "tailwind-merge";

import {
  EverButton,
  EverTg,
  IconButton,
  EverCheckbox,
  EverDivider,
  EverTooltip,
} from "~/v2/components";
import { RemoveConfirmationModal } from "~/v2/features/cpq/components";
import {
  FIELD_TYPE,
  DATA_TYPE,
  FIELD_TYPE_LABELS,
} from "~/v2/features/cpq/constants";
import {
  FIELD_MORE_MENU_ITEMS,
  FIELD_MORE_OPTIONS,
} from "~/v2/features/cpq/form-builders/form-builder/constants";

import Field from "./Field";

function getDataType(selectedFieldType) {
  switch (selectedFieldType) {
    case FIELD_TYPE.CHECKBOX: {
      return DATA_TYPE.BOOLEAN;
    }
    case FIELD_TYPE.MULTI_SELECT: {
      return DATA_TYPE.ARRAY;
    }
    case FIELD_TYPE.DATE: {
      return DATA_TYPE.DATE;
    }
    case FIELD_TYPE.DATE_TIME: {
      return DATA_TYPE.DATE_TIME;
    }
    case DATA_TYPE.NUMBER:
    case DATA_TYPE.CURRENCY: {
      return DATA_TYPE.NUMBER;
    }
    default: {
      return DATA_TYPE.STRING;
    }
  }
}

function getDefaultValue(selectedFieldType) {
  switch (selectedFieldType) {
    case FIELD_TYPE.CHECKBOX: {
      return false;
    }
    case FIELD_TYPE.MULTI_SELECT: {
      return [];
    }
    case FIELD_TYPE.DATE:
    case FIELD_TYPE.DATE_TIME: {
      return null;
    }
    default: {
      return "";
    }
  }
}

const NON_EDITABLE_SECTION_TAGS = new Set(["select_product", "terms_library"]);

export default function Section({
  isNewSection,
  index,
  sectionId,
  sectionOrder,
  section,
  sections,
  updateSections,
  sourceFields,
  defaultFields,
  duplicateSectionIds,
  duplicateFieldIds,
  setChangedFields,
}) {
  const nameRef = useRef(null);

  const [isSectionHovering, setIsSectionHovering] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isEditSectionName, setIsEditSectionName] = useState(false);
  const [nameWidth, setNameWidth] = useState(0);

  const containsDefaultFields =
    intersection(defaultFields, section.field_order).length > 0;

  useEffect(() => {
    if (isNewSection) {
      setIsEditSectionName(true);
    }
  }, [isNewSection]);

  const handleDeleteSection = () => {
    const newSections = { ...sections };
    delete newSections[sectionId];
    const newSectionOrder = sectionOrder.filter((id) => id !== sectionId);
    updateSections(newSections, newSectionOrder);
  };

  const handleMoreOptionsClick = (type) => {
    if (type === FIELD_MORE_OPTIONS.DELETE) {
      if (containsDefaultFields) {
        return;
      }
      setShowDeleteConfirmation(true);
    }
  };

  const handleSectionOrderChange = (direction) => {
    const newSectionOrder = [...sectionOrder];

    if (direction === "up") {
      if (index === 0) {
        return;
      }
      [newSectionOrder[index - 1], newSectionOrder[index]] = [
        newSectionOrder[index],
        newSectionOrder[index - 1],
      ];
    } else if (direction === "down") {
      if (index === sectionOrder.length - 1) {
        return;
      }
      [newSectionOrder[index], newSectionOrder[index + 1]] = [
        newSectionOrder[index + 1],
        newSectionOrder[index],
      ];
    }

    updateSections(sections, newSectionOrder);
  };

  const handleSectionNameChange = (e) => {
    if (e.target.value.length <= 50) {
      updateSections({
        ...sections,
        [sectionId]: {
          ...section,
          label: e.target.value,
        },
      });
      setTimeout(() => {
        setNameWidth(nameRef.current?.offsetWidth ?? 0);
      }, 10);
    }
  };

  const handleSectionNameBlur = () => {
    if (section.label.length > 0) {
      setIsEditSectionName(false);
    }
  };

  const moreOptionsMenu = (
    <Menu
      className="flex items-start !p-2"
      onClick={(event) => {
        event.domEvent.stopPropagation();
      }}
    >
      {FIELD_MORE_MENU_ITEMS.map((item, key) => (
        <Menu.Item
          className="!px-0 w-full hover:!bg-transparent !h-9"
          key={key}
        >
          <EverTooltip
            title={
              containsDefaultFields
                ? "This section contains default fields. You can't delete this section"
                : ""
            }
            placement="top"
            overlayClassName="!text-sm"
          >
            <EverButton
              type="text"
              color={item.type === FIELD_MORE_OPTIONS.DELETE ? "error" : "base"}
              size="small"
              className={twMerge(
                "w-full justify-start",
                containsDefaultFields && "!opacity-50 cursor-not-allowed"
              )}
              prependIcon={item.icon}
              onClick={() => handleMoreOptionsClick(item.type)}
            >
              <EverTg.Caption
                className={twMerge(
                  "font-normal",
                  item.type === FIELD_MORE_OPTIONS.DELETE
                    ? "text-ever-error-lite-content"
                    : ""
                )}
              >
                {item.label}
              </EverTg.Caption>
            </EverButton>
          </EverTooltip>
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div className="relative">
      <div
        className={twMerge(
          "mx-20 py-3 px-4 border border-solid border-transparent",
          isSectionHovering &&
            "rounded-lg border-ever-base-200 bg-ever-base-50 shadow-md"
        )}
        onMouseEnter={() => setIsSectionHovering(true)}
        onMouseLeave={() => setIsSectionHovering(false)}
      >
        <Droppable
          key={sectionId}
          droppableId={sectionId}
          isDropDisabled={NON_EDITABLE_SECTION_TAGS.has(section.tag)}
        >
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="section relative"
            >
              <div
                className={twMerge(
                  "flex items-center justify-between",
                  NON_EDITABLE_SECTION_TAGS.has(section.tag) ? "mb-0" : "mb-4"
                )}
              >
                {isEditSectionName && (
                  <input
                    autoFocus
                    className={twMerge(
                      "min-w-20 h-6 font-bold border-0 border-b border-ever-primary bg-transparent outline-0 transition-all text-sm",
                      section.label.length === 0 ? "border-ever-error" : ""
                    )}
                    style={{
                      width: nameWidth - 8 + "px",
                    }}
                    value={section.label}
                    onChange={handleSectionNameChange}
                    onBlur={handleSectionNameBlur}
                    onKeyUp={(e) => {
                      if (e.key === "Enter") {
                        handleSectionNameBlur();
                      }
                    }}
                  />
                )}
                <div
                  ref={nameRef}
                  className={twMerge(
                    "flex items-center gap-1.5 min-w-24",
                    isEditSectionName ? "absolute invisible" : ""
                  )}
                >
                  <div
                    className="flex items-center gap-1 cursor-pointer"
                    onClick={() => {
                      setIsEditSectionName(true);
                      setNameWidth(nameRef.current?.offsetWidth ?? 0);
                    }}
                  >
                    <EverTg.Text className="font-bold">
                      {section.label}
                    </EverTg.Text>
                    {duplicateSectionIds.includes(sectionId) && (
                      <EverTooltip title="Section name already exists">
                        <AlertCircleIcon className="w-4 h-4 text-ever-error" />
                      </EverTooltip>
                    )}
                    <EditPencilAltIcon className="w-4 h-4 shrink-0 text-ever-base-content-low" />
                  </div>
                </div>
                <Dropdown
                  overlay={moreOptionsMenu}
                  trigger={["click"]}
                  type="filled"
                  color="base"
                  onClick={(event) => event.stopPropagation()}
                >
                  <IconButton
                    type="text"
                    className="!w-8 !h-8 m-0 rounded-md bg-ever-base-50"
                    icon={
                      <DotsVerticalIcon className="shrink-0	p-0.5 text-ever-base-content-mid" />
                    }
                  />
                </Dropdown>
              </div>
              <div className="flex">
                <div className="absolute bottom-0 w-1 h-[calc(100%-48px)] rounded-md bg-ever-primary-ring/80" />
                <div className="w-full ml-4">
                  {section.field_order.map((fieldId, index) => {
                    if (NON_EDITABLE_SECTION_TAGS.has(section.tag)) {
                      return <></>;
                    }
                    return (
                      <Field
                        key={fieldId}
                        section={section}
                        fieldId={fieldId}
                        index={index}
                        sourceFields={sourceFields}
                        defaultFields={defaultFields}
                        duplicateFieldIds={duplicateFieldIds}
                        updateFields={(fields, fieldOrder) => {
                          updateSections({
                            ...sections,
                            [sectionId]: {
                              ...section,
                              fields: fields ?? section.fields,
                              field_order: fieldOrder ?? section.field_order,
                            },
                          });
                        }}
                        setChangedFields={setChangedFields}
                      />
                    );
                  })}
                </div>
              </div>
              {provided.placeholder}
            </div>
          )}
        </Droppable>
        {NON_EDITABLE_SECTION_TAGS.has(section.tag) ? (
          <></>
        ) : (
          <AddFieldPopover
            sectionId={sectionId}
            section={section}
            sections={sections}
            updateSections={updateSections}
          />
        )}
      </div>
      <div
        className={twMerge(
          "absolute top-3 right-1 pl-5 opacity-0",
          isSectionHovering && "opacity-100"
        )}
        onMouseMove={() => setIsSectionHovering(true)}
        onMouseEnter={() => setIsSectionHovering(true)}
        onMouseLeave={() => setIsSectionHovering(false)}
      >
        <div className="flex items-center gap-1 bg-ever-info rounded-md p-0.5 w-fit">
          <EverTooltip
            title="Move section up"
            placement="top"
            overlayClassName="!text-sm"
          >
            <IconButton
              type="text"
              color="base"
              className="-rotate-90 hover:!bg-transparent !w-7 !h-7"
              icon={<ArrowCircleRightIcon className="text-ever-base" />}
              size="small"
              onClick={() => handleSectionOrderChange("up")}
              disabled={index === 0}
            />
          </EverTooltip>
          <EverTooltip
            title="Move section down"
            placement="top"
            overlayClassName="!text-sm"
          >
            <IconButton
              type="text"
              color="base"
              className="rotate-90 hover:!bg-transparent !w-7 !h-7"
              icon={<ArrowCircleRightIcon className="text-ever-base" />}
              size="small"
              onClick={() => handleSectionOrderChange("down")}
              disabled={index === sectionOrder.length - 1}
            />
          </EverTooltip>
        </div>
      </div>
      <RemoveConfirmationModal
        title={`Remove ${section.label} section?`}
        subtitle="This action cannot be undone"
        visible={showDeleteConfirmation}
        onCancel={() => setShowDeleteConfirmation(false)}
        onConfirm={() => handleDeleteSection()}
      />
    </div>
  );
}

const AddFieldPopover = ({ sectionId, section, sections, updateSections }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFieldTypes, setSelectedFieldTypes] = useState([]);

  const handleClosePopover = () => {
    setSelectedFieldTypes([]);
    setIsOpen(false);
  };

  const handleAddField = () => {
    if (selectedFieldTypes.length > 0) {
      const fields = {};
      const fieldOrder = [];
      const existingFieldOrder = Object.values(sections).flatMap((section) =>
        get(section, "field_order", [])
      );
      const highestFieldNumber =
        existingFieldOrder
          .map((field) => {
            const fieldNumber = field?.split("field")?.[1] ?? 0;
            return Number(fieldNumber);
          })
          .sort((a, b) => b - a)[0] ?? 0;

      const untitledLabels = [];
      Object.values(section.fields).forEach((field) => {
        if (
          startsWith(field.label, "Untitled Field") &&
          !isEmpty(field.label.split("Untitled Field ")[1]) &&
          !Number.isNaN(Number(field.label.split("Untitled Field ")[1]))
        ) {
          untitledLabels.push(Number(field.label.split("Untitled Field ")[1]));
        }
      });
      const highestUntitledNumber =
        untitledLabels.sort((a, b) => b - a)[0] ?? 0;

      for (let [index, selectedFieldType] of selectedFieldTypes.entries()) {
        const field = {
          id: `field${highestFieldNumber + index + 1}`,
          label: `Untitled Field ${highestUntitledNumber + index + 1}`,
          label_placement:
            FIELD_TYPE.CHECKBOX === selectedFieldType ? "right" : "top",
          placeholder: "",
          help_text: "",
          field_type: selectedFieldType,
          data_type: getDataType(selectedFieldType),
          tag: "",
          tag_type: "",
          value: getDefaultValue(selectedFieldType),
          options_lookup_spec: {},
          properties: {
            is_mandatory: false,
            is_read_only: false,
            is_hidden: false,
          },
          ...([FIELD_TYPE.MULTI_SELECT, FIELD_TYPE.SINGLE_SELECT].includes(
            selectedFieldType
          ) && {
            options: [],
          }),
        };

        fields[field.id] = field;
        fieldOrder.push(field.id);
      }

      updateSections({
        ...sections,
        [sectionId]: {
          ...section,
          fields: { ...section.fields, ...fields },
          field_order: [...section.field_order, ...fieldOrder],
        },
      });

      handleClosePopover();
    }
  };

  const content = (
    <div className="flex flex-col h-full">
      <div className="flex flex-col gap-1.5 h-60 overflow-y-auto">
        {Object.entries(FIELD_TYPE_LABELS).map(([fieldType, label]) => {
          return (
            <div
              key={fieldType}
              className="flex items-center gap-2 px-3 py-1.5"
            >
              <EverCheckbox
                label={label}
                checked={selectedFieldTypes.includes(fieldType)}
                onChange={() =>
                  setSelectedFieldTypes((prev) =>
                    prev.includes(fieldType)
                      ? prev.filter((type) => type !== fieldType)
                      : [...prev, fieldType]
                  )
                }
              />
            </div>
          );
        })}
      </div>
      <EverDivider className="my-1.5" />
      <div className="flex items-center justify-end pt-2 gap-2">
        <EverButton
          className="!h-8 !px-2"
          type="ghost"
          color="base"
          onClick={handleClosePopover}
        >
          Discard
        </EverButton>
        <EverButton
          className="!h-8 !px-2"
          color="primary"
          onClick={handleAddField}
        >
          Add fields
        </EverButton>
      </div>
    </div>
  );

  return (
    <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <EverButton
          type="dashed"
          color="info"
          size="small"
          className="w-full !h-8 mt-2 !text-xs !text-ever-info [&>span]:flex [&>span]:items-center [&_svg]:w-4 [&_svg]:h-4"
        >
          <PlusCircleIcon className="w-4 h-4" />
          Add new field
        </EverButton>
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          align="center"
          className="w-72 p-2 pb-3 shadow-xl my-1.5 border border-solid border-ever-base-400 rounded-lg bg-ever-base cursor-pointer"
        >
          {content}
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};
