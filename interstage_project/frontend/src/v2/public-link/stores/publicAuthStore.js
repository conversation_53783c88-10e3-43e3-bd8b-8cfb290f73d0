import { jwtDecode } from "jwt-decode";
import { makeAutoObservable } from "mobx";
import { useLocalStore } from "mobx-react";
import { useContext, createContext } from "react";

class PublicAuthStore {
  publicAccessToken = "";

  constructor(publicAccessToken) {
    makeAutoObservable(this);
    this.publicAccessToken = publicAccessToken;
  }

  setPublicAccessToken(token) {
    this.publicAccessToken = token;
  }

  get decodedToken() {
    try {
      // Check if token exists and is not empty
      if (!this.publicAccessToken || this.publicAccessToken.trim() === "") {
        return null;
      }

      return jwtDecode(this.publicAccessToken);
    } catch (error) {
      console.error("Failed to decode JWT token:", error);
      return null;
    }
  }

  get publicToken() {
    return this.publicAccessToken;
  }
}

const PublicAuthStoreContext = createContext(null);

export function PublicAuthStoreProvider({ publicAccessToken, children }) {
  const store = useLocalStore(() => new PublicAuthStore(publicAccessToken));
  return (
    <PublicAuthStoreContext.Provider value={store}>
      {children}
    </PublicAuthStoreContext.Provider>
  );
}

export function usePublicAuthStore() {
  const store = useContext(PublicAuthStoreContext);
  if (!store)
    throw new Error(
      "usePublicAuthStore must be used within PublicAuthStoreProvider"
    );
  return store;
}
