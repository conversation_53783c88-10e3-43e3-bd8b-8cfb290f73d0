# Public Link Frontend Usage

This document explains the structure, flow, and usage of the Public Link feature in the Everstage frontend (`src/v2/public-link`).

---

## Overview

The Public Link feature allows external users to securely access shared resources (such as quotes) via a unique URL. The frontend provides a seamless, branded experience for:

- Validating recipient access (via email)
- Displaying the shared resource (e.g., quote PDF)
- Handling invalid or expired links

---

## Main Components

### 1. `PublicApp`

- **Entry point** for all public link routes.
- Handles token parsing, client info fetching, and validation state.
- Renders either the landing page, the quote view, or an invalid token page.

### 2. `LandingPage`

- Shown when a user first visits a public link.
- Prompts for email validation.
- Displays client branding (logo, name) fetched from the backend.
- On successful validation, transitions to the resource view.

### 3. `QuoteView`

- Displays the shared quote PDF using the `PDFGenerator` component.
- Handles loading and error states.

### 4. `InvalidTokenPage`

- Shown if the link is invalid or expired.
- Provides a clear error message and branding.

### 5. `publicAuthStore`

- MobX store for managing the public access token and state.
- Used by all components to access the current token.

### 6. `restApi.js`

- Contains API functions for:
  - Fetching client info
  - Validating email access
  - Fetching the public PDF data
- All requests include the public access token in the Authorization header.

---

## Authentication & Validation Flow

1. **Routing:**

   - In `AuthValidator.js`, if the URL contains `/public`, the app renders `PublicApp` inside `PublicAuthStoreProvider`.

2. **Token Handling:**

   - `PublicApp` parses the JWT from the URL and stores it in the MobX store.
   - Fetches client info for branding.

3. **Email Validation:**

   - `LandingPage` prompts the user for their email.
   - Calls `validatePublicLinkAccess` API.
   - On success, sets `isValidated` and renders `QuoteView`.

4. **Resource Display:**

   - `QuoteView` fetches the PDF data using the token.
   - Handles loading and error states.

5. **Error Handling:**
   - If the token is invalid/expired, or validation fails, `InvalidTokenPage` is shown.

---

## Example Integration

**Routing in `AuthValidator.js`:**

```js
if (window.location.pathname.includes("/public")) {
  const pathParts = window.location.pathname.split("/");
  const publicAccessToken = pathParts.pop() || "";
  return (
    <PublicAuthStoreProvider publicAccessToken={publicAccessToken}>
      <PublicApp />
    </PublicAuthStoreProvider>
  );
}
```

---

## Extending the Feature

- To support new entity types (e.g., dealroom):
  - Update backend to support new entity.
  - Add new view component (e.g., `DealroomView.js`).
  - Update `PublicApp` to route based on `payload.entity`.

---

## Styling & Branding

- Uses Everstage design system components (`EverTg`, `EverButton`, etc.).
- Client logo and name are fetched and displayed on the landing page.
- Error and loading states are styled for clarity and consistency.

---

## Best Practices & Notes

- **Always use the MobX store** for accessing the public token.
- **All API calls must include the token** in the Authorization header.
- **Do not expose sensitive information** in error messages.
- **Keep the UI minimal and focused** for external users.
- **Handle all error and edge cases** (invalid token, expired link, network errors).

---

## Example: Adding a New Entity Type

1. Add backend support for the new entity.
2. Create a new view component (e.g., `DealroomView.js`).
3. Update `PublicApp`:

```js
if (entity === "quote") {
  // ...
} else if (entity === "dealroom") {
  return <DealroomView />;
} else {
  return <InvalidTokenPage />;
}
```

---

For questions or improvements, check the backend `public_link.md` for data model details.
