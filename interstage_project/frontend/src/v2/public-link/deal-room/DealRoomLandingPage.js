import { useState } from "react";
import { useQuery as useReactQuery } from "react-query";

import { EverLoader } from "~/v2/components";
import { WelcomePageComponent } from "~/v2/features/cpq/deal-room/components/builder-components";

import { getDealRoomWelcomePage, validatePublicLinkAccess } from "../restApi";
import { usePublicAuthStore } from "../stores";

const isValidEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(String(email).toLowerCase());
};

const DealRoomLandingPage = ({ onValidationSuccess, clientInfo }) => {
  const publicAuthStore = usePublicAuthStore();

  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const { publicToken } = publicAuthStore;

  const {
    data: dealRoomWelcomePage,
    isLoading: isDealRoomWelcomePageLoading,
    error: dealRoomWelcomePageError,
  } = useReactQuery(
    ["getDealRoomWelcomePage", publicToken],
    () => getDealRoomWelcomePage(publicToken),
    {
      enabled: !!publicToken,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  if (isDealRoomWelcomePageLoading) {
    return <EverLoader />;
  }

  if (dealRoomWelcomePageError || !dealRoomWelcomePage) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <div className="text-center p-8">
          <h2 className="text-2xl font-semibold text-ever-base-content mb-2">
            Unable to Load Deal Room
          </h2>
          <p className="text-ever-base-content-mid">
            The deal room you are trying to access may be unavailable or the
            link has expired.
          </p>
        </div>
      </div>
    );
  }

  const handleProceed = async () => {
    if (!email) {
      setError("Please enter your email address.");
      return;
    }
    if (!isValidEmail(email)) {
      setError("Please enter a valid email address.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      const data = await validatePublicLinkAccess(publicToken, email);
      if (data?.isValid) {
        onValidationSuccess();
      } else {
        setError(
          data?.message || "Invalid email or access denied. Please try again."
        );
      }
    } catch (error) {
      setError(error?.message || "Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <WelcomePageComponent
      data={dealRoomWelcomePage}
      clientInfo={clientInfo}
      inputProps={{
        value: email,
        disabled: isLoading,
        error: error ? { message: error } : null,
        onChange: (e) => setEmail(e.target.value),
      }}
      buttonProps={{
        loading: isLoading,
        disabled: !email || isLoading || !isValidEmail(email),
        onClick: handleProceed,
      }}
    />
  );
};

export default DealRoomLandingPage;
