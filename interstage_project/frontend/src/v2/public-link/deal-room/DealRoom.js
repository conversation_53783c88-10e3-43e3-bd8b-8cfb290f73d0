import { get } from "lodash";
import { useState } from "react";
import { useQuery as useReactQuery } from "react-query";

import { EverLoader } from "~/v2/components";
import DealRoomPreview from "~/v2/features/cpq/deal-room/deal-room-preview";
import { fetchBrandData } from "~/v2/features/cpq/utils";

import { getDealRoomData } from "../restApi";
import { usePublicAuthStore } from "../stores";

const getCompanies = async ({ searchTerm }) => {
  try {
    const { response, data } = await fetchBrandData(searchTerm);

    if (!response.ok) {
      return []; // Return empty array on error
    }

    return data;
  } catch (error) {
    console.error("Error loading company data:", error);
    return []; // Return empty array on error
  }
};

const DealRoom = ({ clientInfo }) => {
  const { publicToken } = usePublicAuthStore();

  const [buyersLogoUrl, setBuyersLogoUrl] = useState("");

  const setBuyersLogo = async (data) => {
    const customerLogo = get(data, "customer_logo", {});
    const { type, url } = customerLogo;
    if (url) {
      if (type === "brand_fetch") {
        try {
          const companies = await getCompanies({ searchTerm: url });
          if (Array.isArray(companies) && companies.length > 0) {
            setBuyersLogoUrl(companies[0]?.icon);
          }
        } catch (error) {
          console.error("Error loading company data:", error);
        }
      } else {
        setBuyersLogoUrl(url);
      }
    }
  };

  const {
    data: dealRoomData,
    isLoading: isDealRoomDataLoading,
    error: dealRoomDataError,
  } = useReactQuery(
    ["getDealRoomData", publicToken],
    () => getDealRoomData(publicToken),
    {
      enabled: !!publicToken,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setBuyersLogo(data);
      },
    }
  );

  if (isDealRoomDataLoading) {
    return <EverLoader />;
  }

  if (dealRoomDataError || !dealRoomData) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <div className="text-center p-8">
          <h2 className="text-2xl font-semibold text-ever-base-content mb-2">
            Unable to Load Deal Room
          </h2>
          <p className="text-ever-base-content-mid">
            The deal room you are trying to access may be unavailable or the
            link has expired.
          </p>
        </div>
      </div>
    );
  }

  return (
    <DealRoomPreview
      data={dealRoomData}
      clientInfo={clientInfo}
      buyersLogoUrl={buyersLogoUrl}
    />
  );
};

export default DealRoom;
