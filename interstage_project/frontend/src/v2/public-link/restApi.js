import { isArray, isObject } from "lodash";

const getErrorMessage = (errorMessage) => {
  if (isArray(errorMessage)) {
    return errorMessage.join(", ");
  } else if (isObject(errorMessage)) {
    return Object.values(errorMessage).flat().join(", ");
  } else {
    return errorMessage;
  }
};

const fetchAndParseResponse = async (url, options) => {
  const response = await fetch(url, options);
  const data = await response.json();
  if (!response.ok) {
    if (data?.message) {
      console.error(data.message);
    }
    throw new Error(getErrorMessage(data?.message || ""));
  }
  return data;
};

export const getPublicPdf = (publicAccessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${publicAccessToken}`,
    },
  };

  return fetchAndParseResponse(`/public/get_public_pdf`, requestOptions);
};

export const validatePublicLinkAccess = (publicAccessToken, email) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${publicAccessToken}`,
    },
    body: JSON.stringify({ email }),
  };

  return fetchAndParseResponse(`/public/validate`, requestOptions);
};

export const getBasicClientInfo = (publicAccessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${publicAccessToken}`,
    },
  };

  return fetchAndParseResponse(`/public/get_basic_client_info`, requestOptions);
};

export const getDealRoomWelcomePage = (publicAccessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${publicAccessToken}`,
    },
  };

  return fetchAndParseResponse(`/public/dealroom_welcome_page`, requestOptions);
};

export const getDealRoomData = (publicAccessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${publicAccessToken}`,
    },
  };

  return fetchAndParseResponse(`/public/get_dealroom`, requestOptions);
};
