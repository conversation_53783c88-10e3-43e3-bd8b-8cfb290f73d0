import { Image } from "antd";
import { useState } from "react";

import { EverInput, EverButton, EverTg } from "~/v2/components";
import { logoPlaceholder } from "~/v2/images";

import { validatePublicLinkAccess } from "../restApi";
import { usePublicAuthStore } from "../stores";

const isValidEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(String(email).toLowerCase());
};

const QuoteLandingPage = ({ onValidationSuccess, clientInfo }) => {
  const publicAuthStore = usePublicAuthStore();
  const { publicToken } = publicAuthStore;
  const { logoUrl, name } = clientInfo;
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleProceed = async () => {
    if (!email) {
      setError("Please enter your email address.");
      return;
    }
    if (!isValidEmail(email)) {
      setError("Please enter a valid email address.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      const data = await validatePublicLinkAccess(publicToken, email);
      if (data?.isValid) {
        onValidationSuccess();
      } else {
        setError(
          data?.message || "Invalid email or access denied. Please try again."
        );
      }
    } catch (error) {
      setError(error?.message || "Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white h-full">
      <div className="relative bg-ever-base-50 h-full">
        <div className="flex flex-col gap-12 items-center justify-center h-full py-20 px-44">
          {logoUrl ? (
            <Image
              src={logoUrl}
              alt={name}
              className="w-[100px] h-[100px] rounded-2xl object-contain"
              preview={false}
            />
          ) : (
            <img
              src={logoPlaceholder}
              alt="Logo Placeholder"
              className="w-[100px] h-[100px] rounded-2xl object-contain"
            />
          )}
          <EverTg.Heading2 className="text-center text-[32px]">
            {name} shared a quote with you
          </EverTg.Heading2>
          <div className="flex flex-col items-center gap-10">
            <div className="flex flex-col items-center gap-3">
              <EverTg.Caption className="text-center text-2xl">
                Enter your email to proceed
              </EverTg.Caption>
              <div className="flex flex-col items-center gap-1.5 w-96">
                <EverInput
                  placeholder="Enter your email"
                  className="w-full !h-12 !text-lg"
                  size="large"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
                {error && (
                  <EverTg.Text className="text-ever-error self-start">
                    {error}
                  </EverTg.Text>
                )}
              </div>
            </div>
            <EverButton
              className="w-40 h-12 text-lg"
              size="large"
              onClick={handleProceed}
              loading={isLoading}
              disabled={!email || isLoading || !isValidEmail(email)}
            >
              Proceed
            </EverButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuoteLandingPage;
