import PDFGenerator from "@everstage/ev-pdf-lib/preview";
import { useQuery as useReactQuery } from "react-query";

import { EverLoader } from "~/v2/components";

import { getPublicPdf } from "../restApi";
import { usePublicAuthStore } from "../stores";

const QuoteView = () => {
  const publicAuthStore = usePublicAuthStore();
  const { publicToken } = publicAuthStore;

  const {
    data: pdfData,
    isLoading: isPdfDataLoading,
    error: pdfDataError,
  } = useReactQuery(
    ["getPublicPdf", publicToken],
    () => getPublicPdf(publicToken),
    {
      enabled: !!publicToken,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  if (isPdfDataLoading) {
    return (
      <EverLoader.Skeleton
        config={[1, 2, 1, 2, 1, 2, 2, 1, 2, 1, 2, 1, 2, 2]}
        className="h-full px-9"
        fixedSize
      />
    );
  }

  if (pdfDataError || !pdfData?.pdf_data) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <div className="text-center p-8">
          <h2 className="text-2xl font-semibold text-ever-base-content mb-2">
            Unable to Load Quote
          </h2>
          <p className="text-ever-base-content-mid">
            The document you are trying to access may be unavailable or the link
            has expired.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-ever-base-50">
      <div className="flex flex-col items-center py-10 bg-ever-base-50">
        <div
          className={
            "shadow-md bg-white w-1/2 overflow-auto h-full border border-solid border-ever-base-300 rounded-xl" +
            (pdfData.pdf_data?.customTemplate ? "" : " py-12")
          }
        >
          <PDFGenerator {...pdfData.pdf_data} previewMode={true} />
        </div>
      </div>
    </div>
  );
};

export default QuoteView;
