import { useState, useEffect } from "react";
import { QueryClient, QueryClientProvider } from "react-query";

import { DealRoomLandingPage, DealRoom } from "~/v2/public-link/deal-room";
import InvalidTokenPage from "~/v2/public-link/InvalidTokenPage";
import { QuoteLandingPage, QuoteView } from "~/v2/public-link/quotes";
import "~/v2/globalImports";

import { getBasicClientInfo } from "./restApi";
import { usePublicAuthStore } from "./stores";

function PublicApp() {
  const { decodedToken, publicAccessToken } = usePublicAuthStore();

  const [isValidated, setIsValidated] = useState(false);
  const [clientInfo, setClientInfo] = useState({
    name: "",
    logoUrl: "",
    metaInfo: {},
  });
  const [invalidToken, setInvalidToken] = useState(false);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        if (!publicAccessToken) {
          return;
        }
        const info = await getBasicClientInfo(publicAccessToken);
        setClientInfo({
          name: info.name,
          logoUrl: info.logoUrl,
          metaInfo: info.metaInfo,
        });
      } catch (error) {
        console.error("Failed to fetch client info:", error);
        setInvalidToken(true);
      }
    };

    fetchClientInfo();
  }, [publicAccessToken]);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  });

  if (invalidToken) {
    return <InvalidTokenPage />;
  }

  const handleValidationSuccess = () => {
    setIsValidated(true);
  };

  return (
    <QueryClientProvider client={queryClient}>
      {decodedToken?.entity === "quote" ? (
        isValidated ? (
          <QuoteView />
        ) : (
          <QuoteLandingPage
            onValidationSuccess={handleValidationSuccess}
            clientInfo={clientInfo}
          />
        )
      ) : decodedToken?.entity === "dealroom" ? (
        isValidated ? (
          <DealRoom clientInfo={clientInfo} />
        ) : (
          <DealRoomLandingPage
            onValidationSuccess={handleValidationSuccess}
            clientInfo={clientInfo}
          />
        )
      ) : (
        <InvalidTokenPage />
      )}
    </QueryClientProvider>
  );
}

export default PublicApp;
