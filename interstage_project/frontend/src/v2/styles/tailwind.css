@tailwind base;
@tailwind components;
@tailwind utilities;

/**
* The following css provies the font-family throughout the application.
*/
@layer base {
  html {
    color: theme("colors.ever.base.content.DEFAULT");
  }

  html,
  body,
  *:not(.ag-icon):not(.ag-checkbox-input-wrapper) {
    font-family: "IBM Plex Sans"; /** Forcing this because there maybe other conflicting fonts by other libraries */
    letter-spacing: theme("letterSpacing.normal");
  }

  [class*="font-dealroom-heading"] {
    word-spacing: -0.18em;
  }
}

.animate-infinite {
  animation-iteration-count: infinite !important;
}
.animate-once {
  animation-iteration-count: 1 !important;
}
.animate-twice {
  animation-iteration-count: 2 !important;
}
.animate-thrice {
  animation-iteration-count: 3 !important;
}

.custom-scroll {
  overflow-y: auto;
}

.custom-scroll::-webkit-scrollbar {
  @apply h-1 w-1;
}

.custom-scroll::-webkit-scrollbar-thumb {
  @apply bg-ever-chartColors-19;
}

.custom-scroll::-webkit-scrollbar-track {
  @apply bg-ever-base-400 border border-solid border-ever-base;
}
