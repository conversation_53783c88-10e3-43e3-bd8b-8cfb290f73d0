import { gql, useQuery } from "@apollo/client";
import React, { useEffect } from "react";
import { useSetRecoilState } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { segmentIdentify } from "~/Utils/SegmentUtils";
import { EverLoader } from "~/v2/components";

import { CpqRouter } from "./CpqRouter";
import PushNotification from "./PushNotification";
import { Router } from "./Router";

const GET_MY_CLIENT = gql`
  query MyClientAndBaseCurrencySymbol {
    myClient {
      clientId
      name
      domain
      logoUrl
      authConnectionName
      timeZone
      connectionType
      fiscalStartMonthZero
      baseCurrency
      clientFeatures {
        hideCategories
        freezeDate
        showCommissionPercent
        showCommissionBuddy
        showTerritoryPlan
        showSupersetDashboard
        syncColumnsToSuperset
        datasheetV2
        showPayoutTableBreakdown
        showReturnV1Button
        showSalesforceIntegration
        showStatementsV2
        crystalVersion
        databookSyncStrategy
        showApprovalFeature
        showRoles
        showDatasheetPermission
        showCustomObjectPermission
        isNewFrozenPayrollEtl
        showSimulationV2
        enableConcurrentSessions
        enableSupportUserAccess
        enableTsarWebappCustomRoles
        chromeExtensionEnabled
        enableEverai
        allowAdjustmentsToFrozenCommission
        salesforceEnv
        showChatgpt
        showAdvancedFilter
        showStatementsPdf
        exposeCommReportsInPlan
        profilePicturePermission
        commissionPlanVersion
        customCalendar
        crmHyperlinks
        expressionboxVersion
        databookExpressionboxVersion
        payoutSnapshotEtl
        settlementV2
        editLockedQuota
        runSettlementReport
        enableHrisIntegration
        splitSummationToLi
        isCommissionAdjustmentV2Enabled
        quotaEffectiveDated
        allowQuotaSettingsOverride
        showMetrics
        avoidIframeInContracts
        takeDsSnapshot
        insertMetaDataToVecDb
        cpqRagLoadingEnabled
        showForecast
        objectKnowledgeDateQueryStrategy
        globalSearch
        showGetUserPropertyCommission
        enableCustomWorkflows
        revalidateDatasheetOnUpdate
        enableRoundingInTierFunctions
        showDataSourcesV2
        fivetranSync
        enableCustomTheme
        enableSidebarV3
        enableMultiLanguageSupport
        showG2ReviewForm
        modules
        allowAnnualQuotaEffectiveDated
        allowCsvUploadBulkPaymentRegister
        warnOnUnlock
        notificationV2
        enablePlanExclusion
        allowOnlyAdminsToModifyUserName
        runSyncForMultiplePeriod
        isAutoEnrichReport
        asyncExportDatasheet
        showDatasheetV2
        uploadExcelFilesInCustomObject
        datasheetBuilder
        avoidConcurrentRegisterPayment
        canAvoidLockingWithPendingChanges
        enableContractPermissions
        showObservableNotification
        crystalCalcFieldsOverrideLogicV2
        arrearsAsTransaction
        approvalWorkflowSearch
        enableQuotaDeletionByType
        isShowDanglingAdjustmentsEnabled
        fxRateMode
        isDsSkipValidationEnabled
      }
      metaInfo {
        updatedAt
      }
    }
    baseCurrencySymbol
  }
`;

function removeStylesheetWithString(searchString) {
  const styleSheets = document.querySelectorAll(
    'style, link[rel="stylesheet"]'
  );

  Array.from(styleSheets).forEach((sheet) => {
    if (sheet.tagName.toLowerCase() === "style") {
      if (sheet.textContent.includes(searchString)) {
        sheet.parentNode.removeChild(sheet);
      }
    } else if (sheet.tagName.toLowerCase() === "link") {
      if (sheet.href.includes(searchString)) {
        sheet.parentNode.removeChild(sheet);
      }
    }
  });
}

export default function App() {
  const setMyClient = useSetRecoilState(myClientAtom);

  const authStore = useAuthStore();
  const { email } = authStore;

  const employeeStore = useEmployeeStore();
  const { setUserEmail } = employeeStore;

  const { data, loading } = useQuery(GET_MY_CLIENT, {
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (email) {
      // identifying login user emailid in segment analytics. This is also used in full story.
      segmentIdentify(email, {
        email: email,
      });
      setUserEmail(email);
    }
  }, [email]);

  useEffect(() => {
    if (data?.myClient) {
      setMyClient({
        ...data.myClient,
        baseCurrencySymbol: data.baseCurrencySymbol,
      });
    }
  }, [data]);

  useEffect(() => {
    //A hotfix to avoid Demandbase extension from interfering with Ag-grid.
    const scripts = document.getElementsByTagName("script");

    for (let script of scripts) {
      if (script.src.includes("demandbase.com")) {
        removeStylesheetWithString("* Generic Styles");
        break;
      }
    }

    window.addEventListener("storage", () => {
      _setCustomThemeToCss();
    });

    _setCustomThemeToCss();
  }, []);

  const determineCurrentModule = () => {
    const modules = data?.myClient?.clientFeatures?.modules ?? [];
    const storedModule = window.localStorage.getItem("module");
    const isCPQPath = window.location.pathname.startsWith("/cpq");

    // First check if we're on a CPQ path and CPQ is available
    if (isCPQPath && modules.includes("CPQ")) {
      return "CPQ";
    }

    // Then check if we're on a non-CPQ path and ICM is available
    if (!isCPQPath && modules.includes("ICM")) {
      return "ICM";
    }

    // If no path-based module is applicable, use stored module or default to first available
    if (storedModule && modules.includes(storedModule)) {
      return storedModule;
    }

    return modules[0];
  };

  const getModuleBasedRouter = () => {
    const currentModule = determineCurrentModule();
    window.localStorage.setItem("module", currentModule);

    switch (currentModule) {
      case "ICM": {
        return <Router />;
      }
      case "CPQ": {
        return <CpqRouter />;
      }
      default: {
        // TODO: Need to remove this once we set modules for all client
        return <Router />;
      }
    }
  };

  if (loading) {
    return (
      <div className="flex w-full justify-center">
        <EverLoader tip="Loading..." indicatorType="logo" spinning />
      </div>
    );
  }

  return (
    <>
      <PushNotification />
      {getModuleBasedRouter()}
    </>
  );
}

function _setCustomThemeToCss() {
  if (window.localStorage.getItem("customTheme")) {
    const customTheme = JSON.parse(window.localStorage.getItem("customTheme"));
    Object.entries(customTheme).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--colors-ever-${key}`, value);
    });
  }
}
