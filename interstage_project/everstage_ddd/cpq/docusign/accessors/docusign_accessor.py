from commission_engine.models.client_models import ClientSettings
from spm.models.docusign_models import Docusign

from ..docusign_enums import DocusignModuleEnum
from ..models.docusign_models import DocusignAccountSettings, QuoteTemplateDetails


class DocusignCpqAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def create(self, record):
        record.pk = None
        record.save()

    def client_aware(self):
        return Docusign.objects.filter(
            client=self.client_id,
        )

    def client_kd_aware(self, module=DocusignModuleEnum.CPQ.value):
        """
        By default This function return all the active CPQ client
        """
        return self.client_aware().filter(
            is_deleted=False,
            knowledge_end_date__isnull=True,
            additional_details__module=module,
        )

    def get_oauth_details(self):
        return self.client_kd_aware().first()

    def invalidate(self, account_id, knowledge_date):
        query_set = self.client_kd_aware().filter(account_id=account_id)
        if query_set.exists():
            query_set.update(knowledge_end_date=knowledge_date)

    def delete_client_account(self, prev_record, knowledge_date):
        if prev_record:
            self.client_kd_aware().update(knowledge_end_date=knowledge_date)
            prev_record.knowledge_begin_date = knowledge_date
            prev_record.is_deleted = True
            self.create(prev_record)

    def get_docusign_data_by_account(self, account_id):
        query_set = self.client_kd_aware().filter(account_id=account_id)
        return query_set.first() if query_set else None


class DocusignQuoteTemplateDetailsAccessor:
    def __init__(self, client_id=None):
        self.client_id = client_id

    def client_aware(self):
        return QuoteTemplateDetails.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def update_quote_template(self, quote_id, sharing_type, update_dict):
        return (
            self.client_kd_aware()
            .filter(quote_id=quote_id, sharing_type=sharing_type)
            .update(**update_dict)
        )

    def get_quote_template_details_by_id(self, quote_id, sharing_type=None):
        """
        Currently, only one quote with a specific sharing type can be active at a time.
        If the sharing type is None, the active quote details will be returned.
        """
        if sharing_type is None:
            return self.client_kd_aware().filter(quote_id=quote_id).first()

        return (
            self.client_kd_aware()
            .filter(quote_id=quote_id, sharing_type=sharing_type)
            .first()
        )

    def persist_docusign_quote_template_fields(self, data):
        data.save()

    def get_quote_template_by_account_and_envelope(
        self, account_id, envelope_id, sharing_type
    ):
        return QuoteTemplateDetails.objects.filter(
            is_deleted=False,
            knowledge_end_date__isnull=True,
            sharing_type=sharing_type,
            account_id=account_id,
            envelope_id=envelope_id,
        ).first()

    def get_all_active_quote_ids_with_envelope(self, account_id, sharing_type):
        """
        Fetch all active quote_ids for the given client, account_id, sharing_type='docusign',
        where envelope_id is not null.
        """
        queryset = self.client_kd_aware().filter(
            sharing_type=sharing_type,
            account_id=account_id,
            envelope_id__isnull=False,  # Only rows where envelope_id is not null
        )
        return queryset.values_list("quote_id", flat=True)


class DocusignAccountSettingsAccessor:
    def __init__(self, client_id=None):
        self.client_id = client_id

    def client_aware(self):
        return DocusignAccountSettings.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def get_docusign_account_settings_data(self):
        return self.client_kd_aware().first()

    def update_account_settings_data(self, update_dict):
        return self.client_kd_aware().update(**update_dict)

    def persist_docusign_account_settings_data(self, data):
        data.save()

    def get_secret_key_by_account_id(self, account_id):
        existing_row = DocusignAccountSettings.objects.filter(
            is_deleted=False,
            knowledge_end_date__isnull=True,
            account_data__has_key=account_id,
        ).first()

        if existing_row:
            return existing_row.account_data.get(account_id)
        return None

    def update_cpq_basic_settings_data(self, cpq_settings_data):
        ClientSettings.objects.update_or_create(
            client_id=self.client_id, defaults={"cpq_settings": cpq_settings_data}
        )
