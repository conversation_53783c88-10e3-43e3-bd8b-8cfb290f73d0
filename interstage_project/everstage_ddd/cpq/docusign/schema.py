from typing import Dict, List, Optional

from ninja import Schema


class CreateTemplatePayload(Schema):
    email_id: str
    template_name: str
    recipients: str
    template_notification_settings: Optional[str] = None
    quote_id: str
    email_subject: str
    email_body: str


class UpdateTemplatePayload(CreateTemplatePayload):
    template_id: str


class SendEnvelopePayload(Schema):
    email_id: str
    template_id: str
    quote_id: str


class TemplateEditViewPayload(Schema):
    email_id: str
    template_id: str


class QuoteTemplateDetailsPayload(Schema):
    email_id: str
    quote_id: str
    type: Optional[str] = None


class SaveQuoteTemplateDataPayload(Schema):
    quote_id: str
    type: str
    reason: Optional[str] = None
    recipients: Optional[str] = None
    email_body: Optional[str] = None
    email_subject: Optional[str] = None


class OauthCallbackPayload(Schema):
    code: str
    login_email_id: str


class ValidateDisconnectPayload(Schema):
    email_id: str


class OauthDisconnectPayload(Schema):
    email_id: str


class VoidEnvelopePayload(Schema):
    envelope_id: str
    void_reason: str
    email_id: str
    quote_id: str


class SaveDocusignAccountSettingsPayload(Schema):
    secret_key: Optional[str] = None
    recipients: Optional[List] = None
    allow_offline_signing: Optional[bool] = None
    mandate_offline_signing: Optional[bool] = None


class DeleteQuoteDataPayload(Schema):
    quote_id: str
    type: str


class SaveQuoteTemplateDataResponse(Schema):
    status: str
    quote_id: str
    template_recipients: List = []


class SendEnvelopeResponse(Schema):
    status: str
    envelope_id: str
    template_id: str
    quote_id: str
    envelope_status: str


class QuoteTemplateDetailsResponse(Schema):
    status: str
    template_id: Optional[str] = None
    template_name: Optional[str] = None
    template_recipients: Optional[List] = None
    is_signing_order_enabled: Optional[bool] = None
    template_files: Optional[List] = None
    template_notification_settings: Optional[Dict] = None
    quote_id: str
    email_body: Optional[str] = None
    email_subject: Optional[str] = None
    envelope_id: Optional[str] = None
    files_with_urls: Optional[List] = None
    sharing_reason: Optional[str] = None
    quote_status: Optional[str] = None
    sharing_type: Optional[str] = None
    days_left: Optional[str] = None
    is_locked: Optional[bool] = None


class ValidateDisconnectResponse(Schema):
    status: str
    message: Optional[str] = None


class OauthCallbackResponse(Schema):
    status: str


class LogoutUrlResponse(Schema):
    url: str


class ConnectionStatusResponse(Schema):
    has_integration: bool
    email: Optional[str] = None
    has_secret: bool


class VoidEnvelopeResponse(Schema):
    status: str


class SaveDocusignAccountSettingsResponse(Schema):
    status: str
    message: str


class GetDocusignAccountSettingsResponse(ConnectionStatusResponse):
    status: str
    default_signer: Optional[List] = None
    allow_offline_signing: bool = False
    mandate_offline_signing: bool = False


class DeleteQuoteDataResponse(Schema):
    status: str
    quote_id: str


class CreateUpdateTemplateResponse(Schema):
    status: str
    template_id: str
    template_name: str
    quote_id: str
