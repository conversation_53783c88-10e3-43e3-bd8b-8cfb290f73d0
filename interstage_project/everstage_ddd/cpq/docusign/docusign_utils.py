import os
import re
import uuid

from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
    QuoteStatusSelector,
)

from .accessors.docusign_accessor import DocusignCpqAccessor
from .docusign_exception import DocusignCpqError
from .services.docusign_api_service import DocusignCpqApiService


def clean_filename(filename):
    """Remove brackets, spaces, and other problematic characters"""
    if filename:
        filename = filename.replace(" ", "_")
        filename = filename.replace("[", "")
        filename = filename.replace("]", "")
        filename = filename.replace("(", "")
        filename = filename.replace(")", "")
        filename = filename.replace("+", "_")
        filename = filename.replace("&", "_")
        filename = filename.replace("%", "_")
        filename = re.sub(r"_+", "_", filename)
    if not filename:
        filename = str(uuid.uuid4().hex)
    return filename


def get_status_of_quote(client_id, quote_id):
    quote_status_response = QuoteStatusSelector(client_id).get_quote_status(
        quote_id, projection=["status"]
    )
    return (quote_status_response or {}).get("status")


def get_docusign_auth_details(client_id, email_id):
    oauth_data = DocusignCpqAccessor(client_id).get_oauth_details()
    if oauth_data is None:
        message = "Oauth data not present for the user"
        code = "DOCUSIGN_CPQ_OAUTH"
        raise DocusignCpqError(code, message)
    account_id = oauth_data.account_id
    docusign_service = DocusignCpqApiService(client_id, email_id, account_id)
    api_client = docusign_service.get_api_client()
    return account_id, api_client


CPQ_ENVIRONMENT = {
    "PRODUCTION": "PRODUCTION",
    "PRODUCTION-EU": "PRODUCTION-EU",
}


def fetch_cpq_site_url():
    if os.environ.get("ENV") == CPQ_ENVIRONMENT["PRODUCTION"]:
        return "https://app.everstage.com/cpq"
    elif os.environ.get("ENV") == CPQ_ENVIRONMENT["PRODUCTION-EU"]:
        return "https://app.eu.everstage.com/cpq"
    else:
        return os.environ.get("URL")
