import base64
import logging
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import requests
from django.core.cache import cache
from django.utils import timezone
from docusign_esign.apis import EnvelopesApi, Templates<PERSON>pi
from docusign_esign.models import (
    CarbonCopy,
    CertifiedDelivery,
    Document,
    EnvelopeDefinition,
    EnvelopeTemplate,
    Recipients,
    Signer,
    SignHere,
    Tabs,
)

from commission_engine.accessors.client_accessor import get_client_settings
from commission_engine.utils.cloudfront_utils import invalidate_cloudfront_cache
from commission_engine.utils.s3_utils import S3Uploader
from everstage_ddd.cpq.quote.quote_status.quote_status_service import (
    change_quote_status,
    retract_quote_to_approved,
)

from ..accessors.docusign_accessor import (
    DocusignAccountSettingsAccessor,
    DocusignCpqAccessor,
    DocusignQuoteTemplateDetailsAccessor,
)
from ..docusign_enums import CpqQuoteStatusEnum, DocusignEnum, SharingTypeEnum
from ..docusign_exception import (
    DocusignApiError,
    DocusignCpqError,
    DocusignNoDataFoundError,
)
from ..docusign_serializers import (
    DocusignAccountSettingsSerializer,
    DocusignQuoteTemplateDetailsSerializer,
)
from ..docusign_utils import (
    clean_filename,
    get_docusign_auth_details,
    get_status_of_quote,
)
from .docusign_api_service import DocusignCpqApiService


def get_quote_template_documents(template):
    template_details = {}
    template_files = []

    documents = template.documents
    if documents is not None:
        for document in documents:
            template_files.extend(
                [
                    {
                        "name": document.name,
                        "contents": document.document_base64,
                    }
                ]
            )
    template_details["documents"] = template_files

    return template_details


def get_docusign_template_lock_details(account_id, template_id, templates_api):
    try:
        lock_info = templates_api.get_lock_with_http_info(account_id, template_id)
        logging.info("DOCUSIGN API LIMIT: get_lock %s ", lock_info[2])
        return {"is_locked": True}
    except Exception as e:
        logging.info("DOCUSIGN API LIMIT: get_lock %s", e)
        return {"is_locked": False}


def get_quote_template_data(client_id, quote_id, sharing_type=None):
    """
    Get quote template data.
    Currently, only one quote with a specific sharing type can be active at a time.
    If the sharing type is None, the active quote details will be returned.
    """
    quote_template_details = DocusignQuoteTemplateDetailsAccessor(
        client_id
    ).get_quote_template_details_by_id(quote_id, sharing_type)

    template_meta_data = (
        quote_template_details.template_meta_data
        if quote_template_details and quote_template_details.template_meta_data
        else {}
    )
    quote_template_data = {"template_meta_data": template_meta_data}
    if quote_template_details:
        quote_template_data["quote_template_details"] = {
            "template_id": quote_template_details.template_id,
            "template_name": quote_template_details.template_name,
            "envelope_id": quote_template_details.envelope_id,
            "sharing_type": quote_template_details.sharing_type,
        }

    return quote_template_data


def add_sign_tag_to_the_recipients(recipients):
    for recipient in recipients:
        signing_preference = str(recipient["signingPreference"])
        if signing_preference == DocusignEnum.NEEDS_TO_SIGN.value:
            if "signatureUrl" not in recipient:
                recipient["signHere"] = f"sn{recipient['id']}"
        else:
            recipient.pop("signHere", None)


def add_signature_url_to_the_recipients(recipients, s3_path):
    cloudfront_cdn = os.environ.get("S3_AVATAR_CDN")
    bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    for recipient in recipients or []:
        if "base64Signature" in recipient:
            binary_file = base64.b64decode(recipient["base64Signature"])
            name = clean_filename(recipient["name"])
            final_s3_path = f"{s3_path}/{recipient['id']}/{name}_signature.png"
            S3Uploader(bucket_name).upload_file(binary_file, final_s3_path)
            recipient["signatureUrl"] = f"{cloudfront_cdn}/{final_s3_path}"
            recipient.pop("base64Signature", None)
            invalidate_cloudfront_cache(cloudfront_cdn, "/" + final_s3_path)


def get_offline_files_urls(template_files, s3_path):
    index = 1
    cloudfront_cdn = os.environ.get("S3_AVATAR_CDN")
    bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    files_with_s3_urls = []
    if template_files:
        for template_file_key in template_files:
            file = template_files[template_file_key]
            logging.info("Document ready to upload to S3")
            document_name = clean_filename(file.name)
            final_s3_path = f"{s3_path}/{index}/{document_name}"
            S3Uploader(bucket_name).upload_file(file, final_s3_path)
            logging.info("Document uploaded to S3")
            document_url = f"{cloudfront_cdn}/{final_s3_path}"
            files_with_s3_urls.append(
                {
                    "documentName": document_name,
                    "documentUrl": document_url,
                }
            )
            invalidate_cloudfront_cache(cloudfront_cdn, "/" + final_s3_path)
            index = index + 1
    return files_with_s3_urls


def update_quote_template_notification_settings(
    templates_api, account_id, template_id, template_notification_settings
):
    update_template_notification_settings = (
        templates_api.update_notification_settings_with_http_info(
            account_id,
            template_id,
            template_notification_request=template_notification_settings,
        )
    )
    logging.info(
        "DOCUSIGN API LIMIT: update_template_notification_settings %s",
        update_template_notification_settings[2],
    )


def prepare_quote_template_documents(template_files):
    documents = []
    index = 1
    if template_files:
        for template_file_key in template_files:
            document_id = index
            file_name = template_files[template_file_key].name
            file_extension = file_name.split(".")[-1]
            base64_file_content = base64.b64encode(
                template_files[template_file_key].read()
            ).decode("ascii")
            document = Document(
                document_base64=base64_file_content,
                file_extension=file_extension,
                name=template_files[template_file_key].name,
                document_id=document_id,
            )
            index = index + 1
            documents.append(document)
    return documents


def build_envelope_template_from_roles(
    roles, documents, template_name, email_subject, email_body
):
    signers = []
    carbon_copies = []
    certified_deliveries = []
    signing_order = set()

    for role in roles:
        signing_preference = str(role["signingPreference"])

        if signing_preference == DocusignEnum.NEEDS_TO_SIGN.value:
            signer = Signer(
                recipient_id=role["id"],
                routing_order=role["routingOrder"],
                email=role["email"],
                name=role["name"],
                role_name=f'{role["role"]}::{role["title"]}',
            )
            if "signHere" in role:
                sign_here = SignHere(
                    anchor_string=role["signHere"],
                    anchor_units="pixels",
                    anchor_x_offset="10",
                    anchor_y_offset="20",
                )
                signer.tabs = Tabs(sign_here_tabs=[sign_here])
            signers.append(signer)
        elif signing_preference == DocusignEnum.RECEIVES_A_COPY.value:
            cc = CarbonCopy(
                recipient_id=role["id"],
                routing_order=role["routingOrder"],
                email=role["email"],
                name=role["name"],
                role_name=f'{role["role"]}::{role["title"]}',
            )
            carbon_copies.append(cc)
        elif signing_preference == DocusignEnum.NEEDS_TO_VIEW.value:
            certified_delivery = CertifiedDelivery(
                recipient_id=role["id"],
                routing_order=role["routingOrder"],
                email=role["email"],
                name=role["name"],
                role_name=f'{role["role"]}::{role["title"]}',
            )
            certified_deliveries.append(certified_delivery)

        signing_order.add(role["routingOrder"])

    recipients = Recipients()
    if signers:
        recipients.__setattr__("signers", signers)
    if carbon_copies:
        recipients.__setattr__("carbon_copies", carbon_copies)
    if certified_deliveries:
        recipients.__setattr__("certified_deliveries", certified_deliveries)

    template_req_object = EnvelopeTemplate(
        documents=documents,
        recipients=recipients,
        name=template_name,
        email_subject=email_subject,
        email_blurb=email_body,
        recipients_lock=False,
        brand_lock=False,
        message_lock=False,
    )

    return template_req_object, len(signing_order) > 1


def create_template(client_id, email_id, roles, template_files, **kwargs):
    """
    Creates a Docusign template and also update the docusign_quote_template_details table
    """
    template_name = kwargs.get("template_name")
    template_notification_settings = kwargs.get("template_notification_settings")
    quote_id = kwargs.get("quote_id")
    quote_status = get_status_of_quote(client_id, quote_id)
    if quote_status != CpqQuoteStatusEnum.APPROVED.value:
        code = "CREATE_DOCUSIGN_QUOTE_TEMPLATE_ERROR"
        message = "To create the quote template details quote status must be in approved state"
        raise DocusignCpqError(
            code,
            message,
        )
    email_body = kwargs.get("email_body")
    email_subject = kwargs.get("email_subject")
    account_id, api_client = get_docusign_auth_details(client_id, email_id)
    templates_api = TemplatesApi(api_client)
    documents = prepare_quote_template_documents(template_files)
    template_req_object, signing_order = build_envelope_template_from_roles(
        roles, documents, template_name, email_subject, email_body
    )
    template_with_http_info = templates_api.create_template_with_http_info(
        account_id=account_id, envelope_template=template_req_object
    )
    res = template_with_http_info[0]
    logging.info("DOCUSIGN API LIMIT: create_template %s", template_with_http_info[2])
    template_id = res.template_id

    template_meta_data = get_quote_template_data(
        client_id, quote_id, SharingTypeEnum.DOCUSIGN.value
    )["template_meta_data"]
    template_meta_data["recipients"] = sorted(
        roles, key=lambda role: (int(role["routingOrder"]))
    )
    template_meta_data["signing_order"] = signing_order
    template_meta_data["email_body"] = email_body
    template_meta_data["email_subject"] = email_subject

    if template_notification_settings:
        update_quote_template_notification_settings(
            templates_api, account_id, template_id, template_notification_settings
        )
        template_meta_data["template_notification_settings"] = (
            template_notification_settings
        )
    quote_updated_template_details = {
        "template_id": template_id,
        "account_id": account_id,
        "template_name": template_name,
        "template_meta_data": template_meta_data,
    }

    DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
        quote_id, SharingTypeEnum.DOCUSIGN.value, quote_updated_template_details
    )

    return {
        "status": "SUCCESS",
        "template_id": template_id,
        "template_name": template_name,
        "quote_id": quote_id,
    }


def delete_non_existing_recipients(
    existing_recipients, templates_api, account_id, template_id, updated_roles_ids
):
    """
    delete the recipients from the template which does not part of the current recipient list.
    """
    if existing_recipients.signers is not None:
        for signer in existing_recipients.signers:
            if signer.recipient_id not in updated_roles_ids:
                result_with_http_info = templates_api.delete_recipient_with_http_info(
                    account_id=account_id,
                    template_id=template_id,
                    recipient_id=signer.recipient_id,
                )

                logging.info(
                    "DOCUSIGN API LIMIT: delete_recipient %s", result_with_http_info[2]
                )
    if existing_recipients.carbon_copies is not None:
        for cc in existing_recipients.carbon_copies:
            if cc.recipient_id not in updated_roles_ids:
                result_with_http_info = templates_api.delete_recipient_with_http_info(
                    account_id=account_id,
                    template_id=template_id,
                    recipient_id=cc.recipient_id,
                )
                logging.info(
                    "DOCUSIGN API LIMIT: delete_recipient %s", result_with_http_info[2]
                )
    if existing_recipients.certified_deliveries is not None:
        for certified_delivery in existing_recipients.certified_deliveries:
            if certified_delivery.recipient_id not in updated_roles_ids:
                result_with_http_info = templates_api.delete_recipient_with_http_info(
                    account_id=account_id,
                    template_id=template_id,
                    recipient_id=certified_delivery.recipient_id,
                )
                logging.info(
                    "DOCUSIGN API LIMIT: delete_recipient %s", result_with_http_info[2]
                )


def delete_non_existing_documents(
    existing_documents, templates_api, account_id, template_id, template_files
):
    """
    Delete the document from the template which does not exist in current template files
    """
    if existing_documents.template_documents is not None:
        existing_documents_map = {}
        document_ids_to_delete = []
        # creating map of document name to document_ids
        for document in existing_documents.template_documents:
            if document.name not in existing_documents_map:
                existing_documents_map[document.name] = []
            existing_documents_map[document.name].append(document.document_id)

        # creating list of document_ids to delete by comparing existing documents to updated documents
        all_template_name = [file.name for _, file in template_files.items()]
        for existing_file_key, document_ids in existing_documents_map.items():
            if existing_file_key not in all_template_name:
                document_ids_to_delete.extend(document_ids)
        if document_ids_to_delete:
            result_with_http_info = templates_api.delete_documents_with_http_info(
                account_id=account_id,
                template_id=template_id,
                envelope_definition=EnvelopeDefinition(
                    documents=[
                        Document(document_id=document_id)
                        for document_id in document_ids_to_delete
                    ]
                ),
            )
            logging.info(
                "DOCUSIGN API LIMIT: delete_documents %s", result_with_http_info[2]
            )


def update_template(client_id, email_id, roles, template_files, **kwargs):
    """
    Update a Docusign template for the given template id and also update the docusign_quote_template_details table
    """
    template_id = kwargs.get("template_id")
    template_name = kwargs.get("template_name")
    template_notification_settings = kwargs.get("template_notification_settings")
    quote_id = kwargs.get("quote_id")
    quote_status = get_status_of_quote(client_id, quote_id)
    if quote_status != CpqQuoteStatusEnum.APPROVED.value:
        code = "UPDATE_DOCUSIGN_QUOTE_TEMPLATE_ERROR"
        message = "To update the quote template details quote status must be in approved state"
        raise DocusignCpqError(
            code,
            message,
        )
    email_body = kwargs.get("email_body")
    email_subject = kwargs.get("email_subject")
    account_id, api_client = get_docusign_auth_details(client_id, email_id)
    templates_api = TemplatesApi(api_client)
    updated_roles_ids = [role["id"] for role in roles]
    existing_recipients_with_http_info = templates_api.list_recipients_with_http_info(
        account_id=account_id, template_id=template_id
    )
    logging.info(
        "DOCUSIGN API LIMIT: list_recipients %s", existing_recipients_with_http_info[2]
    )
    # docusign api to get template documents
    template_documents_with_http_info = templates_api.list_documents_with_http_info(
        account_id=account_id, template_id=template_id
    )
    logging.info(
        "DOCUSIGN API LIMIT: list_documents %s", template_documents_with_http_info[2]
    )
    existing_recipients = existing_recipients_with_http_info[0]
    existing_documents = template_documents_with_http_info[0]
    delete_non_existing_recipients(
        existing_recipients, templates_api, account_id, template_id, updated_roles_ids
    )
    delete_non_existing_documents(
        existing_documents, templates_api, account_id, template_id, template_files
    )

    documents = prepare_quote_template_documents(template_files)
    template_req_object, signing_order = build_envelope_template_from_roles(
        roles, documents, template_name, email_subject, email_body
    )

    if documents:
        update_doc_result_with_http_info = (
            templates_api.update_documents_with_http_info(
                account_id,
                template_id,
                envelope_definition=EnvelopeDefinition(documents=documents),
            )
        )
        logging.info(
            "DOCUSIGN API LIMIT: update_documents %s",
            update_doc_result_with_http_info[2],
        )

    update_template_with_http_info = templates_api.update_with_http_info(
        account_id=account_id,
        template_id=template_id,
        envelope_template=template_req_object,
    )
    logging.info(
        "DOCUSIGN API LIMIT: update_template %s", update_template_with_http_info[2]
    )
    template_meta_data = get_quote_template_data(
        client_id, quote_id, SharingTypeEnum.DOCUSIGN.value
    )["template_meta_data"]
    template_meta_data["recipients"] = sorted(
        roles, key=lambda role: (int(role["routingOrder"]))
    )
    template_meta_data["signing_order"] = signing_order
    template_meta_data["email_body"] = email_body
    template_meta_data["email_subject"] = email_subject

    if template_notification_settings:
        update_quote_template_notification_settings(
            templates_api, account_id, template_id, template_notification_settings
        )
        template_meta_data["template_notification_settings"] = (
            template_notification_settings
        )
    quote_updated_template_details = {
        "template_id": template_id,
        "account_id": account_id,
        "template_name": template_name,
        "template_meta_data": template_meta_data,
    }

    DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
        quote_id, SharingTypeEnum.DOCUSIGN.value, quote_updated_template_details
    )
    return {
        "status": "SUCCESS",
        "template_id": template_id,
        "template_name": template_name,
        "quote_id": quote_id,
    }


def create_edit_view(client_id, email_id, template_id):
    oauth_data = DocusignCpqAccessor(client_id).get_oauth_details()
    if oauth_data is None:
        message = "oauth data not present for the user"
        code = "DOCUSIGN_CPQ_OAUTH_DATA_NOT_FOUND"
        raise DocusignNoDataFoundError(code, message)
    account_id = oauth_data.account_id
    docusign_service = DocusignCpqApiService(client_id, email_id, account_id)
    cache_key = f"docusign_token##{email_id}##{account_id}"

    if cache.get(cache_key) is None:
        response = docusign_service.get_access_token_from_refresh_token(
            oauth_data.refresh_token
        )
        access_token = response.get("access_token")
        timeout = response.get("expires_in", 3600)
        cache.set(cache_key, access_token, timeout - 120)
    else:
        access_token = cache.get(cache_key)
    url = f"{oauth_data.base_uri}/restapi/v2.1/accounts/{account_id}/templates/{template_id}/views/edit"
    headers = {
        "Authorization": "Bearer {0}".format(access_token),
        "Content-Type": "application/json",
        "Content-Length": "0",
    }
    response = requests.post(url, json={}, headers=headers, timeout=20)
    if response:
        response = response.json()
        url = response.get("url", "")
        url = url.replace("templateDetails", "prepareTemplateFields")
        return url
    else:
        logging.error(response.json())
        message = "Error in getting template edit view url"
        raise DocusignApiError(message)


def save_docusign_quote_template_data(client_id, quote_id, recipients, **kwargs):
    """
    save the docusign template data to the DB
    """
    sharing_type = kwargs.get("sharing_type")
    email_body = kwargs.get("email_body")
    email_subject = kwargs.get("email_subject")
    code = "DOCUSIGN_CPQ_QUOTE_TEMPLATE"
    signing_order_set = {recipient["routingOrder"] for recipient in recipients}
    signing_order = len(signing_order_set) > 1
    quote_template_data = get_quote_template_data(client_id, quote_id, sharing_type)
    template_meta_data = quote_template_data["template_meta_data"]
    template_meta_data["recipients"] = recipients
    template_meta_data["signing_order"] = signing_order
    template_meta_data["email_subject"] = email_subject
    template_meta_data["email_body"] = email_body
    if "quote_template_details" in quote_template_data:
        DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
            quote_id,
            sharing_type,
            {"template_meta_data": template_meta_data},
        )
    else:
        quote_data = {
            "client": client_id,
            "knowledge_begin_date": timezone.now(),
            "sharing_type": sharing_type,
            "quote_id": quote_id,
            "template_meta_data": template_meta_data,
        }
        docusign_template_details_ser = DocusignQuoteTemplateDetailsSerializer(
            data=quote_data
        )
        if docusign_template_details_ser.is_valid():
            DocusignQuoteTemplateDetailsAccessor(
                client_id
            ).persist_docusign_quote_template_fields(docusign_template_details_ser)
        else:
            message = "Error while persisting docusign quote template details"
            raise DocusignCpqError(code, message)


def save_offline_quote_template_data(client_id, quote_id, template_files, **kwargs):
    """
    save offline quote template data to the db
    change quote status to Won when file uploaded
    change quote status to Pending Signature when this function call first time for that particular quote
    """
    sharing_type = kwargs.get("sharing_type")
    sharing_reason = kwargs.get("sharing_reason")
    code = "OFFLINE_QUOTE_TEMPLATE"
    quote_template_data = get_quote_template_data(client_id, quote_id, sharing_type)
    template_meta_data = quote_template_data["template_meta_data"]
    if "quote_template_details" in quote_template_data:
        s3_path = f"quote_offline_documents/{client_id}/{quote_id}"
        files_with_s3_urls = get_offline_files_urls(template_files, s3_path)
        change_quote_status(client_id, quote_id, CpqQuoteStatusEnum.WON.value)
        template_meta_data["files_with_s3_urls"] = files_with_s3_urls
        DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
            quote_id,
            sharing_type,
            {"template_meta_data": template_meta_data},
        )
    else:
        change_quote_status(
            client_id, quote_id, CpqQuoteStatusEnum.PENDING_SIGNATURE.value
        )
        template_meta_data["sharing_reason"] = sharing_reason
        quote_data = {
            "client": client_id,
            "knowledge_begin_date": timezone.now(),
            "quote_id": quote_id,
            "sharing_type": sharing_type,
            "template_meta_data": template_meta_data,
        }
        docusign_template_details_ser = DocusignQuoteTemplateDetailsSerializer(
            data=quote_data
        )
        if docusign_template_details_ser.is_valid():
            DocusignQuoteTemplateDetailsAccessor(
                client_id
            ).persist_docusign_quote_template_fields(docusign_template_details_ser)
        else:
            message = "Error while persisting offline quote template details"
            raise DocusignCpqError(code, message)


def save_quote_template_details(
    client_id, quote_id, recipients, template_files, **kwargs
):
    """
    save the quote template details for both docusign and offline sharing type
    if save quote template details for docusign then all active data of offline sharing_type get invalidate
    if save quote template details for offline then all active data of docusign sharing_type get invalidate
    """
    sharing_type = kwargs.get("sharing_type")
    email_body = kwargs.get("email_body")
    email_subject = kwargs.get("email_subject")
    quote_status = get_status_of_quote(client_id, quote_id)

    if sharing_type == SharingTypeEnum.DOCUSIGN.value:
        code = "DOCUSIGN_CPQ_QUOTE_TEMPLATE"
        if quote_status != CpqQuoteStatusEnum.APPROVED.value:
            message = "To save the quote template details quote status must be in approved state"
            raise DocusignCpqError(
                code,
                message,
            )
        if not recipients or not email_body or not email_subject:
            message = "Any one field is missing in the payload among recipients, emailBody and emailSubject"
            raise DocusignCpqError(code, message)
        recipients = sorted(
            recipients, key=lambda recipient: (int(recipient["routingOrder"]))
        )
        add_sign_tag_to_the_recipients(recipients)
        save_docusign_quote_template_data(client_id, quote_id, recipients, **kwargs)
        invalidate_quote_data(client_id, quote_id, SharingTypeEnum.OFFLINE.value)
    elif sharing_type == SharingTypeEnum.OFFLINE.value:
        save_offline_quote_template_data(client_id, quote_id, template_files, **kwargs)
        invalidate_quote_data(client_id, quote_id, SharingTypeEnum.DOCUSIGN.value)
    else:
        code = "INVALID_SHARING_TYPE"
        message = "Invalid sharing_type"
        raise DocusignCpqError(code, message)
    return {
        "status": "SUCCESS",
        "quote_id": quote_id,
        "template_recipients": recipients or [],
    }


def get_days_until_expiration(sent_datetime_utc_str, expiration_days):
    """
    Returns after how many days from the current day envelope will get expired
    """
    # Safely strip fractional seconds before the 'Z'
    if sent_datetime_utc_str.endswith("Z") and "." in sent_datetime_utc_str:
        sent_datetime_utc_str = sent_datetime_utc_str.split(".")[0] + "Z"
    sent_datetime_utc = timezone.datetime.strptime(
        sent_datetime_utc_str, "%Y-%m-%dT%H:%M:%SZ"
    ).replace(tzinfo=timezone.utc)
    expiration_datetime = sent_datetime_utc + timedelta(days=expiration_days)
    now_utc = timezone.now()
    time_remaining = expiration_datetime - now_utc
    if time_remaining.total_seconds() <= 0:
        return 0
    return (
        (time_remaining.days + 1) if time_remaining.seconds > 0 else time_remaining.days
    )


def get_quote_template_details_by_quote_id(client_id, email_id, quote_id, sharing_type):
    """
    return the quote template details for the given quote id
    """
    quote_template_data = get_quote_template_data(client_id, quote_id, sharing_type)
    quote_status = get_status_of_quote(client_id, quote_id)
    if "quote_template_details" in quote_template_data:
        template_meta_data = quote_template_data["template_meta_data"]
        quote_template_details = quote_template_data["quote_template_details"]
        template_details = {}
        days_left = None
        if quote_template_details.get("template_id") and not quote_template_details.get(
            "envelope_id"
        ):
            account_id, api_client = get_docusign_auth_details(client_id, email_id)
            templates_api = TemplatesApi(api_client)
            template_id = quote_template_details["template_id"]
            logging.info("Getting docusign template documents")
            template_documents_with_http_info = templates_api.get_with_http_info(
                account_id,
                template_id,
                include="documents,tabs",
            )
            template = template_documents_with_http_info[0]
            template_details = get_quote_template_documents(template)
            lock_details = get_docusign_template_lock_details(
                account_id, template_id, templates_api
            )
            template_details["is_locked"] = lock_details["is_locked"]
        if (
            quote_template_details.get("envelope_id")
            and quote_status != CpqQuoteStatusEnum.WON.value
        ):
            envelope_sent_date_time = template_meta_data.get("envelope_sent_date_time")
            expiration_after_data = (
                template_meta_data.get("template_notification_settings", {})
                .get("expirations", {})
                .get("expireAfter")
            )
            expiration_days = (
                expiration_after_data if expiration_after_data is not None else 120
            )
            days_left = str(
                get_days_until_expiration(envelope_sent_date_time, expiration_days)
                if envelope_sent_date_time is not None
                else expiration_days
            )

        return {
            "status": "SUCCESS",
            "template_id": quote_template_details.get("template_id"),
            "template_name": quote_template_details.get("template_name"),
            "template_recipients": template_meta_data.get("recipients"),
            "is_signing_order_enabled": template_meta_data.get("signing_order"),
            "template_files": template_details.get("documents", None),
            "template_notification_settings": template_meta_data.get(
                "template_notification_settings"
            ),
            "email_subject": template_meta_data.get("email_subject"),
            "email_body": template_meta_data.get("email_body"),
            "quote_id": quote_id,
            "envelope_id": quote_template_details.get("envelope_id"),
            "files_with_urls": template_meta_data.get("files_with_s3_urls"),
            "sharing_reason": template_meta_data.get("sharing_reason"),
            "quote_status": quote_status,
            "sharing_type": quote_template_details.get("sharing_type"),
            "days_left": days_left,
            "is_locked": template_details.get("is_locked", None),
        }
    return {
        "status": "DATA_NOT_FOUND",
        "quote_id": quote_id,
        "quote_status": quote_status,
    }


def get_updated_recipients(envelope_recipients):
    """
    return the updated recipient after creating the envelope(recipient id got changed after creating the envelope)
    also return the signing
    """
    recipients = []
    signing_order = set()
    if envelope_recipients.signers is not None:
        for signer in envelope_recipients.signers:
            role, title = signer.role_name.split("::", 1)
            recipient = {
                "signingPreference": DocusignEnum.NEEDS_TO_SIGN.value,
                "routingOrder": signer.routing_order,
                "id": signer.recipient_id,
                "email": signer.email,
                "name": signer.name,
                "role": role,
                "title": title,
            }
            signing_order.add(recipient["routingOrder"])
            recipients.append(recipient)

    if envelope_recipients.carbon_copies is not None:
        for cc in envelope_recipients.carbon_copies:
            role, title = cc.role_name.split("::", 1)
            recipient = {
                "signingPreference": DocusignEnum.RECEIVES_A_COPY.value,
                "routingOrder": cc.routing_order,
                "id": cc.recipient_id,
                "email": cc.email,
                "name": cc.name,
                "role": role,
                "title": title,
            }
            signing_order.add(recipient["routingOrder"])
            recipients.append(recipient)

    if envelope_recipients.certified_deliveries is not None:
        for certified_delivery in envelope_recipients.certified_deliveries:
            role, title = certified_delivery.role_name.split("::", 1)
            recipient = {
                "signingPreference": DocusignEnum.NEEDS_TO_VIEW.value,
                "routingOrder": certified_delivery.routing_order,
                "id": certified_delivery.recipient_id,
                "email": certified_delivery.email,
                "name": certified_delivery.name,
                "role": role,
                "title": title,
            }
            signing_order.add(recipient["routingOrder"])
            recipients.append(recipient)

    recipients = sorted(
        recipients, key=lambda recipient: (int(recipient["routingOrder"]))
    )

    return recipients, len(signing_order) > 1


def get_envelope_document_urls(
    client_id, envelope_api, envelope_details, quote_id, **kwargs
):
    """
    return the document with s3 url
    """
    account_id = kwargs.get("account_id")
    envelope_id = kwargs.get("envelope_id")
    envelope_documents = envelope_details.envelope_documents
    cloudfront_cdn = os.environ.get("S3_AVATAR_CDN")
    bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    files_with_s3_urls = []

    if envelope_documents is not None:
        for document in envelope_documents:
            if document.type == "content":
                document_res = envelope_api.get_document_with_http_info(
                    account_id=account_id,
                    envelope_id=envelope_id,
                    document_id=document.document_id,
                    show_changes=True,
                    watermark=False,
                )
                if document_res:
                    with Path(document_res[0]).open("rb") as file_to_write_binary:
                        encoded_string = file_to_write_binary.read()
                    logging.info("Document encoded and ready to upload to S3")
                    document_name = clean_filename(document.name)
                    s3_path = f"quote_template_docusign_documents/{client_id}/{quote_id}/{account_id}/{envelope_id}/{document.document_id}/{document_name}"
                    S3Uploader(bucket_name).upload_file(encoded_string, s3_path)
                    logging.info("Document uploaded to S3")
                    document_url = f"{cloudfront_cdn}/{s3_path}"
                    files_with_s3_urls.append(
                        {
                            "documentName": document_name,
                            "documentUrl": document_url,
                        }
                    )
                    invalidate_cloudfront_cache(cloudfront_cdn, "/" + s3_path)
    return files_with_s3_urls


def envelope_send(client_id, email_id, template_id, quote_id):
    """
    create the envelope
    update the table with updated recipient id,email subject,email body,account id,template id and s3 files url
    send the envelope
    change the status of the quote to Pending Signature
    """
    quote_status = get_status_of_quote(client_id, quote_id)
    if quote_status != CpqQuoteStatusEnum.APPROVED.value:
        code = "Envelope send error"
        message = "To send the mail quote status must be in approved state"
        raise DocusignCpqError(
            code,
            message,
        )
    account_id, api_client = get_docusign_auth_details(client_id, email_id)
    envelope_api = EnvelopesApi(api_client)
    template_api = TemplatesApi(api_client)
    template_details = template_api.get_with_http_info(
        account_id=account_id, template_id=template_id
    )
    envelope_definition = EnvelopeDefinition(
        template_id=template_id,
        status="created",
    )

    envelope_summary_result_with_http_info = (
        envelope_api.create_envelope_with_http_info(
            account_id=account_id, envelope_definition=envelope_definition
        )
    )

    envelope_id = envelope_summary_result_with_http_info[0].envelope_id
    envelope_details = envelope_api.get_envelope(
        account_id=account_id, envelope_id=envelope_id, include="recipients,documents"
    )
    recipients, signing_order = get_updated_recipients(envelope_details.recipients)
    files_with_s3_urls = get_envelope_document_urls(
        client_id=client_id,
        envelope_api=envelope_api,
        envelope_details=envelope_details,
        quote_id=quote_id,
        account_id=account_id,
        envelope_id=envelope_id,
    )
    template_meta_data = get_quote_template_data(
        client_id, quote_id, SharingTypeEnum.DOCUSIGN.value
    )["template_meta_data"]
    template_meta_data["recipients"] = recipients
    template_meta_data["signing_order"] = signing_order
    template_meta_data["files_with_s3_urls"] = files_with_s3_urls
    template_meta_data["email_subject"] = envelope_details.email_subject or ""
    template_meta_data["email_body"] = envelope_details.email_blurb or ""
    DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
        quote_id,
        SharingTypeEnum.DOCUSIGN.value,
        {
            "envelope_id": envelope_id,
            "account_id": account_id,
            "template_id": template_id,
            "template_name": template_details[0].name or "",
            "template_meta_data": template_meta_data,
        },
    )
    envelope_api.update_with_http_info(
        account_id=account_id,
        envelope_id=envelope_id,
        envelope=EnvelopeDefinition(status="sent"),
    )
    logging.info("Envelope successfully sent with ID: %s", envelope_id)
    change_quote_status(client_id, quote_id, CpqQuoteStatusEnum.PENDING_SIGNATURE.value)

    return {
        "status": "SUCCESS",
        "envelope_id": envelope_id,
        "quote_id": quote_id,
        "template_id": template_id,
        "envelope_status": "sent",
    }


def save_docusign_account_settings_data(client_id, secret_key, recipients, **kwargs):
    """
    save the default signer, account id and secret key data to the docusign account settings table
    """
    allow_offline_signing = kwargs.get("allow_offline_signing")
    mandate_offline_signing = kwargs.get("mandate_offline_signing")
    existing_docusign_account_settings_data = DocusignAccountSettingsAccessor(
        client_id
    ).get_docusign_account_settings_data()
    account_settings_data = {}
    account_id = ""
    if secret_key:
        oauth_data = DocusignCpqAccessor(client_id).get_oauth_details()
        if oauth_data is None:
            message = "oauth data not present for the user"
            code = "DOCUSIGN_CPQ_OAUTH_DATA_NOT_FOUND"
            raise DocusignNoDataFoundError(code, message)
        account_id = oauth_data.account_id
    if recipients is not None:
        s3_path = f"docusign_recepients_signature_documents/{client_id}"
        add_signature_url_to_the_recipients(recipients, s3_path)
        account_settings_data["default_signer"] = recipients

    if allow_offline_signing is not None or mandate_offline_signing is not None:
        cpq_settings = get_client_settings(client_id).get("cpq_settings", {})
        if allow_offline_signing is not None:
            cpq_settings["allow_offline_signing"] = allow_offline_signing
        if mandate_offline_signing is not None:
            cpq_settings["mandate_offline_signing"] = mandate_offline_signing
        DocusignAccountSettingsAccessor(client_id).update_cpq_basic_settings_data(
            cpq_settings
        )

    if existing_docusign_account_settings_data:
        existing_account_data = (
            existing_docusign_account_settings_data.account_data
            if existing_docusign_account_settings_data.account_data
            else {}
        )
        if account_id and secret_key:
            existing_account_data[account_id] = secret_key
            account_settings_data["account_data"] = existing_account_data
        DocusignAccountSettingsAccessor(client_id).update_account_settings_data(
            account_settings_data
        )
    else:
        if account_id and secret_key:
            account_settings_data["account_data"] = {account_id: secret_key}
        account_settings_data["client"] = client_id
        account_settings_data["knowledge_begin_date"] = timezone.now()
        docusign_account_settings_ser = DocusignAccountSettingsSerializer(
            data=account_settings_data
        )
        if docusign_account_settings_ser.is_valid():
            DocusignAccountSettingsAccessor(
                client_id
            ).persist_docusign_account_settings_data(docusign_account_settings_ser)
        else:
            code = "DOCUSIGN_ACCOUNT_SETTINGS"
            message = "Error while persisting docusign account settings data"
            raise DocusignCpqError(code, message)

    return {
        "status": "SUCCESS",
        "message": (
            "Connect key updated successfully" if account_id and secret_key else ""
        ),
    }


def get_docusign_account_settings_data(client_id):
    """
    return the default signer data and allow offline signing data for the particular client
    """
    existing_docusign_account_settings_data = DocusignAccountSettingsAccessor(
        client_id
    ).get_docusign_account_settings_data()
    existing_default_signer = (
        existing_docusign_account_settings_data.default_signer or []
        if existing_docusign_account_settings_data
        else []
    )
    client_settings = get_client_settings(client_id)
    cpq_settings = client_settings.get("cpq_settings", {})
    allow_offline_signing = cpq_settings.get("allow_offline_signing") or False
    mandate_offline_signing = cpq_settings.get("mandate_offline_signing") or False
    return {
        "status": "SUCCESS",
        "default_signer": existing_default_signer,
        "allow_offline_signing": allow_offline_signing,
        "mandate_offline_signing": mandate_offline_signing,
    }


def invalidate_quote_data(client_id, quote_id, sharing_type):
    """
    delete the quote data for the corresponding quote id and sharing type of the particular client
    """
    data_to_be_update = {"is_deleted": True, "knowledge_end_date": timezone.now()}
    DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
        quote_id, sharing_type, data_to_be_update
    )
    return {"status": "SUCCESS", "quote_id": quote_id}


def envelope_void(
    client_id,
    email_id,
    envelope_id,
    void_reason,
    **kwargs,
):
    """
    void the envelope for the corresponding envelope id
    also change the status of quote to Approved if need_to_change_quote_status is True
    """
    quote_id = kwargs.get("quote_id")
    need_to_change_quote_status = kwargs.get("need_to_change_quote_status", True)
    account_id, api_client = get_docusign_auth_details(client_id, email_id)
    envelope_definition = EnvelopeDefinition(status="voided", voided_reason=void_reason)
    results_info = EnvelopesApi(api_client).update_with_http_info(
        account_id, envelope_id, envelope=envelope_definition
    )
    logging.info("DOCUSIGN API LIMIT: void_envelope %s", results_info[2])
    if need_to_change_quote_status:
        retract_quote_to_approved(client_id=client_id, quote_id=quote_id)
    invalidate_quote_data(client_id, quote_id, SharingTypeEnum.DOCUSIGN.value)
    return {"status": "SUCCESS"}
