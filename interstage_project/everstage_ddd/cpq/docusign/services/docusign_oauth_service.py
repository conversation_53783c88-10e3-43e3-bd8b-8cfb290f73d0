import datetime
import logging
import os

from django.core.cache import cache
from django.utils import timezone
from docusign_esign import Connect<PERSON><PERSON>, ConnectCustomConfiguration, ConnectEventData

from spm.serializers.docusign_serializers import DocusignSerializer

from ..accessors.docusign_accessor import (
    DocusignAccountSettingsAccessor,
    DocusignCpqAccessor,
    DocusignQuoteTemplateDetailsAccessor,
)
from ..docusign_enums import (
    CpqQuoteStatusEnum,
    DocusignWebHookConfigurationEnum,
    DocusignWebhookEventEnum,
    SharingTypeEnum,
)
from ..docusign_serializers import DocusignAccountSettingsSerializer
from ..docusign_utils import (
    fetch_cpq_site_url,
    get_docusign_auth_details,
    get_status_of_quote,
)

DOCUSIGN_CLIENT_ID, DOCUSIGN_CLIENT_SECRET = (
    os.environ["DOCUSIGN_CLIENT_ID"],
    os.environ["DOCUSIGN_CLIENT_SECRET"],
)


def setup_docusign_webhook(api_client, account_id, base_uri, webhook_url, access_token):
    api_client.host = base_uri + "/restapi/"
    api_client.set_default_header(
        header_name="Authorization", header_value=f"Bearer {access_token}"
    )
    event_data = ConnectEventData(
        include_data=[
            "documents",
            "recipients",
            "attachments",
        ],
        version="restv2.1",
    )
    connect_config = ConnectCustomConfiguration(
        url_to_publish_to=webhook_url,
        allow_envelope_publish=True,
        enable_log=True,
        event_data=event_data,
        events=[
            DocusignWebhookEventEnum.ENVELOPE_SENT.value,
            DocusignWebhookEventEnum.ENVELOPE_COMPLETED.value,
            DocusignWebhookEventEnum.ENVELOPE_DECLINED.value,
            DocusignWebhookEventEnum.ENVELOPE_VOIDED.value,
            DocusignWebhookEventEnum.RECIPIENT_COMPLETED.value,
            DocusignWebhookEventEnum.RECIPIENT_SENT.value,
            DocusignWebhookEventEnum.RECIPIENT_DECLINED.value,
        ],
        include_documents=True,
        include_hmac=True,
        use_soap_interface=False,
        include_cert_soap_header=False,
        all_users=True,
        configuration_type=DocusignWebHookConfigurationEnum.CONFIGURATION_TYPE.value,
        delivery_mode=DocusignWebHookConfigurationEnum.DELIVERY_MODE.value,
        name=DocusignWebHookConfigurationEnum.CONFIGURATION_NAME.value,
        requires_acknowledgement=True,
    )
    connect_api = ConnectApi(api_client)
    response = connect_api.create_configuration(
        account_id, connect_custom_configuration=connect_config
    )
    return response


def authenticate_docusign_user(client_id, api_client, code, login_email_id):
    time = timezone.now()
    oauth_data = {}
    response = api_client.generate_access_token(
        client_id=DOCUSIGN_CLIENT_ID,
        client_secret=DOCUSIGN_CLIENT_SECRET,
        code=code,
    )
    access_token = response.access_token
    refresh_token = response.refresh_token
    timeout = int(response.expires_in)
    refresh_token_expiry = time + datetime.timedelta(
        days=30
    )  # standard expiry is 30 days for refresh token
    # get user info
    user_info = api_client.get_user_info(access_token=access_token)
    base_uri_response = api_client.sanitize_for_serialization(user_info)
    logging.info("DOCUSIGN: get_user_info response %s", base_uri_response)

    # docusign data of the user
    oauth_data["client"] = client_id
    oauth_data["refresh_token"] = refresh_token
    oauth_data["refresh_token_expires_at"] = refresh_token_expiry
    oauth_data["knowledge_begin_date"] = timezone.now()
    oauth_data["email_id"] = base_uri_response.get("email")
    oauth_data["login_email_id"] = login_email_id
    oauth_data["additional_details"] = {"module": "CPQ"}
    accounts = base_uri_response.get("accounts", [])
    for account in accounts:
        is_default = account.get("is_default")
        if (
            str(is_default).lower() == "true"
        ):  # converting both bool and string value to string
            oauth_data["account_id"] = account.get("account_id")
            oauth_data["base_uri"] = account.get("base_uri")
            break
    cache_key = (
        f'docusign_token##{oauth_data.get("email_id")}##{oauth_data.get("account_id")}'
    )
    cache.set(cache_key, access_token, timeout)
    docusign_account_settings_accessor = DocusignAccountSettingsAccessor(client_id)
    docusign_account_settings_data = (
        docusign_account_settings_accessor.get_docusign_account_settings_data()
    )
    connect_id_data = (
        docusign_account_settings_data.connect_id_data
        if docusign_account_settings_data
        and docusign_account_settings_data.connect_id_data
        else {}
    )
    if not connect_id_data.get(oauth_data["account_id"]):
        res = setup_docusign_webhook(
            api_client,
            oauth_data["account_id"],
            oauth_data["base_uri"],
            f"{fetch_cpq_site_url().replace('http://', 'https://')}/ninja-webhook/docusign",
            access_token,
        )
        account_settings_data = {}
        connect_id_data[oauth_data["account_id"]] = res.connect_id
        account_settings_data["connect_id_data"] = connect_id_data
        if docusign_account_settings_data:
            docusign_account_settings_accessor.update_account_settings_data(
                account_settings_data
            )
        else:
            account_settings_data["client"] = client_id
            account_settings_data["knowledge_begin_date"] = timezone.now()
            docusign_account_settings_ser = DocusignAccountSettingsSerializer(
                data=account_settings_data
            )
            if docusign_account_settings_ser.is_valid():
                DocusignAccountSettingsAccessor(
                    client_id
                ).persist_docusign_account_settings_data(docusign_account_settings_ser)

    ser = DocusignSerializer(data=oauth_data)
    if ser.is_valid(raise_exception=True):
        DocusignCpqAccessor(client_id).invalidate(
            account_id=oauth_data["account_id"], knowledge_date=time
        )
        ser.save()


def disconnect_docusign_user(client_id, login_email_id, action_summary):
    time = timezone.now()
    logging.info({"email_id": login_email_id, "action": action_summary})

    prev_record = DocusignCpqAccessor(client_id).get_oauth_details()

    DocusignCpqAccessor(client_id).delete_client_account(
        prev_record=prev_record, knowledge_date=time
    )
    return {"status": "Success"}


def has_docusign_integration(client_id, login_email_id):
    oauth_data = DocusignCpqAccessor(client_id).get_oauth_details()
    token_threshold = timezone.now() + datetime.timedelta(days=1)
    if oauth_data and oauth_data.refresh_token_expires_at <= token_threshold:
        try:
            disconnect_docusign_user(
                client_id=client_id,
                login_email_id=login_email_id,
                action_summary="Disconnected Docusign (Token Expired)",
            )

            return {"has_integration": False, "email": None, "has_secret": False}
        except Exception as e:
            logging.exception("An error occurred: %s", exc_info=e)
            return {"has_integration": False, "email": None, "has_secret": False}
    elif oauth_data:
        has_secret = bool(
            DocusignAccountSettingsAccessor(client_id).get_secret_key_by_account_id(
                oauth_data.account_id
            )
        )
        return {
            "has_integration": True,
            "email": oauth_data.email_id,
            "has_secret": has_secret,
        }
    else:
        return {"has_integration": False, "email": None, "has_secret": False}


def validate_disconnect_docusign(client_id, email_id):
    """
    If Error throw then it means not able to disconnect docusign
    else able to disconnect docusign
    """
    account_id, _ = get_docusign_auth_details(client_id, email_id)
    quote_ids = DocusignQuoteTemplateDetailsAccessor(
        client_id
    ).get_all_active_quote_ids_with_envelope(account_id, SharingTypeEnum.DOCUSIGN.value)
    if quote_ids:
        for quote_id in quote_ids:
            if (
                get_status_of_quote(client_id, quote_id)
                == CpqQuoteStatusEnum.PENDING_SIGNATURE.value
            ):
                message = "There are quotes that are pending signature. Please void the envelopes before disconnecting Docusign."
                return {"status": "Not Valid", "message": message}
    return {"status": "Valid"}
