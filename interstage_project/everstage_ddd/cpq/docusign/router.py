import json
import logging
import os

from django.db import transaction
from docusign_esign import ApiException
from docusign_esign.client.api_client import ApiClient
from docusign_esign.client.auth import OAuth
from ninja import Form, Router
from ninja.decorators import decorate_view
from ninja.errors import HttpError
from rest_framework import status

from commission_engine.utils.general_data import RbacPermissions
from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.auth_utils import requires_scope

from .docusign_exception import (
    DocusignApiError,
    DocusignCpqError,
    DocusignNoDataFoundError,
)
from .schema import (
    CreateTemplatePayload,
    CreateUpdateTemplateResponse,
    DeleteQuoteDataPayload,
    DeleteQuoteDataResponse,
    GetDocusignAccountSettingsResponse,
    LogoutUrlResponse,
    OauthCallbackPayload,
    OauthCallbackResponse,
    OauthDisconnectPayload,
    QuoteTemplateDetailsPayload,
    QuoteTemplateDetailsResponse,
    SaveDocusignAccountSettingsPayload,
    SaveDocusignAccountSettingsResponse,
    SaveQuoteTemplateDataPayload,
    SaveQuoteTemplateDataResponse,
    SendEnvelopePayload,
    SendEnvelopeResponse,
    TemplateEditViewPayload,
    UpdateTemplatePayload,
    ValidateDisconnectPayload,
    ValidateDisconnectResponse,
    VoidEnvelopePayload,
    VoidEnvelopeResponse,
)
from .services.docusign_oauth_service import (
    authenticate_docusign_user,
    disconnect_docusign_user,
    has_docusign_integration,
    validate_disconnect_docusign,
)
from .services.docusign_template_service import (
    create_edit_view,
    create_template,
    envelope_send,
    envelope_void,
    get_docusign_account_settings_data,
    get_quote_template_details_by_quote_id,
    invalidate_quote_data,
    save_docusign_account_settings_data,
    save_quote_template_details,
    update_template,
)

DOCUSIGN_CLIENT_ID, DOCUSIGN_CLIENT_SECRET = (
    os.environ["DOCUSIGN_CLIENT_ID"],
    os.environ["DOCUSIGN_CLIENT_SECRET"],
)

docusign_router = Router(tags=["docusign"])


def handle_docusign_errors(e: Exception, api_name: str):
    if isinstance(e, DocusignNoDataFoundError):
        logging.exception(f"{api_name} - No data found error", exc_info=e)
        return HttpError(e.status.value, str(e))

    if isinstance(e, DocusignApiError):
        logging.exception(f"{api_name} - API error", exc_info=e)
        return HttpError(e.status.value, str(e))

    if isinstance(e, DocusignCpqError):
        logging.exception(f"{api_name} - Bad request error", exc_info=e)
        return HttpError(e.status.value, str(e))

    if isinstance(e, ApiException):
        error_message = e.body.decode("utf-8") if e.body else "Docusign API Error"
        logging.exception(f"{api_name} - API exception", exc_info=e)
        return HttpError(status.HTTP_400_BAD_REQUEST, error_message)

    logging.exception(f"{api_name} - Unexpected error", exc_info=e)
    return HttpError(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@docusign_router.get("oauth/auth")
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_oauth_access_token(request) -> str:
    try:
        if is_prod_env():
            oauth_host_name = OAuth.PRODUCTION_OAUTH_BASE_PATH
        else:
            oauth_host_name = OAuth.DEMO_OAUTH_BASE_PATH
        api_client = ApiClient(oauth_host_name=oauth_host_name)
        redirect_uri = os.environ.get("URL") + "/cpq/settings/docusign-integration"
        scopes = OAuth.SCOPE_EXTENDED
        auth_url = api_client.get_authorization_uri(
            client_id=DOCUSIGN_CLIENT_ID,
            scopes=scopes,
            redirect_uri=redirect_uri,
            response_type="code",
        )
        return auth_url
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign OAuth Auth") from e


@docusign_router.post("oauth/callback", response=OauthCallbackResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def oauth_callback(request, payload: OauthCallbackPayload) -> OauthCallbackResponse:
    if is_prod_env():
        oauth_host_name = OAuth.PRODUCTION_OAUTH_BASE_PATH
    else:
        oauth_host_name = OAuth.DEMO_OAUTH_BASE_PATH
    api_client = ApiClient(oauth_host_name=oauth_host_name)
    client_id = request.client_id
    code = payload.code
    login_email_id = payload.login_email_id
    try:
        authenticate_docusign_user(client_id, api_client, code, login_email_id)
        logging.info("Saved docusign user info!")
        return OauthCallbackResponse(status="Success")
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign OAuth Callback") from e


@docusign_router.get("logout/url", response=LogoutUrlResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_docusign_logout_url(request) -> LogoutUrlResponse:
    try:
        if is_prod_env():
            url = "https://account.docusign.com/"
        else:
            url = "https://account-d.docusign.com/"
        logout_url = (
            url
            + f"logout?client_id={os.environ.get('DOCUSIGN_CLIENT_ID')}&redirect_uri={os.environ.get('URL')}/cpq/settings/docusign-integration"
            f"&response_mode=logout_redirect "
        )
        return LogoutUrlResponse(url=logout_url)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Logout URL") from e


@docusign_router.post("validate_disconnect", response=ValidateDisconnectResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def disconnect_validate(
    request, payload: ValidateDisconnectPayload
) -> ValidateDisconnectResponse:
    try:
        client_id = request.client_id
        email_id = payload.email_id
        res = validate_disconnect_docusign(client_id, email_id)
        return ValidateDisconnectResponse(**res)
    except Exception as e:
        raise handle_docusign_errors(e, "Validate Disconnect Docusign") from e


@docusign_router.post("oauth/disconnect", response=OauthCallbackResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def disconnect_docusign(
    request, payload: OauthDisconnectPayload
) -> OauthCallbackResponse:
    try:
        with transaction.atomic():
            email = payload.email_id
            docusign_disconnection_res = disconnect_docusign_user(
                client_id=request.client_id,
                login_email_id=email,
                action_summary="Docusign Disconnected by User",
            )
            return OauthCallbackResponse(**docusign_disconnection_res)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign OAuth Disconnect") from e


@docusign_router.post("template/create", response=CreateUpdateTemplateResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def template_create(
    request, payload: Form[CreateTemplatePayload]
) -> CreateUpdateTemplateResponse:
    try:
        client_id = request.client_id
        template_files = request.FILES
        email_id = payload.email_id
        template_recipients = json.loads(payload.recipients)
        template_notification_settings = (
            json.loads(payload.template_notification_settings)
            if payload.template_notification_settings
            else None
        )
        template_name = payload.template_name
        quote_id = payload.quote_id
        email_subject = payload.email_subject
        email_body = payload.email_body
        template_details = create_template(
            client_id=client_id,
            email_id=email_id,
            roles=template_recipients,
            template_files=template_files,
            template_name=template_name,
            template_notification_settings=template_notification_settings,
            quote_id=quote_id,
            email_body=email_body,
            email_subject=email_subject,
        )
        return CreateUpdateTemplateResponse(**template_details)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Template Create") from e


@docusign_router.post("template/update", response=CreateUpdateTemplateResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def template_update(
    request, payload: Form[UpdateTemplatePayload]
) -> CreateUpdateTemplateResponse:
    try:
        client_id = request.client_id
        template_files = request.FILES
        email_id = payload.email_id
        template_recipients = json.loads(payload.recipients)
        template_notification_settings = (
            json.loads(payload.template_notification_settings)
            if payload.template_notification_settings
            else None
        )
        template_id = payload.template_id
        template_name = payload.template_name
        quote_id = payload.quote_id
        email_subject = payload.email_subject
        email_body = payload.email_body
        template_details = update_template(
            client_id=client_id,
            email_id=email_id,
            roles=template_recipients,
            template_files=template_files,
            template_id=template_id,
            template_name=template_name,
            template_notification_settings=template_notification_settings,
            quote_id=quote_id,
            email_body=email_body,
            email_subject=email_subject,
        )
        return CreateUpdateTemplateResponse(**template_details)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Template Update") from e


@docusign_router.post("template/edit_view", response=LogoutUrlResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def template_edit_view(request, payload: TemplateEditViewPayload) -> LogoutUrlResponse:
    template_id = payload.template_id
    client_id = request.client_id
    email_id = payload.email_id
    try:
        url = create_edit_view(client_id, email_id, template_id)
        return LogoutUrlResponse(url=url)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Template Edit View") from e


@docusign_router.post("send/envelope", response=SendEnvelopeResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def send_envelope(request, payload: SendEnvelopePayload) -> SendEnvelopeResponse:
    try:
        client_id = request.client_id
        email_id = payload.email_id
        template_id = payload.template_id
        quote_id = payload.quote_id
        envelope_send_response = envelope_send(
            client_id, email_id, template_id, quote_id
        )
        return SendEnvelopeResponse(**envelope_send_response)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Send Envelope") from e


@docusign_router.post("save/quote_data", response=SaveQuoteTemplateDataResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def save_quote_template_data(
    request, payload: Form[SaveQuoteTemplateDataPayload]
) -> SaveQuoteTemplateDataResponse:
    try:
        client_id = request.client_id
        template_files = request.FILES
        quote_id = payload.quote_id
        recipients = json.loads(payload.recipients) if payload.recipients else None
        email_subject = payload.email_subject
        email_body = payload.email_body
        sharing_reason = payload.reason
        sharing_type = payload.type
        res = save_quote_template_details(
            client_id=client_id,
            quote_id=quote_id,
            recipients=recipients,
            template_files=template_files,
            email_subject=email_subject,
            email_body=email_body,
            sharing_type=sharing_type,
            sharing_reason=sharing_reason,
        )
        return SaveQuoteTemplateDataResponse(**res)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Save Quote Template Data") from e


@docusign_router.post("quote/template_details", response=QuoteTemplateDetailsResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def get_quote_template_details(
    request, payload: QuoteTemplateDetailsPayload
) -> QuoteTemplateDetailsResponse:
    try:
        client_id = request.client_id
        email_id = payload.email_id
        quote_id = payload.quote_id
        sharing_type = payload.type
        quote_template_details = get_quote_template_details_by_quote_id(
            client_id, email_id, quote_id, sharing_type
        )
        return QuoteTemplateDetailsResponse(**quote_template_details)

    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Get Quote Template Details") from e


@docusign_router.post("void/envelope", response=VoidEnvelopeResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def void_envelope(request, payload: VoidEnvelopePayload) -> VoidEnvelopeResponse:
    try:
        client_id = request.client_id
        envelope_id = payload.envelope_id
        email_id = payload.email_id
        void_reason = payload.void_reason
        quote_id = payload.quote_id
        envelope_void_response = envelope_void(
            client_id=client_id,
            email_id=email_id,
            envelope_id=envelope_id,
            void_reason=void_reason,
            quote_id=quote_id,
        )
        return VoidEnvelopeResponse(**envelope_void_response)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Void Envelope") from e


@docusign_router.post(
    "save/account_settings", response=SaveDocusignAccountSettingsResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def save_account_settings_data(
    request, payload: SaveDocusignAccountSettingsPayload
) -> SaveDocusignAccountSettingsResponse:
    try:
        client_id = request.client_id
        secret_key = payload.secret_key
        recipients = payload.recipients
        allow_offline_signing = payload.allow_offline_signing
        mandate_offline_signing = payload.mandate_offline_signing
        res = save_docusign_account_settings_data(
            client_id=client_id,
            secret_key=secret_key,
            recipients=recipients,
            allow_offline_signing=allow_offline_signing,
            mandate_offline_signing=mandate_offline_signing,
        )
        return SaveDocusignAccountSettingsResponse(**res)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Save Account Settings Data") from e


@docusign_router.post("account/settings", response=GetDocusignAccountSettingsResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def get_account_settings_data(
    request, payload: OauthDisconnectPayload
) -> GetDocusignAccountSettingsResponse:
    try:
        client_id = request.client_id
        login_email_id = payload.email_id
        account_settings_data = get_docusign_account_settings_data(client_id)
        docusign_connection_status_res = has_docusign_integration(
            client_id, login_email_id
        )

        return GetDocusignAccountSettingsResponse(
            **account_settings_data, **docusign_connection_status_res
        )
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Get Account Settings Data") from e


@docusign_router.post("delete/quote_data", response=DeleteQuoteDataResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def delete_quote_data(
    request, payload: DeleteQuoteDataPayload
) -> DeleteQuoteDataResponse:
    try:
        client_id = request.client_id
        quote_id = payload.quote_id
        sharing_type = payload.type
        res = invalidate_quote_data(client_id, quote_id, sharing_type)
        return DeleteQuoteDataResponse(**res)
    except Exception as e:
        raise handle_docusign_errors(e, "Docusign Delete Quote Data") from e
