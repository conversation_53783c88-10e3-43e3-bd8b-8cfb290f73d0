from uuid import UUID

from celery import shared_task
from django.db import connection
from django.utils.timezone import now

from commission_engine.accessors.client_accessor import get_client_subscription_plan
from commission_engine.accessors.etl_housekeeping_accessor import ETLSyncStatusAccessor
from common.celery.celery_base_task import EverC<PERSON>ryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group

from .constants import ETLActivity, ETLStatus


def get_misc_queue_name(client_id: int) -> str:
    subscription_plan = get_client_subscription_plan(client_id)
    return get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.MISC.value
    )


def get_etl_status_summary(
    client_id: int, e2e_sync_run_id: UUID, task: str
) -> tuple[int, int]:
    if task == ETLActivity.UPSTREAM_E2E_SYNC.value:
        query = """
        select
            count(*) as total,
            count(*) filter (where status = 'complete' or status = 'success') as success
        from
            (
                select sync_status as status from upstream_etl_status
                where client_id = %(client_id)s and e2e_sync_run_id = %(e2e_sync_run_id)s and object_id != 'UPSTREAM_WRAPPER_SYNC'
                union all
                select sync_status as status from databook_etl_status
                where client_id = %(client_id)s and e2e_sync_run_id = %(e2e_sync_run_id)s and task != 'DATASHEET_WRAPPER_SYNC'
            ) as _;
        """
    elif task == ETLActivity.DOWNSTREAM_E2E_SYNC.value:
        query = """
        select
            count(*) as total,
            count(*) filter (where status = 'complete' or status = 'success') as success
        from
            (
                select sync_status as status from report_etl_status
                where client_id = %(client_id)s and e2e_sync_run_id = %(e2e_sync_run_id)s and task != 'REPORT_WRAPPER_SYNC'
                union all
                select sync_status as status from databook_etl_status
                where client_id = %(client_id)s and e2e_sync_run_id = %(e2e_sync_run_id)s and task != 'DATASHEET_WRAPPER_SYNC'
                union all
                select state as status from downstream_etl_status
                where client_id = %(client_id)s and e2e_sync_run_id = %(e2e_sync_run_id)s
            ) as _;
        """
    else:
        return (0, 0)
    with connection.cursor() as cursor:
        params = {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
        cursor.execute(query, params)
        return cursor.fetchall()[0]


@shared_task(base=EverCeleryBaseTask)
def begin_e2e_sync(
    client_id: int, e2e_sync_run_id: UUID, task: str, params: dict, audit: dict
) -> None:
    ETLSyncStatusAccessor(client_id, e2e_sync_run_id).insert_sync_status(
        **{
            "task": task,
            "sync_status": ETLStatus.STARTED.value,
            "sync_start_time": now(),
            "params": params,
            "audit": audit,
        }
    )


@shared_task(base=EverCeleryBaseTask)
def end_e2e_sync(client_id: int, e2e_sync_run_id: UUID, task: str) -> None:
    total, success = get_etl_status_summary(client_id, e2e_sync_run_id, task)
    if total == 0:
        sync_status = ETLStatus.SKIPPED.value
    elif total == success:
        sync_status = ETLStatus.COMPLETE.value
    elif success == 0:
        sync_status = ETLStatus.FAILED.value
    else:
        sync_status = ETLStatus.PARTIALLY_FAILED.value
    ETLSyncStatusAccessor(client_id, e2e_sync_run_id).update_object(
        {
            "sync_status": sync_status,
            "sync_end_time": now(),
        }
    )
