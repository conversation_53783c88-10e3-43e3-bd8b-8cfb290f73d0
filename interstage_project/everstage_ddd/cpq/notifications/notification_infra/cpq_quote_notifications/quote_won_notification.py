import logging

from typing_extensions import Unpack

from commission_engine.utils.general_data import CpqNotification
from everstage_ddd.cpq.notifications.constants import CpqEmailTemplateID
from everstage_ddd.cpq.notifications.cpq_base_notification import CpqBaseNotification
from everstage_ddd.cpq.notifications.notification_infra.notifi_types import (
    QuoteWonData,
    QuoteWonParams,
)
from everstage_ddd.notifications.types import EmailAttachments
from everstage_ddd.notifications.utils import memoize_notif_data
from ms_teams_everstage.services.card_services import create_hero_card
from ms_teams_everstage.utils.emoji import EmojiMsteams
from slack_everstage.utils.emoji import Emoji
from slack_everstage.utils.slack_utils import get_site_url

logger = logging.getLogger(__name__)


class QuoteWonNotification(CpqBaseNotification):
    def __init__(self, client_id: int, **kwargs: Unpack[QuoteWonParams]) -> None:
        """
        Notifies the quote owner when their quote is won/signed.

        Notification name - `QUOTE_WON_NOTIFICATION`.

        Potential Recipients - Quote owner (sales rep who created the quote)
        """
        super().__init__(client_id, **kwargs)

        self.quote_id = kwargs["quote_id"]
        self.notif_task = CpqNotification.QUOTE_WON_NOTIFICATION

        # Will be populated in get_notification_data methods
        self.data_map: dict[str, QuoteWonData] = {}

    @memoize_notif_data
    def get_notification_data(self, email_id: str) -> QuoteWonData:
        from commission_engine.accessors.client_accessor import get_client_settings
        from everstage_ddd.cpq.forms.accessors.form_accessor import (
            EverstageFormAccessor,
        )
        from everstage_ddd.cpq.quote import QuoteSelector
        from everstage_ddd.cpq.quote.quote_status.enums import QuoteStatusEnum
        from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
            QuoteStatusSelector,
        )
        from everstage_ddd.cpq.quote.service.quote_form_service import (
            fetch_opportunity_and_account_details,
        )
        from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
        from spm.services.config_services.employee_services import get_employee_name_map

        name_map = get_employee_name_map(self.client_id, [email_id])
        quote_data = QuoteSelector(self.client_id).get_quote_by_id(self.quote_id)

        client_settings = get_client_settings(client_id=self.client_id)
        portal_id = client_settings.get("cpq_settings", {}).get("portal_id", "")

        quote_status_selector = QuoteStatusSelector(client_id=self.client_id)
        quote_status = quote_status_selector.get_quote_status(quote_id=self.quote_id)

        form_selector = EverstageFormAccessor(client_id=self.client_id)
        form = form_selector.get_object(form_id=quote_data.form_id)
        form_data = form.form_data

        auto_save_form = form_selector.get_auto_save_form(form_id=quote_data.form_id)
        auto_save_form_data = auto_save_form.form_data

        currency_code_symbol_map = {
            country.currency_code: country.currency_symbol
            for country in list(CountriesAccessor(self.client_id).get_all_countries())
        }

        if quote_status and quote_status.status == QuoteStatusEnum.DRAFT.value:
            opportunity_details = fetch_opportunity_and_account_details(
                auto_save_form_data, portal_id
            )
        else:
            opportunity_details = fetch_opportunity_and_account_details(
                form_data, portal_id
            )

        data = {
            "sales_rep": name_map[email_id]["first_name"],
            "recipient_name": name_map[email_id]["full_name"],
            "quote_name": quote_data.quote_name,
            "quote_id": str(quote_data.quote_display_id),
            "account_name": opportunity_details.get("account_name"),
            "opportunity_name": opportunity_details.get("opportunity_name"),
            "quote_url": get_site_url() + f"/cpq/quotes-details/{quote_data.quote_id}",
            "quote_total": currency_code_symbol_map[quote_data.quote_currency]
            + " "
            + str(quote_data.net_quote_total),
            "quote_duration": str(quote_data.duration_value)
            + " "
            + quote_data.duration_type,
            "subscription_start_date": quote_data.start_date.strftime("%b %d, %Y"),
            "subscription_end_date": quote_data.end_date.strftime("%b %d, %Y"),
        }
        notif_data = QuoteWonData(**data)

        return notif_data

    def get_recipients(self, mode) -> list[str]:
        super().get_recipients(mode)
        from everstage_ddd.cpq.quote import QuoteSelector

        quote_data = QuoteSelector(self.client_id).get_quote_by_id(self.quote_id)
        return [quote_data.owner_id]

    def prepare_for_email(self, email_id: str) -> tuple[str, dict, EmailAttachments]:
        data = self.get_notification_data(email_id)

        template_data = {
            "sales_rep": data.sales_rep,
            "recipient_name": data.recipient_name,
            "quote_name": data.quote_name,
            "quote_id": data.quote_id,
            "account_name": data.account_name,
            "opportunity_name": data.opportunity_name,
            "quote_url": data.quote_url,
            "quote_total": str(data.quote_total),
            "quote_duration": data.quote_duration,
            "subscription_start_date": data.subscription_start_date,
            "subscription_end_date": data.subscription_end_date,
        }
        return (
            CpqEmailTemplateID.QUOTE_WON_NOTIFICATION,
            template_data,
            [],
        )

    def prepare_for_slack(self, email_id: str):
        data = self.get_notification_data(email_id)

        # Define the blocks for the Slack message
        blocks = [
            # Header Section
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Woohoo! Congrats on winning this deal, {data.sales_rep}! {Emoji.TADA.value}",
                },
            },
            # Proposal Section
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": (
                        f"><{data.quote_url}|*Proposal for {data.account_name}(#{data.quote_id})*>\n"
                        f">:spiral_calendar_pad: Duration: *{data.quote_duration}*  \u2002|\u2002  "
                        f":moneybag: Value: *{data.quote_total}*"
                    ),
                },
            },
        ]

        # Return title and blocks for Slack notification
        return "Quote Won", blocks

    def prepare_for_msteams(self, email_id: str):
        data = self.get_notification_data(email_id)

        # Create a more detailed and formatted message similar to Slack
        text = (
            f"Woohoo! Congrats on winning this deal, {data.sales_rep}! {EmojiMsteams.TADA.value}<br><br>"
            f"<a href='{data.quote_url}'><b>Proposal for {data.account_name} (#{data.quote_id})</b></a><br>"
            f"{EmojiMsteams.SPIRALCALENDAR.value} Duration: <b>{data.quote_duration}</b> &nbsp;|&nbsp; "
            f"{EmojiMsteams.MONEYBAG.value} Value: <b>{data.quote_total}</b>"
        )

        # No buttons, using hyperlink in the text instead
        return create_hero_card(text=text, buttons=[])
