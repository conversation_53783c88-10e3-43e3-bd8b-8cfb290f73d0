# CPQ Data Sync Setup


## Creating downstream integrations, mappings, and endpoints
- Fetch `client_id` from the `interstage_clients` table.
- Generate a new `integration_id` for the downstream integration.
- Retrieve `datasheet_id` for the source from the `datasheet` table.
- Obtain the destination object name from the data setup template.
- Fetch `connection_id` from the `access_token_config` table.
- Get system names of source fields from the `datasheet_variable` table.
- Extract destination fields and data types from the data setup template.
- Update the `endpoint_url` with the correct instance URL and object name.
- Update the fields in the provided sample queries accordingly.
- Execute the following queries using DBeaver.

```sql
INSERT INTO public.downstream_integration (integration_id, integration_name, source_object, destination_object, synced_till, additional_data, client_id, connection_id) VALUES
('481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid, 'Quote', 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6', 'EverstageQuote__c', '1970-01-01 00:00:00.000', '{"batch_size": 10000}'::jsonb, 31617, 2);

INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES
('name', 'name', 'string', false, false, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid),
('net_total', 'net_total__c', 'number', false, false, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid),
('owner_email', 'owner_email__c', 'string', false, false, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid),
('primary_quote', 'primary_quote__c', 'boolean', false, false, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid),
('quote_id', 'quote_id__c', 'string', true, true, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid),
('won_date', 'won_date__c', 'datetime', false, false, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid);

INSERT INTO public.downstream_endpoint (operation, url, "method", page_size, headers, body, additional_data, client_id, integration_id) VALUES 
('bulk_upsert', 'https://everstage-c-dev-ed.develop.my.salesforce.com/services/data/v63.0/composite/sobjects/EverstageQuote__c/quote_id__c', 'patch', 200, NULL, NULL, NULL, 31617, '481a2fa9-ad43-4f3f-a503-4ba40d57011c'::uuid);
```


## Creating scheduled tasks for E2E wrapper syncs
- Retrieve `client_id` and `subscription_plan` from the `interstage_clients` table.
- Generate new `task_ids` for the scheduled tasks.
- Upstream E2E Wrapper:
    - Retrieve `custom_object_id` from the `custom_object` table.
    - Using the `custom_object_id`, fetch `integration_id` from the `integration` table.
    - Retrieve `databook_id` from the `databook` table.
- Downstream E2E Wrapper:
    - Retrieve `report_object_id` from the `ever_object` table.
    - Retrieve `databook_id` from the `databook` table.
    - Fetch `integration_id` from the `downstream_integration` table.
- Update the fields in the provided sample queries accordingly.
- Execute the following queries using DBeaver.

```sql
INSERT INTO public.scheduled_tasks (id, path, params, queue, priority, is_enabled, client_id) VALUES
('692e1891-97a2-4945-8df5-e23e2d5a7420'::uuid, 'everstage_ddd.cpq.tasks.upstream_e2e_wrapper.upstream_e2e_wrapper', '{"trigger": "realtime_extraction", "client_id": 31617, "databook_ids": ["132c4b7f-9bf4-4a50-b812-c9f186a94287"], "integration_ids": ["1e5a4a41-e529-40b6-9b10-c36b4fb13b1a", "b1c97719-c19d-4ad2-85bd-26b37fea5955", "eb24429d-39ea-4e9e-af01-92a03b76fb0e"]}'::jsonb, 'eq.BASIC.UPSTREAM.31617', 0, true, 31617),
('fd51e5d8-cd8d-414d-8552-3bf0ad598223'::uuid, 'everstage_ddd.cpq.tasks.downstream_e2e_wrapper.downstream_e2e_wrapper', '{"client_id": 31617, "databook_ids": ["1e286993-275f-4500-bd47-d56f088a2e52"], "integration_ids": ["481a2fa9-ad43-4f3f-a503-4ba40d57011c", "c58aa18e-18d9-4e84-9327-052224fcc724", "a5a65372-d8e4-419f-a607-b61daf9b4810"], "report_object_ids": ["quote", "quote_line_item", "quote_line_item_tier"]}'::jsonb, 'eq.BASIC.DOWNSTREAM.31617', 0, true, 31617);
```


## Enabling scheduled tasks for a given interval
- Generate the cron expression for the given interval. For interval of 15 minutes, `0,15,30,45`.
- Run the following code snippet through the Django shell with the appropriate `client_id` and `minute`.

```bash
sudo su
docker ps
docker exec -it <container_id> /bin/bash
python
```

```python
from django import setup
from interstage_project import settings

setup()

from everstage_ddd.scheduler.services import MissMinutes

MissMinutes.toggle(31617, is_enabled=True, minute="0,15,30,45")
```
