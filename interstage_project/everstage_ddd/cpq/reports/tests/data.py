# ruff: noqa: DTZ001
from datetime import datetime
from decimal import Decimal
from uuid import UUID

from psycopg2.extras import RealDictRow

QUOTE_EXTRACTED = [
    RealDictRow(
        [
            ("quote_id", UUID("e3a81046-b36e-4d61-87ea-689cc5a93384")),
            ("display_id", "ES1174"),
            ("name", "Katun "),
            ("form_id", UUID("fe887d13-391d-4762-85d7-0e872d7c4adf")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Approved"),
            ("owner_name", "Mona Lisa"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 7, 5, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 7, 1, 0, 0)),
            ("subscription_end_date", datetime(2027, 6, 30, 23, 59, 59, 999000)),
            ("duration", 2),
            ("duration_type", "Years"),
            ("list_total", 142424.0),
            ("net_total", 85992.0),
            ("discount", 56432.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 5, 13, 26, 28, 321000)),
            ("last_updated_date", datetime(2025, 6, 5, 13, 41, 12, 509000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("082aea32-18ab-4f0d-ae2d-5fb95d5ba679")),
            ("display_id", "ES1171"),
            ("name", "Everstage Quote for APFM "),
            ("form_id", UUID("5dcb0c0c-8e99-485d-a479-e00a3f7ca5c7")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Pending Approval"),
            ("owner_name", "Jack Halley"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 9, 30, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 7, 1, 0, 0)),
            ("subscription_end_date", datetime(2027, 6, 30, 23, 59, 59, 999000)),
            ("duration", 2),
            ("duration_type", "Years"),
            ("list_total", 540600.0),
            ("net_total", 268000.0),
            ("discount", 272600.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 3, 15, 21, 51, 844000)),
            ("last_updated_date", datetime(2025, 6, 5, 19, 34, 13, 306000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("283a911a-2faf-498a-b616-4012fe2476c1")),
            ("display_id", "ES1175"),
            ("name", "G&A Partners - Everstage Order Form"),
            ("form_id", UUID("1a1a2511-cbeb-4c8d-96b0-3c40a96342da")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Approved"),
            ("owner_name", "Shaun Marsh"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 10, 31, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 7, 1, 0, 0)),
            ("subscription_end_date", datetime(2028, 6, 30, 23, 59, 59, 999000)),
            ("duration", 3),
            ("duration_type", "Years"),
            ("list_total", 530840.0),
            ("net_total", 243760.0),
            ("discount", 287080.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 5, 22, 46, 41, 248000)),
            ("last_updated_date", datetime(2025, 6, 6, 12, 9, 3, 904000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("bc147d09-3af4-4a00-91c5-1965a43f6dcf")),
            ("display_id", "ES1176"),
            ("name", "Greenix"),
            ("form_id", UUID("63835bb8-4b07-4d86-a9b6-ea3d08315e16")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Approved"),
            ("owner_name", "Charile Pinkman"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 6, 27, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 6, 7, 0, 0)),
            ("subscription_end_date", datetime(2028, 6, 6, 23, 59, 59, 999000)),
            ("duration", 3),
            ("duration_type", "Years"),
            ("list_total", 1600000.0),
            ("net_total", 326000.0),
            ("discount", 1274000.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 7, 17, 23, 54, 279000)),
            ("last_updated_date", datetime(2025, 6, 9, 15, 0, 53, 511000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("db344a84-4517-41e0-b6fc-d24270beaed5")),
            ("display_id", "ES1042"),
            ("name", "Internal Testing - Ignore"),
            ("form_id", UUID("9cde6d34-1da6-460c-b0b4-524ef9d3b36d")),
            ("form_name", "Deal Hub Form"),
            ("currency", None),
            ("status", "Draft"),
            ("owner_name", "Ashutosh Sharma"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 4, 18, 18, 30)),
            ("opportunity_id", "***********"),
            ("account_id", "None"),
            ("contact_id", "None"),
            ("subscription_start_date", datetime(2025, 1, 25, 0, 0)),
            ("subscription_end_date", datetime(2027, 1, 24, 23, 59, 59, 999000)),
            ("duration", 2),
            ("duration_type", "Years"),
            ("list_total", 262800.0),
            ("net_total", 228600.0),
            ("discount", 34200.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 1, 22, 13, 43, 56, 860000)),
            ("last_updated_date", datetime(2025, 6, 9, 8, 20, 25, 106000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("bd104813-3980-4dcb-a00e-79805f74aa38")),
            ("display_id", "ES1177"),
            ("name", "Untitled Quote(3)"),
            ("form_id", UUID("d1d0f1ef-5b31-417f-bb2d-057afd67a055")),
            ("form_name", "Deal Hub Form"),
            ("currency", None),
            ("status", "Draft"),
            ("owner_name", "Charile Pinkman"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 7, 8, 0, 0)),
            ("opportunity_id", ""),
            ("account_id", ""),
            ("contact_id", ""),
            ("subscription_start_date", None),
            ("subscription_end_date", None),
            ("duration", None),
            ("duration_type", "Unknown"),
            ("list_total", None),
            ("net_total", None),
            ("discount", None),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 8, 19, 59, 57, 705000)),
            ("last_updated_date", datetime(2025, 6, 9, 14, 43, 24, 747000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("9b7874ab-a021-43ef-b0bf-7cb9c6c85477")),
            ("display_id", "ES1173"),
            ("name", "Zelis <> Everstage Proposal "),
            ("form_id", UUID("1a2c175a-943f-4afa-b2e0-3db6f9bcf75a")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Pending Signature"),
            ("owner_name", "Charile Pinkman"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 7, 16, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 8, 1, 0, 0)),
            ("subscription_end_date", datetime(2028, 7, 31, 23, 59, 59, 999000)),
            ("duration", 3),
            ("duration_type", "Years"),
            ("list_total", 288280.0),
            ("net_total", 197800.0),
            ("discount", 90480.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 4, 14, 11, 55, 164000)),
            ("last_updated_date", datetime(2025, 6, 9, 17, 20, 48, 907000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("dca2e392-f304-4c32-84dc-fadeab7e86b3")),
            ("display_id", "ES1178"),
            ("name", "Zelis <> Everstage Proposal  1 yr"),
            ("form_id", UUID("********-f9fe-45c2-8c23-71e53b0bdf85")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Approved"),
            ("owner_name", "Charile Pinkman"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 7, 9, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 8, 1, 0, 0)),
            ("subscription_end_date", datetime(2026, 7, 31, 23, 59, 59, 999000)),
            ("duration", 1),
            ("duration_type", "Years"),
            ("list_total", 112760.0),
            ("net_total", 85360.0),
            ("discount", 27400.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 9, 17, 31, 13, 437000)),
            ("last_updated_date", datetime(2025, 6, 9, 17, 34, 25, 585000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("a3409b10-6400-46c6-b494-7101c558ea17")),
            ("display_id", "ES1061"),
            ("name", "Everstage Proposal for Irvine Company"),
            ("form_id", UUID("0b930682-01aa-4cb5-b5fc-2bd6b1c9300a")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Approved"),
            ("owner_name", "Duke Wright"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 7, 31, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "***********"),
            ("contact_id", "***********"),
            ("subscription_start_date", datetime(2025, 11, 3, 0, 0)),
            ("subscription_end_date", datetime(2028, 11, 2, 23, 59, 59, 999000)),
            ("duration", 3),
            ("duration_type", "Years"),
            ("list_total", 816200.0),
            ("net_total", 379000.0),
            ("discount", 437200.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 2, 10, 17, 6, 17, 783000)),
            ("last_updated_date", datetime(2025, 6, 9, 21, 57, 55, 839000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", UUID("6fd3cf49-a59b-4d45-84cc-4a10ba4e935d")),
            ("display_id", "ES1182"),
            ("name", "Cube Global (1 year) Pricing"),
            ("form_id", UUID("5dc0ce77-07d1-4e32-8ae9-8bb043ca5a8f")),
            ("form_name", "Deal Hub Form"),
            ("currency", "USD"),
            ("status", "Approved"),
            ("owner_name", "Jack Halley"),
            ("owner_email", "<EMAIL>"),
            ("valid_till", datetime(2025, 8, 31, 23, 59, 59, 999000)),
            ("opportunity_id", "***********"),
            ("account_id", "**********"),
            ("contact_id", "************"),
            ("subscription_start_date", datetime(2025, 7, 1, 0, 0)),
            ("subscription_end_date", datetime(2026, 6, 30, 23, 59, 59, 999000)),
            ("duration", 1),
            ("duration_type", "Years"),
            ("list_total", 51400.0),
            ("net_total", 29900.0),
            ("discount", 21500.0),
            ("primary_quote", False),
            ("won_date", None),
            ("created_date", datetime(2025, 6, 11, 16, 31, 53, 212000)),
            ("last_updated_date", datetime(2025, 6, 11, 16, 34, 56, 80000)),
            ("deleted", False),
        ]
    ),
]

QUOTE_TRANSFORMED = [
    '(\'\', \'\', \'{"quote_id": "e3a81046-b36e-4d61-87ea-689cc5a93384", "display_id": "ES1174", "name": "Katun ", "form_id": "fe887d13-391d-4762-85d7-0e872d7c4adf", "form_name": "Deal Hub Form", "currency": "USD", "status": "Approved", "owner_name": "Mona Lisa", "owner_email": "<EMAIL>", "valid_till": "2025-07-05 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "************", "subscription_start_date": "2025-07-01 00:00:00", "subscription_end_date": "2027-06-30 23:59:59.999000", "duration": 2, "duration_type": "Years", "list_total": 142424.0, "net_total": 85992.0, "discount": 56432.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-05 13:26:28.321000", "last_updated_date": "2025-06-05 13:41:12.509000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "082aea32-18ab-4f0d-ae2d-5fb95d5ba679", "display_id": "ES1171", "name": "Everstage Quote for APFM ", "form_id": "5dcb0c0c-8e99-485d-a479-e00a3f7ca5c7", "form_name": "Deal Hub Form", "currency": "USD", "status": "Pending Approval", "owner_name": "Jack Halley", "owner_email": "<EMAIL>", "valid_till": "2025-09-30 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "************", "subscription_start_date": "2025-07-01 00:00:00", "subscription_end_date": "2027-06-30 23:59:59.999000", "duration": 2, "duration_type": "Years", "list_total": 540600.0, "net_total": 268000.0, "discount": 272600.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-03 15:21:51.844000", "last_updated_date": "2025-06-05 19:34:13.306000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "display_id": "ES1175", "name": "G&A Partners - Everstage Order Form", "form_id": "1a1a2511-cbeb-4c8d-96b0-3c40a96342da", "form_name": "Deal Hub Form", "currency": "USD", "status": "Approved", "owner_name": "Shaun Marsh", "owner_email": "<EMAIL>", "valid_till": "2025-10-31 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "************", "subscription_start_date": "2025-07-01 00:00:00", "subscription_end_date": "2028-06-30 23:59:59.999000", "duration": 3, "duration_type": "Years", "list_total": 530840.0, "net_total": 243760.0, "discount": 287080.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-05 22:46:41.248000", "last_updated_date": "2025-06-06 12:09:03.904000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "bc147d09-3af4-4a00-91c5-1965a43f6dcf", "display_id": "ES1176", "name": "Greenix", "form_id": "63835bb8-4b07-4d86-a9b6-ea3d08315e16", "form_name": "Deal Hub Form", "currency": "USD", "status": "Approved", "owner_name": "Charile Pinkman", "owner_email": "<EMAIL>", "valid_till": "2025-06-27 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "************", "subscription_start_date": "2025-06-07 00:00:00", "subscription_end_date": "2028-06-06 23:59:59.999000", "duration": 3, "duration_type": "Years", "list_total": 1600000.0, "net_total": 326000.0, "discount": 1274000.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-07 17:23:54.279000", "last_updated_date": "2025-06-09 15:00:53.511000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "db344a84-4517-41e0-b6fc-d24270beaed5", "display_id": "ES1042", "name": "Internal Testing - Ignore", "form_id": "9cde6d34-1da6-460c-b0b4-524ef9d3b36d", "form_name": "Deal Hub Form", "currency": null, "status": "Draft", "owner_name": "Ashutosh Sharma", "owner_email": "<EMAIL>", "valid_till": "2025-04-18 18:30:00", "opportunity_id": "***********", "account_id": "None", "contact_id": "None", "subscription_start_date": "2025-01-25 00:00:00", "subscription_end_date": "2027-01-24 23:59:59.999000", "duration": 2, "duration_type": "Years", "list_total": 262800.0, "net_total": 228600.0, "discount": 34200.0, "primary_quote": false, "won_date": null, "created_date": "2025-01-22 13:43:56.860000", "last_updated_date": "2025-06-09 08:20:25.106000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "bd104813-3980-4dcb-a00e-79805f74aa38", "display_id": "ES1177", "name": "Untitled Quote(3)", "form_id": "d1d0f1ef-5b31-417f-bb2d-057afd67a055", "form_name": "Deal Hub Form", "currency": null, "status": "Draft", "owner_name": "Charile Pinkman", "owner_email": "<EMAIL>", "valid_till": "2025-07-08 00:00:00", "opportunity_id": "", "account_id": "", "contact_id": "", "subscription_start_date": null, "subscription_end_date": null, "duration": null, "duration_type": "Unknown", "list_total": null, "net_total": null, "discount": null, "primary_quote": false, "won_date": null, "created_date": "2025-06-08 19:59:57.705000", "last_updated_date": "2025-06-09 14:43:24.747000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "9b7874ab-a021-43ef-b0bf-7cb9c6c85477", "display_id": "ES1173", "name": "Zelis <> Everstage Proposal ", "form_id": "1a2c175a-943f-4afa-b2e0-3db6f9bcf75a", "form_name": "Deal Hub Form", "currency": "USD", "status": "Pending Signature", "owner_name": "Charile Pinkman", "owner_email": "<EMAIL>", "valid_till": "2025-07-16 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "************", "subscription_start_date": "2025-08-01 00:00:00", "subscription_end_date": "2028-07-31 23:59:59.999000", "duration": 3, "duration_type": "Years", "list_total": 288280.0, "net_total": 197800.0, "discount": 90480.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-04 14:11:55.164000", "last_updated_date": "2025-06-09 17:20:48.907000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "dca2e392-f304-4c32-84dc-fadeab7e86b3", "display_id": "ES1178", "name": "Zelis <> Everstage Proposal  1 yr", "form_id": "********-f9fe-45c2-8c23-71e53b0bdf85", "form_name": "Deal Hub Form", "currency": "USD", "status": "Approved", "owner_name": "Charile Pinkman", "owner_email": "<EMAIL>", "valid_till": "2025-07-09 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "************", "subscription_start_date": "2025-08-01 00:00:00", "subscription_end_date": "2026-07-31 23:59:59.999000", "duration": 1, "duration_type": "Years", "list_total": 112760.0, "net_total": 85360.0, "discount": 27400.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-09 17:31:13.437000", "last_updated_date": "2025-06-09 17:34:25.585000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "a3409b10-6400-46c6-b494-7101c558ea17", "display_id": "ES1061", "name": "Everstage Proposal for Irvine Company", "form_id": "0b930682-01aa-4cb5-b5fc-2bd6b1c9300a", "form_name": "Deal Hub Form", "currency": "USD", "status": "Approved", "owner_name": "Duke Wright", "owner_email": "<EMAIL>", "valid_till": "2025-07-31 23:59:59.999000", "opportunity_id": "***********", "account_id": "***********", "contact_id": "***********", "subscription_start_date": "2025-11-03 00:00:00", "subscription_end_date": "2028-11-02 23:59:59.999000", "duration": 3, "duration_type": "Years", "list_total": 816200.0, "net_total": 379000.0, "discount": 437200.0, "primary_quote": false, "won_date": null, "created_date": "2025-02-10 17:06:17.783000", "last_updated_date": "2025-06-09 21:57:55.839000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "6fd3cf49-a59b-4d45-84cc-4a10ba4e935d", "display_id": "ES1182", "name": "Cube Global (1 year) Pricing", "form_id": "5dc0ce77-07d1-4e32-8ae9-8bb043ca5a8f", "form_name": "Deal Hub Form", "currency": "USD", "status": "Approved", "owner_name": "Jack Halley", "owner_email": "<EMAIL>", "valid_till": "2025-08-31 23:59:59.999000", "opportunity_id": "***********", "account_id": "**********", "contact_id": "************", "subscription_start_date": "2025-07-01 00:00:00", "subscription_end_date": "2026-06-30 23:59:59.999000", "duration": 1, "duration_type": "Years", "list_total": 51400.0, "net_total": 29900.0, "discount": 21500.0, "primary_quote": false, "won_date": null, "created_date": "2025-06-11 16:31:53.212000", "last_updated_date": "2025-06-11 16:34:56.080000", "deleted": false}\')',
]

QUOTE_LINE_ITEM_EXTRACTED = [
    RealDictRow(
        [
            ("quote_id", "d19e0129-248e-4362-bcb9-4f6c53604374"),
            (
                "line_item_id",
                "8fc1e173-aee9-4ecb-989f-9537b07a12b1#_#ed33302a-2c7f-497f-8dba-14563f29e920",
            ),
            ("quote_display_id", "ES1109"),
            ("product_id", "8fc1e173-aee9-4ecb-989f-9537b07a12b1"),
            ("pricepoint_id", "833f5d33-c6ed-4045-8840-cd5e714b216d"),
            ("product_code", "CONNECTOR"),
            ("product_name", "Connector Charges"),
            ("product_description", ""),
            ("category", "Software"),
            ("charged_on", "Connectors"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Monthly"),
            ("phase_name", "Year 1"),
            ("phase_start_date", datetime(2025, 5, 5, 0, 0)),
            ("phase_end_date", datetime(2026, 5, 4, 23, 59, 59, 999000)),
            ("quantity", 2),
            ("list_price", Decimal("200.000000")),
            ("net_price", Decimal("0.000000")),
            ("discount_percentage", Decimal("100.000000")),
            ("total", Decimal("0.000000")),
            ("last_updated_date", datetime(2025, 3, 26, 20, 26, 54, 64000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "d19e0129-248e-4362-bcb9-4f6c53604374"),
            (
                "line_item_id",
                "8fc1e173-aee9-4ecb-989f-9537b07a12b1#_#7f50bc76-ad1b-4cb7-bcfe-1f228cf14d64",
            ),
            ("quote_display_id", "ES1109"),
            ("product_id", "8fc1e173-aee9-4ecb-989f-9537b07a12b1"),
            ("pricepoint_id", "833f5d33-c6ed-4045-8840-cd5e714b216d"),
            ("product_code", "CONNECTOR"),
            ("product_name", "Connector Charges"),
            ("product_description", ""),
            ("category", "Software"),
            ("charged_on", "Connectors"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Monthly"),
            ("phase_name", "Year 2"),
            ("phase_start_date", datetime(2026, 5, 5, 0, 0)),
            ("phase_end_date", datetime(2027, 5, 4, 23, 59, 59, 999000)),
            ("quantity", 2),
            ("list_price", Decimal("200.000000")),
            ("net_price", Decimal("0.000000")),
            ("discount_percentage", Decimal("100.000000")),
            ("total", Decimal("0.000000")),
            ("last_updated_date", datetime(2025, 3, 26, 20, 26, 54, 64000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "d19e0129-248e-4362-bcb9-4f6c53604374"),
            (
                "line_item_id",
                "8fc1e173-aee9-4ecb-989f-9537b07a12b1#_#3d8ed030-94be-4491-aab4-084cef19cde2",
            ),
            ("quote_display_id", "ES1109"),
            ("product_id", "8fc1e173-aee9-4ecb-989f-9537b07a12b1"),
            ("pricepoint_id", "833f5d33-c6ed-4045-8840-cd5e714b216d"),
            ("product_code", "CONNECTOR"),
            ("product_name", "Connector Charges"),
            ("product_description", ""),
            ("category", "Software"),
            ("charged_on", "Connectors"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Monthly"),
            ("phase_name", "Year 3"),
            ("phase_start_date", datetime(2027, 5, 5, 0, 0)),
            ("phase_end_date", datetime(2028, 5, 4, 23, 59, 59, 999000)),
            ("quantity", 2),
            ("list_price", Decimal("200.000000")),
            ("net_price", Decimal("0.000000")),
            ("discount_percentage", Decimal("100.000000")),
            ("total", Decimal("0.000000")),
            ("last_updated_date", datetime(2025, 3, 26, 20, 26, 54, 64000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "70184a09-c6c9-4599-a435-e2cdbe6eee17#_#19dac7c8-7728-4c08-b221-142c0f3944a6",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "70184a09-c6c9-4599-a435-e2cdbe6eee17"),
            ("pricepoint_id", "43b6b3af-afae-4a90-993f-9ec286a8b1e6"),
            ("product_code", "IMPLEMENTATION"),
            ("product_name", "One-time Implementation"),
            ("product_description", ""),
            ("category", "Services"),
            ("charged_on", "Accounts"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Unknown"),
            ("billing_frequency", "One-time"),
            ("phase_name", "Year 1"),
            ("phase_start_date", datetime(2025, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2026, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 1),
            ("list_price", Decimal("15000.000000")),
            ("net_price", Decimal("10000.000000")),
            ("discount_percentage", Decimal("33.330000")),
            ("total", Decimal("10000.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 32, 37, 590000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "7485619a-17de-452c-9b5b-afe5c5ad178f#_#19dac7c8-7728-4c08-b221-142c0f3944a6",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "7485619a-17de-452c-9b5b-afe5c5ad178f"),
            ("pricepoint_id", "08696bc9-d34e-4a06-969c-a76805536967"),
            ("product_code", "PREMIUMSUPPORT"),
            ("product_name", "Premium White Glove Support"),
            ("product_description", ""),
            ("category", "Services"),
            ("charged_on", "Accounts"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Annual"),
            ("phase_name", "Year 1"),
            ("phase_start_date", datetime(2025, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2026, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 1),
            ("list_price", Decimal("10000.000000")),
            ("net_price", Decimal("5000.000000")),
            ("discount_percentage", Decimal("50.000000")),
            ("total", Decimal("5000.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 32, 52, 163000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "7485619a-17de-452c-9b5b-afe5c5ad178f#_#********-f835-417a-8d70-f978a967f7b7",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "7485619a-17de-452c-9b5b-afe5c5ad178f"),
            ("pricepoint_id", "08696bc9-d34e-4a06-969c-a76805536967"),
            ("product_code", "PREMIUMSUPPORT"),
            ("product_name", "Premium White Glove Support"),
            ("product_description", ""),
            ("category", "Services"),
            ("charged_on", "Accounts"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Annual"),
            ("phase_name", "Year 2"),
            ("phase_start_date", datetime(2026, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2027, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 1),
            ("list_price", Decimal("10000.000000")),
            ("net_price", Decimal("5000.000000")),
            ("discount_percentage", Decimal("50.000000")),
            ("total", Decimal("5000.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 32, 52, 163000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "7485619a-17de-452c-9b5b-afe5c5ad178f#_#ac84ec41-897b-47ab-ac53-9a527b1cb665",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "7485619a-17de-452c-9b5b-afe5c5ad178f"),
            ("pricepoint_id", "08696bc9-d34e-4a06-969c-a76805536967"),
            ("product_code", "PREMIUMSUPPORT"),
            ("product_name", "Premium White Glove Support"),
            ("product_description", ""),
            ("category", "Services"),
            ("charged_on", "Accounts"),
            ("currency", "USD"),
            ("pricing_model", "Unknown"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Annual"),
            ("phase_name", "Year 3"),
            ("phase_start_date", datetime(2027, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2028, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 1),
            ("list_price", Decimal("10000.000000")),
            ("net_price", Decimal("5000.000000")),
            ("discount_percentage", Decimal("50.000000")),
            ("total", Decimal("5000.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 32, 52, 163000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#19dac7c8-7728-4c08-b221-142c0f3944a6",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("pricepoint_id", "1c052871-d377-45d2-b045-7d4f7bb07a33"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("product_description", ""),
            ("category", "Software"),
            ("charged_on", "Users"),
            ("currency", "USD"),
            ("pricing_model", "Tiered"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Monthly"),
            ("phase_name", "Year 1"),
            ("phase_start_date", datetime(2025, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2026, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 200),
            ("list_price", Decimal("38.000000")),
            ("net_price", Decimal("28.000000")),
            ("discount_percentage", Decimal("26.315789")),
            ("total", Decimal("5600.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 33, 33, 270000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#********-f835-417a-8d70-f978a967f7b7",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("pricepoint_id", "1c052871-d377-45d2-b045-7d4f7bb07a33"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("product_description", ""),
            ("category", "Software"),
            ("charged_on", "Users"),
            ("currency", "USD"),
            ("pricing_model", "Tiered"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Monthly"),
            ("phase_name", "Year 2"),
            ("phase_start_date", datetime(2026, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2027, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 200),
            ("list_price", Decimal("38.000000")),
            ("net_price", Decimal("28.000000")),
            ("discount_percentage", Decimal("26.315789")),
            ("total", Decimal("5600.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 33, 33, 270000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "e75bc40c-2885-479b-9c88-7542679e5266"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ac84ec41-897b-47ab-ac53-9a527b1cb665",
            ),
            ("quote_display_id", "ES1110"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("pricepoint_id", "1c052871-d377-45d2-b045-7d4f7bb07a33"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("product_description", ""),
            ("category", "Software"),
            ("charged_on", "Users"),
            ("currency", "USD"),
            ("pricing_model", "Tiered"),
            ("billing_type", "Recurring"),
            ("billing_frequency", "Monthly"),
            ("phase_name", "Year 3"),
            ("phase_start_date", datetime(2027, 5, 1, 0, 0)),
            ("phase_end_date", datetime(2028, 4, 30, 23, 59, 59, 999000)),
            ("quantity", 200),
            ("list_price", Decimal("38.000000")),
            ("net_price", Decimal("28.000000")),
            ("discount_percentage", Decimal("26.315789")),
            ("total", Decimal("5600.000000")),
            ("last_updated_date", datetime(2025, 3, 27, 19, 33, 33, 270000)),
            ("deleted", False),
        ]
    ),
]

QUOTE_LINE_ITEM_TRANSFORMED = [
    '(\'\', \'\', \'{"quote_id": "d19e0129-248e-4362-bcb9-4f6c53604374", "line_item_id": "8fc1e173-aee9-4ecb-989f-9537b07a12b1#_#ed33302a-2c7f-497f-8dba-14563f29e920", "quote_display_id": "ES1109", "product_id": "8fc1e173-aee9-4ecb-989f-9537b07a12b1", "pricepoint_id": "833f5d33-c6ed-4045-8840-cd5e714b216d", "product_code": "CONNECTOR", "product_name": "Connector Charges", "product_description": "", "category": "Software", "charged_on": "Connectors", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Recurring", "billing_frequency": "Monthly", "phase_name": "Year 1", "phase_start_date": "2025-05-05 00:00:00", "phase_end_date": "2026-05-04 23:59:59.999000", "quantity": 2, "list_price": "200.000000", "net_price": "0.000000", "discount_percentage": "100.000000", "total": "0.000000", "last_updated_date": "2025-03-26 20:26:54.064000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "d19e0129-248e-4362-bcb9-4f6c53604374", "line_item_id": "8fc1e173-aee9-4ecb-989f-9537b07a12b1#_#7f50bc76-ad1b-4cb7-bcfe-1f228cf14d64", "quote_display_id": "ES1109", "product_id": "8fc1e173-aee9-4ecb-989f-9537b07a12b1", "pricepoint_id": "833f5d33-c6ed-4045-8840-cd5e714b216d", "product_code": "CONNECTOR", "product_name": "Connector Charges", "product_description": "", "category": "Software", "charged_on": "Connectors", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Recurring", "billing_frequency": "Monthly", "phase_name": "Year 2", "phase_start_date": "2026-05-05 00:00:00", "phase_end_date": "2027-05-04 23:59:59.999000", "quantity": 2, "list_price": "200.000000", "net_price": "0.000000", "discount_percentage": "100.000000", "total": "0.000000", "last_updated_date": "2025-03-26 20:26:54.064000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "d19e0129-248e-4362-bcb9-4f6c53604374", "line_item_id": "8fc1e173-aee9-4ecb-989f-9537b07a12b1#_#3d8ed030-94be-4491-aab4-084cef19cde2", "quote_display_id": "ES1109", "product_id": "8fc1e173-aee9-4ecb-989f-9537b07a12b1", "pricepoint_id": "833f5d33-c6ed-4045-8840-cd5e714b216d", "product_code": "CONNECTOR", "product_name": "Connector Charges", "product_description": "", "category": "Software", "charged_on": "Connectors", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Recurring", "billing_frequency": "Monthly", "phase_name": "Year 3", "phase_start_date": "2027-05-05 00:00:00", "phase_end_date": "2028-05-04 23:59:59.999000", "quantity": 2, "list_price": "200.000000", "net_price": "0.000000", "discount_percentage": "100.000000", "total": "0.000000", "last_updated_date": "2025-03-26 20:26:54.064000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "70184a09-c6c9-4599-a435-e2cdbe6eee17#_#19dac7c8-7728-4c08-b221-142c0f3944a6", "quote_display_id": "ES1110", "product_id": "70184a09-c6c9-4599-a435-e2cdbe6eee17", "pricepoint_id": "43b6b3af-afae-4a90-993f-9ec286a8b1e6", "product_code": "IMPLEMENTATION", "product_name": "One-time Implementation", "product_description": "", "category": "Services", "charged_on": "Accounts", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Unknown", "billing_frequency": "One-time", "phase_name": "Year 1", "phase_start_date": "2025-05-01 00:00:00", "phase_end_date": "2026-04-30 23:59:59.999000", "quantity": 1, "list_price": "15000.000000", "net_price": "10000.000000", "discount_percentage": "33.330000", "total": "10000.000000", "last_updated_date": "2025-03-27 19:32:37.590000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "7485619a-17de-452c-9b5b-afe5c5ad178f#_#19dac7c8-7728-4c08-b221-142c0f3944a6", "quote_display_id": "ES1110", "product_id": "7485619a-17de-452c-9b5b-afe5c5ad178f", "pricepoint_id": "08696bc9-d34e-4a06-969c-a76805536967", "product_code": "PREMIUMSUPPORT", "product_name": "Premium White Glove Support", "product_description": "", "category": "Services", "charged_on": "Accounts", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Recurring", "billing_frequency": "Annual", "phase_name": "Year 1", "phase_start_date": "2025-05-01 00:00:00", "phase_end_date": "2026-04-30 23:59:59.999000", "quantity": 1, "list_price": "10000.000000", "net_price": "5000.000000", "discount_percentage": "50.000000", "total": "5000.000000", "last_updated_date": "2025-03-27 19:32:52.163000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "7485619a-17de-452c-9b5b-afe5c5ad178f#_#********-f835-417a-8d70-f978a967f7b7", "quote_display_id": "ES1110", "product_id": "7485619a-17de-452c-9b5b-afe5c5ad178f", "pricepoint_id": "08696bc9-d34e-4a06-969c-a76805536967", "product_code": "PREMIUMSUPPORT", "product_name": "Premium White Glove Support", "product_description": "", "category": "Services", "charged_on": "Accounts", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Recurring", "billing_frequency": "Annual", "phase_name": "Year 2", "phase_start_date": "2026-05-01 00:00:00", "phase_end_date": "2027-04-30 23:59:59.999000", "quantity": 1, "list_price": "10000.000000", "net_price": "5000.000000", "discount_percentage": "50.000000", "total": "5000.000000", "last_updated_date": "2025-03-27 19:32:52.163000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "7485619a-17de-452c-9b5b-afe5c5ad178f#_#ac84ec41-897b-47ab-ac53-9a527b1cb665", "quote_display_id": "ES1110", "product_id": "7485619a-17de-452c-9b5b-afe5c5ad178f", "pricepoint_id": "08696bc9-d34e-4a06-969c-a76805536967", "product_code": "PREMIUMSUPPORT", "product_name": "Premium White Glove Support", "product_description": "", "category": "Services", "charged_on": "Accounts", "currency": "USD", "pricing_model": "Unknown", "billing_type": "Recurring", "billing_frequency": "Annual", "phase_name": "Year 3", "phase_start_date": "2027-05-01 00:00:00", "phase_end_date": "2028-04-30 23:59:59.999000", "quantity": 1, "list_price": "10000.000000", "net_price": "5000.000000", "discount_percentage": "50.000000", "total": "5000.000000", "last_updated_date": "2025-03-27 19:32:52.163000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#19dac7c8-7728-4c08-b221-142c0f3944a6", "quote_display_id": "ES1110", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "pricepoint_id": "1c052871-d377-45d2-b045-7d4f7bb07a33", "product_code": "PLATFORM", "product_name": "Everstage Platform", "product_description": "", "category": "Software", "charged_on": "Users", "currency": "USD", "pricing_model": "Tiered", "billing_type": "Recurring", "billing_frequency": "Monthly", "phase_name": "Year 1", "phase_start_date": "2025-05-01 00:00:00", "phase_end_date": "2026-04-30 23:59:59.999000", "quantity": 200, "list_price": "38.000000", "net_price": "28.000000", "discount_percentage": "26.315789", "total": "5600.000000", "last_updated_date": "2025-03-27 19:33:33.270000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#********-f835-417a-8d70-f978a967f7b7", "quote_display_id": "ES1110", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "pricepoint_id": "1c052871-d377-45d2-b045-7d4f7bb07a33", "product_code": "PLATFORM", "product_name": "Everstage Platform", "product_description": "", "category": "Software", "charged_on": "Users", "currency": "USD", "pricing_model": "Tiered", "billing_type": "Recurring", "billing_frequency": "Monthly", "phase_name": "Year 2", "phase_start_date": "2026-05-01 00:00:00", "phase_end_date": "2027-04-30 23:59:59.999000", "quantity": 200, "list_price": "38.000000", "net_price": "28.000000", "discount_percentage": "26.315789", "total": "5600.000000", "last_updated_date": "2025-03-27 19:33:33.270000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "e75bc40c-2885-479b-9c88-7542679e5266", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ac84ec41-897b-47ab-ac53-9a527b1cb665", "quote_display_id": "ES1110", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "pricepoint_id": "1c052871-d377-45d2-b045-7d4f7bb07a33", "product_code": "PLATFORM", "product_name": "Everstage Platform", "product_description": "", "category": "Software", "charged_on": "Users", "currency": "USD", "pricing_model": "Tiered", "billing_type": "Recurring", "billing_frequency": "Monthly", "phase_name": "Year 3", "phase_start_date": "2027-05-01 00:00:00", "phase_end_date": "2028-04-30 23:59:59.999000", "quantity": 200, "list_price": "38.000000", "net_price": "28.000000", "discount_percentage": "26.315789", "total": "5600.000000", "last_updated_date": "2025-03-27 19:33:33.270000", "deleted": false}\')',
]

QUOTE_LINE_ITEM_TIER_EXTRACTED = [
    RealDictRow(
        [
            ("quote_id", "283a911a-2faf-498a-b616-4012fe2476c1"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d#_#0",
            ),
            ("quote_display_id", "ES1175"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("0")),
            ("tier_start", Decimal("1")),
            ("tier_end", Decimal("130")),
            ("tier_quantity", Decimal("130")),
            ("tier_list_unit_price", Decimal("38.0")),
            ("tier_net_unit_price", Decimal("27.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("3510.0")),
            ("last_updated_date", datetime(2025, 6, 5, 22, 52, 17, 825000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "283a911a-2faf-498a-b616-4012fe2476c1"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d#_#1",
            ),
            ("quote_display_id", "ES1175"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("1")),
            ("tier_start", Decimal("131")),
            ("tier_end", Decimal("1000000")),
            ("tier_quantity", Decimal("200")),
            ("tier_list_unit_price", Decimal("38.0")),
            ("tier_net_unit_price", Decimal("12.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("2400.0")),
            ("last_updated_date", datetime(2025, 6, 5, 22, 52, 17, 825000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "283a911a-2faf-498a-b616-4012fe2476c1"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495#_#0",
            ),
            ("quote_display_id", "ES1175"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("0")),
            ("tier_start", Decimal("1")),
            ("tier_end", Decimal("130")),
            ("tier_quantity", Decimal("130")),
            ("tier_list_unit_price", Decimal("38.0")),
            ("tier_net_unit_price", Decimal("27.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("3510.0")),
            ("last_updated_date", datetime(2025, 6, 5, 22, 52, 17, 825000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "283a911a-2faf-498a-b616-4012fe2476c1"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495#_#1",
            ),
            ("quote_display_id", "ES1175"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("1")),
            ("tier_start", Decimal("131")),
            ("tier_end", Decimal("1000000")),
            ("tier_quantity", Decimal("200")),
            ("tier_list_unit_price", Decimal("38.0")),
            ("tier_net_unit_price", Decimal("12.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("2400.0")),
            ("last_updated_date", datetime(2025, 6, 5, 22, 52, 17, 825000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "283a911a-2faf-498a-b616-4012fe2476c1"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e#_#0",
            ),
            ("quote_display_id", "ES1175"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("0")),
            ("tier_start", Decimal("1")),
            ("tier_end", Decimal("130")),
            ("tier_quantity", Decimal("130")),
            ("tier_list_unit_price", Decimal("38.0")),
            ("tier_net_unit_price", Decimal("27.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("3510.0")),
            ("last_updated_date", datetime(2025, 6, 5, 22, 52, 17, 825000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "283a911a-2faf-498a-b616-4012fe2476c1"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e#_#1",
            ),
            ("quote_display_id", "ES1175"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("1")),
            ("tier_start", Decimal("131")),
            ("tier_end", Decimal("1000000")),
            ("tier_quantity", Decimal("200")),
            ("tier_list_unit_price", Decimal("38.0")),
            ("tier_net_unit_price", Decimal("12.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("2400.0")),
            ("last_updated_date", datetime(2025, 6, 5, 22, 52, 17, 825000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "bc147d09-3af4-4a00-91c5-1965a43f6dcf"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba#_#0",
            ),
            ("quote_display_id", "ES1176"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("0")),
            ("tier_start", Decimal("1")),
            ("tier_end", Decimal("1000")),
            ("tier_quantity", Decimal("1000")),
            ("tier_list_unit_price", Decimal("33.0")),
            ("tier_net_unit_price", Decimal("6.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("6000.0")),
            ("last_updated_date", datetime(2025, 6, 8, 19, 56, 14, 732000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "bc147d09-3af4-4a00-91c5-1965a43f6dcf"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba#_#1",
            ),
            ("quote_display_id", "ES1176"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("1")),
            ("tier_start", Decimal("1001")),
            ("tier_end", Decimal("2000")),
            ("tier_quantity", Decimal("250")),
            ("tier_list_unit_price", Decimal("33.0")),
            ("tier_net_unit_price", Decimal("5.5")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("1375.0")),
            ("last_updated_date", datetime(2025, 6, 8, 19, 56, 14, 732000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "bc147d09-3af4-4a00-91c5-1965a43f6dcf"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b#_#0",
            ),
            ("quote_display_id", "ES1176"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("0")),
            ("tier_start", Decimal("1")),
            ("tier_end", Decimal("1000")),
            ("tier_quantity", Decimal("1000")),
            ("tier_list_unit_price", Decimal("33.0")),
            ("tier_net_unit_price", Decimal("6.0")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("6000.0")),
            ("last_updated_date", datetime(2025, 6, 8, 19, 56, 14, 732000)),
            ("deleted", False),
        ]
    ),
    RealDictRow(
        [
            ("quote_id", "bc147d09-3af4-4a00-91c5-1965a43f6dcf"),
            (
                "line_item_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b",
            ),
            (
                "tier_id",
                "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b#_#1",
            ),
            ("quote_display_id", "ES1176"),
            ("product_id", "2f25d64d-c825-4dae-a6e7-479a58d11d53"),
            ("product_code", "PLATFORM"),
            ("product_name", "Everstage Platform"),
            ("tier_number", Decimal("1")),
            ("tier_start", Decimal("1001")),
            ("tier_end", Decimal("2000")),
            ("tier_quantity", Decimal("250")),
            ("tier_list_unit_price", Decimal("33.0")),
            ("tier_net_unit_price", Decimal("5.5")),
            ("tier_list_flat_price", Decimal("0.0")),
            ("tier_net_flat_price", Decimal("0.0")),
            ("tier_total", Decimal("1375.0")),
            ("last_updated_date", datetime(2025, 6, 8, 19, 56, 14, 732000)),
            ("deleted", False),
        ]
    ),
]

QUOTE_LINE_ITEM_TIER_TRANSFORMED = [
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d#_#0", "quote_display_id": "ES1175", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "0", "tier_start": "1", "tier_end": "130", "tier_quantity": "130", "tier_list_unit_price": "38.0", "tier_net_unit_price": "27.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "3510.0", "last_updated_date": "2025-06-05 22:52:17.825000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#fb3ada94-6c7e-46da-b6a5-17b997df1f4d#_#1", "quote_display_id": "ES1175", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "1", "tier_start": "131", "tier_end": "1000000", "tier_quantity": "200", "tier_list_unit_price": "38.0", "tier_net_unit_price": "12.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "2400.0", "last_updated_date": "2025-06-05 22:52:17.825000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495#_#0", "quote_display_id": "ES1175", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "0", "tier_start": "1", "tier_end": "130", "tier_quantity": "130", "tier_list_unit_price": "38.0", "tier_net_unit_price": "27.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "3510.0", "last_updated_date": "2025-06-05 22:52:17.825000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#0b37c86a-e03a-4444-889e-138648532495#_#1", "quote_display_id": "ES1175", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "1", "tier_start": "131", "tier_end": "1000000", "tier_quantity": "200", "tier_list_unit_price": "38.0", "tier_net_unit_price": "12.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "2400.0", "last_updated_date": "2025-06-05 22:52:17.825000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e#_#0", "quote_display_id": "ES1175", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "0", "tier_start": "1", "tier_end": "130", "tier_quantity": "130", "tier_list_unit_price": "38.0", "tier_net_unit_price": "27.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "3510.0", "last_updated_date": "2025-06-05 22:52:17.825000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "283a911a-2faf-498a-b616-4012fe2476c1", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#a67e233d-05fc-4dc5-8286-17bc4901d06e#_#1", "quote_display_id": "ES1175", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "1", "tier_start": "131", "tier_end": "1000000", "tier_quantity": "200", "tier_list_unit_price": "38.0", "tier_net_unit_price": "12.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "2400.0", "last_updated_date": "2025-06-05 22:52:17.825000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "bc147d09-3af4-4a00-91c5-1965a43f6dcf", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba#_#0", "quote_display_id": "ES1176", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "0", "tier_start": "1", "tier_end": "1000", "tier_quantity": "1000", "tier_list_unit_price": "33.0", "tier_net_unit_price": "6.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "6000.0", "last_updated_date": "2025-06-08 19:56:14.732000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "bc147d09-3af4-4a00-91c5-1965a43f6dcf", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#9a75db29-8671-4aeb-a138-9b48da47c6ba#_#1", "quote_display_id": "ES1176", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "1", "tier_start": "1001", "tier_end": "2000", "tier_quantity": "250", "tier_list_unit_price": "33.0", "tier_net_unit_price": "5.5", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "1375.0", "last_updated_date": "2025-06-08 19:56:14.732000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "bc147d09-3af4-4a00-91c5-1965a43f6dcf", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b#_#0", "quote_display_id": "ES1176", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "0", "tier_start": "1", "tier_end": "1000", "tier_quantity": "1000", "tier_list_unit_price": "33.0", "tier_net_unit_price": "6.0", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "6000.0", "last_updated_date": "2025-06-08 19:56:14.732000", "deleted": false}\')',
    '(\'\', \'\', \'{"quote_id": "bc147d09-3af4-4a00-91c5-1965a43f6dcf", "line_item_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b", "tier_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53#_#ab323043-3d9e-46b5-9b7b-ed521015e37b#_#1", "quote_display_id": "ES1176", "product_id": "2f25d64d-c825-4dae-a6e7-479a58d11d53", "product_code": "PLATFORM", "product_name": "Everstage Platform", "tier_number": "1", "tier_start": "1001", "tier_end": "2000", "tier_quantity": "250", "tier_list_unit_price": "33.0", "tier_net_unit_price": "5.5", "tier_list_flat_price": "0.0", "tier_net_flat_price": "0.0", "tier_total": "1375.0", "last_updated_date": "2025-06-08 19:56:14.732000", "deleted": false}\')',
]
