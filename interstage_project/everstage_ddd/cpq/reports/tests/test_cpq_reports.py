# ruff: noqa: SLF001,PLR0913
from datetime import datetime
from typing import Union
from unittest.mock import patch
from uuid import uuid4

from psycopg2.extras import RealDictRow
from pytest import mark
from pytz import utc

from everstage_ddd.snowflake_reports.snowflake_report_utils import ReportSyncMode

from ..strategies.quote import QuoteReportStrategy as QuoteReport
from ..strategies.quote_line_item import (
    QuoteLineItemReportStrategy as Quote<PERSON><PERSON>ItemReport,
)
from ..strategies.quote_line_item_tier import (
    QuoteLineItemTierReportStrategy as QuoteLineItemTierReport,
)
from ..utils import get_postgres_connection
from .data import (
    QUOTE_EXTRACTED,
    QUOTE_LINE_ITEM_EXTRACTED,
    QUOTE_LINE_ITEM_TIER_EXTRACTED,
    QUOTE_LINE_ITEM_TIER_TRANSFORMED,
    QUOTE_LINE_ITEM_TRANSFORMED,
    QUOTE_TRANSFORMED,
)

ReportType = Union[
    type[QuoteReport], type[QuoteLineItemReport], type[QuoteLineItemTierReport]
]


@mark.cpq_reports
@mark.django_db
class TestCPQReports:
    @mark.parametrize(
        "report, client_id, limit, offset, extracted_expected, transformed_expected",
        [
            (
                QuoteReport,
                10042,
                10,
                150,
                QUOTE_EXTRACTED,
                QUOTE_TRANSFORMED,
            ),
            (
                QuoteLineItemReport,
                10042,
                10,
                600,
                QUOTE_LINE_ITEM_EXTRACTED,
                QUOTE_LINE_ITEM_TRANSFORMED,
            ),
            (
                QuoteLineItemTierReport,
                10042,
                10,
                0,
                QUOTE_LINE_ITEM_TIER_EXTRACTED,
                QUOTE_LINE_ITEM_TIER_TRANSFORMED,
            ),
        ],
    )
    def test_report_etl(
        self,
        report: ReportType,
        client_id: int,
        limit: int,
        offset: int,
        extracted_expected: list[RealDictRow],
        transformed_expected: list[str],
    ) -> None:
        # Create instance
        strategy = report(
            client_id=client_id,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            sync_mode=ReportSyncMode.ALL,
            knowledge_date=datetime(2025, 6, 30, tzinfo=utc),
            log_context={},
        )

        # Create connection
        strategy.pg_cursor = get_postgres_connection(client_id)[1]

        # Test extraction
        extracted = strategy._extract_data(limit, offset)
        assert len(extracted) == len(extracted_expected)
        assert extracted == extracted_expected

        # Test transformation
        transformed = strategy._transform_data(extracted)
        assert len(transformed) == len(transformed_expected)
        assert transformed == transformed_expected

        # Test loading
        with (
            patch.object(
                strategy, "_invalidate_data", return_value=0
            ) as invalidate_data,
            patch.object(
                strategy, "_insert_data", return_value=len(transformed)
            ) as insert_data,
        ):
            strategy._load_data(transformed)
            invalidate_data.assert_called_once()
            insert_data.assert_called_once()

        # Close connection
        strategy.pg_cursor.close()
