from datetime import datetime
from unittest.mock import patch

from pytest import mark
from pytz import utc

from ...forms import EverstageFormAccessor
from ...quote import Quote<PERSON>ineItemAccessor, QuoteSelector, QuoteStatusSelector
from .report_etl import QRGenerator
from .sample_data import (  # Test cases for pricing calculations; Test cases for quote selection
    TC_1_ARR_DICTS,
    TC_1_QUOTE_FORM_DATA,
    TC_1_QUOTE_LINE_ITEMS,
    TC_1_QUOTE_REPORT,
    TC_1_QUOTE_STATUSES,
    TC_1_QUOTES,
    TC_2_ARR_DICTS,
    TC_2_QUOTE_FORM_DATA,
    TC_2_QUOTE_LINE_ITEMS,
    TC_2_QUOTE_REPORT,
    TC_2_QUOTE_STATUSES,
    TC_2_QUOTES,
)


@mark.cpq_reports
class TestQRGenerator:
    @mark.django_db
    @mark.parametrize(
        "client_id, start_ts, end_ts, quotes, quote_statuses, quote_form_data, quote_line_items, arr_dicts, expected_report",
        [
            (
                10031,
                datetime(2024, 12, 8, tzinfo=utc),
                datetime(2024, 12, 10, tzinfo=utc),
                TC_1_QUOTES,
                TC_1_QUOTE_STATUSES,
                TC_1_QUOTE_FORM_DATA,
                TC_1_QUOTE_LINE_ITEMS,
                TC_1_ARR_DICTS,
                TC_1_QUOTE_REPORT,
            ),
            (
                31617,
                datetime(2025, 2, 12, tzinfo=utc),
                datetime(2025, 2, 14, tzinfo=utc),
                TC_2_QUOTES,
                TC_2_QUOTE_STATUSES,
                TC_2_QUOTE_FORM_DATA,
                TC_2_QUOTE_LINE_ITEMS,
                TC_2_ARR_DICTS,
                TC_2_QUOTE_REPORT,
            ),
        ],
    )
    def test_run(  # noqa: PLR0913
        self,
        client_id,
        start_ts,
        end_ts,
        quotes,
        quote_statuses,
        quote_form_data,
        quote_line_items,
        arr_dicts,
        expected_report,
    ) -> None:
        with patch.object(
            QuoteSelector, "get_quotes_between"
        ) as mock_get_quotes, patch.object(
            QuoteStatusSelector, "get_quote_statuses"
        ) as mock_get_quote_statuses, patch.object(
            EverstageFormAccessor, "get_form_data"
        ) as mock_get_form_data, patch.object(
            QuoteLineItemAccessor, "get_quote_line_items"
        ) as mock_get_qli, patch(
            "everstage_ddd.cpq.reports.custom.report_etl.calculate_year_wise_arr"
        ) as mock_calculate_arr:
            mock_get_quotes.return_value = quotes
            mock_get_quote_statuses.return_value = quote_statuses
            mock_get_form_data.return_value = quote_form_data
            mock_get_qli.return_value = quote_line_items
            mock_calculate_arr.side_effect = arr_dicts
            actual_report_df = QRGenerator(client_id).run(start_ts, end_ts)
            actual_report = actual_report_df.to_dict(orient="records")

        assert len(actual_report) == len(expected_report)  # nosec # noqa: S101
        assert actual_report == expected_report  # nosec # noqa: S101
