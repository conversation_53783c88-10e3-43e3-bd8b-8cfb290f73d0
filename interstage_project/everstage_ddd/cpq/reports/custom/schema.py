from typing import Optional

from pydantic import BaseModel


class CustomReport(BaseModel):
    # Deal Fields
    hs_object_id: str

    # Additional Fields
    quote_id: Optional[str]
    quote_name: Optional[str]
    opportunity: Optional[str]
    account: Optional[str]

    # Quote Fields
    subscription_start_date: Optional[str]
    subscription_end_date: Optional[str]
    contract_period: Optional[str]
    deal_credit_terms: Optional[int]
    integrations: Optional[str]
    data_centre_requirements: Optional[str]
    dpa_signed_: Optional[bool]
    offline_msa_signed_: Optional[bool]
    autorenwal_: Optional[bool]

    # Quote Line Item Fields
    no_of_payees: Optional[int]
    implementation_charges: Optional[float]
    tiered_pricing_offered__includes_platform_fee_based_pricing__: Optional[bool]

    # Calculated Revenue Fields
    amount: float
    year_2_arr: float
    year_3_arr: float
    white_glove_support_charges: float
    white_glove_support_charges_year_2: float
    white_glove_support_charges_year_3: float
    connector_charges: float
    connector_charges_year_2: float
    connector_charges_year_3: float
    asc606_charges: float
    asc606_charges_year_2: float
    asc606_charges_year_3: float
