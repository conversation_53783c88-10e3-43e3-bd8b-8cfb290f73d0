from collections import defaultdict
from datetime import datetime
from uuid import UUID

from pytz import utc

TC_1_QUOTES = [
    {
        "client_id": 10031,
        "temporal_id": 584,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 41, 27, 647833, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("2619a5b8-ef4e-4d14-8cba-64e5a422e624"),
        "quote_display_id": 1002,
        "quote_name": "IDG",
        "quote_currency": "USD",
        "form_id": UUID("96a72213-6a01-4ec3-891c-68876b1022a0"),
        "valid_till": datetime(2025, 1, 8, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": True,
        "start_date": datetime(2024, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 36,
        "duration_type": "months",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 3848600.0,
        "net_quote_total": 923400.0,
        "discount_amount": 2925200.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 893400.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2024, 12, 9, 13, 26, 9, 146139, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 10031,
        "temporal_id": 577,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 53, 463302, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("072263e0-5538-4ef5-bae1-f8acd82b17a3"),
        "quote_display_id": 1001,
        "quote_name": "Bazaarvoice",
        "quote_currency": "USD",
        "form_id": UUID("21bace2f-8e0f-4f77-9cff-a487999267bb"),
        "valid_till": datetime(2025, 1, 8, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": True,
        "start_date": datetime(2024, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 2,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 531600.0,
        "net_quote_total": 184399.998804,
        "discount_amount": 347200.001196,
        "closed_date": None,
        "custom_data": {"recurring_total": 174399.998804},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2024, 12, 9, 13, 17, 31, 141314, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 10031,
        "temporal_id": 585,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 56, 53, 978166, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("e4743cdc-c2ec-431d-8959-92c3a04dc900"),
        "quote_display_id": 1003,
        "quote_name": "Progress",
        "quote_currency": "USD",
        "form_id": UUID("1d99e20d-2cbd-452b-aabe-5e583cfccbe4"),
        "valid_till": datetime(2025, 1, 8, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": True,
        "start_date": datetime(2024, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 3,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 1038800.0,
        "net_quote_total": 558000.0002,
        "discount_amount": 480799.9998,
        "closed_date": None,
        "custom_data": {"recurring_total": 543000.00015},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2024, 12, 9, 13, 38, 52, 279282, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
]

TC_1_QUOTE_STATUSES = [
    {
        "client_id": 10031,
        "temporal_id": 229,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 41, 27, 647833, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("0b26c4dd-5b8b-4644-ae1d-21ee62820f7a"),
        "quote_id": UUID("2619a5b8-ef4e-4d14-8cba-64e5a422e624"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2024, 12, 9, 13, 26, 9, 146139, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 10031,
        "temporal_id": 230,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 56, 53, 978166, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("66ac64e4-3915-4f1c-9430-9aa9b66613d0"),
        "quote_id": UUID("e4743cdc-c2ec-431d-8959-92c3a04dc900"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2024, 12, 9, 13, 38, 52, 279282, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 10031,
        "temporal_id": 226,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 53, 463302, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("05c217f1-3d60-45fa-88d5-be33a302babe"),
        "quote_id": UUID("072263e0-5538-4ef5-bae1-f8acd82b17a3"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2024, 12, 9, 13, 17, 31, 141314, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
]

TC_1_QUOTE_FORM_DATA = [
    {
        "field1": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "field2": "Progress",
        "field3": "ubs - Sarine Wressell",
        "field4": "***********",
        "field5": "ubs",
        "field6": "***********",
        "field7": "Sarine Wressell",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-01-08T00:00:00.000Z",
        "field12": "New Business",
        "field13": "US Data Center",
        "field14": "Indonesia",
        "field15": "Zürich",
        "field16": "Cigedang",
        "field17": "8",
        "field18": "Vermont Place",
        "field19": "8001",
        "field20": "8,Vermont Place,Cigedang,Zürich,Indonesia",
        "field21": False,
        "field22": None,
        "field23": "Include (Limit)",
        "field24": None,
        "field25": 3,
        "field26": "Salesforce, Data Warehouse",
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": "Annual Upfront",
        "field31": "Wire Transfer to Everstage",
        "field32": "30",
        "field33": "MSA",
        "field34": "Yes",
        "field35": "The order form includes charges for the following connectors:Salesforce, Data Warehouse",
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": ", the Data Processing Agreement",
        "field52": "<EMAIL>",
    },
    {
        "field1": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "field2": "Bazaarvoice",
        "field3": "walgreens - Caz Normavill",
        "field4": "***********",
        "field5": "walgreens",
        "field6": "***********",
        "field7": "Caz Normavill",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-01-08T00:00:00.000Z",
        "field12": "New Business",
        "field13": "US Data Center",
        "field14": "Norway",
        "field15": "Oslo",
        "field16": "Oslo",
        "field17": "8 Sunfield Pass",
        "field18": "Sunfield Pass",
        "field19": "0669",
        "field20": "8 Sunfield Pass,Sunfield Pass,Oslo,Oslo,Norway",
        "field21": False,
        "field22": None,
        "field23": "Include (Limit)",
        "field24": None,
        "field25": 3,
        "field26": "Salesforce, Netsuite, Snowflake, and Workday",
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": "Annual Upfront",
        "field31": "Wire Transfer to Everstage",
        "field32": "30",
        "field33": "Online Terms (Website)",
        "field34": "Yes",
        "field35": "The order form includes charges for the following connectors:Salesforce, Netsuite, Snowflake, and Workday",
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": ", the Data Processing Agreement",
        "field52": "<EMAIL>",
    },
    {
        "field1": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "field2": "IDG",
        "field3": "starbucks - Nona Trass",
        "field4": "***********",
        "field5": "starbucks",
        "field6": "***********",
        "field7": "Nona Trass",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-01-08T00:00:00.000Z",
        "field12": "New Business",
        "field13": "US Data Center",
        "field14": "China",
        "field15": "WA",
        "field16": "Liuzu",
        "field17": "13",
        "field18": "Bobwhite Alley",
        "field19": "98134",
        "field20": "13,Bobwhite Alley,Liuzu,WA,China",
        "field21": False,
        "field22": None,
        "field23": "Include (Limit)",
        "field24": None,
        "field25": 3,
        "field26": "Snowflake, Salesforce, and Ultipro",
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": "Annual Upfront",
        "field31": "Wire Transfer to Everstage",
        "field32": "30",
        "field33": "Online Terms (Website)",
        "field34": "Yes",
        "field35": "The order form includes charges for the following connectors:Snowflake, Salesforce, and Ultipro",
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": ", the Data Processing Agreement",
        "field52": "<EMAIL>",
    },
]

TC_1_QUOTE_LINE_ITEMS = [
    {
        "client_id": 10031,
        "temporal_id": 986,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 50, 77405, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "CONNECTOR#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 3,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("600.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("600.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 980,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 47, 733022, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "PLATFORM#_#97489a63-1d18-4abf-9275-e05e309f8d83",
        "start_date": datetime(2025, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "97489a63-1d18-4abf-9275-e05e309f8d83",
        "phase_name": "Year II",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 450,
        "discount_percent": float("65.789474"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("13.000000"),
        "list_total": float("17100.000000"),
        "net_total": float("5849.999946"),
        "prorated_list_total": float("205200.000000"),
        "prorated_net_total": float("70199.999352"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 981,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 47, 733022, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "PREMIUMSUPPORT#_#97489a63-1d18-4abf-9275-e05e309f8d83",
        "start_date": datetime(2025, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "97489a63-1d18-4abf-9275-e05e309f8d83",
        "phase_name": "Year II",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("60.000000"),
        "list_unit_price": float("25000.000000"),
        "net_unit_price": float("10000.000000"),
        "list_total": float("25000.000000"),
        "net_total": float("10000.000000"),
        "prorated_list_total": float("25000.000000"),
        "prorated_net_total": float("10000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 25000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 982,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 47, 733022, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "CONNECTOR#_#97489a63-1d18-4abf-9275-e05e309f8d83",
        "start_date": datetime(2025, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "97489a63-1d18-4abf-9275-e05e309f8d83",
        "phase_name": "Year II",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 3,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("600.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("600.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 983,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 47, 733022, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "ASC606#_#97489a63-1d18-4abf-9275-e05e309f8d83",
        "start_date": datetime(2025, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "97489a63-1d18-4abf-9275-e05e309f8d83",
        "phase_name": "Year II",
        "product_name": "ASC606 Module",
        "sku": "ASC606",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("53.333333"),
        "list_unit_price": float("15000.000000"),
        "net_unit_price": float("7000.000050"),
        "list_total": float("15000.000000"),
        "net_total": float("7000.000050"),
        "prorated_list_total": float("15000.000000"),
        "prorated_net_total": float("7000.000050"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "ASC606",
            "label": "USD - Flat Fee - Annual",
            "row_key": "801",
            "currency": "USD",
            "tier_data": None,
            "list_price": 15000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 4,
        "unit_of_measure": "Modules",
    },
    {
        "client_id": 10031,
        "temporal_id": 984,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 50, 77405, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "PLATFORM#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 450,
        "discount_percent": float("65.789474"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("13.000000"),
        "list_total": float("17100.000000"),
        "net_total": float("5849.999946"),
        "prorated_list_total": float("205200.000000"),
        "prorated_net_total": float("70199.999352"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 985,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 50, 77405, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "PREMIUMSUPPORT#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("60.000000"),
        "list_unit_price": float("25000.000000"),
        "net_unit_price": float("10000.000000"),
        "list_total": float("25000.000000"),
        "net_total": float("10000.000000"),
        "prorated_list_total": float("25000.000000"),
        "prorated_net_total": float("10000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 25000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 988,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 50, 77405, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "ASC606#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "ASC606 Module",
        "sku": "ASC606",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("53.333333"),
        "list_unit_price": float("15000.000000"),
        "net_unit_price": float("7000.000050"),
        "list_total": float("15000.000000"),
        "net_total": float("7000.000050"),
        "prorated_list_total": float("15000.000000"),
        "prorated_net_total": float("7000.000050"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "ASC606",
            "label": "USD - Flat Fee - Annual",
            "row_key": "801",
            "currency": "USD",
            "tier_data": None,
            "list_price": 15000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 4,
        "unit_of_measure": "Modules",
    },
    {
        "client_id": 10031,
        "temporal_id": 987,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 30, 50, 77405, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_line_item_id": "IMPLEMENTATION#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 7, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 6, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "One-time Implementation",
        "sku": "IMPLEMENTATION",
        "billing_frequency": "one-time",
        "billing_type": "one-time",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("75.000000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("10000.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("10000.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("10000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "IMPLEMENTATION",
            "label": "USD - Flat Fee - One-time",
            "row_key": "701",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": 100,
            "pricing_method": "Flat Fee",
            "billing_frequency": "one-time",
        },
        "order": 3,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1067,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 37, 27, 97614, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "PLATFORM#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 2600,
        "discount_percent": float("75.303644"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("9.384615"),
        "list_total": float("98800.000000"),
        "net_total": float("24400.000000"),
        "prorated_list_total": float("1185600.000000"),
        "prorated_net_total": float("292800.000000"),
        "custom_data": {
            "tier_allocations": {
                "0": {
                    "id": "8e469fcc-c4ee-4a2b-9432-60b287d976e7",
                    "name": "Tier 1",
                    "quantity": 850,
                    "net_total": 3400.0,
                    "list_total": 32300.0,
                    "lower_bound": 1,
                    "upper_bound": 850,
                    "net_flat_price": 0.0,
                    "net_unit_price": 4.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 40800.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 387600.0,
                    "unit_price_discount": 89.47,
                },
                "1": {
                    "id": "31a0a9be-2dbd-4787-9273-8114a8105c77",
                    "name": "Tier 2",
                    "quantity": 1750,
                    "net_total": 21000.0,
                    "list_total": 66500.0,
                    "lower_bound": 851,
                    "upper_bound": 2600,
                    "net_flat_price": 0.0,
                    "net_unit_price": 12.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 252000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 798000.0,
                    "unit_price_discount": 68.42,
                },
            }
        },
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Tiered - Monthly",
            "row_key": "301",
            "currency": "USD",
            "tier_data": [
                {
                    "id": "8e469fcc-c4ee-4a2b-9432-60b287d976e7",
                    "tier_name": "Tier 1",
                    "lower_bound": 1,
                    "upper_bound": 850,
                    "net_flat_price": 0,
                    "net_unit_price": 4,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 89.47,
                },
                {
                    "id": "31a0a9be-2dbd-4787-9273-8114a8105c77",
                    "tier_name": "Tier 2",
                    "lower_bound": 851,
                    "upper_bound": 2600,
                    "net_flat_price": 0,
                    "net_unit_price": 12,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 68.42,
                },
                {
                    "id": "0802c83d-0be3-4be8-9a7a-08554151549e",
                    "tier_name": "Tier 3",
                    "lower_bound": 2601,
                    "upper_bound": None,
                    "net_flat_price": 0,
                    "net_unit_price": 38,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 0,
                },
            ],
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Tiered",
            "billing_frequency": "monthly",
            "show_future_tiers": False,
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 1068,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 38, 0, 52720, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "PLATFORM#_#5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "start_date": datetime(2025, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "phase_name": "Year II",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 2600,
        "discount_percent": float("75.303644"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("9.384615"),
        "list_total": float("98800.000000"),
        "net_total": float("24400.000000"),
        "prorated_list_total": float("1185600.000000"),
        "prorated_net_total": float("292800.000000"),
        "custom_data": {
            "tier_allocations": {
                "0": {
                    "id": "0c00fe29-b09e-4dbc-91ca-410cf551f798",
                    "name": "Tier 1",
                    "quantity": 850,
                    "net_total": 3400.0,
                    "list_total": 32300.0,
                    "lower_bound": 1,
                    "upper_bound": 850,
                    "net_flat_price": 0.0,
                    "net_unit_price": 4.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 40800.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 387600.0,
                    "unit_price_discount": 89.47,
                },
                "1": {
                    "id": "ef579c4e-9998-4d8f-869b-6acc4115cfe2",
                    "name": "Tier 2",
                    "quantity": 1750,
                    "net_total": 21000.0,
                    "list_total": 66500.0,
                    "lower_bound": 851,
                    "upper_bound": 2600,
                    "net_flat_price": 0.0,
                    "net_unit_price": 12.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 252000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 798000.0,
                    "unit_price_discount": 68.42,
                },
            }
        },
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Tiered - Monthly",
            "row_key": "301",
            "currency": "USD",
            "tier_data": [
                {
                    "id": "0c00fe29-b09e-4dbc-91ca-410cf551f798",
                    "tier_name": "Tier 1",
                    "lower_bound": 1,
                    "upper_bound": 850,
                    "net_flat_price": 0,
                    "net_unit_price": 4,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 89.47,
                },
                {
                    "id": "ef579c4e-9998-4d8f-869b-6acc4115cfe2",
                    "tier_name": "Tier 2",
                    "lower_bound": 851,
                    "upper_bound": 2600,
                    "net_flat_price": 0,
                    "net_unit_price": 12,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 68.42,
                },
                {
                    "id": "28425fbf-9303-4e59-9de9-6a7222436901",
                    "tier_name": "Tier 3",
                    "lower_bound": 2601,
                    "upper_bound": None,
                    "net_flat_price": 0,
                    "net_unit_price": 38,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 0,
                },
            ],
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Tiered",
            "billing_frequency": "monthly",
            "show_future_tiers": False,
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 1038,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 25, 270538, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "CONNECTOR#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 3,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("600.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("600.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 1039,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 25, 270538, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "IMPLEMENTATION#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "One-time Implementation",
        "sku": "IMPLEMENTATION",
        "billing_frequency": "one-time",
        "billing_type": "one-time",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("40.000000"),
        "list_unit_price": float("50000.000000"),
        "net_unit_price": float("30000.000000"),
        "list_total": float("50000.000000"),
        "net_total": float("30000.000000"),
        "prorated_list_total": float("50000.000000"),
        "prorated_net_total": float("30000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "IMPLEMENTATION",
            "label": "USD - Flat Fee - One-time",
            "row_key": "701",
            "currency": "USD",
            "tier_data": None,
            "list_price": 50000,
            "max_discount": 100,
            "pricing_method": "Flat Fee",
            "billing_frequency": "one-time",
        },
        "order": 3,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1040,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 25, 270538, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "ASC606#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "ASC606 Module",
        "sku": "ASC606",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "ASC606",
            "label": "USD - Flat Fee - Annual",
            "row_key": "801",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 4,
        "unit_of_measure": "Modules",
    },
    {
        "client_id": 10031,
        "temporal_id": 1037,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 25, 270538, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "PREMIUMSUPPORT#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("87.500000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("5000.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("5000.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("5000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1054,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 42, 58536, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "PREMIUMSUPPORT#_#5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "start_date": datetime(2025, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "phase_name": "Year II",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("87.500000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("5000.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("5000.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("5000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1055,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 42, 58536, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "CONNECTOR#_#5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "start_date": datetime(2025, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "phase_name": "Year II",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 3,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("600.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("600.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 1056,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 32, 42, 58536, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "ASC606#_#5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "start_date": datetime(2025, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "5e946c56-35be-4bbc-8860-bd9390c50c3d",
        "phase_name": "Year II",
        "product_name": "ASC606 Module",
        "sku": "ASC606",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "ASC606",
            "label": "USD - Flat Fee - Annual",
            "row_key": "801",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 4,
        "unit_of_measure": "Modules",
    },
    {
        "client_id": 10031,
        "temporal_id": 1096,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 41, 22, 985890, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "PLATFORM#_#269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "start_date": datetime(2026, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "phase_name": "Year III",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 2600,
        "discount_percent": float("75.303644"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("9.384615"),
        "list_total": float("98800.000000"),
        "net_total": float("24400.000000"),
        "prorated_list_total": float("1185600.000000"),
        "prorated_net_total": float("292800.000000"),
        "custom_data": {
            "tier_allocations": {
                "0": {
                    "id": "e13a3a40-5dd3-4fcc-a99d-b28ecfa6ec7b",
                    "name": "Tier 1",
                    "quantity": 850,
                    "net_total": 3400.0,
                    "list_total": 32300.0,
                    "lower_bound": 1,
                    "upper_bound": 850,
                    "net_flat_price": 0.0,
                    "net_unit_price": 4.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 40800.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 387600.0,
                    "unit_price_discount": 89.47,
                },
                "1": {
                    "id": "be3b12f3-d070-4f03-8d18-7ca86805794a",
                    "name": "Tier 2",
                    "quantity": 1750,
                    "net_total": 21000.0,
                    "list_total": 66500.0,
                    "lower_bound": 851,
                    "upper_bound": 2600,
                    "net_flat_price": 0.0,
                    "net_unit_price": 12.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 252000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 798000.0,
                    "unit_price_discount": 68.42,
                },
            }
        },
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Tiered - Monthly",
            "row_key": "301",
            "currency": "USD",
            "tier_data": [
                {
                    "id": "e13a3a40-5dd3-4fcc-a99d-b28ecfa6ec7b",
                    "tier_name": "Tier 1",
                    "lower_bound": 1,
                    "upper_bound": 850,
                    "net_flat_price": 0,
                    "net_unit_price": 4,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 89.47,
                },
                {
                    "id": "be3b12f3-d070-4f03-8d18-7ca86805794a",
                    "tier_name": "Tier 2",
                    "lower_bound": 851,
                    "upper_bound": 2600,
                    "net_flat_price": 0,
                    "net_unit_price": 12,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 68.42,
                },
                {
                    "id": "1fd62f53-b049-469f-971d-334a05a11189",
                    "tier_name": "Tier 3",
                    "lower_bound": 2601,
                    "upper_bound": None,
                    "net_flat_price": 0,
                    "net_unit_price": 38,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 0,
                },
            ],
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Tiered",
            "billing_frequency": "monthly",
            "show_future_tiers": False,
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 1097,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 41, 22, 985890, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "PREMIUMSUPPORT#_#269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "start_date": datetime(2026, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "phase_name": "Year III",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("87.500000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("5000.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("5000.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("5000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1098,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 41, 22, 985890, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "CONNECTOR#_#269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "start_date": datetime(2026, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "phase_name": "Year III",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 3,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("600.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("600.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 1099,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 41, 22, 985890, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_line_item_id": "ASC606#_#269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "start_date": datetime(2026, 9, 30, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 9, 29, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "269dbfcf-e55f-4198-bab1-c65bcbd76ffb",
        "phase_name": "Year III",
        "product_name": "ASC606 Module",
        "sku": "ASC606",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("40000.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("40000.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("40000.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "ASC606",
            "label": "USD - Flat Fee - Annual",
            "row_key": "801",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 4,
        "unit_of_measure": "Modules",
    },
    {
        "client_id": 10031,
        "temporal_id": 1147,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 40, 54395, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "IMPLEMENTATION#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "One-time Implementation",
        "sku": "IMPLEMENTATION",
        "billing_frequency": "one-time",
        "billing_type": "one-time",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("57.142857"),
        "list_unit_price": float("35000.000000"),
        "net_unit_price": float("15000.000050"),
        "list_total": float("35000.000000"),
        "net_total": float("15000.000050"),
        "prorated_list_total": float("35000.000000"),
        "prorated_net_total": float("15000.000050"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "IMPLEMENTATION",
            "label": "USD - Flat Fee - One-time",
            "row_key": "701",
            "currency": "USD",
            "tier_data": None,
            "list_price": 35000,
            "max_discount": 100,
            "pricing_method": "Flat Fee",
            "billing_frequency": "one-time",
        },
        "order": 3,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1157,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 56, 28, 497287, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "CONNECTOR#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 2,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("400.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("400.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 1158,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 56, 37, 987151, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "CONNECTOR#_#71f98532-a031-4649-8663-305a6ba4f3eb",
        "start_date": datetime(2025, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "71f98532-a031-4649-8663-305a6ba4f3eb",
        "phase_name": "Year II",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 2,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("400.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("400.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 1159,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 56, 44, 713416, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "CONNECTOR#_#b39d2e63-3e0a-4464-b052-e2a7a7b8970c",
        "start_date": datetime(2026, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b39d2e63-3e0a-4464-b052-e2a7a7b8970c",
        "phase_name": "Year III",
        "product_name": "Connector Charges",
        "sku": "CONNECTOR",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 2,
        "discount_percent": float("100.000000"),
        "list_unit_price": float("200.000000"),
        "net_unit_price": float("0.000000"),
        "list_total": float("400.000000"),
        "net_total": float("0.000000"),
        "prorated_list_total": float("400.000000"),
        "prorated_net_total": float("0.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "CONNECTOR",
            "label": "USD - Per Unit - Annual",
            "row_key": "601",
            "currency": "USD",
            "tier_data": None,
            "list_price": 200,
            "max_discount": 100,
            "pricing_method": "Per Unit",
            "billing_frequency": "annual",
        },
        "order": 2,
        "unit_of_measure": "Connectors",
    },
    {
        "client_id": 10031,
        "temporal_id": 1144,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 40, 54395, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "PLATFORM#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 700,
        "discount_percent": float("45.488722"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("20.714286"),
        "list_total": float("26600.000000"),
        "net_total": float("14500.000000"),
        "prorated_list_total": float("319200.000000"),
        "prorated_net_total": float("174000.000000"),
        "custom_data": {
            "tier_allocations": {
                "0": {
                    "id": "cdec5f65-6cec-42cc-8e29-ebc027578d12",
                    "name": "Tier 1",
                    "quantity": 500,
                    "net_total": 10500.0,
                    "list_total": 19000.0,
                    "lower_bound": 1,
                    "upper_bound": 500,
                    "net_flat_price": 0.0,
                    "net_unit_price": 21.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 126000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 228000.0,
                    "unit_price_discount": 44.74,
                },
                "1": {
                    "id": "d9e3b8ed-a981-4534-9bdc-de8a95b20804",
                    "name": "Tier 2",
                    "quantity": 200,
                    "net_total": 4000.0,
                    "list_total": 7600.0,
                    "lower_bound": 501,
                    "upper_bound": 700,
                    "net_flat_price": 0.0,
                    "net_unit_price": 20.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 48000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 91200.0,
                    "unit_price_discount": 47.37,
                },
            }
        },
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Tiered - Monthly",
            "row_key": "301",
            "currency": "USD",
            "tier_data": [
                {
                    "id": "cdec5f65-6cec-42cc-8e29-ebc027578d12",
                    "tier_name": "Tier 1",
                    "lower_bound": 1,
                    "upper_bound": 500,
                    "net_flat_price": 0,
                    "net_unit_price": 21,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 44.74,
                },
                {
                    "id": "d9e3b8ed-a981-4534-9bdc-de8a95b20804",
                    "tier_name": "Tier 2",
                    "lower_bound": 501,
                    "upper_bound": 700,
                    "net_flat_price": 0,
                    "net_unit_price": 20,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 47.37,
                },
                {
                    "id": "a24dcd96-bb27-4d10-8d45-2b546af93593",
                    "tier_name": "Tier 3",
                    "lower_bound": 701,
                    "upper_bound": None,
                    "net_flat_price": 0,
                    "net_unit_price": 19,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 50,
                },
            ],
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Tiered",
            "billing_frequency": "monthly",
            "show_future_tiers": True,
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 1145,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 40, 54395, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "PREMIUMSUPPORT#_#b771d122-7b42-443e-b2d0-c6d83cb41614",
        "start_date": datetime(2024, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2025, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b771d122-7b42-443e-b2d0-c6d83cb41614",
        "phase_name": "Year I",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("53.333333"),
        "list_unit_price": float("15000.000000"),
        "net_unit_price": float("7000.000050"),
        "list_total": float("15000.000000"),
        "net_total": float("7000.000050"),
        "prorated_list_total": float("15000.000000"),
        "prorated_net_total": float("7000.000050"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 15000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1151,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 44, 457165, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "PLATFORM#_#71f98532-a031-4649-8663-305a6ba4f3eb",
        "start_date": datetime(2025, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "71f98532-a031-4649-8663-305a6ba4f3eb",
        "phase_name": "Year II",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 700,
        "discount_percent": float("45.488722"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("20.714286"),
        "list_total": float("26600.000000"),
        "net_total": float("14500.000000"),
        "prorated_list_total": float("319200.000000"),
        "prorated_net_total": float("174000.000000"),
        "custom_data": {
            "tier_allocations": {
                "0": {
                    "id": "cdec5f65-6cec-42cc-8e29-ebc027578d12",
                    "name": "Tier 1",
                    "quantity": 500,
                    "net_total": 10500.0,
                    "list_total": 19000.0,
                    "lower_bound": 1,
                    "upper_bound": 500,
                    "net_flat_price": 0.0,
                    "net_unit_price": 21.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 126000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 228000.0,
                    "unit_price_discount": 44.74,
                },
                "1": {
                    "id": "d9e3b8ed-a981-4534-9bdc-de8a95b20804",
                    "name": "Tier 2",
                    "quantity": 200,
                    "net_total": 4000.0,
                    "list_total": 7600.0,
                    "lower_bound": 501,
                    "upper_bound": 700,
                    "net_flat_price": 0.0,
                    "net_unit_price": 20.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 48000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 91200.0,
                    "unit_price_discount": 47.37,
                },
            }
        },
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Tiered - Monthly",
            "row_key": "301",
            "currency": "USD",
            "tier_data": [
                {
                    "id": "cdec5f65-6cec-42cc-8e29-ebc027578d12",
                    "tier_name": "Tier 1",
                    "lower_bound": 1,
                    "upper_bound": 500,
                    "net_flat_price": 0,
                    "net_unit_price": 21,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 44.74,
                },
                {
                    "id": "d9e3b8ed-a981-4534-9bdc-de8a95b20804",
                    "tier_name": "Tier 2",
                    "lower_bound": 501,
                    "upper_bound": 700,
                    "net_flat_price": 0,
                    "net_unit_price": 20,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 47.37,
                },
                {
                    "id": "a24dcd96-bb27-4d10-8d45-2b546af93593",
                    "tier_name": "Tier 3",
                    "lower_bound": 701,
                    "upper_bound": None,
                    "net_flat_price": 0,
                    "net_unit_price": 19,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 50,
                },
            ],
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Tiered",
            "billing_frequency": "monthly",
            "show_future_tiers": True,
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 1152,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 44, 457165, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "PREMIUMSUPPORT#_#71f98532-a031-4649-8663-305a6ba4f3eb",
        "start_date": datetime(2025, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "71f98532-a031-4649-8663-305a6ba4f3eb",
        "phase_name": "Year II",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("53.333333"),
        "list_unit_price": float("15000.000000"),
        "net_unit_price": float("7000.000050"),
        "list_total": float("15000.000000"),
        "net_total": float("7000.000050"),
        "prorated_list_total": float("15000.000000"),
        "prorated_net_total": float("7000.000050"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 15000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
    {
        "client_id": 10031,
        "temporal_id": 1154,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 44, 457165, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "PLATFORM#_#b39d2e63-3e0a-4464-b052-e2a7a7b8970c",
        "start_date": datetime(2026, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b39d2e63-3e0a-4464-b052-e2a7a7b8970c",
        "phase_name": "Year III",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 700,
        "discount_percent": float("45.488722"),
        "list_unit_price": float("38.000000"),
        "net_unit_price": float("20.714286"),
        "list_total": float("26600.000000"),
        "net_total": float("14500.000000"),
        "prorated_list_total": float("319200.000000"),
        "prorated_net_total": float("174000.000000"),
        "custom_data": {
            "tier_allocations": {
                "0": {
                    "id": "cdec5f65-6cec-42cc-8e29-ebc027578d12",
                    "name": "Tier 1",
                    "quantity": 500,
                    "net_total": 10500.0,
                    "list_total": 19000.0,
                    "lower_bound": 1,
                    "upper_bound": 500,
                    "net_flat_price": 0.0,
                    "net_unit_price": 21.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 126000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 228000.0,
                    "unit_price_discount": 44.74,
                },
                "1": {
                    "id": "d9e3b8ed-a981-4534-9bdc-de8a95b20804",
                    "name": "Tier 2",
                    "quantity": 200,
                    "net_total": 4000.0,
                    "list_total": 7600.0,
                    "lower_bound": 501,
                    "upper_bound": 700,
                    "net_flat_price": 0.0,
                    "net_unit_price": 20.0,
                    "list_flat_price": 0.0,
                    "list_unit_price": 38.0,
                    "prorated_net_total": 48000.0,
                    "flat_price_discount": 0.0,
                    "prorated_list_total": 91200.0,
                    "unit_price_discount": 47.37,
                },
            }
        },
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Tiered - Monthly",
            "row_key": "301",
            "currency": "USD",
            "tier_data": [
                {
                    "id": "cdec5f65-6cec-42cc-8e29-ebc027578d12",
                    "tier_name": "Tier 1",
                    "lower_bound": 1,
                    "upper_bound": 500,
                    "net_flat_price": 0,
                    "net_unit_price": 21,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 44.74,
                },
                {
                    "id": "d9e3b8ed-a981-4534-9bdc-de8a95b20804",
                    "tier_name": "Tier 2",
                    "lower_bound": 501,
                    "upper_bound": 700,
                    "net_flat_price": 0,
                    "net_unit_price": 20,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 47.37,
                },
                {
                    "id": "a24dcd96-bb27-4d10-8d45-2b546af93593",
                    "tier_name": "Tier 3",
                    "lower_bound": 701,
                    "upper_bound": None,
                    "net_flat_price": 0,
                    "net_unit_price": 19,
                    "list_flat_price": 0,
                    "list_unit_price": 38,
                    "flat_price_discount": 0,
                    "unit_price_discount": 50,
                },
            ],
            "list_price": 38,
            "max_discount": None,
            "pricing_method": "Tiered",
            "billing_frequency": "monthly",
            "show_future_tiers": True,
        },
        "order": 0,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 10031,
        "temporal_id": 1155,
        "knowledge_begin_date": datetime(2024, 12, 9, 13, 44, 44, 457165, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_line_item_id": "PREMIUMSUPPORT#_#b39d2e63-3e0a-4464-b052-e2a7a7b8970c",
        "start_date": datetime(2026, 11, 11, 0, 0, tzinfo=utc),
        "end_date": datetime(2027, 11, 10, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b39d2e63-3e0a-4464-b052-e2a7a7b8970c",
        "phase_name": "Year III",
        "product_name": "Premium White Glove Support",
        "sku": "PREMIUMSUPPORT",
        "billing_frequency": "annual",
        "billing_type": "recurring",
        "prorate_multiplier": float("1.000000"),
        "quantity": 1,
        "discount_percent": float("53.333333"),
        "list_unit_price": float("15000.000000"),
        "net_unit_price": float("7000.000050"),
        "list_total": float("15000.000000"),
        "net_total": float("7000.000050"),
        "prorated_list_total": float("15000.000000"),
        "prorated_net_total": float("7000.000050"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PREMIUMSUPPORT",
            "label": "USD - Flat Fee - Annual",
            "row_key": "501",
            "currency": "USD",
            "tier_data": None,
            "list_price": 15000,
            "max_discount": None,
            "pricing_method": "Flat Fee",
            "billing_frequency": "annual",
        },
        "order": 1,
        "unit_of_measure": "Accounts",
    },
]

TC_1_ARR_DICTS = [
    defaultdict(
        lambda: float("0.00"),
        {1: float("87200.00"), 2: float("87200.00"), 3: float("0.00")},
    ),
    defaultdict(lambda: float("0.00"), {}),
    defaultdict(
        lambda: float("0.00"),
        {1: float("10000.00"), 2: float("10000.00"), 3: float("0.00")},
    ),
    defaultdict(
        lambda: float("0.00"),
        {1: float("7000.00"), 2: float("7000.00"), 3: float("0.00")},
    ),
    defaultdict(
        lambda: float("0.00"),
        {
            1: float("297800.00"),
            2: float("297800.00"),
            3: float("297800.00"),
            4: float("0.00"),
        },
    ),
    defaultdict(lambda: float("0.00"), {}),
    defaultdict(
        lambda: float("0.00"),
        {
            1: float("5000.00"),
            2: float("5000.00"),
            3: float("5000.00"),
            4: float("0.00"),
        },
    ),
    defaultdict(lambda: float("0.00"), {}),
    defaultdict(
        lambda: float("0.00"),
        {
            1: float("181000.00"),
            2: float("181000.00"),
            3: float("181000.00"),
            4: float("0.00"),
        },
    ),
    defaultdict(lambda: float("0.00"), {}),
    defaultdict(
        lambda: float("0.00"),
        {
            1: float("7000.00"),
            2: float("7000.00"),
            3: float("7000.00"),
            4: float("0.00"),
        },
    ),
    defaultdict(lambda: float("0.00"), {}),
]

TC_1_QUOTE_REPORT = [
    {
        "hs_object_id": "***********",
        "quote_id": "2619a5b8-ef4e-4d14-8cba-64e5a422e624",
        "quote_name": "IDG",
        "opportunity": "starbucks - Nona Trass",
        "account": "starbucks",
        "subscription_start_date": "2024-09-30",
        "subscription_end_date": "2027-09-29",
        "contract_period": "3 years",
        "deal_credit_terms": 30,
        "integrations": "Snowflake, Salesforce, and Ultipro",
        "data_centre_requirements": "US data center",
        "dpa_signed_": True,
        "offline_msa_signed_": False,
        "autorenwal_": False,
        "no_of_payees": 2600,
        "implementation_charges": 30000.0,
        "tiered_pricing_offered__includes_platform_fee_based_pricing__": True,
        "amount": 87200.0,
        "year_2_arr": 87200.0,
        "year_3_arr": 0.0,
        "white_glove_support_charges": 10000.0,
        "white_glove_support_charges_year_2": 10000.0,
        "white_glove_support_charges_year_3": 0.0,
        "connector_charges": 0.0,
        "connector_charges_year_2": 0.0,
        "connector_charges_year_3": 0.0,
        "asc606_charges": 7000.0,
        "asc606_charges_year_2": 7000.0,
        "asc606_charges_year_3": 0.0,
    },
    {
        "hs_object_id": "***********",
        "quote_id": "072263e0-5538-4ef5-bae1-f8acd82b17a3",
        "quote_name": "Bazaarvoice",
        "opportunity": "walgreens - Caz Normavill",
        "account": "walgreens",
        "subscription_start_date": "2024-11-07",
        "subscription_end_date": "2026-11-06",
        "contract_period": "2 years",
        "deal_credit_terms": 30,
        "integrations": "Salesforce, Netsuite, Snowflake, and Workday",
        "data_centre_requirements": "US data center",
        "dpa_signed_": True,
        "offline_msa_signed_": False,
        "autorenwal_": False,
        "no_of_payees": 450,
        "implementation_charges": 10000.0,
        "tiered_pricing_offered__includes_platform_fee_based_pricing__": False,
        "amount": 297800.0,
        "year_2_arr": 297800.0,
        "year_3_arr": 297800.0,
        "white_glove_support_charges": 5000.0,
        "white_glove_support_charges_year_2": 5000.0,
        "white_glove_support_charges_year_3": 5000.0,
        "connector_charges": 0.0,
        "connector_charges_year_2": 0.0,
        "connector_charges_year_3": 0.0,
        "asc606_charges": 0.0,
        "asc606_charges_year_2": 0.0,
        "asc606_charges_year_3": 0.0,
    },
    {
        "hs_object_id": "***********",
        "quote_id": "e4743cdc-c2ec-431d-8959-92c3a04dc900",
        "quote_name": "Progress",
        "opportunity": "ubs - Sarine Wressell",
        "account": "ubs",
        "subscription_start_date": "2024-11-11",
        "subscription_end_date": "2027-11-10",
        "contract_period": "3 years",
        "deal_credit_terms": 30,
        "integrations": "Salesforce, Data Warehouse",
        "data_centre_requirements": "US data center",
        "dpa_signed_": True,
        "offline_msa_signed_": True,
        "autorenwal_": False,
        "no_of_payees": 700,
        "implementation_charges": 15000.0,
        "tiered_pricing_offered__includes_platform_fee_based_pricing__": True,
        "amount": 181000.0,
        "year_2_arr": 181000.0,
        "year_3_arr": 181000.0,
        "white_glove_support_charges": 7000.0,
        "white_glove_support_charges_year_2": 7000.0,
        "white_glove_support_charges_year_3": 7000.0,
        "connector_charges": 0.0,
        "connector_charges_year_2": 0.0,
        "connector_charges_year_3": 0.0,
        "asc606_charges": 0.0,
        "asc606_charges_year_2": 0.0,
        "asc606_charges_year_3": 0.0,
    },
]

TC_2_QUOTES = [
    {
        "client_id": 31617,
        "temporal_id": 20,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 41, 16, 566858, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("378e7b06-b3f8-4767-bec5-34b297b84772"),
        "quote_display_id": 1005,
        "quote_name": "ATT - 50",
        "quote_currency": "USD",
        "form_id": UUID("1d510d75-3ce3-462d-9b89-b70e255f1eef"),
        "valid_till": datetime(2025, 3, 15, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": False,
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 1,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 60000.0,
        "net_quote_total": 60000.0,
        "discount_amount": 0.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 60000.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2025, 2, 13, 5, 40, 29, 967823, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 31617,
        "temporal_id": 7,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 37, 45, 927009, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("516730e9-de22-4075-9a25-a7843fd93d57"),
        "quote_display_id": 1002,
        "quote_name": "Intel - 50",
        "quote_currency": "USD",
        "form_id": UUID("a8b28685-d430-4fa3-8d3d-ebaf3ef47795"),
        "valid_till": datetime(2025, 3, 15, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": False,
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 1,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 30000.0,
        "net_quote_total": 30000.0,
        "discount_amount": 0.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 30000.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2025, 2, 13, 5, 37, 29, 526726, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 31617,
        "temporal_id": 23,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 42, 37, 376251, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("63b3bd8e-030e-4f0c-81ad-858e3e8136b2"),
        "quote_display_id": 1006,
        "quote_name": "ATT - 100",
        "quote_currency": "USD",
        "form_id": UUID("3a94fc3f-5db9-4feb-aabd-9546a190074e"),
        "valid_till": datetime(2025, 3, 15, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": False,
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 1,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 120000.0,
        "net_quote_total": 120000.0,
        "discount_amount": 0.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 120000.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2025, 2, 13, 5, 41, 21, 765120, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 31617,
        "temporal_id": 8,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 38, 27, 114627, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("8e817a00-1b69-4dde-bc42-9d809d136658"),
        "quote_display_id": 1001,
        "quote_name": "Intel - 100",
        "quote_currency": "USD",
        "form_id": UUID("b9870c5a-4223-403f-b228-5f87aab69670"),
        "valid_till": datetime(2025, 3, 15, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": True,
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 1,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 60000.0,
        "net_quote_total": 60000.0,
        "discount_amount": 0.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 60000.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2025, 2, 13, 5, 35, 7, 980702, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 31617,
        "temporal_id": 13,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 39, 32, 229044, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("e41a343a-569f-4c5d-a893-faf738a4b66b"),
        "quote_display_id": 1003,
        "quote_name": "Pfizer - 100",
        "quote_currency": "USD",
        "form_id": UUID("3cc7ffb4-a5c1-4438-b314-2b296c79682c"),
        "valid_till": datetime(2025, 3, 15, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": True,
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 1,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 48000.0,
        "net_quote_total": 48000.0,
        "discount_amount": 0.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 48000.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2025, 2, 13, 5, 38, 40, 791148, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
    {
        "client_id": 31617,
        "temporal_id": 16,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 40, 17, 374806, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": {
            "server": "backend",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        "quote_id": UUID("e561425c-da57-4064-a20f-d4409d68a82f"),
        "quote_display_id": 1004,
        "quote_name": "Pfizer - 50",
        "quote_currency": "USD",
        "form_id": UUID("7c9bb703-57fd-4d88-9ffa-4744c728ba89"),
        "valid_till": datetime(2025, 3, 15, 23, 59, 59, 999999, tzinfo=utc),
        "is_primary": False,
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "duration_value": 1,
        "duration_type": "years",
        "opportunity_id": "***********",
        "account_id": "***********",
        "contact_id": "***********",
        "bill_to": None,
        "ship_to": None,
        "list_quote_total": 24000.0,
        "net_quote_total": 24000.0,
        "discount_amount": 0.0,
        "closed_date": None,
        "custom_data": {"recurring_total": 24000.0},
        "owner_id": "<EMAIL>",
        "created_at": datetime(2025, 2, 13, 5, 39, 39, 488785, tzinfo=utc),
        "created_by": "<EMAIL>",
    },
]

TC_2_QUOTE_STATUSES = [
    {
        "client_id": 31617,
        "temporal_id": 15,
        "knowledge_begin_date": datetime(2025, 2, 13, 6, 41, 16, 566858, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("4cdf3a05-e7e5-4161-b6a6-5bd18ad067cf"),
        "quote_id": UUID("378e7b06-b3f8-4767-bec5-34b297b84772"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2025, 2, 13, 5, 40, 29, 967823, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 31617,
        "temporal_id": 4,
        "knowledge_begin_date": datetime(2025, 2, 13, 6, 37, 45, 927009, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("5b2fe72a-b915-416d-8815-76fa68634676"),
        "quote_id": UUID("516730e9-de22-4075-9a25-a7843fd93d57"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2025, 2, 13, 5, 37, 29, 526726, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 31617,
        "temporal_id": 18,
        "knowledge_begin_date": datetime(2025, 2, 13, 6, 42, 37, 376251, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("afe54c91-1747-4930-9756-4924fdd48841"),
        "quote_id": UUID("63b3bd8e-030e-4f0c-81ad-858e3e8136b2"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2025, 2, 13, 5, 41, 21, 765120, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 31617,
        "temporal_id": 6,
        "knowledge_begin_date": datetime(2025, 2, 13, 6, 38, 27, 55717, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("03be09d5-5391-4714-83a5-e1e95575d215"),
        "quote_id": UUID("8e817a00-1b69-4dde-bc42-9d809d136658"),
        "status": "Won",
        "tags": None,
        "created_at": datetime(2025, 2, 13, 5, 35, 7, 980702, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 31617,
        "temporal_id": 10,
        "knowledge_begin_date": datetime(2025, 2, 13, 6, 39, 32, 194389, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("607d396f-0a93-46bc-99fe-d3a18e51afe4"),
        "quote_id": UUID("e41a343a-569f-4c5d-a893-faf738a4b66b"),
        "status": "Won",
        "tags": None,
        "created_at": datetime(2025, 2, 13, 5, 38, 40, 791148, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
    {
        "client_id": 31617,
        "temporal_id": 13,
        "knowledge_begin_date": datetime(2025, 2, 13, 6, 40, 17, 374806, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_status_id": UUID("2269ef19-21e2-4a06-9859-c8359ad454be"),
        "quote_id": UUID("e561425c-da57-4064-a20f-d4409d68a82f"),
        "status": "Approved",
        "tags": None,
        "created_at": datetime(2025, 2, 13, 5, 39, 39, 488785, tzinfo=utc),
        "updated_by": "<EMAIL>",
        "comment": None,
    },
]

TC_2_QUOTE_FORM_DATA = [
    {
        "field1": "378e7b06-b3f8-4767-bec5-34b297b84772",
        "field2": "ATT - 50",
        "field3": "at&t - Florri Clemendot",
        "field4": "***********",
        "field5": "at&t",
        "field6": "***********",
        "field7": "Florri Clemendot",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-03-15",
        "field12": None,
        "field13": None,
        "field14": "Philippines",
        "field15": "TX",
        "field16": "Basak",
        "field17": "73 Mayfield Crossing",
        "field18": "None",
        "field19": "5317",
        "field20": "73 Mayfield Crossing,,Basak,TX,Philippines",
        "field21": False,
        "field22": None,
        "field23": None,
        "field24": None,
        "field25": None,
        "field26": None,
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": None,
        "field31": None,
        "field32": None,
        "field33": None,
        "field34": False,
        "field35": None,
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": None,
        "field52": "<EMAIL>",
    },
    {
        "field1": "63b3bd8e-030e-4f0c-81ad-858e3e8136b2",
        "field2": "ATT - 100",
        "field3": "at&t - Florri Clemendot",
        "field4": "***********",
        "field5": "at&t",
        "field6": "***********",
        "field7": "Florri Clemendot",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-03-15",
        "field12": None,
        "field13": None,
        "field14": "Philippines",
        "field15": "TX",
        "field16": "Basak",
        "field17": "73 Mayfield Crossing",
        "field18": "None",
        "field19": "5317",
        "field20": "73 Mayfield Crossing,,Basak,TX,Philippines",
        "field21": False,
        "field22": None,
        "field23": None,
        "field24": None,
        "field25": None,
        "field26": None,
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": None,
        "field31": None,
        "field32": None,
        "field33": None,
        "field34": False,
        "field35": None,
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": None,
        "field52": "<EMAIL>",
    },
    {
        "field1": "e41a343a-569f-4c5d-a893-faf738a4b66b",
        "field2": "Pfizer - 100",
        "field3": "pfizer - Salvador Tefft",
        "field4": "***********",
        "field5": "pfizer",
        "field6": "***********",
        "field7": "Salvador Tefft",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-03-15",
        "field12": None,
        "field13": None,
        "field14": "China",
        "field15": "NY",
        "field16": "Duqu",
        "field17": "89592 Pankratz Pass",
        "field18": "None",
        "field19": "10017",
        "field20": "89592 Pankratz Pass,,Duqu,NY,China",
        "field21": False,
        "field22": None,
        "field23": None,
        "field24": None,
        "field25": None,
        "field26": None,
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": None,
        "field31": None,
        "field32": None,
        "field33": None,
        "field34": False,
        "field35": None,
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": None,
        "field52": "<EMAIL>",
    },
    {
        "field1": "e561425c-da57-4064-a20f-d4409d68a82f",
        "field2": "Pfizer - 50",
        "field3": "pfizer - Salvador Tefft",
        "field4": "***********",
        "field5": "pfizer",
        "field6": "***********",
        "field7": "Salvador Tefft",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-03-15",
        "field12": None,
        "field13": None,
        "field14": "China",
        "field15": "NY",
        "field16": "Duqu",
        "field17": "89592 Pankratz Pass",
        "field18": "None",
        "field19": "10017",
        "field20": "89592 Pankratz Pass,,Duqu,NY,China",
        "field21": False,
        "field22": None,
        "field23": None,
        "field24": None,
        "field25": None,
        "field26": None,
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": None,
        "field31": None,
        "field32": None,
        "field33": None,
        "field34": False,
        "field35": None,
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": None,
        "field52": "<EMAIL>",
    },
    {
        "field1": "516730e9-de22-4075-9a25-a7843fd93d57",
        "field2": "Intel - 50",
        "field3": "intel corporation - Hilary Kidney",
        "field4": "***********",
        "field5": "intel corporation",
        "field6": "***********",
        "field7": "Hilary Kidney",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-03-15",
        "field12": None,
        "field13": None,
        "field14": "Dominican Republic",
        "field15": "CA",
        "field16": "Cachón",
        "field17": "23059 Stuart Drive",
        "field18": "None",
        "field19": "11809",
        "field20": "23059 Stuart Drive,,Cachón,CA,Dominican Republic",
        "field21": False,
        "field22": None,
        "field23": None,
        "field24": None,
        "field25": None,
        "field26": None,
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": None,
        "field31": None,
        "field32": None,
        "field33": None,
        "field34": False,
        "field35": None,
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": None,
        "field52": "<EMAIL>",
    },
    {
        "field1": "8e817a00-1b69-4dde-bc42-9d809d136658",
        "field2": "Intel - 100",
        "field3": "intel corporation - Hilary Kidney",
        "field4": "***********",
        "field5": "intel corporation",
        "field6": "***********",
        "field7": "Hilary Kidney",
        "field8": "***********",
        "field9": "USD",
        "field10": None,
        "field11": "2025-03-15",
        "field12": None,
        "field13": None,
        "field14": "Dominican Republic",
        "field15": "CA",
        "field16": "Cachón",
        "field17": "23059 Stuart Drive",
        "field18": "None",
        "field19": "11809",
        "field20": "23059 Stuart Drive,,Cachón,CA,Dominican Republic",
        "field21": False,
        "field22": None,
        "field23": None,
        "field24": None,
        "field25": None,
        "field26": None,
        "field27": None,
        "field28": None,
        "field29": None,
        "field30": None,
        "field31": None,
        "field32": None,
        "field33": None,
        "field34": False,
        "field35": None,
        "field36": False,
        "field37": False,
        "field38": True,
        "field45": "No Approval",
        "field46": "No Approval",
        "field47": "No Approval",
        "field48": "No Approval",
        "field49": "No Approval",
        "field50": "No Approval",
        "field51": None,
        "field52": "<EMAIL>",
    },
]

TC_2_QUOTE_LINE_ITEMS = [
    {
        "client_id": 31617,
        "temporal_id": 14,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 41, 8, 360268, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "378e7b06-b3f8-4767-bec5-34b297b84772",
        "quote_line_item_id": "PLATFORM#_#f781c60a-ad29-4ff7-b6a6-4d4905f541e7",
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "f781c60a-ad29-4ff7-b6a6-4d4905f541e7",
        "phase_name": "Year 1",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 50,
        "discount_percent": float("0.000000"),
        "list_unit_price": float("100.000000"),
        "net_unit_price": float("100.000000"),
        "list_total": float("5000.000000"),
        "net_total": float("5000.000000"),
        "prorated_list_total": float("60000.000000"),
        "prorated_net_total": float("60000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 100,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 1,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 31617,
        "temporal_id": 6,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 37, 43, 748496, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "516730e9-de22-4075-9a25-a7843fd93d57",
        "quote_line_item_id": "PLATFORM#_#b44d614a-092e-4800-be98-70105929228e",
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b44d614a-092e-4800-be98-70105929228e",
        "phase_name": "Year 1",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 50,
        "discount_percent": float("0.000000"),
        "list_unit_price": float("50.000000"),
        "net_unit_price": float("50.000000"),
        "list_total": float("2500.000000"),
        "net_total": float("2500.000000"),
        "prorated_list_total": float("30000.000000"),
        "prorated_net_total": float("30000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 50,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 1,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 31617,
        "temporal_id": 16,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 41, 32, 437333, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "63b3bd8e-030e-4f0c-81ad-858e3e8136b2",
        "quote_line_item_id": "PLATFORM#_#f781c60a-ad29-4ff7-b6a6-4d4905f541e7",
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "f781c60a-ad29-4ff7-b6a6-4d4905f541e7",
        "phase_name": "Year 1",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 100,
        "discount_percent": float("0.000000"),
        "list_unit_price": float("100.000000"),
        "net_unit_price": float("100.000000"),
        "list_total": float("10000.000000"),
        "net_total": float("10000.000000"),
        "prorated_list_total": float("120000.000000"),
        "prorated_net_total": float("120000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 100,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 1,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 31617,
        "temporal_id": 4,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 37, 8, 575319, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "8e817a00-1b69-4dde-bc42-9d809d136658",
        "quote_line_item_id": "PLATFORM#_#b44d614a-092e-4800-be98-70105929228e",
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "b44d614a-092e-4800-be98-70105929228e",
        "phase_name": "Year 1",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 100,
        "discount_percent": float("0.000000"),
        "list_unit_price": float("50.000000"),
        "net_unit_price": float("50.000000"),
        "list_total": float("5000.000000"),
        "net_total": float("5000.000000"),
        "prorated_list_total": float("60000.000000"),
        "prorated_net_total": float("60000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 50,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 1,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 31617,
        "temporal_id": 9,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 39, 16, 144706, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e41a343a-569f-4c5d-a893-faf738a4b66b",
        "quote_line_item_id": "PLATFORM#_#bfda60be-9b32-40dd-94d5-bc43c611204a",
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "bfda60be-9b32-40dd-94d5-bc43c611204a",
        "phase_name": "Year 1",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 100,
        "discount_percent": float("0.000000"),
        "list_unit_price": float("40.000000"),
        "net_unit_price": float("40.000000"),
        "list_total": float("4000.000000"),
        "net_total": float("4000.000000"),
        "prorated_list_total": float("48000.000000"),
        "prorated_net_total": float("48000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 1,
        "unit_of_measure": "Users",
    },
    {
        "client_id": 31617,
        "temporal_id": 11,
        "knowledge_begin_date": datetime(2025, 2, 13, 5, 39, 55, 976492, tzinfo=utc),
        "knowledge_end_date": None,
        "is_deleted": False,
        "additional_details": None,
        "quote_id": "e561425c-da57-4064-a20f-d4409d68a82f",
        "quote_line_item_id": "PLATFORM#_#bfda60be-9b32-40dd-94d5-bc43c611204a",
        "start_date": datetime(2025, 3, 1, 0, 0, tzinfo=utc),
        "end_date": datetime(2026, 2, 28, 23, 59, 59, 999999, tzinfo=utc),
        "phase_id": "bfda60be-9b32-40dd-94d5-bc43c611204a",
        "phase_name": "Year 1",
        "product_name": "Everstage Platform",
        "sku": "PLATFORM",
        "billing_frequency": "monthly",
        "billing_type": "recurring",
        "prorate_multiplier": float("12.000000"),
        "quantity": 50,
        "discount_percent": float("0.000000"),
        "list_unit_price": float("40.000000"),
        "net_unit_price": float("40.000000"),
        "list_total": float("2000.000000"),
        "net_total": float("2000.000000"),
        "prorated_list_total": float("24000.000000"),
        "prorated_net_total": float("24000.000000"),
        "custom_data": None,
        "pricepoint_data": {
            "sku": "PLATFORM",
            "label": "USD - Per Unit - Monthly",
            "row_key": "101",
            "currency": "USD",
            "tier_data": None,
            "list_price": 40,
            "max_discount": None,
            "pricing_method": "Per Unit",
            "billing_frequency": "monthly",
        },
        "order": 1,
        "unit_of_measure": "Users",
    },
]

TC_2_ARR_DICTS = [
    defaultdict(lambda: float("0.00"), {1: float("120000.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("48000.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("60000.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
    defaultdict(lambda: float("0.00"), {1: float("0.00")}),
]

TC_2_QUOTE_REPORT = [
    {
        "hs_object_id": "***********",
        "quote_id": "63b3bd8e-030e-4f0c-81ad-858e3e8136b2",
        "quote_name": "ATT - 100",
        "opportunity": "at&t - Florri Clemendot",
        "account": "at&t",
        "subscription_start_date": "2025-03-01",
        "subscription_end_date": "2026-02-28",
        "contract_period": "1 year",
        "deal_credit_terms": None,
        "integrations": None,
        "data_centre_requirements": None,
        "dpa_signed_": False,
        "offline_msa_signed_": False,
        "autorenwal_": False,
        "no_of_payees": 100,
        "implementation_charges": 0.0,
        "tiered_pricing_offered__includes_platform_fee_based_pricing__": False,
        "amount": 120000.0,
        "year_2_arr": 0.0,
        "year_3_arr": 0.0,
        "white_glove_support_charges": 0.0,
        "white_glove_support_charges_year_2": 0.0,
        "white_glove_support_charges_year_3": 0.0,
        "connector_charges": 0.0,
        "connector_charges_year_2": 0.0,
        "connector_charges_year_3": 0.0,
        "asc606_charges": 0.0,
        "asc606_charges_year_2": 0.0,
        "asc606_charges_year_3": 0.0,
    },
    {
        "hs_object_id": "***********",
        "quote_id": "e41a343a-569f-4c5d-a893-faf738a4b66b",
        "quote_name": "Pfizer - 100",
        "opportunity": "pfizer - Salvador Tefft",
        "account": "pfizer",
        "subscription_start_date": "2025-03-01",
        "subscription_end_date": "2026-02-28",
        "contract_period": "1 year",
        "deal_credit_terms": None,
        "integrations": None,
        "data_centre_requirements": None,
        "dpa_signed_": False,
        "offline_msa_signed_": False,
        "autorenwal_": False,
        "no_of_payees": 100,
        "implementation_charges": 0.0,
        "tiered_pricing_offered__includes_platform_fee_based_pricing__": False,
        "amount": 48000.0,
        "year_2_arr": 0.0,
        "year_3_arr": 0.0,
        "white_glove_support_charges": 0.0,
        "white_glove_support_charges_year_2": 0.0,
        "white_glove_support_charges_year_3": 0.0,
        "connector_charges": 0.0,
        "connector_charges_year_2": 0.0,
        "connector_charges_year_3": 0.0,
        "asc606_charges": 0.0,
        "asc606_charges_year_2": 0.0,
        "asc606_charges_year_3": 0.0,
    },
    {
        "hs_object_id": "***********",
        "quote_id": "8e817a00-1b69-4dde-bc42-9d809d136658",
        "quote_name": "Intel - 100",
        "opportunity": "intel corporation - Hilary Kidney",
        "account": "intel corporation",
        "subscription_start_date": "2025-03-01",
        "subscription_end_date": "2026-02-28",
        "contract_period": "1 year",
        "deal_credit_terms": None,
        "integrations": None,
        "data_centre_requirements": None,
        "dpa_signed_": False,
        "offline_msa_signed_": False,
        "autorenwal_": False,
        "no_of_payees": 100,
        "implementation_charges": 0.0,
        "tiered_pricing_offered__includes_platform_fee_based_pricing__": False,
        "amount": 60000.0,
        "year_2_arr": 0.0,
        "year_3_arr": 0.0,
        "white_glove_support_charges": 0.0,
        "white_glove_support_charges_year_2": 0.0,
        "white_glove_support_charges_year_3": 0.0,
        "connector_charges": 0.0,
        "connector_charges_year_2": 0.0,
        "connector_charges_year_3": 0.0,
        "asc606_charges": 0.0,
        "asc606_charges_year_2": 0.0,
        "asc606_charges_year_3": 0.0,
    },
]
