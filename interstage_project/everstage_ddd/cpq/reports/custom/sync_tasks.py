import io
import json
from logging import Logger, getLogger
from uuid import uuid4

from celery import chain, shared_task
from django.utils import timezone

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.threadlocal_log_context import set_threadlocal_context

logger: Logger = getLogger(__name__)


@shared_task(base=EverCeleryBaseTask)
def upload_report_dataframe_to_s3(client_id, e2e_sync_run_id):
    try:
        from async_tasks.accessor import AsyncTaskAccessor
        from commission_engine.accessors.client_accessor import get_client_settings
        from commission_engine.accessors.custom_object_accessor import (
            CustomObjectAccessor,
        )
        from everstage_ddd.cpq.reports.custom.report_etl import QRGenerator
        from everstage_ddd.cpq.reports.custom.utils import get_latest_report_start_time
        from spm.services.config_services.employee_services import (
            get_power_admin_users_for_client,
        )
        from spm.services.custom_object_services.custom_object_service import (
            get_object_by_ids,
        )
        from spm.services.data_import_services.data_import_services import (
            get_config_file,
            get_s3_path_prefix_for_upload_input,
            upload_files_to_s3,
        )

        set_threadlocal_context(
            {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
        )
        power_admin = get_power_admin_users_for_client(client_id)[0]
        async_params = {
            "e2e_sync_run_id": e2e_sync_run_id,
        }
        task = AsyncTaskAccessor(client_id).add_task(
            power_admin,
            "QUOTE_REPORT_UPLOAD",
            async_params,
        )

        report = QRGenerator(client_id)
        start_ts = get_latest_report_start_time(client_id)
        if not start_ts:
            start_ts = timezone.now() - timezone.timedelta(days=30)
        end_ts = timezone.now()
        report_df = report.run(start_ts, end_ts)

        if report_df.empty:
            logger.warning("No data to upload")
            AsyncTaskAccessor(client_id).set_done(
                task.id, {"message": "No data to upload"}
            )
            return
        logger.info("Uploading %s records to s3", len(report_df))
        # Upload data to custom object
        csv_file_obj = io.BytesIO()
        report_df.to_csv(csv_file_obj)  # write to BytesIO buffer
        csv_file_obj.seek(0)

        # client_settings = ClientSettings.objects.filter(client_id=client_id).first()
        client_settings = get_client_settings(client_id)
        custom_object_id = client_settings.get("cpq_settings", {}).get(
            "quote_report_object_id"
        )

        custom_object = get_object_by_ids(
            client_id=client_id,
            custom_object_ids=custom_object_id,
            as_dicts=True,
        )
        object_map = CustomObjectAccessor(client_id).get_custom_object_id_to_name_map()
        custom_object_name = object_map.get(custom_object_id, "")
        csv_header_map = custom_object[0]["csv_header_map"]
        task_config_file = get_config_file(
            client_id, custom_object_id, custom_object_name, csv_header_map
        )
        csv_file_path, config_file_path = upload_files_to_s3(
            custom_object_id,
            "BULK_IMPORT_DATA",
            csv_file_obj,
            path_prefix=get_s3_path_prefix_for_upload_input(client_id),
            task_config_file=task_config_file,
            logger=logger,
        )

        async_params = {
            "csv_file_path": csv_file_path,
            "custom_object_id": custom_object_id,
            "custom_object_name": custom_object_name,
            "config_file_path": config_file_path,
            "e2e_sync_run_id": e2e_sync_run_id,
        }
        fields_to_update = {
            "params": async_params,
        }
        AsyncTaskAccessor(client_id).update_task(
            task.id,
            fields_to_update,
        )

    except Exception as e:
        from commission_engine.services.etl_sync_status_service import (
            change_status_to_failed,
        )

        logger.exception("Error while uploading quote report dataframe to s3")
        AsyncTaskAccessor(client_id).set_failed(task.id, str(e))
        failed_timestamp = timezone.now()
        change_status_to_failed(
            client_id,
            e2e_sync_run_id,
            failed_timestamp,
            should_update_completion_time=True,
        )
        raise e from e


@shared_task(base=EverCeleryBaseTask)
def upload_report_data_to_custom_object(client_id, e2e_sync_run_id):
    try:
        from async_tasks.accessor import AsyncTaskAccessor
        from commission_engine.accessors.skd_pkd_map_accessor import (
            CustomObjectPkdMapAccessor,
        )
        from spm.services.custom_object_services.custom_object_service import (
            get_object_by_ids,
        )
        from spm.services.data_import_services.data_import_services import (
            download_files_from_s3,
            get_config_file,
        )
        from spm.services.data_import_services.data_import_tasks import ManageData

        set_threadlocal_context(
            {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
        )
        pending_task = AsyncTaskAccessor(client_id).get_task_by_params_key(
            "QUOTE_REPORT_UPLOAD", "e2e_sync_run_id", e2e_sync_run_id
        )
        if not pending_task:
            logger.error(
                "No pending task found for e2e_sync_run_id: %s", e2e_sync_run_id
            )
            return None

        task_id = pending_task.id
        AsyncTaskAccessor(client_id).set_processing(task_id)

        csv_file_path = pending_task.params.get("csv_file_path")
        config_file_path = pending_task.params.get("config_file_path")

        if not csv_file_path or not config_file_path:
            logger.error("csv_file_path or config_file_path is missing")
            AsyncTaskAccessor(client_id).set_failed(
                task_id, "No data to upload to custom object"
            )
            return None

        csv_object, task_config = download_files_from_s3(
            csv_file_path, config_file_path, logger
        )

        custom_object_id = task_config.get("custom_object_id", None)
        custom_object_name = task_config.get("custom_object_name", None)

        custom_object = get_object_by_ids(
            client_id=client_id,
            custom_object_ids=custom_object_id,
            as_dicts=True,
        )
        custom_obj_config = get_config_file(
            client_id,
            custom_object_id,
            custom_object[0]["name"],
            custom_object[0]["csv_header_map"],
        )
        task_config = json.loads(str(custom_obj_config))

        manage_data = ManageData(
            task="upsert",
            client_id=client_id,
            custom_object_id=custom_object_id,
            custom_object_name=custom_object_name,
            task_config=task_config,
            audit=None,
            uploaded_file_name="uploaded_file_name",
            csv_object=csv_object,
            logger=logger,
        )

        result, primary_kd = manage_data.upsert()
        AsyncTaskAccessor(client_id).set_done(task_id, result)
        CustomObjectPkdMapAccessor(client_id).upsert_custom_object_primary_kd(
            custom_object_id, primary_kd
        )

        return primary_kd
    except Exception as e:
        from commission_engine.services.etl_sync_status_service import (
            change_status_to_failed,
        )

        logger.exception("Error while uploading quote report to custom object")
        AsyncTaskAccessor(client_id).set_failed(task_id, str(e))
        failed_timestamp = timezone.now()
        change_status_to_failed(
            client_id,
            e2e_sync_run_id,
            failed_timestamp,
            should_update_completion_time=True,
        )
        raise e from e


@shared_task(base=EverCeleryBaseTask)
def custom_quote_report_wrapper_sync(client_id):
    from commission_engine.accessors.client_accessor import (
        get_client_settings,
        get_client_subscription_plan,
    )
    from commission_engine.services.etl_sync_status_service import (
        update_completion_time,
    )
    from commission_engine.services.etl_tasks_service import ETLSync
    from commission_engine.utils import ETL_ACTIVITY
    from everstage_ddd.downstream import get_downstream_wrapper_sync_task
    from everstage_etl.tasks import get_datasheet_wrapper_sync_task
    from interstage_project.celery import TaskGroupEnum
    from interstage_project.utils import get_queue_name_respect_to_task_group

    e2e_sync_run_id = uuid4()
    etl_obj = ETLSync(client_id=client_id, e2e_sync_run_id=e2e_sync_run_id)
    etl_obj.insert_sync_status(
        task=ETL_ACTIVITY.QUOTE_REPORT_SYNC.value,
    )
    set_threadlocal_context(
        {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
    )

    try:
        subscription_plan = get_client_subscription_plan(client_id)
        queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MISC.value
        )

        client_settings = get_client_settings(client_id)
        datasheet_id = client_settings.get("cpq_settings", {}).get(
            "quote_report_sheet_id"
        )
        integration_id = client_settings.get("cpq_settings", {}).get(
            "quote_integration_id"
        )

        datasheet_task = get_datasheet_wrapper_sync_task(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_type=None,
            datasheet_id=datasheet_id,
            is_force_invalidate=False,
        )
        downstream_task = get_downstream_wrapper_sync_task(
            client_id=client_id,
            integration_ids=[integration_id],
            e2e_sync_run_id=e2e_sync_run_id,
        )

        chain_tasks = [
            upload_report_dataframe_to_s3.si(  # type: ignore
                client_id=client_id, e2e_sync_run_id=str(e2e_sync_run_id)
            ).set(queue=queue_name),
            upload_report_data_to_custom_object.si(  # type: ignore
                client_id=client_id, e2e_sync_run_id=str(e2e_sync_run_id)
            ).set(queue=queue_name),
            datasheet_task,
            downstream_task,
            update_completion_time.si(  # type: ignore
                client_id=client_id, e2e_sync_run_id=str(e2e_sync_run_id)
            ).set(queue=queue_name),
        ]
        logger.info(f"Creating quote report sync chain with {len(chain_tasks)} tasks")
        chain(*chain_tasks).apply_async(compression="lzma", serializer="pickle")
    except Exception as e:
        from commission_engine.services.etl_sync_status_service import (
            change_status_to_failed,
        )

        logger.exception("Error while creating quote report sync chain")
        failed_timestamp = timezone.now()
        change_status_to_failed(
            client_id,
            e2e_sync_run_id,
            failed_timestamp,
            should_update_completion_time=True,
        )
        raise e from e
