def get_latest_report_start_time(client_id):
    from commission_engine.accessors.etl_housekeeping_accessor import (
        ETLSyncStatusReaderAccessor,
    )
    from commission_engine.utils.general_data import ETL_ACTIVITY

    etl_record = ETLSyncStatusReaderAccessor(
        client_id
    ).get_latest_successfull_sync_record(ETL_ACTIVITY.QUOTE_REPORT_SYNC.value)

    if etl_record:
        return etl_record.sync_start_time
    return None
