from datetime import datetime
from decimal import Decimal
from logging import Logger, get<PERSON>ogger
from uuid import UUID

from numpy import NaN, nan
from pandas import DataFrame, NaT

from ...enums import DurationType, QuoteStatus
from ...forms import EverstageFormAccessor
from ...quote import Quo<PERSON><PERSON><PERSON><PERSON>temAccessor, QuoteSelector, QuoteStatusSelector
from ...quote.service.revenue_calculation_service import calculate_year_wise_arr
from .schema import CustomReport

logger: Logger = getLogger(__name__)


class QRGenerator:
    """
    Generates customised 'Quote Report' for Everstage Inc. to be synced to Deal object in Hubspot CRM.
    Data is fetched from the following CPQ objects: quote, quote status, quote form and quote line item.

    Example usage: report = QRGenerator(10031).run(datetime(2024, 1, 1), datetime(2025, 1, 1))
    """

    def __init__(self, client_id: int) -> None:
        self.client_id: int = client_id
        self._qkd_map: dict[UUID, datetime] = {}
        self._qt_df: DataFrame | None = None  # Quote Object
        self._qs_df: DataFrame | None = None  # Quote Status
        self._qf_df: DataFrame | None = None  # Quote Form Data
        self._qli_df: DataFrame | None = None  # Quote Line Items
        self._report_df: DataFrame | None = None

    @property
    def quote_columns(self) -> list[str]:
        return [
            "quote_id",
            "form_id",
            "start_date",
            "end_date",
            "duration_value",
            "duration_type",
            "opportunity_id",
            "is_primary",
        ]

    @property
    def status_columns(self) -> list[str]:
        return [
            "quote_id",
            "knowledge_begin_date",
            "status",
        ]

    @property
    def form_mappings(self) -> dict[str, str]:
        return {
            "field1": "quote_id",
            "field2": "quote_name",
            "field32": "credit_terms",
            "field26": "connectors",
            "field13": "data_center",
            "field34": "dpa_signed",
            "field33": "terms_of_use",
            "field37": "autorenwal",
            "field3": "opportunity",
            "field5": "account",
        }

    @property
    def qli_columns(self) -> list[str]:
        return [
            "quote_id",
            "sku",
            "pricepoint_data",
            "quantity",
            "prorated_net_total",
        ]

    @property
    def status_filters(self) -> list[str]:
        return [
            QuoteStatus.DRAFT.value,
            QuoteStatus.PENDING_APPROVAL.value,
            QuoteStatus.REJECTED.value,
        ]

    def _calculate_duration(self, duration_type: str, duration_value: int) -> str:
        if duration_type == DurationType.YEARS.value:
            years = int(duration_value)
        elif duration_type == DurationType.MONTHS.value:
            years = int(round(duration_value / 12))
        else:
            years = None
        if years in (1, 2, 3):
            return f"{years} year{'s' if years > 1 else ''}"
        return "Custom terms"

    def _parse_data_center(self, data_center: str) -> str | None:
        if data_center == "US Data Center":
            return "US data center"
        elif data_center == "EU Data Center":
            return "EU data center"
        return None

    def _count_licenses(self, quote_id: str) -> int:
        if self._qli_df is None:
            return 0
        filtered_df = self._qli_df.query(
            f"(quote_id == '{quote_id}') & (sku == 'PLATFORM')"
        )
        if not filtered_df.empty:
            return filtered_df["quantity"].max()
        return 0

    def _calculate_implementation_charges(self, quote_id: str) -> Decimal:
        if self._qli_df is None:
            return Decimal(0)
        filtered_df = self._qli_df.query(
            f"(quote_id == '{quote_id}') & (sku == 'IMPLEMENTATION')"
        )
        if not filtered_df.empty:
            return round(filtered_df["prorated_net_total"].sum(), 2)
        return Decimal(0)

    def _check_tier_pricing(self, quote_id: str) -> bool:
        if self._qli_df is None:
            return False
        filtered_df = self._qli_df.query(
            f"(quote_id == '{quote_id}') & (sku == 'PLATFORM')"
        ).loc[
            lambda df: df["pricepoint_data"].apply(
                lambda x: isinstance(x, dict) and x.get("pricing_method") == "Tiered"
            )
        ]
        return not filtered_df.empty

    def _calculate_arr(self, quote_id: UUID) -> dict:
        kd = self._qkd_map.get(quote_id)
        arr_map = calculate_year_wise_arr(self.client_id, quote_id, sku=None, kd=kd)
        connector_arr_map = calculate_year_wise_arr(
            self.client_id, quote_id, sku="CONNECTOR", kd=kd
        )
        support_arr_map = calculate_year_wise_arr(
            self.client_id, quote_id, sku="PREMIUMSUPPORT", kd=kd
        )
        asc606_arr_map = calculate_year_wise_arr(
            self.client_id, quote_id, sku="ASC606", kd=kd
        )
        return {
            "amount": arr_map[1],
            "year_2_arr": arr_map[2],
            "year_3_arr": arr_map[3],
            "white_glove_support_charges": support_arr_map[1],
            "white_glove_support_charges_year_2": support_arr_map[2],
            "white_glove_support_charges_year_3": support_arr_map[3],
            "connector_charges": connector_arr_map[1],
            "connector_charges_year_2": connector_arr_map[2],
            "connector_charges_year_3": connector_arr_map[3],
            "asc606_charges": asc606_arr_map[1],
            "asc606_charges_year_2": asc606_arr_map[2],
            "asc606_charges_year_3": asc606_arr_map[3],
        }

    def _generate_record(self, quote: dict) -> dict:
        return {
            "hs_object_id": str(quote["opportunity_id"]),
            "quote_id": quote["quote_id"],
            "quote_name": quote["quote_name"],
            "opportunity": quote["opportunity"],
            "account": quote["account"],
            "subscription_start_date": (
                quote["start_date"].strftime("%Y-%m-%d")
                if isinstance(quote["start_date"], datetime)
                else None
            ),
            "subscription_end_date": (
                quote["end_date"].strftime("%Y-%m-%d")
                if isinstance(quote["end_date"], datetime)
                else None
            ),
            "contract_period": self._calculate_duration(
                quote["duration_type"], quote["duration_value"]
            ),
            "deal_credit_terms": (
                int(quote["credit_terms"]) if quote["credit_terms"] else None
            ),
            "integrations": quote["connectors"] or None,
            "data_centre_requirements": self._parse_data_center(quote["data_center"]),
            "dpa_signed_": quote["dpa_signed"] == "Yes",
            "offline_msa_signed_": quote["terms_of_use"] == "MSA",
            "autorenwal_": quote["autorenwal"],
            "no_of_payees": self._count_licenses(quote["quote_id"]),
            "implementation_charges": self._calculate_implementation_charges(
                quote["quote_id"]
            ),
            "tiered_pricing_offered__includes_platform_fee_based_pricing__": self._check_tier_pricing(
                quote["quote_id"]
            ),
            **self._calculate_arr(UUID(quote["quote_id"])),
        }

    def _extract(self, start_ts: datetime, end_ts: datetime) -> None:
        logger.info("Starting extraction for custom report")
        quotes = QuoteSelector(self.client_id).get_quotes_between(start_ts, end_ts)
        self._qt_df = DataFrame(quotes, columns=self.quote_columns)

        quote_ids = [quote["quote_id"] for quote in quotes]
        statuses = QuoteStatusSelector(self.client_id).get_quote_statuses(quote_ids)
        self._qs_df = DataFrame(statuses, columns=self.status_columns)

        form_ids = [quote["form_id"] for quote in quotes]
        form_data = EverstageFormAccessor(self.client_id).get_form_data(form_ids)
        self._qf_df = DataFrame(form_data, columns=list(self.form_mappings.keys()))

        self._qkd_map = {st["quote_id"]: st["knowledge_begin_date"] for st in statuses}
        qlis = QuoteLineItemAccessor(self.client_id).get_quote_line_items(self._qkd_map)
        self._qli_df = DataFrame(qlis, columns=self.qli_columns)
        logger.info(f"Fetched {len(quotes)} quotes and {len(qlis)} line items")

    def _transform(self) -> None:
        logger.info("Transforming data for custom report")
        if (
            self._qt_df is None
            or self._qs_df is None
            or self._qf_df is None
            or self._qli_df is None
        ):
            logger.warning("No data to transform")
            return

        logger.info("Converting data types for custom report")
        self._qt_df[["quote_id", "form_id"]] = self._qt_df[
            ["quote_id", "form_id"]
        ].astype(str)
        self._qs_df["quote_id"] = self._qs_df["quote_id"].astype(str)
        self._qf_df.rename(columns=self.form_mappings, inplace=True)

        logger.info("Joining quotes with quote statuses and form data")
        enriched_quotes = (
            self._qt_df.merge(right=self._qs_df, how="left", on="quote_id")
            .merge(right=self._qf_df, how="left", on="quote_id")
            .sort_values(by=["opportunity_id", "is_primary", "knowledge_begin_date"])
            .drop_duplicates(subset=["quote_id"])
            .dropna(subset=["opportunity_id"])
        )
        logger.info(f"Number of records after joining: {len(enriched_quotes)}")

        logger.info("Filtering based on status and removing duplicates")
        enriched_quotes.replace({nan: None, NaN: None, NaT: None}, inplace=True)
        filtered_quotes = enriched_quotes[
            ~enriched_quotes["status"].isin(self.status_filters)
        ].drop_duplicates(subset=["opportunity_id"], keep="last")
        logger.info(f"Number of records after filtering: {len(filtered_quotes)}")

        logger.info("Aggregating quote line item data")
        records = []
        for _, quote in filtered_quotes.iterrows():
            record = {}
            try:
                logger.debug(f"Processing quote: '{quote['quote_id']}'")
                record = self._generate_record(quote.to_dict())
                report_model = CustomReport(**record)
                records.append(report_model.model_dump())
            except Exception:
                logger.exception(f"Error processing quote: '{quote['quote_id']}'")
                continue

        self._report_df = DataFrame(records)
        logger.info(f"Transformed {len(self._report_df)} records")

    def _load(self) -> DataFrame:
        if self._report_df is None:
            return DataFrame()
        logger.info(f"Generating report with {len(self._report_df)} records")
        return self._report_df

    def run(self, start_ts: datetime, end_ts: datetime) -> DataFrame:
        self._extract(start_ts, end_ts)
        self._transform()
        return self._load()
