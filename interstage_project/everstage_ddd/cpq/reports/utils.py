from logging import Logger, getLogger
from os import environ

from psycopg2 import connect
from psycopg2.extensions import ISOLATION_LEVEL_REPEATABLE_READ, connection, cursor
from psycopg2.extras import RealDictCursor
from snowflake.connector import SnowflakeConnection
from snowflake.snowpark import Session

from commission_engine.accessors.client_accessor import get_client_features
from commission_engine.database.snowflake_connection import (
    create_snowpark_session,
    get_connection,
)

logger: Logger = getLogger(__name__)


def get_batch_size(client_id: int) -> int:
    client_features = get_client_features(client_id) or {}
    return client_features.get("cpq_report_etl_batch_size", 10000)


def get_postgres_connection(client_id: int) -> tuple[connection, cursor]:
    logger.debug(f"Getting postgres connection for client {client_id}")
    pg_connection = connect(
        host=environ.get("DB_HOST"),
        port=environ.get("DB_PORT"),
        dbname=environ.get("DB_NAME"),
        user=environ.get("DB_USER"),
        password=environ.get("DB_PASSWORD"),
        cursor_factory=RealDictCursor,
    )
    pg_connection.set_session(
        ISOLATION_LEVEL_REPEATABLE_READ, readonly=True, autocommit=False
    )
    pg_cursor = pg_connection.cursor()
    return pg_connection, pg_cursor


def get_snowflake_connection(client_id: int) -> tuple[SnowflakeConnection, Session]:
    logger.debug(f"Getting snowflake connection for client {client_id}")
    sf_connection = get_connection(client_id, auto_commit=False)
    sf_session = create_snowpark_session(client_id, sf_connection)
    return sf_connection, sf_session
