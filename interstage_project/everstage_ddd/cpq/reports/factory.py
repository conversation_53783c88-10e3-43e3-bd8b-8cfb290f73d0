from commission_engine.utils.general_data import ReportObject

from .strategies.quote import QuoteReportStrategy
from .strategies.quote_line_item import QuoteLineItemReportStrategy
from .strategies.quote_line_item_tier import QuoteLineItemTierReportStrategy


def get_cpq_report_strategies() -> dict[str, type]:
    return {
        ReportObject.QUOTE.value: QuoteReportStrategy,
        ReportObject.QUOTE_LINE_ITEM.value: QuoteLineItemReportStrategy,
        ReportObject.QUOTE_LINE_ITEM_TIER.value: QuoteLineItemTierReportStrategy,
    }
