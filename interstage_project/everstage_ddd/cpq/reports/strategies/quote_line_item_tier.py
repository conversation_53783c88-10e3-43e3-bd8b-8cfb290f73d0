from commission_engine.utils.general_data import ReportObject
from everstage_ddd.cpq.product_catalog.accessors.product_catalog_accessor import (
    ProductCatalogAccessor,
)
from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector

from .base_cpq_report import BaseCPQReportStrategy


class QuoteLineItemTierReportStrategy(BaseCPQReportStrategy):
    @property
    def object_id(self) -> str:
        return ReportObject.QUOTE_LINE_ITEM_TIER.value

    @property
    def accessors(self) -> list:
        return [QuoteSelector, QuoteLineItemAccessor, ProductCatalogAccessor]

    @property
    def extraction_query(self) -> str:
        return """
            SELECT
                qli.quote_id AS "quote_id",
                qli.quote_line_item_id AS "line_item_id",
                CONCAT(qli.quote_line_item_id, '#_#', tier.key) AS "tier_id",
                CONCAT(q.quote_display_id_prefix, q.quote_display_id) AS "quote_display_id",
                qli.sku AS "product_id",
                pcp.sku AS "product_code",
                qli.product_name AS "product_name",
                tier.key::NUMERIC AS "tier_number",
                (tier.value ->> 'lower_bound')::NUMERIC AS "tier_start",
                (tier.value ->> 'upper_bound')::NUMERIC AS "tier_end",
                (tier.value ->> 'quantity')::NUMERIC AS "tier_quantity",
                (tier.value ->> 'list_unit_price')::NUMERIC AS "tier_list_unit_price",
                (tier.value ->> 'net_unit_price')::NUMERIC AS "tier_net_unit_price",
                (tier.value ->> 'list_flat_price')::NUMERIC AS "tier_list_flat_price",
                (tier.value ->> 'net_flat_price')::NUMERIC AS "tier_net_flat_price",
                (tier.value ->> 'net_total')::NUMERIC AS "tier_total",
                qli.knowledge_begin_date AT TIME ZONE 'UTC' AS "last_updated_date",
                qli.is_deleted AS "deleted"
            FROM
                quote_line_item qli
            LEFT JOIN quote q ON
                q.knowledge_end_date IS NULL
                AND NOT q.is_deleted
                AND qli.client_id = q.client_id
                AND qli.quote_id = q.quote_id::text
            LEFT JOIN product_catalog_product pcp ON
                pcp.knowledge_end_date IS NULL
                AND NOT pcp.is_deleted
                AND qli.client_id = pcp.client_id
                AND qli.sku = pcp.product_id::text
            CROSS JOIN LATERAL
                jsonb_each(qli.custom_data -> 'tier_allocations') AS tier(key, value)
            WHERE
                qli.knowledge_end_date IS NULL
                AND q.client_id = '{client_id}'
                AND (qli.pricepoint_data ->> 'pricing_method') = 'tiered'
                AND qli.knowledge_begin_date > '{start_time}'
                AND qli.knowledge_begin_date <= '{end_time}'
            ORDER BY qli.temporal_id
            LIMIT {limit}
            OFFSET {offset} ;\
    """
