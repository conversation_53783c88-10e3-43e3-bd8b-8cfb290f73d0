from commission_engine.utils.general_data import ReportObject
from everstage_ddd.cpq.product_catalog.accessors.product_catalog_accessor import (
    ProductCatalogAccessor,
)
from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector

from .base_cpq_report import BaseCPQReportStrategy


class QuoteLineItemReportStrategy(BaseCPQReportStrategy):
    @property
    def object_id(self) -> str:
        return ReportObject.QUOTE_LINE_ITEM.value

    @property
    def accessors(self) -> list:
        return [QuoteSelector, QuoteLineItemAccessor, ProductCatalogAccessor]

    @property
    def extraction_query(self) -> str:
        return """
            SELECT
                qli.quote_id AS "quote_id",
                qli.quote_line_item_id AS "line_item_id",
                CONCAT(q.quote_display_id_prefix, q.quote_display_id) AS "quote_display_id",
                qli.sku AS "product_id",
                qli.pricepoint_data->>'pricepoint_id' AS "pricepoint_id",
                pcp.sku AS "product_code",
                qli.product_name AS "product_name",
                pcp.description AS "product_description",
                CASE LOWER(pcp.category)
                    WHEN 'services' THEN 'Services'
                    WHEN 'software' THEN 'Software'
                    WHEN 'add-ons' THEN 'Add-ons'
                    WHEN 'others' THEN 'Others'
                    ELSE 'Unknown'
                END AS "category",
                qli.unit_of_measure AS "charged_on",
                q.quote_currency AS "currency",
                CASE LOWER(qli.pricepoint_data->>'pricing_method')
                    WHEN 'perunit' THEN 'Per Unit'
                    WHEN 'tiered' THEN 'Tiered'
                    WHEN 'flatfee' THEN 'Flat Fee'
                    WHEN 'volume' THEN 'Volume'
                    ELSE 'Unknown'
                END AS "pricing_model",
                CASE LOWER(qli.billing_type)
                    WHEN 'recurring' THEN 'Recurring'
                    WHEN 'one_time' THEN 'One-time'
                    ELSE 'Unknown'
                END AS "billing_type",
                CASE LOWER(qli.billing_frequency)
                    WHEN 'one-time' THEN 'One-time'
                    WHEN 'monthly' THEN 'Monthly'
                    WHEN 'quarterly' THEN 'Quarterly'
                    WHEN 'half-yearly' THEN 'Half-yearly'
                    WHEN 'annual' THEN 'Annual'
                    ELSE 'Unknown'
                END AS "billing_frequency",
                qli.phase_name AS "phase_name",
                qli.start_date AT TIME ZONE 'UTC' AS "phase_start_date",
                qli.end_date AT TIME ZONE 'UTC' AS "phase_end_date",
                qli.quantity AS "quantity",
                qli.list_unit_price AS "list_price",
                qli.net_unit_price AS "net_price",
                qli.discount_percent AS "discount_percentage",
                qli.net_total AS "total",
                qli.knowledge_begin_date AT TIME ZONE 'UTC' AS "last_updated_date",
                qli.is_deleted AS "deleted"
            FROM
                quote_line_item qli
            LEFT JOIN quote q ON
                q.knowledge_end_date IS NULL
                AND NOT q.is_deleted
                AND qli.client_id = q.client_id
                AND qli.quote_id = q.quote_id::text
            LEFT JOIN product_catalog_product pcp ON
                pcp.knowledge_end_date IS NULL
                AND NOT pcp.is_deleted
                AND qli.client_id = pcp.client_id
                AND qli.sku = pcp.product_id::text
            WHERE
                qli.knowledge_end_date IS NULL
                AND q.client_id = '{client_id}'
                AND qli.knowledge_begin_date > '{start_time}'
                AND qli.knowledge_begin_date <= '{end_time}'
            ORDER BY qli.temporal_id
            LIMIT {limit}
            OFFSET {offset} ;\
    """
