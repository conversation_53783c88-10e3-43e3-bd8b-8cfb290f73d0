from commission_engine.utils.general_data import ReportObject
from everstage_ddd.cpq.forms.accessors.form_accessor import EverstageFormAccessor
from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)
from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
    QuoteStatusSelector,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor

from .base_cpq_report import BaseCPQReportStrategy


class QuoteReportStrategy(BaseCPQReportStrategy):
    @property
    def object_id(self) -> str:
        return ReportObject.QUOTE.value

    @property
    def accessors(self) -> list:
        return [
            QuoteSelector,
            EverstageFormAccessor,
            QuoteStatusSelector,
            QuoteLineItemAccessor,
            EmployeeAccessor,
        ]

    @property
    def extraction_query(self) -> str:
        return """
            SELECT
                q.quote_id AS "quote_id",
                CONCAT(q.quote_display_id_prefix, q.quote_display_id) AS "display_id",
                q.quote_name AS "name",
                ef.form_id AS "form_id",
                ef.form_spec->>'label' AS "form_name",
                q.quote_currency AS "currency",
                qs.status AS "status",
                CONCAT(e.first_name, ' ', e.last_name) AS "owner_name",
                q.owner_id AS "owner_email",
                q.valid_till AT TIME ZONE 'UTC' AS "valid_till",
                q.opportunity_id AS "opportunity_id",
                q.account_id AS "account_id",
                q.contact_id AS "contact_id",
                q.start_date AT TIME ZONE 'UTC' AS "subscription_start_date",
                q.end_date AT TIME ZONE 'UTC' AS "subscription_end_date",
                q.duration_value AS "duration",
                CASE LOWER(q.duration_type)
                    WHEN 'forever' THEN 'Forever'
                    WHEN 'months' THEN 'Months'
                    WHEN 'years' THEN 'Years'
                    ELSE 'Unknown'
                END AS "duration_type",
                q.list_quote_total AS "list_total",
                q.net_quote_total AS "net_total",
                q.discount_amount AS "discount",
                q.is_primary AS "primary_quote",
                q.closed_date AT TIME ZONE 'UTC' AS "won_date",
                q.created_at AT TIME ZONE 'UTC' AS "created_date",
                lu.last_updated_date AT TIME ZONE 'UTC' AS "last_updated_date",
                q.is_deleted AS "deleted"
            FROM
                quote q
            LEFT JOIN everstage_form ef ON
                ef.knowledge_end_date IS NULL
                AND NOT ef.is_deleted
                AND q.client_id = ef.client_id
                AND q.form_id = ef.form_id
            LEFT JOIN quote_status qs ON
                qs.knowledge_end_date IS NULL
                AND NOT qs.is_deleted
                AND q.client_id = qs.client_id
                AND q.quote_id = qs.quote_id
            LEFT JOIN employee e ON
                e.knowledge_end_date IS NULL
                AND NOT e.is_deleted
                AND q.client_id = e.client_id
                AND q.owner_id = e.employee_email_id
            LEFT JOIN LATERAL (
                SELECT
                    MAX(qli.knowledge_begin_date) AS knowledge_begin_date
                FROM
                    quote_line_item qli
                WHERE
                    qli.knowledge_end_date IS NULL
                    AND NOT qli.is_deleted
                    AND q.client_id = qli.client_id
                    AND q.quote_id::text = qli.quote_id
            ) AS qli ON TRUE
            LEFT JOIN LATERAL (
                SELECT GREATEST(
                    q.knowledge_begin_date,
                    ef.knowledge_begin_date,
                    qs.knowledge_begin_date,
                    qli.knowledge_begin_date
                ) AS last_updated_date
            ) AS lu ON TRUE
            WHERE
                q.knowledge_end_date IS NULL
                AND q.client_id = '{client_id}'
                AND lu.last_updated_date > '{start_time}'
                AND lu.last_updated_date <= '{end_time}'
            ORDER BY q.temporal_id
            LIMIT {limit}
            OFFSET {offset} ;\
    """
