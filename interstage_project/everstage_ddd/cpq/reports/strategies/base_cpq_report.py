from abc import abstractmethod
from datetime import datetime
from json import dumps
from logging import Logger, getLogger
from uuid import UUID, uuid4

from django.utils.timezone import make_aware, now
from psycopg2.extras import RealDictRow

from commission_engine.accessors.etl_housekeeping_accessor import (
    ReportETLStatusAccessor,
)
from commission_engine.accessors.ever_object_accessor import EverObjectAccessor
from commission_engine.services.data_sources.abstract_strategy import AbsDataStrategy
from commission_engine.utils.general_utils import log_time_taken
from commission_engine.utils.report_utils import get_report_object_data_table_name
from everstage_ddd.snowflake_reports.base_snowflake_report import BaseSnowflakeReport
from everstage_ddd.snowflake_reports.snowflake_report_utils import (
    REPORT_OBJECT_DATA_COLUMNS,
    ReportSyncMode,
    get_last_successful_sync_time,
)
from interstage_project.threadlocal_log_context import (
    get_threadlocal_context,
    set_threadlocal_context,
)

from ..utils import get_batch_size, get_postgres_connection, get_snowflake_connection

logger: Logger = getLogger(__name__)


class BaseCPQReportStrategy(BaseSnowflakeReport, AbsDataStrategy):
    """
    Steps to create a new CPQ Report Object:

    1. Registration:
    - Add a new entry to ReportObject in general_data.py.
    - Add the object to the SYNC_OBJECT.REPORT_OBJECT_SYNC mapping.
    - Update the frontend constants in data-sync-view/ReportEtl.js.

    2. Implementation:
    - Create a new class that inherits from BaseCPQReportStrategy.
    - Implement all required abstract methods for the new object.
    - Expose the is_data_stale() and run_report_etl() methods.

    3. Integration:
    - Include the new strategy in reports/factory.py.
    - Update populate_cpq_report_objects.sql to add ever_object metadata.
    - Write unit tests for the new strategy in test_cpq_reports.py.
    """

    def __init__(  # noqa: PLR0913
        self,
        client_id: int,
        e2e_sync_run_id: UUID | None = None,
        sync_run_id: UUID | None = None,
        sync_mode: ReportSyncMode | None = None,
        knowledge_date: datetime | None = None,
        log_context: dict | None = None,
    ) -> None:
        self.client_id: int = client_id
        self.e2e_sync_run_id: UUID = e2e_sync_run_id or uuid4()
        self.sync_run_id: UUID = sync_run_id or uuid4()
        self.sync_mode: ReportSyncMode = sync_mode or ReportSyncMode.CHANGES
        self.start_time: datetime = (
            make_aware(datetime.min)
            if self.sync_mode == ReportSyncMode.ALL
            else get_last_successful_sync_time(self.client_id, self.object_id)
        )
        self.end_time: datetime = knowledge_date or now()
        self.batch_size: int = get_batch_size(self.client_id)
        self.object_type: str = "report_object"
        self.primary_keys: tuple[str, ...] = tuple(
            EverObjectAccessor().get_primary_key(object_id=self.object_id)
        )
        self.rod_table: str = get_report_object_data_table_name(
            self.client_id, self.object_id
        )
        self.rod_columns: str = f"({', '.join(REPORT_OBJECT_DATA_COLUMNS)})"
        _ = self._set_log_context(log_context or {}) if e2e_sync_run_id else None

    @property
    @abstractmethod
    def object_id(self) -> str:
        pass

    @property
    @abstractmethod
    def accessors(self) -> list:
        pass

    @property
    @abstractmethod
    def extraction_query(self) -> str:
        pass

    @property
    def creation_query(self) -> str:
        return """
            CREATE TABLE IF NOT EXISTS {table} 
            (
                temporal_id NUMBER(38,0) NOT NULL AUTOINCREMENT START 1 INCREMENT 1 ORDER,
                knowledge_begin_date TIMESTAMP_NTZ,
                knowledge_end_date TIMESTAMP_NTZ,
                is_deleted BOOLEAN,
                additional_details VARIANT,
                object_id VARCHAR,
                object_type VARCHAR,
                row_key VARCHAR,
                snapshot_key VARCHAR,
                data VARIANT,
                client_id NUMBER,
                PRIMARY KEY (temporal_id)
            ) 
            CLUSTER BY (knowledge_end_date) ;\
        """

    @property
    def truncation_query(self) -> str:
        return """
            UPDATE {table} AS rod
            SET knowledge_end_date = '{knowledge_end_date}'
            WHERE
                rod.client_id = {client_id}
                AND rod.object_id = '{object_id}'
                AND rod.object_type = '{object_type}'
                AND rod.knowledge_end_date IS NULL
                AND NOT rod.is_deleted ;\
        """

    @property
    def invalidation_query(self) -> str:
        return """
            UPDATE {table} AS rod
            SET knowledge_end_date = '{knowledge_end_date}'
            FROM (
                SELECT t.row_key
                FROM VALUES
                {values}
                AS t(row_key, snapshot_key, data)
            ) AS tmp
            WHERE
                rod.client_id = {client_id}
                AND rod.object_id = '{object_id}'
                AND rod.object_type = '{object_type}'
                AND rod.knowledge_end_date IS NULL
                AND NOT rod.is_deleted
                AND rod.row_key = tmp.row_key ;\
        """

    @property
    def insertion_query(self) -> str:
        return """
            INSERT INTO 
                {table} {columns}
            SELECT
                '{knowledge_begin_date}' AS knowledge_begin_date,
                NULL AS knowledge_end_date,
                FALSE AS is_deleted,
                NULL AS additional_details,
                '{object_id}' AS object_id,
                '{object_type}' AS object_type,
                t.row_key AS row_key,
                t.snapshot_key AS snapshot_key,
                PARSE_JSON(t.data) AS data,
                '{client_id}' AS client_id
            FROM VALUES
                {values}
            AS t(row_key, snapshot_key, data) ;\
        """

    def _set_log_context(self, log_context: dict) -> None:
        set_threadlocal_context(
            {
                **get_threadlocal_context(),
                **log_context,
                "client_id": self.client_id,
                "e2e_sync_run_id": self.e2e_sync_run_id,
                "sync_run_id": self.sync_run_id,
                "object_id": self.object_id,
                "object_type": self.object_type,
                "sync_mode": self.sync_mode,
                "batch_size": self.batch_size,
                "start_time": self.start_time,
                "end_time": self.end_time,
            }
        )

    def _open_connections(self) -> None:
        self.pg_connection, self.pg_cursor = get_postgres_connection(self.client_id)
        self.sf_connection, self.sf_session = get_snowflake_connection(self.client_id)

    def _close_connections(self) -> None:
        self.sf_session.close()
        self.sf_connection.close()
        self.pg_cursor.close()
        self.pg_connection.close()

    def _create_table(self) -> None:
        query = self.creation_query.format(table=self.rod_table)
        logger.debug("SQL: Table Creation", extra={"query": query})
        self.sf_session.sql(query).collect()
        logger.info("Created report_object_data table")

    def _truncate_table(self) -> None:
        if self.sync_mode != ReportSyncMode.ALL:
            return
        query = self.truncation_query.format(
            table=self.rod_table,
            knowledge_end_date=self.end_time.isoformat(),
            object_id=self.object_id,
            object_type=self.object_type,
            client_id=self.client_id,
        )
        logger.debug("SQL: Table Truncation", extra={"query": query})
        self.sf_session.sql(query).collect()
        logger.info("Truncated report_object_data table")

    def _extract_data(self, limit: int, offset: int) -> list:
        query = self.extraction_query.format(
            client_id=self.client_id,
            start_time=self.start_time.isoformat(),
            end_time=self.end_time.isoformat(),
            limit=limit,
            offset=offset,
        )
        logger.debug("SQL: Data Extraction", extra={"query": query})
        self.pg_cursor.execute(query)
        extracted_records = self.pg_cursor.fetchall()
        logger.info(f"Extracted {len(extracted_records)} records")
        return extracted_records

    def _transform_data(self, records: list[RealDictRow]) -> list[str]:
        if len(records) == 0:
            return []
        transformed_records = []
        for record in records:
            row_key = "#_#".join(str(record[key]) for key in self.primary_keys)
            snapshot_key = row_key
            data = dumps(record, default=str).replace("'", r"''").replace("\\", r"\\")
            transformed_records.append(f"('{row_key}', '{snapshot_key}', '{data}')")
        logger.info(f"Transformed {len(transformed_records)} records")
        return transformed_records

    def _invalidate_data(self, records: list[str]) -> int:
        query = self.invalidation_query.format(
            table=self.rod_table,
            knowledge_end_date=self.end_time.isoformat(),
            object_id=self.object_id,
            object_type=self.object_type,
            client_id=self.client_id,
            values=",\n\t\t".join(records),
        )
        logger.debug("SQL: Data Invalidation", extra={"query": query})
        result = self.sf_session.sql(query).collect()
        return int(result[0][0]) if result else 0  # type: ignore

    def _insert_data(self, records: list[str]) -> int:
        query = self.insertion_query.format(
            table=self.rod_table,
            columns=self.rod_columns,
            knowledge_begin_date=self.end_time.isoformat(),
            object_id=self.object_id,
            object_type=self.object_type,
            client_id=self.client_id,
            values=",\n\t\t".join(records),
        )
        logger.debug("SQL: Data Insertion", extra={"query": query})
        result = self.sf_session.sql(query).collect()
        return int(result[0][0]) if result else 0  # type: ignore

    def _load_data(self, records: list[str]) -> int:
        if len(records) == 0:
            return 0
        num_invalidated = self._invalidate_data(records)
        logger.info(f"Invalidated {num_invalidated} records")
        num_inserted = self._insert_data(records)
        logger.info(f"Inserted {num_inserted} records")
        return num_inserted

    def batch_etl(self) -> None:
        _, _ = self._create_table(), self._truncate_table()
        done, limit, offset = False, self.batch_size, 0
        while not done:
            logger.debug(f"Processing Page-{offset // limit + 1}")
            extracted_records = self._extract_data(limit, offset)
            transformed_records = self._transform_data(extracted_records)
            num_loaded = self._load_data(transformed_records)
            if num_loaded < limit:
                done = True
            else:
                offset += limit

    # BaseSnowflakeReport
    @log_time_taken()
    def run_report_etl(self, _) -> None:
        try:
            self._open_connections()
            logger.info(f"BEGIN: Report ETL - {self.object_id}")
            self.batch_etl()
            logger.info(f"END: Report ETL - {self.object_id}")
        except Exception:
            logger.exception(f"ERROR: Report ETL - {self.object_id}")
            self.sf_connection.rollback()
            logger.debug("ROLLBACK: Snowflake transaction")
            self._close_connections()
            raise
        else:
            self.sf_connection.commit()
            logger.debug("COMMIT: Snowflake transaction")
            self._close_connections()

    # AbsDataStrategy
    def get_data(self, **kwargs) -> None:
        pass

    # AbsDataStrategy
    def get_latest_record(self, **kwargs) -> None:
        pass

    # AbsDataStrategy
    def is_data_stale(self, **kwargs) -> bool:  # noqa: ARG002
        last_success_obj = ReportETLStatusAccessor(
            client_id=self.client_id
        ).get_last_success_obj(object_id=self.object_id)
        last_sync_time = (
            last_success_obj.sync_completion_time if last_success_obj else None
        )
        return (last_sync_time is None) or any(
            accessor(self.client_id).does_record_exist_after_date(last_sync_time)
            for accessor in self.accessors
        )
