------------------------------------------------------------------------------------------------------------------------

-- interstage_clients

INSERT INTO public.interstage_clients (client_id, name, domain, logo_url, auth_connection_name, connection_type, base_currency, fiscal_start_month, secondary_calculator, client_notification, cpq_client_notification, payee_notification, meta_info, logo_s3_path, statement_logo_url, statement_logo_s3_path, time_zone, client_features, selected_languages, is_deleted) VALUES (31617, 'CPQ', 'cpq.com', 'https://d177w6gi8jmdvz.cloudfront.net/Logos/Everstage_Inc.', 'email-password', 'email-password', 'USD', 1, NULL, true, false, false, '{"is_live": false, "is_test": false, "created_at": "2024-10-09T12:31:59.653Z", "created_by": "<EMAIL>", "updated_at": "2024-10-25T11:26:33.143Z", "updated_by": "<EMAIL>", "oauth_users": [{"email": "<EMAIL>", "password": "HJnG:~8wk"}], "super_admin_user": {"email": "<EMAIL>", "password": "hcMJ/6<8u"}}', '', NULL, '', 'UTC', '{"modules": ["CPQ"], "show_roles": true, "datasheet_v2": true, "show_chatgpt": false, "show_metrics": false, "enable_everai": false, "global_search": {"enabled": true, "modules": {"users": true, "payouts": true}}, "settlement_v2": true, "show_forecast": false, "crm_hyperlinks": false, "is_db_comm_etl": true, "salesforce_env": "production", "warn_on_unlock": "NONE", "crystal_version": "3", "custom_calendar": false, "evaluation_mode": "vectorize", "hide_categories": [], "is_upstream_etl": true, "delete_approvers": ["<EMAIL>", "<EMAIL>"], "take_ds_snapshot": true, "documentation_url": "", "edit_locked_quota": false, "manager_rollup_ed": false, "subscription_plan": "BASIC", "help_doc_user_role": [], "plan_summary_model": "gpt-4-1106-preview", "show_simulation_v2": false, "show_statements_v2": true, "task_orchestration": "celery-dynamic-chaining", "enable_custom_theme": false, "payout_snapshot_etl": false, "show_g2_review_form": "Off", "show_statements_pdf": true, "calc_fields_strategy": "udf", "celery_sync_strategy": "group", "show_advanced_filter": true, "show_data_sources_v2": true, "upstream_etl_version": "v1", "expressionbox_version": "v2", "quota_effective_dated": false, "run_settlement_report": "if_needed", "show_approval_feature": true, "show_commission_buddy": true, "show_return_v1_button": false, "split_summation_to_li": false, "enable_ever_comparison": true, "commission_plan_version": "v2", "enable_custom_workflows": false, "enable_hris_integration": false, "show_commission_percent": false, "show_superset_dashboard": false, "use_upstream_timestamps": true, "chrome_extension_enabled": false, "enable_slack_integration": false, "is_report_object_sharded": true, "show_realtime_extraction": true, "avoid_iframe_in_contracts": false, "is_new_frozen_payroll_etl": true, "show_datasheet_permission": true, "use_aggrid_for_pdf_export": true, "enable_concurrent_sessions": true, "enable_support_user_access": false, "insert_meta_data_to_vec_db": false, "profile_picture_permission": "ALL", "enable_ms_teams_integration": false, "expose_comm_reports_in_plan": false, "show_salesforce_integration": true, "snapshot_data_for_statements": false, "calc_fields_lambda_chunk_size": 100000, "show_custom_object_permission": true, "use_multi_engine_stormbreaker": true, "databook_expressionbox_version": "v2", "enable_rounding_in_tier_functions": false, "show_get_user_property_commission": false, "allow_annual_quota_effective_dated": false, "generate_associated_datasheets_only": false, "object_knowledge_date_query_strategy": "postgres", "allow_adjustments_to_frozen_commission": false, "crystal_custom_calendar_future_periods": 6, "email_statement_for_manage_payouts_role": false}', '[]', false);

SELECT pg_catalog.setval('public.interstage_clients_client_id_seq', 31617, true);

------------------------------------------------------------------------------------------------------------------------

-- variable_data_type

INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (1, 'Integer', '{}', '[6, 9, 10, 11, 13]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (2, 'Date', '{}', '[]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (3, 'Boolean', '{}', '[]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (4, 'String', '{}', '[12, 14]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (5, 'Object', '{}', '[]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (6, 'Percentage', '{}', '[1, 9, 10, 11, 13]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (7, 'LeftBracket', '{}', '[1, 2, 3, 4, 5, 6, 7, 9, 10, 11]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (8, 'RightBracket', '{}', '[1, 2, 3, 4, 5, 6, 8, 9, 10, 11]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (9, 'DayDuration', '{}', '[1, 6, 10, 11, 13]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (10, 'MinuteDuration', '{}', '[1, 6, 9, 11, 13]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (11, 'SecondDuration', '{}', '[1, 6, 9, 10, 13]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (12, 'Email', '{}', '[4, 14]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (13, 'IntArray', '{}', '[1, 6, 9, 10, 11]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (14, 'StringArray', '{}', '[4, 12]');
INSERT INTO public.variable_data_type (id, data_type, data_type_meta, compatible_type_ids) VALUES (15, 'Hierarchy', '{}', '[4, 12]');

------------------------------------------------------------------------------------------------------------------------

-- access_token_config

INSERT INTO public.access_token_config (client_id, knowledge_begin_date, knowledge_end_date, access_token_config_id, access_type, payload_type, api_access_key, access_token_url, access_request_body, jwt_data, service_name, domain, connection_name, connection_status, connection_type, created_on, additional_data) VALUES (31617, '2025-01-08 17:57:48.394479+05:30', NULL, 1, 'API Key', 'params', '********************************************', 'https://api.hubapi.com/oauth/v2/private-apps/get/access-token-info', NULL, NULL, 'hubspot', 'https://api.hubapi.com', 'Hubspot', 'CONNECTED', NULL, '2025-01-08 17:57:48.40809+05:30', NULL);
INSERT INTO public.access_token_config (client_id, knowledge_begin_date, knowledge_end_date, access_token_config_id, access_type, payload_type, api_access_key, access_token_url, access_request_body, jwt_data, service_name, "domain", connection_name, connection_status, connection_type, created_on, additional_data) VALUES(31617, '2025-04-01 11:05:42.952', NULL, 2, 'UN-PWD', 'params', NULL, 'https://login.salesforce.com/services/oauth2/token', '{"password": "SalesForce", "username": "<EMAIL>", "client_id": "3MVG9GCMQoQ6rpzSVJftBznuS_J5Zw.Hp82GRcrb9ThZVwcosRtNLqt5t1MS9ASGmfmQX8vpTaEuQcDruCRvV", "grant_type": "password", "client_secret": "****************************************************************"}'::jsonb, NULL, 'salesforce', 'https://everstage-c-dev-ed.develop.my.salesforce.com', 'Salesforce', 'CONNECTED', NULL, '2025-04-01 11:05:42.976', NULL);

------------------------------------------------------------------------------------------------------------------------

-- api_access_config

INSERT INTO public.api_access_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, request_url, request_type, request_body, request_header, access_token_config_id, integration, response_key, additional_data, integration_id) VALUES (31617, '2024-10-09 18:49:36.093946+05:30', NULL, 'companies', 'https://api.hubapi.com/crm/v3/objects/companies/search', 'post', '{"last_modified_date_column": "hs_lastmodifieddate"}', '{"Content-type": "application/json", "Authorization": "Bearer {access_token}"}', 1, 'hubspot', 'results', '{"api_type": "search"}', '************************************');
INSERT INTO public.api_access_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, request_url, request_type, request_body, request_header, access_token_config_id, integration, response_key, additional_data, integration_id) VALUES (31617, '2024-10-09 18:49:36.105381+05:30', NULL, 'companies_delete', 'https://api.hubapi.com/crm/v3/objects/companies', 'get', NULL, '{"Content-type": "application/json", "Authorization": "Bearer {access_token}"}', 1, 'hubspot', 'results', '{"api_type": "list"}', '************************************');
INSERT INTO public.api_access_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, request_url, request_type, request_body, request_header, access_token_config_id, integration, response_key, additional_data, integration_id) VALUES (31617, '2024-10-09 18:53:05.285132+05:30', NULL, 'contacts', 'https://api.hubapi.com/crm/v3/objects/contacts/search', 'post', '{"last_modified_date_column": "lastmodifieddate"}', '{"Content-type": "application/json", "Authorization": "Bearer {access_token}"}', 1, 'hubspot', 'results', '{"api_type": "search"}', '************************************');
INSERT INTO public.api_access_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, request_url, request_type, request_body, request_header, access_token_config_id, integration, response_key, additional_data, integration_id) VALUES (31617, '2024-10-09 18:53:05.28941+05:30', NULL, 'contacts_delete', 'https://api.hubapi.com/crm/v3/objects/contacts', 'get', NULL, '{"Content-type": "application/json", "Authorization": "Bearer {access_token}"}', 1, 'hubspot', 'results', '{"api_type": "list"}', '************************************');
INSERT INTO public.api_access_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, request_url, request_type, request_body, request_header, access_token_config_id, integration, response_key, additional_data, integration_id) VALUES (31617, '2024-10-09 19:07:34.454528+05:30', NULL, 'deals', 'https://api.hubapi.com/crm/v3/objects/deals/search', 'post', '{"last_modified_date_column": "hs_lastmodifieddate"}', '{"Content-type": "application/json", "Authorization": "Bearer {access_token}"}', 1, 'hubspot', 'results', '{"api_type": "search"}', '************************************');
INSERT INTO public.api_access_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, request_url, request_type, request_body, request_header, access_token_config_id, integration, response_key, additional_data, integration_id) VALUES (31617, '2024-10-09 19:07:34.461455+05:30', NULL, 'deals_delete', 'https://api.hubapi.com/crm/v3/objects/deals', 'get', NULL, '{"Content-type": "application/json", "Authorization": "Bearer {access_token}"}', 1, 'hubspot', 'results', '{"api_type": "list"}', '************************************');

------------------------------------------------------------------------------------------------------------------------

-- approval_rule_group

INSERT INTO public.approval_rule_group (knowledge_begin_date, knowledge_end_date, is_deleted, created_at, additional_details, rule_group_id, group_name, stage_count, form_builder_id, created_by, group_description, notify_on_reject, notify_on_approve, status, client_id) VALUES ('2024-11-22 10:35:19.578783+05:30', NULL, false, '2024-11-22 10:35:19.578783+05:30', '{"approval_matrix_book": "999ba40c-a92e-4e74-9af0-d9b98bc684bd", "approval_matrix_sheet": "9c8d7eef-214f-43e7-8aa0-93a7728fa23a"}', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'Everstage approvals', 2, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', '<EMAIL>', 'Everstage rules for approvals', '{"users": [], "groups": [], "dynamic": ["payee", "all_approvers"]}', '{"users": [], "groups": [], "dynamic": ["payee"]}', 'active', 31617);

------------------------------------------------------------------------------------------------------------------------

-- approval_rules

INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.664904+05:30', NULL, false, NULL, '855974a4-fd29-4c8d-9ca7-a898f761653e', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "field29", "name": "field29", "data_type": "String", "system_name": "field29"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Yes", "args": ["String", "Yes"], "name": "Yes", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}]}], "trigger": "HasChanged(field29)", "source_fields": ["field29"], "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'ASC606 module offering approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.640066+05:30', NULL, false, NULL, '051a6ac1-3ae5-451d-b734-8de823f4daa5', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field49", "name": "field49", "data_type": "String", "system_name": "field49"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["101-250", "251-1000", ">1000"], "args": ["StringArray", ["101-250", "251-1000", ">1000"]], "name": "Array - 101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field50", "name": "field50", "data_type": "String", "system_name": "field50"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["251-1000", ">1000"], "args": ["StringArray", ["251-1000", ">1000"]], "name": "Array - 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field49)", "is_qli_rule": true, "source_fields": ["field49", "field50"], "qli_rule_fields": {"field49": {"sku": "SUPPORT", "price_column": "prorated_net_total", "matrix_column": "vp_price_threshold"}, "field50": {"sku": "SUPPORT", "price_column": "prorated_net_total", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Support discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.657803+05:30', NULL, false, NULL, '2bf8bd82-c208-46db-89ce-866783ccc1ff', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field27", "name": "field27", "data_type": "String", "system_name": "field27"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Yes", "args": ["String", "Yes"], "name": "Yes", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "CONNECTOR", "args": ["String", "CONNECTOR"], "name": "CONNECTOR", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "discount_percent", "name": "discount_percent", "data_type": "Integer", "system_name": "discount_percent"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": 100, "args": ["Integer", 100], "name": "100", "data_type": "Integer"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field27)", "source_fields": ["field27"], "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Connectors waive approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.652218+05:30', NULL, false, NULL, '3c2f6acc-db5b-407e-99d1-fb3b2c9b2450', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "flat_fee", "args": ["String", "Flat Fee"], "name": "Flat fee", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field45", "name": "field45", "data_type": "String", "system_name": "field45"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "NOTIN", "name": "NOTIN"}, "token_type": "OPERATORS"}, {"token": {"key": [">20", "21-100", "251-1000", ">1000"], "args": ["StringArray", [">20", "21-100", "251-1000", ">1000"]], "name": "Array - >20,21-100,251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field45)", "is_qli_rule": true, "source_fields": ["field45", "field46"], "qli_rule_fields": {"field45": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "vp_price_threshold"}, "field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Platform fee discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.702227+05:30', NULL, false, NULL, 'fc664678-6246-41a3-adaa-9e48cf7b432c', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Tiered", "args": ["String", "Tiered"], "name": "Tiered", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.show_future_tiers", "name": "pricepoint_data.show_future_tiers", "data_type": "Boolean", "system_name": "pricepoint_data.show_future_tiers"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "True", "args": ["Boolean", "True"], "name": "True", "data_type": "Boolean"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field45", "name": "field45", "data_type": "String", "system_name": "field45"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field45)", "is_qli_rule": true, "source_fields": ["field45", "field46"], "qli_rule_fields": {"field45": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "vp_price_threshold"}, "field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "vp_price_threshold"}}, "is_show_all_tiers": true, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Low price quoted for future pricing tier', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.670467+05:30', NULL, false, NULL, 'd788ec65-e154-417c-a829-a216b2ee84c9', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Support", "args": ["String", "Support"], "name": "Support", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "net_unit_price", "name": "net_unit_price", "data_type": "Integer", "system_name": "net_unit_price"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": 0, "args": ["Integer", 0], "name": "0", "data_type": "Integer"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field23", "name": "field23", "data_type": "String", "system_name": "field23"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Exclude (No Limit)", "args": ["String", "Exclude (No Limit)"], "name": "Exclude (No Limit)", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "summary.recurring_total", "name": "summary.recurring_total", "data_type": "Integer", "system_name": "summary.recurring_total"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "<", "name": "<"}, "token_type": "OPERATORS"}, {"token": {"key": 20000, "args": ["Integer", 20000], "name": "20000", "data_type": "Integer"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field23)", "source_fields": ["field23"], "destination_fields": []}', '{"users": [], "groups": ["4b9bca18-a7ed-467b-b8e1-aabe7dea81c4"], "dynamic": []}', 'Support is waived without 100hr limit for small deal (<20K ARR)', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.705717+05:30', NULL, false, NULL, '0cb0bdeb-3200-4ddc-aa8c-d3566bff1d5f', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Tiered", "args": ["String", "Tiered"], "name": "Tiered", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - >20,21-100,101-250,251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field46)", "is_qli_rule": true, "source_fields": ["field46"], "qli_rule_fields": {"field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Tier pricing discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.719585+05:30', NULL, false, NULL, '6ac119e6-62bf-46b8-8aa2-85b88b4df271', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Tiered", "args": ["String", "Tiered"], "name": "Tiered", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.show_future_tiers", "name": "pricepoint_data.show_future_tiers", "data_type": "Boolean", "system_name": "pricepoint_data.show_future_tiers"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "True", "args": ["Boolean", "True"], "name": "True", "data_type": "Boolean"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field46)", "is_qli_rule": true, "source_fields": ["field46"], "qli_rule_fields": {"field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "is_show_all_tiers": true, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Low price quoted for future pricing tier', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.715314+05:30', NULL, false, NULL, 'fcf428a1-847c-40e8-b0d0-9f5712ce6d20', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["251-1000", ">1000"], "args": ["StringArray", ["251-1000", ">1000"]], "name": "Array - 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field46)", "is_qli_rule": true, "source_fields": ["field46"], "qli_rule_fields": {"field46": {"sku": "PLATFORM", "price_column": "quote_duration", "matrix_column": "ceo_duration_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Contract length aprpoval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.697136+05:30', NULL, false, NULL, '2a622808-2fd5-4421-801b-376c276307e0', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field32", "name": "field32", "data_type": "String", "system_name": "field32"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "!=", "name": "!="}, "token_type": "OPERATORS"}, {"token": {"key": "30", "args": ["String", "30"], "name": "30", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "field32", "name": "field32", "data_type": "String", "system_name": "field32"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "!=", "name": "!="}, "token_type": "OPERATORS"}, {"token": {"key": "empty", "args": ["String", ""], "name": "empty", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field32)", "source_fields": ["field32"], "destination_fields": []}', '{"users": [], "groups": ["4b9bca18-a7ed-467b-b8e1-aabe7dea81c4"], "dynamic": []}', 'Credit terms higher than 30 days', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.717434+05:30', NULL, false, NULL, '9a4e894b-d04a-4742-96fe-1f2a3b954ea6', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field30", "name": "field30", "data_type": "String", "system_name": "field30"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "!=", "name": "!="}, "token_type": "OPERATORS"}, {"token": {"key": "Annual Upfront", "args": ["String", "Annual Upfront"], "name": "Annual Upfront", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "field30", "name": "field30", "data_type": "String", "system_name": "field30"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "!=", "name": "!="}, "token_type": "OPERATORS"}, {"token": {"key": "empty", "args": ["String", ""], "name": "empty", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field30)", "source_fields": ["field30"], "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Billing frequency is not annual upfront', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.667964+05:30', NULL, false, NULL, '0f51e2c6-89a1-454b-90ea-6080335321c9', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field45", "name": "field45", "data_type": "String", "system_name": "field45"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["101-250", "251-1000", ">1000"], "args": ["StringArray", ["101-250", "251-1000", ">1000"]], "name": "Array - 101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["251-1000", ">1000"], "args": ["StringArray", ["251-1000", ">1000"]], "name": "Array - 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field45)", "is_qli_rule": true, "source_fields": ["field45", "field46"], "qli_rule_fields": {"field45": {"sku": "PLATFORM", "price_column": "quote_duration", "matrix_column": "vp_duration_threshold"}, "field46": {"sku": "PLATFORM", "price_column": "quote_duration", "matrix_column": "ceo_duration_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Contract length aprpoval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.700363+05:30', NULL, false, NULL, '50c5df6a-7dd0-4c99-9548-ae7f67989ae1', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field47", "name": "field47", "data_type": "String", "system_name": "field47"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["101-250", "251-1000", ">1000"], "args": ["StringArray", ["101-250", "251-1000", ">1000"]], "name": "Array - 101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field48", "name": "field48", "data_type": "String", "system_name": "field48"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["251-1000", ">1000"], "args": ["StringArray", ["251-1000", ">1000"]], "name": "Array - 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field47)", "is_qli_rule": true, "source_fields": ["field47", "field48"], "qli_rule_fields": {"field47": {"sku": "IMPLEMENTATION", "price_column": "prorated_net_total", "matrix_column": "vp_price_threshold"}, "field48": {"sku": "IMPLEMENTATION", "price_column": "prorated_net_total", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Implementation fee discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.712724+05:30', NULL, false, NULL, '3261b255-4c24-4831-8c5d-3797a47f19d9', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field48", "name": "field48", "data_type": "String", "system_name": "field48"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["251-1000", ">1000"], "args": ["StringArray", ["251-1000", ">1000"]], "name": "Array - 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field48)", "is_qli_rule": true, "source_fields": ["field48"], "qli_rule_fields": {"field48": {"sku": "IMPLEMENTATION", "price_column": "prorated_net_total", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Implementation fee discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.707658+05:30', NULL, false, NULL, '1925b0c0-263c-4e9e-a084-2a244db7b6fc', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "flat_fee", "args": ["String", "Flat Fee"], "name": "Flat fee", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250,251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field46)", "is_qli_rule": true, "source_fields": ["field46"], "qli_rule_fields": {"field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Platform fee discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.704144+05:30', NULL, false, NULL, 'a5540af1-b0dd-4ade-a930-bc17df6c2330', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field50", "name": "field50", "data_type": "String", "system_name": "field50"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["251-1000", ">1000"], "args": ["StringArray", ["251-1000", ">1000"]], "name": "Array - 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field50)", "is_qli_rule": true, "source_fields": ["field50"], "qli_rule_fields": {"field50": {"sku": "SUPPORT", "price_column": "prorated_net_total", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Support discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.646408+05:30', NULL, false, NULL, 'ac096fcb-aa24-409a-8287-89b196f15374', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "Tiered", "args": ["String", "Tiered"], "name": "Tiered", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field45", "name": "field45", "data_type": "String", "system_name": "field45"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field45)", "is_qli_rule": true, "source_fields": ["field45", "field46"], "qli_rule_fields": {"field45": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "vp_price_threshold"}, "field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Tier pricing discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.654782+05:30', NULL, false, NULL, '2354bf98-8cef-4987-bb47-2d596d4aeb20', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 1, '0952146d-a96f-433f-82f4-615f3077cbc7', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "per_unit", "args": ["String", "Per Unit"], "name": "Per Unit", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field45", "name": "field45", "data_type": "String", "system_name": "field45"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250, 251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": [">20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - >20,21-100,101-250,251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field45)", "is_qli_rule": true, "source_fields": ["field45", "field46"], "qli_rule_fields": {"field45": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "vp_price_threshold"}, "field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Volume based discount approval', 31617);
INSERT INTO public.approval_rules (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, rule_id, rule_group_id, form_builder_id, stage_order, stage_template_id, condition, approvers, rule_name, client_id) VALUES ('2024-11-22 10:35:19.709568+05:30', NULL, false, NULL, 'da4ba3db-259d-4cb8-a178-575379410d62', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'f7c3c817-2a5c-4a88-94f1-fae970bd7395', 2, '73722a6f-95db-496e-9060-a1e2e492cc8b', '{"actions": [{"then": [{"value": "", "function": "GetApprovals", "component_id": "", "form_component": ""}], "condition": [{"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "sku", "name": "sku", "data_type": "String", "system_name": "sku"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "PLATFORM", "args": ["String", "PLATFORM"], "name": "PLATFORM", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "pricepoint_data.pricing_method", "name": "pricepoint_data.pricing_method", "data_type": "String", "system_name": "pricepoint_data.pricing_method"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"}, {"token": {"key": "per_unit", "args": ["String", "Per Unit"], "name": "Per Unit", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}, {"token": {"key": "(", "name": "("}, "token_type": "GROUPING_OPERATORS"}, {"token": {"key": "field46", "name": "field46", "data_type": "String", "system_name": "field46"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "IN", "name": "IN"}, "token_type": "OPERATORS"}, {"token": {"key": ["<20", "21-100", "101-250", "251-1000", ">1000"], "args": ["StringArray", ["<20", "21-100", "101-250", "251-1000", ">1000"]], "name": "Array - <20,21-100,101-250,251-1000, >1000", "data_type": "StringArray"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": ")", "name": ")"}, "token_type": "GROUPING_OPERATORS"}]}], "trigger": "HasChanged(field46)", "is_qli_rule": true, "source_fields": ["field46"], "qli_rule_fields": {"field46": {"sku": "PLATFORM", "price_column": "list_unit_price", "matrix_column": "ceo_price_threshold"}}, "destination_fields": []}', '{"users": ["<EMAIL>"], "groups": [], "dynamic": []}', 'Volume based discount approval', 31617);

------------------------------------------------------------------------------------------------------------------------

-- approval_template_stage

INSERT INTO public.approval_template_stage (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, stage_template_id, template_id, stage_name, approvers, approval_strategy, due_period, approval_trigger, cool_off_period, is_auto_approve, notes, stage_order, client_id) VALUES ('2024-11-22 10:35:19.623202+05:30', NULL, false, NULL, '0952146d-a96f-433f-82f4-615f3077cbc7', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'Stage #1', '{}', 'everyone', 2, 'approved', NULL, false, '.', 1, 31617);
INSERT INTO public.approval_template_stage (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, stage_template_id, template_id, stage_name, approvers, approval_strategy, due_period, approval_trigger, cool_off_period, is_auto_approve, notes, stage_order, client_id) VALUES ('2024-11-22 10:35:19.629532+05:30', NULL, false, NULL, '73722a6f-95db-496e-9060-a1e2e492cc8b', 'ed338a83-9495-48d3-9004-d77eae651a9c', 'Stage #2', '{}', 'everyone', 2, 'approved', NULL, false, '.', 2, 31617);

------------------------------------------------------------------------------------------------------------------------

-- client_settings

INSERT INTO public.client_settings (icm_settings, cpq_settings, client_id) VALUES (NULL, '{"form_spec": {"label": "Deal Hub Form", "sections": {"section1": {"id": "section1", "label": "Opportunity Details", "fields": {"field1": {"id": "field1", "label": "Quote ID", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "Quote ID", "label_placement": "top"}, "field2": {"id": "field2", "label": "Quote Name", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": true, "is_read_only": false}, "description": "Quote Name", "label_placement": "top"}, "field3": {"id": "field3", "label": "Opportunity", "value": "", "data_type": "string", "help_text": "", "field_type": "lookup", "properties": {"is_hidden": false, "is_mandatory": true, "is_read_only": false}, "description": "Opportunity Name", "label_placement": "top", "options_lookup_spec": {"column": "co_5_deal_name", "source": "datasheet_data", "filters": [], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"}}, "field4": {"id": "field4", "label": "Opportunity Id", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": true, "is_read_only": false}, "description": "Opportunity Id, lookup from Opportunity Name", "label_placement": "top", "options_lookup_spec": {"column": "co_5_record_id", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field3", "col_name": "co_5_deal_name", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field3", "needs_operand": true, "col_display_name": "Opportunity Name"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"}}, "field5": {"id": "field5", "label": "Account", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": true, "is_read_only": false}, "description": "Account Name", "label_placement": "top", "options_lookup_spec": {"column": "co_2_companyname", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Record Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field6": {"id": "field6", "label": "Account Id", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": true, "is_read_only": false}, "description": "Account Id", "label_placement": "top", "options_lookup_spec": {"column": "co_5_company_id", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field3", "col_name": "co_5_deal_name", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field3", "needs_operand": true, "col_display_name": "Opportunity Name"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"}}, "field7": {"id": "field7", "label": "Contact", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": true, "is_read_only": false}, "description": "Contact Name", "label_placement": "top", "options_lookup_spec": {"column": "cf_name", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field8", "col_name": "co_4_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field8", "needs_operand": true, "col_display_name": "Record Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"}}, "field8": {"id": "field8", "label": "Contact Id", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": true, "is_read_only": false}, "description": "Contact Id", "label_placement": "top", "options_lookup_spec": {"column": "co_5_contact_id", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field3", "col_name": "co_5_deal_name", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field3", "needs_operand": true, "col_display_name": "Opportunity Name"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"}}, "field9": {"id": "field9", "tag": "currency", "label": "Currency", "value": "USD", "tag_type": "pricebook", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": true, "is_read_only": true}, "description": "Currency", "label_placement": "top"}, "field10": {"id": "field10", "label": "Price Book", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Price Book", "label_placement": "top"}, "field11": {"id": "field11", "label": "Valid Till", "value": "", "data_type": "date", "help_text": "", "field_type": "date", "properties": {"is_hidden": false, "is_mandatory": true, "is_read_only": false}, "description": "Valid Till", "label_placement": "top"}, "field12": {"id": "field12", "label": "Deal Type", "value": "", "options": [{"label": "New Business", "value": "New Business"}, {"label": "Change Order", "value": "Change Order"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Type of Deal", "label_placement": "top"}, "field13": {"id": "field13", "label": "Data Center Location", "value": "", "options": [{"label": "US Data Center", "value": "US Data Center"}, {"label": "EU Data Center", "value": "EU Data Center"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "US / EU data center", "label_placement": "top"}, "field52": {"id": "field52", "label": "Contact Email", "value": "", "data_type": "string", "help_text": "", "field_type": "email", "properties": {"is_hidden": false, "is_mandatory": true, "is_read_only": false}, "description": "Contact Email", "label_placement": "top", "options_lookup_spec": {"column": "co_4_email", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field8", "col_name": "co_4_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field8", "needs_operand": true, "col_display_name": "Record Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"}}}, "help_text": "", "is_hidden": false, "description": "Basic information about the Quote like Opportunity, Account , Contact , etc. ", "field_order": ["field1", "field2", "field3", "field4", "field5", "field6", "field7", "field52", "field8", "field9", "field10", "field11", "field12", "field13"]}, "section2": {"id": "section2", "label": "Contact Details", "fields": {"field14": {"id": "field14", "label": "Country", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Country", "label_placement": "top", "options_lookup_spec": {"column": "co_2_country_region__1", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Company Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field15": {"id": "field15", "label": "State", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "State", "label_placement": "top", "options_lookup_spec": {"column": "co_2_state_region", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Company Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field16": {"id": "field16", "label": "City", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "City", "label_placement": "top", "options_lookup_spec": {"column": "co_2_city", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Company Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field17": {"id": "field17", "label": "Street Address Line 1", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Street Address Line 1", "label_placement": "top", "options_lookup_spec": {"column": "co_2_street_address", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Company Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field18": {"id": "field18", "label": "Street Address Line 2", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Street Address Line 2", "label_placement": "top", "options_lookup_spec": {"column": "co_2_street_address_2", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Company Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field19": {"id": "field19", "label": "Postal Code", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Postal Code", "label_placement": "top", "options_lookup_spec": {"column": "co_2_postal_code", "source": "datasheet_data", "filters": [{"type": "FILTER", "value": "field6", "col_name": "co_2_record_id", "is_valid": true, "operator": "==", "data_type": "String", "multi_valued": false, "source_field": "field6", "needs_operand": true, "col_display_name": "Company Id"}], "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"}}, "field20": {"id": "field20", "label": "Full Address from Hubspot", "value": "", "formula": [{"token": {"key": "Concat(,, [Address Line 1, Address Line 2, City, State, Country, Postal Code])", "args": [",", [{"token": {"key": "field17", "name": "field17", "data_type": "String", "system_name": "field17"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "field18", "name": "field18", "data_type": "String", "system_name": "field18"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "field16", "name": "field16", "data_type": "String", "system_name": "field16"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "field15", "name": "field15", "data_type": "String", "system_name": "field15"}, "token_type": "FORM_VARIABLES"}, {"token": {"key": "field14", "name": "field14", "data_type": "String", "system_name": "field14"}, "token_type": "FORM_VARIABLES"}]], "name": "Concat(,, [Address Line 1, Address Line 2, City, State, Country, Postal Code])", "type": "VARIABLE", "data_type": "String", "function_name": "Concat", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}], "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Full Address from Hubspot", "label_placement": "top"}, "field21": {"id": "field21", "label": "Change Address?", "value": false, "data_type": "boolean", "help_text": "", "field_type": "checkbox", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Change Address?", "label_placement": "right"}, "field22": {"id": "field22", "label": "Manual Address", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Manual Address", "label_placement": "top"}}, "help_text": "", "is_hidden": false, "description": "General Contact Details ", "field_order": ["field14", "field15", "field16", "field17", "field18", "field19", "field20", "field21", "field22"]}, "section3": {"id": "section3", "label": "Support & Connectors", "fields": {"field23": {"id": "field23", "label": "Include limitation of 100 hours/year White Glove support", "value": "", "options": [{"label": "Include (Limit)", "value": "Include (Limit)"}, {"label": "Exclude (No limit)", "value": "Exclude (No limit)"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Include limitation of 100 hours/year White Glove support", "label_placement": "top"}, "field24": {"id": "field24", "label": "Waive of support", "value": "", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Waive of support", "label_placement": "top"}, "field25": {"id": "field25", "label": "Number of Connectors", "value": "", "data_type": "number", "help_text": "", "field_type": "number", "properties": {"max": 15, "min": 0, "is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Number of Connectors", "label_placement": "top"}, "field26": {"id": "field26", "label": "Connector names", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Connector names", "label_placement": "top"}, "field27": {"id": "field27", "label": "Waive of connector charges", "value": "", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Waive of connector charges", "label_placement": "top"}}, "help_text": "", "is_hidden": false, "description": "Details about the support limitations, connectors, and related charges.", "field_order": ["field23", "field24", "field25", "field26", "field27"]}, "section4": {"id": "section4", "label": "Implementation Fee & Modules", "fields": {"field28": {"id": "field28", "label": "Include implementation package?", "value": "", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Include implementation package?", "label_placement": "top"}, "field29": {"id": "field29", "label": "Include ASC606 module?", "value": "", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "Include ASC606 module?", "label_placement": "top"}}, "help_text": "", "is_hidden": true, "description": "Details about the implementation package and modules.", "field_order": ["field28", "field29"]}, "section5": {"id": "section5", "label": "Payment Terms", "fields": {"field30": {"id": "field30", "label": "Payment Term", "value": "", "options": [{"label": "Annual Upfront", "value": "Annual Upfront"}, {"label": "Semi Annual", "value": "Semi Annual"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Select the payment term.", "label_placement": "top"}, "field31": {"id": "field31", "label": "Payment Method", "value": "", "options": [{"label": "Wire Transfer to Everstage", "value": "Wire Transfer to Everstage"}, {"label": "ACH", "value": "ACH"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Select the payment method.", "label_placement": "top"}, "field32": {"id": "field32", "label": "Credit Terms", "value": "", "options": [{"label": "30", "value": "30"}, {"label": "45", "value": "45"}, {"label": "60", "value": "60"}, {"label": "75", "value": "75"}, {"label": "90", "value": "90"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Select the credit terms in days.", "label_placement": "top"}}, "help_text": "", "is_hidden": false, "description": "Payment term details, including method and credit terms.", "field_order": ["field30", "field31", "field32"]}, "section6": {"id": "section6", "label": "Output Document", "fields": {"field33": {"id": "field33", "label": "Terms of Use", "value": "", "options": [{"label": "Online Terms (Website)", "value": "Online Terms (Website)"}, {"label": "MSA", "value": "MSA"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Select the Terms of Use.", "label_placement": "top"}, "field34": {"id": "field34", "label": "DPA Signed?", "value": false, "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "data_type": "string", "help_text": "", "field_type": "single_select", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Has the DPA been signed?", "label_placement": "top"}, "field35": {"id": "field35", "label": "Connectors Wording", "value": "", "formula": [{"token": {"args": [":", [{"token": {"key": "The order form includes charges for the following connectorss", "args": ["String", "The order form includes charges for the following connectors"], "name": "The order form includes charges for the following connectors", "data_type": "String"}, "token_type": "CONSTANT_VARIABLES"}, {"token": {"key": "field26", "name": "field26", "data_type": "String", "system_name": "field26"}, "token_type": "FORM_VARIABLES"}]], "name": "Concat(,,[Order Form Includes Charges For The Following Connectors, field26])", "type": "VARIABLE", "data_type": "String", "function_name": "Concat", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}], "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "The order form includes charges for the following connectors:", "label_placement": "top"}, "field36": {"id": "field36", "label": "Change Terms", "value": false, "data_type": "boolean", "help_text": "", "field_type": "checkbox", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Option to change terms.", "label_placement": "right"}, "field37": {"id": "field37", "label": "Auto Renewal", "value": false, "data_type": "boolean", "help_text": "", "field_type": "checkbox", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Is auto-renewal enabled?", "label_placement": "right"}, "field51": {"id": "field51", "label": "DPA Wording", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": false}, "description": "DPA Wording", "label_placement": "top"}}, "help_text": "", "is_hidden": false, "description": "Details related to the output document and terms.", "field_order": ["field33", "field34", "field51", "field35", "field36", "field37"]}, "section7": {"id": "section7", "tag": "select_product", "label": "Select Product", "fields": {"field38": {"id": "field38", "tag": "select_product", "label": "Select Product", "value": "", "table_id": 1, "data_type": "array", "help_text": "", "field_type": "table", "properties": {"is_hidden": false, "is_mandatory": false, "is_read_only": false}, "description": "Table containing order details", "label_placement": "top"}}, "help_text": "", "is_hidden": false, "description": "Select Product Step", "field_order": ["field38"]}, "section8": {"id": "section8", "tag": "approval_matrix", "label": "Approval Matrix", "fields": {"field45": {"id": "field45", "label": "EP - Mike Approval", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "EP Mike Approval", "label_placement": "top"}, "field46": {"id": "field46", "label": "EP - Siva Approval", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "EP Siva Approval", "label_placement": "top"}, "field47": {"id": "field47", "label": "Implementation - Mike Approval", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "Implementation Mike Approval", "label_placement": "top"}, "field48": {"id": "field48", "label": "Implementation - Siva Approval", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "Implementation Siva Approval", "label_placement": "top"}, "field49": {"id": "field49", "label": "Support - Mike Approval", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "Support Mike Approval", "label_placement": "top"}, "field50": {"id": "field50", "label": "Support - Siva Approval", "value": "", "data_type": "string", "help_text": "", "field_type": "single_line", "properties": {"is_hidden": true, "is_mandatory": false, "is_read_only": true}, "description": "Support Siva Approval", "label_placement": "top"}}, "help_text": "", "is_hidden": true, "description": "Approval Matrix hidden fields", "field_order": ["field45", "field46", "field47", "field48", "field49", "field50"]}}, "section_order": ["section1", "section2", "section3", "section4", "section5", "section6", "section7", "section8"]}, "portal_id": "47413328", "catalog_id": "eb3be818-6203-4df2-b8ca-8f60c993f65e", "pricebook_id": "bd79083c-4f4d-4970-bd92-cd0ec79e70a0", "catalog_db_id": "4b459ad2-8100-4ced-adc9-ca926c3db4ba", "pricebook_db_id": "4b459ad2-8100-4ced-adc9-ca926c3db4ba", "approval_matrix_book": "999ba40c-a92e-4e74-9af0-d9b98bc684bd", "quote_integration_id": "f3605d9e-91d1-4db7-8ddf-1e100d1879a5", "approval_matrix_sheet": "9c8d7eef-214f-43e7-8aa0-93a7728fa23a", "quote_report_sheet_id": "6e49f3b7-1db1-4976-8bc8-61e746c50e21", "quote_display_id_start": 1000, "quote_report_object_id": 7, "quote_display_id_prefix": "ES"}', 31617);

------------------------------------------------------------------------------------------------------------------------

-- countries

INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('IND', 'India', 'INR', '₹', true, 'en-IN', false, '{}');
INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('USA', 'United States', 'USD', '$', true, 'en-US', false, '{}');
INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('CAN', 'Canada', 'CAD', 'CA$', true, 'en-CA', false, '{}');
INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('NLD', 'Netherlands', 'EUR', '€', true, 'nl-NL', false, '{}');
INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('GBR', 'United Kingdom', 'GBP', '£', true, 'en-GB', false, '{}');
INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('FRA', 'France', 'EUR', '€', true, 'fr-FR', false, '{}');
INSERT INTO public.countries (country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES ('AUS', 'Australia', 'AUD', 'AU$', true, 'en-AU', false, '{}');

------------------------------------------------------------------------------------------------------------------------

-- custom_object

INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2024-10-09 18:07:05.068', NULL, false, NULL, 4, 'Contacts', '["co_4_record_id"]'::jsonb, NULL, '["co_4_record_id"]'::jsonb, '2024-10-09 13:23:04.678', '<EMAIL>', '{co_4_record_id,co_4_first_name,co_4_last_name,co_4_email,co_4_company_id}', NULL, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2024-10-09 18:09:33.099', NULL, false, NULL, 2, 'Companies', '["co_2_record_id"]'::jsonb, NULL, '["co_2_record_id"]'::jsonb, '2024-10-09 13:19:35.222', '<EMAIL>', '{co_2_record_id,co_2_companyname,co_2_phone_number,co_2_country_region__1,co_2_state_region,co_2_city,co_2_street_address,co_2_street_address_2,co_2_postal_code}', NULL, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2024-10-09 13:25:13.313', NULL, false, NULL, 3, 'Price Book', '["co_3_price_point_id"]'::jsonb, NULL, '["co_3_price_point_id"]'::jsonb, '2024-10-09 13:23:00.767', '<EMAIL>', '{co_3_product_name,co_3_product_code_sku,co_3_billing_frequency,co_3_currency,co_3_tier_name,co_3_tier_lower_bound,co_3_tier_upper_bound,co_3_tier_unit_discount,co_3_tier_flat_price,co_3_list_price,co_3_max_discount,co_3_canmodifylistpriceonquote,co_3_candiscountlistpriceonquote,co_3_price_point_id,co_3_pricing_method}', '[{"config": {"co_3_currency": "Currency", "co_3_tier_name": "Tier Name", "co_3_list_price": "List Price", "co_3_max_discount": "Max Discount (%)", "co_3_product_name": "Product Name", "co_3_price_point_id": "Price Point ID", "co_3_pricing_method": "Pricing Method", "co_3_tier_flat_price": "Tier Flat Price", "co_3_product_code_sku": "Product Code/SKU", "co_3_tier_lower_bound": "Tier Lower Bound", "co_3_tier_upper_bound": "Tier Upper Bound", "co_3_billing_frequency": "Billing Frequency", "co_3_tier_unit_discount": "Tier Unit Discount (%)", "co_3_canmodifylistpriceonquote": "Can modify list price on quote?", "co_3_candiscountlistpriceonquote": "Can discount list price on quote?"}, "systemName": "primaryConfig", "displayName": "Primary Config", "date_format_map": {}}]'::jsonb, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2024-10-09 13:25:36.623', NULL, false, NULL, 1, 'Product Catalog', '["co_1_product_code_sku"]'::jsonb, NULL, '["co_1_product_code_sku"]'::jsonb, '2024-10-09 13:18:49.107', '<EMAIL>', '{co_1_product_name,co_1_product_code_sku,co_1_category,co_1_subscription_type,co_1_description,co_1_managed_by,co_1_unitof_measure_uo_m,co_1_product_id}', '[{"config": {"co_1_category": "Category", "co_1_managed_by": "Managed By", "co_1_product_id": "Product ID", "co_1_description": "Description", "co_1_product_name": "Product Name", "co_1_product_code_sku": "Product Code/SKU", "co_1_subscription_type": "Subscription Type", "co_1_unitof_measure_uo_m": "Unit of Measure (UoM)"}, "systemName": "primaryConfig", "displayName": "Primary Config", "date_format_map": {}}]'::jsonb, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2024-12-04 08:41:14.792', NULL, false, NULL, 6, 'Approval Matrix', '["co_6_produc_code_sku", "co_6_pricing_method", "co_6_qty_range"]'::jsonb, NULL, '["co_6_produc_code_sku"]'::jsonb, '2024-10-23 12:33:25.550', '<EMAIL>', '{co_6_produc_code_sku,co_6_pricing_method,co_6_qty_range,co_6_lowerbound,co_6_upperbound,co_6_ep_mike_threshold,co_6_ep_siva_threshold,co_6_duration_mike_threshold,co_6_duration_siva_threshold}', '[{"config": {"co_6_qty_range": "Qty Range", "co_6_lowerbound": "Lower bound", "co_6_upperbound": "Upper bound", "co_6_pricing_method": "Pricing Method", "co_6_produc_code_sku": "Product Code/SKU", "co_6_ep_mike_threshold": "EP_Mike_Threshold", "co_6_ep_siva_threshold": "EP_Siva_Threshold", "co_6_duration_mike_threshold": "Duration_Mike_Threshold", "co_6_duration_siva_threshold": "Duration_Siva_Threshold"}, "systemName": "primaryConfig", "displayName": "Primary Config", "date_format_map": {}}]'::jsonb, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2024-10-09 18:04:56.923', NULL, false, NULL, 5, 'Deals', '["co_5_record_id"]'::jsonb, NULL, '["co_5_record_id"]'::jsonb, '2024-10-09 13:37:33.708', '<EMAIL>', '{co_5_record_id,co_5_deal_name,co_5_company_id,co_5_contact_id,co_5_currency}', NULL, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2025-02-13 08:14:37.673', NULL, false, NULL, 7, 'Quote Report', '["co_7_hsobjectid"]'::jsonb, NULL, '["co_7_hsobjectid"]'::jsonb, '2025-01-08 15:20:32.161', '<EMAIL>', '{co_7_hsobjectid,co_7_quoteid,co_7_quotename,co_7_opportunity,co_7_account,co_7_amount,co_7_asc_606_charges,co_7_asc_606_chargesyear_2,co_7_asc_606_chargesyear_3,co_7_autorenwal,co_7_connectorcharges,co_7_connectorchargesyear_2,co_7_connectorchargesyear_3,co_7_contractperiod,co_7_datacentrerequirements,co_7_dealcreditterms,co_7_dpasigned,co_7_implementationcharges,co_7_integrations,co_7_noofpayees,co_7_offlinemsasigned,co_7_subscriptionenddate,co_7_subscriptionstartdate,co_7_tieredpricingofferedincludesplatformfeebasedpricing,co_7_whiteglovesupportcharges,co_7_whiteglovesupportchargesyear_2,co_7_whiteglovesupportchargesyear_3,co_7_year_2_arr,co_7_year_3_arr}', '[{"config": {"co_7_amount": "amount", "co_7_account": "account", "co_7_quoteid": "quote_id", "co_7_quotename": "quote_name", "co_7_dpasigned": "dpa_signed_", "co_7_autorenwal": "autorenwal_", "co_7_hsobjectid": "hs_object_id", "co_7_noofpayees": "no_of_payees", "co_7_year_2_arr": "year_2_arr", "co_7_year_3_arr": "year_3_arr", "co_7_opportunity": "opportunity", "co_7_integrations": "integrations", "co_7_contractperiod": "contract_period", "co_7_asc_606_charges": "asc606_charges", "co_7_dealcreditterms": "deal_credit_terms", "co_7_connectorcharges": "connector_charges", "co_7_offlinemsasigned": "offline_msa_signed_", "co_7_subscriptionenddate": "subscription_end_date", "co_7_asc_606_chargesyear_2": "asc606_charges_year_2", "co_7_asc_606_chargesyear_3": "asc606_charges_year_3", "co_7_implementationcharges": "implementation_charges", "co_7_subscriptionstartdate": "subscription_start_date", "co_7_connectorchargesyear_2": "connector_charges_year_2", "co_7_connectorchargesyear_3": "connector_charges_year_3", "co_7_datacentrerequirements": "data_centre_requirements", "co_7_whiteglovesupportcharges": "white_glove_support_charges", "co_7_whiteglovesupportchargesyear_2": "white_glove_support_charges_year_2", "co_7_whiteglovesupportchargesyear_3": "white_glove_support_charges_year_3", "co_7_tieredpricingofferedincludesplatformfeebasedpricing": "tiered_pricing_offered__includes_platform_fee_based_pricing__"}, "systemName": "primaryConfig", "displayName": "Primary Config", "date_format_map": {"co_7_subscriptionenddate": "%Y-%m-%d", "co_7_subscriptionstartdate": "%Y-%m-%d"}}]'::jsonb, 31617);
INSERT INTO public.custom_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, "name", primary_key, tags, snapshot_key, created_at, created_by, ordered_columns, csv_header_map, client_id) VALUES('2025-03-12 07:50:11.305', NULL, false, NULL, 10, 'Quotes', '["co_10_quote_id"]'::jsonb, NULL, '["co_10_quote_id"]'::jsonb, '2025-03-12 07:49:30.540', '<EMAIL>', '{co_10_quote_id,co_10_quote_name,co_10_amount,co_10_signed,co_10_signed_date}', '[{"config": {"co_10_amount": "Amount", "co_10_signed": "Signed?", "co_10_quote_id": "Quote Id", "co_10_quote_name": "Quote Name", "co_10_signed_date": "Signed Date"}, "systemName": "primaryConfig", "displayName": "Primary Config", "date_format_map": {"co_10_signed_date": "%Y-%m-%d"}}]'::jsonb, 31617);

------------------------------------------------------------------------------------------------------------------------

-- custom_object_variable

INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_tier_lower_bound', 'Tier Lower Bound', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_tier_upper_bound', 'Tier Upper Bound', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_tier_unit_discount', 'Tier Unit Discount (%)', NULL, 31617, 6);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_tier_flat_price', 'Tier Flat Price', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_list_price', 'List Price', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_max_discount', 'Max Discount (%)', NULL, 31617, 6);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_canmodifylistpriceonquote', 'Can modify list price on quote?', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_candiscountlistpriceonquote', 'Can discount list price on quote?', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_price_point_id', 'Price Point ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_pricing_method', 'Pricing Method', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:04.678', NULL, false, NULL, 4, 'co_4_record_id', 'Record ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:04.678', NULL, false, NULL, 4, 'co_4_first_name', 'First Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:04.678', NULL, false, NULL, 4, 'co_4_last_name', 'Last Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:04.678', NULL, false, NULL, 4, 'co_4_email', 'Email', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:04.678', NULL, false, NULL, 4, 'co_4_company_id', 'Company ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 08:07:33.708', NULL, false, NULL, 5, 'co_5_record_id', 'Record ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 08:07:33.708', NULL, false, NULL, 5, 'co_5_deal_name', 'Deal Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 08:07:33.708', NULL, false, NULL, 5, 'co_5_company_id', 'Company ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:28:15.201', NULL, false, NULL, 5, 'co_5_contact_id', 'Contact ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:32:18.438', NULL, false, NULL, 5, 'co_5_currency', 'Currency', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:39:33.020', NULL, false, NULL, 2, 'co_2_country_region__1', 'Country/Region', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:39:33.020', NULL, false, NULL, 2, 'co_2_state_region', 'State/Region', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:39:33.020', NULL, false, NULL, 2, 'co_2_city', 'City', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:39:33.020', NULL, false, NULL, 2, 'co_2_street_address', 'Street Address', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:39:33.020', NULL, false, NULL, 2, 'co_2_street_address_2', 'Street Address 2', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 12:39:33.020', NULL, false, NULL, 2, 'co_2_postal_code', 'Postal Code', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_product_name', 'Product Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_product_code_sku', 'Product Code/SKU', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_category', 'Category', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_subscription_type', 'Subscription Type', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_description', 'Description', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_asc_606_chargesyear_2', 'asc606_charges_year_2', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_asc_606_chargesyear_3', 'asc606_charges_year_3', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_product_name', 'Product Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_product_code_sku', 'Product Code/SKU', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_billing_frequency', 'Billing Frequency', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_currency', 'Currency', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_produc_code_sku', 'Produc Code/SKU', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_pricing_method', 'Pricing Method', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_qty_range', 'Qty Range', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_lowerbound', 'Lower bound', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_amount', 'amount', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_asc_606_charges', 'asc606_charges', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_autorenwal', 'autorenwal_', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_connectorcharges', 'connector_charges', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_connectorchargesyear_2', 'connector_charges_year_2', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_connectorchargesyear_3', 'connector_charges_year_3', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_datacentrerequirements', 'data_centre_requirements', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_dealcreditterms', 'deal_credit_terms', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_managed_by', 'Managed By', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_unitof_measure_uo_m', 'Unit of Measure (UoM)', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:48:49.107', NULL, false, NULL, 1, 'co_1_product_id', 'Product ID', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:49:35.222', NULL, false, NULL, 2, 'co_2_record_id', 'Record ID', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:49:35.222', NULL, false, NULL, 2, 'co_2_companyname', 'Company name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:49:35.222', NULL, false, NULL, 2, 'co_2_phone_number', 'Phone Number', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_upperbound', 'Upper bound', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_ep_mike_threshold', 'EP_Mike_Threshold', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_ep_siva_threshold', 'EP_Siva_Threshold', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_duration_mike_threshold', 'Duration_Mike_Threshold', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-23 07:03:25.550', NULL, false, NULL, 6, 'co_6_duration_siva_threshold', 'Duration_Siva_Threshold', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_dpasigned', 'dpa_signed_', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_hsobjectid', 'hs_object_id', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_implementationcharges', 'implementation_charges', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_integrations', 'integrations', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_contractperiod', 'contract_period', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_noofpayees', 'no_of_payees', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_offlinemsasigned', 'offline_msa_signed_', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_subscriptionenddate', 'subscription_end_date', NULL, 31617, 2);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_subscriptionstartdate', 'subscription_start_date', NULL, 31617, 2);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_tieredpricingofferedincludesplatformfeebasedpricing', 'tiered_pricing_offered__includes_platform_fee_based_pricing__', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_whiteglovesupportcharges', 'white_glove_support_charges', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_whiteglovesupportchargesyear_2', 'white_glove_support_charges_year_2', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_whiteglovesupportchargesyear_3', 'white_glove_support_charges_year_3', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_year_2_arr', 'year_2_arr', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_year_3_arr', 'year_3_arr', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_opportunity', 'opportunity', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_account', 'account', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_quoteid', 'quote_id', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-01-07 06:59:07.699', NULL, false, NULL, 7, 'co_7_quotename', 'quote_name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2024-10-09 07:53:00.767', NULL, false, NULL, 3, 'co_3_tier_name', 'Tier Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-03-12 07:49:30.540', NULL, false, NULL, 10, 'co_10_quote_id', 'Quote Id', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-03-12 07:49:30.540', NULL, false, NULL, 10, 'co_10_quote_name', 'Quote Name', NULL, 31617, 4);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-03-12 07:49:30.540', NULL, false, NULL, 10, 'co_10_amount', 'Amount', NULL, 31617, 1);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-03-12 07:49:30.540', NULL, false, NULL, 10, 'co_10_signed', 'Signed?', NULL, 31617, 3);
INSERT INTO public.custom_object_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, custom_object_id, system_name, display_name, tags, client_id, data_type_id) VALUES('2025-03-12 07:49:30.540', NULL, false, NULL, 10, 'co_10_signed_date', 'Signed Date', NULL, 31617, 2);

------------------------------------------------------------------------------------------------------------------------

-- databook

INSERT INTO public.databook (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, "name", is_draft, is_archived, created_at, created_by, datasheet_order, client_id) VALUES('2024-10-09 07:14:34.503', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'Upstream', true, false, '2024-10-09 07:03:56.611', '<EMAIL>', '["b62c1287-190c-4329-8e91-4bb07ffccd25", "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27", "b3d0e741-ba81-436d-848b-4b7a6c90a82c"]'::jsonb, 31617);
INSERT INTO public.databook (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, "name", is_draft, is_archived, created_at, created_by, datasheet_order, client_id) VALUES('2025-01-09 19:11:54.198', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'Reports', true, false, '2025-01-08 04:23:52.562', '<EMAIL>', '["6e49f3b7-1db1-4976-8bc8-61e746c50e21"]'::jsonb, 31617);
INSERT INTO public.databook (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, "name", is_draft, is_archived, created_at, created_by, datasheet_order, client_id) VALUES('2024-10-09 02:26:39.823', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'Catalog & Prices', true, false, '2024-10-09 02:26:03.434', '<EMAIL>', '["eb3be818-6203-4df2-b8ca-8f60c993f65e", "bd79083c-4f4d-4970-bd92-cd0ec79e70a0"]'::jsonb, 31617);
INSERT INTO public.databook (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, "name", is_draft, is_archived, created_at, created_by, datasheet_order, client_id) VALUES('2024-10-23 01:35:45.835', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, 'Approvals', true, false, '2024-10-23 01:35:28.698', '<EMAIL>', '["9c8d7eef-214f-43e7-8aa0-93a7728fa23a"]'::jsonb, 31617);
INSERT INTO public.databook (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, "name", is_draft, is_archived, created_at, created_by, datasheet_order, client_id) VALUES('2025-03-12 07:51:22.024', NULL, false, NULL, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, 'Downstream', true, false, '2025-03-12 07:50:52.677', '<EMAIL>', '["60393465-8fb4-4304-b3ab-aa5e30650d56"]'::jsonb, 31617);

------------------------------------------------------------------------------------------------------------------------

-- datasheet

INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2024-10-09 02:40:38.821', NULL, false, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'Product Catalog', NULL, 'object', '1', NULL, 1, '["co_1_product_code_sku"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_1_product_name,co_1_product_code_sku,co_1_category,co_1_subscription_type,co_1_description,co_1_managed_by,co_1_unitof_measure_uo_m,co_1_product_id}', NULL, NULL, false, true, true, false, false, 'custom_object', '2024-10-09 02:40:38.821', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2024-10-09 02:41:08.957', NULL, false, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'Price Book', NULL, 'object', '3', NULL, 1, '["co_3_price_point_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_3_product_name,co_3_product_code_sku,co_3_billing_frequency,co_3_currency,co_3_tier_name,co_3_tier_lower_bound,co_3_tier_upper_bound,co_3_tier_unit_discount,co_3_tier_flat_price,co_3_list_price,co_3_max_discount,co_3_canmodifylistpriceonquote,co_3_candiscountlistpriceonquote,co_3_price_point_id,co_3_pricing_method}', NULL, NULL, false, true, true, false, false, 'custom_object', '2024-10-09 02:41:08.957', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2024-10-09 07:14:50.458', NULL, false, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b62c1287-190c-4329-8e91-4bb07ffccd25'::uuid, 'Deals', NULL, 'object', '5', NULL, 1, '["co_5_record_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_5_record_id,co_5_deal_name,co_5_company_id,co_5_contact_id,co_5_currency}', NULL, NULL, false, true, true, false, false, 'custom_object', '2024-10-09 07:14:50.458', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2024-10-09 07:18:28.263', NULL, false, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'Contacts', NULL, 'object', '4', NULL, 1, '["co_4_record_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_4_record_id,cf_name,co_4_email,co_4_company_id,co_4_first_name,co_4_last_name}', '{co_4_first_name,co_4_last_name}', NULL, false, true, true, false, false, 'custom_object', '2024-10-09 07:14:50.458', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2024-10-09 20:51:07.869', NULL, false, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'Companies', NULL, 'object', '2', NULL, 1, '["co_2_record_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_2_record_id,co_2_companyname,co_2_phone_number,co_2_country_region__1,co_2_state_region,co_2_city,co_2_street_address,co_2_street_address_2,co_2_postal_code}', NULL, NULL, false, true, true, false, false, 'custom_object', '2024-10-09 07:14:50.458', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2024-11-10 21:30:12.922', NULL, false, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'Approvals Matrix', NULL, 'object', '6', NULL, 1, '["co_6_qty_range", "co_6_produc_code_sku", "co_6_pricing_method"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_6_produc_code_sku,co_6_pricing_method,co_6_qty_range,co_6_lowerbound,co_6_upperbound,co_6_ep_mike_threshold,co_6_ep_siva_threshold,co_6_duration_mike_threshold,co_6_duration_siva_threshold}', NULL, NULL, false, true, true, false, false, 'custom_object', '2024-10-23 01:35:53.787', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2025-03-12 02:21:27.298', NULL, false, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, '60393465-8fb4-4304-b3ab-aa5e30650d56'::uuid, 'Quotes', NULL, 'object', '10', NULL, 1, '["co_10_quote_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{co_10_quote_id,co_10_quote_name,co_10_amount,co_10_signed,co_10_signed_date}', NULL, NULL, false, true, true, false, false, 'custom_object', '2025-03-12 02:21:27.298', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2025-06-23 07:13:54.608', NULL, false, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'Quote Line Item Tier', NULL, 'report', 'quote_line_item_tier', NULL, 1, '["quote_id", "line_item_id", "tier_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{quote_id,line_item_id,tier_id,quote_display_id,product_id,product_name,product_code,tier_number,tier_start,tier_end,tier_quantity,tier_list_unit_price,tier_net_unit_price,tier_list_flat_price,tier_net_flat_price,tier_total,last_updated_date,deleted}', NULL, NULL, false, true, true, false, false, 'quote_object', '2025-06-23 07:13:54.608', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2025-06-23 07:13:54.608', NULL, false, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'Quote Line Item', NULL, 'report', 'quote_line_item', NULL, 1, '["quote_id", "line_item_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{quote_id,line_item_id,quote_display_id,product_id,pricepoint_id,product_code,product_name,product_description,category,charged_on,currency,pricing_model,billing_type,billing_frequency,phase_name,phase_start_date,phase_end_date,quantity,list_price,net_price,discount_percentage,total,last_updated_date,deleted}', NULL, NULL, false, true, true, false, false, 'quote_object', '2025-06-23 07:13:54.608', 31617);
INSERT INTO public.datasheet (knowledge_begin_date, knowledge_end_date, is_deleted, databook_id, datasheet_id, "name", description, source_type, source_id, source_databook_id, "order", primary_key, additional_details, transformation_spec, ordered_columns, hidden_columns, tags, is_pk_modified, is_datasheet_generated, is_config_changed, is_calc_field_changed, is_force_skip, data_origin, data_last_updated_at, client_id) VALUES('2025-06-23 07:13:54.608', NULL, false, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'Quote', NULL, 'report', 'quote', NULL, 1, '["quote_id"]'::jsonb, '{}'::jsonb, '[]'::jsonb, '{quote_id,display_id,name,form_id,form_name,currency,status,owner_name,owner_email,valid_till,opportunity_id,account_id,contact_id,subscription_start_date,subscription_end_date,duration,duration_type,list_total,net_total,discount,primary_quote,won_date,created_date,last_updated_date,deleted}', NULL, NULL, false, true, true, false, false, 'quote_object', '2025-06-23 07:13:54.608', 31617);

------------------------------------------------------------------------------------------------------------------------

-- datasheet_variable

INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:47:31.761', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'co_4_record_id', 'Record ID', NULL, NULL, NULL, 0, NULL, 'co_4_record_id', '4', '944a7859-0385-4f57-b9d6-2e986537e069'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_product_id', 'Product ID', NULL, NULL, NULL, 0, NULL, 'co_1_product_id', '1', '693ec8dd-c32e-4751-8830-b3f0d7a3841d'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_product_name', 'Product Name', NULL, NULL, '{"semantic": "name"}'::jsonb, 0, NULL, 'co_1_product_name', '1', 'c2c81baa-114d-4b50-baa5-438cb9088e49'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_product_code_sku', 'Product Code/SKU', NULL, NULL, '{"semantic": "sku"}'::jsonb, 0, NULL, 'co_1_product_code_sku', '1', '1b1140e7-6008-42b2-9b13-4958d454dcbd'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_category', 'Category', NULL, NULL, '{"semantic": "category"}'::jsonb, 0, NULL, 'co_1_category', '1', '541c3c33-4c5c-40b3-bdea-3cf204514f4b'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_description', 'Description', NULL, NULL, '{"semantic": "description"}'::jsonb, 0, NULL, 'co_1_description', '1', '50579190-2ff1-46fd-b64d-df15fce0d1a5'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:44:34.491', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b62c1287-190c-4329-8e91-4bb07ffccd25'::uuid, 'co_5_deal_name', 'Deal Name', NULL, NULL, NULL, 0, NULL, 'co_5_deal_name', '5', '55154ade-92f8-4eca-8447-5032b6e927d6'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:44:34.491', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b62c1287-190c-4329-8e91-4bb07ffccd25'::uuid, 'co_5_currency', 'Currency', NULL, NULL, NULL, 0, NULL, 'co_5_currency', '5', 'faba984f-9e82-427c-affb-ea2accc1ab20'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_product_name', 'Product Name', NULL, NULL, NULL, 0, NULL, 'co_3_product_name', '3', 'fd559504-f1e3-4e2d-a9c3-b84a5f12fbd3'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_tier_name', 'Tier Name', NULL, NULL, NULL, 0, NULL, 'co_3_tier_name', '3', 'a22e2d1f-75d0-437e-8316-96ee23066da1'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_canmodifylistpriceonquote', 'Can modify list price on quote?', NULL, NULL, NULL, 0, NULL, 'co_3_canmodifylistpriceonquote', '3', '023f15df-adb4-4050-bab5-7564bd56af30'::uuid, true, 'object', false, NULL, 31617, 3);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_candiscountlistpriceonquote', 'Can discount list price on quote?', NULL, NULL, NULL, 0, NULL, 'co_3_candiscountlistpriceonquote', '3', '40cc6c2e-d64d-43b3-996d-404d40a890f4'::uuid, true, 'object', false, NULL, 31617, 3);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_price_point_id', 'Price Point ID', NULL, NULL, NULL, 0, NULL, 'co_3_price_point_id', '3', '7f1b828c-e4d4-4b8f-8ff7-45c41ee9c0e6'::uuid, true, 'object', true, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_product_code_sku', 'Product Code/SKU', NULL, NULL, '{"semantic": "sku"}'::jsonb, 0, NULL, 'co_3_product_code_sku', '3', '624a1c25-ec92-412d-936c-2ca2bd752478'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_currency', 'Currency', NULL, NULL, '{"marker": ["price_factor"], "semantic": "currency"}'::jsonb, 0, NULL, 'co_3_currency', '3', '718585b3-dc60-497e-b735-47e8b9ecac9b'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_tier_lower_bound', 'Tier Lower Bound', NULL, NULL, '{"semantic": "lower_bound"}'::jsonb, 0, NULL, 'co_3_tier_lower_bound', '3', '02f1acb0-49df-41e2-b627-e6386199cf0b'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:44:34.491', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b62c1287-190c-4329-8e91-4bb07ffccd25'::uuid, 'co_5_company_id', 'Company ID', NULL, NULL, NULL, 0, NULL, 'co_5_company_id', '5', 'd325cb2e-1343-44ea-9f6d-e0806fe003e3'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:44:34.491', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b62c1287-190c-4329-8e91-4bb07ffccd25'::uuid, 'co_5_contact_id', 'Contact ID', NULL, NULL, NULL, 0, NULL, 'co_5_contact_id', '5', '43680fff-7d8e-493a-8d68-6ed544bd2f37'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_tier_upper_bound', 'Tier Upper Bound', NULL, NULL, '{"semantic": "upper_bound"}'::jsonb, 0, NULL, 'co_3_tier_upper_bound', '3', '78244ccd-8864-4e8f-b48d-cdc50680810e'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_list_price', 'List Price', NULL, NULL, '{"semantic": "list_price"}'::jsonb, 0, NULL, 'co_3_list_price', '3', 'bfe3007b-c1e5-4692-a8c3-277d912bf09c'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:47:31.761', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'co_4_first_name', 'First Name', NULL, NULL, NULL, 0, NULL, 'co_4_first_name', '4', 'ff4faec0-cff9-43eb-8e0d-44593f73776e'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:47:31.761', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'co_4_last_name', 'Last Name', NULL, NULL, NULL, 0, NULL, 'co_4_last_name', '4', '4a06d3c7-2c94-4f5a-b494-bb62bf6662a0'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:47:31.761', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'co_4_email', 'Email', NULL, NULL, NULL, 0, NULL, 'co_4_email', '4', 'ab7e90ba-4dcb-4c31-9a16-e6cf8c7bdce2'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:47:31.761', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'cf_name', 'Name', NULL, NULL, NULL, 1, '{"ast": {"args": [" ", [{"meta": {"category": null, "model_name": "Contacts", "system_name": "co_4_first_name", "data_type_id": 4}, "name": "First Name", "tags": null, "type": "VARIABLE", "data_type": "String", "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"}, {"meta": {"category": null, "model_name": "Contacts", "system_name": "co_4_last_name", "data_type_id": 4}, "name": "Last Name", "tags": null, "type": "VARIABLE", "data_type": "String", "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"}]], "name": "Concat( , [First Name, Last Name])", "type": "VARIABLE", "data_type": "String", "function_name": "Concat", "token_category": "DYNAMIC"}, "infix": [{"token": {"key": "Concat( , [First Name, Last Name])", "args": [" ", [{"token": {"key": "co_4_first_name", "name": "First Name", "system_name": "co_4_first_name", "variable_id": "ff4faec0-cff9-43eb-8e0d-44593f73776e"}, "token_type": "DATASHEET_VARIABLES"}, {"token": {"key": "co_4_last_name", "name": "Last Name", "system_name": "co_4_last_name", "variable_id": "4a06d3c7-2c94-4f5a-b494-bb62bf6662a0"}, "token_type": "DATASHEET_VARIABLES"}]], "name": "Concat( , [First Name, Last Name])", "type": "VARIABLE", "data_type": "String", "function_name": "Concat", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}, {"token": {"type": "simple", "client_id": 31617, "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"}, "token_type": "AST_META"}, {"token": {"type": "simple", "client_id": 31617, "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287", "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"}, "token_type": "AST_META"}], "criteria_type": "simple", "case_insensitive": true, "used_system_names": [], "evaluation_context": "delta"}'::jsonb, NULL, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27', '3cdd5ca2-3955-4e62-822b-680f73c9725d'::uuid, true, 'datasheet', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_companyname', 'Company name', NULL, NULL, NULL, 0, NULL, 'co_2_companyname', '2', '81797a25-166e-4c70-89d5-f68b66ba36ba'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_phone_number', 'Phone Number', NULL, NULL, NULL, 0, NULL, 'co_2_phone_number', '2', '5fa8a997-5764-45ec-9210-753f43471e4e'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_country_region__1', 'Country/Region', NULL, NULL, NULL, 0, NULL, 'co_2_country_region__1', '2', '159a9e88-3d43-4b3d-8b00-0c64c11c13f8'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_state_region', 'State/Region', NULL, NULL, NULL, 0, NULL, 'co_2_state_region', '2', '4bf0098b-19e1-4dc6-ad4b-ad8044f19a05'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_city', 'City', NULL, NULL, NULL, 0, NULL, 'co_2_city', '2', '6662c7fa-a426-4f6a-a1b1-79e76e7f074f'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_street_address', 'Street Address', NULL, NULL, NULL, 0, NULL, 'co_2_street_address', '2', '855de7c4-fdd7-421a-9011-73f30b843219'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_street_address_2', 'Street Address 2', NULL, NULL, NULL, 0, NULL, 'co_2_street_address_2', '2', '421275e9-e2da-4106-b45f-b5e46bedccaa'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_postal_code', 'Postal Code', NULL, NULL, NULL, 0, NULL, 'co_2_postal_code', '2', '3b186361-de4e-4933-bca9-309036dae8b5'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:47:31.761', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'cf6007bc-9cf1-4cc8-b413-35c5eaee1e27'::uuid, 'co_4_company_id', 'Company ID', NULL, NULL, NULL, 0, NULL, 'co_4_company_id', '4', '263b080d-2949-4f9c-a85a-f213113df31a'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-10 02:21:07.892', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b3d0e741-ba81-436d-848b-4b7a6c90a82c'::uuid, 'co_2_record_id', 'Record ID', NULL, NULL, NULL, 0, NULL, 'co_2_record_id', '2', '5d4ea5b6-113e-4ff8-8abb-4705b55ef4b4'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_max_discount', 'Max Discount (%)', NULL, NULL, '{"semantic": "max_discount"}'::jsonb, 0, NULL, 'co_3_max_discount', '3', '31838593-fdcc-4a4f-bb51-e8cf80803580'::uuid, true, 'object', false, NULL, 31617, 6);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_pricing_method', 'Pricing Method', NULL, NULL, '{"marker": ["price_factor"], "semantic": "pricing_method"}'::jsonb, 0, NULL, 'co_3_pricing_method', '3', '56858e7b-c086-43a4-aa0e-a580d809eedc'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_billing_frequency', 'Billing Frequency', NULL, NULL, '{"marker": ["price_factor"], "semantic": "billing_frequency"}'::jsonb, 0, NULL, 'co_3_billing_frequency', '3', '120348dc-9b4b-417b-b8ee-72a25003f869'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_tier_unit_discount', 'Tier Unit Discount (%)', NULL, NULL, '{"semantic": "unit_discount"}'::jsonb, 0, NULL, 'co_3_tier_unit_discount', '3', 'cfb75e32-f44a-45c1-8ced-1c24787560ff'::uuid, true, 'object', false, NULL, 31617, 6);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:27.634', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'bd79083c-4f4d-4970-bd92-cd0ec79e70a0'::uuid, 'co_3_tier_flat_price', 'Tier Flat Price', NULL, NULL, '{"semantic": "flat_price"}'::jsonb, 0, NULL, 'co_3_tier_flat_price', '3', '69c32a25-fd81-4cea-8dce-ad7ad3016a22'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 12:44:34.491', NULL, false, NULL, '132c4b7f-9bf4-4a50-b812-c9f186a94287'::uuid, 'b62c1287-190c-4329-8e91-4bb07ffccd25'::uuid, 'co_5_record_id', 'Record ID', NULL, NULL, NULL, 0, NULL, 'co_5_record_id', '5', '42d31b66-ed71-45ae-b3ad-10c963a0e83f'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_qty_range', 'Qty Range', NULL, NULL, '{"semantic": "quantity_range"}'::jsonb, 0, NULL, 'co_6_qty_range', '6', '33490fb5-73db-457c-a76e-b2fdb24b3c13'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_lowerbound', 'Lower bound', NULL, NULL, '{"semantic": "lower_bound"}'::jsonb, 0, NULL, 'co_6_lowerbound', '6', '537cba39-e378-49bb-89da-21362a1481f9'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_produc_code_sku', 'Produc Code/SKU', NULL, NULL, '{"semantic": "sku"}'::jsonb, 0, NULL, 'co_6_produc_code_sku', '6', '3c8d17bd-2cdc-46a9-b115-176e0021fc48'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_pricing_method', 'Pricing Method', NULL, NULL, '{"semantic": "pricing_method"}'::jsonb, 0, NULL, 'co_6_pricing_method', '6', 'ae37e030-a613-4d40-bb1e-deed19c33f65'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_upperbound', 'Upper bound', NULL, NULL, '{"semantic": "upper_bound"}'::jsonb, 0, NULL, 'co_6_upperbound', '6', 'f5b346b2-74c3-47c2-95f5-c296873161c1'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_ep_mike_threshold', 'EP_Mike_Threshold', NULL, NULL, '{"semantic": "vp_price_threshold"}'::jsonb, 0, NULL, 'co_6_ep_mike_threshold', '6', '71eba1cf-10b0-42f8-8d8d-aac0b7ecbb4d'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_ep_siva_threshold', 'EP_Siva_Threshold', NULL, NULL, '{"semantic": "ceo_price_threshold"}'::jsonb, 0, NULL, 'co_6_ep_siva_threshold', '6', '43d1bb1c-e8c3-4aa1-8a0f-bc7333662d9b'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_duration_mike_threshold', 'Duration_Mike_Threshold', NULL, NULL, '{"semantic": "vp_duration_threshold"}'::jsonb, 0, NULL, 'co_6_duration_mike_threshold', '6', 'bf7632c1-63c9-4d05-b6dc-d05a8df9796a'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-11-11 03:00:12.937', NULL, false, NULL, '999ba40c-a92e-4e74-9af0-d9b98bc684bd'::uuid, '9c8d7eef-214f-43e7-8aa0-93a7728fa23a'::uuid, 'co_6_duration_siva_threshold', 'Duration_Siva_Threshold', NULL, NULL, '{"semantic": "ceo_duration_threshold"}'::jsonb, 0, NULL, 'co_6_duration_siva_threshold', '6', '1b5c51b8-e994-488a-b13f-c5e0ef41cc01'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_subscription_type', 'Subscription Type', NULL, NULL, '{"semantic": "billing_type"}'::jsonb, 0, NULL, 'co_1_subscription_type', '1', '61286567-ce6d-460f-a949-a1df1a24adf3'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_unitof_measure_uo_m', 'Unit of Measure (UoM)', NULL, NULL, '{"semantic": "uom"}'::jsonb, 0, NULL, 'co_1_unitof_measure_uo_m', '1', 'b19ba251-05a4-4b31-a13a-f779ce35dcf4'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2024-10-09 07:56:39.811', NULL, false, NULL, '4b459ad2-8100-4ced-adc9-ca926c3db4ba'::uuid, 'eb3be818-6203-4df2-b8ca-8f60c993f65e'::uuid, 'co_1_managed_by', 'Managed By', NULL, NULL, NULL, 0, NULL, 'co_1_managed_by', '1', 'fe6c3d3a-ac6c-40b2-8081-2e898d2fa7f2'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-03-12 07:51:22.007', NULL, false, NULL, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, '60393465-8fb4-4304-b3ab-aa5e30650d56'::uuid, 'co_10_quote_id', 'Quote Id', NULL, NULL, NULL, 0, NULL, 'co_10_quote_id', '10', '38f40f92-7317-42c7-a432-a47cfe0131e3'::uuid, true, 'object', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-03-12 07:51:22.007', NULL, false, NULL, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, '60393465-8fb4-4304-b3ab-aa5e30650d56'::uuid, 'co_10_quote_name', 'Quote Name', NULL, NULL, NULL, 0, NULL, 'co_10_quote_name', '10', '80ca2e03-4699-454c-ac35-3b772020eb14'::uuid, true, 'object', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-03-12 07:51:22.007', NULL, false, NULL, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, '60393465-8fb4-4304-b3ab-aa5e30650d56'::uuid, 'co_10_amount', 'Amount', NULL, NULL, NULL, 0, NULL, 'co_10_amount', '10', '7074f228-21e6-4168-b995-747a7f440717'::uuid, true, 'object', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-03-12 07:51:22.007', NULL, false, NULL, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, '60393465-8fb4-4304-b3ab-aa5e30650d56'::uuid, 'co_10_signed', 'Signed?', NULL, NULL, NULL, 0, NULL, 'co_10_signed', '10', '9de0c50e-845d-482a-b852-c2d6d7c94a01'::uuid, true, 'object', false, NULL, 31617, 3);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-03-12 07:51:22.007', NULL, false, NULL, 'f28c9c20-b9dd-434c-b02e-6a1f80f0537c'::uuid, '60393465-8fb4-4304-b3ab-aa5e30650d56'::uuid, 'co_10_signed_date', 'Signed Date', NULL, NULL, NULL, 0, NULL, 'co_10_signed_date', '10', 'd761140f-afe5-411d-860c-20f8dfef4729'::uuid, true, 'object', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'quote_id', 'Quote ID', NULL, NULL, NULL, 0, NULL, 'quote_id_quote', 'quote', 'a4490a4e-6dd9-47f6-b0fa-1b0b8ee198a0'::uuid, true, 'report', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'display_id', 'Display ID', NULL, NULL, NULL, 0, NULL, 'display_id_quote', 'quote', 'dc93d6b9-ed17-4265-bc74-708962e90091'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'name', 'Name', NULL, NULL, NULL, 0, NULL, 'name_quote', 'quote', '873281f7-b2ce-4f4d-b357-b8e919ca9614'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'form_id', 'Form ID', NULL, NULL, NULL, 0, NULL, 'form_id_quote', 'quote', 'e9fdc584-e713-4f21-b495-8a2d06b71700'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'form_name', 'Form Name', NULL, NULL, NULL, 0, NULL, 'form_name_quote', 'quote', 'acd649aa-b52d-4c3a-86f5-470871a2e2f0'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'currency', 'Currency', NULL, NULL, NULL, 0, NULL, 'currency_quote', 'quote', '37a89c84-56a4-4895-83ff-814d46cad3bd'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'status', 'Status', NULL, NULL, NULL, 0, NULL, 'status_quote', 'quote', 'ab8e173c-dfbd-4451-9a8a-08ca764fee67'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'owner_name', 'Owner Name', NULL, NULL, NULL, 0, NULL, 'owner_name_quote', 'quote', '50d62fa8-4169-4283-abe8-fc8fd5f1e511'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'owner_email', 'Owner Email', NULL, NULL, NULL, 0, NULL, 'owner_email_quote', 'quote', '4a8026e9-15bc-4667-9747-b8a26f1ce502'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'valid_till', 'Valid Till', NULL, NULL, NULL, 0, NULL, 'valid_till_quote', 'quote', 'c4555899-fd17-4331-a7da-9673a81c481f'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'opportunity_id', 'Opportunity ID', NULL, NULL, NULL, 0, NULL, 'opportunity_id_quote', 'quote', '1331e3ba-2b1a-4dcb-8609-30eb4304edd4'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'account_id', 'Account ID', NULL, NULL, NULL, 0, NULL, 'account_id_quote', 'quote', '7e2f5fb3-677c-464d-9ecc-e7798b5af39f'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'contact_id', 'Contact ID', NULL, NULL, NULL, 0, NULL, 'contact_id_quote', 'quote', 'cf169b8a-b3d2-4a81-b95c-3a15c9000d2d'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'subscription_start_date', 'Subscription Start Date', NULL, NULL, NULL, 0, NULL, 'subscription_start_date_quote', 'quote', '21e55130-d5cd-4d96-84d2-2e1d5a3f70de'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'subscription_end_date', 'Subscription End Date', NULL, NULL, NULL, 0, NULL, 'subscription_end_date_quote', 'quote', 'a1602819-6820-4e51-a251-b6a103a6f3bc'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'duration', 'Duration', NULL, NULL, NULL, 0, NULL, 'duration_quote', 'quote', '0c63b02a-fbe8-423a-a17d-5059a3bc1609'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'duration_type', 'Duration Type', NULL, NULL, NULL, 0, NULL, 'duration_type_quote', 'quote', 'a5fa2153-9980-4373-a179-1814c714798d'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'list_total', 'List Total', NULL, NULL, NULL, 0, NULL, 'list_total_quote', 'quote', 'fc960430-f33b-47b1-9fd2-aa1d6a4145bc'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'net_total', 'Net Total', NULL, NULL, NULL, 0, NULL, 'net_total_quote', 'quote', 'aabc3014-f122-4d3b-8334-a76faa25fb44'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'discount', 'Discount', NULL, NULL, NULL, 0, NULL, 'discount_quote', 'quote', 'a703331f-44bb-462f-ace6-2e2359b289c5'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'primary_quote', 'Primary Quote', NULL, NULL, NULL, 0, NULL, 'primary_quote_quote', 'quote', '1eda4868-807a-4255-9250-158af9e5d540'::uuid, true, 'report', false, NULL, 31617, 3);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'won_date', 'Won Date', NULL, NULL, NULL, 0, NULL, 'won_date_quote', 'quote', 'ee170790-f125-427a-9b18-5323951fec5c'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'created_date', 'Created Date', NULL, NULL, NULL, 0, NULL, 'created_date_quote', 'quote', 'ca7b7311-e5fa-403d-bcb4-cc3bee3ac461'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'last_updated_date', 'Last Updated Date', NULL, NULL, NULL, 0, NULL, 'last_updated_date_quote', 'quote', '743cffb4-e731-465e-8a9b-88b384c47455'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:12:25.885', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cbb7d2b2-74ba-41b4-baa1-fab1b63911f6'::uuid, 'deleted', 'Deleted', NULL, NULL, NULL, 0, NULL, 'deleted_quote', 'quote', '1d4655ae-cdf5-43a3-843e-c23d5e07f206'::uuid, true, 'report', false, NULL, 31617, 3);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'quote_id', 'Quote ID', NULL, NULL, NULL, 0, NULL, 'quote_id_quote_line_item', 'quote_line_item', '3f0e92ee-2c63-42be-986f-ca8765f2b896'::uuid, true, 'report', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'line_item_id', 'Line Item ID', NULL, NULL, NULL, 0, NULL, 'line_item_id_quote_line_item', 'quote_line_item', 'fa9f6703-df17-49f7-8b82-b9424b8aa6cc'::uuid, true, 'report', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'quote_display_id', 'Quote Display ID', NULL, NULL, NULL, 0, NULL, 'quote_display_id_quote_line_item', 'quote_line_item', '1cd7eaa1-d95d-4da0-855b-ed3d4ae87bc7'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'product_id', 'Product ID', NULL, NULL, NULL, 0, NULL, 'product_id_quote_line_item', 'quote_line_item', 'af97c783-bcc1-4b61-8775-a28dd537eeec'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'pricepoint_id', 'Price Point ID', NULL, NULL, NULL, 0, NULL, 'pricepoint_id_quote_line_item', 'quote_line_item', '6c8ac7e1-8dd0-461e-a884-59ad180c5df3'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'product_code', 'Product Code/SKU', NULL, NULL, NULL, 0, NULL, 'product_code_quote_line_item', 'quote_line_item', '4b9a3665-07fc-496a-837a-f05811c0c468'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'product_name', 'Product Name', NULL, NULL, NULL, 0, NULL, 'product_name_quote_line_item', 'quote_line_item', '5ca3fee7-7c58-435c-b67d-9e3ad88e2053'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'product_description', 'Product Description', NULL, NULL, NULL, 0, NULL, 'product_description_quote_line_item', 'quote_line_item', 'fb209fed-8336-4caf-8a10-33c65de9d3a9'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'category', 'Category', NULL, NULL, NULL, 0, NULL, 'category_quote_line_item', 'quote_line_item', 'd0ccecd9-d6df-4faa-a645-7b214abe04a4'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'charged_on', 'Charged On', NULL, NULL, NULL, 0, NULL, 'charged_on_quote_line_item', 'quote_line_item', 'fa6331a0-3d3e-44ed-bb9d-e325dcc340a6'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'currency', 'Currency', NULL, NULL, NULL, 0, NULL, 'currency_quote_line_item', 'quote_line_item', 'a816ffef-ccf9-45a8-aa83-4a4a6ad662fd'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'pricing_model', 'Pricing Model', NULL, NULL, NULL, 0, NULL, 'pricing_model_quote_line_item', 'quote_line_item', '148d8aca-e8e7-445c-b734-e30073107ebe'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'billing_type', 'Billing Type', NULL, NULL, NULL, 0, NULL, 'billing_type_quote_line_item', 'quote_line_item', '5515c6ba-480e-4be9-a8e4-ac1938bd0037'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'billing_frequency', 'Billing Frequency', NULL, NULL, NULL, 0, NULL, 'billing_frequency_quote_line_item', 'quote_line_item', '924c5d9f-e59a-4ced-8ca5-9c2ce947a95e'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'phase_name', 'Phase Name', NULL, NULL, NULL, 0, NULL, 'phase_name_quote_line_item', 'quote_line_item', '70d2301b-f142-4da0-9c56-80a0d01c0ab5'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'phase_start_date', 'Phase Start Date', NULL, NULL, NULL, 0, NULL, 'phase_start_date_quote_line_item', 'quote_line_item', 'b9988213-7774-43b0-b73c-bf9d98683f27'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'phase_end_date', 'Phase End Date', NULL, NULL, NULL, 0, NULL, 'phase_end_date_quote_line_item', 'quote_line_item', 'b700fcb9-b4de-41a1-9abf-b4c091e0d181'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'quantity', 'Quantity', NULL, NULL, NULL, 0, NULL, 'quantity_quote_line_item', 'quote_line_item', 'd73a128f-dd51-4a9b-9d1e-4d17d9f9c434'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'list_price', 'List Price', NULL, NULL, NULL, 0, NULL, 'list_price_quote_line_item', 'quote_line_item', '3a4abdbd-6f4b-4204-ac7a-8285321dcdb2'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'net_price', 'Net Price', NULL, NULL, NULL, 0, NULL, 'net_price_quote_line_item', 'quote_line_item', 'd6b424c2-8313-4038-ac7c-81d167a99c70'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'discount_percentage', 'Discount Percentage', NULL, NULL, NULL, 0, NULL, 'discount_percentage_quote_line_item', 'quote_line_item', 'f10a8386-5cf6-4ea9-b757-6c406691981c'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'total', 'Total', NULL, NULL, NULL, 0, NULL, 'total_quote_line_item', 'quote_line_item', 'c66b9e2c-5712-42ff-89bb-85688c0f44ea'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'last_updated_date', 'Last Updated Date', NULL, NULL, NULL, 0, NULL, 'last_updated_date_quote_line_item', 'quote_line_item', '3b0cd715-d675-4d75-b6db-8a23af0a7fed'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:02.439', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, 'cacc1d85-e26a-409d-9187-9e154f7612e6'::uuid, 'deleted', 'Deleted', NULL, NULL, NULL, 0, NULL, 'deleted_quote_line_item', 'quote_line_item', '1a882ea2-07a2-4b2c-9bf0-a4ec24ef9004'::uuid, true, 'report', false, NULL, 31617, 3);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'quote_id', 'Quote ID', NULL, NULL, NULL, 0, NULL, 'quote_id_quote_line_item_tier', 'quote_line_item_tier', '4eac35fa-96d8-4a7e-b8fe-495d94ddb576'::uuid, true, 'report', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'line_item_id', 'Line Item ID', NULL, NULL, NULL, 0, NULL, 'line_item_id_quote_line_item_tier', 'quote_line_item_tier', 'c582d741-d3bf-4b98-a25d-2ae5f390e489'::uuid, true, 'report', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_id', 'Tier ID', NULL, NULL, NULL, 0, NULL, 'tier_id_quote_line_item_tier', 'quote_line_item_tier', '478e91e7-fcc4-43ed-992e-ac50d6732a69'::uuid, true, 'report', true, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'quote_display_id', 'Quote Display ID', NULL, NULL, NULL, 0, NULL, 'quote_display_id_quote_line_item_tier', 'quote_line_item_tier', 'b957392f-a896-45d7-a195-7b04bcea8ef7'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'product_id', 'Product ID', NULL, NULL, NULL, 0, NULL, 'product_id_quote_line_item_tier', 'quote_line_item_tier', '39db5d27-f107-45c9-9bc3-5a204b6d77fc'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'product_name', 'Product Name', NULL, NULL, NULL, 0, NULL, 'product_name_quote_line_item_tier', 'quote_line_item_tier', 'c3b78b49-8331-4aee-95d9-09f690170980'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'product_code', 'Product Code/SKU', NULL, NULL, NULL, 0, NULL, 'product_code_quote_line_item_tier', 'quote_line_item_tier', '65413605-9e91-4a6b-9549-08c16bdb8672'::uuid, true, 'report', false, NULL, 31617, 4);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_number', 'Tier Number', NULL, NULL, NULL, 0, NULL, 'tier_number_quote_line_item_tier', 'quote_line_item_tier', 'aac853de-e2e9-48a4-9632-123ead686581'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_start', 'Tier Start', NULL, NULL, NULL, 0, NULL, 'tier_start_quote_line_item_tier', 'quote_line_item_tier', '28e75298-c491-4a7d-a3ac-d7b9d0c4372c'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_end', 'Tier End', NULL, NULL, NULL, 0, NULL, 'tier_end_quote_line_item_tier', 'quote_line_item_tier', '528e0382-5825-4590-9eee-b285ef11de14'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_quantity', 'Tier Quantity', NULL, NULL, NULL, 0, NULL, 'tier_quantity_quote_line_item_tier', 'quote_line_item_tier', 'fda589c5-cdc8-4aa8-8115-9473f7a6801b'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_list_unit_price', 'Tier List Unit Price', NULL, NULL, NULL, 0, NULL, 'tier_list_unit_price_quote_line_item_tier', 'quote_line_item_tier', '7e8542e8-22e7-4a58-83be-11e533bc6cba'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_net_unit_price', 'Tier Net Unit Price', NULL, NULL, NULL, 0, NULL, 'tier_net_unit_price_quote_line_item_tier', 'quote_line_item_tier', '18b2045f-aef5-4812-aaf5-d98f923ec789'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_list_flat_price', 'Tier List Flat Price', NULL, NULL, NULL, 0, NULL, 'tier_list_flat_price_quote_line_item_tier', 'quote_line_item_tier', '86b06dbb-4e01-44ed-8b26-d6441ebb709b'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_net_flat_price', 'Tier Net Flat Price', NULL, NULL, NULL, 0, NULL, 'tier_net_flat_price_quote_line_item_tier', 'quote_line_item_tier', '50dfadbc-6154-4daf-baa4-6dbe4cb8f903'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'tier_total', 'Tier Total', NULL, NULL, NULL, 0, NULL, 'tier_total_quote_line_item_tier', 'quote_line_item_tier', '951b45b6-9c78-496e-ae56-99b68cdf0898'::uuid, true, 'report', false, NULL, 31617, 1);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'last_updated_date', 'Last Updated Date', NULL, NULL, NULL, 0, NULL, 'last_updated_date_quote_line_item_tier', 'quote_line_item_tier', 'a41489cc-a811-4c31-856e-d02801428ebb'::uuid, true, 'report', false, NULL, 31617, 2);
INSERT INTO public.datasheet_variable (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, databook_id, datasheet_id, system_name, display_name, description, source_cf_meta_data, tags, field_order, meta_data, source_variable_id, source_id, variable_id, is_selected, source_type, is_primary, warning, client_id, data_type_id) VALUES('2025-06-23 07:13:22.423', NULL, false, NULL, '1e286993-275f-4500-bd47-d56f088a2e52'::uuid, '7ab19d8b-19aa-488c-b18b-c5e343b830a2'::uuid, 'deleted', 'Deleted', NULL, NULL, NULL, 0, NULL, 'deleted_quote_line_item_tier', 'quote_line_item_tier', '600ffbff-c290-4982-971d-6a1aafd071c0'::uuid, true, 'report', false, NULL, 31617, 3);

------------------------------------------------------------------------------------------------------------------------

-- downstream_integration

INSERT INTO public.downstream_integration (integration_id, integration_name, source_object, destination_object, synced_till, additional_data, client_id, connection_id) VALUES('a9642d28-77e2-47af-b899-618654282cd3'::uuid, 'HS Quotes', '60393465-8fb4-4304-b3ab-aa5e30650d56', 'p47413328_es_quotes', '2025-03-27 12:33:15.818', '{"batch_size": 5}'::jsonb, 31617, 1);
INSERT INTO public.downstream_integration (integration_id, integration_name, source_object, destination_object, synced_till, additional_data, client_id, connection_id) VALUES('02635924-966f-4d00-8adf-3d83c209debd'::uuid, 'SF Quotes', '60393465-8fb4-4304-b3ab-aa5e30650d56', 'ES_Quote__c', '2025-03-27 12:33:14.136', '{"batch_size": 5}'::jsonb, 31617, 2);

------------------------------------------------------------------------------------------------------------------------

-- downstream_endpoint

INSERT INTO public.downstream_endpoint (url, method, operation, page_size, additional_data, client_id, integration_id, body, headers) VALUES('https://everstage-c-dev-ed.develop.my.salesforce.com/services/data/v63.0/composite/sobjects/ES_Quote__c/Quote_Id__c', 'patch', 'bulk_upsert', 200, NULL, 31617, '02635924-966f-4d00-8adf-3d83c209debd'::uuid, NULL, NULL);
INSERT INTO public.downstream_endpoint (url, method, operation, page_size, additional_data, client_id, integration_id, body, headers) VALUES('https://api.hubapi.com/crm/v3/objects/p47413328_es_quotes/batch/upsert', 'post', 'bulk_upsert', 100, NULL, 31617, 'a9642d28-77e2-47af-b899-618654282cd3'::uuid, NULL, NULL);

------------------------------------------------------------------------------------------------------------------------

-- downstream_mapping

INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_quote_id', 'Quote_Id__c', 'string', true, true, NULL, 31617, '02635924-966f-4d00-8adf-3d83c209debd'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_quote_name', 'Name', 'string', false, false, NULL, 31617, '02635924-966f-4d00-8adf-3d83c209debd'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_amount', 'Amount__c', 'number', false, false, NULL, 31617, '02635924-966f-4d00-8adf-3d83c209debd'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_signed', 'Signed__c', 'boolean', false, false, NULL, 31617, '02635924-966f-4d00-8adf-3d83c209debd'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_signed_date', 'Signed_Date__c', 'datetime', false, false, NULL, 31617, '02635924-966f-4d00-8adf-3d83c209debd'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_amount', 'amount', 'number', false, false, NULL, 31617, 'a9642d28-77e2-47af-b899-618654282cd3'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_signed', 'signed', 'boolean', false, false, NULL, 31617, 'a9642d28-77e2-47af-b899-618654282cd3'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_signed_date', 'signed_date', 'datetime', false, false, NULL, 31617, 'a9642d28-77e2-47af-b899-618654282cd3'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_quote_id', 'quote_id', 'string', true, true, NULL, 31617, 'a9642d28-77e2-47af-b899-618654282cd3'::uuid);
INSERT INTO public.downstream_mapping (source_field, destination_field, destination_type, is_source_pk, is_destination_id, additional_data, client_id, integration_id) VALUES('co_10_quote_name', 'quote_name', 'string', false, false, NULL, 31617, 'a9642d28-77e2-47af-b899-618654282cd3'::uuid);

------------------------------------------------------------------------------------------------------------------------

-- email_template_details

INSERT INTO public.email_template_details (template_id, template_name, email_event_code, client_ids) VALUES ('d-3a4f3fd41b734f12857a98bb3b5cf06e', 'email-password-invite', 'SEND_INVITE', '{}');
INSERT INTO public.email_template_details (template_id, template_name, email_event_code, client_ids) VALUES ('d-de18807722c8459ba069f06ab5802df6', 'everstage-social-invite', 'SEND_INVITE', '{}');

------------------------------------------------------------------------------------------------------------------------

-- employee

INSERT INTO public.employee (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, employee_email_id, first_name, last_name, user_role, user_source, time_zone, email_alias, created_date, created_by, profile_picture, login_mode, send_notification, cpq_user_notification, employee_config, exit_date, deactivation_date, last_commission_date, status, is_internal_support_user, preferred_language, client_id) VALUES ('2024-10-23 12:42:21.758+05:30', NULL, false, NULL, '<EMAIL>', 'CEO', 'Cleverstage', '["d6653025-dce9-41cd-b13b-6004cb752337"]', 'manual', NULL, NULL, '2024-10-23 12:42:21.758+05:30', '<EMAIL>', NULL, NULL, true, true, NULL, NULL, NULL, NULL, 'Active', false, '[]', 31617);
INSERT INTO public.employee (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, employee_email_id, first_name, last_name, user_role, user_source, time_zone, email_alias, created_date, created_by, profile_picture, login_mode, send_notification, cpq_user_notification, employee_config, exit_date, deactivation_date, last_commission_date, status, is_internal_support_user, preferred_language, client_id) VALUES ('2024-10-23 12:42:44.828+05:30', NULL, false, NULL, '<EMAIL>', 'VP', 'Cleverstage', '["d6653025-dce9-41cd-b13b-6004cb752337"]', 'manual', NULL, NULL, '2024-10-23 12:42:44.828+05:30', '<EMAIL>', NULL, NULL, true, true, NULL, NULL, NULL, NULL, 'Active', false, '[]', 31617);
INSERT INTO public.employee (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, employee_email_id, first_name, last_name, user_role, user_source, time_zone, email_alias, created_date, created_by, profile_picture, login_mode, send_notification, cpq_user_notification, employee_config, exit_date, deactivation_date, last_commission_date, status, is_internal_support_user, preferred_language, client_id) VALUES ('2024-10-15 14:33:38.41+05:30', NULL, false, NULL, '<EMAIL>', 'Payee', '3', '["94a90761-9fb5-4863-ae0c-a0a6dfebd5da"]', 'manual', NULL, NULL, '2024-10-15 14:33:38.41+05:30', '<EMAIL>', NULL, NULL, true, true, NULL, NULL, NULL, NULL, 'Active', false, '[]', 31617);
INSERT INTO public.employee (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, employee_email_id, first_name, last_name, user_role, user_source, time_zone, email_alias, created_date, created_by, profile_picture, login_mode, send_notification, cpq_user_notification, employee_config, exit_date, deactivation_date, last_commission_date, status, is_internal_support_user, preferred_language, client_id) VALUES ('2024-11-20 08:04:29.199+05:30', NULL, false, NULL, '<EMAIL>', 'Payee', '1', '["b0a62c80-d2d3-475d-ba51-e05fd3c89913"]', 'manual', NULL, NULL, '2024-10-15 14:33:01.914+05:30', '<EMAIL>', NULL, NULL, true, true, NULL, NULL, NULL, NULL, 'Active', false, '[]', 31617);
INSERT INTO public.employee (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, employee_email_id, first_name, last_name, user_role, user_source, time_zone, email_alias, created_date, created_by, profile_picture, login_mode, send_notification, cpq_user_notification, employee_config, exit_date, deactivation_date, last_commission_date, status, is_internal_support_user, preferred_language, client_id) VALUES ('2024-11-20 08:04:35.413+05:30', NULL, false, NULL, '<EMAIL>', 'Payee', '2', '["b0a62c80-d2d3-475d-ba51-e05fd3c89913"]', 'manual', NULL, NULL, '2024-10-15 14:33:12.116+05:30', '<EMAIL>', NULL, NULL, true, true, NULL, NULL, NULL, NULL, 'Active', false, '[]', 31617);
INSERT INTO public.employee (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, employee_email_id, first_name, last_name, user_role, user_source, time_zone, email_alias, created_date, created_by, profile_picture, login_mode, send_notification, cpq_user_notification, employee_config, exit_date, deactivation_date, last_commission_date, status, is_internal_support_user, preferred_language, client_id) VALUES ('2025-02-11 16:49:46.382524+05:30', NULL, false, NULL, '<EMAIL>', 'Power', 'Admin', '["d2dafd7a-34f0-4d14-821e-60c2de715f5a"]', NULL, NULL, NULL, '2025-02-11 16:49:46.382524+05:30', NULL, NULL, NULL, false, true, NULL, NULL, NULL, NULL, 'Active', true, '[]', 31617);

------------------------------------------------------------------------------------------------------------------------

-- extraction_config

INSERT INTO public.extraction_config (client_id, priority, knowledge_begin_date, knowledge_end_date, is_disabled, task_group, task, source_object_id, destination_object_id, sync_type, destination_object_type, integration_id, access_token_config_id, additional_data) VALUES (31617, 1, '2024-10-09 18:49:36.034747+05:30', NULL, false, 'UPSTREAM_WRAPPER_SYNC', 'HUBSPOT_COMPANIES_SYNC', 'companies', '2', 'changes', 'custom', '************************************', 2, NULL);
INSERT INTO public.extraction_config (client_id, priority, knowledge_begin_date, knowledge_end_date, is_disabled, task_group, task, source_object_id, destination_object_id, sync_type, destination_object_type, integration_id, access_token_config_id, additional_data) VALUES (31617, 1, '2024-10-09 18:53:05.252145+05:30', NULL, false, 'UPSTREAM_WRAPPER_SYNC', 'HUBSPOT_CONTACTS_SYNC', 'contacts', '4', 'changes', 'custom', '************************************', 2, NULL);
INSERT INTO public.extraction_config (client_id, priority, knowledge_begin_date, knowledge_end_date, is_disabled, task_group, task, source_object_id, destination_object_id, sync_type, destination_object_type, integration_id, access_token_config_id, additional_data) VALUES (31617, 1, '2024-10-09 19:07:34.413627+05:30', NULL, false, 'UPSTREAM_WRAPPER_SYNC', 'HUBSPOT_DEALS_SYNC', 'deals', '5', 'changes', 'custom', '************************************', 2, NULL);

------------------------------------------------------------------------------------------------------------------------

-- integration

INSERT INTO public.integration (client_id, knowledge_begin_date, knowledge_end_date, integration_id, name, service_name, "desc", logo_url, source_object_id, properties, additional_data, is_api, destination_object_id, hyperlink_info, batch_etl_page_size, preprocessing_metadata, transformation_logic, sf_cdc_enabled) VALUES (31617, '2024-10-09 19:07:34.407683+05:30', NULL, '************************************', 'Deals', 'hubspot', 'Self-serviced integration for Hubspot - Deals', NULL, 'deals', NULL, NULL, true, 5, NULL, NULL, NULL, NULL, false);
INSERT INTO public.integration (client_id, knowledge_begin_date, knowledge_end_date, integration_id, name, service_name, "desc", logo_url, source_object_id, properties, additional_data, is_api, destination_object_id, hyperlink_info, batch_etl_page_size, preprocessing_metadata, transformation_logic, sf_cdc_enabled) VALUES (31617, '2024-10-09 18:49:36.026083+05:30', NULL, '************************************', 'Companies', 'hubspot', 'Self-serviced integration for Hubspot - Companies', NULL, 'companies', NULL, NULL, true, 2, NULL, NULL, NULL, NULL, false);
INSERT INTO public.integration (client_id, knowledge_begin_date, knowledge_end_date, integration_id, name, service_name, "desc", logo_url, source_object_id, properties, additional_data, is_api, destination_object_id, hyperlink_info, batch_etl_page_size, preprocessing_metadata, transformation_logic, sf_cdc_enabled) VALUES (31617, '2024-10-09 18:53:05.246653+05:30', NULL, '************************************', 'Contacts', 'hubspot', 'Self-serviced integration for Hubspot - Contacts', NULL, 'contacts', NULL, NULL, true, 4, NULL, NULL, NULL, NULL, false);

------------------------------------------------------------------------------------------------------------------------

-- operators

INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134249+05:30', NULL, false, NULL, 'abd638c0-80c9-4abf-8f2d-1f197e089c18', '+', 'PLUS', 'ARITHMETIC', '[1, 9, 10, 11]', '[1]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134357+05:30', NULL, false, NULL, 'd0a2f5fb-9d32-4ba8-97a5-5faf9be0a36d', '-', 'MINUS', 'ARITHMETIC', '[1, 9, 10, 11]', '[1]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134381+05:30', NULL, false, NULL, '065ace4b-e097-4ec3-910d-7c7cd77c6f8b', '*', 'MULTIPLY', 'ARITHMETIC', '[1, 6, 9, 10, 11]', '[1]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.1344+05:30', NULL, false, NULL, '3ddfdd69-1b50-4b2b-a20b-f3a66fcf7f6b', '/', 'DIVIDE', 'ARITHMETIC', '[1, 9, 10, 11]', '[1]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134422+05:30', NULL, false, NULL, 'f2566a05-ffdf-4037-a04a-f9112220d36a', '>', 'GREATERTHAN', 'LOGICAL', '[1, 2, 6, 9, 10, 11]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134443+05:30', NULL, false, NULL, 'addfc54b-327b-47e3-a78a-e5ee10ef8bde', '>=', 'GREATERTHANEQUALTO', 'LOGICAL', '[1, 2, 6, 9, 10, 11]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134462+05:30', NULL, false, NULL, '10d64484-3836-469a-a8f9-fe62c5370bde', '<', 'LESSERTHAN', 'LOGICAL', '[1, 2, 6, 9, 10, 11]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134484+05:30', NULL, false, NULL, 'bfaa4a06-3571-413d-b49c-acdf611f4d8a', '<=', 'LESSERTHANEQUALTO', 'LOGICAL', '[1, 2, 6, 9, 10, 11]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134502+05:30', NULL, false, NULL, 'd25abf81-1041-4db5-8bd2-3383d657ba5c', '==', 'EQUALS', 'LOGICAL', '[1, 2, 3, 4, 6, 9, 10, 11, 12]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.13452+05:30', NULL, false, NULL, '6014fdf2-e4c4-4d40-9ddc-f4a7393a7255', '!=', 'NOTEQUALS', 'LOGICAL', '[1, 2, 3, 4, 6, 9, 10, 11, 12]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134538+05:30', NULL, false, NULL, '698be1e6-7629-4472-be3d-dd7c707c3c32', 'AND', 'AND', 'LOGICAL', '[3]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134556+05:30', NULL, false, NULL, '7dd2cd54-d5a3-4033-afd4-15ff9169806c', 'OR', 'OR', 'LOGICAL', '[3]', '[3]', true, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134574+05:30', NULL, false, NULL, '720323b5-88b5-49e0-9989-9b18a4f432b6', 'IN', 'IN', 'LOGICAL', '[1, 4, 12, 13, 14]', '[3]', true, true);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134592+05:30', NULL, false, NULL, '93fad29b-6eed-40d5-a967-34043060fb14', 'NOTIN', 'NOTIN', 'LOGICAL', '[1, 4, 12, 13, 14]', '[3]', true, true);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.13461+05:30', NULL, false, NULL, 'bf721e03-4243-4a13-b2c6-9282c6d9dc73', 'IS_EMPTY', 'IS_EMPTY', 'LOGICAL', '[1, 2, 4, 6, 12]', '[3]', false, false);
INSERT INTO public.operators (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, id, name, alt_name, category, operand_type_ids, output_type_ids, needs_operand, multi_valued) VALUES ('2025-02-11 16:48:03.134628+05:30', NULL, false, NULL, 'a773fea8-940c-41cd-aed6-fbde16376804', 'IS_NOT_EMPTY', 'IS_NOT_EMPTY', 'LOGICAL', '[1, 2, 4, 6, 12]', '[3]', false, false);

------------------------------------------------------------------------------------------------------------------------

-- permissions

INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('create:commissionplan', 'Create Commission Plan', '2024-10-09 18:01:59.714857+05:30', '2024-10-09 18:01:59.714865+05:30', true, true, false, 'commission_plans', 'Commission Plans', 'edit:commissionplan', 3, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('delete:commissionplan', 'Delete published plans', '2024-10-09 18:01:59.714908+05:30', '2024-10-09 18:01:59.714917+05:30', true, true, false, 'commission_plans', 'Commission Plans', 'edit:commissionplan', 3, 'Plans in draft state can be deleted by users with ''edit commission plans'' permission.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:payouts', 'View payouts page', '2024-10-09 18:01:59.71496+05:30', '2024-10-09 18:01:59.714969+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', NULL, 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:payouts', 'Manage payouts & arrears', '2024-10-09 18:01:59.715012+05:30', '2024-10-09 18:01:59.71502+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', 'view:payouts', 5, 'Users can lock payouts, email statements, manage approval stages.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('register:payouts', 'Register payouts & arrears', '2024-10-09 18:01:59.715063+05:30', '2024-10-09 18:01:59.715073+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', 'view:payouts', 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('invalidate:payouts', 'Invalidate payouts & arrears', '2024-10-09 18:01:59.715116+05:30', '2024-10-09 18:01:59.715125+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', 'register:payouts', 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:requestapprovals', 'Request approvals', '2024-10-09 18:01:59.715169+05:30', '2024-10-09 18:01:59.715178+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', 'view:payouts', 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('export:payouts', 'Export payouts', '2024-10-09 18:01:59.715221+05:30', '2024-10-09 18:01:59.71523+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', 'view:payouts', 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:statements', 'View Statement tab on navigation bar', '2024-10-09 18:01:59.715273+05:30', '2024-10-09 18:01:59.715282+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', NULL, 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('export:statement', 'Export statements', '2024-10-09 18:01:59.715325+05:30', '2024-10-09 18:01:59.715333+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', 'view:statements', 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:hiddencriteria', 'View hidden criteria in statements', '2024-10-09 18:01:59.715376+05:30', '2024-10-09 18:01:59.715385+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', NULL, 5, 'Users can see hidden criteria configured in commission plans on the statement.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:commissionadjustment', 'Manage commission adjustments', '2024-10-09 18:01:59.715428+05:30', '2024-10-09 18:01:59.715437+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', NULL, 5, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:dashboard', 'Create & Edit custom dashboards', '2024-10-09 18:01:59.714096+05:30', '2024-10-09 18:01:59.714106+05:30', true, true, false, 'dashboard', 'Dashboard', 'view:dashboard', 1, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('delete:dashboard', 'Delete custom dashboards', '2024-10-09 18:01:59.714152+05:30', '2024-10-09 18:01:59.714162+05:30', true, true, false, 'dashboard', 'Dashboard', 'view:dashboard', 1, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:admindashboard', ' View default admin dashboard', '2024-10-09 18:01:59.714207+05:30', '2024-10-09 18:01:59.714216+05:30', true, true, false, 'dashboard', 'Dashboard', 'view:dashboard', 1, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:payeedashboard', 'View default payee dashboard ', '2024-10-09 18:01:59.714262+05:30', '2024-10-09 18:01:59.714271+05:30', true, true, false, 'dashboard', 'Dashboard', 'view:dashboard', 1, 'Admins who are part of commission plans can see the payee dashboard even without this permission.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:analytics', 'Manage analytics', '2024-10-09 18:01:59.714316+05:30', '2024-10-09 18:01:59.714324+05:30', true, true, false, 'dashboard', 'Dashboard', 'view:dashboard', 1, 'Users can access analytics portal to create and manage dashboards.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:databook', 'View databooks page', '2024-10-09 18:01:59.71437+05:30', '2024-10-09 18:01:59.714378+05:30', true, true, false, 'databooks', 'Databooks', NULL, 2, 'Users will get read-only access to datasheets.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:databook', 'Create & Edit databooks', '2024-10-09 18:01:59.714491+05:30', '2024-10-09 18:01:59.7145+05:30', true, true, false, 'databooks', 'Databooks', 'view:databook', 2, 'Datasheet level row and column restrictions won''t apply to users with this permission.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:datasheetpermissions', 'Manage datasheet permissions', '2024-10-09 18:01:59.714544+05:30', '2024-10-09 18:01:59.714553+05:30', true, true, false, 'databooks', 'Databooks', 'view:databook', 2, 'Datasheet level row and column restrictions won''t apply to users with this permission.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:datasheetadjustments', 'Adjust records in datasheet', '2024-10-09 18:01:59.714597+05:30', '2024-10-09 18:01:59.714606+05:30', true, true, false, 'databooks', 'Databooks', 'view:databook', 2, 'Datasheet level row and column restrictions won''t apply to users with this permission.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('export:datasheet', 'Export datasheet', '2024-10-09 18:01:59.71465+05:30', '2024-10-09 18:01:59.714659+05:30', true, true, false, 'databooks', 'Databooks', 'view:databook', 2, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('delete:datasheet', 'Delete databooks', '2024-10-09 18:01:59.714702+05:30', '2024-10-09 18:01:59.714711+05:30', true, true, false, 'databooks', 'Databooks', 'view:databook', 2, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:commissionplan', 'View Commission Plan', '2024-10-09 18:01:59.714754+05:30', '2024-10-09 18:01:59.714762+05:30', true, true, false, 'commission_plans', 'Commission Plans', NULL, 3, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('edit:commissionplan', 'Edit Commission Plan', '2024-10-09 18:01:59.714805+05:30', '2024-10-09 18:01:59.714813+05:30', true, true, false, 'commission_plans', 'Commission Plans', 'view:commissionplan', 3, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:dashboard', 'View dashboards page', '2024-10-09 18:01:59.714017+05:30', '2024-10-09 18:01:59.714039+05:30', true, true, false, 'dashboard', 'Dashboard', NULL, 1, 'Users can only see the dashboards shared with them.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:payoutvalueothers', 'View the payout value of others', '2024-10-09 18:01:59.71548+05:30', '2024-10-09 18:01:59.715489+05:30', true, true, true, 'payouts_statements', 'Payouts & Statements', NULL, 5, 'Users can see their own payouts by default. This permission lets them see payout values of others based on their data permissions.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:crystal', 'Manage crystal views', '2024-10-09 18:01:59.715532+05:30', '2024-10-09 18:01:59.71554+05:30', true, true, false, 'crystal', 'Crystal', NULL, 6, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:quotas', 'View quotas page', '2024-10-09 18:01:59.715584+05:30', '2024-10-09 18:01:59.715592+05:30', true, true, true, 'quotas_draws', 'Quotas & Draws', NULL, 7, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:quotas', 'Manages quotas', '2024-10-09 18:01:59.715635+05:30', '2024-10-09 18:01:59.715644+05:30', true, true, true, 'quotas_draws', 'Quotas & Draws', 'view:quotas', 7, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:quotasettings', 'Manages quota settings', '2024-10-09 18:01:59.715686+05:30', '2024-10-09 18:01:59.715695+05:30', true, true, true, 'quotas_draws', 'Quotas & Draws', 'view:quotas', 7, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:hiddenquotas', 'View hidden quota categories', '2024-10-09 18:01:59.715738+05:30', '2024-10-09 18:01:59.715746+05:30', true, true, true, 'quotas_draws', 'Quotas & Draws', NULL, 7, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:draws', 'View draws page', '2024-10-09 18:01:59.71579+05:30', '2024-10-09 18:01:59.715798+05:30', true, true, true, 'quotas_draws', 'Quotas & Draws', NULL, 7, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:draws', 'Manage draws', '2024-10-09 18:01:59.715841+05:30', '2024-10-09 18:01:59.71585+05:30', true, true, true, 'quotas_draws', 'Quotas & Draws', 'view:draws', 7, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:users', 'View users page', '2024-10-09 18:01:59.715894+05:30', '2024-10-09 18:01:59.715902+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', NULL, 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:users', 'Create & Edit Users', '2024-10-09 18:01:59.715945+05:30', '2024-10-09 18:01:59.715954+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', 'view:users', 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:payroll', 'View Base Pay & Variable Pay', '2024-10-09 18:01:59.715997+05:30', '2024-10-09 18:01:59.716005+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', NULL, 8, 'Users can see their base pay and variable pay numbers by default. This permission lets them see payroll information of others based on their data permissions.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('edit:payroll', 'Edit Base Pay & Variable Pay', '2024-10-09 18:01:59.716048+05:30', '2024-10-09 18:01:59.716057+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', 'view:payroll', 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('export:users', 'Export users', '2024-10-09 18:01:59.7161+05:30', '2024-10-09 18:01:59.716108+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', 'view:users', 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('allow:impersonation', 'Can login as others', '2024-10-09 18:01:59.716152+05:30', '2024-10-09 18:01:59.71616+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', 'view:users', 8, 'Users with current role can login as users who belong to these roles', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:teams', 'View teams & pods page', '2024-10-09 18:01:59.716204+05:30', '2024-10-09 18:01:59.716212+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', NULL, 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:usergroups', 'Manage user groups', '2024-10-09 18:01:59.716256+05:30', '2024-10-09 18:01:59.716265+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', NULL, 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:usercustomfield', 'Manage user custom fields', '2024-10-09 18:01:59.716308+05:30', '2024-10-09 18:01:59.716317+05:30', true, true, true, 'manage_users', 'Teams, Groups & Users', NULL, 8, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:queries', 'View queries page', '2024-10-09 18:01:59.716359+05:30', '2024-10-09 18:01:59.716368+05:30', true, true, true, 'queries', 'Queries', NULL, 9, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('create:queries', 'Create queries', '2024-10-09 18:01:59.716411+05:30', '2024-10-09 18:01:59.716419+05:30', true, true, true, 'queries', 'Queries', 'view:queries', 9, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('edit:queries', 'Edit queries', '2024-10-09 18:01:59.716463+05:30', '2024-10-09 18:01:59.716471+05:30', true, true, true, 'queries', 'Queries', 'view:queries', 9, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('delete:queries', 'Delete queries', '2024-10-09 18:01:59.716514+05:30', '2024-10-09 18:01:59.716523+05:30', false, true, true, 'queries', 'Queries', 'view:queries', 9, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:config', 'Manage general configurations', '2024-10-09 18:01:59.716566+05:30', '2024-10-09 18:01:59.716574+05:30', true, true, false, 'settings', 'Settings', NULL, 10, 'Users can modify basic account settings, notifications, customise statements and approval workflows.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:contracts', 'Manage contracts', '2024-10-09 18:01:59.716617+05:30', '2024-10-09 18:01:59.716625+05:30', true, true, false, 'settings', 'Settings', NULL, 10, 'Users can setup integrations with contract management systems like DocuSign and manage contracts within Everstage.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:datasettings', 'Manage data & data integrations', '2024-10-09 18:01:59.716668+05:30', '2024-10-09 18:01:59.716677+05:30', true, true, false, 'settings', 'Settings', NULL, 10, 'Users can set up integrations, custom objects, upload data to objects, view audit logs, access commissions & data sync page.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:accountnotifications', 'Manage account notifications', '2024-10-09 18:01:59.716738+05:30', '2024-10-09 18:01:59.716747+05:30', true, true, false, 'settings', 'Settings', NULL, 10, 'This permission allows users to manage notifications at an account level.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:roles', 'Manage roles', '2024-10-09 18:01:59.716791+05:30', '2024-10-09 18:01:59.7168+05:30', true, true, false, 'advanced_permissions', 'Advanced Admin Controls', NULL, 11, 'Users cannot modify their own role.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:owndata', 'Modify their own data across Everstage', '2024-10-09 18:01:59.716843+05:30', '2024-10-09 18:01:59.716852+05:30', true, true, false, 'advanced_permissions', 'Advanced Admin Controls', NULL, 11, 'Users can modify plans they belong to & their own quotas, draws, commission/draw adjustment, user information.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:reportenrich', 'Manage tags & report enrichment', '2024-10-09 18:01:59.716896+05:30', '2024-10-09 18:01:59.716904+05:30', true, false, false, 'advanced_permissions', 'Advanced Admin Controls', NULL, 11, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:alladmins', 'Manage All Admins', '2024-10-09 18:01:59.716948+05:30', '2024-10-09 18:01:59.716956+05:30', true, false, false, 'advanced_permissions', 'Advanced Admin Controls', NULL, 11, 'Users with this permission are considered as Power Admins in Everstage (for internal use).', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:everstage', 'General permission for common api''s', '2024-10-09 18:01:59.717+05:30', '2024-10-09 18:01:59.717008+05:30', true, false, false, 'everstage', 'Everstage', NULL, 12, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:adminui', 'Admin UI permission', '2024-10-09 18:01:59.717051+05:30', '2024-10-09 18:01:59.71706+05:30', true, false, false, 'everstage', 'Everstage', NULL, 12, '', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:agentworkbench', 'Access agent workbench', '2024-10-09 18:01:59.717102+05:30', '2024-10-09 18:01:59.717111+05:30', true, true, false, 'everai', 'EverAI Admin Controls', NULL, 13, 'Users can access the chat interface to interact with AI Agents.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:autogendescription', 'Manage autogenerated descriptions', '2024-10-09 18:01:59.717154+05:30', '2024-10-09 18:01:59.717162+05:30', true, true, false, 'everai', 'EverAI Admin Controls', NULL, 13, 'Enables datasheet descriptions and provides option to generate descriptions for datasheets.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:commissionformulageneration', 'Manage commission formula generation', '2024-10-09 18:01:59.717205+05:30', '2024-10-09 18:01:59.717214+05:30', true, true, false, 'everai', 'EverAI Admin Controls', NULL, 13, 'Users can access the commission formula generation AI to create and manage formulas.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:datasheetaigeneration', 'Manage datasheet AI generation', '2024-10-09 18:01:59.717257+05:30', '2024-10-09 18:01:59.717265+05:30', true, true, false, 'everai', 'EverAI Admin Controls', NULL, 13, 'Users can access the datasheet AI generation to create and manage AI generated datasheets.', 'icm', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:cpqdashboard', 'View dashboards page', '2025-07-08 08:19:08.393', '2025-07-08 08:19:08.393', true, true, false, 'dashboard', 'Dashboard', NULL, 1, 'Users can only see the dashboards shared with them.', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:cpqdashboard', 'Create & Edit custom dashboards', '2025-07-08 08:19:08.449', '2025-07-08 08:19:08.450', true, true, false, 'dashboard', 'Dashboard', 'view:cpqdashboard', 1, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('view:quotes', 'View quotes', '2025-07-08 08:19:08.485', '2025-07-08 08:19:08.485', true, true, true, 'quotes', 'Quotes', NULL, 2, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('edit:quotes', 'Edit quotes', '2025-07-08 08:19:08.528', '2025-07-08 08:19:08.528', true, true, true, 'quotes', 'Quotes', 'view:quotes', 2, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('create:quotes', 'Create quotes', '2025-07-08 08:19:08.562', '2025-07-08 08:19:08.562', true, true, true, 'quotes', 'Quotes', 'view:quotes', 2, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('delete:quotes', 'Delete quotes', '2025-07-08 08:19:08.596', '2025-07-08 08:19:08.596', true, true, true, 'quotes', 'Quotes', 'view:quotes', 2, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:cpqsettings', 'Manage settings', '2025-07-08 08:19:08.646', '2025-07-08 08:19:08.646', true, true, true, 'cpq_settings', 'Settings', NULL, 3, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:productsandprices', 'Manage products and prices', '2025-07-08 08:19:08.689', '2025-07-08 08:19:08.689', true, true, true, 'cpq_settings', 'Settings', 'view:cpqsettings', 3, '', 'cpq', 31617);
INSERT INTO public.permissions (permission_id, permission_name, created_at, updated_at, is_active, show_to_user, show_data_permissions, component_system_name, component_display_name, parent_id, component_order, permission_description, "module", client_id) VALUES('manage:cpqusersandgroups', 'Manage users and groups', '2025-07-08 08:19:08.723', '2025-07-08 08:19:08.723', true, true, true, 'cpq_settings', 'Settings', 'view:cpqsettings', 3, '', 'cpq', 31617);

------------------------------------------------------------------------------------------------------------------------

-- role_permissions

INSERT INTO public.role_permissions (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, role_permission_id, display_name, description, show_to_user, permissions, created_at, updated_at, is_editable, "module", client_id) VALUES('2025-07-18 07:23:56.700', NULL, false, NULL, 'd2dafd7a-34f0-4d14-821e-60c2de715f5a'::uuid, 'Admin', '', true, '{"quotes": {"permissions": ["view:quotes", "edit:quotes", "create:quotes", "delete:quotes"], "data_permission": {"type": "ALL_DATA"}, "impersonated_user_roles": ["ALL"]}, "crystal": {"permissions": ["manage:crystal"], "data_permission": null}, "queries": {"permissions": ["view:queries", "create:queries", "edit:queries", "delete:queries"], "data_permission": {"type": "ALL_DATA"}}, "settings": {"permissions": ["manage:productsandprices", "manage:cpqusersandgroups", "manage:cpqsettings", "manage:datasettings"], "data_permission": null}, "dashboard": {"permissions": ["view:dashboard", "manage:dashboard", "view:cpqdashboard", "manage:cpqdashboard"], "data_permission": null}, "databooks": {"permissions": ["view:databook", "manage:databook", "manage:datasheetpermissions", "manage:datasheetadjustments", "export:datasheet", "delete:datasheet"], "data_permission": null}, "everstage": {"permissions": ["view:everstage", "view:approvals"]}, "manage_users": {"permissions": ["manage:users", "view:payroll", "edit:payroll", "export:users", "allow:impersonation", "view:teams", "manage:teams", "manage:usergroups", "manage:usercustomfield", "view:users", "delete:users"], "data_permission": {"type": "ALL_DATA"}, "impersonated_user_roles": ["ALL"]}, "quotas_draws": {"permissions": ["view:quotas", "manage:quotas", "view:hiddenquotas", "view:draws", "manage:draws", "manage:quotasettings"], "data_permission": {"type": "ALL_DATA"}}, "global_search": {"permissions": ["view:globalsearch"], "data_permission": null}, "commission_plans": {"permissions": ["view:commissionplan", "edit:commissionplan", "create:commissionplan", "delete:commissionplan"], "plans_scope": {"can_edit": "ALL_PLANS", "can_view": "ALL_PLANS", "can_delete": "ALL_PLANS"}, "data_permission": null}, "payouts_statements": {"permissions": ["view:payouts", "manage:payouts", "view:requestapprovals", "export:payouts", "view:hiddencriteria", "manage:commissionadjustment", "view:payoutvalueothers"], "data_permission": {"type": "ALL_DATA"}}, "advanced_permissions": {"permissions": ["manage:roles", "manage:owndata", "manage:reportenrich", "manage:alladmins"], "data_permission": null}}'::jsonb, '2025-07-08 02:49:08.785', '2025-07-18 07:23:56.706', false, 'cpq', 31617);
INSERT INTO public.role_permissions (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, role_permission_id, display_name, description, show_to_user, permissions, created_at, updated_at, is_editable, "module", client_id) VALUES('2025-07-08 08:19:08.785', NULL, false, NULL, '94a90761-9fb5-4863-ae0c-a0a6dfebd5da'::uuid, 'Manager', '', true, '{"quotes": {"permissions": ["view:quotes", "edit:quotes", "create:quotes", "delete:quotes"], "data_permission": {"type": "INDIVIDUAL_AND_TEAM_DATA", "selected_user_groups": [], "is_user_groups_selected": false, "is_reporting_team_selected": true}}, "everstage": {"permissions": ["view:everstage", "view:approvals"]}}'::jsonb, '2025-07-08 08:19:08.785', '2025-07-08 08:19:08.798', false, 'cpq', 31617);
INSERT INTO public.role_permissions (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, role_permission_id, display_name, description, show_to_user, permissions, created_at, updated_at, is_editable, "module", client_id) VALUES('2025-07-08 08:19:08.785', NULL, false, NULL, 'b0a62c80-d2d3-475d-ba51-e05fd3c89913'::uuid, 'Sales Rep', '', true, '{"quotes": {"permissions": ["view:quotes", "edit:quotes", "create:quotes", "delete:quotes"], "data_permission": {"type": "INDIVIDUAL_DATA"}}, "everstage": {"permissions": ["view:everstage", "view:approvals"]}}'::jsonb, '2025-07-08 08:19:08.785', '2025-07-08 08:19:08.798', false, 'cpq', 31617);
INSERT INTO public.role_permissions (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, role_permission_id, display_name, description, show_to_user, permissions, created_at, updated_at, is_editable, "module", client_id) VALUES('2025-07-08 08:19:08.785', NULL, false, NULL, 'd6653025-dce9-41cd-b13b-6004cb752337'::uuid, 'Approver', '', true, '{"quotes": {"permissions": ["view:quotes", "edit:quotes"], "data_permission": {"type": "ALL_DATA"}}, "everstage": {"permissions": ["view:everstage", "view:approvals"]}}'::jsonb, '2025-07-08 08:19:08.785', '2025-07-08 08:19:08.798', false, 'cpq', 31617);

------------------------------------------------------------------------------------------------------------------------

-- transformation_config

INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:23:05.282+05:30', NULL, 'contacts', '4', 'company_id', 'co_4_company_id', 'Integer', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:23:05.255+05:30', NULL, 'contacts', '4', 'hs_object_id', 'co_4_record_id', 'Integer', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:23:05.259+05:30', NULL, 'contacts', '4', 'firstname', 'co_4_first_name', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:23:05.262+05:30', NULL, 'contacts', '4', 'lastname', 'co_4_last_name', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:23:05.265+05:30', NULL, 'contacts', '4', 'email', 'co_4_email', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:37:34.451+05:30', NULL, 'deals', '5', 'company_id', 'co_5_company_id', 'Integer', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 17:58:15.568+05:30', NULL, 'deals', '5', 'contact_id', 'co_5_contact_id', 'Integer', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:02:18.495+05:30', NULL, 'deals', '5', 'deal_currency_code', 'co_5_currency', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:37:34.417+05:30', NULL, 'deals', '5', 'hs_object_id', 'co_5_record_id', 'Integer', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:37:34.421+05:30', NULL, 'deals', '5', 'dealname', 'co_5_deal_name', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:19:36.042+05:30', NULL, 'companies', '2', 'hs_object_id', 'co_2_record_id', 'Integer', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:19:36.06+05:30', NULL, 'companies', '2', 'name', 'co_2_companyname', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 13:19:36.071+05:30', NULL, 'companies', '2', 'phone', 'co_2_phone_number', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:09:33.114+05:30', NULL, 'companies', '2', 'country', 'co_2_country_region__1', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:09:33.119+05:30', NULL, 'companies', '2', 'state', 'co_2_state_region', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:09:33.122+05:30', NULL, 'companies', '2', 'city', 'co_2_city', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:09:33.125+05:30', NULL, 'companies', '2', 'address', 'co_2_street_address', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:09:33.128+05:30', NULL, 'companies', '2', 'address2', 'co_2_street_address_2', 'String', NULL, '************************************', NULL);
INSERT INTO public.transformation_config (client_id, knowledge_begin_date, knowledge_end_date, source_object_id, destination_object_id, source_field, destination_field, field_type, transformation_logic_id, integration_id, additional_config) VALUES (31617, '2024-10-09 18:09:33.131+05:30', NULL, 'companies', '2', 'zip', 'co_2_postal_code', 'String', NULL, '************************************', NULL);

------------------------------------------------------------------------------------------------------------------------

-- upstream_timestamps

INSERT INTO public.upstream_timestamps (integration_id, api_changes_synced_till, api_deletes_synced_till, upstream_source_synced_till, last_extracted_by, historic_sync_date, client_id) VALUES ('************************************', '2024-02-10 16:52:52.859828+05:30', '2024-02-10 16:52:52.859828+05:30', '2024-02-10 13:11:46.60927+05:30', 'f70c35dc-6f61-4ba0-b258-61fe318e849b', '2024-02-10 13:11:46.60927+05:30', 31617);
INSERT INTO public.upstream_timestamps (integration_id, api_changes_synced_till, api_deletes_synced_till, upstream_source_synced_till, last_extracted_by, historic_sync_date, client_id) VALUES ('************************************', '2024-02-10 16:52:52.861686+05:30', '2024-02-10 16:52:52.861686+05:30', '2024-02-10 13:11:46.623723+05:30', 'f70c35dc-6f61-4ba0-b258-61fe318e849b', '2024-02-10 13:11:46.60927+05:30', 31617);
INSERT INTO public.upstream_timestamps (integration_id, api_changes_synced_till, api_deletes_synced_till, upstream_source_synced_till, last_extracted_by, historic_sync_date, client_id) VALUES ('************************************', '2024-02-10 16:52:52.860705+05:30', '2024-02-10 16:52:52.860705+05:30', '2024-02-10 13:11:46.611063+05:30', 'f70c35dc-6f61-4ba0-b258-61fe318e849b', '2024-02-10 13:11:46.60927+05:30', 31617);

------------------------------------------------------------------------------------------------------------------------

-- user_group

INSERT INTO public.user_group (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, user_group_name, user_group_config, created_by, created_at, client_id) VALUES ('2024-10-09 18:02:00.405079+05:30', NULL, false, NULL, '31eafaf7-9d3e-4444-9571-c50ec819eb66', 'All Payees', '{"rules": {"rule_list": [{"value": "5982f4cc-8ab8-41a3-9a45-92afa1113978", "col_name": "user_role", "operator": "CONTAINS", "field_type": "UserRoleArray"}], "aggregation": "AND", "exclude_users": []}, "user_options": ["ACTIVE", "YET_TO_JOIN"], "static_members": []}', 'Everstage', '2024-10-09 18:02:00.405079+05:30', 31617);
INSERT INTO public.user_group (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, user_group_name, user_group_config, created_by, created_at, client_id) VALUES ('2024-10-09 18:02:00.405079+05:30', NULL, false, NULL, 'd66c88c0-9580-4a72-a174-8993304461b7', 'All Admins', '{"rules": {"rule_list": [{"value": ["98bc2374-030e-4c1e-8924-1908b1be1d97", "a044a67b-724d-40b1-89a0-81a6bf9129d5"], "col_name": "user_role", "operator": "CONTAINS_ONE_OF", "field_type": "UserRoleArray"}], "aggregation": "AND", "exclude_users": []}, "user_options": ["ACTIVE", "YET_TO_JOIN"], "static_members": []}', 'Everstage', '2024-10-09 18:02:00.405079+05:30', 31617);
INSERT INTO public.user_group (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, user_group_name, user_group_config, created_by, created_at, client_id) VALUES ('2024-10-23 18:13:12.388501+05:30', NULL, false, '{"server": "0.0.0.0", "updated_by": "<EMAIL>", "prohibited_usage___login_user": "<EMAIL>"}', '4b9bca18-a7ed-467b-b8e1-aabe7dea81c4', 'Deals Desk', '{"rules": {"rule_list": [], "aggregation": "AND", "exclude_users": []}, "user_options": ["ACTIVE"], "static_members": ["<EMAIL>", "<EMAIL>"]}', '<EMAIL>', '2024-10-23 18:13:12.388522+05:30', 31617);

------------------------------------------------------------------------------------------------------------------------

-- user_group_members

INSERT INTO public.user_group_members (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, employee_email_id, client_id) VALUES ('2024-10-15 20:03:38.761104+05:30', NULL, false, NULL, '31eafaf7-9d3e-4444-9571-c50ec819eb66', '<EMAIL>', 31617);
INSERT INTO public.user_group_members (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, employee_email_id, client_id) VALUES ('2024-10-23 18:13:12.388501+05:30', NULL, false, NULL, '4b9bca18-a7ed-467b-b8e1-aabe7dea81c4', '<EMAIL>', 31617);
INSERT INTO public.user_group_members (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, employee_email_id, client_id) VALUES ('2024-10-23 18:13:12.388501+05:30', NULL, false, NULL, '4b9bca18-a7ed-467b-b8e1-aabe7dea81c4', '<EMAIL>', 31617);
INSERT INTO public.user_group_members (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, employee_email_id, client_id) VALUES ('2024-11-20 13:34:29.386847+05:30', NULL, false, NULL, 'd66c88c0-9580-4a72-a174-8993304461b7', '<EMAIL>', 31617);
INSERT INTO public.user_group_members (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, user_group_id, employee_email_id, client_id) VALUES ('2024-11-20 13:34:35.537561+05:30', NULL, false, NULL, 'd66c88c0-9580-4a72-a174-8993304461b7', '<EMAIL>', 31617);

------------------------------------------------------------------------------------------------------------------------

-- form_builder

update public.form_builder
set
    knowledge_end_date = now()
where
    client_id = 31617
    and form_builder_id = 'f7c3c817-2a5c-4a88-94f1-fae970bd7395';

INSERT INTO public.form_builder
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, form_builder_id, form_builder_name, form_builder_description, form_spec, status, created_by, updated_by, created_at, client_id)
VALUES(now(), NULL, false, NULL, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395'::uuid, 'Everstage form', NULL, '{
  "label": "Deal Hub Form",
  "sections": {
    "section1": {
      "id": "section1",
      "label": "Opportunity Details",
      "fields": {
        "field1": {
          "id": "field1",
          "label": "Quote ID",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "Quote ID",
          "label_placement": "top"
        },
        "field2": {
          "id": "field2",
          "label": "Quote Name",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Quote Name",
          "label_placement": "top"
        },
        "field3": {
          "id": "field3",
          "label": "Opportunity",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "lookup",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Opportunity Name",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_5_deal_name",
            "source": "datasheet_data",
            "filters": [],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"
          }
        },
        "field4": {
          "id": "field4",
          "label": "Opportunity Id",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Opportunity Id, lookup from Opportunity Name",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_5_record_id",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_5_deal_name",
                  "name": "Deal Name",
                  "system_name": "co_5_deal_name"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field3",
                  "name": "Opportunity",
                  "data_type": "String",
                  "system_name": "field3",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"
          }
        },
        "field5": {
          "id": "field5",
          "label": "Account",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Account Name",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_companyname",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field6": {
          "id": "field6",
          "label": "Account Id",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Account Id",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_5_company_id",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_5_deal_name",
                  "name": "Deal Name",
                  "system_name": "co_5_deal_name"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field3",
                  "name": "Opportunity",
                  "data_type": "String",
                  "system_name": "field3",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"
          }
        },
        "field7": {
          "id": "field7",
          "label": "Contact",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Contact Name",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "cf_name",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_4_record_id",
                  "name": "Record ID",
                  "system_name": "co_4_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field8",
                  "name": "Contact Id",
                  "data_type": "String",
                  "system_name": "field8",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"
          }
        },
        "field8": {
          "id": "field8",
          "label": "Contact Id",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Contact Id",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_5_contact_id",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_5_deal_name",
                  "name": "Deal Name",
                  "system_name": "co_5_deal_name"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field3",
                  "name": "Opportunity",
                  "data_type": "String",
                  "system_name": "field3",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25"
          }
        },
        "field9": {
          "id": "field9",
          "tag": "currency",
          "label": "Currency",
          "value": "USD",
          "tag_type": "pricebook",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": true,
            "is_read_only": true
          },
          "description": "Currency",
          "label_placement": "top"
        },
        "field10": {
          "id": "field10",
          "label": "Price Book",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Price Book",
          "label_placement": "top"
        },
        "field11": {
          "id": "field11",
          "label": "Valid Till",
          "value": "",
          "data_type": "date",
          "help_text": "",
          "field_type": "date",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Valid Till",
          "label_placement": "top"
        },
        "field12": {
          "id": "field12",
          "label": "Deal Type",
          "value": "",
          "options": [
            { "label": "New Business", "value": "New Business" },
            { "label": "Change Order", "value": "Change Order" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Type of Deal",
          "label_placement": "top"
        },
        "field13": {
          "id": "field13",
          "label": "Data Center Location",
          "value": "",
          "options": [
            { "label": "US Data Center", "value": "US Data Center" },
            { "label": "EU Data Center", "value": "EU Data Center" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "US / EU data center",
          "label_placement": "top"
        },
        "field52": {
          "id": "field52",
          "label": "Contact Email",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "email",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Contact Email",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_4_email",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_4_record_id",
                  "name": "Record ID",
                  "system_name": "co_4_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field8",
                  "name": "Contact Id",
                  "data_type": "String",
                  "system_name": "field8",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27"
          }
        }
      },
      "help_text": "",
      "is_hidden": false,
      "description": "Basic information about the Quote like Opportunity, Account , Contact , etc. ",
      "field_order": [
        "field1",
        "field2",
        "field3",
        "field4",
        "field5",
        "field6",
        "field7",
        "field52",
        "field8",
        "field9",
        "field10",
        "field11",
        "field12",
        "field13"
      ]
    },
    "section2": {
      "id": "section2",
      "label": "Contact Details",
      "fields": {
        "field14": {
          "id": "field14",
          "label": "Country",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Country",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_country_region__1",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field15": {
          "id": "field15",
          "label": "State",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "State",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_state_region",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field16": {
          "id": "field16",
          "label": "City",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "City",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_city",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field17": {
          "id": "field17",
          "label": "Street Address Line 1",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Street Address Line 1",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_street_address",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field18": {
          "id": "field18",
          "label": "Street Address Line 2",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Street Address Line 2",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_street_address_2",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field19": {
          "id": "field19",
          "label": "Postal Code",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Postal Code",
          "label_placement": "top",
          "options_lookup_spec": {
            "column": "co_2_postal_code",
            "source": "datasheet_data",
            "filters": [
              {
                "token": {
                  "key": "co_2_record_id",
                  "name": "Record ID",
                  "system_name": "co_2_record_id"
                },
                "token_type": "DATASHEET_VARIABLES"
              },
              {
                "token": { "key": "==", "name": "==" },
                "token_type": "OPERATORS"
              },
              {
                "token": {
                  "key": "field6",
                  "name": "Account Id",
                  "data_type": "String",
                  "system_name": "field6",
                  "data_type_id": 4
                },
                "token_type": "FORM_VARIABLES"
              }
            ],
            "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
            "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c"
          }
        },
        "field20": {
          "id": "field20",
          "label": "Full Address from Hubspot",
          "value": "",
          "formula": [
            {
              "token": {
                "key": "Concat(,, [Address Line 1, Address Line 2, City, State, Country, Postal Code])",
                "args": [
                  ", ",
                  [
                    {
                      "token": {
                        "key": "field17",
                        "name": "field17",
                        "data_type": "String",
                        "system_name": "field17"
                      },
                      "token_type": "FORM_VARIABLES"
                    },
                    {
                      "token": {
                        "key": "field18",
                        "name": "field18",
                        "data_type": "String",
                        "system_name": "field18"
                      },
                      "token_type": "FORM_VARIABLES"
                    },
                    {
                      "token": {
                        "key": "field16",
                        "name": "field16",
                        "data_type": "String",
                        "system_name": "field16"
                      },
                      "token_type": "FORM_VARIABLES"
                    },
                    {
                      "token": {
                        "key": "field15",
                        "name": "field15",
                        "data_type": "String",
                        "system_name": "field15"
                      },
                      "token_type": "FORM_VARIABLES"
                    },
                    {
                      "token": {
                        "key": "field14",
                        "name": "field14",
                        "data_type": "String",
                        "system_name": "field14"
                      },
                      "token_type": "FORM_VARIABLES"
                    },
                    {
                      "token": {
                        "key": "field19",
                        "name": "field19",
                        "data_type": "String",
                        "system_name": "field19"
                      },
                      "token_type": "FORM_VARIABLES"
                    }
                  ]
                ],
                "name": "Concat(,, [Address Line 1, Address Line 2, City, State, Country, Postal Code])",
                "type": "VARIABLE",
                "data_type": "String",
                "function_name": "Concat",
                "token_category": "DYNAMIC"
              },
              "token_type": "FUNCTIONS"
            }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Full Address from Hubspot",
          "label_placement": "top"
        },
        "field21": {
          "id": "field21",
          "label": "Change Address?",
          "value": false,
          "data_type": "boolean",
          "help_text": "",
          "field_type": "checkbox",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Change Address?",
          "label_placement": "right"
        },
        "field22": {
          "id": "field22",
          "label": "Manual Address",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Manual Address",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": false,
      "description": "General Contact Details ",
      "field_order": [
        "field14",
        "field15",
        "field16",
        "field17",
        "field18",
        "field19",
        "field20",
        "field21",
        "field22"
      ]
    },
    "section3": {
      "id": "section3",
      "label": "Support & Connectors",
      "fields": {
        "field23": {
          "id": "field23",
          "label": "Include limitation of 100 hours/year White Glove support",
          "value": "Include (Limit)",
          "options": [
            { "label": "Include (Limit)", "value": "Include (Limit)" },
            { "label": "Exclude (No limit)", "value": "Exclude (No limit)" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Include limitation of 100 hours/year White Glove support",
          "label_placement": "top"
        },
        "field24": {
          "id": "field24",
          "label": "Waive of support",
          "value": "",
          "options": [
            { "label": "Yes", "value": "Yes" },
            { "label": "No", "value": "No" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Waive of support",
          "label_placement": "top"
        },
        "field25": {
          "id": "field25",
          "label": "Number of Connectors",
          "value": "",
          "data_type": "number",
          "help_text": "",
          "field_type": "number",
          "properties": {
            "max": 15,
            "min": 0,
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Number of Connectors",
          "label_placement": "top"
        },
        "field26": {
          "id": "field26",
          "label": "Connector names",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Connector names",
          "label_placement": "top"
        },
        "field27": {
          "id": "field27",
          "label": "Waive of connector charges",
          "value": "",
          "options": [
            { "label": "Yes", "value": "Yes" },
            { "label": "No", "value": "No" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Waive of connector charges",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": false,
      "description": "Details about the support limitations, connectors, and related charges.",
      "field_order": ["field23", "field24", "field25", "field26", "field27"]
    },
    "section4": {
      "id": "section4",
      "label": "Implementation Fee & Modules",
      "fields": {
        "field28": {
          "id": "field28",
          "label": "Include implementation package?",
          "value": "",
          "options": [
            { "label": "Yes", "value": "Yes" },
            { "label": "No", "value": "No" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Include implementation package?",
          "label_placement": "top"
        },
        "field29": {
          "id": "field29",
          "label": "Include ASC606 module?",
          "value": "",
          "options": [
            { "label": "Yes", "value": "Yes" },
            { "label": "No", "value": "No" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Include ASC606 module?",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": true,
      "description": "Details about the implementation package and modules.",
      "field_order": ["field28", "field29"]
    },
    "section5": {
      "id": "section5",
      "label": "Payment Terms",
      "fields": {
        "field30": {
          "id": "field30",
          "label": "Payment Term",
          "value": "",
          "options": [
            { "label": "Annual Upfront", "value": "Annual Upfront" },
            { "label": "Semi Annual", "value": "Semi Annual" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Select the payment term.",
          "label_placement": "top"
        },
        "field31": {
          "id": "field31",
          "label": "Payment Method",
          "value": "",
          "options": [
            {
              "label": "Wire Transfer to Everstage",
              "value": "Wire Transfer to Everstage"
            },
            { "label": "ACH", "value": "ACH" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Select the payment method.",
          "label_placement": "top"
        },
        "field32": {
          "id": "field32",
          "label": "Credit Terms",
          "value": "",
          "options": [
            { "label": "30", "value": "30" },
            { "label": "45", "value": "45" },
            { "label": "60", "value": "60" },
            { "label": "75", "value": "75" },
            { "label": "90", "value": "90" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Select the credit terms in days.",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": false,
      "description": "Payment term details, including method and credit terms.",
      "field_order": ["field30", "field31", "field32"]
    },
    "section6": {
      "id": "section6",
      "label": "Output Document",
      "fields": {
        "field33": {
          "id": "field33",
          "label": "Terms of Use",
          "value": "",
          "options": [
            {
              "label": "Online Terms (Website)",
              "value": "Online Terms (Website)"
            },
            { "label": "MSA", "value": "MSA" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Select the Terms of Use.",
          "label_placement": "top"
        },
        "field34": {
          "id": "field34",
          "label": "DPA Signed?",
          "value": "No",
          "options": [
            { "label": "Yes", "value": "Yes" },
            { "label": "No", "value": "No" }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_select",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Has the DPA been signed?",
          "label_placement": "top"
        },
        "field35": {
          "id": "field35",
          "label": "Connectors Wording",
          "value": "",
          "formula": [
            {
              "token": {
                "args": [
                  ": ",
                  [
                    {
                      "token": {
                        "key": "The order form includes charges for the following connectors",
                        "args": [
                          "String",
                          "The order form includes charges for the following connectors"
                        ],
                        "name": "The order form includes charges for the following connectors",
                        "data_type": "String"
                      },
                      "token_type": "CONSTANT_VARIABLES"
                    },
                    {
                      "token": {
                        "key": "field26",
                        "name": "field26",
                        "data_type": "String",
                        "system_name": "field26"
                      },
                      "token_type": "FORM_VARIABLES"
                    }
                  ]
                ],
                "name": "Concat(,,[Order Form Includes Charges For The Following Connectors, field26])",
                "type": "VARIABLE",
                "data_type": "String",
                "function_name": "Concat",
                "token_category": "DYNAMIC"
              },
              "token_type": "FUNCTIONS"
            }
          ],
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "The order form includes charges for the following connectors:",
          "label_placement": "top"
        },
        "field36": {
          "id": "field36",
          "label": "Change Terms",
          "value": false,
          "data_type": "boolean",
          "help_text": "",
          "field_type": "checkbox",
          "properties": {
            "is_hidden": false,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Option to change terms.",
          "label_placement": "right"
        },
        "field37": {
          "id": "field37",
          "label": "Auto Renewal",
          "value": false,
          "data_type": "boolean",
          "help_text": "",
          "field_type": "checkbox",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "Is auto-renewal enabled?",
          "label_placement": "right"
        },
        "field51": {
          "id": "field51",
          "label": "DPA Wording",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": false
          },
          "description": "DPA Wording",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": false,
      "description": "Details related to the output document and terms.",
      "field_order": [
        "field33",
        "field34",
        "field51",
        "field35",
        "field36",
        "field37"
      ]
    },
    "section7": {
      "id": "section7",
      "tag": "select_product",
      "label": "Select Product",
      "fields": {
        "field38": {
          "id": "field38",
          "tag": "select_product",
          "label": "Select Product",
          "value": "",
          "table_id": 1,
          "data_type": "array",
          "help_text": "",
          "field_type": "table",
          "properties": {
            "is_hidden": false,
            "is_mandatory": true,
            "is_read_only": false
          },
          "description": "Table containing order details",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": false,
      "description": "Select Product Step",
      "field_order": ["field38"]
    },
    "section8": {
      "id": "section8",
      "tag": "approval_matrix",
      "label": "Approval Matrix",
      "fields": {
        "field45": {
          "id": "field45",
          "label": "EP - Mike Approval",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "EP Mike Approval",
          "label_placement": "top"
        },
        "field46": {
          "id": "field46",
          "label": "EP - Siva Approval",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "EP Siva Approval",
          "label_placement": "top"
        },
        "field47": {
          "id": "field47",
          "label": "Implementation - Mike Approval",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "Implementation Mike Approval",
          "label_placement": "top"
        },
        "field48": {
          "id": "field48",
          "label": "Implementation - Siva Approval",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "Implementation Siva Approval",
          "label_placement": "top"
        },
        "field49": {
          "id": "field49",
          "label": "Support - Mike Approval",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "Support Mike Approval",
          "label_placement": "top"
        },
        "field50": {
          "id": "field50",
          "label": "Support - Siva Approval",
          "value": "",
          "data_type": "string",
          "help_text": "",
          "field_type": "single_line",
          "properties": {
            "is_hidden": true,
            "is_mandatory": false,
            "is_read_only": true
          },
          "description": "Support Siva Approval",
          "label_placement": "top"
        }
      },
      "help_text": "",
      "is_hidden": true,
      "description": "Approval Matrix hidden fields",
      "field_order": [
        "field45",
        "field46",
        "field47",
        "field48",
        "field49",
        "field50"
      ]
    }
  },
  "section_order": [
    "section1",
    "section2",
    "section3",
    "section4",
    "section5",
    "section6",
    "section7",
    "section8"
  ]
}
'::jsonb, 'active', '<EMAIL>', NULL, '2025-05-30 10:27:03.975', 31617);

------------------------------------------------------------------------------------------------------------------------

-- form_rules

INSERT INTO public.form_rules
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, form_builder_id, form_rule_id, form_rule_name, form_rule_description, is_automated_rule, created_by, source_fields, destination_fields, is_lookup, is_formula, status, "trigger", actions, updated_by, created_at, updated_at, client_id)
VALUES(now(), NULL, false, NULL, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395'::uuid, '416f0660-45f6-45cc-ab41-42d7fcd12d9f'::uuid, NULL, NULL, true, '<EMAIL>', '{field3}', '{field4,field6,field8}', true, false, 'active', 'HasChanged(field3)', '[{"then": [], "condition": [{"token": {"key": "IsNotEmpty(field3)", "args": [{"token": {"key": "field3", "name": "field3", "data_type": "String", "system_name": "field3"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field3)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}]}, {"default": [{"value": "", "function": "SetValue", "component_id": "field4", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field6", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field8", "form_component": "field"}]}]'::jsonb, NULL, '2025-06-02 20:25:45.599', NULL, 31617);

INSERT INTO public.form_rules
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, form_builder_id, form_rule_id, form_rule_name, form_rule_description, is_automated_rule, created_by, source_fields, destination_fields, is_lookup, is_formula, status, "trigger", actions, updated_by, created_at, updated_at, client_id)
VALUES(now(), NULL, false, NULL, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395'::uuid, '0c8d61fe-f305-4fcb-998e-e271820e8e4e'::uuid, NULL, NULL, true, '<EMAIL>', '{field6}', '{field5,field14,field15,field16,field17,field18,field19}', true, false, 'active', 'HasChanged(field6)', '[{"then": [], "condition": [{"token": {"key": "IsNotEmpty(field6)", "args": [{"token": {"key": "field6", "name": "field6", "data_type": "String", "system_name": "field6"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field6)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}]}, {"default": [{"value": "", "function": "SetValue", "component_id": "field5", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field14", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field15", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field16", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field17", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field18", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field19", "form_component": "field"}]}]'::jsonb, NULL, '2025-06-02 20:25:45.599', NULL, 31617);

INSERT INTO public.form_rules
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, form_builder_id, form_rule_id, form_rule_name, form_rule_description, is_automated_rule, created_by, source_fields, destination_fields, is_lookup, is_formula, status, "trigger", actions, updated_by, created_at, updated_at, client_id)
VALUES(now(), NULL, false, NULL, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395'::uuid, '4533d510-d0dc-414f-a40b-19921a3d7959'::uuid, NULL, NULL, true, '<EMAIL>', '{field8}', '{field7,field52}', true, false, 'active', 'HasChanged(field8)', '[{"then": [], "condition": [{"token": {"key": "IsNotEmpty(field8)", "args": [{"token": {"key": "field8", "name": "field8", "data_type": "String", "system_name": "field8"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field8)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}]}, {"default": [{"value": "", "function": "SetValue", "component_id": "field7", "form_component": "field"}, {"value": "", "function": "SetValue", "component_id": "field52", "form_component": "field"}]}]'::jsonb, NULL, '2025-06-02 20:25:45.599', NULL, 31617);

INSERT INTO public.form_rules
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, form_builder_id, form_rule_id, form_rule_name, form_rule_description, is_automated_rule, created_by, source_fields, destination_fields, is_lookup, is_formula, status, "trigger", actions, updated_by, created_at, updated_at, client_id)
VALUES(now(), NULL, false, NULL, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395'::uuid, '0ca127c0-b512-4706-b5fd-d67fd386c701'::uuid, NULL, NULL, true, '<EMAIL>', '{field16,field14,field17,field18,field19,field15}', '{field20}', false, true, 'active', 'HasChanged(field16) or HasChanged(field14) or HasChanged(field17) or HasChanged(field18) or HasChanged(field19) or HasChanged(field15)', '[{"then": [{"value": "", "function": "SetValue", "component_id": "field20", "form_component": "field"}], "condition": [{"token": {"key": "IsNotEmpty(field16)", "args": [{"token": {"key": "field16", "name": "field16", "data_type": "String", "system_name": "field16"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field16)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "IsNotEmpty(field14)", "args": [{"token": {"key": "field14", "name": "field14", "data_type": "String", "system_name": "field14"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field14)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "IsNotEmpty(field17)", "args": [{"token": {"key": "field17", "name": "field17", "data_type": "String", "system_name": "field17"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field17)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "IsNotEmpty(field18)", "args": [{"token": {"key": "field18", "name": "field18", "data_type": "String", "system_name": "field18"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field18)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "IsNotEmpty(field19)", "args": [{"token": {"key": "field19", "name": "field19", "data_type": "String", "system_name": "field19"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field19)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}, {"token": {"key": "OR", "name": "OR"}, "token_type": "OPERATORS"}, {"token": {"key": "IsNotEmpty(field15)", "args": [{"token": {"key": "field15", "name": "field15", "data_type": "String", "system_name": "field15"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field15)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}]}, {"default": [{"value": "", "function": "SetValue", "component_id": "field20", "form_component": "field"}]}]'::jsonb, NULL, '2025-06-02 20:25:45.599', NULL, 31617);

INSERT INTO public.form_rules
(knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, form_builder_id, form_rule_id, form_rule_name, form_rule_description, is_automated_rule, created_by, source_fields, destination_fields, is_lookup, is_formula, status, "trigger", actions, updated_by, created_at, updated_at, client_id)
VALUES(now(), NULL, false, NULL, 'f7c3c817-2a5c-4a88-94f1-fae970bd7395'::uuid, '4af7611f-65f9-4da4-b695-a06e4884cd5c'::uuid, NULL, NULL, true, '<EMAIL>', '{field26}', '{field35}', false, true, 'active', 'HasChanged(field26)', '[{"then": [{"value": "", "function": "SetValue", "component_id": "field35", "form_component": "field"}], "condition": [{"token": {"key": "IsNotEmpty(field26)", "args": [{"token": {"key": "field26", "name": "field26", "data_type": "String", "system_name": "field26"}, "token_type": "FORM_VARIABLES"}], "name": "IsNotEmpty(field26)", "type": "VARIABLE", "data_type": "Boolean", "function_name": "IsNotEmpty", "token_category": "DYNAMIC"}, "token_type": "FUNCTIONS"}]}, {"default": [{"value": "", "function": "SetValue", "component_id": "field35", "form_component": "field"}]}]'::jsonb, NULL, '2025-06-02 20:25:45.599', NULL, 31617);

------------------------------------------------------------------------------------------------------------------------

-- product_catalog_product

INSERT INTO product_catalog_product (knowledge_begin_date,knowledge_end_date,is_deleted,additional_details,product_id,"name",sku,category,billing_type,effective_start_date,effective_end_date,created_at,created_by,custom_factors,charge_unit,cost_to_build,description,client_id) VALUES
	 ('2025-04-29 08:20:28.943814+05:30',NULL,false,NULL,'bfe7dbd3-133a-4c40-8a98-c947e2aa0077','Standard White Glove Support','SUPPORT','services','recurring','2025-04-29 05:30:00+05:30',NULL,'2025-04-29 08:20:28.945699+05:30',NULL,'{"manager_id": null}','Accounts',NULL,'',31617),
	 ('2025-04-29 08:18:48.095813+05:30',NULL,false,NULL,'2f25d64d-c825-4dae-a6e7-479a58d11d53','Everstage Platform','PLATFORM','software','recurring','2025-04-29 05:30:00+05:30',NULL,'2025-04-29 08:18:48.098826+05:30',NULL,'{"manager_id": null}','Users',NULL,'',31617),
	 ('2025-04-29 08:13:14.961691+05:30',NULL,false,NULL,'8fc1e173-aee9-4ecb-989f-9537b07a12b1','Connector Charges','CONNECTOR','software','recurring','2025-04-29 05:30:00+05:30',NULL,'2025-04-29 08:13:14.963573+05:30',NULL,'{"manager_id": null}','Connectors',NULL,'',31617),
	 ('2025-04-29 08:10:19.570702+05:30',NULL,false,NULL,'70184a09-c6c9-4599-a435-e2cdbe6eee17','One-time Implementation','IMPLEMENTATION','services','one_time','2025-04-29 05:30:00+05:30',NULL,'2025-04-29 08:10:19.573259+05:30',NULL,'{"manager_id": null}','Accounts',NULL,'',31617),
	 ('2025-05-20 14:44:29.914159+05:30',NULL,false,NULL,'a7e96d32-7516-4d4a-8f9f-c626441cd1ef','Salesforce App','SFAPP','add-ons','recurring','2025-05-20 05:30:00+05:30',NULL,'2025-05-20 14:44:29.915959+05:30',NULL,'{"manager_id": null}','Users',NULL,'',31617),
	 ('2025-05-20 14:46:30.41733+05:30',NULL,false,NULL,'7485619a-17de-452c-9b5b-afe5c5ad178f','Premium White Glove Support','PREMIUMSUPPORT','services','recurring','2025-04-29 05:30:00+05:30',NULL,'2025-05-20 14:46:30.419103+05:30',NULL,'{"manager_id": null}','Accounts',NULL,'',31617),
	 ('2025-05-23 16:25:07.405418+05:30',NULL,false,NULL,'ff458e78-2c34-4597-8d1e-0dcc6f783c0c','ASC606 Module','ASC606','software','recurring','2025-05-23 05:30:00+05:30',NULL,'2025-05-23 16:25:07.407355+05:30',NULL,'{"manager_id": null}','Users',NULL,'',31617); 

------------------------------------------------------------------------------------------------------------------------

-- price_point

INSERT INTO price_point (knowledge_begin_date,knowledge_end_date,is_deleted,additional_details,pricepoint_id,product_id,price_book_id,"name",price_model,billing_frequency,currency,custom_factors,list_price,flat_price,effective_start_date,effective_end_date,tier_data,max_discount,modification_treshold,flat_max_discount,flat_modification_treshold,client_id) VALUES
	 ('2025-04-29 08:20:23.493562+05:30',NULL,false,NULL,'fccb499b-7dd5-4f59-81ee-2d2d81952f6c','bfe7dbd3-133a-4c40-8a98-c947e2aa0077','d9b4a062-da25-42ed-afa2-909690b3d570','Flat Fee - USD - Annual','flatfee','annual','USD','{}',5000.00,5000.00,'2025-04-29 05:30:00+05:30',NULL,NULL,0.00,0.00,0.00,0.00,31617),
	 ('2025-05-20 14:34:50.440081+05:30',NULL,false,NULL,'54740934-de1f-4d0c-9105-aadccbc82731','ff458e78-2c34-4597-8d1e-0dcc6f783c0c','d9b4a062-da25-42ed-afa2-909690b3d570','Flat Fee - USD - Annual','flatfee','annual','USD','{}',10000.00,10000.00,'2025-05-20 05:30:00+05:30',NULL,NULL,0.00,0.00,10000.00,10000.00,31617),
	 ('2025-05-20 14:40:53.741684+05:30',NULL,false,NULL,'833f5d33-c6ed-4045-8840-cd5e714b216d','8fc1e173-aee9-4ecb-989f-9537b07a12b1','d9b4a062-da25-42ed-afa2-909690b3d570','Per Unit - USD - Monthly','perunit','monthly','USD','{}',200.00,NULL,'2025-05-20 05:30:00+05:30',NULL,NULL,200.00,200.00,0.00,0.00,31617),
	 ('2025-05-20 14:41:30.23456+05:30',NULL,false,NULL,'79abe7d1-30af-4222-852c-1f3a4dec4b05','8fc1e173-aee9-4ecb-989f-9537b07a12b1','d9b4a062-da25-42ed-afa2-909690b3d570','Per Unit - USD - Annual','perunit','annual','USD','{}',200.00,NULL,'2025-05-20 05:30:00+05:30',NULL,NULL,200.00,200.00,0.00,0.00,31617),
	 ('2025-05-20 14:46:09.218194+05:30',NULL,false,NULL,'08696bc9-d34e-4a06-969c-a76805536967','7485619a-17de-452c-9b5b-afe5c5ad178f','d9b4a062-da25-42ed-afa2-909690b3d570','Flat Fee - USD - Annual','flatfee','annual','USD','{}',10000.00,10000.00,'2025-05-20 05:30:00+05:30',NULL,NULL,0.00,0.00,10000.00,10000.00,31617),
	 ('2025-05-20 14:49:14.668346+05:30',NULL,false,NULL,'1c052871-d377-45d2-b045-7d4f7bb07a33','2f25d64d-c825-4dae-a6e7-479a58d11d53','d9b4a062-da25-42ed-afa2-909690b3d570','Tiered - USD - Monthly','tiered','monthly','USD','{}',38.00,0.00,'2025-05-20 05:30:00+05:30',NULL,'[{"flat_price": 0.0, "list_price": 38.0, "lower_bound": 1, "upper_bound": 2, "tier_discount": 0.0, "flat_fee_discount": 0.0}, {"flat_price": 0.0, "list_price": 36.0012, "lower_bound": 3, "upper_bound": 1000000, "tier_discount": 5.26, "flat_fee_discount": 0.0}]',38.00,38.00,0.00,0.00,31617),
	 ('2025-05-20 14:50:28.900642+05:30',NULL,false,NULL,'08e55e67-d9d8-4e72-ba23-3cc92ffb7ec7','2f25d64d-c825-4dae-a6e7-479a58d11d53','d9b4a062-da25-42ed-afa2-909690b3d570','Per Unit - USD - Monthly','perunit','monthly','USD','{}',38.00,NULL,'2025-05-20 05:30:00+05:30',NULL,NULL,38.00,38.00,0.00,0.00,31617),
	 ('2025-05-23 16:23:59.854098+05:30',NULL,false,NULL,'478d613e-030a-47b0-896c-7b43bbeb8854','2f25d64d-c825-4dae-a6e7-479a58d11d53','d9b4a062-da25-42ed-afa2-909690b3d570','Flat Fee - USD - Monthly','flatfee','annual','USD','{}',15000.00,15000.00,'2025-05-23 05:30:00+05:30',NULL,NULL,0.00,0.00,15000.00,15000.00,31617),
	 ('2025-05-23 16:24:47.79328+05:30',NULL,false,NULL,'43b6b3af-afae-4a90-993f-9ec286a8b1e6','70184a09-c6c9-4599-a435-e2cdbe6eee17','d9b4a062-da25-42ed-afa2-909690b3d570','Flat Fee - USD - One Time','flatfee','one-time','USD','{}',10000.00,10000.00,'2025-05-23 05:30:00+05:30',NULL,NULL,0.00,0.00,10000.00,10000.00,31617);

------------------------------------------------------------------------------------------------------------------------

-- ever_object

INSERT INTO public.ever_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, ever_object_id, "name", data_origin, source_table, primary_key, table_name) VALUES('2025-06-23 07:02:11.517', NULL, false, NULL, 'quote', 'Quote', 'quote_object', NULL, '["quote_id"]'::jsonb, 'report_object_data');
INSERT INTO public.ever_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, ever_object_id, "name", data_origin, source_table, primary_key, table_name) VALUES('2025-06-23 07:02:11.517', NULL, false, NULL, 'quote_line_item', 'Quote Line Item', 'quote_object', NULL, '["quote_id", "line_item_id"]'::jsonb, 'report_object_data');
INSERT INTO public.ever_object (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, ever_object_id, "name", data_origin, source_table, primary_key, table_name) VALUES('2025-06-23 07:02:11.517', NULL, false, NULL, 'quote_line_item_tier', 'Quote Line Item Tier', 'quote_object', NULL, '["quote_id", "line_item_id", "tier_id"]'::jsonb, 'report_object_data');

------------------------------------------------------------------------------------------------------------------------

-- ever_object_variable

INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'quote_id', 'Quote ID', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'display_id', 'Display ID', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'name', 'Name', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'form_id', 'Form ID', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'form_name', 'Form Name', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'currency', 'Currency', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'status', 'Status', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'owner_name', 'Owner Name', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'owner_email', 'Owner Email', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'valid_till', 'Valid Till', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'opportunity_id', 'Opportunity ID', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'account_id', 'Account ID', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'contact_id', 'Contact ID', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'subscription_start_date', 'Subscription Start Date', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'subscription_end_date', 'Subscription End Date', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'duration', 'Duration', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'duration_type', 'Duration Type', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'list_total', 'List Total', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'net_total', 'Net Total', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'discount', 'Discount', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 3, 'primary_quote', 'Primary Quote', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'won_date', 'Won Date', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'created_date', 'Created Date', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'last_updated_date', 'Last Updated Date', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 3, 'deleted', 'Deleted', 'quote');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'quote_id', 'Quote ID', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'line_item_id', 'Line Item ID', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'quote_display_id', 'Quote Display ID', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_id', 'Product ID', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'pricepoint_id', 'Price Point ID', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_code', 'Product Code/SKU', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_name', 'Product Name', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_description', 'Product Description', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'category', 'Category', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'charged_on', 'Charged On', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'currency', 'Currency', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'pricing_model', 'Pricing Model', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'billing_type', 'Billing Type', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'billing_frequency', 'Billing Frequency', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'phase_name', 'Phase Name', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'phase_start_date', 'Phase Start Date', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'phase_end_date', 'Phase End Date', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'quantity', 'Quantity', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'list_price', 'List Price', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'net_price', 'Net Price', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'discount_percentage', 'Discount Percentage', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'total', 'Total', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'last_updated_date', 'Last Updated Date', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 3, 'deleted', 'Deleted', 'quote_line_item');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'quote_id', 'Quote ID', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'line_item_id', 'Line Item ID', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'tier_id', 'Tier ID', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'quote_display_id', 'Quote Display ID', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_id', 'Product ID', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_name', 'Product Name', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 4, 'product_code', 'Product Code/SKU', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_number', 'Tier Number', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_start', 'Tier Start', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_end', 'Tier End', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_quantity', 'Tier Quantity', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_list_unit_price', 'Tier List Unit Price', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_net_unit_price', 'Tier Net Unit Price', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_list_flat_price', 'Tier List Flat Price', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_net_flat_price', 'Tier Net Flat Price', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 1, 'tier_total', 'Tier Total', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 2, 'last_updated_date', 'Last Updated Date', 'quote_line_item_tier');
INSERT INTO public.ever_object_variables (knowledge_begin_date, knowledge_end_date, is_deleted, additional_details, data_type_id, system_name, display_name, ever_object_id) VALUES('2025-06-23 07:02:11.522', NULL, false, NULL, 3, 'deleted', 'Deleted', 'quote_line_item_tier');

------------------------------------------------------------------------------------------------------------------------
