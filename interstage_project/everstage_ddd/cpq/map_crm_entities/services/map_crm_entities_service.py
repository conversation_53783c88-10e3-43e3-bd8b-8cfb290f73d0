from commission_engine.accessors.client_accessor import get_client_settings

from ..accessors import MapCrmEntitiesAccessor


def save_cpq_map_crm_entities(
    client_id, company_map_source, deal_map_source, contact_map_source
):
    """
    Save CPQ map crm entities for the client.
    """
    client_settings = get_client_settings(client_id)
    cpq_settings = client_settings.get("cpq_settings", {})
    if company_map_source is not None:
        cpq_settings["company_databook_id"] = company_map_source.databook_id
        cpq_settings["company_datasheet_id"] = company_map_source.datasheet_id
        cpq_settings["company_system_name_col_name"] = company_map_source.account_name
        cpq_settings["company_id_col_name"] = company_map_source.account_id
    if deal_map_source is not None:
        cpq_settings["deal_room_databook_id"] = deal_map_source.databook_id
        cpq_settings["deal_room_datasheet_id"] = deal_map_source.datasheet_id
        cpq_settings["deal_name_col_system_name"] = deal_map_source.deal_name
        cpq_settings["deal_id_col_system_name"] = deal_map_source.deal_id
        cpq_settings["deal_account_id_col_system_name"] = (
            deal_map_source.deal_account_id
        )
    if contact_map_source is not None:
        cpq_settings["contact_databook_id"] = contact_map_source.databook_id
        cpq_settings["contact_datasheet_id"] = contact_map_source.datasheet_id
        cpq_settings["contact_name_col_system_name"] = contact_map_source.contact_name
        cpq_settings["contact_email_col_system_name"] = contact_map_source.contact_email
        cpq_settings["contact_id_col_system_name"] = contact_map_source.contact_id
        cpq_settings["contact_account_id_col_system_name"] = (
            contact_map_source.contact_account_id
        )
    MapCrmEntitiesAccessor(client_id).update_map_crm_entities_data(
        cpq_settings_data=cpq_settings
    )
    return {"status": "SUCCESS"}


def get_cpq_map_crm_entities(client_id):
    client_settings = get_client_settings(client_id)
    cpq_settings = client_settings.get("cpq_settings", {})
    company_map_source = {
        "databook_id": cpq_settings.get("company_databook_id", ""),
        "datasheet_id": cpq_settings.get("company_datasheet_id", ""),
        "account_name": cpq_settings.get("company_system_name_col_name", ""),
        "account_id": cpq_settings.get("company_id_col_name", ""),
    }
    deal_map_source = {
        "databook_id": cpq_settings.get("deal_room_databook_id", ""),
        "datasheet_id": cpq_settings.get("deal_room_datasheet_id", ""),
        "deal_name": cpq_settings.get("deal_name_col_system_name", ""),
        "deal_id": cpq_settings.get("deal_id_col_system_name", ""),
        "deal_account_id": cpq_settings.get("deal_account_id_col_system_name", ""),
    }
    contact_map_source = {
        "databook_id": cpq_settings.get("contact_databook_id", ""),
        "datasheet_id": cpq_settings.get("contact_datasheet_id", ""),
        "contact_name": cpq_settings.get("contact_name_col_system_name", ""),
        "contact_email": cpq_settings.get("contact_email_col_system_name", ""),
        "contact_id": cpq_settings.get("contact_id_col_system_name", ""),
        "contact_account_id": cpq_settings.get(
            "contact_account_id_col_system_name", ""
        ),
    }
    return {
        "status": "SUCCESS",
        "company_map_source": company_map_source,
        "deal_map_source": deal_map_source,
        "contact_map_source": contact_map_source,
    }
