from typing import Optional

from ninja import Schema


class CompanyMapSourceSchema(Schema):
    databook_id: str
    datasheet_id: str
    account_name: str
    account_id: str


class DealMapSourceSchema(Schema):
    databook_id: str
    datasheet_id: str
    deal_name: str
    deal_id: str
    deal_account_id: str


class ContactMapSourceSchema(Schema):
    databook_id: str
    datasheet_id: str
    contact_name: str
    contact_email: str
    contact_id: str
    contact_account_id: str


class SaveMapCrmEntitiesPayload(Schema):
    company_map_source: Optional[CompanyMapSourceSchema] = None
    deal_map_source: Optional[DealMapSourceSchema] = None
    contact_map_source: Optional[ContactMapSourceSchema] = None


class SaveMapCrmEntitiesResponse(Schema):
    status: str


class GetMapCrmEntitiesResponse(Schema):
    status: str
    company_map_source: CompanyMapSourceSchema
    deal_map_source: DealMapSourceSchema
    contact_map_source: ContactMapSourceSchema
