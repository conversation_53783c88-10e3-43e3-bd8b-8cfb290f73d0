from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, computed_field, root_validator

from .exceptions import QuoteFormMappingError


class QuoteLineItemModel(BaseModel):
    quote_id: str
    quote_line_item_id: str
    start_date: Optional[datetime | str] = None
    end_date: Optional[datetime | str] = None
    phase_id: Optional[str] = None
    phase_name: Optional[str] = None
    product_name: str
    sku: str
    billing_frequency: str
    billing_type: str
    quantity: int
    list_unit_price: float
    net_unit_price: float
    list_total: float
    net_total: float
    prorated_list_total: float
    prorated_net_total: float
    discount_percent: Optional[float] = None
    custom_data: Optional[dict] = None
    pricepoint_data: Optional[dict] = None
    order: Optional[int] = None
    unit_of_measure: Optional[str] = None

    @computed_field
    def line_item_errors(self) -> list:
        pricepoint = self.pricepoint_data
        # TODO: backend validations will be added here
        # errors = []
        if pricepoint:
            # discount_allowed = pricepoint.get("discount_allowed", False)
            # # max_discount = pricepoint.get("max_discount", 0)
            # if not discount_allowed and self.discount_percent:
            #     errors.append("Discount not allowed for this product")
            # # if (
            # #     self.discount_percent
            # #     and max_discount
            # #     and self.discount_percent > max_discount
            # # ):
            # #     errors.append("Unit discount is greater than max discount")
            # if errors:
            #     return errors
            return []
        else:
            return ["Price point data not found"]

    @computed_field
    def is_valid(self) -> bool:
        return not bool(self.line_item_errors)


class QuoteCreateRequest(BaseModel):
    client_id: int
    form_builder_id: UUID | None = None
    created_by: str | None = None
    additional_details: dict | None = None


class QuoteListItem(BaseModel):
    quote_id: UUID
    quote_display_id: str
    quote_name: str
    quote_currency: str | None = None
    form_id: UUID
    form_builder_id: UUID | None = None
    status: str
    opportunity_id: str | None = None
    is_primary: bool | None = None
    opportunity_name: str | None = None
    opportunity_type: str | None = None
    opportunity_link: str | None = None
    account_id: str | None = None
    account_name: str | None = None
    valid_till: datetime | None = None
    start_date: datetime | None = None
    end_date: datetime | None = None
    duration_value: int | None = None
    duration_type: str | None = None
    list_quote_total: float | None = None
    net_quote_total: float | None = None
    discount_amount: float | None = None
    closed_date: datetime | None = None
    owner_id: str | None = None
    created_by: str
    created_at: datetime
    updated_at: str | datetime | None = None
    updated_by: str | None = None
    product_count: int = 0
    status_instance_data: dict | None = None


class QuoteClonePayload(BaseModel):
    quote_id: UUID


class QuoteCloneRequest(BaseModel):
    client_id: int
    quote_id: UUID
    created_by: str | None = None
    additional_details: dict | None = None


class QuoteListRequest(BaseModel):
    client_id: int
    limit_value: int
    offset_value: int
    search_term: Optional[str] = None
    status: Optional[list[str]] = None
    account_id: Optional[list[str]] = None
    owner_id: Optional[list[str]] = None
    approvals_waiting_on_you: bool = False


class QuoteFilterRequest(BaseModel):
    client_id: int
    status: Optional[str] = None
    created_by: Optional[str] = None
    opportunity_id: Optional[str] = None


class QuoteSearchRequest(BaseModel):
    client_id: int
    search_term: str


class QuoteDeleteRequest(BaseModel):
    client_id: int
    quote_id: UUID


class QuoteMarkPrimaryRequest(BaseModel):
    client_id: int
    quote_id: UUID


class QuoteUpdateRequest(BaseModel):
    client_id: int
    quote_id: UUID
    data: dict


class QuoteFormMappingData(BaseModel):
    quote_name: str
    quote_currency: str
    opportunity_id: str
    form_id: UUID
    valid_till: datetime
    account_id: str
    contact_id: str
    list_quote_total: float
    net_quote_total: float
    discount_amount: float
    custom_data: Optional[dict] = None

    @root_validator(pre=True)
    def check_all_fields_present(cls, values):  # noqa: N805
        missing_fields = [
            field
            for field in cls.__annotations__
            if field not in values
            or (isinstance(values[field], str) and not values[field])
            or values[field] is None
        ]
        if missing_fields:
            raise QuoteFormMappingError(missing_fields)
        return values


class QuoteDetailsItem(BaseModel):
    quote_id: UUID
    quote_display_id: str
    quote_name: str
    status: str
