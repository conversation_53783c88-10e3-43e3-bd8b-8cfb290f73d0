from copy import deepcopy
from decimal import Decimal
from logging import getLogger

from django.utils import timezone

from commission_engine.utils.date_utils import (
    convert_str_to_date,
    end_of_day,
    make_aware_wrapper,
    start_of_day,
)
from everstage_ddd.cpq.quote.quote_status.enums import QuoteStatusEnum
from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
    QuoteStatusSelector,
)

from ...price_book.service.proration_service import get_prorate_multiplier
from ...price_book.service.quote_price_service import get_price_model
from ...product_catalog.utils import sanitize_for_json
from ..accessors.quote_line_item_accessor import QuoteLineItemAccessor
from ..models import QuoteLineItem
from ..selectors.quote_selector import QuoteSelector
from ..types import QuoteLineItemModel
from .qli_phase_service import _create_yearly_phases
from .quote_service import get_catalog_records

logger = getLogger(__name__)


def _add_phase_details_to_qli(line_item_detail, phase_details):
    start_date = phase_details.get("start_date")
    end_date = phase_details.get("end_date")
    if start_date:
        start_date = convert_str_to_date(start_date)
        start_date = make_aware_wrapper(start_of_day(start_date))
    if end_date:
        end_date = convert_str_to_date(end_date)
        end_date = make_aware_wrapper(end_of_day(end_date))
    line_item_detail["start_date"] = start_date
    line_item_detail["end_date"] = end_date
    line_item_detail["phase_id"] = phase_details.get("phase_id")
    line_item_detail["phase_name"] = phase_details.get("phase_name")
    return line_item_detail


def _add_pricepoint_data(client_id, line_item_detail, pricepoint_data, is_bulk):
    start_date, end_date = line_item_detail["start_date"], line_item_detail["end_date"]
    billing_frequency = pricepoint_data.get("billing_frequency", "").lower()
    billing_type = line_item_detail.get("billing_type").lower()
    prorate_multiplier = get_prorate_multiplier(
        client_id, start_date, end_date, billing_frequency
    )
    quantity = Decimal(line_item_detail.get("quantity", 1) or 1)
    discount_percent = Decimal(line_item_detail.get("discount_percent", 0) or 0)
    pricepoint = {
        **pricepoint_data,
        "list_unit_price": Decimal(line_item_detail.get("list_unit_price", 0) or 0),
        "net_unit_price": Decimal(line_item_detail.get("net_unit_price", 0) or 0),
        "is_bulk": is_bulk,
    }
    price_model = get_price_model(
        pricepoint, quantity, discount_percent, prorate_multiplier
    )
    line_item_detail = {
        **line_item_detail,
        "billing_frequency": billing_frequency,
        "billing_type": billing_type,
        "quantity": quantity,
        "prorate_multiplier": prorate_multiplier,
        "discount_percent": price_model.discount_percent,
        "list_unit_price": price_model.list_unit_price,
        "net_unit_price": price_model.net_unit_price,
        "list_total": price_model.list_total,
        "net_total": price_model.net_total,
        "prorated_list_total": price_model.prorated_list_total,
        "prorated_net_total": price_model.prorated_net_total,
    }
    if hasattr(price_model, "tier_allocations"):
        tier_allocations = price_model.tier_allocations
        line_item_detail["custom_data"] = {"tier_allocations": tier_allocations}
        if is_bulk:
            pp_tier_data = pricepoint_data.get("tier_data", [])
            new_tier_info = []
            for index, pp_tier in enumerate(pp_tier_data):
                allocated_data = tier_allocations.get(index)
                if allocated_data:
                    pp_tier.update(allocated_data)
                    new_tier_info.append(pp_tier)
                else:
                    pp_tier["unit_price_discount"] = float(discount_percent)
                    pp_tier["net_unit_price"] = float(
                        pp_tier["list_unit_price"] * (1 - discount_percent / 100)
                    )
                    new_tier_info.append(pp_tier)
            pricepoint_data["tier_data"] = new_tier_info
            line_item_detail["pricepoint_data"] = pricepoint_data
    return line_item_detail


def build_basic_quote_line_item(client_id, line_item_detail):
    is_bulk = line_item_detail.get("is_bulk", False)
    line_item_detail_rec = {}
    line_item_detail_rec = _add_phase_details_to_qli(
        line_item_detail_rec, line_item_detail
    )
    line_item_detail_rec["quote_id"] = line_item_detail.get("quote_id")
    line_item_detail_rec["quote_line_item_id"] = (
        line_item_detail.get("sku", "none") + "#_#" + line_item_detail["phase_id"]
    )
    line_item_detail_rec["product_name"] = line_item_detail.get("product_name")
    line_item_detail_rec["sku"] = line_item_detail.get("sku")
    line_item_detail_rec["order"] = line_item_detail.get("order", 0)

    quantity = line_item_detail.get("quantity", 1)
    pricepoint_data = line_item_detail.get("pricepoint_data", {})
    line_item_detail_rec["pricepoint_data"] = pricepoint_data
    line_item_detail_rec["quantity"] = quantity
    line_item_detail_rec["prorate_multiplier"] = line_item_detail.get(
        "prorate_multiplier", 1
    )
    line_item_detail_rec["discount_percent"] = line_item_detail.get(
        "discount_percent", 0
    )
    line_item_detail_rec["list_unit_price"] = line_item_detail.get("list_unit_price", 0)
    line_item_detail_rec["net_unit_price"] = line_item_detail.get("net_unit_price", 0)
    line_item_detail_rec["list_total"] = line_item_detail.get("list_total", 0)
    line_item_detail_rec["net_total"] = line_item_detail.get("net_total", 0)
    line_item_detail_rec["prorated_list_total"] = line_item_detail.get(
        "prorated_list_total", 0
    )
    line_item_detail_rec["prorated_net_total"] = line_item_detail.get(
        "prorated_net_total", 0
    )
    line_item_detail_rec["custom_data"] = line_item_detail.get("custom_data", {})
    line_item_detail_rec["billing_frequency"] = ""
    line_item_detail_rec["billing_type"] = line_item_detail.get("billing_type")
    line_item_detail_rec["unit_of_measure"] = line_item_detail.get("unit_of_measure")
    if pricepoint_data:
        line_item_detail_rec = _add_pricepoint_data(
            client_id, line_item_detail_rec, pricepoint_data, is_bulk
        )
    return line_item_detail_rec


def build_quote_line_item_rec(client_id, sku, pb_rec, cat_rec, line_item_detail):
    quantity = Decimal(line_item_detail.get("quantity", 1) or 1)
    is_bulk = line_item_detail.get("is_bulk")
    discount_percent = Decimal(line_item_detail.get("discount_percent", 0) or 0)
    quote_line_item_rec = {}
    quote_line_item_rec = _add_phase_details_to_qli(
        quote_line_item_rec, line_item_detail
    )
    quote_line_item_rec["quote_id"] = line_item_detail.get("quote_id")
    quote_line_item_rec["quote_line_item_id"] = (
        sku + "#_#" + str(line_item_detail.get("phase_id"))
    )
    quote_line_item_rec["billing_type"] = cat_rec.get("billing_type")
    quote_line_item_rec["unit_of_measure"] = cat_rec.get("uom")
    quote_line_item_rec["product_name"] = cat_rec.get("name")
    quote_line_item_rec["sku"] = cat_rec.get("sku")
    quote_line_item_rec["quantity"] = quantity
    quote_line_item_rec["discount_percent"] = discount_percent
    quote_line_item_rec["pricepoint_data"] = pb_rec
    quote_line_item_rec["order"] = line_item_detail.get("order", 0)
    quote_line_item_rec["list_unit_price"] = line_item_detail.get("list_unit_price")
    quote_line_item_rec["net_unit_price"] = line_item_detail.get("net_unit_price")
    quote_line_item_rec = _add_pricepoint_data(
        client_id, quote_line_item_rec, pb_rec, is_bulk
    )
    return quote_line_item_rec


def get_quote_line_item(client_id, line_item, catalog_rec, use_basic=False):
    pricepoint_data = line_item.get("pricepoint_data")
    sku = line_item.get("sku")
    error = None
    if catalog_rec:
        line_item["billing_type"] = catalog_rec.get("billing_type")
        line_item["unit_of_measure"] = catalog_rec.get("uom")
    if pricepoint_data and (not use_basic):
        quote_line_item_rec = build_quote_line_item_rec(
            client_id, sku, pricepoint_data, catalog_rec, line_item
        )
    else:
        quote_line_item_rec = build_basic_quote_line_item(client_id, line_item)
    quote_line_item = QuoteLineItemModel(**quote_line_item_rec)
    if not quote_line_item.is_valid:
        error = quote_line_item.line_item_errors
    return (quote_line_item_rec, error)


def _handle_line_item_removal(
    changed_skus, sku_qli_dict, phase_id_list, first_index, copy_flag
):
    last_index = len(phase_id_list)
    line_item_ids = []
    result = {}
    changed_phase_ids = set()
    if not copy_flag:
        last_index = first_index + 1
    for sku in changed_skus:
        sku_qlis = sku_qli_dict[sku]
        for i in range(first_index, last_index):
            current_phase_id = phase_id_list[i]
            if current_phase_id in sku_qlis:
                lineitem_id = sku_qlis[current_phase_id].get("quote_line_item_id")
                line_item_ids.append(lineitem_id)
                changed_phase_ids.add(current_phase_id)
    result["changed_phase_ids"] = list(changed_phase_ids)
    result["line_item_ids"] = line_item_ids
    return result


def _handle_line_item_update(
    client_id, line_item_data, sku_qli_dict, phase_id_list, **kwargs
):
    copy_flag = kwargs["copy_flag"]
    sku_catalog_dict = kwargs["sku_catalog_dict"]
    is_bulk = kwargs["is_bulk"]
    first_index = kwargs.get("current_index", 0)
    changed_skus = []
    line_item_data_dict = {}
    changed_sku_pricept_dict = {}
    last_index = len(phase_id_list)
    changed_quote_line_items = []
    result = {}
    errors = {}
    if not copy_flag:
        last_index = first_index + 1

    for li in line_item_data:
        changed_sku_pricept_dict[li.get("sku")] = li.get("pricepoint_data", {}).get(
            "label", ""
        )
        line_item_data_dict[li.get("sku")] = li
        changed_skus.append(li.get("sku"))

    for sku in changed_skus:
        sku_qlis = sku_qli_dict[sku]
        is_price_point_changed = False
        changed_item = line_item_data_dict[sku]
        changed_phase_id = changed_item.get("phase_id")
        changed_pricept_label = changed_sku_pricept_dict[sku]
        catalog_rec = sku_catalog_dict.get(sku)
        for i in range(first_index, last_index):
            changed_line_item = deepcopy(changed_item)
            current_phase_id = phase_id_list[i]
            if current_phase_id in sku_qlis:
                current_phase_details = {}
                li = sku_qlis[current_phase_id]
                current_phase_details["start_date"] = li["start_date"]
                current_phase_details["end_date"] = li["end_date"]
                current_phase_details["phase_id"] = li["phase_id"]
                current_phase_details["phase_name"] = li["phase_name"]
                current_pricept_label = li.get("pricepoint_data", {}).get("label", "")
                if (changed_phase_id == current_phase_id) and (
                    current_pricept_label != changed_pricept_label
                ):
                    is_price_point_changed = True
                is_curr_price_point_match = (
                    current_pricept_label == changed_pricept_label
                )
                # if `is_curr_price_point_match` -  current pricepoint label matches with changed label. make changes in that
                # if `is_price_point_changed` - pricepoint changed in edit. make changes in subsequent phases
                if is_curr_price_point_match or is_price_point_changed:
                    # Get changed line item and replace phase details
                    changed_line_item = _add_phase_details_to_qli(
                        changed_line_item, current_phase_details
                    )
                    changed_line_item["is_bulk"] = is_bulk
                    (quote_line_item_rec, line_item_error) = get_quote_line_item(
                        client_id, changed_line_item, catalog_rec
                    )
                    changed_quote_line_items.append(quote_line_item_rec)
                    if current_phase_id not in result:
                        result[current_phase_id] = {}
                    result[current_phase_id][sku] = quote_line_item_rec
                    if line_item_error:
                        if current_phase_id not in errors:
                            errors[current_phase_id] = {}
                        errors[current_phase_id][sku] = line_item_error

    return (changed_quote_line_items, result, errors)


def _get_phase_details(current_line_items, new_phase_details=None):
    phase_qli_dict = {}
    phase_id_list = []
    phase_order_dict = {}
    new_phase_details_dict = {}
    result = {}
    if current_line_items:
        current_line_items = sorted(current_line_items, key=lambda d: d["start_date"])
        for qli in current_line_items:
            if qli["phase_id"] not in phase_qli_dict:
                phase_qli_dict[qli["phase_id"]] = []
                phase_id_list.append(qli["phase_id"])
                phase_order_dict[qli["phase_id"]] = 0
            phase_qli_dict[qli["phase_id"]].append(qli)
            curr_max = phase_order_dict[qli["phase_id"]]
            phase_order_dict[qli["phase_id"]] = max(curr_max, qli["order"])
    elif new_phase_details:
        # products are added for the 1st time. Get phase details from new_phase_details.
        new_phase_details = sorted(new_phase_details, key=lambda d: d["start_date"])
        for new_phase in new_phase_details:
            phase_order_dict[new_phase["phase_id"]] = 0
            phase_id_list.append(new_phase["phase_id"])
            phase_qli_dict[new_phase["phase_id"]] = []
            new_phase_details_dict[new_phase["phase_id"]] = new_phase

    result["phase_qli_dict"] = phase_qli_dict
    result["phase_id_list"] = phase_id_list
    result["phase_order_dict"] = phase_order_dict
    result["new_phase_details_dict"] = new_phase_details_dict
    return result


def _handle_line_item_create(  # noqa: PLR0915
    client_id,
    quote_id,
    new_skus,
    phase_details,
    **kwargs,
):
    current_line_items = kwargs["current_line_items"]
    sku_pricepoint_dict = kwargs["sku_pricepoint_dict"]
    sku_catalog_dict = kwargs["sku_catalog_dict"]
    copy_flag = kwargs["copy_flag"]
    new_phase_details = kwargs.get("new_phase_details", [])
    line_item_data = []
    errors = {}
    phase_id = phase_details.get("phase_id")
    phase_info = _get_phase_details(current_line_items, new_phase_details)
    phase_id_list = phase_info["phase_id_list"]
    phase_qli_dict = phase_info["phase_qli_dict"]
    phase_order_dict = phase_info["phase_order_dict"]
    new_phase_details_dict = phase_info["new_phase_details_dict"]
    total_phases = len(phase_id_list)
    current_index = 0
    if phase_id in phase_id_list:
        current_index = phase_id_list.index(phase_id)

    if not copy_flag:
        total_phases = current_index + 1

    for i in range(current_index, total_phases):
        current_phase_id = phase_id_list[i]
        if current_line_items:
            pd = phase_qli_dict[current_phase_id][0]
        else:
            pd = new_phase_details_dict[current_phase_id]
        current_phase_details = _add_phase_details_to_qli({}, pd)
        existing_order = (
            phase_order_dict[current_phase_id] + 1
        )  # index starts from 0. Add 1 here
        current_phase_skus = []
        if current_phase_id in phase_qli_dict:
            current_qlis = phase_qli_dict.get(current_phase_id, [])
            current_phase_skus = [li["sku"] for li in current_qlis]
        for index, sku in enumerate(new_skus):
            pricepoints = sku_pricepoint_dict.get(sku)
            catalog_rec = sku_catalog_dict.get(sku)
            billing_type = catalog_rec.get("billing_type", "").lower()
            if i != current_index and billing_type != "recurring":
                # do not add one time product to other phases.
                continue
            if sku in current_phase_skus:
                # sku already present in this phase.
                continue
            order = existing_order + index
            line_item_dict = {}
            line_item_dict["quote_id"] = quote_id
            line_item_dict = _add_phase_details_to_qli(
                line_item_dict, current_phase_details
            )
            line_item_dict["order"] = order
            line_item_dict["product_name"] = catalog_rec.get("name")
            line_item_dict["sku"] = sku
            if pricepoints and len(pricepoints) == 1:
                pricepoint = pricepoints[0]
                line_item_dict["pricepoint_data"] = pricepoint
                line_item_dict["list_unit_price"] = pricepoint.get("list_price", 0)
                line_item_dict["net_unit_price"] = pricepoint.get("list_price", 0)
            line_item_rec, _ = get_quote_line_item(
                client_id, line_item_dict, catalog_rec
            )
            line_item_model = QuoteLineItemModel(**line_item_rec)
            if not line_item_model.is_valid:
                if phase_details.get("phase_id") not in errors:
                    errors[phase_details.get("phase_id")] = {}
                errors[phase_details.get("phase_id")][
                    line_item_model.quote_line_item_id
                ] = line_item_model.line_item_errors
            line_item_data.append(line_item_rec)
    return (line_item_data, errors)


def edit_quote_line_items(  # noqa: PLR0912 PLR0915
    client_id, quote_id, changed_attribute, **kwargs
):
    skus_set = set()
    errors = {}
    changed_quote_line_items = []
    result = {}
    change_type = kwargs.get("change_type")
    phase_detail = kwargs.get("phase_detail")
    copy_flag = kwargs.get("copy_flag", True)
    is_bulk = kwargs.get("is_bulk", False)
    # ////////////////////////////////////////////////////?
    # get all quote line items for quote
    quote_line_item_acc = QuoteLineItemAccessor(client_id)
    quote_line_items = quote_line_item_acc.get_line_items_for_quote(
        quote_id, as_dicts=True
    )
    quote_line_items = sorted(quote_line_items, key=lambda d: d["start_date"])
    phase_id_list = []
    sku_qli_dict = {}
    for line_item in quote_line_items:
        phase_id = line_item["phase_id"]
        qli_sku = line_item["sku"]
        if phase_id not in phase_id_list:
            phase_id_list.append(phase_id)
        if qli_sku not in sku_qli_dict:
            sku_qli_dict[qli_sku] = {}
        sku_qli_dict[qli_sku][phase_id] = line_item
        skus_set.add(qli_sku)
    skus = list(skus_set)
    # ////////////////////////////////////////////////////?
    # get catalog sku dict
    catalog_records = get_catalog_records(client_id, skus)
    sku_catalog_dict = {}
    changed_phase_ids = []
    for rec in catalog_records:
        sku_catalog_dict[rec["sku"]] = rec
        skus_set.remove(rec["sku"])
    # ////////////////////////////////////////////////////?
    # if changed_atttribute is phase_name, no need to calculate anything. Just update the phase_name in all line items
    if changed_attribute == "phase_name":
        new_phase_name = phase_detail.get("phase_name")
        phase_id = phase_detail.get("phase_id")
        changed_phase_ids.append(phase_id)
        quote_line_item_acc.change_phase_name(quote_id, phase_id, new_phase_name)
    else:
        changed_phase_id = phase_detail["phase_id"]
        quote_line_items = []
        line_item_data = phase_detail["row_data"]
        changed_skus = [li.get("sku") for li in line_item_data]
        current_index = phase_id_list.index(changed_phase_id)
        if change_type == "remove":
            delete_result = _handle_line_item_removal(
                changed_skus, sku_qli_dict, phase_id_list, current_index, copy_flag
            )
            line_item_ids = delete_result.get("line_item_ids")
            changed_phase_ids = delete_result.get("changed_phase_ids")
            quote_line_item_acc.invalidate_by_quote_line_item_id(
                quote_id, line_item_ids
            )
        elif change_type == "update":
            if skus_set and [sku for sku in changed_skus if sku in skus_set]:
                return {}, {
                    "status": "error",
                    "message": "This product can't be updated as it’s no longer active in the catalog.",
                }
            (changed_quote_line_items, result, errors) = _handle_line_item_update(
                client_id,
                line_item_data,
                sku_qli_dict,
                phase_id_list,
                current_index=current_index,
                copy_flag=copy_flag,
                sku_catalog_dict=sku_catalog_dict,
                is_bulk=is_bulk,
            )
            changed_phase_ids = list(result.keys())
    result["changed_phase_ids"] = changed_phase_ids
    # update quote line items in db
    if changed_quote_line_items:
        quote_line_item_acc.update_quote_line_items(quote_id, changed_quote_line_items)

    return result, errors


def create_quote_line_items(client_id, quote_id, new_skus, phase_details, **kwargs):
    sku_pricepoint_dict = kwargs["sku_pricepoint_dict"]
    sku_catalog_dict = kwargs["sku_catalog_dict"]
    copy_flag = kwargs["copy_flag"]
    quote_line_items_dict = {}
    quote_line_item_acc = QuoteLineItemAccessor(client_id)
    line_item_data = []
    errors = {}
    # phase_id = phase_details.get("phase_id")
    current_line_items = quote_line_item_acc.get_line_items_for_quote(
        quote_id, as_dicts=True
    )
    current_line_items = sorted(current_line_items, key=lambda d: d["start_date"])
    new_phases = []
    if not current_line_items:  # inserting for the first time
        new_phases = _create_yearly_phases(
            phase_info=phase_details, current_line_items=current_line_items
        )
        new_phases = sorted(new_phases, key=lambda d: d["start_date"])
        phase_details = new_phases[0]

    (line_item_data, errors) = _handle_line_item_create(
        client_id,
        quote_id,
        new_skus,
        phase_details,
        sku_pricepoint_dict=sku_pricepoint_dict,
        sku_catalog_dict=sku_catalog_dict,
        current_line_items=current_line_items,
        new_phase_details=new_phases,
        copy_flag=copy_flag,
    )

    for li in line_item_data:
        if li["phase_id"] not in quote_line_items_dict:
            quote_line_items_dict[li["phase_id"]] = {}
        quote_line_items_dict[li["phase_id"]][li["sku"]] = li

    if line_item_data:
        quote_line_item_acc.insert_object_from_rule(sanitize_for_json(line_item_data))
    return quote_line_items_dict, errors


def show_quote_line_items(client_id, quote_id, use_kd=True):
    quote_line_item_acc = QuoteLineItemAccessor(client_id)
    kd = None
    if use_kd:
        quote_status = QuoteStatusSelector(client_id).get_quote_status(quote_id)
        if quote_status and quote_status.status != QuoteStatusEnum.DRAFT.value:
            kd = quote_status.knowledge_begin_date
    quote = QuoteSelector(client_id).get_quote_by_id(
        quote_id, projection=None, point_in_time=kd
    )
    quote_line_items = quote_line_item_acc.get_line_items_for_quote(
        quote_id, kd, as_dicts=True
    )
    result = {
        "start_date": quote.start_date,
        "end_date": quote.end_date,
        "duration_value": quote.duration_value,
        "duration_type": quote.duration_type,
    }
    phase_line_item_dict = {}
    should_populate_start_date = False
    curr_date = timezone.now()
    new_line_items = []
    phase_details = {}
    for line_item in quote_line_items:
        phase_id = line_item["phase_id"]
        start_date = line_item["start_date"]
        prorated_net_total = line_item.get("prorated_net_total", 0)
        if not start_date:
            should_populate_start_date = True
            line_item["start_date"] = curr_date
            line_item["end_date"] = None
            new_line_items.append(line_item)
        if phase_id not in phase_details:
            phase_line_item_dict[phase_id] = []
            phase_details[phase_id] = {
                "start_date": line_item["start_date"],
                "end_date": line_item["end_date"],
                "phase_id": phase_id,
                "phase_name": line_item["phase_name"],
                "sub_total": 0,
            }
        phase_line_item_dict[phase_id].append(line_item)
        phase_details[phase_id]["sub_total"] += prorated_net_total

    if should_populate_start_date and new_line_items:
        quote_line_item_acc.insert_object_from_rule(new_line_items)

    for phase_id, line_items in phase_line_item_dict.items():
        sorted_items = sorted(line_items, key=lambda d: d["order"])
        phase_details[phase_id]["row_data"] = sorted_items
    phases = list(phase_details.values())
    phases = sorted(phases, key=lambda d: d["start_date"])
    result["phases"] = phases

    return result


def get_data_for_rules(client_id, quote_id):
    quote_line_item_acc = QuoteLineItemAccessor(client_id)
    quote_line_items = quote_line_item_acc.get_line_items_for_quote(
        quote_id, as_dicts=True
    )
    return quote_line_items


def _check_duplicates(client_id, quote_id, quote_line_items):
    line_items = []
    qli_item_dict = {}
    duplicate_temporal_ids = []
    for qli in quote_line_items:
        qli_id = qli.quote_line_item_id
        if qli_id not in qli_item_dict:
            qli_item_dict[qli_id] = qli
            line_items.append(qli)
        else:
            duplicate_temporal_ids.append(qli.temporal_id)
    # duplicate line items present. remove them
    if duplicate_temporal_ids:
        logger.info(
            "Removing Duplicate for Quote {} - {}".format(
                quote_id, duplicate_temporal_ids
            )
        )
        QuoteLineItemAccessor(client_id).invalidate_by_temporal_ids(
            quote_id, duplicate_temporal_ids
        )
    return line_items


def get_quote_summary(
    client_id: int, quote_id: str, use_kd=True, phase_ids: list[str] | None = None
):
    qli_accessor = QuoteLineItemAccessor(str(client_id))
    kd = None
    if use_kd:
        quote_status = QuoteStatusSelector(client_id).get_quote_status(quote_id)
        if quote_status and quote_status.status != QuoteStatusEnum.DRAFT.value:
            kd = quote_status.knowledge_begin_date
    quote_line_items = qli_accessor.get_line_items_for_quote(
        quote_id, kd, as_dicts=False
    )
    quote_line_items = _check_duplicates(client_id, quote_id, quote_line_items)
    skus = [item.sku for item in quote_line_items if isinstance(item, QuoteLineItem)]

    catalog_records = get_catalog_records(client_id, skus)
    catalog_dict = {rec["sku"]: rec for rec in catalog_records}
    summary, phase_totals = (
        {
            "list_quote_total": Decimal(0),
            "net_quote_total": Decimal(0),
            "discount_amount": Decimal(0),
            "recurring_total": Decimal(0),
        },
        {},
    )
    phase_detail_dict = {}
    if phase_ids is not None:
        for phase_id in phase_ids:
            phase_detail = {
                "phase_id": phase_id,
                "phase_name": "",
                "start_date": None,
                "end_date": None,
                "row_data": [],
                "sub_total": Decimal(0),
            }
            phase_detail_dict[phase_id] = phase_detail

    for item in quote_line_items:
        if (
            not isinstance(item, QuoteLineItem)
            or not isinstance(item.prorated_net_total, Decimal)
            or not isinstance(item.prorated_list_total, Decimal)
        ):
            continue
        if (phase_ids) and (item.phase_id in phase_ids):
            phase_detail = phase_detail_dict[item.phase_id]
            if not phase_detail["phase_name"]:
                phase_detail.update(
                    {
                        "phase_name": item.phase_name,
                        "start_date": item.start_date,
                        "end_date": item.end_date,
                    }
                )
            qli_item = item.__dict__
            qli_item.pop("_state")
            phase_detail["row_data"].append(qli_item)

        summary["list_quote_total"] += item.prorated_list_total
        summary["net_quote_total"] += item.prorated_net_total

        catalog_record = catalog_dict.get(item.sku)
        if (
            isinstance(catalog_record, dict)
            and isinstance(item.prorated_net_total, Decimal)
            and catalog_record.get("billing_type") == "Recurring"
        ):
            summary["recurring_total"] += item.prorated_net_total

        if item.phase_id not in phase_totals:
            phase_totals[item.phase_id] = 0
        phase_totals[item.phase_id] += item.prorated_net_total

    summary["discount_amount"] = (
        summary["list_quote_total"] - summary["net_quote_total"]
    )

    if phase_ids is not None:
        for phase_id, phase_detail in phase_detail_dict.items():
            phase_detail["row_data"].sort(key=lambda x: x.get("order", 0))
            phase_detail["sub_total"] = Decimal(phase_totals.get(phase_id, 0) or 0)
        return list(phase_detail_dict.values()), summary, phase_totals
    return summary, phase_totals


def delete_quote_line_items(client_id, quote_id):
    quote_line_item_acc = QuoteLineItemAccessor(client_id)
    quote_line_item_acc.delete_quote_line_items(quote_id)
    return {"status": "success"}


def clone_quote_line_items_to_new_quote(
    client_id, kd, quote_kd, old_quote_id, new_quote_id
):
    qli_accessor = QuoteLineItemAccessor(client_id)
    quote_line_items = qli_accessor.get_line_items_for_quote(old_quote_id, quote_kd)

    qlis = []
    for line_item in quote_line_items:
        line_item.pk = None
        line_item.quote_id = new_quote_id
        line_item.knowledge_begin_date = kd
        qlis.append(line_item)
    qli_accessor.insert_object(qlis)


def get_product_count_map(client_id: int) -> dict[str, int]:
    sku_counts_list = QuoteLineItemAccessor(str(client_id)).get_distinct_skus_count()
    return {quote["quote_id"]: quote["count"] for quote in sku_counts_list}


def get_quote_properties(client_id: int, quote_id: str) -> dict:
    quote_accessor = QuoteSelector(client_id)
    quote = quote_accessor.get_quote_by_id(quote_id)
    return {
        "qp_quote_name": quote.quote_name,
        "qp_quote_display_id": quote.quote_display_id,
        "qp_quote_owner_email": quote.owner_id,
        "qp_subscription_start_date": quote.start_date,
        "qp_subscription_end_date": quote.end_date,
        "qp_duration": quote.duration_value,
        "qp_duration_unit": quote.duration_type,
        # "qp_quote_total": quote.net_quote_total,
        "qp_quote_net_total": quote.net_quote_total,
        "qp_quote_list_total": quote.list_quote_total,
    }
