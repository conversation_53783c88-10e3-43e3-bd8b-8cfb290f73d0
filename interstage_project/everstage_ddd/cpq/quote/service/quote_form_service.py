"""Quote form service."""

import logging
import re
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict

import pydash
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_settings
from commission_engine.utils.date_utils import convert_str_to_date, end_of_day
from commission_engine.utils.general_data import RBAC, RBACComponent
from everstage_ddd.cpq.approvals.service.approvals_service import (
    delete_quote_approval_requests,
    get_all_quotes_waiting_on_you,
    get_quote_status_map,
    get_quotes_with_needs_attention_status,
)
from everstage_ddd.cpq.basic_settings.services.basic_settings_service import (
    get_quote_display_id_prefix_from_db,
)
from everstage_ddd.cpq.forms.accessors.form_accessor import (
    AutoSaveFormAccessor,
    EverstageFormAccessor,
)
from everstage_ddd.cpq.forms.accessors.form_spec_selector import FormSpecSelector
from everstage_ddd.cpq.forms.models.form_models import EverstageForm
from everstage_ddd.cpq.forms.service.form_builder_service import get_form_builder_terms
from everstage_ddd.cpq.forms.service.form_spec_navigator import FormSpecNavigator
from everstage_ddd.cpq.price_book import (
    PriceBookDatasheetTable,
    PriceBookDatasheetTableParams,
)
from everstage_ddd.cpq.quote.exceptions import QuoteError
from everstage_ddd.cpq.quote.quote_status.data_models import QuoteStatusDataModel
from everstage_ddd.cpq.quote.quote_status.enums import QuoteStatusEnum
from everstage_ddd.cpq.quote.quote_status.models import QuoteStatus
from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
    QuoteStatusSelector,
)
from everstage_ddd.cpq.quote.schema import (
    AccountDetailsResponse,
    QuoteDeleteResponse,
    QuoteDetailsResponse,
    QuoteFilterResponse,
    QuoteListResponse,
    QuoteMarkPrimaryResponse,
    QuoteSearchResponse,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector
from everstage_ddd.cpq.quote.service.quote_line_item_service import (
    clone_quote_line_items_to_new_quote,
)
from everstage_ddd.cpq.quote.types import (
    QuoteCloneRequest,
    QuoteCreateRequest,
    QuoteDeleteRequest,
    QuoteDetailsItem,
    QuoteFilterRequest,
    QuoteFormMappingData,
    QuoteListItem,
    QuoteListRequest,
    QuoteMarkPrimaryRequest,
    QuoteSearchRequest,
)
from everstage_ddd.cpq.quote.utils import (
    _format_data_for_assert,
    get_duration_label,
    get_table_variables,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.services.rbac_services import get_data_permission, get_valid_payee_emails

from ..schema import GetQuoteResponse

logger = logging.getLogger(__name__)


def get_new_quote_display_id(client_id: int) -> int:
    """
    Get the quote display ID for a given client.
    """
    quote_selector = QuoteSelector(client_id=client_id)
    max_display_id = quote_selector.get_max_quote_display_id()

    return max_display_id + 1


def create_empty_autosave_form(client_id, form_builder_id, form_id):
    """
    Create an empty autosave form.
    """
    auto_save_form_selector = AutoSaveFormAccessor(client_id)
    return auto_save_form_selector.create_object(form_id, form_builder_id)


def create_empty_form_spec_change(client_id, form_builder_id, form_id, form_spec):
    """
    Create an empty form spec change record.
    """
    data = {
        "form_id": form_id,
        "form_builder_id": form_builder_id,
        "form_spec": form_spec,
    }
    auto_save_form_selector = FormSpecSelector(client_id)
    return auto_save_form_selector.create_form_spec_change(data)


def create_empty_everstage_form(
    client_id, form_builder_id, created_by, form_spec, form_data
):
    """
    Create an empty everstage form.
    """
    form_selector = EverstageFormAccessor(client_id)
    return form_selector.create_object(
        form_builder_id, user=created_by, form_spec=form_spec, form_data=form_data
    )


def get_untitled_quote_name(quote_names):
    """
    Get the default name for a quote.

    Args:
        quote_names (list): List of existing quote names in lowercase.

    Returns:
        str: A unique name for an untitled quote (e.g., "Untitled Quote(1)").
    """
    untitled_count = 1 + max(
        [
            int(name.split("(")[-1].split(")")[0])
            for name in quote_names
            if name.startswith("untitled quote(")
        ],
        default=0,  # Default to 0 if no matching names are found.
    )
    return f"Untitled Quote({untitled_count})"


def get_all_quote_names(client_id):
    """
    Get all quote names for a given client.

    Args:
        client_id (int): The ID of the client.

    Returns:
        list: A list of quote names in lowercase.
    """
    all_quotes = list(
        QuoteSelector(client_id=client_id)
        .client_kd_deleted_aware()
        .values("quote_name")
    )

    return [quote["quote_name"].lower() for quote in all_quotes]


def mapping_quote_form_to_quote(
    quote_id,
    everstage_form: EverstageForm,
    additional_details: Dict[str, Any] | None = None,
    pdf_data: Dict[str, Any] | None = None,
    knowledge_date: datetime | None = None,
):
    from .quote_line_item_service import get_quote_summary

    logger.info("START: Mapping quote form to quote.")

    form_data = everstage_form.form_data
    client_id = everstage_form.client.client_id

    quote_fields_mapping = {
        "quote_name": "field2",
        "account_id": "field6",
        "contact_id": "field8",
        "quote_currency": "field9",
        "valid_till": "field11",
        "opportunity_id": "field4",
    }

    valid_till_date = convert_str_to_date(
        pydash.get(form_data, quote_fields_mapping["valid_till"])
    )
    valid_till = end_of_day(valid_till_date.replace(tzinfo=None))

    quote_summary = get_quote_summary(client_id, quote_id, use_kd=False)[0]
    custom_data = {"recurring_total": float(quote_summary["recurring_total"])}
    quote_create_request = QuoteFormMappingData(
        quote_name=pydash.get(form_data, quote_fields_mapping["quote_name"]),
        form_id=everstage_form.form_id,  # type: ignore
        quote_currency=pydash.get(form_data, quote_fields_mapping["quote_currency"]),
        valid_till=valid_till,
        opportunity_id=pydash.get(form_data, quote_fields_mapping["opportunity_id"]),
        account_id=pydash.get(form_data, quote_fields_mapping["account_id"]),
        contact_id=pydash.get(form_data, quote_fields_mapping["contact_id"]),
        list_quote_total=float(quote_summary["list_quote_total"]),
        net_quote_total=float(quote_summary["net_quote_total"]),
        discount_amount=float(quote_summary["discount_amount"]),
        custom_data=custom_data,
    )

    quote_data = {
        "quote_id": quote_id,
        "quote_name": quote_create_request.quote_name,
        "form_id": everstage_form.form_id,
        "quote_currency": quote_create_request.quote_currency,
        "account_id": quote_create_request.account_id,
        "opportunity_id": quote_create_request.opportunity_id,
        "contact_id": quote_create_request.contact_id,
        "valid_till": quote_create_request.valid_till,
        "list_quote_total": quote_create_request.list_quote_total,
        "net_quote_total": quote_create_request.net_quote_total,
        "discount_amount": quote_create_request.discount_amount,
        "custom_data": quote_create_request.custom_data,
        "additional_details": additional_details,
        "pdf_config_data": pdf_data,
    }

    quote_data = _format_data_for_assert(quote_data)

    quote_selector = QuoteSelector(client_id=client_id)
    created_quote = quote_selector.update_quote(
        quote_id, quote_data, knowledge_date=knowledge_date
    )

    logger.info("END: Mapping quote form to quote.")

    return created_quote


def is_quote_name_exists(quote_name, quote_names):
    """
    Check if a quote name already exists.

    Args:
        quote_name (str): The name to check.
        quote_names (list): List of existing quote names.

    Returns:
        bool: True if the name exists, False otherwise.
    """
    return quote_name.lower() in quote_names


@transaction.atomic
def create_quote(
    quote_create_request: QuoteCreateRequest,
) -> GetQuoteResponse:
    """
    Create a quote.
    """
    from everstage_ddd.cpq.forms.service.form_service import get_form_spec

    curr_time = timezone.now()
    client_id = quote_create_request.client_id
    created_by = quote_create_request.created_by
    form_builder_id = quote_create_request.form_builder_id
    form_spec = get_form_spec(client_id, form_builder_id)["form_spec"]
    additional_details = quote_create_request.additional_details

    quote_id = uuid.uuid4()
    quote_name = get_untitled_quote_name(
        quote_names=get_all_quote_names(client_id=client_id)
    )
    valid_till = end_of_day((curr_time + timedelta(days=30)))

    updated_form_spec = set_quote_data_in_form_spec(
        form_spec=form_spec,
        quote_id=quote_id,
        quote_name=quote_name,
        valid_till=valid_till.strftime("%Y-%m-%d"),
    )

    form_data = set_quote_info_in_form_data(
        form_data={},
        quote_id=quote_id,
        quote_name=quote_name,
        valid_till=valid_till.strftime("%Y-%m-%d"),
    )

    everstage_form = create_empty_everstage_form(
        client_id=client_id,
        form_builder_id=form_builder_id,
        created_by=created_by,
        form_spec=updated_form_spec,
        form_data=form_data,
    )

    terms_data = get_form_builder_terms(client_id, form_builder_id) or []
    valid_terms = [term for term in terms_data if term.get("default", False)]

    create_empty_autosave_form(
        client_id=client_id,
        form_builder_id=form_builder_id,
        form_id=everstage_form.form_id,
    )
    create_empty_form_spec_change(
        client_id=client_id,
        form_builder_id=form_builder_id,
        form_id=everstage_form.form_id,
        form_spec=updated_form_spec,
    )

    quote_selector = QuoteSelector(client_id=quote_create_request.client_id)

    quote_display_id = get_new_quote_display_id(client_id=client_id)
    quote_display_id_prefix = get_quote_display_id_prefix_from_db(client_id)

    quote_data = {
        "quote_name": quote_name,
        "quote_id": quote_id,
        "client_id": client_id,
        "form_id": everstage_form.form_id,
        "quote_display_id": quote_display_id,
        "quote_display_id_prefix": quote_display_id_prefix,
        "created_by": created_by,
        "created_at": curr_time,
        "owner_id": created_by,
        "valid_till": valid_till,
        "additional_details": additional_details,
        "terms": valid_terms,
    }

    quote_data = _format_data_for_assert(quote_data)

    created_quote = quote_selector.create_quote(quote_data)

    quote_status_request = QuoteStatusDataModel(
        client_id=client_id,
        quote_id=created_quote.quote_id,
        status=QuoteStatusEnum.DRAFT.value,
        created_at=curr_time,
        updated_by=quote_create_request.created_by or "",
    )

    create_quote_status(quote_status_request)

    response = GetQuoteResponse(form_spec=updated_form_spec)

    return response


def set_quote_data_in_form_spec(form_spec, quote_id, quote_name=None, valid_till=None):
    form_navigator = FormSpecNavigator(form_spec=form_spec)
    field = form_navigator.get_field("field1")
    field["value"] = str(quote_id)
    if quote_name:
        field = form_navigator.get_field("field2")
        field["value"] = quote_name
    if valid_till:
        field = form_navigator.get_field("field11")
        field["value"] = valid_till
    return form_spec


def set_quote_info_in_form_data(form_data, quote_id, quote_name, valid_till):
    form_data["field1"] = str(quote_id)
    form_data["field2"] = quote_name
    form_data["field11"] = valid_till
    return form_data


@transaction.atomic
def create_quote_status(quote_status_request: QuoteStatusDataModel) -> QuoteStatus:
    """
    Create a quote status.
    """
    quote_status_data = QuoteStatusDataModel(
        client_id=quote_status_request.client_id,
        quote_id=quote_status_request.quote_id,
        status=quote_status_request.status,
        created_at=quote_status_request.created_at,
        updated_by=quote_status_request.updated_by,
        tags=quote_status_request.tags,
        comment=quote_status_request.comment,
        additional_details=quote_status_request.additional_details,
    )

    quote_status_selector = QuoteStatusSelector(
        client_id=quote_status_request.client_id
    )
    quote_status = quote_status_selector.create_quote_status(data=quote_status_data)

    return quote_status


def fetch_opportunity_and_account_details(form_data, portal_id):
    opportunity_name = pydash.get(form_data, "field3")
    opportunity_type = pydash.get(form_data, "field12")
    opportunity_id = pydash.get(form_data, "field4")
    opportunity_link = (
        f"https://app.hubspot.com/contacts/{portal_id}/deals/{opportunity_id}"
    )
    account_id = pydash.get(form_data, "field6")
    account_name = pydash.get(form_data, "field5")

    return {
        "opportunity_id": opportunity_id,
        "opportunity_name": opportunity_name,
        "opportunity_type": opportunity_type,
        "opportunity_link": opportunity_link,
        "account_id": account_id,
        "account_name": account_name,
    }


def get_quote_name(form_data):
    return pydash.get(form_data, "field2")


# ruff: noqa: PLR0915, PLR0912
def list_quotes(
    list_request: QuoteListRequest,
    logged_in_user: str,
) -> QuoteListResponse:
    """
    List all quotes for a given client.

    :param client_id: The client ID to list quotes for.
    :return: A list of quotes.
    """
    from .quote_line_item_service import get_product_count_map

    client_id = list_request.client_id
    quote_selector = QuoteSelector(client_id=client_id)
    form_selector = EverstageFormAccessor(client_id=list_request.client_id)
    approvals_waiting_on_you = list_request.approvals_waiting_on_you
    filtered_quote_ids = set()
    any_filter_applied = approvals_waiting_on_you or list_request.status

    component = RBACComponent.QUOTES.value
    data_permission = get_data_permission(client_id, logged_in_user, component)

    # If the user does not have data permission, return an empty list,
    # as they will not be able to see any quotes.
    if not data_permission:
        return QuoteListResponse(quotes=[])

    valid_payee_emails = []
    if data_permission["type"] != RBAC.ALL_DATA.value:
        # If the user does not have all data permission, get the valid payee emails
        # We will show the quotes for which the valid payee emails are quote owners.
        valid_payee_emails = get_valid_payee_emails(
            client_id, logged_in_user, data_permission
        )
        if not valid_payee_emails:
            # If the user does not have valid payee emails, return an empty list,
            # as they will not be able to see any quotes.
            return QuoteListResponse(quotes=[])
    if approvals_waiting_on_you:
        pending_approval_quote_ids = get_all_quotes_waiting_on_you(
            client_id, logged_in_user
        )
        filtered_quote_ids.update(pending_approval_quote_ids)

    if list_request.status:
        needs_attention_quote_ids = get_quotes_with_needs_attention_status(client_id)
        _quote_ids = QuoteStatusSelector(client_id=client_id).get_quote_ids_by_status(
            list_request.status
        )
        _quote_ids_formatted = [str(quote_id) for quote_id in _quote_ids]
        filtered_quote_ids.update(_quote_ids_formatted)
        if QuoteStatusEnum.NEEDS_ATTENTION.value in list_request.status:
            filtered_quote_ids.update(needs_attention_quote_ids)

        if (
            QuoteStatusEnum.REJECTED.value in list_request.status
            and QuoteStatusEnum.NEEDS_ATTENTION.value not in list_request.status
        ):
            filtered_quote_ids.difference_update(needs_attention_quote_ids)

    quotes = quote_selector.get_filtered_quotes(
        limit=list_request.limit_value,
        offset=list_request.offset_value,
        search_term=list_request.search_term,
        quote_ids=list(filtered_quote_ids),
        account_id=list_request.account_id,
        owner_id=list_request.owner_id,
        valid_owners=valid_payee_emails,
        quote_prefix=get_quote_display_id_prefix_from_db(client_id),
        any_filter_applied=any_filter_applied,
    )
    product_count_map = get_product_count_map(client_id)

    client_settings = get_client_settings(client_id=client_id)
    portal_id = client_settings.get("cpq_settings", {}).get("portal_id", "")

    quote_ids = [str(quote.quote_id) for quote in quotes]

    quote_status_map = get_quote_status_map(client_id, quote_ids)
    quote_list = []
    for quote in quotes:
        form_id = quote.form_id
        form = form_selector.get_object(form_id=form_id)
        form_builder_id = form.form_builder_id
        form_data = form.form_data

        quote_statuses = quote_status_map.get(str(quote.quote_id), {})
        quote_status_list = set(list(quote_statuses.keys()))
        auto_save_form = form_selector.get_auto_save_form(form_id=form_id)
        auto_save_form_data = auto_save_form.form_data
        quote_display_id = (quote.quote_display_id_prefix) + str(quote.quote_display_id)

        quote_status_selector = QuoteStatusSelector(client_id=quote.client_id)
        quote_status = quote_status_selector.get_quote_status(quote_id=quote.quote_id)
        quote_name = quote.quote_name

        if quote_status and quote_status.status == QuoteStatusEnum.DRAFT.value:
            opportunity_details = fetch_opportunity_and_account_details(
                auto_save_form_data, portal_id
            )
            if auto_save_form_data and auto_save_form_data.get("field2"):
                quote_name = get_quote_name(auto_save_form_data)
        else:
            opportunity_details = fetch_opportunity_and_account_details(
                form_data, portal_id
            )
            if form_data and form_data.get("field2"):
                quote_name = get_quote_name(form_data)
        if (
            quote_status
            and len(quote_status_list) > 1
            and quote_status.status == QuoteStatusEnum.REJECTED.value
        ):
            _status = QuoteStatusEnum.NEEDS_ATTENTION.value
        else:
            _status = (
                quote_status.status if quote_status else QuoteStatusEnum.DRAFT.value
            )

        employee_accessor = EmployeeAccessor(client_id=client_id)
        employee = employee_accessor.get_employee_by_email_id(quote.owner_id)
        quote_list.append(
            QuoteListItem(
                quote_id=quote.quote_id,
                quote_display_id=quote_display_id,
                status=_status,
                quote_name=quote_name,
                form_id=form_id,
                form_builder_id=form_builder_id,
                is_primary=quote.is_primary,
                opportunity_id=opportunity_details.get("opportunity_id"),
                opportunity_name=opportunity_details.get("opportunity_name"),
                opportunity_type=str(opportunity_details.get("opportunity_type")),
                opportunity_link=opportunity_details.get("opportunity_link"),
                account_id=opportunity_details.get("account_id"),
                account_name=opportunity_details.get("account_name"),
                quote_currency=quote.quote_currency,
                list_quote_total=(
                    quote.list_quote_total if quote.list_quote_total else 0
                ),
                net_quote_total=(quote.net_quote_total if quote.net_quote_total else 0),
                discount_amount=(quote.discount_amount if quote.discount_amount else 0),
                closed_date=quote.closed_date,
                owner_id=(
                    f"{employee[0].first_name} {employee[0].last_name}"
                    if employee
                    else ""
                ),
                updated_at=quote.knowledge_begin_date,
                updated_by=(
                    quote.additional_details.get("updated_by", "")
                    if quote.additional_details
                    else None
                ),
                valid_till=quote.valid_till,
                start_date=quote.start_date,
                end_date=quote.end_date,
                created_by=quote.created_by,
                created_at=quote.created_at,
                duration_value=quote.duration_value,
                duration_type=get_duration_label(
                    quote.duration_type, quote.duration_value
                ),
                product_count=product_count_map.get(str(quote.quote_id), 0),
                status_instance_data=quote_status_map.get(str(quote.quote_id), {}),
            )
        )
    return QuoteListResponse(quotes=quote_list)


def quote_status_count(client_id: str, logged_in_user: str) -> AccountDetailsResponse:
    """
    Get the count of quotes for each status, employee and account details.
    """

    component = RBACComponent.QUOTES.value
    data_permission = get_data_permission(client_id, logged_in_user, component)

    # If the user does not have data permission, return an empty list,
    # as they will not be able to see any filters.
    if not data_permission:
        return AccountDetailsResponse(
            quote_status=[],
            employee_details=[],
            account_details=[],
        )

    quote_ids = []
    if data_permission["type"] != RBAC.ALL_DATA.value:
        # If the user does not have all data permission, get the valid payee emails
        # We will show the quotes for which the valid payee emails are quote owners.
        valid_payee_emails = get_valid_payee_emails(
            client_id, logged_in_user, data_permission
        )
        if not valid_payee_emails:
            # If the user does not have valid payee emails, return an empty list,
            # as they will not be able to see any filters.
            return AccountDetailsResponse(
                quote_status=[],
                employee_details=[],
                account_details=[],
            )
        quote_ids = QuoteSelector(client_id).get_quote_ids_by_owner_id(
            valid_payee_emails
        )
        quote_ids = [str(quote_id) for quote_id in quote_ids]

    # get quote status count
    quote_status = QuoteStatusSelector(client_id=client_id).get_quote_status_count(
        quote_ids
    )
    needs_attention_quote_ids = get_quotes_with_needs_attention_status(client_id)
    if needs_attention_quote_ids:
        for quote_status_obj in quote_status:
            if quote_status_obj["status"] == QuoteStatusEnum.REJECTED.value:
                quote_status_obj["count"] = quote_status_obj["count"] - len(
                    needs_attention_quote_ids
                )
        quote_status.append(
            {
                "status": QuoteStatusEnum.NEEDS_ATTENTION.value,
                "count": len(needs_attention_quote_ids),
            }
        )

    # get employee/owner_id map
    employee_details = EmployeeAccessor(client_id=client_id).get_all_employee_name_map()

    # get account details
    client_settings = get_client_settings(int(client_id))
    cpq_settings = client_settings.get("cpq_settings", {})
    company_databook_id = cpq_settings.get("company_databook_id", None)
    company_datasheet_id = cpq_settings.get("company_datasheet_id", None)
    company_name = cpq_settings.get("company_system_name_col_name", None)
    company_id = cpq_settings.get("company_id_col_name", None)
    account_details = []

    # check if all the required fields are present in the cpq_settings table
    if company_databook_id and company_datasheet_id and company_name and company_id:
        pricebook_variables = get_table_variables(client_id, company_datasheet_id)
        pricebook_variables = [
            column
            for column in pricebook_variables
            if column.system_name in {company_name, company_id}
        ]
        matrix = PriceBookDatasheetTable(
            params=PriceBookDatasheetTableParams(
                client_id=client_id,
                table_columns=pricebook_variables,
                primary_key_column="row_key",
                unique_table_id=company_datasheet_id,
                unique_schema_id=company_databook_id,
            ),
        )
        records = matrix.get_table_df(use_semantic_tag=True)
        records = records.execute().to_dict(orient="records")
        account_details = [
            {
                "account_name": record.get(company_name),
                "account_id": record.get(company_id),
            }
            for record in records
        ]

    return AccountDetailsResponse(
        quote_status=quote_status,
        employee_details=employee_details,
        account_details=account_details,
    )


def get_quote_details_paginated(client_id, limit, offset, search_term, logged_in_user):
    if limit is not None and offset is not None:
        limit = int(limit)
        offset = int(offset)

    component = RBACComponent.QUOTES.value
    data_permission = get_data_permission(client_id, logged_in_user, component)

    # If the user does not have data permission, return an empty list,
    # as they will not be able to see any quotes.
    if not data_permission:
        return QuoteDetailsResponse(quotes=[])

    valid_payee_emails = []
    if data_permission["type"] != RBAC.ALL_DATA.value:
        # If the user does not have all data permission, get the valid payee emails
        # We will show the quotes for which the valid payee emails are quote owners.
        valid_payee_emails = get_valid_payee_emails(
            client_id, logged_in_user, data_permission
        )
        if not valid_payee_emails:
            # If the user does not have valid payee emails, return an empty list,
            # as they will not be able to see any quotes.
            return QuoteDetailsResponse(quotes=[])

    quotes = QuoteSelector(client_id).get_filtered_quotes(
        limit=limit,
        offset=offset,
        search_term=search_term,
        quote_prefix=get_quote_display_id_prefix_from_db(client_id),
        valid_owners=valid_payee_emails,
    )
    quote_list = []
    for quote in quotes:
        quote_display_id = (quote.quote_display_id_prefix) + str(quote.quote_display_id)
        quote_status = QuoteStatusSelector(client_id=quote.client_id).get_quote_status(
            quote_id=quote.quote_id
        )
        quote_list.append(
            QuoteDetailsItem(
                quote_id=quote.quote_id,
                quote_display_id=quote_display_id,
                quote_name=quote.quote_name,
                status=(
                    quote_status.status if quote_status else QuoteStatusEnum.DRAFT.value
                ),
            )
        )
    return QuoteDetailsResponse(quotes=quote_list)


def filter_quotes(filter_request: QuoteFilterRequest) -> QuoteFilterResponse:
    """
    Filters quotes based on the provided arguments.

    :param client_id: The client to filter by.
    :param filters: Filters like opportunity_id, status, created_by.
    :return: QuerySet of filtered quotes.
    """
    from .quote_line_item_service import get_product_count_map

    client_id = filter_request.client_id
    opportunity_id = filter_request.opportunity_id
    status = filter_request.status
    created_by = filter_request.created_by
    product_count_map = get_product_count_map(client_id)

    query = Q(client_id=client_id)

    quote_selector = QuoteSelector(client_id=client_id)

    if opportunity_id:
        query &= Q(opportunity_id=opportunity_id)
    if status:
        quote_status_selector = QuoteStatusSelector(client_id=client_id)
        quote_ids_with_status = quote_status_selector.get_quote_ids_by_status(status)

        query &= Q(quote_id__in=quote_ids_with_status)
    if created_by:
        query &= Q(created_by=created_by)

    filtered_quotes = quote_selector.filter_quotes(query=query)

    client_settings = get_client_settings(client_id=client_id)
    portal_id = client_settings.get("cpq_settings", {}).get("portal_id", "")

    quote_list = []
    for quote in filtered_quotes:
        form_id = quote.form_id
        form_selector = EverstageFormAccessor(client_id=client_id)
        form = form_selector.get_object(form_id=form_id)
        form_data = form.form_data
        form_builder_id = form.form_builder_id

        auto_save_form = form_selector.get_auto_save_form(form_id=form_id)
        auto_save_form_data = auto_save_form.form_data

        quote_display_id = (quote.quote_display_id_prefix) + str(quote.quote_display_id)
        quote_status_selector = QuoteStatusSelector(client_id=client_id)
        quote_status = quote_status_selector.get_quote_status(quote_id=quote.quote_id)

        if quote_status == QuoteStatusEnum.DRAFT.value:
            opportunity_details = fetch_opportunity_and_account_details(
                auto_save_form_data, portal_id
            )
        else:
            opportunity_details = fetch_opportunity_and_account_details(
                form_data, portal_id
            )

        quote_list.append(
            QuoteListItem(
                quote_id=quote.quote_id,
                quote_display_id=quote_display_id,
                quote_name=quote.quote_name,
                form_id=quote.form_id,
                status=(
                    quote_status.status if quote_status else QuoteStatusEnum.DRAFT.value
                ),
                is_primary=quote.is_primary,
                form_builder_id=form_builder_id,
                opportunity_id=opportunity_details.get("opportunity_id"),
                opportunity_name=opportunity_details.get("opportunity_name"),
                opportunity_type=str(opportunity_details.get("opportunity_type")),
                opportunity_link=opportunity_details.get("opportunity_link"),
                account_id=opportunity_details.get("account_id"),
                account_name=opportunity_details.get("account_name"),
                duration_value=quote.duration_value,
                duration_type=get_duration_label(
                    quote.duration_type, quote.duration_value
                ),
                quote_currency=quote.quote_currency,
                list_quote_total=quote.list_quote_total,
                net_quote_total=quote.net_quote_total,
                discount_amount=quote.discount_amount,
                closed_date=quote.closed_date,
                owner_id=quote.owner_id,
                updated_at=quote.additional_details.get("updated_at", ""),
                updated_by=quote.additional_details.get("updated_by", ""),
                valid_till=quote.valid_till,
                start_date=quote.start_date,
                end_date=quote.end_date,
                created_by=quote.created_by,
                created_at=quote.created_at,
                product_count=product_count_map.get(str(quote.quote_id), 0),
            )
        )
    return QuoteFilterResponse(quotes=quote_list)


def search_quotes(search_request: QuoteSearchRequest) -> QuoteSearchResponse:
    """
    Search quotes by quote_name or quote_id.

    :param client_id: The client ID to search within.
    :param search_term: The term to search for in quote_name or quote_id.
    :return: QuerySet of matching quotes.
    """
    from .quote_line_item_service import get_product_count_map

    client_id = search_request.client_id
    search_term = search_request.search_term
    quote_selector = QuoteSelector(client_id=client_id)
    quotes = quote_selector.search_quotes(search_term=search_term)
    product_count_map = get_product_count_map(client_id)

    client_settings = get_client_settings(client_id=client_id)
    portal_id = client_settings.get("cpq_settings", {}).get("portal_id", "")

    quote_list = []
    for quote in quotes:
        form_id = quote.form_id
        form_selector = EverstageFormAccessor(client_id=client_id)
        form = form_selector.get_object(form_id=form_id)
        form_data = form.form_data
        form_builder_id = form.form_builder_id

        auto_save_form = form_selector.get_auto_save_form(form_id=form_id)
        auto_save_form_data = auto_save_form.form_data

        quote_display_id = (quote.quote_display_id_prefix) + str(quote.quote_display_id)

        quote_status_selector = QuoteStatusSelector(client_id=client_id)
        quote_status = quote_status_selector.get_quote_status(quote_id=quote.quote_id)

        if quote_status and quote_status.status == QuoteStatusEnum.DRAFT.value:
            opportunity_details = fetch_opportunity_and_account_details(
                auto_save_form_data, portal_id
            )
        else:
            opportunity_details = fetch_opportunity_and_account_details(
                form_data, portal_id
            )

        quote_list.append(
            QuoteListItem(
                quote_id=quote.quote_id,
                quote_display_id=quote_display_id,
                quote_name=quote.quote_name,
                form_id=quote.form_id,
                form_builder_id=form_builder_id,
                status=(
                    quote_status.status if quote_status else QuoteStatusEnum.DRAFT.value
                ),
                opportunity_id=quote.opportunity_id,
                is_primary=quote.is_primary,
                opportunity_name=opportunity_details.get("opportunity_name"),
                opportunity_type=str(opportunity_details.get("opportunity_type")),
                account_id=opportunity_details.get("account_id"),
                account_name=opportunity_details.get("account_name"),
                quote_currency=quote.quote_currency,
                duration_value=quote.duration_value,
                duration_type=get_duration_label(
                    quote.duration_type, quote.duration_value
                ),
                list_quote_total=quote.list_quote_total,
                net_quote_total=quote.net_quote_total,
                discount_amount=quote.discount_amount,
                closed_date=quote.closed_date,
                owner_id=quote.owner_id,
                updated_at=quote.additional_details.get("updated_at", ""),
                updated_by=quote.additional_details.get("updated_by", ""),
                valid_till=quote.valid_till,
                start_date=quote.start_date,
                end_date=quote.end_date,
                created_by=quote.created_by,
                created_at=quote.created_at,
                product_count=product_count_map.get(str(quote.quote_id), 0),
            )
        )
    return QuoteSearchResponse(quotes=quote_list)


@transaction.atomic
def mark_quote_as_primary(
    mark_primary_request: QuoteMarkPrimaryRequest,
) -> QuoteMarkPrimaryResponse:
    """
    Mark a quote as primary by setting the is_primary flag to True.

    :param client_id: The client ID to mark the quote within.
    :param quote_id: The ID of the quote to mark as primary.
    :return: The marked quote.
    """
    client_id = mark_primary_request.client_id
    quote_id = mark_primary_request.quote_id

    try:
        quote_selector = QuoteSelector(client_id=client_id)
        quote = quote_selector.get_quote_by_id(quote_id=quote_id)

        opportunity_id = quote.opportunity_id

        existing_primary_quote = quote_selector.get_primary_quote_for_opportunity(
            opportunity_id=opportunity_id
        )

        if existing_primary_quote:
            if quote_id == existing_primary_quote.quote_id:
                return QuoteMarkPrimaryResponse(quote_id=quote_id, is_primary=True)
            quote_selector.update_quote(
                quote_id=existing_primary_quote.quote_id,
                data={"quote_id": existing_primary_quote.quote_id, "is_primary": False},
            )
        updated_quote = quote_selector.update_quote(
            quote_id=quote_id, data={"quote_id": quote_id, "is_primary": True}
        )
    except ObjectDoesNotExist as error:
        raise QuoteError(
            code="QUOTE_DOES_NOT_EXIST",
            message=f"Quote does not exist due to an error: {error}",
        ) from error
    except Exception as error:
        raise QuoteError(
            code="QUOTE_MARK_PRIMARY_FAILED",
            message=f"Failed to mark quote as primary due to an error: {error}",
        ) from error

    return QuoteMarkPrimaryResponse(
        quote_id=updated_quote.quote_id, is_primary=updated_quote.is_primary
    )


@transaction.atomic
def delete_quote(
    quote_delete_request: QuoteDeleteRequest, logged_in_user: str
) -> QuoteDeleteResponse:
    """
    Delete a quote by ID.

    :param quote_id: The ID of the quote to delete.
    :return: The deleted quote.
    """
    from everstage_ddd.cpq.docusign.docusign_enums import SharingTypeEnum
    from everstage_ddd.cpq.docusign.services.docusign_template_service import (
        envelope_void,
        get_quote_template_data,
    )
    from everstage_ddd.cpq.quote.service.quote_line_item_service import (
        delete_quote_line_items,
    )

    client_id = quote_delete_request.client_id
    quote_id = quote_delete_request.quote_id

    quote_selector = QuoteSelector(client_id=client_id)
    quote = quote_selector.get_quote_by_id(quote_id=quote_id)
    quote_status_selector = QuoteStatusSelector(client_id=client_id)
    quote_status = quote_status_selector.get_quote_status(quote_id=quote_id)

    # Before deleting the quote, we need to void the envelope if the quote is pending signature and it is shared via docusign
    # If we get an error while voiding the envelope, we should not delete the quote
    if quote_status and quote_status.status == QuoteStatusEnum.PENDING_SIGNATURE.value:
        quote_template_data = get_quote_template_data(
            client_id=client_id, quote_id=quote_id
        )
        if (
            quote_template_data.get("quote_template_details", {}).get("sharing_type")
            == SharingTypeEnum.DOCUSIGN.value
        ):
            envelope_id = quote_template_data.get("quote_template_details", {}).get(
                "envelope_id"
            )
            if envelope_id:
                envelope_void(
                    client_id=client_id,
                    email_id=logged_in_user,
                    envelope_id=envelope_id,
                    void_reason="The associated quote was removed from Everstage.",
                    quote_id=quote_id,
                    need_to_change_quote_status=False,
                )

    form_accessor = EverstageFormAccessor(client_id=client_id)
    form = form_accessor.get_object(form_id=quote.form_id)
    form_accessor.delete_object(form_id=form.form_id)

    autosave_form = AutoSaveFormAccessor(
        client_id=client_id,
    )
    autosave_form.delete_object(form_id=form.form_id)

    quote_selector.delete_quote(quote_id=quote_id)

    quote_status_selector.delete_quote_status(quote_id=quote_id)

    delete_quote_line_items(client_id, quote_id)
    delete_quote_approval_requests(client_id, logged_in_user, quote_id)
    return QuoteDeleteResponse(
        quote_id=quote_id,
        quote_status=quote_status.quote_status_id if quote_status else None,
    )


@transaction.atomic
def clone_quote(quote_clone_request: QuoteCloneRequest) -> GetQuoteResponse:
    """
    Clone a quote by ID.

    :param quote_id: The ID of the quote to clone.
    :return: The cloned quote.
    """
    client_id = quote_clone_request.client_id
    quote_id = quote_clone_request.quote_id
    created_by = quote_clone_request.created_by
    curr_time = timezone.now()

    quote_selector = QuoteSelector(client_id=client_id)
    quote = quote_selector.get_quote_by_id(quote_id=quote_id)

    form_accessor = EverstageFormAccessor(client_id=client_id)
    form = form_accessor.get_object(form_id=quote.form_id)

    form_spec_change = FormSpecSelector(client_id).get_form_spec_change_by_id(
        quote.form_id, projection=["form_spec"]
    )
    form_spec = form_spec_change["form_spec"]
    quote_new_display_id = get_new_quote_display_id(client_id=client_id)
    quote_display_id_prefix = get_quote_display_id_prefix_from_db(client_id)

    autosave_form_accessor = AutoSaveFormAccessor(
        client_id=client_id,
    )

    autosave_form = autosave_form_accessor.get_last_saved_form(form_id=form.form_id)

    quote_status_selector = QuoteStatusSelector(client_id=client_id)
    quote_status = quote_status_selector.get_quote_status(quote_id=quote_id)
    if quote_status and quote_status.status == QuoteStatusEnum.DRAFT.value:
        form_data = autosave_form.form_data
        old_quote_name = form_data.get("field2", quote.quote_name)
        quote_status_kd = None
    else:
        form_data = form.form_data
        old_quote_name = quote.quote_name
        quote_status_kd = quote_status.knowledge_begin_date

    new_quote_id = uuid.uuid4()
    quote_name = get_new_quote_name(client_id=client_id, quote_name=old_quote_name)
    valid_till = end_of_day((curr_time + timedelta(days=30)))

    form_spec = set_quote_data_in_form_spec(
        form_spec=form_spec,
        quote_id=new_quote_id,
        quote_name=quote_name,
        valid_till=valid_till.strftime("%Y-%m-%d"),
    )

    updated_form_data = set_quote_info_in_form_data(
        form_data=form_data,
        quote_id=new_quote_id,
        quote_name=quote_name,
        valid_till=valid_till.strftime("%Y-%m-%d"),
    )

    cloned_form = form_accessor.create_and_clone_form(
        form=form,
        updated_form_spec=form_spec,
        updated_form_data=updated_form_data,
        audit={
            "knowledge_begin_date": curr_time,
            "created_by": created_by,
            "updated_by": created_by,
        },
    )

    create_empty_form_spec_change(
        client_id=client_id,
        form_builder_id=form.form_builder_id,
        form_id=cloned_form.form_id,
        form_spec=form_spec,
    )

    autosave_form_accessor.create_object(
        form_data=form_data,
        form_id=cloned_form.form_id,
        form_builder_id=form.form_builder_id,
        kd=curr_time,
    )

    cloned_quote_data = {
        "quote_id": new_quote_id,
        "client_id": quote.client_id,
        "quote_name": quote_name,
        "quote_display_id": quote_new_display_id,
        "quote_display_id_prefix": quote_display_id_prefix,
        "form_id": cloned_form.form_id,
        "quote_currency": quote.quote_currency,
        "start_date": quote.start_date,
        "end_date": quote.end_date,
        "duration_value": quote.duration_value,
        "duration_type": quote.duration_type,
        "account_id": quote.account_id,
        "contact_id": quote.contact_id,
        "valid_till": valid_till,
        "opportunity_id": quote.opportunity_id,
        "is_primary": False,
        "list_quote_total": quote.list_quote_total,
        "net_quote_total": quote.net_quote_total,
        "discount_amount": quote.discount_amount,
        "closed_date": quote.closed_date,
        "custom_data": quote.custom_data,
        "owner_id": created_by,
        "created_by": created_by,
        "created_at": curr_time,
        "additional_details": quote.additional_details,
        "terms": quote.terms,
    }

    cloned_quote = quote_selector.create_quote(data=cloned_quote_data)

    quote_status_request = QuoteStatusDataModel(
        client_id=client_id,
        quote_id=cloned_quote.quote_id,
        status=QuoteStatusEnum.DRAFT.value,
        created_at=curr_time,
        updated_by=created_by or "",
    )

    create_quote_status(quote_status_request)

    clone_quote_line_items_to_new_quote(
        client_id, curr_time, quote_status_kd, quote_id, new_quote_id
    )

    return GetQuoteResponse(form_spec=form_spec, new_quote_id=new_quote_id)


def get_new_quote_name(client_id: int, quote_name: str) -> str:
    """
    Get the new quote name for a given client. If the quote is already a copy,
    increment the number after 'Copy' instead of adding another 'Copy'.
    """
    quote_selector = QuoteSelector(client_id=client_id)
    quote_names = quote_selector.get_all_quote_names()
    auto_save_form_accessor = AutoSaveFormAccessor(client_id=client_id)
    auto_save_names = auto_save_form_accessor.get_all_values_of_field("field2")
    draft_names = [name.lower() for name in auto_save_names if isinstance(name, str)]
    all_quote_names = list(set(quote_names)) + list(set(draft_names))

    # Regex to match the pattern 'Copy(<number>)' at the end of the quote name
    copy_pattern = re.compile(r"(.*?)( Copy\((\d+)\))?$")

    # Match the quote name to see if it already contains a 'Copy <number>'
    match = copy_pattern.match(quote_name)
    if match:
        base_name = match.group(1)  # Original quote name without 'Copy <number>'
        copy_number = (
            int(match.group(3)) if match.group(3) else 0
        )  # Existing copy number

    new_quote_name = quote_name
    counter = (
        copy_number + 1
    )  # Start incrementing from the next number if it already exists

    while new_quote_name.lower() in all_quote_names:
        new_quote_name = f"{base_name} Copy({counter})"
        counter += 1
    return new_quote_name


def exit_quote(client_id, quote_id, form_id, form_builder_id, form_data):
    from ..accessors.quote_line_item_accessor import QuoteLineItemAccessor
    from .quote_line_item_service import get_quote_summary

    quote_selector = QuoteSelector(client_id=client_id)
    kd = timezone.now()

    quote_status_selector = QuoteStatusSelector(client_id=client_id)
    quote_status = quote_status_selector.get_quote_status(quote_id=quote_id)
    # if status = DRAFT , save in auto save form
    if quote_status and quote_status.status == QuoteStatusEnum.DRAFT.value:
        quote_summary, _ = get_quote_summary(client_id, quote_id)
        autosave_form_accessor = AutoSaveFormAccessor(
            client_id=client_id,
        )
        autosave_form_accessor.insert_object(form_data, form_id, form_builder_id)

        quote_data = {
            "valid_till": pydash.get(form_data, "field11"),
            "opportunity_id": pydash.get(form_data, "field4"),
            "account_id": pydash.get(form_data, "field6"),
            "contact_id": pydash.get(form_data, "field8"),
        }
        if pydash.get(form_data, "field2"):
            quote_data["quote_name"] = pydash.get(form_data, "field2")

        if float(quote_summary["net_quote_total"]) > 0:
            quote_data["list_quote_total"] = float(quote_summary["list_quote_total"])
            quote_data["net_quote_total"] = float(quote_summary["net_quote_total"])
            quote_data["discount_amount"] = float(quote_summary["discount_amount"])

        quote_selector.update_quote(quote_id, quote_data, knowledge_date=kd)

    # Delete quote line items that are created after draft state but are not published yet.
    if quote_status and quote_status.status != QuoteStatusEnum.DRAFT.value:
        quote_status_kd = quote_status.knowledge_begin_date

        quote_selector = QuoteSelector(client_id)
        quotes_created_after_kd = quote_selector.get_quotes_after_kd(
            quote_id, quote_status_kd
        )
        if quotes_created_after_kd:
            quote_selector.delete_quotes_after_kd(quote_id, quote_status_kd)
            quote_selector.remove_ked_for_valid_quote(quote_id, quote_status_kd)

        qli_acc = QuoteLineItemAccessor(client_id)
        qli_created_after_kd = qli_acc.get_items_after_kd(quote_id, quote_status_kd)
        qli_ids = [li.quote_line_item_id for li in qli_created_after_kd]
        if qli_ids:
            qlis_valid_at_kd = qli_acc.get_quote_line_item_at_kd(
                quote_id, quote_status_kd, qli_ids
            )
            valid_qli_ids = [li.quote_line_item_id for li in qlis_valid_at_kd]
            # we should delete this qli_ids and remove ked for valid_qlis at that time
            qli_acc.delete_qli_created_after_kd(quote_id, quote_status_kd, qli_ids)
            qli_acc.remove_ked_for_valid_qli(quote_id, quote_status_kd, valid_qli_ids)

    return {"status": "SUCCESS"}


def update_terms(client_id, quote_id, terms):
    quote_selector = QuoteSelector(client_id)
    quote = quote_selector.update_quote(quote_id, {"terms": terms})
    return quote


def get_quote_terms(client_id, quote_id):
    quote = QuoteSelector(client_id).get_quote_by_id(quote_id)
    return quote.terms
