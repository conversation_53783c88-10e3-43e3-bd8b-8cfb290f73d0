import json
import logging
import os
import uuid
from pathlib import Path

from django.template import Context, Template
from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_settings
from common.html_to_pdf import convert_html_to_pdf
from everstage_ddd.cpq.basic_settings.services.basic_settings_service import (
    get_client_cpq_basic_settings_data,
    get_client_cpq_pdf_settings_data,
)
from everstage_ddd.cpq.docusign.services.docusign_template_service import (
    get_docusign_account_settings_data,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.utils import convert_img_to_base64

logger = logging.getLogger(__name__)

TEMP_FILE_PATH = "temp_quotes"


def django_template_to_pdf(
    client_id,
    template_name,
    header_template_name,
    footer_template_name,
    context,
    **kwargs,
):
    is_local_env = kwargs.get("is_local_env", False)
    order_id = kwargs.get("order_id")
    is_custom_template = kwargs.get("is_custom_template")
    strip_color = kwargs.get("strip_color")
    footer_text = kwargs.get("footer_text", "")
    key = str(uuid.uuid4())
    base_html = Path(f"everstage_ddd/cpq/templates/{template_name}").read_text()

    template = Template(base_html)
    html_string = template.render(Context(context))

    # If render_statements_to_html services is called from python function(local development), then save the html file in Downloads folder
    if is_local_env:
        # downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
        downloads_path = Path.home() / "Downloads"
        file_path = downloads_path / "example.html"
        file_path.write_text(html_string)
        return None
    # The rendered html file is temporarily stored in the current working directory
    file_path = Path.cwd() / f"{TEMP_FILE_PATH}_{key}.html"
    file_path.write_text(html_string)

    if is_custom_template:
        margin = {}
        base_header = Path(
            f"everstage_ddd/cpq/templates/{header_template_name}"
        ).read_text()
        base_footer = Path(
            f"everstage_ddd/cpq/templates/{footer_template_name}"
        ).read_text()

        header_template = Template(base_header)
        footer_template = Template(base_footer)

        header_html = header_template.render(Context({"strip_color": strip_color}))
        footer_html = footer_template.render(
            Context(
                {
                    "footer_text": footer_text,
                }
            )
        )
    else:
        margin = {
            "top": "125px",
            "bottom": "90px",
        }
        base_header = Path("everstage_ddd/cpq/templates/header.html").read_text()
        base_footer = Path("everstage_ddd/cpq/templates/footer.html").read_text()

        header_template = Template(base_header)
        footer_template = Template(base_footer)

        everstage_logo = (
            os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
            + "/everstage-pdf-header.svg"
        )
        everstage_logo_base64 = convert_img_to_base64(everstage_logo)

        header_html = header_template.render(
            Context(
                {
                    "everstage_logo": everstage_logo_base64,
                }
            )
        )
        footer_html = footer_template.render(
            Context(
                {
                    "footer_text": footer_text,
                }
            )
        )

    # The html_path and pdf_path are created in the current working directory as temporary files
    html_path = Path.cwd() / f"{TEMP_FILE_PATH}_{key}.html"
    pdf_path = Path.cwd() / f"{TEMP_FILE_PATH}_{key}.pdf"

    s3_html = (
        key
        + (order_id if order_id else "")
        + "_"
        + str(timezone.now().strftime("%Y%m%d%H%M%S"))
        + ".html"
    )
    pdf_data = convert_html_to_pdf(
        client_id=client_id,
        html_path=html_path,
        pdf_path=pdf_path,
        s3_html=s3_html,
        bulk_key=None,
        header_template=header_html,
        footer_template=footer_html,
        margin=margin,
        display_header_footer=True,
        print_background=True,
    )
    if pdf_path.exists():
        logger.info("Deleting temporary pdf file for client: %s", client_id)
        pdf_path.unlink()
    if html_path.exists():
        logger.info("Deleting temporary html file for client: %s", client_id)
        html_path.unlink()
    logger.info("END: django template to pdf")
    return pdf_data


def get_signature_data(client_id, recipients=None):
    if recipients is None:
        docusign_account_settings_data = get_docusign_account_settings_data(client_id)
        recipients = docusign_account_settings_data.get("default_signer", [])

    signature_data = {}

    for recepient in recipients:
        if not recepient.get("signHere") and not recepient.get("signatureUrl"):
            continue

        company_name = recepient["title"]

        if company_name not in signature_data:
            signature_data[company_name] = {
                "company": company_name,
                "signatories": [],
            }

        signature_data[company_name]["signatories"].append(
            {
                "name": recepient["name"],
                "title": recepient["role"],
                "signatureUrl": recepient.get("signatureUrl", None),
                "signatureTag": recepient.get("signHere", ""),
            }
        )

    return list(signature_data.values())


def download_quote_as_pdf(
    client_id=None,
    pdf_data=None,
    recipients=None,
) -> bytes:

    if pdf_data is None:
        pdf_data = {}

    order_id = pdf_data.get("orderId", "")
    is_custom_template = pdf_data.get("customTemplate", False)
    strip_color = pdf_data.get("theme", {}).get("stripColor", "")
    footer_text = pdf_data.get("theme", {}).get("footerText", "")
    signature_data = get_signature_data(client_id, recipients)
    pdf_data["previewMode"] = False
    pdf_data["signatureData"] = signature_data

    pdf_formatted_data = {
        "data": json.dumps(pdf_data),
    }

    if is_custom_template:
        template_name = "custom_quote.html"
        header_template_name = "custom_header.txt"
        footer_template_name = "custom_footer.html"
    else:
        template_name = "quote.html"
        header_template_name = "header.html"
        footer_template_name = "footer.html"
    pdf_bytes = django_template_to_pdf(
        client_id=client_id,
        template_name=template_name,
        context=pdf_formatted_data,
        is_local_env=False,
        order_id=order_id,
        is_custom_template=is_custom_template,
        strip_color=strip_color,
        header_template_name=header_template_name,
        footer_template_name=footer_template_name,
        footer_text=footer_text,
    )
    return pdf_bytes


def get_pdf_template_context(client_id, quote_id):
    logger.info(
        f"START: get_pdf_template_context for client_id: {client_id} and quote_id: {quote_id}"
    )
    quote = QuoteSelector(client_id).get_quote_by_id(quote_id)
    quote_id = (quote.quote_display_id_prefix) + str(quote.quote_display_id)
    signature_data = get_signature_data(client_id)
    theme_data = get_client_cpq_pdf_settings_data(client_id)
    theme = theme_data.get("pdf_settings", {})
    basic_settings = get_client_cpq_basic_settings_data(client_id)
    created_by = quote.owner_id
    created_by_name = EmployeeAccessor(client_id).get_employee_full_name(created_by)

    company_name = basic_settings.get("company_info", {}).get("company_name", "")
    company_display_address = (
        basic_settings.get("company_info", {}).get("address_line_1", "")
        + ", "
        + basic_settings.get("company_info", {}).get("address_line_2", "")
        + ", "
        + basic_settings.get("company_info", {}).get("state_or_province", "")
        + ", "
        + basic_settings.get("company_info", {}).get("postal_code_or_zip", "")
    )
    client_settings = get_client_settings(client_id)
    custom_pdf = client_settings.get("cpq_settings", {}).get("custom_pdf", True)
    if client_id in [192]:
        custom_pdf = False

    if not custom_pdf:
        template = {
            "customTemplate": False,
            "orderId": quote_id,  # field1
            "customerName": "{{field5}}",  # field5
            "customerInformation": [
                {
                    "label": "Billing Address",  # field22 field20
                    "value": "{{field20}}",  # field20
                },
                {
                    "label": "Contact Name",
                    "value": "{{field7}}",  # field7
                },
                {
                    "label": "Contact Email",
                    "value": "{{field52}}",  # field8
                },
            ],
            "subscriptionDetails": [
                {
                    "label": "Subscription Start Date",
                    "value": "{{start_date}}",
                },
                {
                    "label": "Subscription Term",
                    "value": "{{duration_label}}",
                },
                {
                    "label": "Subscription End Date",
                    "value": "{{end_date}}",
                },
            ],
            "paymentTerms": [
                {
                    "label": "Billing Cycle",
                    "value": "{{field30}}",  # field30
                },
                {
                    "label": "Payment Method",  # field31
                    "value": "{{field31}}",
                },
                {
                    "label": "Other Terms",
                    "value": "First Invoice to be raised on signing the contract",
                },
            ],
            "otherTerms": [],
            "otherData": {
                "autoRenewal": "{{field37}}",
                "numberOfConnectors": "{{field25}}",
                "connectorNames": "{{field26}}",
                "termsOfUse": "{{field33}}",
                "connectorsWording": "{{field35}}",
                "creditTerms": "{{field32}}",
                "manualAddress": "{{field22}}",
                "dpaWording": "{{field51}}",
                "changeAddress": "{{field21}}",
                "newAddress": "{{field22}}",
                "originalAddress": "{{field20}}",
                "support": "{{field23}}",
            },
            "tableData": {},
        }
    else:
        template = {
            "customTemplate": True,
            "orderId": quote_id,
            "previewMode": True,
            "basicData": {
                "orderId": quote_id,
                "validTill": "{{field11}}",
                "createdBy": created_by_name,
                "createdFor": {
                    "name": "{{field7}}",
                    "email": "{{field10}}",
                },
                "supplierAddress": {
                    "name": company_name,
                    "address": company_display_address,
                },
                "customerAddress": {
                    "name": "{{field5}}",
                    "address": "{{field20}}",
                },
                "subscriptionTerm": "{{duration_label}}",
                "startDate": "{{start_date}}",
                "endDate": "{{end_date}}",
                "currency": "{{currency}}",
                "quoteTotal": "{{quote_total}}",
            },
            "theme": theme,
            "tableData": [],
            "signatureData": signature_data,
        }

    return template
