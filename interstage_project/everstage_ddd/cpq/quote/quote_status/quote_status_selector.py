import logging
from datetime import datetime
from typing import List
from uuid import UUID

from django.db.models import Count
from django.db.models.query import QuerySet
from django.utils import timezone

from common.data_selectors.bi_temporal_selector import BiTemporalSelector

from .data_models import QuoteStatusDataModel
from .enums import QuoteStatusEnum
from .models import QuoteStatus

logger = logging.getLogger(__name__)


class QuoteStatusSelector(BiTemporalSelector):
    def __init__(self, client_id):
        self.model = QuoteStatus
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    # WRITE OPERATIONS

    def persist_approval_template(self, data):
        return data.save()

    def _update_record(self, query_set: QuerySet[QuoteStatus], data):
        return query_set.update(**data)

    def _bulk_create(self, quote_status: List[QuoteStatus]):
        # Do not use this function directly for any write.
        QuoteStatus.objects.bulk_create(quote_status, batch_size=1000)

    def _create_objects(
        self, records: List[QuoteStatus] | List[dict] | QuoteStatus | dict
    ):
        """
        Bulk create object when passes as list of QuoteStatus objects or dict.
        This method should not be directly called from services.

        Parameters:
            -  records (List[QuoteStatus])

        """
        quote_status_records = []

        def prepare_record(record):
            if isinstance(record, dict):
                record = QuoteStatus(**record)
            record.pk = None
            return record

        if isinstance(records, list):
            quote_status_records = [prepare_record(record) for record in records]
        else:
            quote_status_records.append(prepare_record(records))
        try:
            return self._bulk_create(quote_status_records)
        except Exception:
            logger.exception("Quote Status BULK INSERT EXP")
            raise

    def invalidate_quote(self, query_set: QuerySet[QuoteStatus], ked=None):
        ked = ked if ked else timezone.now()
        return self._update_record(query_set, {"knowledge_end_date": ked})

    def update_quote_status(self, quote_id, new_status, kd=None):
        knowledge_date = kd if kd else timezone.now()
        quote_status = self.get_quote_status(quote_id)
        return self.bitemporal_update(
            record_identifier=quote_status.pk,
            data={"status": new_status},
            invalidation_date=knowledge_date,
        )

    def create_quote_status(
        self,
        data: QuoteStatusDataModel,
    ):
        """
        Create a new quote status
        """
        return self.bitemporal_create(data=data.model_dump())

    # READ OPERATIONS
    def does_record_exist_after_date(self, date: datetime):
        return self.client_aware().filter(knowledge_begin_date__gte=date).exists()

    def get_quote_status(
        self, quote_id, projection=None, point_in_time=None, as_query_set=False
    ):
        query = self.client_kd_deleted_aware(point_in_time).filter(quote_id=quote_id)
        if as_query_set:
            return query
        if not query.exists():
            return None
        if projection:
            return query.values(*projection).first()
        return query.first()

    def get_quote_ids_by_status(self, status):

        if isinstance(status, str):  # If it's a single string
            status = [status]  # Convert to a list

        return (
            self.client_kd_deleted_aware()
            .filter(status__in=status)
            .values_list("quote_id", flat=True)
        )

    def delete_quote_status(self, quote_id):
        self.client_kd_deleted_aware().filter(quote_id=quote_id).delete()

    def get_quote_statuses(self, quote_ids: list[UUID]) -> list[dict]:
        return list(self.client_kd_aware().filter(quote_id__in=quote_ids).values())

    def get_quote_status_count(self, quote_ids):
        """
        Get the count of quotes for each status for the current client.
        Returns:
            dict: A dictionary with status as keys and their respective counts as values.
        """
        qs = self.client_kd_deleted_aware()
        if quote_ids:
            qs = qs.filter(quote_id__in=quote_ids)
        return list(
            qs.values("status").annotate(count=Count("status")).order_by("status")
        )

    def get_latest_approved_status_for_quote(self, quote_id):
        return (
            self.client_aware()
            .filter(quote_id=quote_id, status=QuoteStatusEnum.APPROVED.value)
            .order_by("-knowledge_begin_date")
            .first()
        )
