from django.db import transaction

from .enums import QuoteStatusEnum
from .exceptions import InvalidActionError
from .quote_status_context import QuoteStatusContext
from .quote_status_selector import QuoteStatusSelector


@transaction.atomic
def change_quote_status(client_id, quote_id, status):

    quote_status_context = QuoteStatusContext(client_id, quote_id)
    status_params = {}

    if status == QuoteStatusEnum.WON.value:
        status_params = {"customer_response": "won"}
    elif status == QuoteStatusEnum.LOST.value:
        status_params = {"customer_response": "lost"}
    elif status == QuoteStatusEnum.CANCELLED.value:
        status_params = {"cancel_quote": True}
    elif status == QuoteStatusEnum.PENDING_SIGNATURE.value:
        status_params = {"customer_response": "pending_signature"}
    else:
        raise InvalidActionError(status)

    quote_status_context.process(**status_params)


def get_latest_approved_status_for_quote(client_id, quote_id):
    quote_status_data = QuoteStatusSelector(
        client_id
    ).get_latest_approved_status_for_quote(quote_id)
    return quote_status_data


def retract_quote_to_approved(client_id, quote_id):
    QuoteStatusSelector(client_id).update_quote_status(
        quote_id, QuoteStatusEnum.APPROVED.value
    )
    return {"status": "SUCCESS"}
