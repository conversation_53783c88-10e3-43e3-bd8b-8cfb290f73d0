from datetime import datetime
from uuid import UUID

from django.db.models import Count, Q
from django.db.models.query import QuerySet
from django.db.transaction import atomic
from django.utils import timezone

from ..models.qli_models import QuoteLineItem
from ..models.quote_models import QuoteSnapshot


class QuoteLineItemAccessor:
    def __init__(self, client_id: int) -> None:
        self.client_id = str(client_id)

    def does_record_exist_after_date(self, date: datetime):
        return self.client_aware().filter(knowledge_begin_date__gte=date).exists()

    def client_aware(self):
        return QuoteLineItem.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            knowledge_end_date__isnull=True, is_deleted=False
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def get_line_items_for_quote(self, quote_id, kd=None, as_dicts=False):
        qs = self.client_kd_aware()
        if kd:
            qs = self.client_kd_deleted_aware(kd)
        qs = qs.filter(quote_id=quote_id)
        if as_dicts:
            return list(qs.values())
        return list(qs)

    def get_items_after_kd(self, quote_id, kd):
        qs = self.client_aware().filter(
            Q(knowledge_begin_date__gt=kd, quote_id=quote_id)
            | Q(knowledge_end_date__gt=kd, quote_id=quote_id)
        )
        return list(qs)

    def get_quote_line_item_at_kd(self, quote_id, kd, line_item_ids):
        qs = self.client_kd_deleted_aware(kd).filter(
            quote_id=quote_id, quote_line_item_id__in=line_item_ids
        )
        return list(qs)

    def get_line_items_for_phase(self, quote_id, phase_id, as_dicts=False):
        qs = self.client_kd_aware().filter(quote_id=quote_id, phase_id=phase_id)
        if as_dicts:
            return list(qs.values())
        return list(qs)

    @atomic
    def bitemporal_delete(self, line_items: QuerySet[QuoteLineItem]) -> None:
        knowledge_date = timezone.now()
        deleted_line_items = line_items.all()

        for item in deleted_line_items:
            item.pk = None
            item.knowledge_begin_date = knowledge_date
            item.knowledge_end_date = None
            item.is_deleted = True

        line_items.update(knowledge_end_date=knowledge_date)
        QuoteLineItem.objects.bulk_create(deleted_line_items, batch_size=1000)

    def insert_object(self, quote_line_item_data: list[dict]):
        kd = None
        quote_id = None
        if quote_line_item_data:
            quote_id = quote_line_item_data[0].quote_id
            kd = quote_line_item_data[0].knowledge_begin_date

        kd = kd if kd else timezone.now()
        # quote_id = quote_line_item_data[0].get("quote_id")
        existing_qs = self.client_kd_aware().filter(quote_id=quote_id)
        if existing_qs.exists():
            existing_qs.update(knowledge_end_date=kd)
        objs = []
        for obj in quote_line_item_data:
            obj.knowledge_begin_date = kd
            obj.client_id = self.client_id
            objs.append(obj)

        QuoteLineItem.objects.bulk_create(objs, batch_size=1000)
        return True

    def insert_object_from_rule(self, quote_line_item_data):
        st = timezone.now()
        phase_id_sku_dict = {}
        for obj in quote_line_item_data:
            phase_id = obj.get("phase_id")
            if phase_id not in phase_id_sku_dict:
                phase_id_sku_dict[phase_id] = []
            phase_id_sku_dict[phase_id].append(obj.get("sku"))
        quote_id = quote_line_item_data[0].get("quote_id")
        objs = []
        for phase_id, new_skus in phase_id_sku_dict.items():
            existing_qs = self.client_kd_aware().filter(
                quote_id=quote_id, phase_id=phase_id, sku__in=new_skus
            )
            # don't change the sku that already exists
            if existing_qs.exists():
                existing_skus = existing_qs.values_list("sku", flat=True)
                # new_skus = list(set(new_skus) - set(existing_skus))
                self.client_kd_aware().filter(
                    quote_id=quote_id, phase_id=phase_id, sku__in=existing_skus
                ).update(knowledge_end_date=st)

            for obj in quote_line_item_data:
                if obj.get("sku") in new_skus:
                    obj["knowledge_begin_date"] = st
                    obj["client_id"] = self.client_id
                    objs.append(QuoteLineItem(**obj))

        QuoteLineItem.objects.bulk_create(objs, batch_size=1000)
        return True

    def invalidate_by_temporal_ids(self, quote_id, temporal_ids):
        st = timezone.now()
        self.client_kd_aware().filter(
            quote_id=quote_id, temporal_id__in=temporal_ids
        ).update(knowledge_end_date=st, is_deleted=True)
        return True

    def remove_sku_from_quote(self, quote_id, sku):
        st = timezone.now()
        self.client_kd_aware().filter(quote_id=quote_id, sku__in=sku).update(
            knowledge_end_date=st
        )
        return True

    def update_quote_line_items(self, quote_id, quote_line_item_data):
        st = timezone.now()
        existing_qs = self.client_kd_aware().filter(quote_id=quote_id)
        quote_line_item_ids = [
            obj.get("quote_line_item_id") for obj in quote_line_item_data
        ]
        if quote_line_item_ids:
            existing_qs.filter(quote_line_item_id__in=quote_line_item_ids).update(
                knowledge_end_date=st
            )
        objs = []
        for obj in quote_line_item_data:
            obj["knowledge_begin_date"] = st
            obj["client_id"] = self.client_id
            objs.append(QuoteLineItem(**obj))

        QuoteLineItem.objects.bulk_create(objs, batch_size=1000)
        return True

    def change_phase_name(self, quote_id, phase_id, phase_name):
        st = timezone.now()
        qs = self.client_kd_aware().filter(quote_id=quote_id, phase_id=phase_id)
        if qs.exists():
            new_data_copy = list(qs.values())
            for nd in new_data_copy:
                nd["temporal_id"] = None
                nd["phase_name"] = phase_name
                nd["knowledge_begin_date"] = st
            qs.update(knowledge_end_date=st)
            QuoteLineItem.objects.bulk_create(
                [QuoteLineItem(**nd) for nd in new_data_copy], batch_size=1000
            )
        return True

    def invalidate_by_phase_ids(self, quote_id, phase_ids):
        line_items = self.client_kd_aware().filter(
            quote_id=quote_id, phase_id__in=phase_ids
        )
        self.bitemporal_delete(line_items)
        return True

    def invalidate_by_quote_line_item_id(self, quote_id, line_item_ids):
        line_items = self.client_kd_aware().filter(
            quote_id=quote_id, quote_line_item_id__in=line_item_ids
        )
        self.bitemporal_delete(line_items)
        return True

    def delete_qli_created_after_kd(self, quote_id, kd, line_item_ids):
        st = timezone.now()
        self.client_kd_aware().filter(
            knowledge_begin_date__gt=kd,
            quote_id=quote_id,
            quote_line_item_id__in=line_item_ids,
        ).update(knowledge_end_date=st, is_deleted=True)
        return True

    def remove_ked_for_valid_qli(self, quote_id, kd, line_item_ids):
        self.client_kd_deleted_aware(kd).filter(
            quote_id=quote_id, quote_line_item_id__in=line_item_ids
        ).update(knowledge_end_date=None)

    def delete_quote_line_items(self, quote_id):
        line_items = self.client_kd_aware().filter(quote_id=quote_id)
        self.bitemporal_delete(line_items)
        return True

    def does_quote_have_line_items(self, quote_id):
        return self.client_kd_aware().filter(quote_id=quote_id).exists()

    def check_qli_without_pricepoints(self, quote_id):
        return (
            self.client_kd_aware()
            .filter(quote_id=quote_id)
            .filter(Q(pricepoint_data=None) | Q(pricepoint_data={}))
            .exists()
        )

    def get_distinct_skus_count(self):
        return (
            self.client_kd_aware()
            .values("quote_id")
            .annotate(count=Count("sku", distinct=True))
        )

    def get_quote_line_items(self, quote_kd_map: dict[UUID, datetime]) -> list[dict]:
        q = Q()
        for quote_id, kd in quote_kd_map.items():
            q |= Q(quote_id=quote_id, knowledge_begin_date__lte=kd) & (
                Q(knowledge_end_date__gt=kd) | Q(knowledge_end_date__isnull=True)
            )
        return list(self.client_aware().filter(is_deleted=False).filter(q).values())

    def get_filtered_quote_line_items(
        self, quote_id: UUID, sku: str | list | None = None, kd: datetime | None = None
    ) -> list[dict]:
        kd = kd or timezone.now()
        items = self.client_kd_deleted_aware(kd).filter(quote_id=quote_id)
        if sku is not None:
            if not isinstance(sku, list):
                items = items.filter(sku=sku)
            else:
                items = items.filter(sku__in=sku)
        return list(items.values())

    def check_if_product_exists(self, product_id):
        return self.client_kd_aware().filter(sku=product_id).exists()

    def update_quote_line_item_data_by_temporal_id(
        self, temporal_id, quote_line_item_data
    ):
        self.client_kd_aware().filter(temporal_id=temporal_id).update(
            **quote_line_item_data
        )

    def get_line_items_for_quote_by_sku(self, quote_id, sku, as_dicts=False):
        qs = self.client_kd_aware()
        qs = qs.filter(quote_id=quote_id, sku=sku)
        if as_dicts:
            return list(qs.values())
        return list(qs)


class QuoteSnapshotAccessor:
    def __init__(self, client_id: str) -> None:
        self.client_id = str(client_id)

    def client_aware(self):
        return QuoteSnapshot.objects.filter(client_id=self.client_id)

    def get_quote_snapshot(self, quote_id: UUID):
        return self.client_aware().get(client_id=self.client_id, quote_id=quote_id)

    def create_quote_snapshot(self, data: dict):
        return QuoteSnapshot.objects.create(client_id=self.client_id, **data)

    def get_quote_snapshot_by_quote_id_and_time(self, quote_id: str, time: datetime):
        qs = self.client_aware().filter(quote_id=quote_id, snapshot_time__lte=time)
        return qs.order_by("-snapshot_time").first()
