"""
This module contains the router and API endpoints for managing price books,
price factors, and price book products.
"""

import base64
import uuid
from uuid import UUID

from django.db import transaction
from ninja import Router
from ninja.decorators import decorate_view

from commission_engine.utils.general_data import RbacPermissions
from crystal.utils.crystal_utils import (
    handle_crystal_exception,
    has_view_crystal_permission,
)
from everstage_ddd.cpq.approvals import get_updated_form_spec_and_data
from everstage_ddd.cpq.approvals.service.approvals_service import (
    get_all_quotes_waiting_on_you,
)
from everstage_ddd.cpq.quote.schema import (
    AccountDetailsResponse,
    ChangeQuoteStatusPayload,
    ChangeQuoteStatusResponse,
    ExitQuotePayload,
    ExitQuoteResponse,
    PendingQuoteCountResponse,
    QuoteCreateOrClonePayload,
    QuoteDeletePayload,
    QuoteDeleteResponse,
    QuoteDetailsResponse,
    QuoteFilterPayload,
    QuoteFilterResponse,
    QuoteListFilterRequest,
    QuoteListResponse,
    QuoteMarkPrimaryPayload,
    QuoteMarkPrimaryResponse,
    QuotePayeeSimulatedCommissionPayload,
    QuotePayeeSimulatedCommissionResponse,
    QuoteSearchPayload,
    QuoteSearchResponse,
    QuoteTermsResponse,
    QuoteTermsUpdatePayload,
    QuoteTermsUpdateResponse,
    RetractQuoteResponse,
)
from everstage_ddd.cpq.quote.service.quote_form_service import (
    clone_quote,
    create_quote,
    delete_quote,
    exit_quote,
    filter_quotes,
    get_quote_details_paginated,
    get_quote_terms,
    list_quotes,
    mark_quote_as_primary,
    quote_status_count,
    search_quotes,
    update_terms,
)
from everstage_ddd.cpq.quote.types import (
    QuoteCloneRequest,
    QuoteCreateRequest,
    QuoteDeleteRequest,
    QuoteFilterRequest,
    QuoteListRequest,
    QuoteMarkPrimaryRequest,
    QuoteSearchRequest,
)
from interstage_project.auth_utils import requires_scope

from ..quote_status import change_quote_status
from ..quote_status.quote_status_service import retract_quote_to_approved
from ..schema import (
    GetQuoteDetailsResponse,
    GetQuoteResponse,
    PublishQuotePayload,
    PublishQuoteResponse,
    ValidateAndPublishQuotePayload,
    ValidateAndPublishQuoteResponse,
)
from ..service.quote_crystal_service import generate_payee_quote_simulation
from ..service.quote_service import (
    get_quote_details,
    publish_quote,
    validate_quote_for_approval_instance,
)

quote_router = Router(tags=["quotes"])


@quote_router.post("/list", response=QuoteListResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def list_quotes_api(request, payload: QuoteListFilterRequest) -> QuoteListResponse:
    """
    List all quotes for a client.

    Args:
        request: The HTTP request object.

    Returns:
        QuoteListResponse: The response containing a list of quotes.
    """
    client_id = request.client_id
    logged_in_user = request.user.username
    limit = payload.limit_value
    offset = payload.offset_value
    search_term = getattr(payload, "search_term", None)
    owner_id = getattr(payload, "owner_id", None)
    account_id = getattr(payload, "account_id", None)
    status = getattr(payload, "status", None)
    approvals_waiting_on_you = getattr(payload, "approvals_waiting_on_you", False)
    list_request = QuoteListRequest(
        client_id=client_id,
        limit_value=limit,
        offset_value=offset,
        search_term=search_term,
        owner_id=owner_id,
        account_id=account_id,
        status=status,
        approvals_waiting_on_you=approvals_waiting_on_you,
    )
    return list_quotes(list_request=list_request, logged_in_user=logged_in_user)


@quote_router.get("/pending-approval-count", response=PendingQuoteCountResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def pending_approval_count_api(request) -> PendingQuoteCountResponse:
    """
    Get the count of pending approvals for a client.
    """
    client_id = request.client_id
    logged_in_user = request.user.username
    return PendingQuoteCountResponse(
        count=len(get_all_quotes_waiting_on_you(client_id, logged_in_user))
    )


@quote_router.get("/account-details", response=AccountDetailsResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def quote_status_count_api(request) -> AccountDetailsResponse:
    """
    Get the count of quotes for each status, user and account details
    Args:
        request: The HTTP request object.
    Returns:
        AccountDetailsResponse: The response containing all the details
    """
    client_id = request.client_id
    logged_in_user = request.user.username
    return quote_status_count(client_id, logged_in_user)


@quote_router.get("/quote-details", response=QuoteDetailsResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def quote_details_api(request) -> QuoteDetailsResponse:
    """
    Get the paginated quote details
    Args:
        request: The HTTP request object.
    Returns:
        QuoteDetailsResponse: The response containing all the details
    """
    client_id = request.client_id
    limit = request.GET.get("limit_value")
    offset = request.GET.get("offset_value")
    search_term = request.GET.get("search_term", None)
    logged_in_user = request.user.username
    return get_quote_details_paginated(
        client_id, limit, offset, search_term, logged_in_user
    )


@quote_router.patch("/mark-primary", response=QuoteMarkPrimaryResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.EDIT_QUOTES.value, RbacPermissions.CREATE_QUOTES.value]
    )
)
@transaction.atomic
def mark_quote_as_primary_api(
    request, payload: QuoteMarkPrimaryPayload
) -> QuoteMarkPrimaryResponse:
    """
    Mark a quote as primary by setting the is_primary flag to True.

    Args:
        request: The HTTP request object.
        payload: The payload containing the quote ID.

    Returns:
        QuoteMarkPrimaryResponse: The response containing the marked quote.
    """
    client_id = request.client_id
    quote_id = payload.quote_id
    mark_primary_request = QuoteMarkPrimaryRequest(
        client_id=client_id, quote_id=quote_id
    )
    return mark_quote_as_primary(mark_primary_request)


@quote_router.get("/search", response=QuoteSearchResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def search_quotes_api(request, payload: QuoteSearchPayload) -> QuoteSearchResponse:
    """
    Search quotes by name or ID.

    Args:
        request: The HTTP request object.
        payload: The payload containing the search term.

    Returns:
        QuoteSearchResponse: The response containing a list of quotes.
    """
    client_id = request.client_id
    search_term = payload.search_term
    search_request = QuoteSearchRequest(client_id=client_id, search_term=search_term)
    return search_quotes(search_request)


@quote_router.get("/filter", response=QuoteFilterResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def filter_quotes_api(request, payload: QuoteFilterPayload) -> QuoteFilterResponse:
    """
    Filter quotes by name or ID.

    Args:
        request: The HTTP request object.
        payload: The payload containing the filter criteria.
    """
    client_id = request.client_id
    status = payload.status
    created_by = payload.created_by
    opportunity_id = payload.opportunity_id
    filter_request = QuoteFilterRequest(
        client_id=client_id,
        status=status,
        created_by=created_by,
        opportunity_id=opportunity_id,
    )
    return filter_quotes(filter_request)


@quote_router.post("/delete", response=QuoteDeleteResponse)
@decorate_view(requires_scope(RbacPermissions.DELETE_QUOTES.value))
@transaction.atomic
def delete_quote_api(request, payload: QuoteDeletePayload) -> QuoteDeleteResponse:
    """
    Delete a quote by ID.

    Args:
        request: The HTTP request object.
        payload: The payload containing the quote ID.
    """
    client_id = request.client_id
    quote_id = payload.quote_id
    logged_in_user = request.user.username
    delete_request = QuoteDeleteRequest(client_id=client_id, quote_id=quote_id)
    return delete_quote(delete_request, logged_in_user)


@quote_router.post("/create", response=GetQuoteResponse)
@decorate_view(requires_scope(RbacPermissions.CREATE_QUOTES.value))
@transaction.atomic
def create_or_clone_quote_api(
    request, payload: QuoteCreateOrClonePayload
) -> GetQuoteResponse:
    """
    Create a new quote or clone an existing one based on the request type.

    Args:
        request: The HTTP request object.
        payload: The payload containing the create or clone request details.

    Returns:
        QuoteCreateResponse: The response containing the created or cloned quote details.
    """
    client_id = request.client_id
    created_by = request.user.username
    form_builder_id = payload.form_builder_id or uuid.uuid4()  # TODO: Remove uuid here

    if payload.request_type == "new":
        create_request = QuoteCreateRequest(
            client_id=client_id,
            form_builder_id=form_builder_id,
            created_by=created_by,
            additional_details=payload.additional_details,
        )
        response = create_quote(create_request)
        return response
    elif payload.request_type == "clone":
        if payload.quote_id is None:
            raise ValueError("quote_id is required for cloning")  # noqa: TRY003
        clone_request = QuoteCloneRequest(
            client_id=client_id,
            quote_id=payload.quote_id,
            created_by=created_by,
            additional_details=payload.additional_details,
        )
        response = clone_quote(clone_request)
        return response
    else:
        raise ValueError(  # noqa: TRY003
            "Invalid request_type. Must be 'new' or 'clone'."
        )


@quote_router.get("/details/{quote_id}", response=GetQuoteDetailsResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_quote(request, quote_id: str) -> GetQuoteDetailsResponse:
    """
    Get a quote by its ID.

    Args:
        request: The HTTP request object.
        price_book_id: The ID of the price book to retrieve.

    Returns:
        GetQuoteDetailsResponse: The response containing a quote.
    """
    client_id = request.client_id
    response = get_quote_details(client_id, quote_id)
    return GetQuoteDetailsResponse(**response)


@quote_router.post("/publish", response=PublishQuoteResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def publish_quote_for_approval(
    request, payload: PublishQuotePayload
) -> PublishQuoteResponse:
    """
    Publish a quote by its ID.
    """
    client_id = request.client_id
    logged_in_user = request.user.username
    additional_details = request.audit
    comments = payload.comments

    updated_time, errors = publish_quote(
        client_id,
        payload.quote_id,
        payload.form_data,
        payload.form_spec,
        logged_in_user=logged_in_user,
        additional_details=additional_details,
        comments=comments,
        pdf_data=payload.pdf_data,
    )
    status = "success" if not errors else "error"
    return PublishQuoteResponse(
        status=status, last_updated_time=updated_time, errors=errors
    )


@quote_router.post("/status/change-status", response=ChangeQuoteStatusResponse)
@decorate_view(requires_scope(RbacPermissions.EDIT_QUOTES.value))
@transaction.atomic
def change_quote_status_api(
    request, payload: ChangeQuoteStatusPayload
) -> ChangeQuoteStatusResponse:
    """
    Change the status of a quote.
    """
    client_id = request.client_id
    change_quote_status(client_id, payload.quote_id, payload.status)
    return ChangeQuoteStatusResponse(status="SUCCESS")


@quote_router.post("/validate-and-publish", response=ValidateAndPublishQuoteResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def validate_quote_for_approval(
    request, payload: ValidateAndPublishQuotePayload
) -> ValidateAndPublishQuoteResponse:
    from everstage_ddd.cpq.forms.service.form_service import prepare_form_spec

    """
    Validate a quote by its ID.
    """
    client_id = request.client_id
    logged_in_user = request.user.username
    additional_details = request.audit

    response = validate_quote_for_approval_instance(
        client_id, logged_in_user, **payload.dict()
    )

    approval_cycles = response.get("approval_cycles", [])

    if not approval_cycles:
        form_spec = payload.form_spec
        form_data = payload.form_data
        updated_form_spec = prepare_form_spec(form_data, form_spec)
        if "fields_df" in response:
            form_spec, form_data = get_updated_form_spec_and_data(
                client_id,
                updated_form_spec,
                payload.form_builder_id,
                response["fields_df"],
            )
        updated_time, errors = publish_quote(
            client_id,
            payload.quote_id,
            form_data,
            updated_form_spec,
            logged_in_user=logged_in_user,
            additional_details=additional_details,
            pdf_data=payload.pdf_data,
        )
        status = "success" if not errors else "error"
        return ValidateAndPublishQuoteResponse(
            status=status,
            last_updated_time=updated_time,
            errors=errors,
        )

    return ValidateAndPublishQuoteResponse(
        status="success",
        approval_cycles=approval_cycles,
        last_updated_time=None,
    )


@quote_router.post("/exit", response=ExitQuoteResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def exit_quote_api(request, payload: ExitQuotePayload) -> ExitQuoteResponse:
    """save form_data in auto save form table and populate quote total in quote table"""
    client_id = request.client_id
    quote_id = payload.quote_id
    form_id = payload.form_id
    form_builder_id = payload.form_builder_id
    form_data = payload.form_data
    res = exit_quote(client_id, quote_id, form_id, form_builder_id, form_data)
    return ExitQuoteResponse(**res)


@quote_router.post(
    "/payee-simulated-commission", response=QuotePayeeSimulatedCommissionResponse
)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@has_view_crystal_permission
@handle_crystal_exception
@transaction.atomic
def get_payee_simulated_commission(
    request, payload: QuotePayeeSimulatedCommissionPayload
) -> QuotePayeeSimulatedCommissionResponse:
    client_id = request.client_id
    payee_email = payload.payee_email
    opportunity_id = payload.opportunity_id
    deal_amount = payload.deal_amount
    simulated_data = generate_payee_quote_simulation(
        client_id, payee_email, opportunity_id, deal_amount
    )
    return QuotePayeeSimulatedCommissionResponse(**simulated_data)


@quote_router.post("/terms/{quote_id}", response=QuoteTermsUpdateResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.EDIT_QUOTES.value, RbacPermissions.VIEW_QUOTES.value]
    )
)
@transaction.atomic
def update_terms_api(
    request, payload: QuoteTermsUpdatePayload, quote_id: UUID
) -> QuoteTermsUpdateResponse:
    """update terms in quote table"""
    client_id = request.client_id
    terms = payload.terms
    term_list = [
        {
            **term.model_dump(),
            "value": base64.b64decode(term.value or "").decode("utf-8"),
        }
        for term in terms
    ]
    update_terms(client_id, quote_id, term_list)
    return QuoteTermsUpdateResponse(
        status="success", message="Terms updated successfully", terms=term_list
    )


@quote_router.get("/terms/{quote_id}", response=QuoteTermsResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_quote_terms_api(request, quote_id: UUID) -> QuoteTermsResponse:
    client_id = request.client_id
    res = get_quote_terms(client_id, quote_id)
    return QuoteTermsResponse(terms=res or [])


@quote_router.post("retract/{quote_id}", response=RetractQuoteResponse)
@decorate_view(requires_scope(RbacPermissions.EDIT_QUOTES.value))
@transaction.atomic
def retract_quote(request, quote_id: UUID) -> RetractQuoteResponse:
    client_id = request.client_id
    retract_quote_response = retract_quote_to_approved(
        client_id=client_id,
        quote_id=quote_id,
    )
    return RetractQuoteResponse(**retract_quote_response)
