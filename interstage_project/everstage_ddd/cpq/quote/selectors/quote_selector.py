"""
Selector for Quote objects.
"""

from datetime import datetime

from django.db.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Q, Value
from django.db.models.functions import Cast, Concat
from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_settings
from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.cpq.basic_settings.accessors.basic_settings_accessor import (
    BasicSettingsAccessor,
)
from everstage_ddd.cpq.quote.models.quote_models import Quote


class QuoteSelector(BiTemporalSelector):
    """
    Selector for Quote objects.
    """

    def __init__(self, client_id):
        """
        Initialize the QuoteSelector.

        Args:
            client_id (int): The ID of the client.
        """
        self.model = Quote
        self.client_settings = get_client_settings(client_id)
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def does_record_exist_after_date(self, date: datetime):
        return self.client_aware().filter(knowledge_begin_date__gte=date).exists()

    def get_quote_by_name(self, quote_name):
        """
        Get a quote by its name.

        Args:
            quote_name (str): The name of the quote.

        Returns:
            Quote: The quote object if found, otherwise None.
        """
        return self.client_kd_deleted_aware().filter(quote_name=quote_name).first()

    def quote_exists(self, quote_id):
        """
        Check if a quote exists.

        Args:
            quote_id (int): The ID of the quote.

        Returns:
            bool: True if the quote exists, False otherwise.
        """
        return self.client_kd_deleted_aware().filter(quote_id=quote_id).exists()

    def get_all_quote_names(self):
        """
        Get all quote names.

        Returns:
            list: A list of all quote names in lowercase.
        """
        all_quotes = list(self.client_kd_deleted_aware().values("quote_name"))
        return [quote["quote_name"].lower() for quote in all_quotes]

    def get_all_quotes(self):
        """
        Get all quotes.

        Returns:
            QuerySet: A QuerySet of all quotes.
        """
        return self.client_kd_deleted_aware().order_by("-knowledge_begin_date")

    def get_quotes_after_kd(self, quote_id, kd):
        qs = (
            self.client_aware()
            .filter(knowledge_end_date__isnull=True)
            .filter(knowledge_begin_date__gt=kd, quote_id=quote_id)
        )
        return list(qs)

    def delete_quotes_after_kd(self, quote_id, kd):
        st = timezone.now()
        self.client_kd_aware().filter(
            knowledge_begin_date__gt=kd, quote_id=quote_id
        ).update(knowledge_end_date=st, is_deleted=True)
        return True

    def remove_ked_for_valid_quote(self, quote_id, kd):
        self.client_kd_deleted_aware(kd).filter(quote_id=quote_id).update(
            knowledge_end_date=None
        )
        return True

    def update_quote(self, quote_id, data, knowledge_date=None):
        """
        Update a quote.

        Args:
            quote_id (int): The ID of the quote to update.
            data (dict): The data to update the quote with.

        Returns:
            Quote: The updated quote object.
        """
        record = self.get_quote_by_id(quote_id=quote_id)
        return self.bitemporal_update(
            record_identifier=record.pk, data=data, invalidation_date=knowledge_date
        )

    def delete_quote(self, quote_id, invalidation_date=None):
        """
        Delete a quote.

        Args:
            quote_id (int): The ID of the quote to delete.
            invalidation_date (datetime, optional): The date to invalidate the quote.
            Defaults to current time.

        Returns:
            Quote: The deleted quote object.
        """
        if not invalidation_date:
            invalidation_date = timezone.now()
        record = self.get_quote_by_id(quote_id=quote_id)
        return self.bitemporal_delete(
            record_identifier=record.pk, invalidation_date=invalidation_date
        )

    def get_quotes_by_status(self, status):
        """
        Get quotes by status.

        Args:
            status (str): The status of the quotes to retrieve.

        Returns:
            QuerySet: A QuerySet of quotes with the specified status.
        """
        return self.client_kd_deleted_aware().filter(status=status)

    def get_quotes_by_opportunity(self, opportunity_id):
        """
        Get quotes by opportunity ID.

        Args:
            opportunity_id (int): The ID of the opportunity.

        Returns:
            QuerySet: A QuerySet of quotes associated with the specified opportunity.
        """
        return self.client_kd_deleted_aware().filter(opportunity_id=opportunity_id)

    def create_quote(self, data):
        """
        Create a new quote.

        Args:
            data (dict): The data for creating the new quote.

        Returns:
            Quote: The newly created quote object.
        """
        return self.bitemporal_create(data=data)

    def bulk_quote_update(self, records):
        """
        Perform bulk update of quotes.

        Args:
            records (dict): A dictionary of quote IDs and their corresponding update data.
        """
        for quote_id, data in records.items():
            quote = self.client_kd_deleted_aware().get(quote_id=quote_id)
            self.bitemporal_update(record_identifier=quote.pk, data=data)

    def get_quote_count(self):
        """
        Get the total count of quotes.

        Returns:
            int: The total number of quotes.
        """
        return self.client_kd_deleted_aware().count()

    def get_quotes_by_created_by(self, created_by):
        """
        Get quotes created by a specific user.

        Args:
            created_by (str): The identifier of the user who created the quotes.

        Returns:
            QuerySet: A QuerySet of quotes created by the specified user.
        """
        return self.client_kd_deleted_aware().filter(created_by=created_by)

    def filter_quotes(self, query: Q):
        """
        Filter quotes using a Q object for flexible querying.

        Args:
            query (Q): The Q object that contains the filtering conditions.

        Returns:
            QuerySet: A QuerySet of filtered quotes.
        """
        return self.client_kd_deleted_aware().filter(query)

    def get_quote_by_id(
        self,
        quote_id,
        projection: list | None = None,
        point_in_time: datetime | None = None,
    ):
        query = self.client_kd_deleted_aware(point_in_time).filter(quote_id=quote_id)
        if projection:
            return query.values(*projection).first()
        quote = query.first()
        return quote

    def get_quote_for_quote_id_order_by_kd(self, quote_id, kd=None):
        qs = (
            self.client_kd_deleted_aware(kd)
            .filter(quote_id=quote_id)
            .order_by("-knowledge_begin_date")
        )
        return list(qs)

    def delete_quote_by_id_temporal_id(self, quote_id, temporal_ids):
        kd = timezone.now()
        self.client_kd_deleted_aware().filter(
            quote_id=quote_id, temporal_id__in=temporal_ids
        ).update(knowledge_end_date=kd, is_deleted=True)
        return True

    def search_quotes(self, search_term):
        """
        Search for quotes based on a search term.

        Args:
            search_term (str): The term to search for in quote names and IDs.

        Returns:
            QuerySet: A QuerySet of quotes that match the search criteria.
        """
        return self.client_kd_deleted_aware().filter(
            Q(quote_name__icontains=search_term) | Q(quote_id__icontains=search_term)
        )

    def get_primary_quote_for_opportunity(self, opportunity_id):
        """
        Retrieves the primary quote for a given opportunity.

        This method queries the database to find the primary quote associated
        with the specified opportunity ID. It filters the quotes by the opportunity ID
        and ensures the 'is_primary' flag is set to True.

        Args:
            opportunity_id (int): The ID of the opportunity.

        Returns:
            Quote: The primary quote if found, otherwise None.
        """
        quote = (
            self.client_kd_deleted_aware()
            .filter(is_primary=True, opportunity_id=opportunity_id)
            .last()
        )
        return quote

    def get_quote_display_id_start(self):
        """
        If no quote_display_id_start exist in DB then it return 1001 as a default value
        """
        client_settings = self.client_settings
        return (
            client_settings.get("cpq_settings", {}).get("quote_display_id_start")
            or 1001
        )

    def start_from_quote_display_id(self):
        client_settings = self.client_settings
        return client_settings.get("cpq_settings", {}).get(
            "start_from_quote_display_id", False
        )

    def get_max_quote_display_id_from_existing_quote(self):
        return self.client_kd_deleted_aware().aggregate(Max("quote_display_id"))[
            "quote_display_id__max"
        ]

    def get_max_quote_display_id(self):
        """
        Retrieves the maximum quote display ID from the database.

        This method queries the database to find the highest value of the 'quote_display_id' field
        among all quotes for the current client. It's used to determine the next available
        display ID when creating a new quote.

        Returns:
            int: The maximum quote display ID.
        """
        if self.start_from_quote_display_id():
            cpq_settings = self.client_settings.get("cpq_settings", {})
            cpq_settings["start_from_quote_display_id"] = False
            BasicSettingsAccessor(self.client_id).update_basic_settings_data(
                cpq_settings_data=cpq_settings
            )

            return self.get_quote_display_id_start() - 1

        else:
            max_display_id = self.get_max_quote_display_id_from_existing_quote()
            return (
                max_display_id
                if max_display_id is not None
                else (self.get_quote_display_id_start() - 1)
            )

    def get_quotes_by_quote_ids(self, quote_ids):
        """
        Get a quote by its ID.
        This method retrieves a quote from the database based on the provided quote ID.
        It uses the client_kd_deleted_aware() method to ensure the query is aware of
        the client and deleted status.
        Args:
            quote_id (int): The ID of the quote to retrieve.
        Returns:
            Quote: The quote object if found, otherwise None.
        """
        qs = (
            self.client_kd_deleted_aware()
            .filter(quote_id__in=quote_ids)
            .order_by("-knowledge_begin_date")
        )
        return qs

    def get_quotes_between(
        self, start_time: datetime, end_time: datetime
    ) -> list[dict]:
        return list(
            self.client_kd_deleted_aware()
            .filter(knowledge_begin_date__gt=start_time)
            .filter(knowledge_begin_date__lte=end_time)
            .values()
        )

    def get_filtered_quotes(
        self,
        limit=None,
        offset=None,
        **kwargs,
    ):

        search_term = kwargs.get("search_term", None)
        quote_ids = kwargs.get("quote_ids", None)
        account_id = kwargs.get("account_id", None)
        owner_id = kwargs.get("owner_id", None)
        valid_owners = kwargs.get("valid_owners", [])
        quote_prefix = kwargs.get("quote_prefix", None)
        any_filter_applied = kwargs.get("any_filter_applied", False)

        query = self.client_kd_deleted_aware().order_by("-created_at")
        if valid_owners:
            query = query.filter(owner_id__in=valid_owners)
        if search_term:
            query = query.annotate(
                combined_display_id=Concat(
                    Value(quote_prefix), Cast("quote_display_id", CharField())
                )
            ).filter(
                Q(combined_display_id__icontains=search_term)
                | Q(quote_name__icontains=search_term)
            )
        if any_filter_applied or quote_ids:
            query = query.filter(quote_id__in=quote_ids)
        if account_id:
            query = query.filter(account_id__in=account_id)
        if owner_id:
            query = query.filter(owner_id__in=owner_id)
        if limit is not None and offset is not None:
            query = query[offset : offset + limit]
        return list(query)

    def get_quote_ids_by_owner_id(self, owner_ids):
        return list(
            self.client_kd_deleted_aware()
            .filter(owner_id__in=owner_ids)
            .values_list("quote_id", flat=True)
        )
