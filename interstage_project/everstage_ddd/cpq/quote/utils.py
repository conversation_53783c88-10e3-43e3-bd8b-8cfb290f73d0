import json
import uuid
from datetime import datetime

from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from common.ever_table import EverTableColumn
from everstage_ddd.cpq.enums import DurationType


def format_currency(value, currency_symbol):
    if value != 0 and not value:
        value = "-"
    if value == "-":
        return value
    try:
        value = round(float(value), 2)
        if value.is_integer():
            return f"{currency_symbol}{value:,.0f}"
        return f"{currency_symbol}{value:,.2f}"
    except ValueError:
        return value


def get_table_variables(client_id, datasheet_id):
    ds_variables = DatasheetVariableAccessor(client_id).get_objects_by_datasheet_id(
        datasheet_id=datasheet_id,
        projection=[
            "system_name",
            "display_name",
            "data_type_id",
            "tags",
            "field_order",
        ],
    )
    pydantic_variables = [
        EverTableColumn(
            **{
                "system_name": variable["system_name"],
                "display_name": variable["display_name"],
                "display_order": variable["field_order"],
                "semantic_tag": (variable.get("tags", {}) or {}).get("semantic", ""),
                "marker_tags": (variable.get("tags", {}) or {}).get("marker", []),
                "is_within_variant": False,
                "variant_column_name": "data",
                "everstage_data_type_id": variable["data_type_id"],
            }
        )
        for variable in ds_variables
    ]
    return pydantic_variables


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle datetime and uuid objects."""

    def default(self, obj):
        """Serialize datetime and uuid objects."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, uuid.UUID):
            return str(obj)
        elif isinstance(obj, dict):
            return json.dumps(obj, sort_keys=True, cls=CustomJSONEncoder)
        return super().default(obj)


def _format_data_for_assert(data):
    """Format the data for assert."""
    serialized_data = json.dumps(data, sort_keys=True, cls=CustomJSONEncoder)
    return json.loads(serialized_data)


def get_duration_label(duration_type: str, duration_value: int | None) -> str:
    if duration_value is None or duration_type == DurationType.FOREVER.value:
        return "Open ended"
    return duration_type[:-1] if duration_value == 1 else duration_type
