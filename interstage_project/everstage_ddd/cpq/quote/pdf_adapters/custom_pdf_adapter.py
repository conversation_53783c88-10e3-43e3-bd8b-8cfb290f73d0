from collections import defaultdict

from ..utils import format_currency

freq_map = {
    "one-time": "",
    "daily": "d",
    "monthly": "mo",
    "quarterly": "qtr",
    "half-yearly": "hy",
    "annual": "yr",
}

primary_column = "PRODUCTS"

header_columns = [
    {
        "key": "units",
        "label": "Units",
    },
    {
        "key": "price",
        "label": "Unit Price",
    },
    {
        "key": "total",
        "label": "Total",
        "align": "right",
    },
]


def format_per_item(
    unit_price, flat_price, currency_symbol, billing_frequency_formatted
):
    if unit_price and unit_price != "-":
        if flat_price and flat_price != "-":
            formatted_list_price = (
                str(format_currency(unit_price, currency_symbol))
                + billing_frequency_formatted
                + "\n + "
                + str(format_currency(flat_price, currency_symbol))
                + billing_frequency_formatted
                + " flat"
            )
        else:
            formatted_list_price = (
                str(format_currency(unit_price, currency_symbol))
                + billing_frequency_formatted
            )
    elif flat_price and flat_price != "-":
        formatted_list_price = (
            str(format_currency(flat_price, currency_symbol))
            + billing_frequency_formatted
            + " flat"
        )
    else:
        formatted_list_price = str(format_currency(unit_price, currency_symbol))

    return formatted_list_price


def convert_to_table_format(  # noqa: PLR0912, PLR0915
    tables, phase_details, catalog_dict, currency_symbol, **kwargs
):
    show_charge_type_table = kwargs.get("show_charge_type_table", True)
    show_original_list_unit_price = kwargs.get("show_original_list_unit_price", False)
    table_data = []
    pricing_data = defaultdict(lambda: defaultdict(float))

    for table in tables:
        phase = table.get("phase_name", "Phase")

        has_tier = any(
            row.get("custom_data", {}).get("tier_allocations")
            for row in table.get("row_data", {})
            if row.get("custom_data", {})
        )

        phase_data = {
            "title": phase if len(tables) > 1 else "",
            "periodStart": (
                table.get("start_date", "").strftime("%b %d, %Y")
                if table.get("start_date", "")
                else ""
            ),
            "periodEnd": (
                table.get("end_date", "").strftime("%b %d, %Y")
                if table.get("end_date", "")
                else ""
            ),
            "primaryColumn": primary_column,
            "columns": header_columns,
            "items": [],
        }

        for row in table.get("row_data", []):
            tier_details = {}
            sku = row.get("sku", "")
            catalog_record = catalog_dict.get(sku, {})
            uom = catalog_record.get("uom", "") or ""
            custom_data = row.get("custom_data", {})
            pricepoint_data = row.get("pricepoint_data", {})
            billing_frequency = freq_map.get(
                pricepoint_data.get("billing_frequency", ""), ""
            )
            billing_frequency_formatted = (
                " /" + billing_frequency if billing_frequency else ""
            )
            # pricing_method = pricepoint_data.get("pricing_method", "")
            tier_details = (
                custom_data.get("tier_allocations", {}) if custom_data else {}
            )
            billing_type = catalog_record.get("billing_type", "-").lower()

            product_description = ""
            if pricepoint_data:
                tier_meta_data = pricepoint_data.get("tier_data", {})
                show_future_tiers = pricepoint_data.get("show_future_tiers", False)
                product_description = pricepoint_data.get("product_description", "")
                tier_details_dict = {
                    tier_details[tier].get("lower_bound", ""): tier_details[tier]
                    for tier in tier_details
                }
                if show_future_tiers:
                    for tier in tier_meta_data:
                        tier_id = tier.get("lower_bound", "")
                        if tier_id not in tier_details_dict:
                            tier["name"] = tier.get("tier_name", "")
                            tier["future"] = True
                            tier_details[tier_id] = tier

            product_name = row.get("product_name", "-")
            qty = str(row.get("quantity", "-"))
            list_price = row.get("list_unit_price", "-")
            # flat_list_price = row.get("list_flat_price", "-")
            approved_price = row.get("net_unit_price", "-")
            flat_approved_price = row.get("net_flat_price", "-")
            net_price = row.get("prorated_net_total", "-")
            pricing_data[phase][billing_type] += float(net_price) if net_price else 0

            formatted_list_price = format_currency(
                list_price,
                currency_symbol,
            )

            formatted_approved_price = format_per_item(
                approved_price,
                flat_approved_price,
                currency_symbol,
                billing_frequency_formatted,
            )

            formatted_net_price = format_currency(net_price, currency_symbol)

            row_data = [
                {
                    "units": {"sup": qty, "text": uom},
                    "price": {
                        "strike": (
                            formatted_list_price
                            if show_original_list_unit_price
                            and list_price != approved_price
                            else ""
                        ),
                        "text": formatted_approved_price,
                    },
                    "total": {"sup": formatted_net_price},
                },
            ]

            item_data = {
                "title": product_name,
                "description": product_description,
                "rows": row_data,
            }

            if has_tier:
                tier_data = []
                tier_details_values = list(tier_details.values())
                if tier_details_values:
                    tier_data.append({})
                for tier in tier_details_values:

                    future = tier.get("future", False)
                    sub_qty = str(tier.get("quantity", "-"))
                    sub_list_price = tier.get("list_unit_price", "-")
                    # sub_flat_list_price = tier.get("list_flat_price", "-")
                    sub_net_price = tier.get("net_unit_price", "-")
                    sub_flat_net_price = tier.get("net_flat_price", "-")
                    sub_total = tier.get("prorated_net_total", "0")

                    # tier_name = tier.get("name", "-")
                    upper_bound = tier.get("upper_bound", "-")
                    lower_bound = str(tier.get("lower_bound", "-"))
                    if (
                        not upper_bound
                        or upper_bound == "-"
                        or (
                            isinstance(upper_bound, int)
                            and upper_bound > 9999  # noqa: PLR2004
                        )
                    ):
                        upper_bound = ""

                    bound_str = lower_bound + (
                        " - " + str(upper_bound) if upper_bound else "+"
                    )

                    tier_data.append(
                        {
                            "units": {
                                "text": (
                                    sub_qty + " " + uom if not future else "Future"
                                ),
                                "sub": "(" + bound_str + ")",
                                "mark": "*" if future else "",
                            },
                            "price": {
                                "strike": (
                                    format_currency(
                                        sub_list_price,
                                        currency_symbol,
                                    )
                                    if show_original_list_unit_price
                                    and sub_list_price != sub_net_price
                                    else ""
                                ),
                                "text": format_per_item(
                                    sub_net_price,
                                    sub_flat_net_price,
                                    currency_symbol,
                                    billing_frequency_formatted,
                                ),
                            },
                            "total": {
                                "sup": (
                                    format_currency(sub_total, currency_symbol)
                                    if not future
                                    else "--(Not billed)"
                                )
                            },
                        }
                    )

                item_data["rows"].extend(tier_data)
            phase_data["items"].append(item_data)

        formatted_subtotal = format_currency(
            phase_details.get(table.get("phase_id", "-"), "-"), currency_symbol
        )
        phase_data["subtotal"] = formatted_subtotal
        table_data.append(phase_data)

    pricing_table = {
        "title": "CHARGE TYPE BREAKDOWN",
        "primaryColumn": "CHARGE TYPE",
        "columns": [],
        "items": [
            {
                "title": "One-time charges",
                "rows": [{}],
            },
            {
                "title": "Recurring charges",
                "rows": [{}],
            },
        ],
    }

    for phase in pricing_data:
        pricing_table["columns"].append(
            {
                "key": phase,
                "label": phase,
                "align": "right",
            }
        )

        formatted_recurring_price = format_currency(
            pricing_data[phase].get("recurring", 0), currency_symbol
        )
        formatted_one_time_price = format_currency(
            pricing_data[phase].get("one_time", 0), currency_symbol
        )

        pricing_table["items"][0]["rows"][0][phase] = formatted_one_time_price
        pricing_table["items"][1]["rows"][0][phase] = formatted_recurring_price

    if show_charge_type_table:
        table_data.append(pricing_table)

    return table_data


def transform_quote_data_for_pdf(
    quote_data, phase_details, catalog_dict, currency_symbol
):

    phases = quote_data.get("phases", [])
    table_data = convert_to_table_format(
        phases, phase_details, catalog_dict, currency_symbol
    )
    return table_data
