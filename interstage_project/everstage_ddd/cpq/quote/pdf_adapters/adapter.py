from commission_engine.accessors.client_accessor import get_client_settings

from .custom_pdf_adapter import (
    convert_to_table_format as custom_convert_to_table_format,
)
from .everstage_pdf_adapter import (
    convert_to_table_format as everstage_convert_to_table_format,
)


class PdfAdapter:
    def __init__(self, client_id):
        self.client_id = client_id

    def get_adapter(self):
        client_settings = get_client_settings(self.client_id)
        custom_pdf = client_settings.get("cpq_settings", {}).get("custom_pdf", True)
        if self.client_id in [192]:
            custom_pdf = False
        if not custom_pdf:
            return everstage_convert_to_table_format
        else:
            return custom_convert_to_table_format

    def transform_quote_data_for_pdf(
        self,
        quote_data,
        phase_details,
        catalog_dict,
        currency_symbol,
        **kwargs,
    ):
        show_charge_type_table = kwargs.get("show_charge_type_table", True)
        show_original_list_unit_price = kwargs.get(
            "show_original_list_unit_price", False
        )
        phases = quote_data.get("phases", [])

        table_data = self.get_adapter()(
            phases,
            phase_details,
            catalog_dict,
            currency_symbol,
            show_charge_type_table=show_charge_type_table,
            show_original_list_unit_price=show_original_list_unit_price,
        )
        return table_data
