import json
import logging
from copy import deepcopy

import pandas as pd
import pytz
from dateutil import tz
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from vectorized_evaluate import vectorized_evaluate

from everstage_ddd.cpq.approvals.approval_accessor import (
    ApprovalRuleAccessor,
    ApprovalRuleGroupAccessor,
)
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
)
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    APPROVAL_WORKFLOW_STATUS,
    ENTITY_KEY_DELIMETER,
)
from spm.services import audit_services
from spm.services.approval_workflow_services.approval_instance_service_utils import (
    get_approval_requests,
    get_approval_stage_instances,
    get_approval_template_stages,
    get_approval_wf_instances,
)
from spm.services.approval_workflow_services.approval_instance_services import (
    approve_request,
    create_approval_instance_and_stages,
    delete_approval_requests,
    get_all_employee_manager_map,
    get_resolved_employees,
)
from spm.services.team_services.membership_services import (
    get_manager_hierarchy_for_payee,
)

from .approvals_helper_service import quote_line_item_fields

logger = logging.getLogger(__name__)


def get_updated_form_spec_and_data(client_id, form_spec, form_builder_id, fields_df):
    from everstage_ddd.cpq.forms import FormModel, FormSpecNavigator

    logger.info("BEGIN: Get updated form spec and data")
    new_form_spec = deepcopy(form_spec)
    form_navigator = FormSpecNavigator(new_form_spec)

    approval_rules_by_form_id = ApprovalRuleAccessor(
        client_id
    ).get_rules_by_form_builder_id(form_builder_id)

    for rule in approval_rules_by_form_id:
        if rule.condition.get("is_qli_rule"):
            qli_rule_fields = rule.condition.get("qli_rule_fields")
            for qli_field in qli_rule_fields:
                if qli_field in form_navigator.field_section_map:
                    field = form_navigator.get_field(qli_field)
                    field["value"] = fields_df.iloc[0][qli_field]
    form_model = FormModel(**new_form_spec)
    form_data_df = form_model.get_data_df
    form_data = form_data_df.to_dict(orient="records")[0]
    logger.info("END: Get updated form spec and data")
    return new_form_spec, form_data


def evaluate_approval_rule(
    client_id: int,
    rule: dict,
    fields_df: pd.DataFrame,
    matrix_data: list[dict],
    qli_data: dict,
):
    """
    This function evaluates the condition for a rule

    Args:
        condition: Condition for a rule
        quote_fields: Quote fields

    Returns:
        bool: True if the condition is satisfied, False otherwise

    Example:
        condition = {
            "ast": [
                    {
                        token: {
                        key: "co_1_amount",
                        name: "amount",
                        data_type: "Integer",
                        system_name: "co_1_amount",
                        },
                        token_type: "FORM_VARIABLES",
                    },
                    { token: { key: ">=", name: ">=" }, token_type: "OPERATORS" },
                    {
                        token: {
                        key: 1000,
                        args: ["Integer", 1000],
                        name: "1000",
                        data_type: "Integer",
                        },
                        token_type: "CONSTANT_VARIABLES",
                    },
                ];
            }
        quote_fields = {"co_1_amount": 1000}
        evaluate_approval_rule(condition, quote_fields) -> True

    """
    from commission_engine.utils import create_ast
    from everstage_ddd.cpq.approvals.service.approvals_qli_services import (
        evaluate_qli_approval_rules,
    )

    logger.info("BEGIN: Evaluate approval rule")
    if "is_qli_rule" in rule and rule["is_qli_rule"]:
        res, fields_df = evaluate_qli_approval_rules(
            client_id, rule, fields_df, matrix_data, qli_data
        )
        return res, fields_df
    actions = rule["actions"]
    for action in actions:
        if "condition" in action:
            condition = action["condition"]
            try:
                ast = create_ast(condition)
                res = vectorized_evaluate(fields_df, ast["ast"], {})
                return res.any(), fields_df
            except Exception:
                logger.info("Error evaluating condition")
                return False, fields_df

    return False, fields_df


def evaluate_approval_rules_for_quote(
    client_id: int,
    approval_rules_by_form_id,
    fields_df: pd.DataFrame,
    quote_id: str,
    **kwargs,
):
    """
    This function evaluates the approval rules for a quote
    And returns the valid approval rule ids and stage template ids
    """

    from .approvals_qli_services import (
        get_approval_matrix_data,
        prepare_quote_line_item_data,
    )

    logger.info(
        "BEGIN: Evaluate approval rules for quote - client %s",
        client_id,
    )
    matrix_sheet_id = kwargs.get("matrix_sheet_id", "")
    matrix_book_id = kwargs.get("matrix_book_id", "")

    matrix_data = get_approval_matrix_data(client_id, matrix_sheet_id, matrix_book_id)
    qli_data = prepare_quote_line_item_data(client_id, quote_id)

    valid_approval_rule_ids = []
    stage_template_ids = []
    for rule in approval_rules_by_form_id:
        approval_rule_condition = deepcopy(rule.condition)
        logger.info(f"Evaluating {rule.rule_name}")
        is_rule_valid, fields_df = evaluate_approval_rule(
            client_id, approval_rule_condition, fields_df, matrix_data, qli_data
        )
        if approval_rule_condition and is_rule_valid:
            valid_approval_rule_ids.append(rule.rule_id)
            stage_template_ids.append(rule.stage_template_id)
    logger.info("Valid approval rule ids %s", valid_approval_rule_ids)
    logger.info(
        "END: Evaluate approval rules for quote - client %s",
        client_id,
    )
    return valid_approval_rule_ids, stage_template_ids, fields_df


def merge_approvers(approvers_dict_1: dict, approvers_dict_2: dict):
    """
    Given two dictionaries of approvers, this function merges them

    Args:
        approvers_dict_1: Dictionary of approvers
        approvers_dict_2: Dictionary of approvers

    Returns:
        merged_approvers: Merged dictionary of approvers

    Example:
        approvers_dict_1 = {"users": [], "groups": [], "dynamic": ["payee", "all_approvers"]}
        approvers_dict_2 = {"users": ["<EMAIL>"], "groups": [], "dynamic": []}
        merged_approvers = merge_approvers(approvers_dict_1, approvers_dict_2)
        merged_approvers = {"users": ["<EMAIL>"], "groups": [], "dynamic": ["payee", "all_approvers"]}
    """
    merged_approvers = {}
    merged_approvers = {
        key: list(set(approvers_dict_1.get(key, []) + approvers_dict_2.get(key, [])))
        for key in set(approvers_dict_1) | set(approvers_dict_2)
    }
    return merged_approvers


def get_logged_in_user_fields(
    client_id: int, logged_in_user: str, quote_owner: str | None
):
    """
    This function returns the fields of the logged in user

    Args:
        client_id: Client id
        logged_in_user: Logged in user

    Returns:
        dict: Fields of the logged in user

    Example:
        {
            "up_first_name": "User",
            "up_last_name": "Person",
            "up_email": "<EMAIL>",
            "up_designation": "Manager",
            "up_is_manager": True, # This will be true if the logged in user is a direct/indirect manager of the quote owner
        }
    """

    logger.info("BEGIN: Get logged in user fields")
    # Get logged in user fields
    user_details: list = EmployeeAccessor(client_id).get_employee_by_email_id(
        email_id=logged_in_user
    )
    user_data = {}
    if user_details:
        user_data["up_first_name"] = user_details[0].first_name
        user_data["up_last_name"] = user_details[0].last_name
        user_data["up_email"] = user_details[0].employee_email_id
    date = timezone.now()
    payroll_details = EmployeePayrollAccessor(client_id).get_employee_payroll_for_date(
        date, logged_in_user, projection=["designation"]
    )
    if payroll_details:
        user_data["up_designation"] = payroll_details["designation"]
    if quote_owner:
        manager_data = get_manager_hierarchy_for_payee(client_id, quote_owner, date)
        managers_list = [
            manager_data[payee]["reporting_manager_email_id"] for payee in manager_data
        ]
        user_data["up_is_manager"] = logged_in_user in managers_list
    else:
        user_data["up_is_manager"] = False
    logger.info("END: Get logged in user fields")
    return user_data


def get_all_fields_for_approvals(
    client_id: int,
    logged_in_user: str,
    quote_owner: str | None,
    quote_id: str,
    **kwargs: dict,
):
    """
    This function returns all the fields required for approvals
    """
    from everstage_ddd.cpq.quote import (
        get_data_for_rules,
        get_quote_properties,
        get_quote_summary,
    )

    form_data = kwargs.get("form_data", {})
    # Convert empty lists to None to avoid pandas DataFrame creation issues
    form_data = {k: None if v == [] else v for k, v in form_data.items()}
    quote_properties = get_quote_properties(client_id, quote_id)
    user_fields = get_logged_in_user_fields(client_id, logged_in_user, quote_owner)
    summary, _ = get_quote_summary(client_id, quote_id, use_kd=False)

    summary_fields_df = pd.json_normalize({"summary": summary}, sep=".")
    form_data_df = pd.DataFrame(form_data, index=[0])
    fields_df = form_data_df.assign(
        **user_fields, **summary_fields_df, **quote_properties
    )

    data = get_data_for_rules(client_id, quote_id)
    data_df = pd.json_normalize(data, sep=".")
    if not data_df.empty:
        fields_df = pd.merge(data_df, fields_df, how="cross")

    return fields_df


def get_valid_approval_rule_groups(approval_rule_groups, fields_df):
    from commission_engine.utils import create_ast

    valid_rule_group_ids = []
    for rule_group in approval_rule_groups:
        condition = rule_group.condition
        if condition and "rule" in condition:
            infix = condition.get("rule")
            if infix:
                try:
                    ast = create_ast(infix)
                    res = vectorized_evaluate(fields_df, ast["ast"], {})
                    if res.any():
                        valid_rule_group_ids.append(str(rule_group.rule_group_id))
                except Exception:
                    logger.info(
                        "Error evaluating approval rule group %s",
                        rule_group.rule_group_id,
                    )
        else:
            valid_rule_group_ids.append(str(rule_group.rule_group_id))
    return valid_rule_group_ids


def get_approval_cycle_for_quote(client_id: int, quote_owner: str, **kwargs):
    """
    This function gets approval cycle for a quote. A quote can have multiple approval cycles.
    Args:
        client_id: Client id
        form_builder_id: Form Builder id
        kwargs: Additional keyword arguments

    Returns:
        approval_cycle: List of approval cycles
        [
            {
                "template_name": "Approval Group 1",
                "template_description": "Approval Group Description 1",
                "entity_type": "quote",
                "notify_on_reject":{},
                "notify_on_approve":{},
                "total_stages": 2,
                "rule_group_id": "901678-1234-5678-9012",
                "stages": [
                    {
                        "actual_stage_order": 1,
                        "overidden_stage_order": 1,
                        "stage_template_id": "901678-123-5678-9011",
                        "stage_name": "Stage 1",
                        "approval_strategy": "all",
                        "due_period": 2,
                        "notes":"Approve before due date",
                        "approvers": [
                            "<EMAIL>",
                            "<EMAIL>
                        ]
                    },
                    {
                        "actual_stage_order": 2,
                        "overidden_stage_order": 2,
                        "stage_template_id": "901678-123-5678-9012",
                        "stage_name": "Stage 2",
                        "approval_strategy": "any",
                        "due_period": 0,
                        "notes":"",
                        "approvers": [
                            "<EMAIL>"
                        ]
                    }
                ]
            }
        ]
    """
    from commission_engine.accessors.client_accessor import get_client_settings

    form_builder_id = kwargs.get("form_builder_id")
    logger.info(
        "BEGIN: Get approval cycle for quote - form %s, client %s",
        form_builder_id,
        client_id,
    )

    quote_id = kwargs.get("quote_id")
    fields_df = kwargs.get("fields_df", {})
    approval_rule_groups = ApprovalRuleGroupAccessor(
        client_id
    ).get_rule_groups_by_form_builder_id(form_builder_id, status="active")
    valid_rule_group_ids = get_valid_approval_rule_groups(
        approval_rule_groups, fields_df
    )

    if not valid_rule_group_ids:
        logger.info(
            "No valid rule groups found for form %s, client %s",
            form_builder_id,
            client_id,
        )
        return {}

    approval_rules = ApprovalRuleAccessor(client_id).get_rules_by_rule_group_id(
        valid_rule_group_ids
    )

    if not approval_rule_groups:
        logger.error(
            "No approval rule groups found for form %s, client %s",
            form_builder_id,
            client_id,
        )
        return {}

    client_settings = get_client_settings(client_id)
    matrix_sheet_id = client_settings.get("cpq_settings", {}).get(
        "approval_matrix_sheet"
    )
    matrix_book_id = client_settings.get("cpq_settings", {}).get("approval_matrix_book")

    valid_approval_rule_ids, stage_template_ids, fields_df = (
        evaluate_approval_rules_for_quote(
            client_id,
            approval_rules,
            fields_df,
            quote_id,
            matrix_sheet_id=matrix_sheet_id,
            matrix_book_id=matrix_book_id,
        )
    )

    filters = {"stage_template_id__in": stage_template_ids}
    stage_template_data = get_approval_template_stages(client_id, filters=filters)

    stage_data_map = {
        str(stage["stage_template_id"]): stage for stage in stage_template_data
    }
    employee_manager_map = get_all_employee_manager_map(client_id)

    employee_records = EmployeeAccessor(client_id).get_all_employees(
        projection=["employee_email_id", "first_name", "last_name", "profile_picture"]
    )
    employee_name_map = {
        record["employee_email_id"]: {
            "first_name": record["first_name"],
            "last_name": record["last_name"],
            "profile_picture": record["profile_picture"],
        }
        for record in employee_records
    }

    payroll_details = EmployeePayrollAccessor(client_id).get_all_employees(
        as_dicts=True
    )
    payroll_details_map = {
        record["employee_email_id"]: record["designation"] for record in payroll_details
    }
    approval_cycles = []
    for rule_group in approval_rule_groups:
        _approval_cycle = {
            "template_name": rule_group.group_name,
            "template_description": rule_group.group_description,
            "total_stages": rule_group.stage_count,
            "stages": [],
            "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
            "notify_on_reject": rule_group.notify_on_reject,
            "notify_on_approve": rule_group.notify_on_approve,
            "notify_on_revoke": {"users": [], "groups": [], "dynamic": []},
            "rule_group_id": str(rule_group.rule_group_id),
        }
        for rule in approval_rules:
            if (
                rule.rule_id in valid_approval_rule_ids
                and rule.rule_group_id == rule_group.rule_group_id
            ):
                _stage_temp_id = str(rule.stage_template_id)
                if (
                    len(_approval_cycle["stages"]) == 0
                    or _approval_cycle["stages"][-1]["actual_stage_order"]
                    != rule.stage_order
                ):
                    _approval_cycle["stages"].append(
                        {
                            "actual_stage_order": rule.stage_order,
                            "overidden_stage_order": len(_approval_cycle["stages"]) + 1,
                            "stage_template_id": str(rule.stage_template_id),
                            "stage_name": stage_data_map[_stage_temp_id]["stage_name"],
                            "approval_strategy": stage_data_map[_stage_temp_id][
                                "approval_strategy"
                            ],
                            "due_period": stage_data_map[_stage_temp_id]["due_period"],
                            "is_auto_approve": stage_data_map[_stage_temp_id][
                                "is_auto_approve"
                            ],
                            "notes": stage_data_map[_stage_temp_id]["notes"],
                            "approvers": rule.approvers,
                            "resolved_approvers": [],
                            "approver_details": {},
                        }
                    )
                    previous_stage_approvers = (
                        _approval_cycle["stages"][len(_approval_cycle["stages"]) - 2][
                            "resolved_approvers"
                        ]
                        if len(_approval_cycle["stages"]) > 1
                        else []
                    )

                    resolved_approvers = get_resolved_employees(
                        client_id=client_id,
                        employees_details_object=rule.approvers,
                        employee_email=quote_owner,
                        employee_manager_map=employee_manager_map,
                        previous_stage_approvers=previous_stage_approvers,
                    )

                    _approval_cycle["stages"][-1][
                        "resolved_approvers"
                    ] = resolved_approvers
                    for _approver in resolved_approvers:
                        if (
                            _approver
                            not in _approval_cycle["stages"][-1]["approver_details"]
                        ):
                            _approval_cycle["stages"][-1]["approver_details"][
                                _approver
                            ] = []
                        _approval_cycle["stages"][-1]["approver_details"][
                            _approver
                        ].append(
                            {
                                "condition": rule.condition,
                                "rule_name": rule.rule_name,
                                "first_name": employee_name_map.get(_approver, {}).get(
                                    "first_name"
                                ),
                                "last_name": employee_name_map.get(_approver, {}).get(
                                    "last_name"
                                ),
                                "profile_picture": employee_name_map.get(
                                    _approver, {}
                                ).get("profile_picture"),
                                "designation": payroll_details_map.get(_approver),
                            }
                        )
                else:
                    previous_stage_approvers = (
                        _approval_cycle["stages"][len(_approval_cycle["stages"]) - 2][
                            "resolved_approvers"
                        ]
                        if len(_approval_cycle["stages"]) > 1
                        else []
                    )

                    resolved_approvers = get_resolved_employees(
                        client_id=client_id,
                        employees_details_object=rule.approvers,
                        employee_email=quote_owner,
                        employee_manager_map=employee_manager_map,
                        previous_stage_approvers=previous_stage_approvers,
                    )

                    _approval_cycle["stages"][-1]["resolved_approvers"] = list(
                        set(_approval_cycle["stages"][-1]["resolved_approvers"])
                        | set(resolved_approvers)
                    )
                    for _approver in resolved_approvers:
                        if (
                            _approver
                            not in _approval_cycle["stages"][-1]["approver_details"]
                        ):
                            _approval_cycle["stages"][-1]["approver_details"][
                                _approver
                            ] = []
                        _approval_cycle["stages"][-1]["approver_details"][
                            _approver
                        ].append(
                            {
                                "condition": rule.condition,
                                "rule_name": rule.rule_name,
                                "first_name": employee_name_map.get(_approver, {}).get(
                                    "first_name"
                                ),
                                "last_name": employee_name_map.get(_approver, {}).get(
                                    "last_name"
                                ),
                                "profile_picture": employee_name_map.get(
                                    _approver, {}
                                ).get("profile_picture"),
                                "designation": payroll_details_map.get(_approver),
                            }
                        )
                    merged_approvers = merge_approvers(
                        _approval_cycle["stages"][-1]["approvers"], rule.approvers
                    )
                    _approval_cycle["stages"][-1]["approvers"] = merged_approvers
        validated_cycle = handle_no_approvers(_approval_cycle)
        if len(_approval_cycle["stages"]) > 0:
            approval_cycles.append(validated_cycle)

    logger.info(
        "END: Get approval cycle for quote - form %s, client %s",
        form_builder_id,
        client_id,
    )

    return {
        "status": "success",
        "message": "Approval cycle fetched successfully",
        "approval_cycles": approval_cycles,
        "fields_df": fields_df,
    }


def handle_no_approvers(approval_cycle: dict):
    """
    This function removes any stage which does not have any approvers
    """
    logger.info("BEGIN: Handle no approvers")
    for stage_order, stage in enumerate(approval_cycle["stages"]):
        if not stage["resolved_approvers"]:
            approval_cycle["stages"].pop(stage_order)
    logger.info("END: Handle no approvers")
    for stage_order, stage in enumerate(approval_cycle["stages"]):
        stage["overidden_stage_order"] = stage_order + 1
    return approval_cycle


@transaction.atomic
def create_approval_cycles(client_id: int, form_builder_id: str, **kwargs):
    """
    This function creates approval cycles for a form,
    given the form_builder_id and client_id and fields of the quote
    """
    from everstage_ddd.cpq.quote import QuoteSelector

    logger.info(
        "BEGIN: Create approval instances for form %s, client %s",
        form_builder_id,
        client_id,
    )
    logged_in_user = kwargs.get("logged_in_user")
    form_data = kwargs.get("form_data", {})
    quote_id = kwargs.get("quote_id")
    comments = kwargs.get("comments")

    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(quote_id)
    quote_total = quote_data.net_quote_total
    quote_owner = quote_data.owner_id
    fields_df = get_all_fields_for_approvals(
        client_id,
        logged_in_user,
        quote_owner,
        quote_id=quote_id,
        form_data=form_data,
    )

    approvals = get_approval_cycle_for_quote(
        client_id,
        quote_owner,
        form_builder_id=form_builder_id,
        fields_df=fields_df,
        quote_id=quote_id,
    )
    if not approvals:
        logger.error(
            "No approval cycles found for form %s, client %s",
            form_builder_id,
            client_id,
        )
        # If no approval cycles are found, then the quote is approved by default
        return {"quote_status": APPROVAL_WORKFLOW_STATUS.APPROVED.value}
    approval_cycles = approvals["approval_cycles"]
    all_fields_df = approvals["fields_df"]
    fields_list = all_fields_df.to_dict(orient="records")
    json_data = json.dumps(fields_list, default=str)

    rule_group_ids = []
    for _approval_cycle in approval_cycles:
        create_approval_instance_and_stages(
            client_id=client_id,
            template_id=None,
            entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value,
            instance_params={
                "instance_details": [
                    {
                        "email_id": quote_owner,
                        "form_builder_id": str(form_builder_id),
                        "quote_id": str(quote_id),
                        "rule_group_id": str(_approval_cycle["rule_group_id"]),
                        "rule_group_name": _approval_cycle["template_name"],
                        "all_fields": json_data,
                        "quote_total": quote_total,
                        "currency": quote_data.quote_currency,
                        "comments": comments,
                    }
                ]
            },
            created_by=logged_in_user,
            template_data=_approval_cycle,
            audit_data={"updated_by": logged_in_user, "created_by": logged_in_user},
        )
        rule_group_ids.append(_approval_cycle["rule_group_id"])
    logger.info(
        "END: Create approval instances for form %s, client %s",
        form_builder_id,
        client_id,
    )
    quote_status = get_approval_status_for_quote(client_id, quote_id)
    ###################### audit log #####################
    event_type_code = EVENT["CREATE_APPROVALS-QUOTE-APPROVALS"]["code"]
    event_key = quote_id
    summary = "Quote Approval created"
    updated_by = logged_in_user
    updated_at = timezone.now()
    audit_data = {"rule_groups": {"new_instances": rule_group_ids}}
    ######################################################
    audit_services.log(
        client_id,
        event_type_code,
        event_key,
        summary,
        updated_by,
        updated_at,
        audit_data,
    )

    return {
        "quote_status": quote_status,
    }


@transaction.atomic
def edit_approval_instance(client_id: int, logged_in_user: str, **kwargs):
    """
    This function is used when a quote is edited
    And this decides to create new approval instances or resume the existing approval instances
    or delete the existing approval instances
    """
    from commission_engine.accessors.client_accessor import get_client_features
    from everstage_ddd.cpq.quote import QuoteSelector

    form_builder_id = kwargs.get("form_builder_id")
    quote_id = kwargs.get("quote_id")
    form_data = kwargs.get("form_data", {})
    comments = kwargs.get("comments")

    logger.info(
        "BEGIN: Edit approval instances for form %s, client %s",
        form_builder_id,
        client_id,
    )
    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(quote_id)
    quote_owner = quote_data.owner_id
    quote_total = quote_data.net_quote_total

    fields_df = get_all_fields_for_approvals(
        client_id,
        logged_in_user,
        quote_owner,
        quote_id=quote_id,
        form_data=form_data,
    )

    approvals = get_approval_cycle_for_quote(
        client_id,
        quote_owner,
        form_builder_id=form_builder_id,
        fields_df=fields_df,
        quote_id=quote_id,
    )
    if not approvals:
        logger.error(
            "No approval cycles found for form %s, client %s",
            form_builder_id,
            client_id,
        )
        # If no approval cycles are found, then the quote is approved by default
        return {"quote_status": APPROVAL_WORKFLOW_STATUS.APPROVED.value}

    approval_cycles = approvals["approval_cycles"]
    all_fields_df = approvals["fields_df"]
    fields_list = all_fields_df.to_dict(orient="records")
    json_data = json.dumps(fields_list, default=str)

    # get existing approval instances for the given quote_id
    filters = {
        "entity_key__contains": str(quote_id),
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    order_by = ["knowledge_begin_date"]
    existing_approval_instances = get_approval_wf_instances(
        client_id, filters=filters, order_by=order_by
    )

    existing_rule_groups_ids = [
        instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
        for instance in existing_approval_instances
    ]
    existing_entity_keys = [
        instance["entity_key"] for instance in existing_approval_instances
    ]
    new_rule_group_ids = [
        _approval_cycle["rule_group_id"] for _approval_cycle in approval_cycles
    ]
    new_instances = []
    existing_instances = []
    for _approval_cycle in approval_cycles:
        if _approval_cycle["rule_group_id"] in existing_rule_groups_ids:
            existing_instances.append(_approval_cycle)
        else:
            new_instances.append(_approval_cycle)
    # after editing some instances might not be needed, so delete those instances
    delete_instance_ids = set(existing_rule_groups_ids) - set(new_rule_group_ids)
    delete_entity_keys = set(
        quote_owner
        + ENTITY_KEY_DELIMETER
        + str(quote_id)
        + ENTITY_KEY_DELIMETER
        + str(rule_group_id)
        for rule_group_id in delete_instance_ids
    )

    delete_entity_keys.update(
        quote_owner
        + ENTITY_KEY_DELIMETER
        + str(quote_id)
        + ENTITY_KEY_DELIMETER
        + str(instance["rule_group_id"])
        for instance in existing_instances
    )

    resume_previous_approval_instance = get_client_features(client_id).get(
        "resume_previous_approval_instance", True
    )
    # service to delete the instances for the rule_group_ids in delete_instance_ids
    # create_approval_instance_and_stages for the new_instances
    # verify each stage level details for the existing approval instances
    # for instance in existing_approval_instances:
    if resume_previous_approval_instance and existing_instances:
        existing_instances = smart_evaluate_approval_instances(
            client_id, fields_list, quote_id, existing_instances, quote_owner
        )

    if delete_entity_keys:
        delete_approval_requests(
            client_id, logged_in_user, entity_keys=list(delete_entity_keys)
        )
    for _approval_cycle in existing_instances + new_instances:
        create_approval_instance_and_stages(
            client_id=client_id,
            template_id=None,
            entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value,
            instance_params={
                "instance_details": [
                    {
                        "email_id": quote_owner,
                        "form_builder_id": str(form_builder_id),
                        "quote_id": str(quote_id),
                        "rule_group_id": str(_approval_cycle["rule_group_id"]),
                        "rule_group_name": _approval_cycle["template_name"],
                        "all_fields": json_data,
                        "quote_total": quote_total,
                        "currency": quote_data.quote_currency,
                        "comments": comments,
                    }
                ]
            },
            created_by=logged_in_user,
            template_data=_approval_cycle,
            audit_data={"created_by": quote_owner, "updated_by": logged_in_user},
            existing_entity_keys=existing_entity_keys,
        )
    # trigger approve_request for approve on requested instances
    trigger_approve_on_request(
        client_id, quote_owner, logged_in_user, quote_id=quote_id
    )
    quote_status = get_approval_status_for_quote(client_id, quote_id)

    ###################### audit log #####################
    event_type_code = EVENT["EDIT_APPROVALS-QUOTE-APPROVALS"]["code"]
    event_key = quote_id
    summary = "Quote Approval edited"
    updated_by = logged_in_user
    updated_at = timezone.now()
    audit_data = {
        "rule_groups": {
            "new_instances": new_rule_group_ids,
            "existing_instances": existing_rule_groups_ids,
            "delete_instances": [
                entity_key.split(ENTITY_KEY_DELIMETER)[2]
                for entity_key in delete_entity_keys
            ],
        }
    }
    ######################################################
    audit_services.log(
        client_id,
        event_type_code,
        event_key,
        summary,
        updated_by,
        updated_at,
        audit_data,
    )
    logger.info(
        "END: Edit approval instances for form %s, client %s",
        form_builder_id,
        client_id,
    )
    return {
        "quote_status": quote_status,
    }


# ruff: noqa: PLR0915
def smart_evaluate_approval_instances(
    client_id: int,
    all_fields: list,
    quote_id: str,
    existing_instances: list,
    quote_owner: str,
):
    """
    This function evaluates the approval instances and decides whether to invalidate old instances & create new instances or not
    """
    from everstage_ddd.cpq.forms.service.form_rules_helper import get_source_fields_used

    logger.info("BEGIN: Smart evaluate approval instances %s", quote_id)
    partial_entity_keys = [
        str(quote_id) + ENTITY_KEY_DELIMETER + str(instance["rule_group_id"])
        for instance in existing_instances
    ]
    contains_any_filter = _query_contains_any_filter("entity_key", partial_entity_keys)
    projection = ["approval_wf_instance_id", "entity_key", "instance_data"]
    order_by = ["-knowledge_begin_date"]
    filters = {"entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value}
    approval_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        qs_filter=contains_any_filter,
        projection=projection,
        order_by=order_by,
    )

    instance_data_map = {
        instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]: instance["instance_data"]
        for instance in approval_instances
    }

    rule_group_ids = [
        instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
        for instance in approval_instances
    ]

    contains_any_filter = _query_contains_any_filter("entity_key", rule_group_ids)
    projection = ["approval_wf_instance_id"]
    order_by = ["-knowledge_begin_date"]
    filters = {"entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value}
    approval_wf_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        qs_filter=contains_any_filter,
        projection=projection,
        order_by=order_by,
    )
    approval_wf_instance_ids = [
        str(wf_instance["approval_wf_instance_id"])
        for wf_instance in approval_wf_instances
    ]
    projection = [
        "approval_wf_instance_id",
        "stage_instance_id",
        "approvers",
        "status",
        "stage_order",
    ]
    order_by = ["stage_order"]
    filters = {
        "approval_wf_instance_id__in": approval_wf_instance_ids,
    }
    stage_details = get_approval_stage_instances(
        client_id, filters=filters, projection=projection, order_by=order_by
    )
    started_stage_ids = [
        stage["stage_instance_id"]
        for stage in stage_details
        if stage["status"] != "not_started"
    ]
    filters = {
        "stage_instance_id__in": started_stage_ids,
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    projection = ["stage_instance_id", "approval_request_id", "status", "approver"]
    approval_req = get_approval_requests(
        client_id, filters=filters, projection=projection
    )
    stage_approver_status_map = {}
    for request in approval_req:
        stage_instance_id = request["stage_instance_id"]
        approver = request["approver"]
        status = request["status"]
        if str(stage_instance_id) not in stage_approver_status_map:
            stage_approver_status_map[str(stage_instance_id)] = {}
        stage_approver_status_map[str(stage_instance_id)][approver] = status

    wf_instance_stage_map = {}
    for stage in stage_details:
        if stage["approval_wf_instance_id"] not in wf_instance_stage_map:
            wf_instance_stage_map[stage["approval_wf_instance_id"]] = []
        wf_instance_stage_map[stage["approval_wf_instance_id"]].append(stage)

    rule_group_wf_instance_map = get_rule_group_instance_map(
        approval_instances, wf_instance_stage_map, stage_approver_status_map
    )

    employee_manager_map = get_all_employee_manager_map(client_id)

    for instance in existing_instances:
        logger.info(f"Instance - rule_group_id: {instance['rule_group_id']}")
        rule_group_id = instance["rule_group_id"]
        previous_stage_approvers = {}
        for stage_order, _stage in enumerate(instance["stages"]):
            resolved_approvers = get_resolved_employees(
                client_id,
                _stage["approvers"],
                quote_owner,
                employee_manager_map,
                previous_stage_approvers=previous_stage_approvers.get(stage_order, []),
            )
            previous_stage_approvers[stage_order + 1] = resolved_approvers
            for approver in resolved_approvers:
                overriden_stage_order = _stage["overidden_stage_order"]
                if (
                    rule_group_wf_instance_map.get(rule_group_id, {})
                    .get(overriden_stage_order, {})
                    .get(approver)
                    == "approved"
                ):
                    approve_on_request_values = []
                    for _condition in _stage["approver_details"][approver]:
                        condition = _condition["condition"]["actions"][0]["condition"]
                        variables = get_source_fields_used(condition)
                        all_fields_str = json.dumps(all_fields, default=str)
                        all_fields_dict = json.loads(all_fields_str)
                        approve_on_request_values.append(
                            has_same_values(
                                json.loads(
                                    instance_data_map[rule_group_id]["all_fields"]
                                ),
                                all_fields_dict,
                                variables,
                            )
                        )
                    if False not in approve_on_request_values:
                        _stage.setdefault("approve_on_request", []).append(approver)
    logger.info("END: Smart evaluate approval instances %s", quote_id)
    return existing_instances


def trigger_approve_on_request(
    client_id: int, quote_owner: str, logged_in_user: str, **kwargs
):
    """
    This function triggers approve on request for the instances which have approve_on_request as True
    This is used when the approval instances are edited, but the approver has already approved the stage
    and the approval needs to be persisted (smart evaluation)

    Args:
        client_id: Client id
        quote_owner: Quote owner
        kwargs: Additional keyword arguments
            quote_id: Quote id

    Returns:
        None
    """
    quote_id = kwargs.get("quote_id")
    entity_key = quote_owner + ENTITY_KEY_DELIMETER + str(quote_id)

    filters = {
        "entity_key__contains": entity_key,
        "status__in": [
            APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
        ],
        "approve_on_request": True,
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    projection = ["approval_request_id"]
    approve_on_requests = get_approval_requests(
        client_id, filters=filters, projection=projection
    )
    approve_on_request_ids = [
        request["approval_request_id"] for request in approve_on_requests
    ]
    if approve_on_request_ids:
        approve_request(
            client_id,
            approve_on_request_ids,
            audit={"approved_by": quote_owner, "updated_by": logged_in_user},
            logger=logger,
            approve_on_request=True,
        )


def get_rule_group_instance_map(
    approval_instances: list,
    wf_instance_stage_map: dict,
    stage_approver_status_map: dict,
):
    """
    This function maps the rule group id to the approval instances
    Given the existing approval instances, this function creates a map
    between rule_group_id, stage_order, approver and status

    Args:
        approval_instances: List of approval instances
        wf_instance_stage_map: Map of workflow instance id to stages
        stage_approver_status_map: Map of stage instance id to approver status

    Returns:
        rule_group_wf_instance_map: Map of rule group id to approval instances

    Example:
    {
        "rule_group_id_1": {
            "1": {
                "<EMAIL>": "requested"
            },
            "2": {
                "<EMAIL>": "not_started"
            },
        },
        "rule_group_id_2": {....}
    """
    rule_group_wf_instance_map = {}
    for instance in approval_instances:
        if (
            instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
            not in rule_group_wf_instance_map
        ):
            rule_group_wf_instance_map[
                instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
            ] = {}
        for stage in wf_instance_stage_map[instance["approval_wf_instance_id"]]:
            rule_group_wf_instance_map[
                instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
            ][stage["stage_order"]] = {}
            if stage["status"] == "not_started":
                for approver in stage["approvers"]:
                    rule_group_wf_instance_map[
                        instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
                    ][stage["stage_order"]][approver] = stage["status"]
            else:
                for approver in stage["approvers"]:
                    rule_group_wf_instance_map[
                        instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
                    ][stage["stage_order"]][approver] = stage_approver_status_map[
                        str(stage["stage_instance_id"])
                    ][
                        approver
                    ]

    return rule_group_wf_instance_map


def has_same_values(list1: list, list2: list, fields: list):
    """
    This function checks if the fields in obj1 and obj2 are same

    Args:
        obj1: Object 1
        obj2: Object 2
        fields: List of fields

    Returns:
        bool: True if the fields are same, False otherwise

    Example:
        obj1 = {
            "field1":100,
            "field2":200
        }
        obj2 = {
            "field1":100,
            "field2":200
        }
        fields = ["field1","field2"]
        has_same_values(obj1,obj2,fields) -> True
    """
    # If the fields are quote line item fields and the length of the lists are different, return False
    # as new line items are added or removed, so we need to trigger approvals.
    if set(fields).intersection(quote_line_item_fields) and len(list1) != len(list2):
        return False
    # Convert list2 into a dictionary for fast lookup by ('sku', 'phase_id')
    dict2 = {(obj.get("sku"), obj.get("phase_id")): obj for obj in list2}
    # Iterate through list1 and compare with corresponding objects in list2
    for obj1 in list1:
        key = (obj1.get("sku"), obj1.get("phase_id"))
        obj2 = dict2.get(key)
        # If the corresponding object by ('sku', 'phase_id') is not found, or values differ
        if not obj2:
            return False  # The object doesn't exist in list2

        for field in fields:
            if obj1.get(field) != obj2.get(field):
                return False  # Return False if any value is different

    return True  # Return True if all values match


def get_multi_approval_quote_status(instances):
    """
    This function returns the aggregate approval status for a quote
    """
    status_instance_map = {}
    quote_status_list = []
    for instance in instances:
        if instance["status"] not in status_instance_map:
            status_instance_map[instance["status"]] = []
        status_instance_map[instance["status"]].append(
            instance["instance_data"].get("rule_group_name")
        )
        quote_status_list.append(instance["status"])

    # Case 1: All approved
    if all(
        status == APPROVAL_WORKFLOW_STATUS.APPROVED.value
        for status in quote_status_list
    ):
        return APPROVAL_WORKFLOW_STATUS.APPROVED.value, status_instance_map

    # Case 2: All rejected
    if all(
        status == APPROVAL_WORKFLOW_STATUS.REJECTED.value
        for status in quote_status_list
    ):
        return APPROVAL_WORKFLOW_STATUS.REJECTED.value, status_instance_map

    # Case 3: All requested
    if all(
        status == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        for status in quote_status_list
    ):
        return APPROVAL_WORKFLOW_STATUS.REQUESTED.value, status_instance_map

    if any(
        status == APPROVAL_WORKFLOW_STATUS.REJECTED.value
        for status in quote_status_list
    ):
        return APPROVAL_WORKFLOW_STATUS.REJECTED.value, status_instance_map

    if any(
        status == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        for status in quote_status_list
    ):
        return APPROVAL_WORKFLOW_STATUS.REQUESTED.value, status_instance_map

    # Default case (shouldn't reach here based on the given conditions)
    return APPROVAL_WORKFLOW_STATUS.REQUESTED.value, status_instance_map


def get_approval_status_for_quote(client_id: int, quote_id: str):
    """
    This functions returns the aggregate approval status for a quote

    Args:
        client_id: Client id
        quote_ids: Quote id

    Returns:
        quote_id_status_map: Map of quote id to approval status

    Example:
        {
            "quote_id_1": "approved",
            "quote_id_2": "rejected",
            "quote_id_3": "pending"
        }
    """
    logger.info("BEGIN: Get approval status for quote - client %s", client_id)
    projection = ["entity_key", "status", "instance_data"]
    order_by = ["-knowledge_begin_date"]
    filters = {
        "entity_key__contains": quote_id,
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    approval_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        projection=projection,
        order_by=order_by,
    )
    status, _ = get_multi_approval_quote_status(approval_instances)
    return status


def get_quotes_with_needs_attention_status(client_id: int):
    """
    This function returns the quotes with needs attention
    """
    from everstage_ddd.cpq.quote.quote_status.enums import QuoteStatusEnum
    from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
        QuoteStatusSelector,
    )

    logger.info("BEGIN: Get quotes with needs attention status - client %s", client_id)

    needs_attention_quote_ids = []
    # get all quote ids with rejected status
    quote_ids = QuoteStatusSelector(client_id=client_id).get_quote_ids_by_status(
        [QuoteStatusEnum.REJECTED.value]
    )

    quote_ids = [str(quote_id) for quote_id in quote_ids]
    projection = ["entity_key", "status", "instance_data"]
    order_by = ["-knowledge_begin_date"]
    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    contains_any_filter = _query_contains_any_filter("entity_key", quote_ids)
    approval_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        qs_filter=contains_any_filter,
        projection=projection,
        order_by=order_by,
    )

    quote_ids = set()
    quote_id_instance_map = {}
    for instance in approval_instances:
        quote_id = instance["entity_key"].split("##::##")[1]
        quote_ids.add(quote_id)
        if quote_id not in quote_id_instance_map:
            quote_id_instance_map[quote_id] = []
        quote_id_instance_map[quote_id].append(instance)

    quote_ids_status_map = {}
    for quote_id in quote_ids:
        _, status_instance_map = get_multi_approval_quote_status(
            quote_id_instance_map[quote_id]
        )
        quote_ids_status_map[quote_id] = status_instance_map

    needs_attention_quote_ids = [
        quote_id
        for quote_id, status_map in quote_ids_status_map.items()
        if len(status_map.keys()) > 1
    ]
    logger.info("END: Get quotes with needs attention status - client %s", client_id)
    return needs_attention_quote_ids


def _query_contains_any_filter(field_name, values):
    """
    Build a Q object for filtering by `field_name` containing any value in `values`.

    :param field_name: The field name on which to apply the `contains` lookup.
    :param values: A list of values to check in the field.
    :return: A Q object with the combined OR query for `contains`.
    """
    q_objects = Q()
    for value in values:
        q_objects |= Q(**{f"{field_name}__contains": value})
    return q_objects


def get_approval_details_for_banner(client_id: int, quote_id: str, logged_in_user: str):
    """
    This function returns the approval details for an approver
    """

    logger.info(
        "BEGIN: Get approval details for approver - client %s, quote %s, approver %s",
        client_id,
        quote_id,
        logged_in_user,
    )
    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "entity_key__contains": quote_id,
    }
    projection = ["approval_wf_instance_id", "entity_key", "status", "completion_time"]
    order_by = ["-knowledge_begin_date"]
    approval_wf_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        projection=projection,
        order_by=order_by,
    )
    if not approval_wf_instances:
        logger.info("No approval instances found for quote %s", quote_id)
        return {}

    approver_details = {}
    quote_owner = approval_wf_instances[0]["entity_key"].split(ENTITY_KEY_DELIMETER)[0]
    approval_wf_instance_id = approval_wf_instances[0]["approval_wf_instance_id"]
    quote_status = approval_wf_instances[0]["status"]
    if quote_status in [
        APPROVAL_WORKFLOW_STATUS.APPROVED.value,
        APPROVAL_WORKFLOW_STATUS.REJECTED.value,
    ]:
        logger.info("Quote %s is already approved/rejected", quote_id)
        # return approve/reject banner
        approver_details = get_approval_completion_banner(
            client_id, approval_wf_instances[0], logged_in_user
        )
        return approver_details

    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "entity_key__contains": quote_id,
        "approver": logged_in_user,
        "status__in": [
            APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            APPROVAL_WORKFLOW_STATUS.REJECTED.value,
        ],
    }
    if quote_owner != logged_in_user:
        stage_filters = {
            "approval_wf_instance_id": approval_wf_instance_id,
            "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
        }
        stage_projection = ["stage_instance_id"]
        approval_stages = get_approval_stage_instances(
            client_id, filters=stage_filters, projection=stage_projection
        )
        if not approval_stages:
            logger.info("No approval stages found for quote %s", quote_id)
            return {}
        stage_instance_id = approval_stages[0]["stage_instance_id"]

        filters["stage_instance_id"] = stage_instance_id

    projection = [
        "approval_wf_instance_id",
        "stage_instance_id",
        "approval_request_id",
        "entity_key",
        "status",
        "comments",
        "request_data",
        "requested_time",
        "completed_time",
    ]
    order_by = ["-knowledge_begin_date"]
    approval_requests = get_approval_requests(
        client_id, filters=filters, projection=projection, order_by=order_by
    )

    if quote_owner == logged_in_user:
        if (
            approval_requests
            and approval_requests[0]["status"]
            == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        ):
            logger.info(
                f"{quote_owner} is the approver and request is pending - {quote_id}"
            )
            approver_details = get_approver_data(
                client_id, quote_id, logged_in_user, approval_requests[0]
            )
        else:
            logger.info(f"{quote_owner} - show rep view - {quote_id}")
            approver_details = get_rep_data(client_id, quote_id, logged_in_user)
    elif (
        approval_requests
        and approval_requests[0]["status"] == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
    ):
        logger.info(f"{logged_in_user} is the approver - {quote_id}")
        approver_details = get_approver_data(
            client_id, quote_id, logged_in_user, approval_requests[0]
        )
    else:
        logger.info(f"{logged_in_user} is not the rep for the quote - {quote_id}")
        approver_details = get_rep_data(client_id, quote_id, quote_owner)
    logger.info(
        "END: Get approval details for approver - client %s, quote %s, approver %s",
        client_id,
        quote_id,
        logged_in_user,
    )
    return approver_details


def get_approval_completion_banner(client_id, approval_wf_instance, logged_in_user):
    approver_details = {}
    approver_details["is_rep_view"] = False
    approver_details["quote_owner_email"] = approval_wf_instance["entity_key"].split(
        ENTITY_KEY_DELIMETER
    )[0]
    quote_owner: list = EmployeeAccessor(client_id).get_employee_by_email_id(
        email_id=approver_details["quote_owner_email"]
    )
    if quote_owner:
        approver_details["quote_owner"] = (
            quote_owner[0].first_name + " " + quote_owner[0].last_name
        )
    approver_details["rule_name"] = None
    approver_details["completed_time"] = approval_wf_instance["completion_time"]

    filters = {
        "approval_wf_instance_id": approval_wf_instance["approval_wf_instance_id"],
    }
    order_by = ["-knowledge_begin_date"]
    approval_requests = get_approval_requests(
        client_id, filters=filters, order_by=order_by
    )

    if approval_requests:
        unique_approvers = set()
        for request in approval_requests:
            unique_approvers.add(request["approver"])
        quote_owner = approval_wf_instance["entity_key"].split(ENTITY_KEY_DELIMETER)[0]
        unique_approvers.add(quote_owner)

        if approval_wf_instance["status"] == APPROVAL_WORKFLOW_STATUS.APPROVED.value:
            approver_details["status"] = APPROVAL_WORKFLOW_STATUS.APPROVED.value
            return approver_details

        for request in approval_requests:
            if request["status"] == APPROVAL_WORKFLOW_STATUS.REJECTED.value:
                approver_details["status"] = APPROVAL_WORKFLOW_STATUS.REJECTED.value
                approver_details["rejection_reason"] = request["comments"]
                for _details in request["request_data"]:
                    rule_name = _details.get("rule_name")
                    if approver_details["rule_name"]:
                        approver_details["rule_name"] += f", {rule_name}"
                    else:
                        approver_details["rule_name"] = rule_name
    return approver_details


def get_approver_data(client_id, quote_id, logged_in_user, approval_request):
    from everstage_ddd.cpq.quote import QuoteSelector

    approver_details = {}
    approver_details["is_rep_view"] = False
    approver_details["request_id"] = str(approval_request["approval_request_id"])
    approver_details["rule_name"] = None
    for _details in approval_request["request_data"]:
        rule_name = _details.get("rule_name")
        if approver_details["rule_name"]:
            approver_details["rule_name"] += f", {rule_name}"
        else:
            approver_details["rule_name"] = rule_name
    approver_details["quote_owner_email"] = approval_request["entity_key"].split(
        ENTITY_KEY_DELIMETER
    )[0]
    quote_owner: list = EmployeeAccessor(client_id).get_employee_by_email_id(
        email_id=approver_details["quote_owner_email"]
    )
    if quote_owner:
        approver_details["quote_owner"] = (
            quote_owner[0].first_name + " " + quote_owner[0].last_name
        )

    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(quote_id)

    approver_details["requested_time"] = approval_request["requested_time"]
    approver_details["status"] = approval_request["status"]
    approver_details["total_quote_value"] = str(quote_data.net_quote_total)
    approver_details["currency"] = quote_data.quote_currency

    filters = {
        "approval_wf_instance_id": approval_request["approval_wf_instance_id"],
        "stage_instance_id": approval_request["stage_instance_id"],
    }
    projection = ["due_date", "notes"]
    stage_details = get_approval_stage_instances(
        client_id, filters=filters, projection=projection
    )
    if stage_details:
        approver_details["due_date"] = format_approval_due_date(
            client_id, logged_in_user, stage_details[0]["due_date"]
        )
        approver_details["notes"] = stage_details[0]["notes"]
    return approver_details


def get_rep_data(client_id, quote_id, logged_in_user):
    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "entity_key__contains": logged_in_user + ENTITY_KEY_DELIMETER + quote_id,
    }
    projection = [
        "approval_wf_instance_id",
        "entity_key",
        "status",
        "requested_time",
    ]
    order_by = ["-knowledge_begin_date"]
    approval_requests = get_approval_wf_instances(
        client_id, filters=filters, projection=projection, order_by=order_by
    )
    approver_details = {}
    if approval_requests:
        approval_request = approval_requests[0]
        approver_details["is_rep_view"] = True
        approver_details["requested_time"] = approval_request["requested_time"]
        request_count, approver_records = get_all_approvers(
            client_id, approval_request["approval_wf_instance_id"]
        )
        approver_details["request_count"] = request_count
        approver_details["approver_records"] = approver_records
    return approver_details


def get_all_approvers(client_id, approval_wf_instance_id):
    filters = {
        "approval_wf_instance_id": approval_wf_instance_id,
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    projection = ["approver", "status"]
    approval_requests = get_approval_requests(
        client_id, filters=filters, projection=projection
    )
    _approvers_data = {
        "total_requests": len(approval_requests),
        "approved": 0,
    }
    all_approvers = []

    for request in approval_requests:
        if request["status"] == APPROVAL_WORKFLOW_STATUS.APPROVED.value:
            _approvers_data["approved"] += 1
        all_approvers.append(request["approver"])

    approver_records = EmployeeAccessor(client_id).get_all_employees(
        email_list=all_approvers,
        projection=["employee_email_id", "first_name", "last_name", "profile_picture"],
    )

    return (_approvers_data, approver_records)


def get_approvals_source_rule_fields(client_id, form_builder_id):

    source_fields = set()
    rule_groups = ApprovalRuleGroupAccessor(
        client_id
    ).get_rule_groups_by_form_builder_id(form_builder_id, status="active")
    for rule_group in rule_groups:
        condition = rule_group.condition
        if condition and "source_fields" in condition:
            source_fields.update(condition["source_fields"])

    rules = ApprovalRuleAccessor(client_id).get_rules_by_form_builder_id(
        form_builder_id
    )
    for rule in rules:
        if rule.condition:
            source_fields.update(rule.condition["source_fields"])
    return list(source_fields)


def format_approval_due_date(client_id, logged_in_user, due_date, date_format=None):
    due_date_format = "%d %b %Y"
    employee_data = EmployeeAccessor(client_id).get_employee_by_email_id(
        logged_in_user,
        projection=[
            "employee_email_id",
            "first_name",
            "last_name",
            "profile_picture",
            "time_zone",
        ],
        as_dicts=True,
    )
    user_timezone = employee_data[0].get("time_zone", None)
    if user_timezone:
        user_timezone = user_timezone.split(" ")
        if len(user_timezone) > 1:
            user_timezone = tz.gettz(user_timezone[1])
        else:
            user_timezone = pytz.timezone(user_timezone[0])

    if due_date:
        return due_date.astimezone(user_timezone).strftime(
            date_format if date_format else due_date_format
        )
    return None


def get_all_quotes_waiting_on_you(client_id: int, logged_in_user: str):
    """
    Get all quotes waiting on you for a given client and logged in user
    """
    from everstage_ddd.cpq.quote import QuoteSelector

    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "approver": logged_in_user,
        "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
    }
    projection = ["entity_key"]
    approval_requests = get_approval_requests(
        client_id, filters=filters, projection=projection
    )

    quote_ids = [
        request["entity_key"].split(ENTITY_KEY_DELIMETER)[1]
        for request in approval_requests
    ]
    quote_selector = QuoteSelector(client_id=client_id)
    valid_quotes = quote_selector.get_quotes_by_quote_ids(quote_ids)
    valid_quote_ids = [str(quote.quote_id) for quote in valid_quotes]
    return valid_quote_ids


def delete_quote_approval_requests(client_id, logged_in_user, quote_id):
    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "entity_key__contains": quote_id,
    }
    approval_wf_instances = get_approval_wf_instances(client_id, filters)
    entity_keys = [instance["entity_key"] for instance in approval_wf_instances]
    delete_approval_requests(
        client_id,
        logged_in_user,
        entity_keys=entity_keys,
        entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value,
    )


def get_quote_status_map(client_id, quote_ids):
    projection = ["entity_key", "status", "instance_data"]
    order_by = ["-knowledge_begin_date"]
    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    contains_any_filter = _query_contains_any_filter("entity_key", quote_ids)
    approval_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        qs_filter=contains_any_filter,
        projection=projection,
        order_by=order_by,
    )
    quote_instances_map = {}
    for instance in approval_instances:
        quote_id = instance["entity_key"].split(ENTITY_KEY_DELIMETER)[1]
        if quote_id not in quote_instances_map:
            quote_instances_map[quote_id] = []
        quote_instances_map[quote_id].append(instance)

    quote_status_map = {}
    for quote_id in quote_ids:
        if quote_id not in quote_instances_map:
            continue
        _, status_instance_map = get_multi_approval_quote_status(
            quote_instances_map[quote_id]
        )

        quote_status_map[quote_id] = status_instance_map
    return quote_status_map


def get_multi_approval_details(client_id, quote_id):
    from spm.accessors.approval_workflow_accessors import ApprovalInstanceAccessor

    all_quote_approval_instances = ApprovalInstanceAccessor(
        client_id
    ).get_instance_by_partial_entity_key(
        quote_id, entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value
    )

    instance_data = []

    for instance in all_quote_approval_instances:
        wf_instance = {}
        wf_instance["status"] = instance["status"]
        wf_instance["entity_key"] = instance["entity_key"]
        wf_instance["rule_name"] = instance["instance_data"]["rule_group_name"]
        instance_data.append(wf_instance)

    return instance_data


def get_approval_instances_smart_evaluate(client_id, logged_in_user, **kwargs):
    from commission_engine.accessors.client_accessor import get_client_features
    from everstage_ddd.cpq.quote import QuoteSelector

    quote_id = kwargs.get("quote_id")
    form_builder_id = kwargs.get("form_builder_id")
    fields_df = kwargs.get("fields_df")
    is_edit_mode = kwargs.get("is_edit_mode", False)

    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(quote_id)
    quote_owner = quote_data.owner_id

    approvals = get_approval_cycle_for_quote(
        client_id,
        quote_owner,
        fields_df=fields_df,
        form_builder_id=form_builder_id,
        quote_id=quote_id,
    )
    if not is_edit_mode:
        logger.info("This is not edit mode, so returning approvals")
        return approvals
    if not approvals:
        logger.error(
            f"No approval cycles found for form {form_builder_id}, client {client_id}"
        )
        return {"approval_cycles": []}
    logger.info("This is edit mode, so smart evaluating approvals")
    approval_cycles = approvals["approval_cycles"]
    all_fields_df = approvals["fields_df"]
    fields_list = all_fields_df.to_dict(orient="records")

    filters = {
        "entity_key__contains": str(quote_id),
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    order_by = ["knowledge_begin_date"]
    existing_approval_instances = get_approval_wf_instances(
        client_id, filters=filters, order_by=order_by
    )

    existing_rule_groups_ids = [
        instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2]
        for instance in existing_approval_instances
    ]

    new_instances = []
    existing_instances = []
    for _approval_cycle in approval_cycles:
        if _approval_cycle["rule_group_id"] in existing_rule_groups_ids:
            existing_instances.append(_approval_cycle)
        else:
            new_instances.append(_approval_cycle)

    resume_previous_approval_instance = get_client_features(client_id).get(
        "resume_previous_approval_instance", True
    )

    if resume_previous_approval_instance and existing_instances:
        existing_instances = smart_evaluate_approval_instances(
            client_id, fields_list, quote_id, existing_instances, quote_owner
        )
    approval_cycles = existing_instances + new_instances

    resolved_approval_cycles = [
        {
            **instance,
            "stages": [
                stage
                for stage in instance["stages"]
                if len(stage.get("approve_on_request", []))
                != len(stage.get("resolved_approvers", 0))
            ],
        }
        for instance in approval_cycles
    ]

    # Removing instances with no remaining stages
    resolved_approval_cycles = [
        instance for instance in resolved_approval_cycles if instance["stages"]
    ]

    for approval_cycle in resolved_approval_cycles:
        for stage_order, stage in enumerate(approval_cycle["stages"]):
            stage["overidden_stage_order"] = stage_order + 1

    return {"approval_cycles": resolved_approval_cycles}


def get_approval_request_common_data(client_id, quote_id):
    from everstage_ddd.cpq.quote import QuoteSelector

    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(quote_id)
    quote_owner: list = EmployeeAccessor(client_id).get_employee_by_email_id(
        email_id=quote_data.owner_id
    )

    common_data = {
        "total_quote_value": str(quote_data.net_quote_total),
        "quote_owner_name": (
            quote_owner[0].first_name + " " + quote_owner[0].last_name
        ),
        "quote_owner_email": quote_data.owner_id,
        "currency": quote_data.quote_currency,
        "quote_owner_profile_picture": quote_owner[0].profile_picture,
    }

    return common_data


def get_multi_approval_request_details(client_id, logged_in_user, quote_id):
    """
    This function will return the approval request details for a given quote_id and logged_in_user
    """
    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "entity_key__contains": quote_id,
        "approver": logged_in_user,
        "status__in": [
            APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            APPROVAL_WORKFLOW_STATUS.REJECTED.value,
        ],
    }
    projection = [
        "approval_wf_instance_id",
        "stage_instance_id",
        "approval_request_id",
        "entity_key",
        "status",
        "completed_time",
        "request_data",
        "comments",
        "requested_time",
    ]
    order_by = ["-knowledge_begin_date"]
    approval_requests = get_approval_requests(
        client_id, filters=filters, projection=projection, order_by=order_by
    )

    if not approval_requests:
        return []

    common_data = get_approval_request_common_data(client_id, quote_id)

    approval_wf_instance_ids = set()
    entity_keys = set()
    requested_approval_requests = []

    for approval_request in approval_requests:
        approval_wf_instance_ids.add(approval_request["approval_wf_instance_id"])

        entity_keys.add(approval_request["entity_key"])

        if approval_request["status"] == APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
            requested_approval_requests.append(approval_request)

    filters = {
        "approval_wf_instance_id__in": approval_wf_instance_ids,
        "entity_key__in": entity_keys,
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
    }
    order_by = ["knowledge_begin_date"]
    projection = ["approval_wf_instance_id", "entity_key", "instance_data"]
    all_approval_instances = get_approval_wf_instances(
        client_id, filters=filters, order_by=order_by, projection=projection
    )

    approval_instance_map = {}
    for instance in all_approval_instances:
        key = (instance["approval_wf_instance_id"], instance["entity_key"])
        approval_instance_map[key] = instance

    stage_details_map = {}
    if requested_approval_requests:
        stage_instance_ids = list(
            set(req["stage_instance_id"] for req in requested_approval_requests)
        )

        filters = {
            "stage_instance_id__in": stage_instance_ids,
        }
        projection = ["stage_instance_id", "due_date"]
        all_stage_details = get_approval_stage_instances(
            client_id, filters=filters, projection=projection
        )

        for stage in all_stage_details:
            stage_details_map[stage["stage_instance_id"]] = stage

    response = []

    for approval_request in approval_requests:
        curr_request = {}
        curr_request["status"] = approval_request["status"]
        curr_request["exception_name"] = approval_request["request_data"][0][
            "rule_name"
        ]
        curr_request["requested_time"] = approval_request["requested_time"]
        curr_request["approval_request_id"] = approval_request["approval_request_id"]

        approval_instance_key = (
            approval_request["approval_wf_instance_id"],
            approval_request["entity_key"],
        )
        approval_instance = approval_instance_map.get(approval_instance_key)

        if approval_instance:
            curr_request["approval_message"] = approval_instance["instance_data"][
                "comments"
            ]
            curr_request["rule_group_name"] = approval_instance["instance_data"][
                "rule_group_name"
            ]

        if approval_request["status"] == APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
            stage_details = stage_details_map.get(approval_request["stage_instance_id"])
            if stage_details:
                curr_request["due_date"] = format_approval_due_date(
                    client_id, logged_in_user, stage_details["due_date"]
                )
        elif approval_request["status"] == APPROVAL_WORKFLOW_STATUS.APPROVED.value:
            curr_request["completed_time"] = approval_request["completed_time"]
        elif approval_request["status"] == APPROVAL_WORKFLOW_STATUS.REJECTED.value:
            curr_request["completed_time"] = approval_request["completed_time"]
            curr_request["rejection_reason"] = approval_request["comments"]

        response.append({**common_data, **curr_request})

    status_order = {
        APPROVAL_WORKFLOW_STATUS.REQUESTED.value: 0,
        APPROVAL_WORKFLOW_STATUS.REJECTED.value: 1,
        APPROVAL_WORKFLOW_STATUS.APPROVED.value: 2,
    }

    response.sort(key=lambda x: status_order.get(x["status"], 3))

    return response
