import logging

from commission_engine.accessors.client_accessor import get_client, is_notification_v2
from everstage_ddd.cpq.approvals.approval_accessor import ApprovalRuleGroupAccessor
from everstage_ddd.cpq.forms.accessors.form_accessor import AutoSaveFormAccessor
from everstage_ddd.cpq.quote import QuoteSelector
from everstage_ddd.cpq.quote.utils import get_duration_label
from everstage_infra.aws_infra.ecs import is_prod_env, is_staging_env
from slack_everstage.utils.slack_utils import get_site_url
from spm.accessors.approval_workflow_accessors import (
    ApprovalInstanceAccessor,
    ApprovalRequestsAccessor,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    APPROVAL_WORKFLOW_STATUS,
    ENTITY_KEY_DELIMETER,
    QUOTE_APPROVAL_EMAIL_TEMPLATE,
)
from spm.services.approval_workflow_services.approval_email_services import (
    get_instance_approvers_map,
    get_instance_notify_users_map,
    send_email_with_template,
)

logger = logging.getLogger(__name__)

quote_data = {
    "quote_id": "ES0001",  # Quote::quote_display_id
    "quote_name": "Quote for Synergy Systems",  # Quote::quote_name
    "opportunity_name": "EverICM for Synergy",
    "account_name": "Synergy Systems",
    "quote_total": "$35,000",  # Quote::quote_amount
    "quote_duration": "2 years",  # Quote::duration_value + Quote::duration_type
    "subscription_start_date": "01-01-2024",  # Quote::subscription_period_start_date
    "subscription_end_date": "31-12-2025",  # Quote::subscription_period_end_date
    "quote_expiry_date": "30 September 2024",  # Quote::valid_until
    "approval_expiry_date": "15 September 2024",  # Approval::due date
    "sales_rep": "Rob Thompson",  # Quote::created_by
    "approver_name": "Mike",  # Approval::approver_name
    "quote_url": "https://staging.everstage.com/cpq/quotes/1",  # Quote::quote_id or Quote::quote_display_id
}

approval_form_map = {
    "opportunity_name": "field3",
    "account_name": "field5",
}


def get_quote_id_quote_data_map(quote_data):
    quote_id_quote_data_map = {}
    for _quote_data in quote_data:
        _quote_id = str(_quote_data.quote_id)
        if _quote_id not in quote_id_quote_data_map:
            quote_id_quote_data_map[_quote_id] = {}
        quote_id_quote_data_map[_quote_id] = _quote_data

    return quote_id_quote_data_map


def get_template_data(
    quote_id_quote_data_map, quote_id, quote_id_form_data_map, currency_code_symbol_map
):
    opportunity_name_field = approval_form_map["opportunity_name"]
    account_name_field = approval_form_map["account_name"]
    subscription_start_date = quote_id_quote_data_map[quote_id].start_date
    if subscription_start_date:
        subscription_start_date = subscription_start_date.strftime("%d-%m-%Y")
    subscription_end_date = quote_id_quote_data_map[quote_id].end_date
    if subscription_end_date:
        subscription_end_date = subscription_end_date.strftime("%d-%m-%Y")
    quote_expiry_date = quote_id_quote_data_map[quote_id].valid_till
    if quote_expiry_date:
        quote_expiry_date = quote_expiry_date.strftime("%d %B %Y")
    duration_value = quote_id_quote_data_map[quote_id].duration_value
    duration_type = quote_id_quote_data_map[quote_id].duration_type

    template_data = {
        "quote_id": quote_id_quote_data_map[quote_id].quote_display_id,
        "quote_name": quote_id_quote_data_map[quote_id].quote_name,
        "opportunity_name": quote_id_form_data_map[quote_id].form_data.get(
            opportunity_name_field, ""
        ),
        "account_name": quote_id_form_data_map[quote_id].form_data.get(
            account_name_field, ""
        ),
        "quote_total": currency_code_symbol_map[
            quote_id_quote_data_map[quote_id].quote_currency
        ]
        + " "
        + str(quote_id_quote_data_map[quote_id].net_quote_total),
        "quote_duration": str(duration_value)
        + get_duration_label(duration_type, duration_value),
        "subscription_start_date": subscription_start_date,
        "subscription_end_date": subscription_end_date,
        "quote_expiry_date": quote_expiry_date,
        "quote_url": f"{get_site_url()}/cpq/quotes-details/{quote_id_quote_data_map[quote_id].quote_id}",  # Quote::quote_id or Quote::quote_display_id
    }
    return template_data


def get_form_and_quote_mapping(client_id, instances):
    quote_ids = [
        instance.entity_key.split(ENTITY_KEY_DELIMETER)[1] for instance in instances
    ]
    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quotes_by_quote_ids(quote_ids)

    quote_id_quote_data_map = get_quote_id_quote_data_map(quote_data)

    form_ids = []
    quote_id_form_id_map = {}
    for _quote in quote_data:
        form_ids.append(_quote.form_id)
        quote_id_form_id_map[str(_quote.quote_id)] = _quote.form_id

    auto_save_form_acc = AutoSaveFormAccessor(client_id)
    form_data = auto_save_form_acc.get_objects_by_form_ids(form_ids)
    form_id_form_data_map = {str(_value.form_id): _value for _value in form_data}

    quote_id_form_data_map = {}
    for _quote_id, _form_id in quote_id_form_id_map.items():
        quote_id_form_data_map[str(_quote_id)] = form_id_form_data_map[str(_form_id)]
    return quote_id_quote_data_map, quote_id_form_data_map


# ruff: noqa: PLR0915
# ruff: noqa: PLR0912
def send_quote_approval_creation_notification(
    client_id,
    instances,
    stage_instances,
    approval_requests,
    **kwargs,
):
    logger.info(
        "BEGIN: Sending quote approval creation notification for client_id: %s",
        client_id,
    )
    audit = kwargs.get("audit_data", {})

    client_email_notification_flag = get_client(client_id).cpq_client_notification
    if not client_email_notification_flag:
        logger.info(
            "Client %s has email notification flag turned off. Skipping email notifications.",
            client_id,
        )
        return
    all_employees = set()
    for ap_rq in approval_requests:
        all_employees.add(ap_rq.approver)
        all_employees.add(ap_rq.entity_key.split(ENTITY_KEY_DELIMETER)[0])
    all_employees = list(all_employees)

    all_employees.append(audit.get("updated_by")) if audit.get("updated_by") else None
    employee_records = EmployeeAccessor(client_id).get_all_employee_details(
        email_ids=all_employees,
        projection=[
            "employee_email_id",
            "first_name",
            "last_name",
            "user_role",
            "cpq_user_notification",
        ],
    )

    employee_email_id_record_map = {
        employee.get("employee_email_id"): employee for employee in employee_records
    }

    active_employee_emails: list[str] = EmployeeAccessor(
        client_id
    ).get_all_non_deactivated_users_email_list()

    currency_code_symbol_map = {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }

    quote_id_quote_data_map, quote_id_form_data_map = get_form_and_quote_mapping(
        client_id, instances
    )

    msg_from_rep_instance_map = {
        instance.entity_key.split(ENTITY_KEY_DELIMETER)[1]: instance.instance_data.get(
            "comments"
        )
        for instance in instances
    }

    stage_instance_map = {}
    for stage_instance in stage_instances:
        if stage_instance.stage_instance_id not in stage_instance_map:
            stage_instance_map[stage_instance.stage_instance_id] = None
        stage_instance_map[stage_instance.stage_instance_id] = stage_instance
    for approval_request in approval_requests:
        quote_id = approval_request.entity_key.split(ENTITY_KEY_DELIMETER)[1]
        quote_owner = approval_request.entity_key.split(ENTITY_KEY_DELIMETER)[0]
        template_data = {}
        if approval_request.status != APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
            logger.info("Approval request status is not requested")
            continue
        approver = approval_request.approver
        if approver not in active_employee_emails:
            logger.info(
                "Approver %s is not an active employee. Skipping notification.",
                approver,
            )
            continue
        approver_details = employee_email_id_record_map.get(approver, {})
        quote_owner_details = employee_email_id_record_map.get(quote_owner, {})

        if not approver_details.get("cpq_user_notification", False):
            logger.info(
                "Approver %s has opted out of notifications. Skipping notification.",
                approver,
            )
            continue

        approve_on_request = approval_request.approve_on_request

        template_data = get_template_data(
            quote_id_quote_data_map,
            quote_id,
            quote_id_form_data_map,
            currency_code_symbol_map,
        )
        due_date = stage_instance_map.get(
            approval_request.stage_instance_id, {}
        ).due_date
        formatted_due_date = due_date.strftime("%b %d, %Y") if due_date else None
        template_data["approval_expiry_date"] = formatted_due_date
        template_data["sales_rep"] = (
            f"{quote_owner_details.get('first_name')} {quote_owner_details.get('last_name')}",
        )
        template_data["approver_name"] = (
            f"{approver_details.get('first_name')} {approver_details.get('last_name')}",
        )
        template_data["additional_message"] = (
            stage_instance_map.get(approval_request.stage_instance_id, {}).notes,
        )
        approval_reasons = [
            details.get("rule_name") for details in approval_request.request_data
        ]
        template_data["message_from_rep"] = msg_from_rep_instance_map.get(quote_id)

        if approval_reasons:
            template_data["approval_reasons"] = approval_reasons
        if approve_on_request:
            # user has already approved, so just say no action needed from the
            # approver, we will maintain the approver's approval action
            updated_by = audit["updated_by"]
            updated_user = employee_email_id_record_map.get(updated_by, {})
            template_data["updated_by"] = (
                f"{updated_user.get('first_name')} {updated_user.get('last_name')}"
            )

        logger.info("Sending approval creation email to %s", approver)
        if is_prod_env() or is_staging_env():
            from everstage_ddd.cpq.notifications.notification_infra.cpq_approval_notifications.utils import (
                notify_new_quote_approval,
            )

            if is_notification_v2(client_id):
                notify_new_quote_approval(
                    client_id,
                    approver=approver,
                    template_data=template_data,
                    template_id=(
                        QUOTE_APPROVAL_EMAIL_TEMPLATE.PERSIST_APPROVAL.value
                        if approve_on_request
                        else QUOTE_APPROVAL_EMAIL_TEMPLATE.NEW_REQUEST.value
                    ),
                    email_type="quote_creation_notification",
                    approval_request_id=approval_request.approval_request_id,
                    quote_uuid=quote_id,
                )
            else:
                send_email_with_template(
                    client_id,
                    template_id=(
                        QUOTE_APPROVAL_EMAIL_TEMPLATE.PERSIST_APPROVAL.value
                        if approve_on_request
                        else QUOTE_APPROVAL_EMAIL_TEMPLATE.NEW_REQUEST.value
                    ),
                    to_email=approver,
                    template_data=template_data,
                    email_type="quote_creation_notification",
                )
    logger.info(
        "END: Sending quote approval creation notification for client_id: %s", client_id
    )


def send_quote_completion_notification(client_id, instances):
    logger.info("BEGIN: Sending completion notification for client_id: %s", client_id)
    email_requests_count_map = {}
    instance_id_record_map = {}
    all_payees = []

    instance_approvers_map = get_instance_approvers_map(client_id, instances, "approve")

    for instance in instances:
        approval_wf_instance_id = instance.approval_wf_instance_id
        instance_id_record_map[str(approval_wf_instance_id)] = instance

        if instance.notify_all_approvers_on_approve is True:
            all_approvers = (
                instance_approvers_map[str(approval_wf_instance_id)]
                if str(approval_wf_instance_id) in instance_approvers_map
                else []
            )
            for approver in all_approvers:
                email_requests_count_map[approver] = (
                    email_requests_count_map.get(approver, 0) + 1
                )

        for notifier in instance.notify_on_approve:
            email_requests_count_map[notifier] = (
                email_requests_count_map.get(notifier, 0) + 1
            )
        all_payees.append(instance.entity_key.split(ENTITY_KEY_DELIMETER)[0])

    all_employees = all_payees + list(email_requests_count_map.keys())

    all_employees_details = EmployeeAccessor(client_id).get_all_employee_details(
        all_employees,
        projection=[
            "employee_email_id",
            "first_name",
            "last_name",
            "time_zone",
            "user_role",
            "cpq_user_notification",
        ],
    )

    employee_email_id_record_map = {
        employee.get("employee_email_id"): employee
        for employee in all_employees_details
    }

    active_employee_emails: list[str] = EmployeeAccessor(
        client_id
    ).get_all_non_deactivated_users_email_list()
    currency_code_symbol_map = {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }

    instance_notify_users_map = get_instance_notify_users_map(
        instances, instance_approvers_map, "approve"
    )

    quote_id_quote_data_map, quote_id_form_data_map = get_form_and_quote_mapping(
        client_id, instances
    )

    all_workflows_approved = True

    quote_id = instances[0].entity_key.split(ENTITY_KEY_DELIMETER)[1]
    all_quote_approval_instances = ApprovalInstanceAccessor(
        client_id
    ).get_instance_by_partial_entity_key(
        quote_id, entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value
    )
    rule_group_ids = []
    for instance in all_quote_approval_instances:
        if instance["status"] != APPROVAL_WORKFLOW_STATUS.APPROVED.value:
            all_workflows_approved = False
        rule_group_ids.append(instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2])
    rule_groups = ApprovalRuleGroupAccessor(client_id).get_rule_groups_by_ids(
        rule_group_ids
    )
    rule_group_name_map = {
        str(_group.rule_group_id): _group.group_name for _group in rule_groups
    }

    for instance in instances:
        quote_id = instance.entity_key.split(ENTITY_KEY_DELIMETER)[1]
        instance_id = str(instance.approval_wf_instance_id)
        notify_on_approve_users = instance_notify_users_map[instance_id]
        quote_owner = instance.entity_key.split(ENTITY_KEY_DELIMETER)[0]
        for approver in notify_on_approve_users:
            template_data = {}
            if approver not in active_employee_emails:
                logger.info(
                    "Approver %s is not an active employee. Skipping notification.",
                    approver,
                )
                continue
            to_email_user_details = employee_email_id_record_map.get(approver, {})
            quote_owner_details = employee_email_id_record_map.get(quote_owner, {})

            if not to_email_user_details.get("cpq_user_notification", False):
                logger.info(
                    "Approver %s has opted out of notifications. Skipping notification.",
                    approver,
                )
                continue

            template_data = get_template_data(
                quote_id_quote_data_map,
                quote_id,
                quote_id_form_data_map,
                currency_code_symbol_map,
            )
            template_data["sales_rep"] = (
                f"{quote_owner_details.get('first_name')} {quote_owner_details.get('last_name')}"
            )
            template_data["approver_name"] = (
                f"{to_email_user_details.get('first_name')} {to_email_user_details.get('last_name')}"
            )

            template_data["all_workflows_approved"] = all_workflows_approved
            template_data["workflow_name"] = rule_group_name_map.get(
                instance.entity_key.split(ENTITY_KEY_DELIMETER)[2]
            )
            # send email notification to each approver
            logger.info("Sending quote approved email to %s", approver)
            if is_prod_env() or is_staging_env():
                send_email_with_template(
                    client_id,
                    template_id=QUOTE_APPROVAL_EMAIL_TEMPLATE.APPROVED.value,
                    to_email=approver,
                    template_data=template_data,
                    email_type="quote_completion_notification",
                )
    logger.info("END: Sending completion notification for client_id: %s", client_id)


def send_quote_reject_notification(
    client_id, instances, comments, instances_reject_users_map
):
    logger.info("BEGIN: Sending reject notification for client_id: %s", client_id)

    logger.info("BEGIN: Send notification to users when request is rejected")
    email_requests_count_map = {}
    instance_id_record_map = {}
    all_payees = []
    instance_approvers_map = get_instance_approvers_map(
        client_id, instances, "reject", logger
    )

    quote_id = instances[0].entity_key.split(ENTITY_KEY_DELIMETER)[1]
    all_quote_approval_instances = ApprovalInstanceAccessor(
        client_id
    ).get_instance_by_partial_entity_key(
        quote_id, entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value
    )
    all_workflows_rejected = True
    rule_group_ids = []
    for instance in all_quote_approval_instances:
        if instance["status"] != APPROVAL_WORKFLOW_STATUS.REJECTED.value:
            all_workflows_rejected = False
        rule_group_ids.append(instance["entity_key"].split(ENTITY_KEY_DELIMETER)[2])
    rule_groups = ApprovalRuleGroupAccessor(client_id).get_rule_groups_by_ids(
        rule_group_ids
    )
    rule_group_name_map = {
        str(_group.rule_group_id): _group.group_name for _group in rule_groups
    }

    for instance in instances:
        approval_wf_instance_id = instance.approval_wf_instance_id
        instance_id_record_map[str(approval_wf_instance_id)] = instance

        if instance.notify_all_approvers is True:
            all_approvers = (
                instance_approvers_map[str(approval_wf_instance_id)]
                if str(approval_wf_instance_id) in instance_approvers_map
                else []
            )
            for approver in all_approvers:
                email_requests_count_map[approver] = (
                    email_requests_count_map.get(approver, 0) + 1
                )

        for notifier in instance.notify_on_reject:
            email_requests_count_map[notifier] = (
                email_requests_count_map.get(notifier, 0) + 1
            )

        all_payees.append(instance.entity_key.split(ENTITY_KEY_DELIMETER)[0])
        if str(approval_wf_instance_id) in instances_reject_users_map:
            all_payees.append(instances_reject_users_map[str(approval_wf_instance_id)])

    all_employees = all_payees + list(email_requests_count_map.keys())

    all_employees_details = EmployeeAccessor(client_id).get_all_employee_details(
        all_employees,
        projection=[
            "employee_email_id",
            "first_name",
            "last_name",
            "time_zone",
            "user_role",
            "cpq_user_notification",
        ],
    )

    employee_email_id_record_map = {
        employee.get("employee_email_id"): employee
        for employee in all_employees_details
    }
    active_employee_emails: list[str] = EmployeeAccessor(
        client_id
    ).get_all_non_deactivated_users_email_list()

    currency_code_symbol_map = {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }

    instance_notify_users_map = get_instance_notify_users_map(
        instances, instance_approvers_map, "reject", logger
    )
    logger.info("Users who rejected the instances map - %s", instances_reject_users_map)

    quote_id_quote_data_map, quote_id_form_data_map = get_form_and_quote_mapping(
        client_id, instances
    )

    for instance in instances:
        quote_id = instance.entity_key.split(ENTITY_KEY_DELIMETER)[1]
        instance_id = str(instance.approval_wf_instance_id)
        notify_on_reject_users = instance_notify_users_map[instance_id]
        quote_owner = instance.entity_key.split(ENTITY_KEY_DELIMETER)[0]
        for approver in notify_on_reject_users:
            template_data = {}
            if approver not in active_employee_emails:
                logger.info(
                    "Approver %s is not an active employee. Skipping notification.",
                    approver,
                )
                continue

            user_who_rejected = instances_reject_users_map.get(instance_id)
            rejected_user_details = employee_email_id_record_map.get(
                user_who_rejected, {}
            )
            quote_owner_details = employee_email_id_record_map.get(quote_owner, {})
            to_user_details = employee_email_id_record_map.get(approver, {})

            if not to_user_details.get("cpq_user_notification", False):
                logger.info(
                    "Approver %s has opted out of notifications. Skipping notification.",
                    approver,
                )
                continue

            # send email notification to each approver
            template_data = get_template_data(
                quote_id_quote_data_map,
                quote_id,
                quote_id_form_data_map,
                currency_code_symbol_map,
            )
            template_data["sales_rep"] = (
                f"{quote_owner_details.get('first_name')} {quote_owner_details.get('last_name')}"
            )
            template_data["approver_name"] = (
                f"{rejected_user_details.get('first_name')} {rejected_user_details.get('last_name')}"
            )
            template_data["rejection_reason"] = comments
            template_data["all_workflows_rejected"] = all_workflows_rejected
            template_data["workflow_name"] = rule_group_name_map.get(
                instance.entity_key.split(ENTITY_KEY_DELIMETER)[2]
            )
            logger.info("Sending quote rejected email to %s", approver)
            if is_prod_env() or is_staging_env():
                send_email_with_template(
                    client_id,
                    template_id=QUOTE_APPROVAL_EMAIL_TEMPLATE.REJECTED.value,
                    to_email=approver,
                    template_data=template_data,
                    email_type="quote_rejection_notification",
                )
    logger.info("END: Sending reject notification for client_id: %s", client_id)


def send_quote_approval_reminder_notification(
    client_id, approval_instance, stage_instance, approval_requests
):
    opportunity_name_field = approval_form_map["opportunity_name"]
    account_name_field = approval_form_map["account_name"]
    payee_email = approval_instance["entity_key"].split(ENTITY_KEY_DELIMETER)[0]
    all_approvers_to_remind = {
        rq.get("approver"): {
            "approval_request_id": rq.get("approval_request_id"),
            "stage_instance_id": rq.get("stage_instance_id"),
            "request_data": rq.get("request_data"),
        }
        for rq in approval_requests
    }

    all_employees = []
    all_employees.append(payee_email)
    all_employees = all_employees + list(all_approvers_to_remind.keys())

    all_employees_details = EmployeeAccessor(client_id).get_all_employee_details(
        all_employees,
        projection=[
            "employee_email_id",
            "first_name",
            "last_name",
            "time_zone",
            "user_role",
            "cpq_user_notification",
        ],
    )
    active_employee_emails: list[str] = EmployeeAccessor(
        client_id
    ).get_all_non_deactivated_users_email_list()

    employee_email_id_record_map = {
        employee.get("employee_email_id"): employee
        for employee in all_employees_details
    }
    due_date = stage_instance["due_date"]
    formatted_due_date = due_date.strftime("%b %d, %Y") if due_date else None
    additional_msg = stage_instance["notes"]
    logger.info("Sending reminder email to  %s stages", len(stage_instance))
    quote_id = approval_instance["entity_key"].split(ENTITY_KEY_DELIMETER)[1]
    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(str(quote_id))
    form_id = quote_data.form_id
    auto_save_form_acc = AutoSaveFormAccessor(client_id)
    form_data = auto_save_form_acc.get_last_saved_form(str(form_id))

    currency_code_symbol_map = {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }

    msg_from_rep = approval_instance.get("instance_data", {}).get("comments")

    for approver, request_details in all_approvers_to_remind.items():
        template_data = {}
        logger.info(
            "Sending reminder email to %s for approval request %s",
            approver,
            request_details["approval_request_id"],
        )
        if approver not in active_employee_emails:
            logger.info(
                "Approver %s is not an active employee. Skipping notification.",
                approver,
            )
            continue
        approver_details = employee_email_id_record_map.get(approver, {})
        quote_owner_details = employee_email_id_record_map.get(payee_email, {})

        if not approver_details.get("cpq_user_notification", False):
            logger.info(
                "Approver %s has opted out of notifications. Skipping notification.",
                approver,
            )
            continue

        duration_value = quote_data.duration_value
        duration_type = quote_data.duration_type

        # send email notification to each approver
        logger.info("Sending quote reminder email to %s", approver)

        template_data = {
            "quote_id": quote_data.quote_display_id,
            "quote_name": quote_data.quote_name,
            "opportunity_name": form_data.form_data.get(opportunity_name_field, ""),
            "account_name": form_data.form_data.get(account_name_field, ""),
            "quote_total": currency_code_symbol_map[quote_data.quote_currency]
            + " "
            + str(quote_data.net_quote_total),
            "quote_duration": str(quote_data.duration_value)
            + get_duration_label(duration_type, duration_value),
            "subscription_start_date": (
                quote_data.start_date.strftime("%b %d, %Y")
                if quote_data.start_date
                else None
            ),
            "subscription_end_date": (
                quote_data.end_date.strftime("%b %d, %Y")
                if quote_data.end_date
                else None
            ),
            "quote_expiry_date": (
                quote_data.valid_till.strftime("%b %d, %Y")
                if quote_data.valid_till
                else None
            ),
            "approval_expiry_date": formatted_due_date,
            "sales_rep": f"{quote_owner_details.get('first_name')} {quote_owner_details.get('last_name')}",
            "approver_name": f"{approver_details.get('first_name')} {approver_details.get('last_name')}",
            "quote_url": f"{get_site_url()}/cpq/quotes-details/{quote_data.quote_id}",
            "additional_message": additional_msg,
            "message_from_rep": msg_from_rep,
        }

        approval_reasons = [
            _data.get("rule_name") for _data in request_details["request_data"]
        ]
        if approval_reasons:
            template_data["approval_reasons"] = approval_reasons
        if is_prod_env() or is_staging_env():
            send_email_with_template(
                client_id,
                template_id=QUOTE_APPROVAL_EMAIL_TEMPLATE.NEW_REQUEST.value,
                to_email=approver,
                template_data=template_data,
                email_type="quote_reminder_notification",
            )


def send_quote_approval_daily_reminder_notification(client_id):
    from everstage_ddd.cpq.notifications.notification_infra.cpq_approval_notifications.utils import (
        notify_pending_quote_approvals,
    )

    # Fetching all employee records to get their name and notification status
    employee_records = list(EmployeeAccessor(client_id).get_all_employees_qs())
    email_id_emp_map = {emp.employee_email_id: emp for emp in employee_records}

    approver_count_map = ApprovalRequestsAccessor(
        client_id
    ).get_pending_requests_count_for_approvers_by_entity_type(
        entity_type=APPROVAL_ENTITY_TYPES.QUOTE.value
    )
    for approver_data in approver_count_map:
        approver = approver_data["approver"]

        if approver not in email_id_emp_map:
            logger.info("%s is not active, skipping notifications", approver)
            continue
        # send email notification to each approver
        template_data = {}
        template_data["approver_name"] = (
            f"{email_id_emp_map[approver].first_name} {email_id_emp_map[approver].last_name}"
        )
        template_data["pending_approval_count"] = approver_data.get("count")
        template_data["pending_quotes_list"] = (
            f"{get_site_url()}/cpq/quotes?pending_approvals=true"
        )
        logger.info("Sending daily reminder email to %s", approver)
        if is_prod_env() or is_staging_env():
            if is_notification_v2(client_id):
                notify_pending_quote_approvals(
                    client_id,
                    approver=approver,
                    template_data=template_data,
                )
            else:
                send_email_with_template(
                    client_id,
                    template_id=QUOTE_APPROVAL_EMAIL_TEMPLATE.DAILY_REMINDER.value,
                    to_email=approver,
                    template_data=template_data,
                    email_type="quote_daily_reminder",
                )
