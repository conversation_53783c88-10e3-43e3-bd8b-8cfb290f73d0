"""Rule spec
{
        source_field: (Derieved from condition, so that we know when to evaluate this rule)
        ui_component: (should get parent_child relationship and each component separately)
        logical_structure:
        condition:
        actions:
        destination_fields: (Fields with set_value in action)
}"""

import logging
from copy import deepcopy

import pandas as pd
import pydash
from django.utils import timezone
from vectorized_evaluate import vectorized_evaluate

from commission_engine.accessors.client_accessor import get_client_settings
from commission_engine.utils.criteria_calculator_utils import create_ast
from everstage_ddd.cpq.approvals import (
    get_approvals_source_rule_fields,
    quote_line_item_fields,
)
from everstage_ddd.cpq.enums import Status

from ...quote.service.quote_line_item_service import get_data_for_rules
from ..accessors.form_accessor import (
    AutoSaveFormAccessor,
    EverstageFormAccessor,
    FormBuilderAccessor,
    FormRuleAccessor,
)
from ..everstage_form import default_form
from .form_action import (
    AddPrint,
    AddQuoteLineItem,
    DoNothing,
    MakeMandatory,
    <PERSON>R<PERSON>Only,
    MakeVisible,
    RemoveQuo<PERSON>Line<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Set<PERSON><PERSON><PERSON>,
    SetValue,
    UpdateQuoteLineItem,
)
from .form_rules_helper import get_source_field_name_mapping, get_source_fields_used
from .form_spec_navigator import FormSpecNavigator
from .form_types import FormModel, RuleModel
from .look_up_data_source import LookUpDatasheetData

logger = logging.getLogger(__name__)

FormActionDict = {
    "MakeVisible": MakeVisible,
    "SetEmpty": SetEmpty,
    "MakeMandatory": MakeMandatory,
    "MakeReadOnly": MakeReadOnly,
    "SetOptions": SetOptions,
    "SetValue": SetValue,
    "AddPrint": AddPrint,
    "AddQuoteLineItem": AddQuoteLineItem,
    "RemoveQuoteLineItem": RemoveQuoteLineItem,
    "UpdateQuoteLineItem": UpdateQuoteLineItem,
    "DoNothing": DoNothing,
}

LookupDict = {
    "datasheet_data": LookUpDatasheetData,
}


# def get_data_from_spec(form_spec):
# form_model = FormModel(**form_spec)
# field_dict = {}
# sections = form_model.sections
# for section_model in list(sections.values()):
#     section = section_model
#     fields = section.fields
#     if fields:
#         for field_model in list(fields.values()):
#             val = field_model.value if field_model.value else None
#             if (
#                 isinstance(field_model.value, list)
#                 and field_model.field_type != "table"
#             ):
#                 val = ",".join(field_model.value)
#             field_dict[field_model.id] = [val]
#     sub_sections = section_model.sub_sections
#     if sub_sections:
#         for sub_section_model in list(sub_sections.values()):
#             fields = sub_section_model.fields
#             for field_model in list(fields.values()):
#                 val = field_model.value if field_model.value else None
#                 if (
#                     isinstance(field_model.value, list)
#                     and field_model.field_type != "table"
#                 ):
#                     val = ",".join(field_model.value)
#                 field_dict[field_model.id] = [val]
# return field_dict


def get_form_spec_from_db(client_id):
    from ..everstage_form import everstage_form

    client_settings = get_client_settings(client_id)
    form_spec = client_settings.get("cpq_settings", {}).get("form_spec", everstage_form)
    return form_spec


def get_source_fields_rules_dict(rules):
    rules_dict = {}
    for rule in rules:
        source_fields = rule["source_fields"]
        for source_field in source_fields:
            if source_field in rules_dict:
                rules_dict[source_field].append(rule)
            else:
                rules_dict[source_field] = [rule]
    return rules_dict


def prepare_form_spec(form_data, form_spec):
    """Fill in value for each field in form_spec from form_data"""
    if not form_data:
        return form_spec
    new_form_spec = deepcopy(form_spec)
    form_navigator = FormSpecNavigator(new_form_spec)
    for field_id, value in form_data.items():
        field = form_navigator.get_field(field_id)
        field["value"] = value
    return new_form_spec


def prepare_df_from_form_data(form_data):
    field_dict = {}
    for field_id, value in form_data.items():
        if isinstance(value, list):
            field_dict[field_id] = ",".join(value)
        field_dict[field_id] = [value]
    df = pd.DataFrame(field_dict)
    return df


# ruff: noqa: PLR0913
def prepare_lookup_options(
    client_id, field_id, form_data, params, quote_id, form_builder_id
):
    from .form_builder_service import get_form_spec_by_quote_id

    form_spec = get_form_spec_by_quote_id(client_id, quote_id)
    df = prepare_df_from_form_data(form_data)
    form_navigator = FormSpecNavigator(form_spec)
    field = form_navigator.get_field(field_id)
    lookup_spec = field["options_lookup_spec"]
    source_class = LookupDict[lookup_spec["source"]]
    source_obj = source_class(client_id, lookup_spec, quote_id)
    data = source_obj.get_data(df, params)
    return data


def get_rules_dependent_and_source_fields(rules):
    source_rule_fields = []
    dependent_fields = {}

    for rule in rules:
        source_rule_fields.extend(rule["source_fields"])
        source_fields = rule["source_fields"]
        dest_fields = rule["destination_fields"]
        for dest_field in dest_fields:
            if dest_field not in dependent_fields:
                dependent_fields[dest_field] = []
            dependent_fields[dest_field].extend(source_fields)

    has_changes = True
    while has_changes:
        has_changes = False
        for dest_field, source_fields in list(dependent_fields.items()):
            indirect_sources = set()
            for source_field in source_fields:
                if source_field in dependent_fields:
                    indirect_sources.update(dependent_fields[source_field])

            new_sources = indirect_sources - set(dependent_fields[dest_field])
            if new_sources:
                dependent_fields[dest_field].extend(new_sources)
                has_changes = True

    for field in dependent_fields:
        dependent_fields[field] = list(set(dependent_fields[field]))

    return source_rule_fields, dependent_fields


def get_form_spec(client_id, form_builder_id):
    # from ..address_sample import addr, form_table
    form_rules = get_form_rules(client_id, form_builder_id, status=Status.ACTIVE.value)

    source_rule_fields, dependent_fields = get_rules_dependent_and_source_fields(
        form_rules
    )

    form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)
    res = {}
    res["form_spec"] = form_builder.form_spec

    approval_source_fields = get_approvals_source_rule_fields(
        client_id, form_builder_id=form_builder_id
    )

    res["source_rule_fields"] = list(set(source_rule_fields + approval_source_fields))
    res["dependent_fields"] = dependent_fields
    res["table_spec"] = {}

    # dependent_fields = {
    #     "field9": ["field8"],
    #     "field10": ["field8"],
    #     "field11": ["field8"],
    #     "field12": ["field8"],
    #     "field13": ["field8"],
    #     "field14": ["field8"],
    #     "field15": ["field8"],
    #     "field16": ["field6"],
    #     "field24": ["field17", "field18"],
    #     # "field5": ["field6"]
    # }
    # res["dependent_fields"] = dependent_fields
    # res["table_spec"] = {1: form_table}

    return res


def evaluate_then_blocks(client_id, then_blocks, form_spec):
    for then_block in then_blocks:
        action_type = then_block["function"]
        form_component = then_block["form_component"]
        component_id = then_block["component_id"]
        value = then_block["value"]
        action_class = FormActionDict[action_type]
        action_obj = action_class(client_id, form_spec)
        form_spec = action_obj.make_change(form_component, component_id, value)
    return form_spec


def evaluate_table_then_blocks(client_id, then_blocks, form_spec, matching_data):
    for then_block in then_blocks:
        action_type = then_block["function"]
        form_component = then_block["form_component"]
        component_id = then_block["component_id"]
        value = {"value": then_block["value"]}
        value["data"] = matching_data.to_dict(orient="records")
        action_class = FormActionDict[action_type]
        action_obj = action_class(client_id, form_spec)
        form_spec = action_obj.make_change(form_component, component_id, value)
    return form_spec


def evaluate_formula(client_id, then_block, dest_field, form_spec, df):
    """
    then_block = {
                        "function": "SetValue",
                        "form_component": "field",
                        "component_id": "field24",
                        "value": "",
                    }
    dest_field = "field24"
    form_spec -> form_spec with current field values
    the formula to be evaluated will be in field spec.
    """
    action_type = then_block["function"]
    form_component = then_block["form_component"]

    form_navigator = FormSpecNavigator(form_spec)
    field = form_navigator.get_field(dest_field)

    ast = create_ast(deepcopy(field["formula"]))["ast"]
    value = vectorized_evaluate(df, ast, {})[0]

    action_obj = FormActionDict[action_type](client_id, form_spec)
    return action_obj.make_change(form_component, dest_field, value)


def evaluate_options_for_lookup(client_id, dest_field, form_spec, df, quote_id):
    form_navigator = FormSpecNavigator(form_spec)
    field = form_navigator.get_field(dest_field)
    lookup_spec = field["options_lookup_spec"]

    source_obj = LookupDict[lookup_spec["source"]](client_id, lookup_spec, quote_id)
    data = source_obj.get_data(df, {})

    data_type = field["data_type"]
    is_selection_field = field["field_type"] in [
        "single_select",
        "multi_select",
        "lookup",
    ]
    action_type = "SetOptions" if is_selection_field else "SetValue"
    if len(data) == 1:
        action_type = "SetValue"

    if not is_selection_field or len(data) == 1:
        if data_type == "number":
            data = (
                float(data[0]["value"])
                if (data and "value" in data[0] and data[0]["value"])
                else 0
            )
        else:
            data = data[0]["value"] if (data and "value" in data[0]) else ""
    action_obj = FormActionDict[action_type](client_id, form_spec)
    return action_obj.make_change("field", dest_field, data)


def merge_quote_fields_with_line_item_fields(
    client_id: int,
    quote_fields_df,
    quote_id: str,
):
    from everstage_ddd.cpq.quote import get_data_for_rules

    line_items = get_data_for_rules(client_id, quote_id)
    line_items_df = pd.json_normalize(line_items, sep=".")
    if not line_items_df.empty:
        fields_df = pd.merge(line_items_df, quote_fields_df, how="cross")
        return fields_df
    return quote_fields_df


def evaluate_form_spec(client_id, form_spec, rules, form_builder_id):
    for rule in rules:
        form_model = FormModel(**form_spec)
        form_navigator = FormSpecNavigator(form_spec)
        quote_id_field = form_navigator.get_field("field1")
        quote_id = quote_id_field["value"]
        quote_fields_df = form_model.get_data_df
        fields_df = merge_quote_fields_with_line_item_fields(
            client_id,
            quote_fields_df,
            quote_id,
        )
        rule_model = RuleModel(**rule)
        is_formula = rule_model.is_rule_a_formula
        is_lookup = rule_model.is_rule_a_lookup
        actions = rule["actions"]
        source_fields = rule_model.source_fields
        destination_fields = rule_model.destination_fields
        source_quote_fields_used = set(source_fields) - set(quote_line_item_fields)
        destination_quote_fields_used = set(destination_fields) - set(
            quote_line_item_fields
        )
        all_quote_fields = form_navigator.field_section_map
        # if any field in quote_fields_used is not in all_quote_fields, then continue the loop
        if not all(
            field in all_quote_fields for field in source_quote_fields_used
        ) or not all(
            field in all_quote_fields for field in destination_quote_fields_used
        ):
            logger.info(
                f"Rule id {rule['rule_id']} is not applicable for the current form spec"
            )
            continue
        for action in actions:
            if "condition" in action:
                condition = deepcopy(action["condition"])
                ast = create_ast(condition)["ast"]
                res = vectorized_evaluate(fields_df, ast, {})
                then_blocks = action["then"]
                if res[0]:
                    if is_formula:
                        dest_field = rule["destination_fields"][0]
                        then_block = then_blocks[0]
                        form_spec = evaluate_formula(
                            client_id, then_block, dest_field, form_spec, fields_df
                        )
                    elif is_lookup:
                        dest_fields = rule["destination_fields"]
                        for dest_field in dest_fields:
                            form_spec = evaluate_options_for_lookup(
                                client_id,
                                dest_field,
                                form_spec,
                                fields_df,
                                quote_id,
                            )
                    else:
                        form_spec = evaluate_then_blocks(
                            client_id, then_blocks, form_spec
                        )
                    break
            elif "default" in action:
                default_blocks = action["default"]
                form_spec = evaluate_then_blocks(client_id, default_blocks, form_spec)
    return form_spec


def get_form_rules(client_id, form_builder_id, status=None):
    form_builder_acc = FormRuleAccessor(client_id)
    form_rules = form_builder_acc.get_form_rules_by_form_builder_id(
        form_builder_id, status
    )
    rules = []
    for _rule in form_rules:
        rule = {
            "rule_id": str(_rule["form_rule_id"]),
            "source_fields": _rule["source_fields"],
            "destination_fields": _rule["destination_fields"],
            "trigger": _rule["trigger"],
            "actions": _rule["actions"],
        }
        if _rule["is_lookup"]:
            rule["is_lookup"] = True
        if _rule["is_formula"]:
            rule["is_formula"] = True
        rules.append(rule)

    return rules


def evaluate_table_rules(client_id, form_spec, rules):
    form_navigator = FormSpecNavigator(form_spec)
    quote_id_field = form_navigator.get_field("field1")
    quote_id = quote_id_field["value"]
    data = get_data_for_rules(client_id, quote_id)
    data_df = pd.DataFrame(data)
    logger.info(f"data_df - {data_df}")
    for rule in rules:
        actions = rule["actions"]
        for action in actions:
            if "condition" in action:
                condition = action["condition"]
                ast = create_ast(condition)["ast"]
                data_df["res"] = vectorized_evaluate(data_df, ast, {})
                matching_data_df = data_df[data_df["res"]]
                then_blocks = action["then"]
                if not matching_data_df.empty:
                    form_spec = evaluate_table_then_blocks(
                        client_id, then_blocks, form_spec, matching_data_df
                    )
                    break
            elif "default" in action:
                default_blocks = action["default"]
                form_spec = evaluate_then_blocks(client_id, default_blocks, form_spec)


def evaluate_rule_spec(  # noqa : PLR0913
    client_id,
    form_builder_id,
    changed_field,
    form_data=None,
    curr_form_spec=None,
) -> dict:

    form_spec = curr_form_spec
    if form_data:
        form_spec = prepare_form_spec(form_data, curr_form_spec)

    form_rules = get_form_rules(client_id, form_builder_id, status=Status.ACTIVE.value)
    source_field_rules_dict = get_source_fields_rules_dict(form_rules)
    selected_rules = source_field_rules_dict.get(changed_field, [])
    # always_run_rules = source_field_rules_dict.get("quote_line_item", [])
    # selected_rules.extend(always_run_rules)
    if changed_field == "quote_line_item":
        field = {}
    else:
        form_navigator = FormSpecNavigator(form_spec)
        field = (
            form_navigator.get_field(changed_field)
            if changed_field in form_navigator.field_section_map
            else {}
        )
    logger.info(f"selected_rules - {selected_rules}")
    if "table_id" in field:
        form_spec = evaluate_table_rules(client_id, form_spec, selected_rules)
    else:
        dependent_fields = []
        for rule in selected_rules:
            dependent_fields.extend(rule["destination_fields"])
            dependent_fields = list(set(dependent_fields))
        form_spec = evaluate_form_spec(
            client_id, form_spec, selected_rules, form_builder_id
        )
        for field in dependent_fields:
            f_selected_rules = source_field_rules_dict.get(field, [])
            if f_selected_rules:
                form_spec = evaluate_rule_spec(
                    client_id,
                    form_builder_id,
                    field,
                    form_data=None,
                    curr_form_spec=form_spec,
                )
    return form_spec


def save_lookup_value(client_id, field_id, selected_value, quote_id):
    from .form_builder_service import get_form_spec_by_quote_id

    form_spec = get_form_spec_by_quote_id(client_id, quote_id)
    form_navigator = FormSpecNavigator(form_spec)
    field = form_navigator.get_field(field_id)
    field["value"] = selected_value
    return form_spec


def auto_save_form(client_id, form_builder_id, form_id, form_data):
    auto_save_acc = AutoSaveFormAccessor(client_id)
    auto_save_acc.insert_object(form_data, form_id, form_builder_id)
    return {
        "status": "success",
    }


def get_section_dict(form_spec, form_data):
    form_navigator = FormSpecNavigator(form_spec)
    section_order_key = form_navigator.get_section_order_key()
    sections = form_spec[section_order_key]

    sections_data = {}
    for section_id in sections:
        section = form_navigator.get_section(section_id)
        section_data = deepcopy(section)
        sec_fields = section_data.get("fields")
        if sec_fields:
            for field_id, field in sec_fields.items():
                field["value"] = form_data.get(field_id)
        sub_sections = section_data.get("sub_sections")
        if sub_sections:
            for sub_sec in list(sub_sections.values()):
                sub_sec_fields = sub_sec.get("fields")
                if sub_sec_fields:
                    for field_id, field in sub_sec_fields.items():
                        field["value"] = form_data.get(field_id)
        sections_data[section_id] = section_data

    section_key = form_navigator.get_section_key()
    pydash.set_(form_spec, section_key, sections_data)

    return form_spec


def submit_form(client_id, form_builder_id, form_id, params):
    form_spec = params["form_spec"]
    form_data = params["form_data"]
    knowledge_date = timezone.now()
    form_acc = EverstageFormAccessor(client_id)
    existing_records = form_acc.get_objects(form_builder_id, form_id)
    created_by = params["user"]
    updated_by = params["user"]
    additional_details = params["additional_details"]
    created_at = knowledge_date
    if existing_records:
        created_by = existing_records[0].created_by
        created_at = existing_records[0].created_at

    submit_form_data = {}
    form_spec = get_section_dict(form_spec, form_data)
    submit_form_data["form_data"] = form_spec

    submit_form_data["form_id"] = form_id
    submit_form_data["form_builder_id"] = form_builder_id
    submit_form_data["status"] = "submitted"
    submit_form_data["created_by"] = created_by
    submit_form_data["updated_by"] = updated_by
    submit_form_data["created_at"] = created_at
    submit_form_data["knowledge_begin_date"] = knowledge_date
    submit_form_data["additional_details"] = additional_details
    submit_form_data["client_id"] = client_id
    form_acc.insert_object(knowledge_date, submit_form_data)

    return {"status": "success"}


def get_all_forms(client_id):
    form_acc = EverstageFormAccessor(client_id)
    section_details = form_acc.get_all_first_sections()
    # we should get form builder details also
    res = []
    cnt = 1
    for section in section_details:
        data = {}
        data["form_id"] = section.form_id
        data["form_builder_id"] = section.form_builder_id
        data["status"] = section.status
        data["created_at"] = section.created_at
        data["created_by"] = section.created_by
        data["updated_by"] = section.updated_by
        data["updated_at"] = section.knowledge_begin_date
        data["form_builder_name"] = "Form Builder Name " + str(cnt)
        data["form_name"] = "Form Name " + str(cnt)
        cnt += 1
        res.append(data)
    return res


def validate_form_data(client_id, form_data, quote_id):
    from .form_builder_service import get_form_spec_by_quote_id

    form_spec = get_form_spec_by_quote_id(client_id, quote_id)
    form_navigator = FormSpecNavigator(form_spec)
    mandatory_fields = form_navigator.get_mandatory_fields()
    errors = {}
    for field in mandatory_fields:
        value = pydash.get(form_data, field)
        if value is None or (isinstance(value, str) and not value.strip()):
            errors[field] = "Field is missing"
    return errors


def get_auto_complete_context(client_id, variables, form_builder_id, **kwargs):
    from commission_engine.services.expression_designer.autocomplete_context import (
        get_operator_suggestions,
        restructure_function_list,
    )
    from commission_engine.services.expression_designer.expression_designer_utils import (
        cpq_functions,
    )
    from everstage_ddd.cpq.forms import FormSpecNavigator
    from everstage_ddd.cpq.forms.accessors.form_accessor import FormBuilderAccessor
    from spm.accessors.variable_accessor import VariableDataTypeAccessor

    databook_id = kwargs.get("databook_id")
    datasheet_id = kwargs.get("datasheet_id")

    all_data_types = VariableDataTypeAccessor().get_all_data_types()
    variable_dtype_map = {
        data_type.data_type.lower(): data_type.id for data_type in all_data_types
    }
    form_builder = FormBuilderAccessor(client_id).get_form_builder(
        form_builder_id=form_builder_id
    )
    form_spec = form_builder.form_spec if form_builder else default_form
    form_navigator = FormSpecNavigator(form_spec)
    ds_suggestions = []
    source_suggestions = []
    operator_suggestions = []
    function_suggestions = []

    if "datasheet_variables" in variables and databook_id and datasheet_id:
        ds_suggestions = get_datasheet_variables(client_id, databook_id, datasheet_id)
    if "quote_fields" in variables:
        get_all_fields = form_navigator.get_all_fields()
        for field in get_all_fields:
            field_data = form_navigator.get_field(field)
            source_suggestions.append(
                {
                    "group": "FORM_VARIABLES",
                    "label": field_data["label"],
                    "value": field_data["id"],
                    "meta": {
                        "data_type": (
                            field_data["data_type"].capitalize()
                            if field_data["data_type"] != "number"
                            else "Integer"
                        ),
                        "data_type_id": variable_dtype_map.get(
                            (
                                "integer"
                                if field_data.get("data_type", "string") == "number"
                                else field_data.get("data_type", "string")
                            ),
                            4,
                        ),
                    },
                }
            )

    if "line_item_fields" in variables:
        source_suggestions.extend(get_line_item_fields(client_id, variable_dtype_map))
    if "tier_fields" in variables:
        source_suggestions.extend(get_tier_fields(client_id, variable_dtype_map))
    if "user_fields" in variables:
        source_suggestions.extend(get_user_fields(variable_dtype_map))
    if "quote_properties" in variables:
        source_suggestions.extend(get_quote_property_fields(variable_dtype_map))
    if "operators" in variables:
        operator_suggestions = get_operator_suggestions()
    if "functions" in variables:
        function_suggestions = restructure_function_list(cpq_functions)
        if "datasheet_variables" in variables:
            # As we do not support GetLineItemValue for Lookup fields.
            function_suggestions = [
                func
                for func in function_suggestions
                if func["value"] != "GetLineItemValue"
            ]
    return (
        ds_suggestions
        + source_suggestions
        + operator_suggestions
        + function_suggestions
    )


def get_line_item_fields(client_id, variable_dtype_map):
    line_item_fields = {
        "start_date": "Phase Start Date",
        "end_date": "Phase End Date",
        "phase_name": "Phase Name",
        "product_name": "Product Name",
        "quantity": "Quantity",
        "list_unit_price": "List Unit Price",
        "net_unit_price": "Net Unit Price",
        "discount_percent": "Discount Percentage",
        "prorated_net_total": "Line Item Total",
    }
    client_settings = get_client_settings(client_id)
    ignored_line_item_fields = client_settings.get("cpq_settings", {}).get(
        "ignored_line_item_fields", []
    )
    fields = []
    for field, label in line_item_fields.items():
        if field in ignored_line_item_fields:
            continue

        _field = {
            "group": "FORM_VARIABLES",
            "label": label,
            "value": field,
            "meta": {},
        }
        if field in ["start_date", "end_date"]:
            _field["meta"]["data_type"] = "Date"
            _field["meta"]["data_type_id"] = variable_dtype_map["date"]
        elif field in ["phase_name", "product_name"]:
            _field["meta"]["data_type"] = "String"
            _field["meta"]["data_type_id"] = variable_dtype_map["string"]
        elif field in [
            "quantity",
            "list_unit_price",
            "net_unit_price",
            "discount_percent",
            "prorated_net_total",
        ]:
            _field["meta"]["data_type"] = "Integer"
            _field["meta"]["data_type_id"] = variable_dtype_map["integer"]
        elif field.startswith("pricepoint_data"):
            tiered_value = field.split(".")
            if tiered_value[1] in ["show_future_tiers"]:
                _field["meta"]["data_type"] = "Boolean"
                _field["meta"]["data_type_id"] = variable_dtype_map["boolean"]
            elif tiered_value[1] in ["pricing_method", "currency"]:
                _field["meta"]["data_type"] = "String"
                _field["meta"]["data_type_id"] = variable_dtype_map["string"]
            else:
                _field["meta"]["data_type"] = "Integer"
                _field["meta"]["data_type_id"] = variable_dtype_map["integer"]
        fields.append(_field)

    return fields


def line_item_return_columns(client_id):
    from spm.accessors.variable_accessor import VariableDataTypeAccessor

    source_suggestions = []
    all_data_types = VariableDataTypeAccessor().get_all_data_types()
    variable_dtype_map = {
        data_type.data_type.lower(): data_type.id for data_type in all_data_types
    }
    source_suggestions.extend(get_line_item_fields(client_id, variable_dtype_map))
    source_suggestions.extend(get_tier_fields(client_id, variable_dtype_map))

    return source_suggestions


def get_tier_fields(client_id, variable_dtype_map):
    tier_fields = {
        "pricepoint_data.show_future_tiers": "Show Future Tiers?",
        "pricepoint_data.pricing_method": "Pricing Method",
        "pricepoint_data.currency": "Currency",
        "pricepoint_data.tier_data.list_unit_price": "Tier List Unit Price",
        "pricepoint_data.tier_data.net_unit_price": "Tier Net Unit Price",
        "pricepoint_data.tier_data.discount_percent": "Tier Discount Percentage",
        "pricepoint_data.tier_data.list_flat_price": "Tier List Flat Price",
        "pricepoint_data.tier_data.net_flat_price": "Tier Net Flat Price",
    }
    client_settings = get_client_settings(client_id)
    ignored_line_item_fields = client_settings.get("cpq_settings", {}).get(
        "ignored_line_item_fields", []
    )
    fields = []
    for field, label in tier_fields.items():
        if field in ignored_line_item_fields:
            continue
        _field = {
            "group": "FORM_VARIABLES",
            "label": label,
            "value": field,
            "meta": {},
        }
        if field.startswith("pricepoint_data"):
            tiered_value = field.split(".")
            if tiered_value[1] in ["show_future_tiers"]:
                _field["meta"]["data_type"] = "Boolean"
                _field["meta"]["data_type_id"] = variable_dtype_map["boolean"]
            elif tiered_value[1] in ["pricing_method", "currency"]:
                _field["meta"]["data_type"] = "String"
                _field["meta"]["data_type_id"] = variable_dtype_map["string"]
            else:
                _field["meta"]["data_type"] = "Integer"
                _field["meta"]["data_type_id"] = variable_dtype_map["integer"]
        fields.append(_field)
    return fields


def get_datasheet_variables(client_id, databook_id, datasheet_id):
    from commission_engine.services.expression_designer import (
        ContextMetaDataModel,
        autocomplete_context,
    )

    context_meta_data = {}
    context_meta_data = ContextMetaDataModel(**context_meta_data)
    ds_variables = autocomplete_context.get_source_variables_wrapper(
        client_id,
        databook_id,
        datasheet_id,
        context_meta_data,
    )
    return ds_variables


def get_user_fields(variable_dtype_map):
    user_fields = {
        "up_first_name": "First Name",
        "up_last_name": "Last Name",
        "up_email": "Email",
        "up_designation": "Designation",
        "up_is_manager": "Is Manager ?",
    }
    fields = []
    for field, label in user_fields.items():
        data = {
            "group": "USER_VARIABLES",
            "label": label,
            "value": field,
            "meta": {},
        }
        if field in ["up_is_manager"]:
            data["meta"] = {
                "data_type": "Boolean",
                "data_type_id": variable_dtype_map["boolean"],
            }
        else:
            data["meta"] = {
                "data_type": "String",
                "data_type_id": variable_dtype_map["string"],
            }
        fields.append(data)
    return fields


def get_quote_property_fields(variable_dtype_map):
    quote_fields = {
        "qp_quote_owner_email": "Quote Owner Email",
        "qp_subscription_start_date": "Subscription Start Date",
        "qp_subscription_end_date": "Subscription End Date",
        "qp_duration": "Duration",
        "qp_duration_unit": "Duration Unit",
        # "qp_quote_total": "Quote Total",
        "qp_quote_net_total": "Quote Net Total",
        "qp_quote_list_total": "Quote List Total",
    }
    fields = []
    for field, label in quote_fields.items():
        data = {
            "group": "FORM_VARIABLES",
            "label": label,
            "value": field,
            "meta": {},
        }
        if field in ["qp_subscription_start_date", "qp_subscription_end_date"]:
            data["meta"] = {
                "data_type": "Date",
                "data_type_id": variable_dtype_map["date"],
            }
        elif field in [
            "qp_duration",
            "qp_quote_total",
            "qp_quote_net_total",
            "qp_quote_list_total",
        ]:
            data["meta"] = {
                "data_type": "Integer",
                "data_type_id": variable_dtype_map["integer"],
            }
        else:
            data["meta"] = {
                "data_type": "String",
                "data_type_id": variable_dtype_map["string"],
            }
        fields.append(data)
    return fields


def validate_expression(client_id, expression, form_builder_id=None):
    from commission_engine.services.commission_calculation_service.commission_simulation.utils import (
        change_keys,
    )
    from commission_engine.services.commission_calculation_service.criteria_calculation_service import (
        validate_infix,
    )

    invalid_exp = {
        "data_type": "INVALID",
        "data_type_id": -1,
        "status": "INVALID",
        "msg": "Field not found in quote form",
    }

    expression = change_keys(expression, pydash.snake_case)
    if form_builder_id:
        form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)
        if not form_builder:
            return invalid_exp
        form_spec = form_builder.form_spec
        form_navigator = FormSpecNavigator(form_spec)
        fields = form_navigator.get_all_fields()
        source_fields = get_source_fields_used(expression)
        source_fields_name_mapping = get_source_field_name_mapping(expression)
        field_id_name_map = form_navigator.get_field_id_name_map()
        quote_fields = set(source_fields) - set(quote_line_item_fields)
        if set(quote_fields) - set(fields):
            return invalid_exp
        for field in quote_fields:
            if (
                field not in field_id_name_map
                or field not in source_fields_name_mapping
            ):
                return invalid_exp
            if field_id_name_map[field] != source_fields_name_mapping[field]:
                return invalid_exp

    res = validate_infix(expression, skip_brackets=False)

    return res
