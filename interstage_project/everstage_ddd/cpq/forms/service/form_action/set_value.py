from typing import Any

from ..form_types import FormModel
from .form_action import FormAction


class SetValue(FormAction):
    def __init__(self, client_id, form_spec: FormModel) -> None:
        self.client_id = client_id
        self.form_spec = form_spec
        self.property = "value"
        super().__init__(form_spec)

    def make_change(self, form_component: str, component_id: str, value: Any):
        if form_component == "field":
            field = self.form_navigator.get_field(component_id)
            data_type = field["data_type"]
            if value:
                field[self.property] = value
            elif data_type == "number":
                # If the data type is number, and the value is empty, set the value to 0
                field[self.property] = 0
            else:
                field[self.property] = ""
        return self.form_spec
