import base64
import binasci<PERSON>
import uuid
from uuid import UUID

from django.db import transaction
from ninja import Router
from ninja.decorators import decorate_view
from pydash import camel_case

from commission_engine.services.commission_calculation_service.commission_simulation.utils import (
    change_keys,
)
from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.cpq.approvals import (
    get_all_fields_for_approvals,
    get_approval_instances_smart_evaluate,
)
from interstage_project.auth_utils import requires_scope

from .accessors.form_spec_selector import FormSpecSelector
from .schema import (
    AutoCompleteContextPayload,
    AutoCompleteContextResponse,
    AutoSaveFormPayload,
    AutoSaveFormResponse,
    CreateFormBuilderTermsPayload,
    CreateFormBuilderTermsResponse,
    CreateFormRulePayload,
    CreateFormRuleResponse,
    DeleteFieldPayload,
    DeleteFieldResponse,
    DeleteFormBuilderResponse,
    DeleteFormRulePayload,
    DeleteFormRuleResponse,
    EvaluateRulePayload,
    EvaluateRuleResponse,
    ExpressionValidatePayload,
    ExpressionValidateResponse,
    FormBuilderPayload,
    FormBuilderResponse,
    GetAllFormBuildersPayload,
    GetAllFormBuildersResponse,
    GetAllFormRulesPayload,
    GetAllFormRulesResponse,
    GetAllFormsBasicInfoResponse,
    GetAllFormsPayload,
    GetAllFormsResponse,
    GetFieldMapByFormBuilderIdResponse,
    GetFieldsAndSectionsResponse,
    GetFormBuilderPayload,
    GetFormBuilderResponse,
    GetFormBuilderTermsResponse,
    GetFormRuleResponse,
    GetFormSpecResponse,
    GetLookupOptionsPayload,
    GetLookupOptionsResponse,
    GetReturnColumnsResponse,
    SaveLookupValuePayload,
    SaveLookupValueResponse,
    SubmitFormPayload,
    SubmitFormResponse,
    UpdateFormBuilderPayload,
    UpdateFormBuilderResponse,
    UpdateFormRulePayload,
    UpdateFormRuleResponse,
    ValidateFormBuilderDeleteResponse,
)
from .service.form_builder_service import (
    clone_form_builder_service,
    create_form_builder_service,
    create_update_form_builder_terms,
    delete_field_service,
    delete_form_builder_service,
    get_all_forms_basic_info,
    get_all_forms_builders,
    get_field_map_by_form_builder_id,
    get_form_builder_by_id_service,
    get_form_builder_terms,
    update_form_builder_service,
    validate_delete_form_builder,
)
from .service.form_rules_service import (
    clone_form_rule_service,
    create_form_rule_service,
    delete_form_rule_service,
    get_all_form_rules_service,
    get_fields_and_sections_service,
    get_form_rule_service,
    update_form_rule_service,
)
from .service.form_service import (
    auto_save_form,
    evaluate_rule_spec,
    get_all_forms,
    get_auto_complete_context,
    get_form_spec,
    line_item_return_columns,
    prepare_lookup_options,
    save_lookup_value,
    submit_form,
    validate_expression,
)
from .service.form_spec_navigator import FormSpecNavigator

forms_router = Router(tags=["forms"])


@forms_router.get("spec/{form_builder_id}", response=GetFormSpecResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_form_spec_api(request, form_builder_id: str) -> GetFormSpecResponse:
    form_spec = get_form_spec(request.client_id, form_builder_id)
    return GetFormSpecResponse(**form_spec)


@forms_router.post("evaluate_rule", response=EvaluateRuleResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def evaluate_rule_api(request, payload: EvaluateRulePayload) -> EvaluateRuleResponse:
    client_id = request.client_id

    new_form_spec = evaluate_rule_spec(
        client_id=client_id,
        form_builder_id=payload.form_builder_id,
        changed_field=payload.changed_field,
        form_data=payload.form_data,
        curr_form_spec=payload.form_spec.model_dump(),
    )
    data = {
        "form_id": payload.form_id,
        "form_builder_id": payload.form_builder_id,
        "form_spec": new_form_spec,
    }
    FormSpecSelector(client_id).create_form_spec_change(data)

    form_navigator = FormSpecNavigator(new_form_spec)
    form_data = form_navigator.get_form_data_from_spec()
    quote_id = form_navigator.get_field("field1").get("value", "")
    logged_in_user = request.user.username
    fields_df = get_all_fields_for_approvals(
        client_id=client_id,
        logged_in_user=logged_in_user,
        quote_owner=None,
        quote_id=quote_id,
        form_data=form_data,
    )
    response = get_approval_instances_smart_evaluate(
        client_id=request.client_id,
        logged_in_user=request.user.username,
        fields_df=fields_df,
        form_builder_id=payload.form_builder_id,
        quote_id=quote_id,
        is_edit_mode=payload.is_edit_mode,
    )
    new_form_spec["approval_cycles"] = response.get("approval_cycles", [])
    return EvaluateRuleResponse(**new_form_spec)


@forms_router.post("get_lookup_options", response=GetLookupOptionsResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def get_lookup_options_api(
    request, payload: GetLookupOptionsPayload
) -> GetLookupOptionsResponse:
    options = prepare_lookup_options(
        request.client_id,
        payload.look_up_field,
        payload.form_data,
        payload.query_params.model_dump(),
        payload.quote_id,
        payload.form_builder_id,
    )
    return GetLookupOptionsResponse(options=options)


@forms_router.post("save_lookup_value", response=SaveLookupValueResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def save_lookup_value_api(
    request, payload: SaveLookupValuePayload
) -> SaveLookupValueResponse:
    form_spec = save_lookup_value(
        request.client_id,
        payload.look_up_field,
        payload.selected_value,
        payload.quote_id,
    )
    return SaveLookupValueResponse(**form_spec)


@forms_router.post("auto_save_form", response=AutoSaveFormResponse)
@decorate_view(
    requires_scope(
        [RbacPermissions.CREATE_QUOTES.value, RbacPermissions.EDIT_QUOTES.value]
    )
)
@transaction.atomic
def auto_save_form_api(request, payload: AutoSaveFormPayload) -> AutoSaveFormResponse:
    client_id = request.client_id
    form_builder_id = payload.form_builder_id
    form_id = payload.form_id
    form_data = payload.form_data
    res = auto_save_form(client_id, form_builder_id, form_id, form_data)
    return AutoSaveFormResponse(**res)


@forms_router.post("submit_form", response=SubmitFormResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def submit_form_api(request, payload: SubmitFormPayload) -> SubmitFormResponse:
    client_id = request.client_id
    user = request.user.username
    form_builder_id = payload.form_builder_id
    form_id = payload.form_id
    params = {"user": user}
    params["form_spec"] = payload.form_spec.model_dump()
    params["form_data"] = payload.form_data
    params["additional_details"] = request.audit
    res = submit_form(client_id, form_builder_id, form_id, params)
    return SubmitFormResponse(**res)


@forms_router.get("get_all_forms", response=GetAllFormsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_all_forms_api(request, payload: GetAllFormsPayload) -> GetAllFormsResponse:
    client_id = request.client_id
    res = get_all_forms(client_id)
    return GetAllFormsResponse(forms=res)


@forms_router.post("all_form_builders", response=GetAllFormBuildersResponse)
@decorate_view(requires_scope(RbacPermissions.CREATE_QUOTES.value))
@transaction.atomic
def get_all_form_builders(
    request, payload: GetAllFormBuildersPayload
) -> GetAllFormBuildersResponse:
    client_id = request.client_id
    res = get_all_forms_builders(client_id, **payload.dict())
    return GetAllFormBuildersResponse(form_builders=res)


@forms_router.post("create_form_builder", response=FormBuilderResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def create_form_builder(request, payload: FormBuilderPayload) -> FormBuilderResponse:
    client_id = request.client_id
    logged_in_user = request.user.username
    form_builder_id = payload.form_builder_id
    if form_builder_id:
        res = clone_form_builder_service(client_id, logged_in_user, **payload.dict())
        return FormBuilderResponse(**res)
    form_builder_name = payload.form_builder_name
    form_builder_description = payload.form_builder_description
    res = create_form_builder_service(
        client_id,
        logged_in_user,
        form_builder_name=form_builder_name,
        form_builder_description=form_builder_description,
    )
    return FormBuilderResponse(**res)


@forms_router.post("delete_form_builder", response=DeleteFormBuilderResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def delete_form_builder(
    request, payload: FormBuilderPayload
) -> DeleteFormBuilderResponse:
    client_id = request.client_id
    res = delete_form_builder_service(client_id, **payload.dict())
    return DeleteFormBuilderResponse(**res)


@forms_router.get("form_builder_id/{form_builder_id}", response=GetFormBuilderResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_form_builder_by_id(request, form_builder_id: str) -> GetFormBuilderResponse:
    client_id = request.client_id
    res = get_form_builder_by_id_service(client_id, form_builder_id)
    return GetFormBuilderResponse(**res)


@forms_router.post("update_form_builder", response=UpdateFormBuilderResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def update_form_builder(
    request, payload: UpdateFormBuilderPayload
) -> UpdateFormBuilderResponse:
    client_id = request.client_id
    logged_in_user = request.user.username
    res = update_form_builder_service(client_id, logged_in_user, **payload.dict())
    return UpdateFormBuilderResponse(**res)


@forms_router.post("delete_field", response=DeleteFieldResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def delete_field(request, payload: DeleteFieldPayload) -> DeleteFieldResponse:
    client_id = request.client_id
    form_builder_id = payload.form_builder_id
    field_id = payload.field_id
    res = delete_field_service(client_id, form_builder_id, field_id)
    return DeleteFieldResponse(**res)


@forms_router.post("/autocomplete_context", response=AutoCompleteContextResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def auto_complete_context(
    request, payload: AutoCompleteContextPayload
) -> AutoCompleteContextResponse:
    """
    Get the auto complete context for a quote.
    """
    client_id = request.client_id
    datasheet_id = payload.datasheet_id
    databook_id = payload.databook_id
    data = get_auto_complete_context(
        client_id,
        payload.variables,
        payload.form_builder_id,
        datasheet_id=datasheet_id,
        databook_id=databook_id,
    )
    res = change_keys(data, camel_case)
    return AutoCompleteContextResponse(data=res)


@forms_router.post("expression_validate", response=ExpressionValidateResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def expression_validate_api(
    request, payload: ExpressionValidatePayload
) -> ExpressionValidateResponse:
    client_id = request.client_id
    meta = payload.meta
    form_builder_id = meta["formBuilderId"] if meta else None
    res = validate_expression(client_id, payload.expression, form_builder_id)
    return ExpressionValidateResponse(**res)


@forms_router.post("create_form_rule", response=CreateFormRuleResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def create_form_rule(request, payload: CreateFormRulePayload) -> CreateFormRuleResponse:
    """Create a new form rule"""
    client_id = request.client_id
    logged_in_user = request.user.username
    form_rule_id = payload.form_rule_id
    if form_rule_id:
        res = clone_form_rule_service(client_id, logged_in_user, form_rule_id)
        return CreateFormRuleResponse(**res)
    form_rule_name = payload.form_rule_name
    form_rule_description = payload.form_rule_description
    form_builder_id = payload.form_builder_id
    res = create_form_rule_service(
        client_id,
        logged_in_user,
        form_rule_name=form_rule_name,
        form_rule_description=form_rule_description,
        form_builder_id=form_builder_id,
    )
    return CreateFormRuleResponse(**res)


@forms_router.post("delete_form_rule", response=DeleteFormRuleResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def delete_form_rule(request, payload: DeleteFormRulePayload) -> DeleteFormRuleResponse:
    """Delete a form rule by ID"""
    client_id = request.client_id
    res = delete_form_rule_service(client_id, **payload.dict())
    return DeleteFormRuleResponse(**res)


@forms_router.post("update_form_rule", response=UpdateFormRuleResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def update_form_rule(request, payload: UpdateFormRulePayload) -> UpdateFormRuleResponse:
    """Update a form rule by ID"""
    client_id = request.client_id
    logged_in_user = request.user.username
    res = update_form_rule_service(client_id, logged_in_user, **payload.dict())
    return UpdateFormRuleResponse(**res)


@forms_router.get("form_rule/{form_rule_id}", response=GetFormRuleResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_form_rule(request, form_rule_id: str) -> GetFormRuleResponse:
    """Get a form rule by ID"""
    client_id = request.client_id
    res = get_form_rule_service(client_id, form_rule_id)
    return GetFormRuleResponse(**res)


@forms_router.post("form_rules", response=GetAllFormRulesResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_all_form_rules(
    request, payload: GetAllFormRulesPayload
) -> GetAllFormRulesResponse:
    """Get all form rules"""
    client_id = request.client_id
    res = get_all_form_rules_service(client_id, **payload.dict())
    return GetAllFormRulesResponse(form_rules=res)


@forms_router.post(
    "create_update_form_builder_terms/{form_builder_id}",
    response=CreateFormBuilderTermsResponse,
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def create_update_form_builder_terms_api(
    request, form_builder_id: UUID, payload: CreateFormBuilderTermsPayload
) -> CreateFormBuilderTermsResponse:
    client_id = request.client_id
    terms = payload.terms
    terms_list = []
    for term in terms:
        term_dict = term.model_dump()
        # Handle null safety and invalid base64 data
        try:
            value = term_dict.get("value") or ""
            if value:
                term_dict["value"] = base64.b64decode(value).decode("utf-8")
            else:
                term_dict["value"] = ""
        except (binascii.Error, ValueError, UnicodeDecodeError):
            return CreateFormBuilderTermsResponse(
                status="error",
                message="Invalid base64 data",
            )

        if not term_dict.get("term_id"):
            term_dict["term_id"] = str(uuid.uuid4())
        terms_list.append(term_dict)
    create_update_form_builder_terms(client_id, form_builder_id, terms_list)
    return CreateFormBuilderTermsResponse(
        status="success",
        message="Terms created successfully",
    )


@forms_router.get(
    "get_form_builder_terms/{form_builder_id}", response=GetFormBuilderTermsResponse
)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_form_builder_terms_api(
    request, form_builder_id: UUID
) -> GetFormBuilderTermsResponse:
    client_id = request.client_id
    res = get_form_builder_terms(client_id, form_builder_id) or []

    return GetFormBuilderTermsResponse(terms=res)


@forms_router.get("all_forms_basic_info", response=GetAllFormsBasicInfoResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_all_forms_basic_info_api(request) -> GetAllFormsBasicInfoResponse:
    client_id = request.client_id
    res = get_all_forms_basic_info(client_id)
    return GetAllFormsBasicInfoResponse(forms=res)


@forms_router.get(
    "get_field_map_by_form_builder_id/{form_builder_id}",
    response=GetFieldMapByFormBuilderIdResponse,
)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_field_map_by_form_builder_id_api(
    request, form_builder_id: UUID
) -> GetFieldMapByFormBuilderIdResponse:
    client_id = request.client_id
    res = get_field_map_by_form_builder_id(client_id, form_builder_id)
    return GetFieldMapByFormBuilderIdResponse(field_map=res)


@forms_router.get(
    "form_rule/fields_and_sections/{form_builder_id}",
    response=GetFieldsAndSectionsResponse,
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
def get_fields_and_sections(
    request, form_builder_id: str
) -> GetFieldsAndSectionsResponse:
    client_id = request.client_id
    res = get_fields_and_sections_service(client_id, form_builder_id)
    return GetFieldsAndSectionsResponse(**res)


@forms_router.get("get_line_item_return_columns", response=GetReturnColumnsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
def get_line_item_return_columns(request) -> GetReturnColumnsResponse:
    client_id = request.client_id
    res = line_item_return_columns(client_id)
    return GetReturnColumnsResponse(columns=res)


@forms_router.post(
    "validate_form_builder_delete", response=ValidateFormBuilderDeleteResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def validate_form_builder_delete(
    request, payload: GetFormBuilderPayload
) -> ValidateFormBuilderDeleteResponse:
    client_id = request.client_id
    form_builder_id = payload.form_builder_id
    res = validate_delete_form_builder(client_id, form_builder_id)
    return ValidateFormBuilderDeleteResponse(**res)
