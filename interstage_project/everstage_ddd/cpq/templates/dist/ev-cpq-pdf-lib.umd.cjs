(function(Vi,Yl){typeof exports=="object"&&typeof module<"u"?Yl(exports):typeof define=="function"&&define.amd?define(["exports"],Yl):(Vi=typeof globalThis<"u"?globalThis:Vi||self,Yl(Vi.PDFGenerator={}))})(this,function(Vi){"use strict";function Yl(w){return w&&w.__esModule&&Object.prototype.hasOwnProperty.call(w,"default")?w.default:w}var sh={exports:{}},ch={exports:{}},$l={exports:{}};/**
 * @license React
 * react.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */$l.exports;var fh;function RS(){return fh||(fh=1,function(w,N){(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var J="18.2.0",ce=Symbol.for("react.element"),ke=Symbol.for("react.portal"),fe=Symbol.for("react.fragment"),d=Symbol.for("react.strict_mode"),ze=Symbol.for("react.profiler"),ae=Symbol.for("react.provider"),B=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),W=Symbol.for("react.suspense"),oe=Symbol.for("react.suspense_list"),G=Symbol.for("react.memo"),ye=Symbol.for("react.lazy"),gn=Symbol.for("react.offscreen"),Ln=Symbol.iterator,Kt="@@iterator";function He(s){if(s===null||typeof s!="object")return null;var v=Ln&&s[Ln]||s[Kt];return typeof v=="function"?v:null}var Se={current:null},it={transition:null},se={current:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1},Je={current:null},De={},bn=null;function Sn(s){bn=s}De.setExtraStackFrame=function(s){bn=s},De.getCurrentStack=null,De.getStackAddendum=function(){var s="";bn&&(s+=bn);var v=De.getCurrentStack;return v&&(s+=v()||""),s};var ht=!1,Xe=!1,Un=!1,ge=!1,Ae=!1,ct={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:it,ReactCurrentOwner:Je};ct.ReactDebugCurrentFrame=De,ct.ReactCurrentActQueue=se;function ft(s){{for(var v=arguments.length,C=new Array(v>1?v-1:0),R=1;R<v;R++)C[R-1]=arguments[R];Vt("warn",s,C)}}function ve(s){{for(var v=arguments.length,C=new Array(v>1?v-1:0),R=1;R<v;R++)C[R-1]=arguments[R];Vt("error",s,C)}}function Vt(s,v,C){{var R=ct.ReactDebugCurrentFrame,A=R.getStackAddendum();A!==""&&(v+="%s",C=C.concat([A]));var te=C.map(function(P){return String(P)});te.unshift("Warning: "+v),Function.prototype.apply.call(console[s],console,te)}}var Na={};function Xn(s,v){{var C=s.constructor,R=C&&(C.displayName||C.name)||"ReactClass",A=R+"."+v;if(Na[A])return;ve("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",v,R),Na[A]=!0}}var ca={isMounted:function(s){return!1},enqueueForceUpdate:function(s,v,C){Xn(s,"forceUpdate")},enqueueReplaceState:function(s,v,C,R){Xn(s,"replaceState")},enqueueSetState:function(s,v,C,R){Xn(s,"setState")}},At=Object.assign,En={};Object.freeze(En);function jn(s,v,C){this.props=s,this.context=v,this.refs=En,this.updater=C||ca}jn.prototype.isReactComponent={},jn.prototype.setState=function(s,v){if(typeof s!="object"&&typeof s!="function"&&s!=null)throw new Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,s,v,"setState")},jn.prototype.forceUpdate=function(s){this.updater.enqueueForceUpdate(this,s,"forceUpdate")};{var Ma={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},fa=function(s,v){Object.defineProperty(jn.prototype,s,{get:function(){ft("%s(...) is deprecated in plain JavaScript React classes. %s",v[0],v[1])}})};for(var da in Ma)Ma.hasOwnProperty(da)&&fa(da,Ma[da])}function Kn(){}Kn.prototype=jn.prototype;function Bt(s,v,C){this.props=s,this.context=v,this.refs=En,this.updater=C||ca}var Cn=Bt.prototype=new Kn;Cn.constructor=Bt,At(Cn,jn.prototype),Cn.isPureReactComponent=!0;function kn(){var s={current:null};return Object.seal(s),s}var zn=Array.isArray;function mt(s){return zn(s)}function Jt(s){{var v=typeof Symbol=="function"&&Symbol.toStringTag,C=v&&s[Symbol.toStringTag]||s.constructor.name||"Object";return C}}function Lt(s){try{return Ut(s),!1}catch{return!0}}function Ut(s){return""+s}function Et(s){if(Lt(s))return ve("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Jt(s)),Ut(s)}function Hn(s,v,C){var R=s.displayName;if(R)return R;var A=v.displayName||v.name||"";return A!==""?C+"("+A+")":C}function Jn(s){return s.displayName||"Context"}function Rn(s){if(s==null)return null;if(typeof s.tag=="number"&&ve("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof s=="function")return s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case fe:return"Fragment";case ke:return"Portal";case ze:return"Profiler";case d:return"StrictMode";case W:return"Suspense";case oe:return"SuspenseList"}if(typeof s=="object")switch(s.$$typeof){case B:var v=s;return Jn(v)+".Consumer";case ae:var C=s;return Jn(C._context)+".Provider";case q:return Hn(s,s.render,"ForwardRef");case G:var R=s.displayName||null;return R!==null?R:Rn(s.type)||"Memo";case ye:{var A=s,te=A._payload,P=A._init;try{return Rn(P(te))}catch{return null}}}return null}var va=Object.prototype.hasOwnProperty,Zn={key:!0,ref:!0,__self:!0,__source:!0},sn,ea,Zt;Zt={};function Fn(s){if(va.call(s,"ref")){var v=Object.getOwnPropertyDescriptor(s,"ref").get;if(v&&v.isReactWarning)return!1}return s.ref!==void 0}function dt(s){if(va.call(s,"key")){var v=Object.getOwnPropertyDescriptor(s,"key").get;if(v&&v.isReactWarning)return!1}return s.key!==void 0}function ta(s,v){var C=function(){sn||(sn=!0,ve("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",v))};C.isReactWarning=!0,Object.defineProperty(s,"key",{get:C,configurable:!0})}function Xa(s,v){var C=function(){ea||(ea=!0,ve("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",v))};C.isReactWarning=!0,Object.defineProperty(s,"ref",{get:C,configurable:!0})}function Ka(s){if(typeof s.ref=="string"&&Je.current&&s.__self&&Je.current.stateNode!==s.__self){var v=Rn(Je.current.type);Zt[v]||(ve('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',v,s.ref),Zt[v]=!0)}}var j=function(s,v,C,R,A,te,P){var re={$$typeof:ce,type:s,key:v,ref:C,props:P,_owner:te};return re._store={},Object.defineProperty(re._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(re,"_self",{configurable:!1,enumerable:!1,writable:!1,value:R}),Object.defineProperty(re,"_source",{configurable:!1,enumerable:!1,writable:!1,value:A}),Object.freeze&&(Object.freeze(re.props),Object.freeze(re)),re};function I(s,v,C){var R,A={},te=null,P=null,re=null,Re=null;if(v!=null){Fn(v)&&(P=v.ref,Ka(v)),dt(v)&&(Et(v.key),te=""+v.key),re=v.__self===void 0?null:v.__self,Re=v.__source===void 0?null:v.__source;for(R in v)va.call(v,R)&&!Zn.hasOwnProperty(R)&&(A[R]=v[R])}var Ye=arguments.length-2;if(Ye===1)A.children=C;else if(Ye>1){for(var Qe=Array(Ye),We=0;We<Ye;We++)Qe[We]=arguments[We+2];Object.freeze&&Object.freeze(Qe),A.children=Qe}if(s&&s.defaultProps){var at=s.defaultProps;for(R in at)A[R]===void 0&&(A[R]=at[R])}if(te||P){var ot=typeof s=="function"?s.displayName||s.name||"Unknown":s;te&&ta(A,ot),P&&Xa(A,ot)}return j(s,te,P,re,Re,Je.current,A)}function me(s,v){var C=j(s.type,v,s.ref,s._self,s._source,s._owner,s.props);return C}function Ve(s,v,C){if(s==null)throw new Error("React.cloneElement(...): The argument must be a React element, but you passed "+s+".");var R,A=At({},s.props),te=s.key,P=s.ref,re=s._self,Re=s._source,Ye=s._owner;if(v!=null){Fn(v)&&(P=v.ref,Ye=Je.current),dt(v)&&(Et(v.key),te=""+v.key);var Qe;s.type&&s.type.defaultProps&&(Qe=s.type.defaultProps);for(R in v)va.call(v,R)&&!Zn.hasOwnProperty(R)&&(v[R]===void 0&&Qe!==void 0?A[R]=Qe[R]:A[R]=v[R])}var We=arguments.length-2;if(We===1)A.children=C;else if(We>1){for(var at=Array(We),ot=0;ot<We;ot++)at[ot]=arguments[ot+2];A.children=at}return j(s.type,te,P,re,Re,Ye,A)}function Be(s){return typeof s=="object"&&s!==null&&s.$$typeof===ce}var Ct=".",vt=":";function Tn(s){var v=/[=:]/g,C={"=":"=0",":":"=2"},R=s.replace(v,function(A){return C[A]});return"$"+R}var Ge=!1,Vn=/\/+/g;function Ze(s){return s.replace(Vn,"$&/")}function et(s,v){return typeof s=="object"&&s!==null&&s.key!=null?(Et(s.key),Tn(""+s.key)):v.toString(36)}function Aa(s,v,C,R,A){var te=typeof s;(te==="undefined"||te==="boolean")&&(s=null);var P=!1;if(s===null)P=!0;else switch(te){case"string":case"number":P=!0;break;case"object":switch(s.$$typeof){case ce:case ke:P=!0}}if(P){var re=s,Re=A(re),Ye=R===""?Ct+et(re,0):R;if(mt(Re)){var Qe="";Ye!=null&&(Qe=Ze(Ye)+"/"),Aa(Re,v,Qe,"",function(of){return of})}else Re!=null&&(Be(Re)&&(Re.key&&(!re||re.key!==Re.key)&&Et(Re.key),Re=me(Re,C+(Re.key&&(!re||re.key!==Re.key)?Ze(""+Re.key)+"/":"")+Ye)),v.push(Re));return 1}var We,at,ot=0,Ue=R===""?Ct:R+vt;if(mt(s))for(var Lr=0;Lr<s.length;Lr++)We=s[Lr],at=Ue+et(We,Lr),ot+=Aa(We,v,C,at,A);else{var Wi=He(s);if(typeof Wi=="function"){var iu=s;Wi===iu.entries&&(Ge||ft("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Ge=!0);for(var uf=Wi.call(iu),nr,lu=0;!(nr=uf.next()).done;)We=nr.value,at=Ue+et(We,lu++),ot+=Aa(We,v,C,at,A)}else if(te==="object"){var uu=String(s);throw new Error("Objects are not valid as a React child (found: "+(uu==="[object Object]"?"object with keys {"+Object.keys(s).join(", ")+"}":uu)+"). If you meant to render a collection of children, use an array instead.")}}return ot}function pa(s,v,C){if(s==null)return s;var R=[],A=0;return Aa(s,R,"","",function(te){return v.call(C,te,A++)}),R}function ai(s){var v=0;return pa(s,function(){v++}),v}function Bi(s,v,C){pa(s,function(){v.apply(this,arguments)},C)}function ql(s){return pa(s,function(v){return v})||[]}function ri(s){if(!Be(s))throw new Error("React.Children.only expected to receive a single React element child.");return s}function ii(s){var v={$$typeof:B,_currentValue:s,_currentValue2:s,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};v.Provider={$$typeof:ae,_context:v};var C=!1,R=!1,A=!1;{var te={$$typeof:B,_context:v};Object.defineProperties(te,{Provider:{get:function(){return R||(R=!0,ve("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")),v.Provider},set:function(P){v.Provider=P}},_currentValue:{get:function(){return v._currentValue},set:function(P){v._currentValue=P}},_currentValue2:{get:function(){return v._currentValue2},set:function(P){v._currentValue2=P}},_threadCount:{get:function(){return v._threadCount},set:function(P){v._threadCount=P}},Consumer:{get:function(){return C||(C=!0,ve("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")),v.Consumer}},displayName:{get:function(){return v.displayName},set:function(P){A||(ft("Setting `displayName` on Context.Consumer has no effect. You should set it directly on the context with Context.displayName = '%s'.",P),A=!0)}}}),v.Consumer=te}return v._currentRenderer=null,v._currentRenderer2=null,v}var Ja=-1,xr=0,Za=1,La=2;function ha(s){if(s._status===Ja){var v=s._result,C=v();if(C.then(function(te){if(s._status===xr||s._status===Ja){var P=s;P._status=Za,P._result=te}},function(te){if(s._status===xr||s._status===Ja){var P=s;P._status=La,P._result=te}}),s._status===Ja){var R=s;R._status=xr,R._result=C}}if(s._status===Za){var A=s._result;return A===void 0&&ve(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`,A),"default"in A||ve(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`,A),A.default}else throw s._result}function Ua(s){var v={_status:Ja,_result:s},C={$$typeof:ye,_payload:v,_init:ha};{var R,A;Object.defineProperties(C,{defaultProps:{configurable:!0,get:function(){return R},set:function(te){ve("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),R=te,Object.defineProperty(C,"defaultProps",{enumerable:!0})}},propTypes:{configurable:!0,get:function(){return A},set:function(te){ve("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),A=te,Object.defineProperty(C,"propTypes",{enumerable:!0})}}})}return C}function li(s){s!=null&&s.$$typeof===G?ve("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):typeof s!="function"?ve("forwardRef requires a render function but was given %s.",s===null?"null":typeof s):s.length!==0&&s.length!==2&&ve("forwardRef render functions accept exactly two parameters: props and ref. %s",s.length===1?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),s!=null&&(s.defaultProps!=null||s.propTypes!=null)&&ve("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?");var v={$$typeof:q,render:s};{var C;Object.defineProperty(v,"displayName",{enumerable:!1,configurable:!0,get:function(){return C},set:function(R){C=R,!s.name&&!s.displayName&&(s.displayName=R)}})}return v}var p;p=Symbol.for("react.module.reference");function O(s){return!!(typeof s=="string"||typeof s=="function"||s===fe||s===ze||Ae||s===d||s===W||s===oe||ge||s===gn||ht||Xe||Un||typeof s=="object"&&s!==null&&(s.$$typeof===ye||s.$$typeof===G||s.$$typeof===ae||s.$$typeof===B||s.$$typeof===q||s.$$typeof===p||s.getModuleId!==void 0))}function U(s,v){O(s)||ve("memo: The first argument must be a component. Instead received: %s",s===null?"null":typeof s);var C={$$typeof:G,type:s,compare:v===void 0?null:v};{var R;Object.defineProperty(C,"displayName",{enumerable:!1,configurable:!0,get:function(){return R},set:function(A){R=A,!s.name&&!s.displayName&&(s.displayName=A)}})}return C}function Y(){var s=Se.current;return s===null&&ve(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`),s}function Ee(s){var v=Y();if(s._context!==void 0){var C=s._context;C.Consumer===s?ve("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?"):C.Provider===s&&ve("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?")}return v.useContext(s)}function Oe(s){var v=Y();return v.useState(s)}function pe(s,v,C){var R=Y();return R.useReducer(s,v,C)}function K(s){var v=Y();return v.useRef(s)}function Rt(s,v){var C=Y();return C.useEffect(s,v)}function tt(s,v){var C=Y();return C.useInsertionEffect(s,v)}function nt(s,v){var C=Y();return C.useLayoutEffect(s,v)}function en(s,v){var C=Y();return C.useCallback(s,v)}function ja(s,v){var C=Y();return C.useMemo(s,v)}function Yi(s,v,C){var R=Y();return R.useImperativeHandle(s,v,C)}function xn(s,v){{var C=Y();return C.useDebugValue(s,v)}}function tf(){var s=Y();return s.useTransition()}function er(s){var v=Y();return v.useDeferredValue(s)}function Ce(){var s=Y();return s.useId()}function ui(s,v,C){var R=Y();return R.useSyncExternalStore(s,v,C)}var wr=0,Gl,Ql,Wl,Il,Xl,Kl,Jl;function zo(){}zo.__reactDisabledLog=!0;function nf(){{if(wr===0){Gl=console.log,Ql=console.info,Wl=console.warn,Il=console.error,Xl=console.group,Kl=console.groupCollapsed,Jl=console.groupEnd;var s={configurable:!0,enumerable:!0,value:zo,writable:!0};Object.defineProperties(console,{info:s,log:s,warn:s,error:s,group:s,groupCollapsed:s,groupEnd:s})}wr++}}function Zl(){{if(wr--,wr===0){var s={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:At({},s,{value:Gl}),info:At({},s,{value:Ql}),warn:At({},s,{value:Wl}),error:At({},s,{value:Il}),group:At({},s,{value:Xl}),groupCollapsed:At({},s,{value:Kl}),groupEnd:At({},s,{value:Jl})})}wr<0&&ve("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var oi=ct.ReactCurrentDispatcher,na;function Dr(s,v,C){{if(na===void 0)try{throw Error()}catch(A){var R=A.stack.trim().match(/\n( *(at )?)/);na=R&&R[1]||""}return`
`+na+s}}var _r=!1,$i;{var eu=typeof WeakMap=="function"?WeakMap:Map;$i=new eu}function Ho(s,v){if(!s||_r)return"";{var C=$i.get(s);if(C!==void 0)return C}var R;_r=!0;var A=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var te;te=oi.current,oi.current=null,nf();try{if(v){var P=function(){throw Error()};if(Object.defineProperty(P.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(P,[])}catch(Ue){R=Ue}Reflect.construct(s,[],P)}else{try{P.call()}catch(Ue){R=Ue}s.call(P.prototype)}}else{try{throw Error()}catch(Ue){R=Ue}s()}}catch(Ue){if(Ue&&R&&typeof Ue.stack=="string"){for(var re=Ue.stack.split(`
`),Re=R.stack.split(`
`),Ye=re.length-1,Qe=Re.length-1;Ye>=1&&Qe>=0&&re[Ye]!==Re[Qe];)Qe--;for(;Ye>=1&&Qe>=0;Ye--,Qe--)if(re[Ye]!==Re[Qe]){if(Ye!==1||Qe!==1)do if(Ye--,Qe--,Qe<0||re[Ye]!==Re[Qe]){var We=`
`+re[Ye].replace(" at new "," at ");return s.displayName&&We.includes("<anonymous>")&&(We=We.replace("<anonymous>",s.displayName)),typeof s=="function"&&$i.set(s,We),We}while(Ye>=1&&Qe>=0);break}}}finally{_r=!1,oi.current=te,Zl(),Error.prepareStackTrace=A}var at=s?s.displayName||s.name:"",ot=at?Dr(at):"";return typeof s=="function"&&$i.set(s,ot),ot}function tu(s,v,C){return Ho(s,!1)}function af(s){var v=s.prototype;return!!(v&&v.isReactComponent)}function Or(s,v,C){if(s==null)return"";if(typeof s=="function")return Ho(s,af(s));if(typeof s=="string")return Dr(s);switch(s){case W:return Dr("Suspense");case oe:return Dr("SuspenseList")}if(typeof s=="object")switch(s.$$typeof){case q:return tu(s.render);case G:return Or(s.type,v,C);case ye:{var R=s,A=R._payload,te=R._init;try{return Or(te(A),v,C)}catch{}}}return""}var Fo={},nu=ct.ReactDebugCurrentFrame;function Pi(s){if(s){var v=s._owner,C=Or(s.type,s._source,v?v.type:null);nu.setExtraStackFrame(C)}else nu.setExtraStackFrame(null)}function Vo(s,v,C,R,A){{var te=Function.call.bind(va);for(var P in s)if(te(s,P)){var re=void 0;try{if(typeof s[P]!="function"){var Re=Error((R||"React class")+": "+C+" type `"+P+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof s[P]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Re.name="Invariant Violation",Re}re=s[P](v,P,R,C,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Ye){re=Ye}re&&!(re instanceof Error)&&(Pi(A),ve("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",R||"React class",C,P,typeof re),Pi(null)),re instanceof Error&&!(re.message in Fo)&&(Fo[re.message]=!0,Pi(A),ve("Failed %s type: %s",C,re.message),Pi(null))}}}function Le(s){if(s){var v=s._owner,C=Or(s.type,s._source,v?v.type:null);Sn(C)}else Sn(null)}var au;au=!1;function ru(){if(Je.current){var s=Rn(Je.current.type);if(s)return`

Check the render method of \``+s+"`."}return""}function de(s){if(s!==void 0){var v=s.fileName.replace(/^.*[\\\/]/,""),C=s.lineNumber;return`

Check your code at `+v+":"+C+"."}return""}function Bo(s){return s!=null?de(s.__source):""}var tn={};function si(s){var v=ru();if(!v){var C=typeof s=="string"?s:s.displayName||s.name;C&&(v=`

Check the top-level render call using <`+C+">.")}return v}function Nr(s,v){if(!(!s._store||s._store.validated||s.key!=null)){s._store.validated=!0;var C=si(v);if(!tn[C]){tn[C]=!0;var R="";s&&s._owner&&s._owner!==Je.current&&(R=" It was passed a child from "+Rn(s._owner.type)+"."),Le(s),ve('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',C,R),Le(null)}}}function Yo(s,v){if(typeof s=="object"){if(mt(s))for(var C=0;C<s.length;C++){var R=s[C];Be(R)&&Nr(R,v)}else if(Be(s))s._store&&(s._store.validated=!0);else if(s){var A=He(s);if(typeof A=="function"&&A!==s.entries)for(var te=A.call(s),P;!(P=te.next()).done;)Be(P.value)&&Nr(P.value,v)}}}function jt(s){{var v=s.type;if(v==null||typeof v=="string")return;var C;if(typeof v=="function")C=v.propTypes;else if(typeof v=="object"&&(v.$$typeof===q||v.$$typeof===G))C=v.propTypes;else return;if(C){var R=Rn(v);Vo(C,s.props,"prop",R,s)}else if(v.PropTypes!==void 0&&!au){au=!0;var A=Rn(v);ve("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",A||"Unknown")}typeof v.getDefaultProps=="function"&&!v.getDefaultProps.isReactClassApproved&&ve("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function lt(s){{for(var v=Object.keys(s.props),C=0;C<v.length;C++){var R=v[C];if(R!=="children"&&R!=="key"){Le(s),ve("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",R),Le(null);break}}s.ref!==null&&(Le(s),ve("Invalid attribute `ref` supplied to `React.Fragment`."),Le(null))}}function $o(s,v,C){var R=O(s);if(!R){var A="";(s===void 0||typeof s=="object"&&s!==null&&Object.keys(s).length===0)&&(A+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var te=Bo(v);te?A+=te:A+=ru();var P;s===null?P="null":mt(s)?P="array":s!==void 0&&s.$$typeof===ce?(P="<"+(Rn(s.type)||"Unknown")+" />",A=" Did you accidentally export a JSX literal instead of a component?"):P=typeof s,ve("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",P,A)}var re=I.apply(this,arguments);if(re==null)return re;if(R)for(var Re=2;Re<arguments.length;Re++)Yo(arguments[Re],s);return s===fe?lt(re):jt(re),re}var Bn=!1;function wn(s){var v=$o.bind(null,s);return v.type=s,Bn||(Bn=!0,ft("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")),Object.defineProperty(v,"type",{enumerable:!1,get:function(){return ft("Factory.type is deprecated. Access the class directly before passing it to createFactory."),Object.defineProperty(this,"type",{value:s}),s}}),v}function ka(s,v,C){for(var R=Ve.apply(this,arguments),A=2;A<arguments.length;A++)Yo(arguments[A],R.type);return jt(R),R}function rf(s,v){var C=it.transition;it.transition={};var R=it.transition;it.transition._updatedFibers=new Set;try{s()}finally{if(it.transition=C,C===null&&R._updatedFibers){var A=R._updatedFibers.size;A>10&&ft("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),R._updatedFibers.clear()}}}var qi=!1,ci=null;function Po(s){if(ci===null)try{var v=("require"+Math.random()).slice(0,7),C=w&&w[v];ci=C.call(w,"timers").setImmediate}catch{ci=function(A){qi===!1&&(qi=!0,typeof MessageChannel>"u"&&ve("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));var te=new MessageChannel;te.port1.onmessage=A,te.port2.postMessage(void 0)}}return ci(s)}var Mr=0,qo=!1;function lf(s){{var v=Mr;Mr++,se.current===null&&(se.current=[]);var C=se.isBatchingLegacy,R;try{if(se.isBatchingLegacy=!0,R=s(),!C&&se.didScheduleLegacyUpdate){var A=se.current;A!==null&&(se.didScheduleLegacyUpdate=!1,Qi(A))}}catch(at){throw tr(v),at}finally{se.isBatchingLegacy=C}if(R!==null&&typeof R=="object"&&typeof R.then=="function"){var te=R,P=!1,re={then:function(at,ot){P=!0,te.then(function(Ue){tr(v),Mr===0?Gi(Ue,at,ot):at(Ue)},function(Ue){tr(v),ot(Ue)})}};return!qo&&typeof Promise<"u"&&Promise.resolve().then(function(){}).then(function(){P||(qo=!0,ve("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"))}),re}else{var Re=R;if(tr(v),Mr===0){var Ye=se.current;Ye!==null&&(Qi(Ye),se.current=null);var Qe={then:function(at,ot){se.current===null?(se.current=[],Gi(Re,at,ot)):at(Re)}};return Qe}else{var We={then:function(at,ot){at(Re)}};return We}}}}function tr(s){s!==Mr-1&&ve("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "),Mr=s}function Gi(s,v,C){{var R=se.current;if(R!==null)try{Qi(R),Po(function(){R.length===0?(se.current=null,v(s)):Gi(s,v,C)})}catch(A){C(A)}else v(s)}}var Ar=!1;function Qi(s){if(!Ar){Ar=!0;var v=0;try{for(;v<s.length;v++){var C=s[v];do C=C(!0);while(C!==null)}s.length=0}catch(R){throw s=s.slice(v+1),R}finally{Ar=!1}}}var Go=$o,Qo=ka,Wo=wn,Io={map:pa,forEach:Bi,count:ai,toArray:ql,only:ri};N.Children=Io,N.Component=jn,N.Fragment=fe,N.Profiler=ze,N.PureComponent=Bt,N.StrictMode=d,N.Suspense=W,N.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ct,N.cloneElement=Qo,N.createContext=ii,N.createElement=Go,N.createFactory=Wo,N.createRef=kn,N.forwardRef=li,N.isValidElement=Be,N.lazy=Ua,N.memo=U,N.startTransition=rf,N.unstable_act=lf,N.useCallback=en,N.useContext=Ee,N.useDebugValue=xn,N.useDeferredValue=er,N.useEffect=Rt,N.useId=Ce,N.useImperativeHandle=Yi,N.useInsertionEffect=tt,N.useLayoutEffect=nt,N.useMemo=ja,N.useReducer=pe,N.useRef=K,N.useState=Oe,N.useSyncExternalStore=ui,N.useTransition=tf,N.version=J,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()}($l,$l.exports)),$l.exports}ch.exports=RS();var Ic=ch.exports;const TS=Yl(Ic);var Pl={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dh;function xS(){return dh||(dh=1,function(){var w=Ic,N=Symbol.for("react.element"),J=Symbol.for("react.portal"),ce=Symbol.for("react.fragment"),ke=Symbol.for("react.strict_mode"),fe=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),ze=Symbol.for("react.context"),ae=Symbol.for("react.forward_ref"),B=Symbol.for("react.suspense"),q=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),oe=Symbol.for("react.lazy"),G=Symbol.for("react.offscreen"),ye=Symbol.iterator,gn="@@iterator";function Ln(p){if(p===null||typeof p!="object")return null;var O=ye&&p[ye]||p[gn];return typeof O=="function"?O:null}var Kt=w.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function He(p){{for(var O=arguments.length,U=new Array(O>1?O-1:0),Y=1;Y<O;Y++)U[Y-1]=arguments[Y];Se("error",p,U)}}function Se(p,O,U){{var Y=Kt.ReactDebugCurrentFrame,Ee=Y.getStackAddendum();Ee!==""&&(O+="%s",U=U.concat([Ee]));var Oe=U.map(function(pe){return String(pe)});Oe.unshift("Warning: "+O),Function.prototype.apply.call(console[p],console,Oe)}}var it=!1,se=!1,Je=!1,De=!1,bn=!1,Sn;Sn=Symbol.for("react.module.reference");function ht(p){return!!(typeof p=="string"||typeof p=="function"||p===ce||p===fe||bn||p===ke||p===B||p===q||De||p===G||it||se||Je||typeof p=="object"&&p!==null&&(p.$$typeof===oe||p.$$typeof===W||p.$$typeof===d||p.$$typeof===ze||p.$$typeof===ae||p.$$typeof===Sn||p.getModuleId!==void 0))}function Xe(p,O,U){var Y=p.displayName;if(Y)return Y;var Ee=O.displayName||O.name||"";return Ee!==""?U+"("+Ee+")":U}function Un(p){return p.displayName||"Context"}function ge(p){if(p==null)return null;if(typeof p.tag=="number"&&He("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof p=="function")return p.displayName||p.name||null;if(typeof p=="string")return p;switch(p){case ce:return"Fragment";case J:return"Portal";case fe:return"Profiler";case ke:return"StrictMode";case B:return"Suspense";case q:return"SuspenseList"}if(typeof p=="object")switch(p.$$typeof){case ze:var O=p;return Un(O)+".Consumer";case d:var U=p;return Un(U._context)+".Provider";case ae:return Xe(p,p.render,"ForwardRef");case W:var Y=p.displayName||null;return Y!==null?Y:ge(p.type)||"Memo";case oe:{var Ee=p,Oe=Ee._payload,pe=Ee._init;try{return ge(pe(Oe))}catch{return null}}}return null}var Ae=Object.assign,ct=0,ft,ve,Vt,Na,Xn,ca,At;function En(){}En.__reactDisabledLog=!0;function jn(){{if(ct===0){ft=console.log,ve=console.info,Vt=console.warn,Na=console.error,Xn=console.group,ca=console.groupCollapsed,At=console.groupEnd;var p={configurable:!0,enumerable:!0,value:En,writable:!0};Object.defineProperties(console,{info:p,log:p,warn:p,error:p,group:p,groupCollapsed:p,groupEnd:p})}ct++}}function Ma(){{if(ct--,ct===0){var p={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Ae({},p,{value:ft}),info:Ae({},p,{value:ve}),warn:Ae({},p,{value:Vt}),error:Ae({},p,{value:Na}),group:Ae({},p,{value:Xn}),groupCollapsed:Ae({},p,{value:ca}),groupEnd:Ae({},p,{value:At})})}ct<0&&He("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var fa=Kt.ReactCurrentDispatcher,da;function Kn(p,O,U){{if(da===void 0)try{throw Error()}catch(Ee){var Y=Ee.stack.trim().match(/\n( *(at )?)/);da=Y&&Y[1]||""}return`
`+da+p}}var Bt=!1,Cn;{var kn=typeof WeakMap=="function"?WeakMap:Map;Cn=new kn}function zn(p,O){if(!p||Bt)return"";{var U=Cn.get(p);if(U!==void 0)return U}var Y;Bt=!0;var Ee=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var Oe;Oe=fa.current,fa.current=null,jn();try{if(O){var pe=function(){throw Error()};if(Object.defineProperty(pe.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(pe,[])}catch(xn){Y=xn}Reflect.construct(p,[],pe)}else{try{pe.call()}catch(xn){Y=xn}p.call(pe.prototype)}}else{try{throw Error()}catch(xn){Y=xn}p()}}catch(xn){if(xn&&Y&&typeof xn.stack=="string"){for(var K=xn.stack.split(`
`),Rt=Y.stack.split(`
`),tt=K.length-1,nt=Rt.length-1;tt>=1&&nt>=0&&K[tt]!==Rt[nt];)nt--;for(;tt>=1&&nt>=0;tt--,nt--)if(K[tt]!==Rt[nt]){if(tt!==1||nt!==1)do if(tt--,nt--,nt<0||K[tt]!==Rt[nt]){var en=`
`+K[tt].replace(" at new "," at ");return p.displayName&&en.includes("<anonymous>")&&(en=en.replace("<anonymous>",p.displayName)),typeof p=="function"&&Cn.set(p,en),en}while(tt>=1&&nt>=0);break}}}finally{Bt=!1,fa.current=Oe,Ma(),Error.prepareStackTrace=Ee}var ja=p?p.displayName||p.name:"",Yi=ja?Kn(ja):"";return typeof p=="function"&&Cn.set(p,Yi),Yi}function mt(p,O,U){return zn(p,!1)}function Jt(p){var O=p.prototype;return!!(O&&O.isReactComponent)}function Lt(p,O,U){if(p==null)return"";if(typeof p=="function")return zn(p,Jt(p));if(typeof p=="string")return Kn(p);switch(p){case B:return Kn("Suspense");case q:return Kn("SuspenseList")}if(typeof p=="object")switch(p.$$typeof){case ae:return mt(p.render);case W:return Lt(p.type,O,U);case oe:{var Y=p,Ee=Y._payload,Oe=Y._init;try{return Lt(Oe(Ee),O,U)}catch{}}}return""}var Ut=Object.prototype.hasOwnProperty,Et={},Hn=Kt.ReactDebugCurrentFrame;function Jn(p){if(p){var O=p._owner,U=Lt(p.type,p._source,O?O.type:null);Hn.setExtraStackFrame(U)}else Hn.setExtraStackFrame(null)}function Rn(p,O,U,Y,Ee){{var Oe=Function.call.bind(Ut);for(var pe in p)if(Oe(p,pe)){var K=void 0;try{if(typeof p[pe]!="function"){var Rt=Error((Y||"React class")+": "+U+" type `"+pe+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof p[pe]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Rt.name="Invariant Violation",Rt}K=p[pe](O,pe,Y,U,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(tt){K=tt}K&&!(K instanceof Error)&&(Jn(Ee),He("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",Y||"React class",U,pe,typeof K),Jn(null)),K instanceof Error&&!(K.message in Et)&&(Et[K.message]=!0,Jn(Ee),He("Failed %s type: %s",U,K.message),Jn(null))}}}var va=Array.isArray;function Zn(p){return va(p)}function sn(p){{var O=typeof Symbol=="function"&&Symbol.toStringTag,U=O&&p[Symbol.toStringTag]||p.constructor.name||"Object";return U}}function ea(p){try{return Zt(p),!1}catch{return!0}}function Zt(p){return""+p}function Fn(p){if(ea(p))return He("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",sn(p)),Zt(p)}var dt=Kt.ReactCurrentOwner,ta={key:!0,ref:!0,__self:!0,__source:!0},Xa,Ka,j;j={};function I(p){if(Ut.call(p,"ref")){var O=Object.getOwnPropertyDescriptor(p,"ref").get;if(O&&O.isReactWarning)return!1}return p.ref!==void 0}function me(p){if(Ut.call(p,"key")){var O=Object.getOwnPropertyDescriptor(p,"key").get;if(O&&O.isReactWarning)return!1}return p.key!==void 0}function Ve(p,O){if(typeof p.ref=="string"&&dt.current&&O&&dt.current.stateNode!==O){var U=ge(dt.current.type);j[U]||(He('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',ge(dt.current.type),p.ref),j[U]=!0)}}function Be(p,O){{var U=function(){Xa||(Xa=!0,He("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",O))};U.isReactWarning=!0,Object.defineProperty(p,"key",{get:U,configurable:!0})}}function Ct(p,O){{var U=function(){Ka||(Ka=!0,He("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",O))};U.isReactWarning=!0,Object.defineProperty(p,"ref",{get:U,configurable:!0})}}var vt=function(p,O,U,Y,Ee,Oe,pe){var K={$$typeof:N,type:p,key:O,ref:U,props:pe,_owner:Oe};return K._store={},Object.defineProperty(K._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(K,"_self",{configurable:!1,enumerable:!1,writable:!1,value:Y}),Object.defineProperty(K,"_source",{configurable:!1,enumerable:!1,writable:!1,value:Ee}),Object.freeze&&(Object.freeze(K.props),Object.freeze(K)),K};function Tn(p,O,U,Y,Ee){{var Oe,pe={},K=null,Rt=null;U!==void 0&&(Fn(U),K=""+U),me(O)&&(Fn(O.key),K=""+O.key),I(O)&&(Rt=O.ref,Ve(O,Ee));for(Oe in O)Ut.call(O,Oe)&&!ta.hasOwnProperty(Oe)&&(pe[Oe]=O[Oe]);if(p&&p.defaultProps){var tt=p.defaultProps;for(Oe in tt)pe[Oe]===void 0&&(pe[Oe]=tt[Oe])}if(K||Rt){var nt=typeof p=="function"?p.displayName||p.name||"Unknown":p;K&&Be(pe,nt),Rt&&Ct(pe,nt)}return vt(p,K,Rt,Ee,Y,dt.current,pe)}}var Ge=Kt.ReactCurrentOwner,Vn=Kt.ReactDebugCurrentFrame;function Ze(p){if(p){var O=p._owner,U=Lt(p.type,p._source,O?O.type:null);Vn.setExtraStackFrame(U)}else Vn.setExtraStackFrame(null)}var et;et=!1;function Aa(p){return typeof p=="object"&&p!==null&&p.$$typeof===N}function pa(){{if(Ge.current){var p=ge(Ge.current.type);if(p)return`

Check the render method of \``+p+"`."}return""}}function ai(p){{if(p!==void 0){var O=p.fileName.replace(/^.*[\\\/]/,""),U=p.lineNumber;return`

Check your code at `+O+":"+U+"."}return""}}var Bi={};function ql(p){{var O=pa();if(!O){var U=typeof p=="string"?p:p.displayName||p.name;U&&(O=`

Check the top-level render call using <`+U+">.")}return O}}function ri(p,O){{if(!p._store||p._store.validated||p.key!=null)return;p._store.validated=!0;var U=ql(O);if(Bi[U])return;Bi[U]=!0;var Y="";p&&p._owner&&p._owner!==Ge.current&&(Y=" It was passed a child from "+ge(p._owner.type)+"."),Ze(p),He('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',U,Y),Ze(null)}}function ii(p,O){{if(typeof p!="object")return;if(Zn(p))for(var U=0;U<p.length;U++){var Y=p[U];Aa(Y)&&ri(Y,O)}else if(Aa(p))p._store&&(p._store.validated=!0);else if(p){var Ee=Ln(p);if(typeof Ee=="function"&&Ee!==p.entries)for(var Oe=Ee.call(p),pe;!(pe=Oe.next()).done;)Aa(pe.value)&&ri(pe.value,O)}}}function Ja(p){{var O=p.type;if(O==null||typeof O=="string")return;var U;if(typeof O=="function")U=O.propTypes;else if(typeof O=="object"&&(O.$$typeof===ae||O.$$typeof===W))U=O.propTypes;else return;if(U){var Y=ge(O);Rn(U,p.props,"prop",Y,p)}else if(O.PropTypes!==void 0&&!et){et=!0;var Ee=ge(O);He("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",Ee||"Unknown")}typeof O.getDefaultProps=="function"&&!O.getDefaultProps.isReactClassApproved&&He("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function xr(p){{for(var O=Object.keys(p.props),U=0;U<O.length;U++){var Y=O[U];if(Y!=="children"&&Y!=="key"){Ze(p),He("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",Y),Ze(null);break}}p.ref!==null&&(Ze(p),He("Invalid attribute `ref` supplied to `React.Fragment`."),Ze(null))}}function Za(p,O,U,Y,Ee,Oe){{var pe=ht(p);if(!pe){var K="";(p===void 0||typeof p=="object"&&p!==null&&Object.keys(p).length===0)&&(K+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var Rt=ai(Ee);Rt?K+=Rt:K+=pa();var tt;p===null?tt="null":Zn(p)?tt="array":p!==void 0&&p.$$typeof===N?(tt="<"+(ge(p.type)||"Unknown")+" />",K=" Did you accidentally export a JSX literal instead of a component?"):tt=typeof p,He("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",tt,K)}var nt=Tn(p,O,U,Ee,Oe);if(nt==null)return nt;if(pe){var en=O.children;if(en!==void 0)if(Y)if(Zn(en)){for(var ja=0;ja<en.length;ja++)ii(en[ja],p);Object.freeze&&Object.freeze(en)}else He("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ii(en,p)}return p===ce?xr(nt):Ja(nt),nt}}function La(p,O,U){return Za(p,O,U,!0)}function ha(p,O,U){return Za(p,O,U,!1)}var Ua=ha,li=La;Pl.Fragment=ce,Pl.jsx=Ua,Pl.jsxs=li}()),Pl}sh.exports=xS();var m=sh.exports,Xc={},vh={exports:{}},Kc={exports:{}},Jc={};/**
 * @license React
 * scheduler.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ph;function wS(){return ph||(ph=1,function(w){(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var N=!1,J=!1,ce=5;function ke(j,I){var me=j.length;j.push(I),ze(j,I,me)}function fe(j){return j.length===0?null:j[0]}function d(j){if(j.length===0)return null;var I=j[0],me=j.pop();return me!==I&&(j[0]=me,ae(j,me,0)),I}function ze(j,I,me){for(var Ve=me;Ve>0;){var Be=Ve-1>>>1,Ct=j[Be];if(B(Ct,I)>0)j[Be]=I,j[Ve]=Ct,Ve=Be;else return}}function ae(j,I,me){for(var Ve=me,Be=j.length,Ct=Be>>>1;Ve<Ct;){var vt=(Ve+1)*2-1,Tn=j[vt],Ge=vt+1,Vn=j[Ge];if(B(Tn,I)<0)Ge<Be&&B(Vn,Tn)<0?(j[Ve]=Vn,j[Ge]=I,Ve=Ge):(j[Ve]=Tn,j[vt]=I,Ve=vt);else if(Ge<Be&&B(Vn,I)<0)j[Ve]=Vn,j[Ge]=I,Ve=Ge;else return}}function B(j,I){var me=j.sortIndex-I.sortIndex;return me!==0?me:j.id-I.id}var q=1,W=2,oe=3,G=4,ye=5;function gn(j,I){}var Ln=typeof performance=="object"&&typeof performance.now=="function";if(Ln){var Kt=performance;w.unstable_now=function(){return Kt.now()}}else{var He=Date,Se=He.now();w.unstable_now=function(){return He.now()-Se}}var it=1073741823,se=-1,Je=250,De=5e3,bn=1e4,Sn=it,ht=[],Xe=[],Un=1,ge=null,Ae=oe,ct=!1,ft=!1,ve=!1,Vt=typeof setTimeout=="function"?setTimeout:null,Na=typeof clearTimeout=="function"?clearTimeout:null,Xn=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function ca(j){for(var I=fe(Xe);I!==null;){if(I.callback===null)d(Xe);else if(I.startTime<=j)d(Xe),I.sortIndex=I.expirationTime,ke(ht,I);else return;I=fe(Xe)}}function At(j){if(ve=!1,ca(j),!ft)if(fe(ht)!==null)ft=!0,Fn(En);else{var I=fe(Xe);I!==null&&dt(At,I.startTime-j)}}function En(j,I){ft=!1,ve&&(ve=!1,ta()),ct=!0;var me=Ae;try{var Ve;if(!J)return jn(j,I)}finally{ge=null,Ae=me,ct=!1}}function jn(j,I){var me=I;for(ca(me),ge=fe(ht);ge!==null&&!N&&!(ge.expirationTime>me&&(!j||Jn()));){var Ve=ge.callback;if(typeof Ve=="function"){ge.callback=null,Ae=ge.priorityLevel;var Be=ge.expirationTime<=me,Ct=Ve(Be);me=w.unstable_now(),typeof Ct=="function"?ge.callback=Ct:ge===fe(ht)&&d(ht),ca(me)}else d(ht);ge=fe(ht)}if(ge!==null)return!0;var vt=fe(Xe);return vt!==null&&dt(At,vt.startTime-me),!1}function Ma(j,I){switch(j){case q:case W:case oe:case G:case ye:break;default:j=oe}var me=Ae;Ae=j;try{return I()}finally{Ae=me}}function fa(j){var I;switch(Ae){case q:case W:case oe:I=oe;break;default:I=Ae;break}var me=Ae;Ae=I;try{return j()}finally{Ae=me}}function da(j){var I=Ae;return function(){var me=Ae;Ae=I;try{return j.apply(this,arguments)}finally{Ae=me}}}function Kn(j,I,me){var Ve=w.unstable_now(),Be;if(typeof me=="object"&&me!==null){var Ct=me.delay;typeof Ct=="number"&&Ct>0?Be=Ve+Ct:Be=Ve}else Be=Ve;var vt;switch(j){case q:vt=se;break;case W:vt=Je;break;case ye:vt=Sn;break;case G:vt=bn;break;case oe:default:vt=De;break}var Tn=Be+vt,Ge={id:Un++,callback:I,priorityLevel:j,startTime:Be,expirationTime:Tn,sortIndex:-1};return Be>Ve?(Ge.sortIndex=Be,ke(Xe,Ge),fe(ht)===null&&Ge===fe(Xe)&&(ve?ta():ve=!0,dt(At,Be-Ve))):(Ge.sortIndex=Tn,ke(ht,Ge),!ft&&!ct&&(ft=!0,Fn(En))),Ge}function Bt(){}function Cn(){!ft&&!ct&&(ft=!0,Fn(En))}function kn(){return fe(ht)}function zn(j){j.callback=null}function mt(){return Ae}var Jt=!1,Lt=null,Ut=-1,Et=ce,Hn=-1;function Jn(){var j=w.unstable_now()-Hn;return!(j<Et)}function Rn(){}function va(j){if(j<0||j>125){console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported");return}j>0?Et=Math.floor(1e3/j):Et=ce}var Zn=function(){if(Lt!==null){var j=w.unstable_now();Hn=j;var I=!0,me=!0;try{me=Lt(I,j)}finally{me?sn():(Jt=!1,Lt=null)}}else Jt=!1},sn;if(typeof Xn=="function")sn=function(){Xn(Zn)};else if(typeof MessageChannel<"u"){var ea=new MessageChannel,Zt=ea.port2;ea.port1.onmessage=Zn,sn=function(){Zt.postMessage(null)}}else sn=function(){Vt(Zn,0)};function Fn(j){Lt=j,Jt||(Jt=!0,sn())}function dt(j,I){Ut=Vt(function(){j(w.unstable_now())},I)}function ta(){Na(Ut),Ut=-1}var Xa=Rn,Ka=null;w.unstable_IdlePriority=ye,w.unstable_ImmediatePriority=q,w.unstable_LowPriority=G,w.unstable_NormalPriority=oe,w.unstable_Profiling=Ka,w.unstable_UserBlockingPriority=W,w.unstable_cancelCallback=zn,w.unstable_continueExecution=Cn,w.unstable_forceFrameRate=va,w.unstable_getCurrentPriorityLevel=mt,w.unstable_getFirstCallbackNode=kn,w.unstable_next=fa,w.unstable_pauseExecution=Bt,w.unstable_requestPaint=Xa,w.unstable_runWithPriority=Ma,w.unstable_scheduleCallback=Kn,w.unstable_shouldYield=Jn,w.unstable_wrapCallback=da,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()}(Jc)),Jc}var hh;function DS(){return hh||(hh=1,Kc.exports=wS()),Kc.exports}var yn={};/**
 * @license React
 * react-dom.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mh;function _S(){return mh||(mh=1,function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var w=Ic,N=DS(),J=w.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ce=!1;function ke(e){ce=e}function fe(e){if(!ce){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];ze("warn",e,n)}}function d(e){if(!ce){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];ze("error",e,n)}}function ze(e,t,n){{var a=J.ReactDebugCurrentFrame,r=a.getStackAddendum();r!==""&&(t+="%s",n=n.concat([r]));var i=n.map(function(l){return String(l)});i.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,i)}}var ae=0,B=1,q=2,W=3,oe=4,G=5,ye=6,gn=7,Ln=8,Kt=9,He=10,Se=11,it=12,se=13,Je=14,De=15,bn=16,Sn=17,ht=18,Xe=19,Un=21,ge=22,Ae=23,ct=24,ft=25,ve=!0,Vt=!1,Na=!1,Xn=!1,ca=!1,At=!0,En=!1,jn=!1,Ma=!0,fa=!0,da=!0,Kn=new Set,Bt={},Cn={};function kn(e,t){zn(e,t),zn(e+"Capture",t)}function zn(e,t){Bt[e]&&d("EventRegistry: More than one plugin attempted to publish the same registration name, `%s`.",e),Bt[e]=t;{var n=e.toLowerCase();Cn[n]=e,e==="onDoubleClick"&&(Cn.ondblclick=e)}for(var a=0;a<t.length;a++)Kn.add(t[a])}var mt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Jt=Object.prototype.hasOwnProperty;function Lt(e){{var t=typeof Symbol=="function"&&Symbol.toStringTag,n=t&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n}}function Ut(e){try{return Et(e),!1}catch{return!0}}function Et(e){return""+e}function Hn(e,t){if(Ut(e))return d("The provided `%s` attribute is an unsupported type %s. This value must be coerced to a string before before using it here.",t,Lt(e)),Et(e)}function Jn(e){if(Ut(e))return d("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Lt(e)),Et(e)}function Rn(e,t){if(Ut(e))return d("The provided `%s` prop is an unsupported type %s. This value must be coerced to a string before before using it here.",t,Lt(e)),Et(e)}function va(e,t){if(Ut(e))return d("The provided `%s` CSS property is an unsupported type %s. This value must be coerced to a string before before using it here.",t,Lt(e)),Et(e)}function Zn(e){if(Ut(e))return d("The provided HTML markup uses a value of unsupported type %s. This value must be coerced to a string before before using it here.",Lt(e)),Et(e)}function sn(e){if(Ut(e))return d("Form field values (value, checked, defaultValue, or defaultChecked props) must be strings, not %s. This value must be coerced to a string before before using it here.",Lt(e)),Et(e)}var ea=0,Zt=1,Fn=2,dt=3,ta=4,Xa=5,Ka=6,j=":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",I=j+"\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040",me=new RegExp("^["+j+"]["+I+"]*$"),Ve={},Be={};function Ct(e){return Jt.call(Be,e)?!0:Jt.call(Ve,e)?!1:me.test(e)?(Be[e]=!0,!0):(Ve[e]=!0,d("Invalid attribute name: `%s`",e),!1)}function vt(e,t,n){return t!==null?t.type===ea:n?!1:e.length>2&&(e[0]==="o"||e[0]==="O")&&(e[1]==="n"||e[1]==="N")}function Tn(e,t,n,a){if(n!==null&&n.type===ea)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":{if(a)return!1;if(n!==null)return!n.acceptsBooleans;var r=e.toLowerCase().slice(0,5);return r!=="data-"&&r!=="aria-"}default:return!1}}function Ge(e,t,n,a){if(t===null||typeof t>"u"||Tn(e,t,n,a))return!0;if(a)return!1;if(n!==null)switch(n.type){case dt:return!t;case ta:return t===!1;case Xa:return isNaN(t);case Ka:return isNaN(t)||t<1}return!1}function Vn(e){return et.hasOwnProperty(e)?et[e]:null}function Ze(e,t,n,a,r,i,l){this.acceptsBooleans=t===Fn||t===dt||t===ta,this.attributeName=a,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var et={},Aa=["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"];Aa.forEach(function(e){et[e]=new Ze(e,ea,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0],n=e[1];et[t]=new Ze(t,Zt,!1,n,null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){et[e]=new Ze(e,Fn,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){et[e]=new Ze(e,Fn,!1,e,null,!1,!1)}),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach(function(e){et[e]=new Ze(e,dt,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){et[e]=new Ze(e,dt,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){et[e]=new Ze(e,ta,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){et[e]=new Ze(e,Ka,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){et[e]=new Ze(e,Xa,!1,e.toLowerCase(),null,!1,!1)});var pa=/[\-\:]([a-z])/g,ai=function(e){return e[1].toUpperCase()};["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach(function(e){var t=e.replace(pa,ai);et[t]=new Ze(t,Zt,!1,e,null,!1,!1)}),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach(function(e){var t=e.replace(pa,ai);et[t]=new Ze(t,Zt,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(pa,ai);et[t]=new Ze(t,Zt,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){et[e]=new Ze(e,Zt,!1,e.toLowerCase(),null,!1,!1)});var Bi="xlinkHref";et[Bi]=new Ze("xlinkHref",Zt,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){et[e]=new Ze(e,Zt,!1,e.toLowerCase(),null,!0,!0)});var ql=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*\:/i,ri=!1;function ii(e){!ri&&ql.test(e)&&(ri=!0,d("A future version of React will block javascript: URLs as a security precaution. Use event handlers instead if you can. If you need to generate unsafe HTML try using dangerouslySetInnerHTML instead. React was passed %s.",JSON.stringify(e)))}function Ja(e,t,n,a){if(a.mustUseProperty){var r=a.propertyName;return e[r]}else{Hn(n,t),a.sanitizeURL&&ii(""+n);var i=a.attributeName,l=null;if(a.type===ta){if(e.hasAttribute(i)){var u=e.getAttribute(i);return u===""?!0:Ge(t,n,a,!1)?u:u===""+n?n:u}}else if(e.hasAttribute(i)){if(Ge(t,n,a,!1))return e.getAttribute(i);if(a.type===dt)return n;l=e.getAttribute(i)}return Ge(t,n,a,!1)?l===null?n:l:l===""+n?n:l}}function xr(e,t,n,a){{if(!Ct(t))return;if(!e.hasAttribute(t))return n===void 0?void 0:null;var r=e.getAttribute(t);return Hn(n,t),r===""+n?n:r}}function Za(e,t,n,a){var r=Vn(t);if(!vt(t,r,a)){if(Ge(t,n,r,a)&&(n=null),a||r===null){if(Ct(t)){var i=t;n===null?e.removeAttribute(i):(Hn(n,t),e.setAttribute(i,""+n))}return}var l=r.mustUseProperty;if(l){var u=r.propertyName;if(n===null){var o=r.type;e[u]=o===dt?!1:""}else e[u]=n;return}var c=r.attributeName,f=r.attributeNamespace;if(n===null)e.removeAttribute(c);else{var y=r.type,h;y===dt||y===ta&&n===!0?h="":(Hn(n,c),h=""+n,r.sanitizeURL&&ii(h.toString())),f?e.setAttributeNS(f,c,h):e.setAttribute(c,h)}}}var La=Symbol.for("react.element"),ha=Symbol.for("react.portal"),Ua=Symbol.for("react.fragment"),li=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),O=Symbol.for("react.provider"),U=Symbol.for("react.context"),Y=Symbol.for("react.forward_ref"),Ee=Symbol.for("react.suspense"),Oe=Symbol.for("react.suspense_list"),pe=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),Rt=Symbol.for("react.scope"),tt=Symbol.for("react.debug_trace_mode"),nt=Symbol.for("react.offscreen"),en=Symbol.for("react.legacy_hidden"),ja=Symbol.for("react.cache"),Yi=Symbol.for("react.tracing_marker"),xn=Symbol.iterator,tf="@@iterator";function er(e){if(e===null||typeof e!="object")return null;var t=xn&&e[xn]||e[tf];return typeof t=="function"?t:null}var Ce=Object.assign,ui=0,wr,Gl,Ql,Wl,Il,Xl,Kl;function Jl(){}Jl.__reactDisabledLog=!0;function zo(){{if(ui===0){wr=console.log,Gl=console.info,Ql=console.warn,Wl=console.error,Il=console.group,Xl=console.groupCollapsed,Kl=console.groupEnd;var e={configurable:!0,enumerable:!0,value:Jl,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}ui++}}function nf(){{if(ui--,ui===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Ce({},e,{value:wr}),info:Ce({},e,{value:Gl}),warn:Ce({},e,{value:Ql}),error:Ce({},e,{value:Wl}),group:Ce({},e,{value:Il}),groupCollapsed:Ce({},e,{value:Xl}),groupEnd:Ce({},e,{value:Kl})})}ui<0&&d("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Zl=J.ReactCurrentDispatcher,oi;function na(e,t,n){{if(oi===void 0)try{throw Error()}catch(r){var a=r.stack.trim().match(/\n( *(at )?)/);oi=a&&a[1]||""}return`
`+oi+e}}var Dr=!1,_r;{var $i=typeof WeakMap=="function"?WeakMap:Map;_r=new $i}function eu(e,t){if(!e||Dr)return"";{var n=_r.get(e);if(n!==void 0)return n}var a;Dr=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var i;i=Zl.current,Zl.current=null,zo();try{if(t){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(l,[])}catch(E){a=E}Reflect.construct(e,[],l)}else{try{l.call()}catch(E){a=E}e.call(l.prototype)}}else{try{throw Error()}catch(E){a=E}e()}}catch(E){if(E&&a&&typeof E.stack=="string"){for(var u=E.stack.split(`
`),o=a.stack.split(`
`),c=u.length-1,f=o.length-1;c>=1&&f>=0&&u[c]!==o[f];)f--;for(;c>=1&&f>=0;c--,f--)if(u[c]!==o[f]){if(c!==1||f!==1)do if(c--,f--,f<0||u[c]!==o[f]){var y=`
`+u[c].replace(" at new "," at ");return e.displayName&&y.includes("<anonymous>")&&(y=y.replace("<anonymous>",e.displayName)),typeof e=="function"&&_r.set(e,y),y}while(c>=1&&f>=0);break}}}finally{Dr=!1,Zl.current=i,nf(),Error.prepareStackTrace=r}var h=e?e.displayName||e.name:"",S=h?na(h):"";return typeof e=="function"&&_r.set(e,S),S}function Ho(e,t,n){return eu(e,!0)}function tu(e,t,n){return eu(e,!1)}function af(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function Or(e,t,n){if(e==null)return"";if(typeof e=="function")return eu(e,af(e));if(typeof e=="string")return na(e);switch(e){case Ee:return na("Suspense");case Oe:return na("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case Y:return tu(e.render);case pe:return Or(e.type,t,n);case K:{var a=e,r=a._payload,i=a._init;try{return Or(i(r),t,n)}catch{}}}return""}function Fo(e){switch(e._debugOwner&&e._debugOwner.type,e._debugSource,e.tag){case G:return na(e.type);case bn:return na("Lazy");case se:return na("Suspense");case Xe:return na("SuspenseList");case ae:case q:case De:return tu(e.type);case Se:return tu(e.type.render);case B:return Ho(e.type);default:return""}}function nu(e){try{var t="",n=e;do t+=Fo(n),n=n.return;while(n);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Pi(e,t,n){var a=e.displayName;if(a)return a;var r=t.displayName||t.name||"";return r!==""?n+"("+r+")":n}function Vo(e){return e.displayName||"Context"}function Le(e){if(e==null)return null;if(typeof e.tag=="number"&&d("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ua:return"Fragment";case ha:return"Portal";case p:return"Profiler";case li:return"StrictMode";case Ee:return"Suspense";case Oe:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case U:var t=e;return Vo(t)+".Consumer";case O:var n=e;return Vo(n._context)+".Provider";case Y:return Pi(e,e.render,"ForwardRef");case pe:var a=e.displayName||null;return a!==null?a:Le(e.type)||"Memo";case K:{var r=e,i=r._payload,l=r._init;try{return Le(l(i))}catch{return null}}}return null}function au(e,t,n){var a=t.displayName||t.name||"";return e.displayName||(a!==""?n+"("+a+")":n)}function ru(e){return e.displayName||"Context"}function de(e){var t=e.tag,n=e.type;switch(t){case ct:return"Cache";case Kt:var a=n;return ru(a)+".Consumer";case He:var r=n;return ru(r._context)+".Provider";case ht:return"DehydratedFragment";case Se:return au(n,n.render,"ForwardRef");case gn:return"Fragment";case G:return n;case oe:return"Portal";case W:return"Root";case ye:return"Text";case bn:return Le(n);case Ln:return n===li?"StrictMode":"Mode";case ge:return"Offscreen";case it:return"Profiler";case Un:return"Scope";case se:return"Suspense";case Xe:return"SuspenseList";case ft:return"TracingMarker";case B:case ae:case Sn:case q:case Je:case De:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n;break}return null}var Bo=J.ReactDebugCurrentFrame,tn=null,si=!1;function Nr(){{if(tn===null)return null;var e=tn._debugOwner;if(e!==null&&typeof e<"u")return de(e)}return null}function Yo(){return tn===null?"":nu(tn)}function jt(){Bo.getCurrentStack=null,tn=null,si=!1}function lt(e){Bo.getCurrentStack=e===null?null:Yo,tn=e,si=!1}function $o(){return tn}function Bn(e){si=e}function wn(e){return""+e}function ka(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return sn(e),e;default:return""}}var rf={button:!0,checkbox:!0,image:!0,hidden:!0,radio:!0,reset:!0,submit:!0};function qi(e,t){rf[t.type]||t.onChange||t.onInput||t.readOnly||t.disabled||t.value==null||d("You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set either `onChange` or `readOnly`."),t.onChange||t.readOnly||t.disabled||t.checked==null||d("You provided a `checked` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultChecked`. Otherwise, set either `onChange` or `readOnly`.")}function ci(e){var t=e.type,n=e.nodeName;return n&&n.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Po(e){return e._valueTracker}function Mr(e){e._valueTracker=null}function qo(e){var t="";return e&&(ci(e)?t=e.checked?"true":"false":t=e.value),t}function lf(e){var t=ci(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t);sn(e[t]);var a=""+e[t];if(!(e.hasOwnProperty(t)||typeof n>"u"||typeof n.get!="function"||typeof n.set!="function")){var r=n.get,i=n.set;Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(u){sn(u),a=""+u,i.call(this,u)}}),Object.defineProperty(e,t,{enumerable:n.enumerable});var l={getValue:function(){return a},setValue:function(u){sn(u),a=""+u},stopTracking:function(){Mr(e),delete e[t]}};return l}}function tr(e){Po(e)||(e._valueTracker=lf(e))}function Gi(e){if(!e)return!1;var t=Po(e);if(!t)return!0;var n=t.getValue(),a=qo(e);return a!==n?(t.setValue(a),!0):!1}function Ar(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Qi=!1,Go=!1,Qo=!1,Wo=!1;function Io(e){var t=e.type==="checkbox"||e.type==="radio";return t?e.checked!=null:e.value!=null}function s(e,t){var n=e,a=t.checked,r=Ce({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:a??n._wrapperState.initialChecked});return r}function v(e,t){qi("input",t),t.checked!==void 0&&t.defaultChecked!==void 0&&!Go&&(d("%s contains an input of type %s with both checked and defaultChecked props. Input elements must be either controlled or uncontrolled (specify either the checked prop, or the defaultChecked prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://reactjs.org/link/controlled-components",Nr()||"A component",t.type),Go=!0),t.value!==void 0&&t.defaultValue!==void 0&&!Qi&&(d("%s contains an input of type %s with both value and defaultValue props. Input elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://reactjs.org/link/controlled-components",Nr()||"A component",t.type),Qi=!0);var n=e,a=t.defaultValue==null?"":t.defaultValue;n._wrapperState={initialChecked:t.checked!=null?t.checked:t.defaultChecked,initialValue:ka(t.value!=null?t.value:a),controlled:Io(t)}}function C(e,t){var n=e,a=t.checked;a!=null&&Za(n,"checked",a,!1)}function R(e,t){var n=e;{var a=Io(t);!n._wrapperState.controlled&&a&&!Wo&&(d("A component is changing an uncontrolled input to be controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://reactjs.org/link/controlled-components"),Wo=!0),n._wrapperState.controlled&&!a&&!Qo&&(d("A component is changing a controlled input to be uncontrolled. This is likely caused by the value changing from a defined to undefined, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://reactjs.org/link/controlled-components"),Qo=!0)}C(e,t);var r=ka(t.value),i=t.type;if(r!=null)i==="number"?(r===0&&n.value===""||n.value!=r)&&(n.value=wn(r)):n.value!==wn(r)&&(n.value=wn(r));else if(i==="submit"||i==="reset"){n.removeAttribute("value");return}t.hasOwnProperty("value")?re(n,t.type,r):t.hasOwnProperty("defaultValue")&&re(n,t.type,ka(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(n.defaultChecked=!!t.defaultChecked)}function A(e,t,n){var a=e;if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type,i=r==="submit"||r==="reset";if(i&&(t.value===void 0||t.value===null))return;var l=wn(a._wrapperState.initialValue);n||l!==a.value&&(a.value=l),a.defaultValue=l}var u=a.name;u!==""&&(a.name=""),a.defaultChecked=!a.defaultChecked,a.defaultChecked=!!a._wrapperState.initialChecked,u!==""&&(a.name=u)}function te(e,t){var n=e;R(n,t),P(n,t)}function P(e,t){var n=t.name;if(t.type==="radio"&&n!=null){for(var a=e;a.parentNode;)a=a.parentNode;Hn(n,"name");for(var r=a.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),i=0;i<r.length;i++){var l=r[i];if(!(l===e||l.form!==e.form)){var u=js(l);if(!u)throw new Error("ReactDOMInput: Mixing React and non-React radio inputs with the same `name` is not supported.");Gi(l),R(l,u)}}}}function re(e,t,n){(t!=="number"||Ar(e.ownerDocument)!==e)&&(n==null?e.defaultValue=wn(e._wrapperState.initialValue):e.defaultValue!==wn(n)&&(e.defaultValue=wn(n)))}var Re=!1,Ye=!1,Qe=!1;function We(e,t){t.value==null&&(typeof t.children=="object"&&t.children!==null?w.Children.forEach(t.children,function(n){n!=null&&(typeof n=="string"||typeof n=="number"||Ye||(Ye=!0,d("Cannot infer the option value of complex children. Pass a `value` prop or use a plain string as children to <option>.")))}):t.dangerouslySetInnerHTML!=null&&(Qe||(Qe=!0,d("Pass a `value` prop if you set dangerouslyInnerHTML so React knows which value should be selected.")))),t.selected!=null&&!Re&&(d("Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>."),Re=!0)}function at(e,t){t.value!=null&&e.setAttribute("value",wn(ka(t.value)))}var ot=Array.isArray;function Ue(e){return ot(e)}var Lr;Lr=!1;function Wi(){var e=Nr();return e?`

Check the render method of \``+e+"`.":""}var iu=["value","defaultValue"];function uf(e){{qi("select",e);for(var t=0;t<iu.length;t++){var n=iu[t];if(e[n]!=null){var a=Ue(e[n]);e.multiple&&!a?d("The `%s` prop supplied to <select> must be an array if `multiple` is true.%s",n,Wi()):!e.multiple&&a&&d("The `%s` prop supplied to <select> must be a scalar value if `multiple` is false.%s",n,Wi())}}}}function nr(e,t,n,a){var r=e.options;if(t){for(var i=n,l={},u=0;u<i.length;u++)l["$"+i[u]]=!0;for(var o=0;o<r.length;o++){var c=l.hasOwnProperty("$"+r[o].value);r[o].selected!==c&&(r[o].selected=c),c&&a&&(r[o].defaultSelected=!0)}}else{for(var f=wn(ka(n)),y=null,h=0;h<r.length;h++){if(r[h].value===f){r[h].selected=!0,a&&(r[h].defaultSelected=!0);return}y===null&&!r[h].disabled&&(y=r[h])}y!==null&&(y.selected=!0)}}function lu(e,t){return Ce({},t,{value:void 0})}function uu(e,t){var n=e;uf(t),n._wrapperState={wasMultiple:!!t.multiple},t.value!==void 0&&t.defaultValue!==void 0&&!Lr&&(d("Select elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled select element and remove one of these props. More info: https://reactjs.org/link/controlled-components"),Lr=!0)}function of(e,t){var n=e;n.multiple=!!t.multiple;var a=t.value;a!=null?nr(n,!!t.multiple,a,!1):t.defaultValue!=null&&nr(n,!!t.multiple,t.defaultValue,!0)}function PS(e,t){var n=e,a=n._wrapperState.wasMultiple;n._wrapperState.wasMultiple=!!t.multiple;var r=t.value;r!=null?nr(n,!!t.multiple,r,!1):a!==!!t.multiple&&(t.defaultValue!=null?nr(n,!!t.multiple,t.defaultValue,!0):nr(n,!!t.multiple,t.multiple?[]:"",!1))}function qS(e,t){var n=e,a=t.value;a!=null&&nr(n,!!t.multiple,a,!1)}var yh=!1;function sf(e,t){var n=e;if(t.dangerouslySetInnerHTML!=null)throw new Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");var a=Ce({},t,{value:void 0,defaultValue:void 0,children:wn(n._wrapperState.initialValue)});return a}function gh(e,t){var n=e;qi("textarea",t),t.value!==void 0&&t.defaultValue!==void 0&&!yh&&(d("%s contains a textarea with both value and defaultValue props. Textarea elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled textarea and remove one of these props. More info: https://reactjs.org/link/controlled-components",Nr()||"A component"),yh=!0);var a=t.value;if(a==null){var r=t.children,i=t.defaultValue;if(r!=null){d("Use the `defaultValue` or `value` props instead of setting children on <textarea>.");{if(i!=null)throw new Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ue(r)){if(r.length>1)throw new Error("<textarea> can only have at most one child.");r=r[0]}i=r}}i==null&&(i=""),a=i}n._wrapperState={initialValue:ka(a)}}function bh(e,t){var n=e,a=ka(t.value),r=ka(t.defaultValue);if(a!=null){var i=wn(a);i!==n.value&&(n.value=i),t.defaultValue==null&&n.defaultValue!==i&&(n.defaultValue=i)}r!=null&&(n.defaultValue=wn(r))}function Sh(e,t){var n=e,a=n.textContent;a===n._wrapperState.initialValue&&a!==""&&a!==null&&(n.value=a)}function GS(e,t){bh(e,t)}var ar="http://www.w3.org/1999/xhtml",QS="http://www.w3.org/1998/Math/MathML",cf="http://www.w3.org/2000/svg";function ff(e){switch(e){case"svg":return cf;case"math":return QS;default:return ar}}function df(e,t){return e==null||e===ar?ff(t):e===cf&&t==="foreignObject"?ar:e}var WS=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,a,r){MSApp.execUnsafeLocalFunction(function(){return e(t,n,a,r)})}:e},Xo,Eh=WS(function(e,t){if(e.namespaceURI===cf&&!("innerHTML"in e)){Xo=Xo||document.createElement("div"),Xo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>";for(var n=Xo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild);return}e.innerHTML=t}),Dn=1,rr=3,yt=8,ir=9,vf=11,Ko=function(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===rr){n.nodeValue=t;return}}e.textContent=t},IS={animation:["animationDelay","animationDirection","animationDuration","animationFillMode","animationIterationCount","animationName","animationPlayState","animationTimingFunction"],background:["backgroundAttachment","backgroundClip","backgroundColor","backgroundImage","backgroundOrigin","backgroundPositionX","backgroundPositionY","backgroundRepeat","backgroundSize"],backgroundPosition:["backgroundPositionX","backgroundPositionY"],border:["borderBottomColor","borderBottomStyle","borderBottomWidth","borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth","borderLeftColor","borderLeftStyle","borderLeftWidth","borderRightColor","borderRightStyle","borderRightWidth","borderTopColor","borderTopStyle","borderTopWidth"],borderBlockEnd:["borderBlockEndColor","borderBlockEndStyle","borderBlockEndWidth"],borderBlockStart:["borderBlockStartColor","borderBlockStartStyle","borderBlockStartWidth"],borderBottom:["borderBottomColor","borderBottomStyle","borderBottomWidth"],borderColor:["borderBottomColor","borderLeftColor","borderRightColor","borderTopColor"],borderImage:["borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth"],borderInlineEnd:["borderInlineEndColor","borderInlineEndStyle","borderInlineEndWidth"],borderInlineStart:["borderInlineStartColor","borderInlineStartStyle","borderInlineStartWidth"],borderLeft:["borderLeftColor","borderLeftStyle","borderLeftWidth"],borderRadius:["borderBottomLeftRadius","borderBottomRightRadius","borderTopLeftRadius","borderTopRightRadius"],borderRight:["borderRightColor","borderRightStyle","borderRightWidth"],borderStyle:["borderBottomStyle","borderLeftStyle","borderRightStyle","borderTopStyle"],borderTop:["borderTopColor","borderTopStyle","borderTopWidth"],borderWidth:["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth"],columnRule:["columnRuleColor","columnRuleStyle","columnRuleWidth"],columns:["columnCount","columnWidth"],flex:["flexBasis","flexGrow","flexShrink"],flexFlow:["flexDirection","flexWrap"],font:["fontFamily","fontFeatureSettings","fontKerning","fontLanguageOverride","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition","fontWeight","lineHeight"],fontVariant:["fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition"],gap:["columnGap","rowGap"],grid:["gridAutoColumns","gridAutoFlow","gridAutoRows","gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],gridArea:["gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart"],gridColumn:["gridColumnEnd","gridColumnStart"],gridColumnGap:["columnGap"],gridGap:["columnGap","rowGap"],gridRow:["gridRowEnd","gridRowStart"],gridRowGap:["rowGap"],gridTemplate:["gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],listStyle:["listStyleImage","listStylePosition","listStyleType"],margin:["marginBottom","marginLeft","marginRight","marginTop"],marker:["markerEnd","markerMid","markerStart"],mask:["maskClip","maskComposite","maskImage","maskMode","maskOrigin","maskPositionX","maskPositionY","maskRepeat","maskSize"],maskPosition:["maskPositionX","maskPositionY"],outline:["outlineColor","outlineStyle","outlineWidth"],overflow:["overflowX","overflowY"],padding:["paddingBottom","paddingLeft","paddingRight","paddingTop"],placeContent:["alignContent","justifyContent"],placeItems:["alignItems","justifyItems"],placeSelf:["alignSelf","justifySelf"],textDecoration:["textDecorationColor","textDecorationLine","textDecorationStyle"],textEmphasis:["textEmphasisColor","textEmphasisStyle"],transition:["transitionDelay","transitionDuration","transitionProperty","transitionTimingFunction"],wordWrap:["overflowWrap"]},ou={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0};function XS(e,t){return e+t.charAt(0).toUpperCase()+t.substring(1)}var KS=["Webkit","ms","Moz","O"];Object.keys(ou).forEach(function(e){KS.forEach(function(t){ou[XS(t,e)]=ou[e]})});function pf(e,t,n){var a=t==null||typeof t=="boolean"||t==="";return a?"":!n&&typeof t=="number"&&t!==0&&!(ou.hasOwnProperty(e)&&ou[e])?t+"px":(va(t,e),(""+t).trim())}var JS=/([A-Z])/g,ZS=/^ms-/;function eE(e){return e.replace(JS,"-$1").toLowerCase().replace(ZS,"-ms-")}var Ch=function(){};{var tE=/^(?:webkit|moz|o)[A-Z]/,nE=/^-ms-/,aE=/-(.)/g,Rh=/;\s*$/,Ii={},hf={},Th=!1,xh=!1,rE=function(e){return e.replace(aE,function(t,n){return n.toUpperCase()})},iE=function(e){Ii.hasOwnProperty(e)&&Ii[e]||(Ii[e]=!0,d("Unsupported style property %s. Did you mean %s?",e,rE(e.replace(nE,"ms-"))))},lE=function(e){Ii.hasOwnProperty(e)&&Ii[e]||(Ii[e]=!0,d("Unsupported vendor-prefixed style property %s. Did you mean %s?",e,e.charAt(0).toUpperCase()+e.slice(1)))},uE=function(e,t){hf.hasOwnProperty(t)&&hf[t]||(hf[t]=!0,d(`Style property values shouldn't contain a semicolon. Try "%s: %s" instead.`,e,t.replace(Rh,"")))},oE=function(e,t){Th||(Th=!0,d("`NaN` is an invalid value for the `%s` css style property.",e))},sE=function(e,t){xh||(xh=!0,d("`Infinity` is an invalid value for the `%s` css style property.",e))};Ch=function(e,t){e.indexOf("-")>-1?iE(e):tE.test(e)?lE(e):Rh.test(t)&&uE(e,t),typeof t=="number"&&(isNaN(t)?oE(e,t):isFinite(t)||sE(e,t))}}var cE=Ch;function fE(e){{var t="",n="";for(var a in e)if(e.hasOwnProperty(a)){var r=e[a];if(r!=null){var i=a.indexOf("--")===0;t+=n+(i?a:eE(a))+":",t+=pf(a,r,i),n=";"}}return t||null}}function wh(e,t){var n=e.style;for(var a in t)if(t.hasOwnProperty(a)){var r=a.indexOf("--")===0;r||cE(a,t[a]);var i=pf(a,t[a],r);a==="float"&&(a="cssFloat"),r?n.setProperty(a,i):n[a]=i}}function dE(e){return e==null||typeof e=="boolean"||e===""}function Dh(e){var t={};for(var n in e)for(var a=IS[n]||[n],r=0;r<a.length;r++)t[a[r]]=n;return t}function vE(e,t){{if(!t)return;var n=Dh(e),a=Dh(t),r={};for(var i in n){var l=n[i],u=a[i];if(u&&l!==u){var o=l+","+u;if(r[o])continue;r[o]=!0,d("%s a style property during rerender (%s) when a conflicting property is set (%s) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.",dE(e[l])?"Removing":"Updating",l,u)}}}}var pE={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},hE=Ce({menuitem:!0},pE),mE="__html";function mf(e,t){if(t){if(hE[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw new Error(e+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw new Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if(typeof t.dangerouslySetInnerHTML!="object"||!(mE in t.dangerouslySetInnerHTML))throw new Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.")}if(!t.suppressContentEditableWarning&&t.contentEditable&&t.children!=null&&d("A component is `contentEditable` and contains `children` managed by React. It is now your responsibility to guarantee that none of those nodes are unexpectedly modified or duplicated. This is probably not intentional."),t.style!=null&&typeof t.style!="object")throw new Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.")}}function fi(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jo={accept:"accept",acceptcharset:"acceptCharset","accept-charset":"acceptCharset",accesskey:"accessKey",action:"action",allowfullscreen:"allowFullScreen",alt:"alt",as:"as",async:"async",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autosave:"autoSave",capture:"capture",cellpadding:"cellPadding",cellspacing:"cellSpacing",challenge:"challenge",charset:"charSet",checked:"checked",children:"children",cite:"cite",class:"className",classid:"classID",classname:"className",cols:"cols",colspan:"colSpan",content:"content",contenteditable:"contentEditable",contextmenu:"contextMenu",controls:"controls",controlslist:"controlsList",coords:"coords",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",data:"data",datetime:"dateTime",default:"default",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",defer:"defer",dir:"dir",disabled:"disabled",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback",download:"download",draggable:"draggable",enctype:"encType",enterkeyhint:"enterKeyHint",for:"htmlFor",form:"form",formmethod:"formMethod",formaction:"formAction",formenctype:"formEncType",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",headers:"headers",height:"height",hidden:"hidden",high:"high",href:"href",hreflang:"hrefLang",htmlfor:"htmlFor",httpequiv:"httpEquiv","http-equiv":"httpEquiv",icon:"icon",id:"id",imagesizes:"imageSizes",imagesrcset:"imageSrcSet",innerhtml:"innerHTML",inputmode:"inputMode",integrity:"integrity",is:"is",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",keyparams:"keyParams",keytype:"keyType",kind:"kind",label:"label",lang:"lang",list:"list",loop:"loop",low:"low",manifest:"manifest",marginwidth:"marginWidth",marginheight:"marginHeight",max:"max",maxlength:"maxLength",media:"media",mediagroup:"mediaGroup",method:"method",min:"min",minlength:"minLength",multiple:"multiple",muted:"muted",name:"name",nomodule:"noModule",nonce:"nonce",novalidate:"noValidate",open:"open",optimum:"optimum",pattern:"pattern",placeholder:"placeholder",playsinline:"playsInline",poster:"poster",preload:"preload",profile:"profile",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rel:"rel",required:"required",reversed:"reversed",role:"role",rows:"rows",rowspan:"rowSpan",sandbox:"sandbox",scope:"scope",scoped:"scoped",scrolling:"scrolling",seamless:"seamless",selected:"selected",shape:"shape",size:"size",sizes:"sizes",span:"span",spellcheck:"spellCheck",src:"src",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",start:"start",step:"step",style:"style",summary:"summary",tabindex:"tabIndex",target:"target",title:"title",type:"type",usemap:"useMap",value:"value",width:"width",wmode:"wmode",wrap:"wrap",about:"about",accentheight:"accentHeight","accent-height":"accentHeight",accumulate:"accumulate",additive:"additive",alignmentbaseline:"alignmentBaseline","alignment-baseline":"alignmentBaseline",allowreorder:"allowReorder",alphabetic:"alphabetic",amplitude:"amplitude",arabicform:"arabicForm","arabic-form":"arabicForm",ascent:"ascent",attributename:"attributeName",attributetype:"attributeType",autoreverse:"autoReverse",azimuth:"azimuth",basefrequency:"baseFrequency",baselineshift:"baselineShift","baseline-shift":"baselineShift",baseprofile:"baseProfile",bbox:"bbox",begin:"begin",bias:"bias",by:"by",calcmode:"calcMode",capheight:"capHeight","cap-height":"capHeight",clip:"clip",clippath:"clipPath","clip-path":"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","clip-rule":"clipRule",color:"color",colorinterpolation:"colorInterpolation","color-interpolation":"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters","color-interpolation-filters":"colorInterpolationFilters",colorprofile:"colorProfile","color-profile":"colorProfile",colorrendering:"colorRendering","color-rendering":"colorRendering",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",cursor:"cursor",cx:"cx",cy:"cy",d:"d",datatype:"datatype",decelerate:"decelerate",descent:"descent",diffuseconstant:"diffuseConstant",direction:"direction",display:"display",divisor:"divisor",dominantbaseline:"dominantBaseline","dominant-baseline":"dominantBaseline",dur:"dur",dx:"dx",dy:"dy",edgemode:"edgeMode",elevation:"elevation",enablebackground:"enableBackground","enable-background":"enableBackground",end:"end",exponent:"exponent",externalresourcesrequired:"externalResourcesRequired",fill:"fill",fillopacity:"fillOpacity","fill-opacity":"fillOpacity",fillrule:"fillRule","fill-rule":"fillRule",filter:"filter",filterres:"filterRes",filterunits:"filterUnits",floodopacity:"floodOpacity","flood-opacity":"floodOpacity",floodcolor:"floodColor","flood-color":"floodColor",focusable:"focusable",fontfamily:"fontFamily","font-family":"fontFamily",fontsize:"fontSize","font-size":"fontSize",fontsizeadjust:"fontSizeAdjust","font-size-adjust":"fontSizeAdjust",fontstretch:"fontStretch","font-stretch":"fontStretch",fontstyle:"fontStyle","font-style":"fontStyle",fontvariant:"fontVariant","font-variant":"fontVariant",fontweight:"fontWeight","font-weight":"fontWeight",format:"format",from:"from",fx:"fx",fy:"fy",g1:"g1",g2:"g2",glyphname:"glyphName","glyph-name":"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal","glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical","glyph-orientation-vertical":"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",hanging:"hanging",horizadvx:"horizAdvX","horiz-adv-x":"horizAdvX",horizoriginx:"horizOriginX","horiz-origin-x":"horizOriginX",ideographic:"ideographic",imagerendering:"imageRendering","image-rendering":"imageRendering",in2:"in2",in:"in",inlist:"inlist",intercept:"intercept",k1:"k1",k2:"k2",k3:"k3",k4:"k4",k:"k",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",kerning:"kerning",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",letterspacing:"letterSpacing","letter-spacing":"letterSpacing",lightingcolor:"lightingColor","lighting-color":"lightingColor",limitingconeangle:"limitingConeAngle",local:"local",markerend:"markerEnd","marker-end":"markerEnd",markerheight:"markerHeight",markermid:"markerMid","marker-mid":"markerMid",markerstart:"markerStart","marker-start":"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",mask:"mask",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",mathematical:"mathematical",mode:"mode",numoctaves:"numOctaves",offset:"offset",opacity:"opacity",operator:"operator",order:"order",orient:"orient",orientation:"orientation",origin:"origin",overflow:"overflow",overlineposition:"overlinePosition","overline-position":"overlinePosition",overlinethickness:"overlineThickness","overline-thickness":"overlineThickness",paintorder:"paintOrder","paint-order":"paintOrder",panose1:"panose1","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointerevents:"pointerEvents","pointer-events":"pointerEvents",points:"points",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",prefix:"prefix",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",property:"property",r:"r",radius:"radius",refx:"refX",refy:"refY",renderingintent:"renderingIntent","rendering-intent":"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",resource:"resource",restart:"restart",result:"result",results:"results",rotate:"rotate",rx:"rx",ry:"ry",scale:"scale",security:"security",seed:"seed",shaperendering:"shapeRendering","shape-rendering":"shapeRendering",slope:"slope",spacing:"spacing",specularconstant:"specularConstant",specularexponent:"specularExponent",speed:"speed",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stemh:"stemh",stemv:"stemv",stitchtiles:"stitchTiles",stopcolor:"stopColor","stop-color":"stopColor",stopopacity:"stopOpacity","stop-opacity":"stopOpacity",strikethroughposition:"strikethroughPosition","strikethrough-position":"strikethroughPosition",strikethroughthickness:"strikethroughThickness","strikethrough-thickness":"strikethroughThickness",string:"string",stroke:"stroke",strokedasharray:"strokeDasharray","stroke-dasharray":"strokeDasharray",strokedashoffset:"strokeDashoffset","stroke-dashoffset":"strokeDashoffset",strokelinecap:"strokeLinecap","stroke-linecap":"strokeLinecap",strokelinejoin:"strokeLinejoin","stroke-linejoin":"strokeLinejoin",strokemiterlimit:"strokeMiterlimit","stroke-miterlimit":"strokeMiterlimit",strokewidth:"strokeWidth","stroke-width":"strokeWidth",strokeopacity:"strokeOpacity","stroke-opacity":"strokeOpacity",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textanchor:"textAnchor","text-anchor":"textAnchor",textdecoration:"textDecoration","text-decoration":"textDecoration",textlength:"textLength",textrendering:"textRendering","text-rendering":"textRendering",to:"to",transform:"transform",typeof:"typeof",u1:"u1",u2:"u2",underlineposition:"underlinePosition","underline-position":"underlinePosition",underlinethickness:"underlineThickness","underline-thickness":"underlineThickness",unicode:"unicode",unicodebidi:"unicodeBidi","unicode-bidi":"unicodeBidi",unicoderange:"unicodeRange","unicode-range":"unicodeRange",unitsperem:"unitsPerEm","units-per-em":"unitsPerEm",unselectable:"unselectable",valphabetic:"vAlphabetic","v-alphabetic":"vAlphabetic",values:"values",vectoreffect:"vectorEffect","vector-effect":"vectorEffect",version:"version",vertadvy:"vertAdvY","vert-adv-y":"vertAdvY",vertoriginx:"vertOriginX","vert-origin-x":"vertOriginX",vertoriginy:"vertOriginY","vert-origin-y":"vertOriginY",vhanging:"vHanging","v-hanging":"vHanging",videographic:"vIdeographic","v-ideographic":"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",visibility:"visibility",vmathematical:"vMathematical","v-mathematical":"vMathematical",vocab:"vocab",widths:"widths",wordspacing:"wordSpacing","word-spacing":"wordSpacing",writingmode:"writingMode","writing-mode":"writingMode",x1:"x1",x2:"x2",x:"x",xchannelselector:"xChannelSelector",xheight:"xHeight","x-height":"xHeight",xlinkactuate:"xlinkActuate","xlink:actuate":"xlinkActuate",xlinkarcrole:"xlinkArcrole","xlink:arcrole":"xlinkArcrole",xlinkhref:"xlinkHref","xlink:href":"xlinkHref",xlinkrole:"xlinkRole","xlink:role":"xlinkRole",xlinkshow:"xlinkShow","xlink:show":"xlinkShow",xlinktitle:"xlinkTitle","xlink:title":"xlinkTitle",xlinktype:"xlinkType","xlink:type":"xlinkType",xmlbase:"xmlBase","xml:base":"xmlBase",xmllang:"xmlLang","xml:lang":"xmlLang",xmlns:"xmlns","xml:space":"xmlSpace",xmlnsxlink:"xmlnsXlink","xmlns:xlink":"xmlnsXlink",xmlspace:"xmlSpace",y1:"y1",y2:"y2",y:"y",ychannelselector:"yChannelSelector",z:"z",zoomandpan:"zoomAndPan"},_h={"aria-current":0,"aria-description":0,"aria-details":0,"aria-disabled":0,"aria-hidden":0,"aria-invalid":0,"aria-keyshortcuts":0,"aria-label":0,"aria-roledescription":0,"aria-autocomplete":0,"aria-checked":0,"aria-expanded":0,"aria-haspopup":0,"aria-level":0,"aria-modal":0,"aria-multiline":0,"aria-multiselectable":0,"aria-orientation":0,"aria-placeholder":0,"aria-pressed":0,"aria-readonly":0,"aria-required":0,"aria-selected":0,"aria-sort":0,"aria-valuemax":0,"aria-valuemin":0,"aria-valuenow":0,"aria-valuetext":0,"aria-atomic":0,"aria-busy":0,"aria-live":0,"aria-relevant":0,"aria-dropeffect":0,"aria-grabbed":0,"aria-activedescendant":0,"aria-colcount":0,"aria-colindex":0,"aria-colspan":0,"aria-controls":0,"aria-describedby":0,"aria-errormessage":0,"aria-flowto":0,"aria-labelledby":0,"aria-owns":0,"aria-posinset":0,"aria-rowcount":0,"aria-rowindex":0,"aria-rowspan":0,"aria-setsize":0},Xi={},yE=new RegExp("^(aria)-["+I+"]*$"),gE=new RegExp("^(aria)[A-Z]["+I+"]*$");function bE(e,t){{if(Jt.call(Xi,t)&&Xi[t])return!0;if(gE.test(t)){var n="aria-"+t.slice(4).toLowerCase(),a=_h.hasOwnProperty(n)?n:null;if(a==null)return d("Invalid ARIA attribute `%s`. ARIA attributes follow the pattern aria-* and must be lowercase.",t),Xi[t]=!0,!0;if(t!==a)return d("Invalid ARIA attribute `%s`. Did you mean `%s`?",t,a),Xi[t]=!0,!0}if(yE.test(t)){var r=t.toLowerCase(),i=_h.hasOwnProperty(r)?r:null;if(i==null)return Xi[t]=!0,!1;if(t!==i)return d("Unknown ARIA attribute `%s`. Did you mean `%s`?",t,i),Xi[t]=!0,!0}}return!0}function SE(e,t){{var n=[];for(var a in t){var r=bE(e,a);r||n.push(a)}var i=n.map(function(l){return"`"+l+"`"}).join(", ");n.length===1?d("Invalid aria prop %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props",i,e):n.length>1&&d("Invalid aria props %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props",i,e)}}function EE(e,t){fi(e,t)||SE(e,t)}var Oh=!1;function CE(e,t){{if(e!=="input"&&e!=="textarea"&&e!=="select")return;t!=null&&t.value===null&&!Oh&&(Oh=!0,e==="select"&&t.multiple?d("`value` prop on `%s` should not be null. Consider using an empty array when `multiple` is set to `true` to clear the component or `undefined` for uncontrolled components.",e):d("`value` prop on `%s` should not be null. Consider using an empty string to clear the component or `undefined` for uncontrolled components.",e))}}var Nh=function(){};{var cn={},Mh=/^on./,RE=/^on[^A-Z]/,TE=new RegExp("^(aria)-["+I+"]*$"),xE=new RegExp("^(aria)[A-Z]["+I+"]*$");Nh=function(e,t,n,a){if(Jt.call(cn,t)&&cn[t])return!0;var r=t.toLowerCase();if(r==="onfocusin"||r==="onfocusout")return d("React uses onFocus and onBlur instead of onFocusIn and onFocusOut. All React events are normalized to bubble, so onFocusIn and onFocusOut are not needed/supported by React."),cn[t]=!0,!0;if(a!=null){var i=a.registrationNameDependencies,l=a.possibleRegistrationNames;if(i.hasOwnProperty(t))return!0;var u=l.hasOwnProperty(r)?l[r]:null;if(u!=null)return d("Invalid event handler property `%s`. Did you mean `%s`?",t,u),cn[t]=!0,!0;if(Mh.test(t))return d("Unknown event handler property `%s`. It will be ignored.",t),cn[t]=!0,!0}else if(Mh.test(t))return RE.test(t)&&d("Invalid event handler property `%s`. React events use the camelCase naming convention, for example `onClick`.",t),cn[t]=!0,!0;if(TE.test(t)||xE.test(t))return!0;if(r==="innerhtml")return d("Directly setting property `innerHTML` is not permitted. For more information, lookup documentation on `dangerouslySetInnerHTML`."),cn[t]=!0,!0;if(r==="aria")return d("The `aria` attribute is reserved for future use in React. Pass individual `aria-` attributes instead."),cn[t]=!0,!0;if(r==="is"&&n!==null&&n!==void 0&&typeof n!="string")return d("Received a `%s` for a string attribute `is`. If this is expected, cast the value to a string.",typeof n),cn[t]=!0,!0;if(typeof n=="number"&&isNaN(n))return d("Received NaN for the `%s` attribute. If this is expected, cast the value to a string.",t),cn[t]=!0,!0;var o=Vn(t),c=o!==null&&o.type===ea;if(Jo.hasOwnProperty(r)){var f=Jo[r];if(f!==t)return d("Invalid DOM property `%s`. Did you mean `%s`?",t,f),cn[t]=!0,!0}else if(!c&&t!==r)return d("React does not recognize the `%s` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `%s` instead. If you accidentally passed it from a parent component, remove it from the DOM element.",t,r),cn[t]=!0,!0;return typeof n=="boolean"&&Tn(t,n,o,!1)?(n?d('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.',n,t,t,n,t):d('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.',n,t,t,n,t,t,t),cn[t]=!0,!0):c?!0:Tn(t,n,o,!1)?(cn[t]=!0,!1):((n==="false"||n==="true")&&o!==null&&o.type===dt&&(d("Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}?",n,t,n==="false"?"The browser will interpret it as a truthy value.":'Although this works, it will not work as expected if you pass the string "false".',t,n),cn[t]=!0),!0)}}var wE=function(e,t,n){{var a=[];for(var r in t){var i=Nh(e,r,t[r],n);i||a.push(r)}var l=a.map(function(u){return"`"+u+"`"}).join(", ");a.length===1?d("Invalid value for prop %s on <%s> tag. Either remove it from the element, or pass a string or number value to keep it in the DOM. For details, see https://reactjs.org/link/attribute-behavior ",l,e):a.length>1&&d("Invalid values for props %s on <%s> tag. Either remove them from the element, or pass a string or number value to keep them in the DOM. For details, see https://reactjs.org/link/attribute-behavior ",l,e)}};function DE(e,t,n){fi(e,t)||wE(e,t,n)}var Ah=1,yf=2,su=4,_E=Ah|yf|su,cu=null;function OE(e){cu!==null&&d("Expected currently replaying event to be null. This error is likely caused by a bug in React. Please file an issue."),cu=e}function NE(){cu===null&&d("Expected currently replaying event to not be null. This error is likely caused by a bug in React. Please file an issue."),cu=null}function ME(e){return e===cu}function gf(e){var t=e.target||e.srcElement||window;return t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===rr?t.parentNode:t}var bf=null,Ki=null,Ji=null;function Lh(e){var t=Br(e);if(t){if(typeof bf!="function")throw new Error("setRestoreImplementation() needs to be called to handle a target for controlled events. This error is likely caused by a bug in React. Please file an issue.");var n=t.stateNode;if(n){var a=js(n);bf(t.stateNode,t.type,a)}}}function AE(e){bf=e}function Uh(e){Ki?Ji?Ji.push(e):Ji=[e]:Ki=e}function LE(){return Ki!==null||Ji!==null}function jh(){if(Ki){var e=Ki,t=Ji;if(Ki=null,Ji=null,Lh(e),t)for(var n=0;n<t.length;n++)Lh(t[n])}}var kh=function(e,t){return e(t)},zh=function(){},Sf=!1;function UE(){var e=LE();e&&(zh(),jh())}function Hh(e,t,n){if(Sf)return e(t,n);Sf=!0;try{return kh(e,t,n)}finally{Sf=!1,UE()}}function jE(e,t,n){kh=e,zh=n}function kE(e){return e==="button"||e==="input"||e==="select"||e==="textarea"}function zE(e,t,n){switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":return!!(n.disabled&&kE(t));default:return!1}}function fu(e,t){var n=e.stateNode;if(n===null)return null;var a=js(n);if(a===null)return null;var r=a[t];if(zE(t,e.type,a))return null;if(r&&typeof r!="function")throw new Error("Expected `"+t+"` listener to be a function, instead got a value of `"+typeof r+"` type.");return r}var Ef=!1;if(mt)try{var du={};Object.defineProperty(du,"passive",{get:function(){Ef=!0}}),window.addEventListener("test",du,du),window.removeEventListener("test",du,du)}catch{Ef=!1}function Fh(e,t,n,a,r,i,l,u,o){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var Vh=Fh;if(typeof window<"u"&&typeof window.dispatchEvent=="function"&&typeof document<"u"&&typeof document.createEvent=="function"){var Cf=document.createElement("react");Vh=function(t,n,a,r,i,l,u,o,c){if(typeof document>"u"||document===null)throw new Error("The `document` global was defined when React was initialized, but is not defined anymore. This can happen in a test environment if a component schedules an update from an asynchronous callback, but the test has already finished running. To solve this, you can either unmount the component at the end of your test (and ensure that any asynchronous operations get canceled in `componentWillUnmount`), or you can change the test itself to be asynchronous.");var f=document.createEvent("Event"),y=!1,h=!0,S=window.event,E=Object.getOwnPropertyDescriptor(window,"event");function T(){Cf.removeEventListener(x,X,!1),typeof window.event<"u"&&window.hasOwnProperty("event")&&(window.event=S)}var F=Array.prototype.slice.call(arguments,3);function X(){y=!0,T(),n.apply(a,F),h=!1}var Q,we=!1,be=!1;function g(b){if(Q=b.error,we=!0,Q===null&&b.colno===0&&b.lineno===0&&(be=!0),b.defaultPrevented&&Q!=null&&typeof Q=="object")try{Q._suppressLogging=!0}catch{}}var x="react-"+(t||"invokeguardedcallback");if(window.addEventListener("error",g),Cf.addEventListener(x,X,!1),f.initEvent(x,!1,!1),Cf.dispatchEvent(f),E&&Object.defineProperty(window,"event",E),y&&h&&(we?be&&(Q=new Error("A cross-origin error was thrown. React doesn't have access to the actual error object in development. See https://reactjs.org/link/crossorigin-error for more information.")):Q=new Error(`An error was thrown inside one of your components, but React doesn't know what it was. This is likely due to browser flakiness. React does its best to preserve the "Pause on exceptions" behavior of the DevTools, which requires some DEV-mode only tricks. It's possible that these don't work in your browser. Try triggering the error in production mode, or switching to a modern browser. If you suspect that this is actually an issue with React, please file an issue.`),this.onError(Q)),window.removeEventListener("error",g),!y)return T(),Fh.apply(this,arguments)}}var HE=Vh,Zi=!1,Zo=null,es=!1,Rf=null,FE={onError:function(e){Zi=!0,Zo=e}};function Tf(e,t,n,a,r,i,l,u,o){Zi=!1,Zo=null,HE.apply(FE,arguments)}function VE(e,t,n,a,r,i,l,u,o){if(Tf.apply(this,arguments),Zi){var c=xf();es||(es=!0,Rf=c)}}function BE(){if(es){var e=Rf;throw es=!1,Rf=null,e}}function YE(){return Zi}function xf(){if(Zi){var e=Zo;return Zi=!1,Zo=null,e}else throw new Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.")}function el(e){return e._reactInternals}function $E(e){return e._reactInternals!==void 0}function PE(e,t){e._reactInternals=t}var Z=0,tl=1,gt=2,_e=4,di=16,vu=32,wf=64,je=128,lr=256,Ur=512,vi=1024,ma=2048,ur=4096,pi=8192,ts=16384,qE=ma|_e|wf|Ur|vi|ts,GE=32767,pu=32768,fn=65536,Df=131072,Bh=1048576,_f=2097152,hi=4194304,Of=8388608,or=16777216,ns=33554432,Nf=_e|vi|0,Mf=gt|_e|di|vu|Ur|ur|pi,hu=_e|wf|Ur|pi,nl=ma|di,sr=hi|Of|_f,QE=J.ReactCurrentOwner;function mi(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{var a=t;do t=a,(t.flags&(gt|ur))!==Z&&(n=t.return),a=t.return;while(a)}return t.tag===W?n:null}function Yh(e){if(e.tag===se){var t=e.memoizedState;if(t===null){var n=e.alternate;n!==null&&(t=n.memoizedState)}if(t!==null)return t.dehydrated}return null}function $h(e){return e.tag===W?e.stateNode.containerInfo:null}function WE(e){return mi(e)===e}function IE(e){{var t=QE.current;if(t!==null&&t.tag===B){var n=t,a=n.stateNode;a._warnedAboutRefsInRender||d("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",de(n)||"A component"),a._warnedAboutRefsInRender=!0}}var r=el(e);return r?mi(r)===r:!1}function Ph(e){if(mi(e)!==e)throw new Error("Unable to find node on an unmounted component.")}function qh(e){var t=e.alternate;if(!t){var n=mi(e);if(n===null)throw new Error("Unable to find node on an unmounted component.");return n!==e?null:e}for(var a=e,r=t;;){var i=a.return;if(i===null)break;var l=i.alternate;if(l===null){var u=i.return;if(u!==null){a=r=u;continue}break}if(i.child===l.child){for(var o=i.child;o;){if(o===a)return Ph(i),e;if(o===r)return Ph(i),t;o=o.sibling}throw new Error("Unable to find node on an unmounted component.")}if(a.return!==r.return)a=i,r=l;else{for(var c=!1,f=i.child;f;){if(f===a){c=!0,a=i,r=l;break}if(f===r){c=!0,r=i,a=l;break}f=f.sibling}if(!c){for(f=l.child;f;){if(f===a){c=!0,a=l,r=i;break}if(f===r){c=!0,r=l,a=i;break}f=f.sibling}if(!c)throw new Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(a.alternate!==r)throw new Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(a.tag!==W)throw new Error("Unable to find node on an unmounted component.");return a.stateNode.current===a?e:t}function Gh(e){var t=qh(e);return t!==null?Qh(t):null}function Qh(e){if(e.tag===G||e.tag===ye)return e;for(var t=e.child;t!==null;){var n=Qh(t);if(n!==null)return n;t=t.sibling}return null}function XE(e){var t=qh(e);return t!==null?Wh(t):null}function Wh(e){if(e.tag===G||e.tag===ye)return e;for(var t=e.child;t!==null;){if(t.tag!==oe){var n=Wh(t);if(n!==null)return n}t=t.sibling}return null}var Ih=N.unstable_scheduleCallback,KE=N.unstable_cancelCallback,JE=N.unstable_shouldYield,ZE=N.unstable_requestPaint,kt=N.unstable_now,eC=N.unstable_getCurrentPriorityLevel,as=N.unstable_ImmediatePriority,Af=N.unstable_UserBlockingPriority,yi=N.unstable_NormalPriority,tC=N.unstable_LowPriority,Lf=N.unstable_IdlePriority,nC=N.unstable_yieldValue,aC=N.unstable_setDisableYieldValue,al=null,nn=null,z=null,za=!1,ya=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u";function rC(e){if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u")return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled)return!0;if(!t.supportsFiber)return d("The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://reactjs.org/link/react-devtools"),!0;try{Ma&&(e=Ce({},e,{getLaneLabelMap:cC,injectProfilingHooks:sC})),al=t.inject(e),nn=t}catch(n){d("React instrumentation encountered an error: %s.",n)}return!!t.checkDCE}function iC(e,t){if(nn&&typeof nn.onScheduleFiberRoot=="function")try{nn.onScheduleFiberRoot(al,e,t)}catch(n){za||(za=!0,d("React instrumentation encountered an error: %s",n))}}function lC(e,t){if(nn&&typeof nn.onCommitFiberRoot=="function")try{var n=(e.current.flags&je)===je;if(fa){var a;switch(t){case Pn:a=as;break;case fr:a=Af;break;case dr:a=yi;break;case cs:a=Lf;break;default:a=yi;break}nn.onCommitFiberRoot(al,e,a,n)}}catch(r){za||(za=!0,d("React instrumentation encountered an error: %s",r))}}function uC(e){if(nn&&typeof nn.onPostCommitFiberRoot=="function")try{nn.onPostCommitFiberRoot(al,e)}catch(t){za||(za=!0,d("React instrumentation encountered an error: %s",t))}}function oC(e){if(nn&&typeof nn.onCommitFiberUnmount=="function")try{nn.onCommitFiberUnmount(al,e)}catch(t){za||(za=!0,d("React instrumentation encountered an error: %s",t))}}function zt(e){if(typeof nC=="function"&&(aC(e),ke(e)),nn&&typeof nn.setStrictMode=="function")try{nn.setStrictMode(al,e)}catch(t){za||(za=!0,d("React instrumentation encountered an error: %s",t))}}function sC(e){z=e}function cC(){{for(var e=new Map,t=1,n=0;n<jf;n++){var a=NC(t);e.set(t,a),t*=2}return e}}function fC(e){z!==null&&typeof z.markCommitStarted=="function"&&z.markCommitStarted(e)}function Xh(){z!==null&&typeof z.markCommitStopped=="function"&&z.markCommitStopped()}function mu(e){z!==null&&typeof z.markComponentRenderStarted=="function"&&z.markComponentRenderStarted(e)}function rl(){z!==null&&typeof z.markComponentRenderStopped=="function"&&z.markComponentRenderStopped()}function dC(e){z!==null&&typeof z.markComponentPassiveEffectMountStarted=="function"&&z.markComponentPassiveEffectMountStarted(e)}function vC(){z!==null&&typeof z.markComponentPassiveEffectMountStopped=="function"&&z.markComponentPassiveEffectMountStopped()}function pC(e){z!==null&&typeof z.markComponentPassiveEffectUnmountStarted=="function"&&z.markComponentPassiveEffectUnmountStarted(e)}function hC(){z!==null&&typeof z.markComponentPassiveEffectUnmountStopped=="function"&&z.markComponentPassiveEffectUnmountStopped()}function mC(e){z!==null&&typeof z.markComponentLayoutEffectMountStarted=="function"&&z.markComponentLayoutEffectMountStarted(e)}function yC(){z!==null&&typeof z.markComponentLayoutEffectMountStopped=="function"&&z.markComponentLayoutEffectMountStopped()}function Kh(e){z!==null&&typeof z.markComponentLayoutEffectUnmountStarted=="function"&&z.markComponentLayoutEffectUnmountStarted(e)}function Jh(){z!==null&&typeof z.markComponentLayoutEffectUnmountStopped=="function"&&z.markComponentLayoutEffectUnmountStopped()}function gC(e,t,n){z!==null&&typeof z.markComponentErrored=="function"&&z.markComponentErrored(e,t,n)}function bC(e,t,n){z!==null&&typeof z.markComponentSuspended=="function"&&z.markComponentSuspended(e,t,n)}function SC(e){z!==null&&typeof z.markLayoutEffectsStarted=="function"&&z.markLayoutEffectsStarted(e)}function EC(){z!==null&&typeof z.markLayoutEffectsStopped=="function"&&z.markLayoutEffectsStopped()}function CC(e){z!==null&&typeof z.markPassiveEffectsStarted=="function"&&z.markPassiveEffectsStarted(e)}function RC(){z!==null&&typeof z.markPassiveEffectsStopped=="function"&&z.markPassiveEffectsStopped()}function Zh(e){z!==null&&typeof z.markRenderStarted=="function"&&z.markRenderStarted(e)}function TC(){z!==null&&typeof z.markRenderYielded=="function"&&z.markRenderYielded()}function em(){z!==null&&typeof z.markRenderStopped=="function"&&z.markRenderStopped()}function xC(e){z!==null&&typeof z.markRenderScheduled=="function"&&z.markRenderScheduled(e)}function wC(e,t){z!==null&&typeof z.markForceUpdateScheduled=="function"&&z.markForceUpdateScheduled(e,t)}function Uf(e,t){z!==null&&typeof z.markStateUpdateScheduled=="function"&&z.markStateUpdateScheduled(e,t)}var ee=0,Te=1,$e=2,bt=8,Ha=16,tm=Math.clz32?Math.clz32:OC,DC=Math.log,_C=Math.LN2;function OC(e){var t=e>>>0;return t===0?32:31-(DC(t)/_C|0)|0}var jf=31,_=0,Ht=0,ie=1,il=2,cr=4,gi=8,Fa=16,yu=32,ll=4194240,gu=64,kf=128,zf=256,Hf=512,Ff=1024,Vf=2048,Bf=4096,Yf=8192,$f=16384,Pf=32768,qf=65536,Gf=131072,Qf=262144,Wf=524288,If=1048576,Xf=2097152,rs=130023424,ul=4194304,Kf=8388608,Jf=16777216,Zf=33554432,ed=67108864,nm=ul,bu=134217728,am=268435455,Su=268435456,bi=536870912,Yn=1073741824;function NC(e){{if(e&ie)return"Sync";if(e&il)return"InputContinuousHydration";if(e&cr)return"InputContinuous";if(e&gi)return"DefaultHydration";if(e&Fa)return"Default";if(e&yu)return"TransitionHydration";if(e&ll)return"Transition";if(e&rs)return"Retry";if(e&bu)return"SelectiveHydration";if(e&Su)return"IdleHydration";if(e&bi)return"Idle";if(e&Yn)return"Offscreen"}}var Ke=-1,is=gu,ls=ul;function Eu(e){switch(Si(e)){case ie:return ie;case il:return il;case cr:return cr;case gi:return gi;case Fa:return Fa;case yu:return yu;case gu:case kf:case zf:case Hf:case Ff:case Vf:case Bf:case Yf:case $f:case Pf:case qf:case Gf:case Qf:case Wf:case If:case Xf:return e&ll;case ul:case Kf:case Jf:case Zf:case ed:return e&rs;case bu:return bu;case Su:return Su;case bi:return bi;case Yn:return Yn;default:return d("Should have found matching lanes. This is a bug in React."),e}}function us(e,t){var n=e.pendingLanes;if(n===_)return _;var a=_,r=e.suspendedLanes,i=e.pingedLanes,l=n&am;if(l!==_){var u=l&~r;if(u!==_)a=Eu(u);else{var o=l&i;o!==_&&(a=Eu(o))}}else{var c=n&~r;c!==_?a=Eu(c):i!==_&&(a=Eu(i))}if(a===_)return _;if(t!==_&&t!==a&&(t&r)===_){var f=Si(a),y=Si(t);if(f>=y||f===Fa&&(y&ll)!==_)return t}(a&cr)!==_&&(a|=n&Fa);var h=e.entangledLanes;if(h!==_)for(var S=e.entanglements,E=a&h;E>0;){var T=Ei(E),F=1<<T;a|=S[T],E&=~F}return a}function MC(e,t){for(var n=e.eventTimes,a=Ke;t>0;){var r=Ei(t),i=1<<r,l=n[r];l>a&&(a=l),t&=~i}return a}function AC(e,t){switch(e){case ie:case il:case cr:return t+250;case gi:case Fa:case yu:case gu:case kf:case zf:case Hf:case Ff:case Vf:case Bf:case Yf:case $f:case Pf:case qf:case Gf:case Qf:case Wf:case If:case Xf:return t+5e3;case ul:case Kf:case Jf:case Zf:case ed:return Ke;case bu:case Su:case bi:case Yn:return Ke;default:return d("Should have found matching lanes. This is a bug in React."),Ke}}function LC(e,t){for(var n=e.pendingLanes,a=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,l=n;l>0;){var u=Ei(l),o=1<<u,c=i[u];c===Ke?((o&a)===_||(o&r)!==_)&&(i[u]=AC(o,t)):c<=t&&(e.expiredLanes|=o),l&=~o}}function UC(e){return Eu(e.pendingLanes)}function td(e){var t=e.pendingLanes&~Yn;return t!==_?t:t&Yn?Yn:_}function jC(e){return(e&ie)!==_}function nd(e){return(e&am)!==_}function rm(e){return(e&rs)===e}function kC(e){var t=ie|cr|Fa;return(e&t)===_}function zC(e){return(e&ll)===e}function os(e,t){var n=il|cr|gi|Fa;return(t&n)!==_}function HC(e,t){return(t&e.expiredLanes)!==_}function im(e){return(e&ll)!==_}function lm(){var e=is;return is<<=1,(is&ll)===_&&(is=gu),e}function FC(){var e=ls;return ls<<=1,(ls&rs)===_&&(ls=ul),e}function Si(e){return e&-e}function Cu(e){return Si(e)}function Ei(e){return 31-tm(e)}function ad(e){return Ei(e)}function $n(e,t){return(e&t)!==_}function ol(e,t){return(e&t)===t}function he(e,t){return e|t}function ss(e,t){return e&~t}function um(e,t){return e&t}function o0(e){return e}function VC(e,t){return e!==Ht&&e<t?e:t}function rd(e){for(var t=[],n=0;n<jf;n++)t.push(e);return t}function Ru(e,t,n){e.pendingLanes|=t,t!==bi&&(e.suspendedLanes=_,e.pingedLanes=_);var a=e.eventTimes,r=ad(t);a[r]=n}function BC(e,t){e.suspendedLanes|=t,e.pingedLanes&=~t;for(var n=e.expirationTimes,a=t;a>0;){var r=Ei(a),i=1<<r;n[r]=Ke,a&=~i}}function om(e,t,n){e.pingedLanes|=e.suspendedLanes&t}function YC(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=_,e.pingedLanes=_,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t;for(var a=e.entanglements,r=e.eventTimes,i=e.expirationTimes,l=n;l>0;){var u=Ei(l),o=1<<u;a[u]=_,r[u]=Ke,i[u]=Ke,l&=~o}}function id(e,t){for(var n=e.entangledLanes|=t,a=e.entanglements,r=n;r;){var i=Ei(r),l=1<<i;l&t|a[i]&t&&(a[i]|=t),r&=~l}}function $C(e,t){var n=Si(t),a;switch(n){case cr:a=il;break;case Fa:a=gi;break;case gu:case kf:case zf:case Hf:case Ff:case Vf:case Bf:case Yf:case $f:case Pf:case qf:case Gf:case Qf:case Wf:case If:case Xf:case ul:case Kf:case Jf:case Zf:case ed:a=yu;break;case bi:a=Su;break;default:a=Ht;break}return(a&(e.suspendedLanes|t))!==Ht?Ht:a}function sm(e,t,n){if(ya)for(var a=e.pendingUpdatersLaneMap;n>0;){var r=ad(n),i=1<<r,l=a[r];l.add(t),n&=~i}}function cm(e,t){if(ya)for(var n=e.pendingUpdatersLaneMap,a=e.memoizedUpdaters;t>0;){var r=ad(t),i=1<<r,l=n[r];l.size>0&&(l.forEach(function(u){var o=u.alternate;(o===null||!a.has(o))&&a.add(u)}),l.clear()),t&=~i}}function fm(e,t){return null}var Pn=ie,fr=cr,dr=Fa,cs=bi,Tu=Ht;function ga(){return Tu}function Ft(e){Tu=e}function PC(e,t){var n=Tu;try{return Tu=e,t()}finally{Tu=n}}function qC(e,t){return e!==0&&e<t?e:t}function GC(e,t){return e===0||e>t?e:t}function ld(e,t){return e!==0&&e<t}function dm(e){var t=Si(e);return ld(Pn,t)?ld(fr,t)?nd(t)?dr:cs:fr:Pn}function fs(e){var t=e.current.memoizedState;return t.isDehydrated}var vm;function QC(e){vm=e}function WC(e){vm(e)}var ud;function IC(e){ud=e}var pm;function XC(e){pm=e}var hm;function KC(e){hm=e}var mm;function JC(e){mm=e}var od=!1,ds=[],jr=null,kr=null,zr=null,xu=new Map,wu=new Map,Hr=[],ZC=["mousedown","mouseup","touchcancel","touchend","touchstart","auxclick","dblclick","pointercancel","pointerdown","pointerup","dragend","dragstart","drop","compositionend","compositionstart","keydown","keypress","keyup","input","textInput","copy","cut","paste","click","change","contextmenu","reset","submit"];function eR(e){return ZC.indexOf(e)>-1}function tR(e,t,n,a,r){return{blockedOn:e,domEventName:t,eventSystemFlags:n,nativeEvent:r,targetContainers:[a]}}function ym(e,t){switch(e){case"focusin":case"focusout":jr=null;break;case"dragenter":case"dragleave":kr=null;break;case"mouseover":case"mouseout":zr=null;break;case"pointerover":case"pointerout":{var n=t.pointerId;xu.delete(n);break}case"gotpointercapture":case"lostpointercapture":{var a=t.pointerId;wu.delete(a);break}}}function Du(e,t,n,a,r,i){if(e===null||e.nativeEvent!==i){var l=tR(t,n,a,r,i);if(t!==null){var u=Br(t);u!==null&&ud(u)}return l}e.eventSystemFlags|=a;var o=e.targetContainers;return r!==null&&o.indexOf(r)===-1&&o.push(r),e}function nR(e,t,n,a,r){switch(t){case"focusin":{var i=r;return jr=Du(jr,e,t,n,a,i),!0}case"dragenter":{var l=r;return kr=Du(kr,e,t,n,a,l),!0}case"mouseover":{var u=r;return zr=Du(zr,e,t,n,a,u),!0}case"pointerover":{var o=r,c=o.pointerId;return xu.set(c,Du(xu.get(c)||null,e,t,n,a,o)),!0}case"gotpointercapture":{var f=r,y=f.pointerId;return wu.set(y,Du(wu.get(y)||null,e,t,n,a,f)),!0}}return!1}function gm(e){var t=Ti(e.target);if(t!==null){var n=mi(t);if(n!==null){var a=n.tag;if(a===se){var r=Yh(n);if(r!==null){e.blockedOn=r,mm(e.priority,function(){pm(n)});return}}else if(a===W){var i=n.stateNode;if(fs(i)){e.blockedOn=$h(n);return}}}}e.blockedOn=null}function aR(e){for(var t=hm(),n={blockedOn:null,target:e,priority:t},a=0;a<Hr.length&&ld(t,Hr[a].priority);a++);Hr.splice(a,0,n),a===0&&gm(n)}function vs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;t.length>0;){var n=t[0],a=fd(e.domEventName,e.eventSystemFlags,n,e.nativeEvent);if(a===null){var r=e.nativeEvent,i=new r.constructor(r.type,r);OE(i),r.target.dispatchEvent(i),NE()}else{var l=Br(a);return l!==null&&ud(l),e.blockedOn=a,!1}t.shift()}return!0}function bm(e,t,n){vs(e)&&n.delete(t)}function rR(){od=!1,jr!==null&&vs(jr)&&(jr=null),kr!==null&&vs(kr)&&(kr=null),zr!==null&&vs(zr)&&(zr=null),xu.forEach(bm),wu.forEach(bm)}function _u(e,t){e.blockedOn===t&&(e.blockedOn=null,od||(od=!0,N.unstable_scheduleCallback(N.unstable_NormalPriority,rR)))}function Ou(e){if(ds.length>0){_u(ds[0],e);for(var t=1;t<ds.length;t++){var n=ds[t];n.blockedOn===e&&(n.blockedOn=null)}}jr!==null&&_u(jr,e),kr!==null&&_u(kr,e),zr!==null&&_u(zr,e);var a=function(u){return _u(u,e)};xu.forEach(a),wu.forEach(a);for(var r=0;r<Hr.length;r++){var i=Hr[r];i.blockedOn===e&&(i.blockedOn=null)}for(;Hr.length>0;){var l=Hr[0];if(l.blockedOn!==null)break;gm(l),l.blockedOn===null&&Hr.shift()}}var sl=J.ReactCurrentBatchConfig,sd=!0;function Sm(e){sd=!!e}function iR(){return sd}function lR(e,t,n){var a=Em(t),r;switch(a){case Pn:r=uR;break;case fr:r=oR;break;case dr:default:r=cd;break}return r.bind(null,t,n,e)}function uR(e,t,n,a){var r=ga(),i=sl.transition;sl.transition=null;try{Ft(Pn),cd(e,t,n,a)}finally{Ft(r),sl.transition=i}}function oR(e,t,n,a){var r=ga(),i=sl.transition;sl.transition=null;try{Ft(fr),cd(e,t,n,a)}finally{Ft(r),sl.transition=i}}function cd(e,t,n,a){sd&&sR(e,t,n,a)}function sR(e,t,n,a){var r=fd(e,t,n,a);if(r===null){xd(e,t,a,ps,n),ym(e,a);return}if(nR(r,e,t,n,a)){a.stopPropagation();return}if(ym(e,a),t&su&&eR(e)){for(;r!==null;){var i=Br(r);i!==null&&WC(i);var l=fd(e,t,n,a);if(l===null&&xd(e,t,a,ps,n),l===r)break;r=l}r!==null&&a.stopPropagation();return}xd(e,t,a,null,n)}var ps=null;function fd(e,t,n,a){ps=null;var r=gf(a),i=Ti(r);if(i!==null){var l=mi(i);if(l===null)i=null;else{var u=l.tag;if(u===se){var o=Yh(l);if(o!==null)return o;i=null}else if(u===W){var c=l.stateNode;if(fs(c))return $h(l);i=null}else l!==i&&(i=null)}}return ps=i,null}function Em(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return Pn;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return fr;case"message":{var t=eC();switch(t){case as:return Pn;case Af:return fr;case yi:case tC:return dr;case Lf:return cs;default:return dr}}default:return dr}}function cR(e,t,n){return e.addEventListener(t,n,!1),n}function fR(e,t,n){return e.addEventListener(t,n,!0),n}function dR(e,t,n,a){return e.addEventListener(t,n,{capture:!0,passive:a}),n}function vR(e,t,n,a){return e.addEventListener(t,n,{passive:a}),n}var Nu=null,dd=null,Mu=null;function pR(e){return Nu=e,dd=Rm(),!0}function hR(){Nu=null,dd=null,Mu=null}function Cm(){if(Mu)return Mu;var e,t=dd,n=t.length,a,r=Rm(),i=r.length;for(e=0;e<n&&t[e]===r[e];e++);var l=n-e;for(a=1;a<=l&&t[n-a]===r[i-a];a++);var u=a>1?1-a:void 0;return Mu=r.slice(e,u),Mu}function Rm(){return"value"in Nu?Nu.value:Nu.textContent}function hs(e){var t,n=e.keyCode;return"charCode"in e?(t=e.charCode,t===0&&n===13&&(t=13)):t=n,t===10&&(t=13),t>=32||t===13?t:0}function ms(){return!0}function Tm(){return!1}function qn(e){function t(n,a,r,i,l){this._reactName=n,this._targetInst=r,this.type=a,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var u in e)if(e.hasOwnProperty(u)){var o=e[u];o?this[u]=o(i):this[u]=i[u]}var c=i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1;return c?this.isDefaultPrevented=ms:this.isDefaultPrevented=Tm,this.isPropagationStopped=Tm,this}return Ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ms)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ms)},persist:function(){},isPersistent:ms}),t}var cl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vd=qn(cl),Au=Ce({},cl,{view:0,detail:0}),mR=qn(Au),pd,hd,Lu;function yR(e){e!==Lu&&(Lu&&e.type==="mousemove"?(pd=e.screenX-Lu.screenX,hd=e.screenY-Lu.screenY):(pd=0,hd=0),Lu=e)}var ys=Ce({},Au,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:yd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(yR(e),pd)},movementY:function(e){return"movementY"in e?e.movementY:hd}}),xm=qn(ys),gR=Ce({},ys,{dataTransfer:0}),bR=qn(gR),SR=Ce({},Au,{relatedTarget:0}),md=qn(SR),ER=Ce({},cl,{animationName:0,elapsedTime:0,pseudoElement:0}),CR=qn(ER),RR=Ce({},cl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),TR=qn(RR),xR=Ce({},cl,{data:0}),wm=qn(xR),wR=wm,DR={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},_R={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"};function OR(e){if(e.key){var t=DR[e.key]||e.key;if(t!=="Unidentified")return t}if(e.type==="keypress"){var n=hs(e);return n===13?"Enter":String.fromCharCode(n)}return e.type==="keydown"||e.type==="keyup"?_R[e.keyCode]||"Unidentified":""}var NR={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function MR(e){var t=this,n=t.nativeEvent;if(n.getModifierState)return n.getModifierState(e);var a=NR[e];return a?!!n[a]:!1}function yd(e){return MR}var AR=Ce({},Au,{key:OR,code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:yd,charCode:function(e){return e.type==="keypress"?hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),LR=qn(AR),UR=Ce({},ys,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Dm=qn(UR),jR=Ce({},Au,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:yd}),kR=qn(jR),zR=Ce({},cl,{propertyName:0,elapsedTime:0,pseudoElement:0}),HR=qn(zR),FR=Ce({},ys,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),VR=qn(FR),BR=[9,13,27,32],_m=229,gd=mt&&"CompositionEvent"in window,Uu=null;mt&&"documentMode"in document&&(Uu=document.documentMode);var YR=mt&&"TextEvent"in window&&!Uu,Om=mt&&(!gd||Uu&&Uu>8&&Uu<=11),Nm=32,Mm=String.fromCharCode(Nm);function $R(){kn("onBeforeInput",["compositionend","keypress","textInput","paste"]),kn("onCompositionEnd",["compositionend","focusout","keydown","keypress","keyup","mousedown"]),kn("onCompositionStart",["compositionstart","focusout","keydown","keypress","keyup","mousedown"]),kn("onCompositionUpdate",["compositionupdate","focusout","keydown","keypress","keyup","mousedown"])}var Am=!1;function PR(e){return(e.ctrlKey||e.altKey||e.metaKey)&&!(e.ctrlKey&&e.altKey)}function qR(e){switch(e){case"compositionstart":return"onCompositionStart";case"compositionend":return"onCompositionEnd";case"compositionupdate":return"onCompositionUpdate"}}function GR(e,t){return e==="keydown"&&t.keyCode===_m}function Lm(e,t){switch(e){case"keyup":return BR.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==_m;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Um(e){var t=e.detail;return typeof t=="object"&&"data"in t?t.data:null}function jm(e){return e.locale==="ko"}var fl=!1;function QR(e,t,n,a,r){var i,l;if(gd?i=qR(t):fl?Lm(t,a)&&(i="onCompositionEnd"):GR(t,a)&&(i="onCompositionStart"),!i)return null;Om&&!jm(a)&&(!fl&&i==="onCompositionStart"?fl=pR(r):i==="onCompositionEnd"&&fl&&(l=Cm()));var u=Cs(n,i);if(u.length>0){var o=new wm(i,t,null,a,r);if(e.push({event:o,listeners:u}),l)o.data=l;else{var c=Um(a);c!==null&&(o.data=c)}}}function WR(e,t){switch(e){case"compositionend":return Um(t);case"keypress":var n=t.which;return n!==Nm?null:(Am=!0,Mm);case"textInput":var a=t.data;return a===Mm&&Am?null:a;default:return null}}function IR(e,t){if(fl){if(e==="compositionend"||!gd&&Lm(e,t)){var n=Cm();return hR(),fl=!1,n}return null}switch(e){case"paste":return null;case"keypress":if(!PR(t)){if(t.char&&t.char.length>1)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Om&&!jm(t)?null:t.data;default:return null}}function XR(e,t,n,a,r){var i;if(YR?i=WR(t,a):i=IR(t,a),!i)return null;var l=Cs(n,"onBeforeInput");if(l.length>0){var u=new wR("onBeforeInput","beforeinput",null,a,r);e.push({event:u,listeners:l}),u.data=i}}function KR(e,t,n,a,r,i,l){QR(e,t,n,a,r),XR(e,t,n,a,r)}var JR={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function km(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!JR[e.type]:t==="textarea"}/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function ZR(e){if(!mt)return!1;var t="on"+e,n=t in document;if(!n){var a=document.createElement("div");a.setAttribute(t,"return;"),n=typeof a[t]=="function"}return n}function eT(){kn("onChange",["change","click","focusin","focusout","input","keydown","keyup","selectionchange"])}function zm(e,t,n,a){Uh(a);var r=Cs(t,"onChange");if(r.length>0){var i=new vd("onChange","change",null,n,a);e.push({event:i,listeners:r})}}var ju=null,ku=null;function tT(e){var t=e.nodeName&&e.nodeName.toLowerCase();return t==="select"||t==="input"&&e.type==="file"}function nT(e){var t=[];zm(t,ku,e,gf(e)),Hh(aT,t)}function aT(e){ty(e,0)}function gs(e){var t=yl(e);if(Gi(t))return e}function rT(e,t){if(e==="change")return t}var Hm=!1;mt&&(Hm=ZR("input")&&(!document.documentMode||document.documentMode>9));function iT(e,t){ju=e,ku=t,ju.attachEvent("onpropertychange",Vm)}function Fm(){ju&&(ju.detachEvent("onpropertychange",Vm),ju=null,ku=null)}function Vm(e){e.propertyName==="value"&&gs(ku)&&nT(e)}function lT(e,t,n){e==="focusin"?(Fm(),iT(t,n)):e==="focusout"&&Fm()}function uT(e,t){if(e==="selectionchange"||e==="keyup"||e==="keydown")return gs(ku)}function oT(e){var t=e.nodeName;return t&&t.toLowerCase()==="input"&&(e.type==="checkbox"||e.type==="radio")}function sT(e,t){if(e==="click")return gs(t)}function cT(e,t){if(e==="input"||e==="change")return gs(t)}function fT(e){var t=e._wrapperState;!t||!t.controlled||e.type!=="number"||re(e,"number",e.value)}function dT(e,t,n,a,r,i,l){var u=n?yl(n):window,o,c;if(tT(u)?o=rT:km(u)?Hm?o=cT:(o=uT,c=lT):oT(u)&&(o=sT),o){var f=o(t,n);if(f){zm(e,f,a,r);return}}c&&c(t,u,n),t==="focusout"&&fT(u)}function vT(){zn("onMouseEnter",["mouseout","mouseover"]),zn("onMouseLeave",["mouseout","mouseover"]),zn("onPointerEnter",["pointerout","pointerover"]),zn("onPointerLeave",["pointerout","pointerover"])}function pT(e,t,n,a,r,i,l){var u=t==="mouseover"||t==="pointerover",o=t==="mouseout"||t==="pointerout";if(u&&!ME(a)){var c=a.relatedTarget||a.fromElement;if(c&&(Ti(c)||Ku(c)))return}if(!(!o&&!u)){var f;if(r.window===r)f=r;else{var y=r.ownerDocument;y?f=y.defaultView||y.parentWindow:f=window}var h,S;if(o){var E=a.relatedTarget||a.toElement;if(h=n,S=E?Ti(E):null,S!==null){var T=mi(S);(S!==T||S.tag!==G&&S.tag!==ye)&&(S=null)}}else h=null,S=n;if(h!==S){var F=xm,X="onMouseLeave",Q="onMouseEnter",we="mouse";(t==="pointerout"||t==="pointerover")&&(F=Dm,X="onPointerLeave",Q="onPointerEnter",we="pointer");var be=h==null?f:yl(h),g=S==null?f:yl(S),x=new F(X,we+"leave",h,a,r);x.target=be,x.relatedTarget=g;var b=null,M=Ti(r);if(M===n){var V=new F(Q,we+"enter",S,a,r);V.target=g,V.relatedTarget=be,b=V}HT(e,x,b,h,S)}}}function hT(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Gn=typeof Object.is=="function"?Object.is:hT;function zu(e,t){if(Gn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(var r=0;r<n.length;r++){var i=n[r];if(!Jt.call(t,i)||!Gn(e[i],t[i]))return!1}return!0}function Bm(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function mT(e){for(;e;){if(e.nextSibling)return e.nextSibling;e=e.parentNode}}function Ym(e,t){for(var n=Bm(e),a=0,r=0;n;){if(n.nodeType===rr){if(r=a+n.textContent.length,a<=t&&r>=t)return{node:n,offset:t-a};a=r}n=Bm(mT(n))}}function yT(e){var t=e.ownerDocument,n=t&&t.defaultView||window,a=n.getSelection&&n.getSelection();if(!a||a.rangeCount===0)return null;var r=a.anchorNode,i=a.anchorOffset,l=a.focusNode,u=a.focusOffset;try{r.nodeType,l.nodeType}catch{return null}return gT(e,r,i,l,u)}function gT(e,t,n,a,r){var i=0,l=-1,u=-1,o=0,c=0,f=e,y=null;e:for(;;){for(var h=null;f===t&&(n===0||f.nodeType===rr)&&(l=i+n),f===a&&(r===0||f.nodeType===rr)&&(u=i+r),f.nodeType===rr&&(i+=f.nodeValue.length),(h=f.firstChild)!==null;)y=f,f=h;for(;;){if(f===e)break e;if(y===t&&++o===n&&(l=i),y===a&&++c===r&&(u=i),(h=f.nextSibling)!==null)break;f=y,y=f.parentNode}f=h}return l===-1||u===-1?null:{start:l,end:u}}function bT(e,t){var n=e.ownerDocument||document,a=n&&n.defaultView||window;if(a.getSelection){var r=a.getSelection(),i=e.textContent.length,l=Math.min(t.start,i),u=t.end===void 0?l:Math.min(t.end,i);if(!r.extend&&l>u){var o=u;u=l,l=o}var c=Ym(e,l),f=Ym(e,u);if(c&&f){if(r.rangeCount===1&&r.anchorNode===c.node&&r.anchorOffset===c.offset&&r.focusNode===f.node&&r.focusOffset===f.offset)return;var y=n.createRange();y.setStart(c.node,c.offset),r.removeAllRanges(),l>u?(r.addRange(y),r.extend(f.node,f.offset)):(y.setEnd(f.node,f.offset),r.addRange(y))}}}function $m(e){return e&&e.nodeType===rr}function Pm(e,t){return!e||!t?!1:e===t?!0:$m(e)?!1:$m(t)?Pm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1}function ST(e){return e&&e.ownerDocument&&Pm(e.ownerDocument.documentElement,e)}function ET(e){try{return typeof e.contentWindow.location.href=="string"}catch{return!1}}function qm(){for(var e=window,t=Ar();t instanceof e.HTMLIFrameElement;){if(ET(t))e=t.contentWindow;else return t;t=Ar(e.document)}return t}function bd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function CT(){var e=qm();return{focusedElem:e,selectionRange:bd(e)?TT(e):null}}function RT(e){var t=qm(),n=e.focusedElem,a=e.selectionRange;if(t!==n&&ST(n)){a!==null&&bd(n)&&xT(n,a);for(var r=[],i=n;i=i.parentNode;)i.nodeType===Dn&&r.push({element:i,left:i.scrollLeft,top:i.scrollTop});typeof n.focus=="function"&&n.focus();for(var l=0;l<r.length;l++){var u=r[l];u.element.scrollLeft=u.left,u.element.scrollTop=u.top}}}function TT(e){var t;return"selectionStart"in e?t={start:e.selectionStart,end:e.selectionEnd}:t=yT(e),t||{start:0,end:0}}function xT(e,t){var n=t.start,a=t.end;a===void 0&&(a=n),"selectionStart"in e?(e.selectionStart=n,e.selectionEnd=Math.min(a,e.value.length)):bT(e,t)}var wT=mt&&"documentMode"in document&&document.documentMode<=11;function DT(){kn("onSelect",["focusout","contextmenu","dragend","focusin","keydown","keyup","mousedown","mouseup","selectionchange"])}var dl=null,Sd=null,Hu=null,Ed=!1;function _T(e){if("selectionStart"in e&&bd(e))return{start:e.selectionStart,end:e.selectionEnd};var t=e.ownerDocument&&e.ownerDocument.defaultView||window,n=t.getSelection();return{anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}}function OT(e){return e.window===e?e.document:e.nodeType===ir?e:e.ownerDocument}function Gm(e,t,n){var a=OT(n);if(!(Ed||dl==null||dl!==Ar(a))){var r=_T(dl);if(!Hu||!zu(Hu,r)){Hu=r;var i=Cs(Sd,"onSelect");if(i.length>0){var l=new vd("onSelect","select",null,t,n);e.push({event:l,listeners:i}),l.target=dl}}}}function NT(e,t,n,a,r,i,l){var u=n?yl(n):window;switch(t){case"focusin":(km(u)||u.contentEditable==="true")&&(dl=u,Sd=n,Hu=null);break;case"focusout":dl=null,Sd=null,Hu=null;break;case"mousedown":Ed=!0;break;case"contextmenu":case"mouseup":case"dragend":Ed=!1,Gm(e,a,r);break;case"selectionchange":if(wT)break;case"keydown":case"keyup":Gm(e,a,r)}}function bs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var vl={animationend:bs("Animation","AnimationEnd"),animationiteration:bs("Animation","AnimationIteration"),animationstart:bs("Animation","AnimationStart"),transitionend:bs("Transition","TransitionEnd")},Cd={},Qm={};mt&&(Qm=document.createElement("div").style,"AnimationEvent"in window||(delete vl.animationend.animation,delete vl.animationiteration.animation,delete vl.animationstart.animation),"TransitionEvent"in window||delete vl.transitionend.transition);function Ss(e){if(Cd[e])return Cd[e];if(!vl[e])return e;var t=vl[e];for(var n in t)if(t.hasOwnProperty(n)&&n in Qm)return Cd[e]=t[n];return e}var Wm=Ss("animationend"),Im=Ss("animationiteration"),Xm=Ss("animationstart"),Km=Ss("transitionend"),Jm=new Map,Zm=["abort","auxClick","cancel","canPlay","canPlayThrough","click","close","contextMenu","copy","cut","drag","dragEnd","dragEnter","dragExit","dragLeave","dragOver","dragStart","drop","durationChange","emptied","encrypted","ended","error","gotPointerCapture","input","invalid","keyDown","keyPress","keyUp","load","loadedData","loadedMetadata","loadStart","lostPointerCapture","mouseDown","mouseMove","mouseOut","mouseOver","mouseUp","paste","pause","play","playing","pointerCancel","pointerDown","pointerMove","pointerOut","pointerOver","pointerUp","progress","rateChange","reset","resize","seeked","seeking","stalled","submit","suspend","timeUpdate","touchCancel","touchEnd","touchStart","volumeChange","scroll","toggle","touchMove","waiting","wheel"];function Fr(e,t){Jm.set(e,t),kn(t,[e])}function MT(){for(var e=0;e<Zm.length;e++){var t=Zm[e],n=t.toLowerCase(),a=t[0].toUpperCase()+t.slice(1);Fr(n,"on"+a)}Fr(Wm,"onAnimationEnd"),Fr(Im,"onAnimationIteration"),Fr(Xm,"onAnimationStart"),Fr("dblclick","onDoubleClick"),Fr("focusin","onFocus"),Fr("focusout","onBlur"),Fr(Km,"onTransitionEnd")}function AT(e,t,n,a,r,i,l){var u=Jm.get(t);if(u!==void 0){var o=vd,c=t;switch(t){case"keypress":if(hs(a)===0)return;case"keydown":case"keyup":o=LR;break;case"focusin":c="focus",o=md;break;case"focusout":c="blur",o=md;break;case"beforeblur":case"afterblur":o=md;break;case"click":if(a.button===2)return;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":o=xm;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":o=bR;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":o=kR;break;case Wm:case Im:case Xm:o=CR;break;case Km:o=HR;break;case"scroll":o=mR;break;case"wheel":o=VR;break;case"copy":case"cut":case"paste":o=TR;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":o=Dm;break}var f=(i&su)!==0;{var y=!f&&t==="scroll",h=kT(n,u,a.type,f,y);if(h.length>0){var S=new o(u,c,null,a,r);e.push({event:S,listeners:h})}}}}MT(),vT(),eT(),DT(),$R();function LT(e,t,n,a,r,i,l){AT(e,t,n,a,r,i);var u=(i&_E)===0;u&&(pT(e,t,n,a,r),dT(e,t,n,a,r),NT(e,t,n,a,r),KR(e,t,n,a,r))}var Fu=["abort","canplay","canplaythrough","durationchange","emptied","encrypted","ended","error","loadeddata","loadedmetadata","loadstart","pause","play","playing","progress","ratechange","resize","seeked","seeking","stalled","suspend","timeupdate","volumechange","waiting"],Rd=new Set(["cancel","close","invalid","load","scroll","toggle"].concat(Fu));function ey(e,t,n){var a=e.type||"unknown-event";e.currentTarget=n,VE(a,t,void 0,e),e.currentTarget=null}function UT(e,t,n){var a;if(n)for(var r=t.length-1;r>=0;r--){var i=t[r],l=i.instance,u=i.currentTarget,o=i.listener;if(l!==a&&e.isPropagationStopped())return;ey(e,o,u),a=l}else for(var c=0;c<t.length;c++){var f=t[c],y=f.instance,h=f.currentTarget,S=f.listener;if(y!==a&&e.isPropagationStopped())return;ey(e,S,h),a=y}}function ty(e,t){for(var n=(t&su)!==0,a=0;a<e.length;a++){var r=e[a],i=r.event,l=r.listeners;UT(i,l,n)}BE()}function jT(e,t,n,a,r){var i=gf(n),l=[];LT(l,e,a,n,i,t),ty(l,t)}function rt(e,t){Rd.has(e)||d('Did not expect a listenToNonDelegatedEvent() call for "%s". This is a bug in React. Please file an issue.',e);var n=!1,a=fw(t),r=FT(e,n);a.has(r)||(ny(t,e,yf,n),a.add(r))}function Td(e,t,n){Rd.has(e)&&!t&&d('Did not expect a listenToNativeEvent() call for "%s" in the bubble phase. This is a bug in React. Please file an issue.',e);var a=0;t&&(a|=su),ny(n,e,a,t)}var Es="_reactListening"+Math.random().toString(36).slice(2);function Vu(e){if(!e[Es]){e[Es]=!0,Kn.forEach(function(n){n!=="selectionchange"&&(Rd.has(n)||Td(n,!1,e),Td(n,!0,e))});var t=e.nodeType===ir?e:e.ownerDocument;t!==null&&(t[Es]||(t[Es]=!0,Td("selectionchange",!1,t)))}}function ny(e,t,n,a,r){var i=lR(e,t,n),l=void 0;Ef&&(t==="touchstart"||t==="touchmove"||t==="wheel")&&(l=!0),e=e,a?l!==void 0?dR(e,t,i,l):fR(e,t,i):l!==void 0?vR(e,t,i,l):cR(e,t,i)}function ay(e,t){return e===t||e.nodeType===yt&&e.parentNode===t}function xd(e,t,n,a,r){var i=a;if(!(t&Ah)&&!(t&yf)){var l=r;if(a!==null){var u=a;e:for(;;){if(u===null)return;var o=u.tag;if(o===W||o===oe){var c=u.stateNode.containerInfo;if(ay(c,l))break;if(o===oe)for(var f=u.return;f!==null;){var y=f.tag;if(y===W||y===oe){var h=f.stateNode.containerInfo;if(ay(h,l))return}f=f.return}for(;c!==null;){var S=Ti(c);if(S===null)return;var E=S.tag;if(E===G||E===ye){u=i=S;continue e}c=c.parentNode}}u=u.return}}}Hh(function(){return jT(e,t,n,i)})}function Bu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function kT(e,t,n,a,r,i){for(var l=t!==null?t+"Capture":null,u=a?l:t,o=[],c=e,f=null;c!==null;){var y=c,h=y.stateNode,S=y.tag;if(S===G&&h!==null&&(f=h,u!==null)){var E=fu(c,u);E!=null&&o.push(Bu(c,E,f))}if(r)break;c=c.return}return o}function Cs(e,t){for(var n=t+"Capture",a=[],r=e;r!==null;){var i=r,l=i.stateNode,u=i.tag;if(u===G&&l!==null){var o=l,c=fu(r,n);c!=null&&a.unshift(Bu(r,c,o));var f=fu(r,t);f!=null&&a.push(Bu(r,f,o))}r=r.return}return a}function pl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==G);return e||null}function zT(e,t){for(var n=e,a=t,r=0,i=n;i;i=pl(i))r++;for(var l=0,u=a;u;u=pl(u))l++;for(;r-l>0;)n=pl(n),r--;for(;l-r>0;)a=pl(a),l--;for(var o=r;o--;){if(n===a||a!==null&&n===a.alternate)return n;n=pl(n),a=pl(a)}return null}function ry(e,t,n,a,r){for(var i=t._reactName,l=[],u=n;u!==null&&u!==a;){var o=u,c=o.alternate,f=o.stateNode,y=o.tag;if(c!==null&&c===a)break;if(y===G&&f!==null){var h=f;if(r){var S=fu(u,i);S!=null&&l.unshift(Bu(u,S,h))}else if(!r){var E=fu(u,i);E!=null&&l.push(Bu(u,E,h))}}u=u.return}l.length!==0&&e.push({event:t,listeners:l})}function HT(e,t,n,a,r){var i=a&&r?zT(a,r):null;a!==null&&ry(e,t,a,i,!1),r!==null&&n!==null&&ry(e,n,r,i,!0)}function FT(e,t){return e+"__"+(t?"capture":"bubble")}var _n=!1,Yu="dangerouslySetInnerHTML",Rs="suppressContentEditableWarning",Vr="suppressHydrationWarning",iy="autoFocus",Ci="children",Ri="style",Ts="__html",wd,xs,$u,ly,ws,uy,oy;wd={dialog:!0,webview:!0},xs=function(e,t){EE(e,t),CE(e,t),DE(e,t,{registrationNameDependencies:Bt,possibleRegistrationNames:Cn})},uy=mt&&!document.documentMode,$u=function(e,t,n){if(!_n){var a=Ds(n),r=Ds(t);r!==a&&(_n=!0,d("Prop `%s` did not match. Server: %s Client: %s",e,JSON.stringify(r),JSON.stringify(a)))}},ly=function(e){if(!_n){_n=!0;var t=[];e.forEach(function(n){t.push(n)}),d("Extra attributes from the server: %s",t)}},ws=function(e,t){t===!1?d("Expected `%s` listener to be a function, instead got `false`.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.",e,e,e):d("Expected `%s` listener to be a function, instead got a value of `%s` type.",e,typeof t)},oy=function(e,t){var n=e.namespaceURI===ar?e.ownerDocument.createElement(e.tagName):e.ownerDocument.createElementNS(e.namespaceURI,e.tagName);return n.innerHTML=t,n.innerHTML};var VT=/\r\n?/g,BT=/\u0000|\uFFFD/g;function Ds(e){Zn(e);var t=typeof e=="string"?e:""+e;return t.replace(VT,`
`).replace(BT,"")}function _s(e,t,n,a){var r=Ds(t),i=Ds(e);if(i!==r&&(a&&(_n||(_n=!0,d('Text content did not match. Server: "%s" Client: "%s"',i,r))),n&&ve))throw new Error("Text content does not match server-rendered HTML.")}function sy(e){return e.nodeType===ir?e:e.ownerDocument}function YT(){}function Os(e){e.onclick=YT}function $T(e,t,n,a,r){for(var i in a)if(a.hasOwnProperty(i)){var l=a[i];if(i===Ri)l&&Object.freeze(l),wh(t,l);else if(i===Yu){var u=l?l[Ts]:void 0;u!=null&&Eh(t,u)}else if(i===Ci)if(typeof l=="string"){var o=e!=="textarea"||l!=="";o&&Ko(t,l)}else typeof l=="number"&&Ko(t,""+l);else i===Rs||i===Vr||i===iy||(Bt.hasOwnProperty(i)?l!=null&&(typeof l!="function"&&ws(i,l),i==="onScroll"&&rt("scroll",t)):l!=null&&Za(t,i,l,r))}}function PT(e,t,n,a){for(var r=0;r<t.length;r+=2){var i=t[r],l=t[r+1];i===Ri?wh(e,l):i===Yu?Eh(e,l):i===Ci?Ko(e,l):Za(e,i,l,a)}}function qT(e,t,n,a){var r,i=sy(n),l,u=a;if(u===ar&&(u=ff(e)),u===ar){if(r=fi(e,t),!r&&e!==e.toLowerCase()&&d("<%s /> is using incorrect casing. Use PascalCase for React components, or lowercase for HTML elements.",e),e==="script"){var o=i.createElement("div");o.innerHTML="<script><\/script>";var c=o.firstChild;l=o.removeChild(c)}else if(typeof t.is=="string")l=i.createElement(e,{is:t.is});else if(l=i.createElement(e),e==="select"){var f=l;t.multiple?f.multiple=!0:t.size&&(f.size=t.size)}}else l=i.createElementNS(u,e);return u===ar&&!r&&Object.prototype.toString.call(l)==="[object HTMLUnknownElement]"&&!Jt.call(wd,e)&&(wd[e]=!0,d("The tag <%s> is unrecognized in this browser. If you meant to render a React component, start its name with an uppercase letter.",e)),l}function GT(e,t){return sy(t).createTextNode(e)}function QT(e,t,n,a){var r=fi(t,n);xs(t,n);var i;switch(t){case"dialog":rt("cancel",e),rt("close",e),i=n;break;case"iframe":case"object":case"embed":rt("load",e),i=n;break;case"video":case"audio":for(var l=0;l<Fu.length;l++)rt(Fu[l],e);i=n;break;case"source":rt("error",e),i=n;break;case"img":case"image":case"link":rt("error",e),rt("load",e),i=n;break;case"details":rt("toggle",e),i=n;break;case"input":v(e,n),i=s(e,n),rt("invalid",e);break;case"option":We(e,n),i=n;break;case"select":uu(e,n),i=lu(e,n),rt("invalid",e);break;case"textarea":gh(e,n),i=sf(e,n),rt("invalid",e);break;default:i=n}switch(mf(t,i),$T(t,e,a,i,r),t){case"input":tr(e),A(e,n,!1);break;case"textarea":tr(e),Sh(e);break;case"option":at(e,n);break;case"select":of(e,n);break;default:typeof i.onClick=="function"&&Os(e);break}}function WT(e,t,n,a,r){xs(t,a);var i=null,l,u;switch(t){case"input":l=s(e,n),u=s(e,a),i=[];break;case"select":l=lu(e,n),u=lu(e,a),i=[];break;case"textarea":l=sf(e,n),u=sf(e,a),i=[];break;default:l=n,u=a,typeof l.onClick!="function"&&typeof u.onClick=="function"&&Os(e);break}mf(t,u);var o,c,f=null;for(o in l)if(!(u.hasOwnProperty(o)||!l.hasOwnProperty(o)||l[o]==null))if(o===Ri){var y=l[o];for(c in y)y.hasOwnProperty(c)&&(f||(f={}),f[c]="")}else o===Yu||o===Ci||o===Rs||o===Vr||o===iy||(Bt.hasOwnProperty(o)?i||(i=[]):(i=i||[]).push(o,null));for(o in u){var h=u[o],S=l!=null?l[o]:void 0;if(!(!u.hasOwnProperty(o)||h===S||h==null&&S==null))if(o===Ri)if(h&&Object.freeze(h),S){for(c in S)S.hasOwnProperty(c)&&(!h||!h.hasOwnProperty(c))&&(f||(f={}),f[c]="");for(c in h)h.hasOwnProperty(c)&&S[c]!==h[c]&&(f||(f={}),f[c]=h[c])}else f||(i||(i=[]),i.push(o,f)),f=h;else if(o===Yu){var E=h?h[Ts]:void 0,T=S?S[Ts]:void 0;E!=null&&T!==E&&(i=i||[]).push(o,E)}else o===Ci?(typeof h=="string"||typeof h=="number")&&(i=i||[]).push(o,""+h):o===Rs||o===Vr||(Bt.hasOwnProperty(o)?(h!=null&&(typeof h!="function"&&ws(o,h),o==="onScroll"&&rt("scroll",e)),!i&&S!==h&&(i=[])):(i=i||[]).push(o,h))}return f&&(vE(f,u[Ri]),(i=i||[]).push(Ri,f)),i}function IT(e,t,n,a,r){n==="input"&&r.type==="radio"&&r.name!=null&&C(e,r);var i=fi(n,a),l=fi(n,r);switch(PT(e,t,i,l),n){case"input":R(e,r);break;case"textarea":bh(e,r);break;case"select":PS(e,r);break}}function XT(e){{var t=e.toLowerCase();return Jo.hasOwnProperty(t)&&Jo[t]||null}}function KT(e,t,n,a,r,i,l){var u,o;switch(u=fi(t,n),xs(t,n),t){case"dialog":rt("cancel",e),rt("close",e);break;case"iframe":case"object":case"embed":rt("load",e);break;case"video":case"audio":for(var c=0;c<Fu.length;c++)rt(Fu[c],e);break;case"source":rt("error",e);break;case"img":case"image":case"link":rt("error",e),rt("load",e);break;case"details":rt("toggle",e);break;case"input":v(e,n),rt("invalid",e);break;case"option":We(e,n);break;case"select":uu(e,n),rt("invalid",e);break;case"textarea":gh(e,n),rt("invalid",e);break}mf(t,n);{o=new Set;for(var f=e.attributes,y=0;y<f.length;y++){var h=f[y].name.toLowerCase();switch(h){case"value":break;case"checked":break;case"selected":break;default:o.add(f[y].name)}}}var S=null;for(var E in n)if(n.hasOwnProperty(E)){var T=n[E];if(E===Ci)typeof T=="string"?e.textContent!==T&&(n[Vr]!==!0&&_s(e.textContent,T,i,l),S=[Ci,T]):typeof T=="number"&&e.textContent!==""+T&&(n[Vr]!==!0&&_s(e.textContent,T,i,l),S=[Ci,""+T]);else if(Bt.hasOwnProperty(E))T!=null&&(typeof T!="function"&&ws(E,T),E==="onScroll"&&rt("scroll",e));else if(l&&typeof u=="boolean"){var F=void 0,X=u&&En?null:Vn(E);if(n[Vr]!==!0){if(!(E===Rs||E===Vr||E==="value"||E==="checked"||E==="selected")){if(E===Yu){var Q=e.innerHTML,we=T?T[Ts]:void 0;if(we!=null){var be=oy(e,we);be!==Q&&$u(E,Q,be)}}else if(E===Ri){if(o.delete(E),uy){var g=fE(T);F=e.getAttribute("style"),g!==F&&$u(E,F,g)}}else if(u&&!En)o.delete(E.toLowerCase()),F=xr(e,E,T),T!==F&&$u(E,F,T);else if(!vt(E,X,u)&&!Ge(E,T,X,u)){var x=!1;if(X!==null)o.delete(X.attributeName),F=Ja(e,E,T,X);else{var b=a;if(b===ar&&(b=ff(t)),b===ar)o.delete(E.toLowerCase());else{var M=XT(E);M!==null&&M!==E&&(x=!0,o.delete(M)),o.delete(E)}F=xr(e,E,T)}var V=En;!V&&T!==F&&!x&&$u(E,F,T)}}}}}switch(l&&o.size>0&&n[Vr]!==!0&&ly(o),t){case"input":tr(e),A(e,n,!0);break;case"textarea":tr(e),Sh(e);break;case"select":case"option":break;default:typeof n.onClick=="function"&&Os(e);break}return S}function JT(e,t,n){var a=e.nodeValue!==t;return a}function Dd(e,t){{if(_n)return;_n=!0,d("Did not expect server HTML to contain a <%s> in <%s>.",t.nodeName.toLowerCase(),e.nodeName.toLowerCase())}}function _d(e,t){{if(_n)return;_n=!0,d('Did not expect server HTML to contain the text node "%s" in <%s>.',t.nodeValue,e.nodeName.toLowerCase())}}function Od(e,t,n){{if(_n)return;_n=!0,d("Expected server HTML to contain a matching <%s> in <%s>.",t,e.nodeName.toLowerCase())}}function Nd(e,t){{if(t===""||_n)return;_n=!0,d('Expected server HTML to contain a matching text node for "%s" in <%s>.',t,e.nodeName.toLowerCase())}}function ZT(e,t,n){switch(t){case"input":te(e,n);return;case"textarea":GS(e,n);return;case"select":qS(e,n);return}}var Pu=function(){},qu=function(){};{var ex=["address","applet","area","article","aside","base","basefont","bgsound","blockquote","body","br","button","caption","center","col","colgroup","dd","details","dir","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","img","input","isindex","li","link","listing","main","marquee","menu","menuitem","meta","nav","noembed","noframes","noscript","object","ol","p","param","plaintext","pre","script","section","select","source","style","summary","table","tbody","td","template","textarea","tfoot","th","thead","title","tr","track","ul","wbr","xmp"],cy=["applet","caption","html","table","td","th","marquee","object","template","foreignObject","desc","title"],tx=cy.concat(["button"]),nx=["dd","dt","li","option","optgroup","p","rp","rt"],fy={current:null,formTag:null,aTagInScope:null,buttonTagInScope:null,nobrTagInScope:null,pTagInButtonScope:null,listItemTagAutoclosing:null,dlItemTagAutoclosing:null};qu=function(e,t){var n=Ce({},e||fy),a={tag:t};return cy.indexOf(t)!==-1&&(n.aTagInScope=null,n.buttonTagInScope=null,n.nobrTagInScope=null),tx.indexOf(t)!==-1&&(n.pTagInButtonScope=null),ex.indexOf(t)!==-1&&t!=="address"&&t!=="div"&&t!=="p"&&(n.listItemTagAutoclosing=null,n.dlItemTagAutoclosing=null),n.current=a,t==="form"&&(n.formTag=a),t==="a"&&(n.aTagInScope=a),t==="button"&&(n.buttonTagInScope=a),t==="nobr"&&(n.nobrTagInScope=a),t==="p"&&(n.pTagInButtonScope=a),t==="li"&&(n.listItemTagAutoclosing=a),(t==="dd"||t==="dt")&&(n.dlItemTagAutoclosing=a),n};var ax=function(e,t){switch(t){case"select":return e==="option"||e==="optgroup"||e==="#text";case"optgroup":return e==="option"||e==="#text";case"option":return e==="#text";case"tr":return e==="th"||e==="td"||e==="style"||e==="script"||e==="template";case"tbody":case"thead":case"tfoot":return e==="tr"||e==="style"||e==="script"||e==="template";case"colgroup":return e==="col"||e==="template";case"table":return e==="caption"||e==="colgroup"||e==="tbody"||e==="tfoot"||e==="thead"||e==="style"||e==="script"||e==="template";case"head":return e==="base"||e==="basefont"||e==="bgsound"||e==="link"||e==="meta"||e==="title"||e==="noscript"||e==="noframes"||e==="style"||e==="script"||e==="template";case"html":return e==="head"||e==="body"||e==="frameset";case"frameset":return e==="frame";case"#document":return e==="html"}switch(e){case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t!=="h1"&&t!=="h2"&&t!=="h3"&&t!=="h4"&&t!=="h5"&&t!=="h6";case"rp":case"rt":return nx.indexOf(t)===-1;case"body":case"caption":case"col":case"colgroup":case"frameset":case"frame":case"head":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return t==null}return!0},rx=function(e,t){switch(e){case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"menu":case"nav":case"ol":case"p":case"section":case"summary":case"ul":case"pre":case"listing":case"table":case"hr":case"xmp":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t.pTagInButtonScope;case"form":return t.formTag||t.pTagInButtonScope;case"li":return t.listItemTagAutoclosing;case"dd":case"dt":return t.dlItemTagAutoclosing;case"button":return t.buttonTagInScope;case"a":return t.aTagInScope;case"nobr":return t.nobrTagInScope}return null},dy={};Pu=function(e,t,n){n=n||fy;var a=n.current,r=a&&a.tag;t!=null&&(e!=null&&d("validateDOMNesting: when childText is passed, childTag should be null"),e="#text");var i=ax(e,r)?null:a,l=i?null:rx(e,n),u=i||l;if(u){var o=u.tag,c=!!i+"|"+e+"|"+o;if(!dy[c]){dy[c]=!0;var f=e,y="";if(e==="#text"?/\S/.test(t)?f="Text nodes":(f="Whitespace text nodes",y=" Make sure you don't have any extra whitespace between tags on each line of your source code."):f="<"+e+">",i){var h="";o==="table"&&e==="tr"&&(h+=" Add a <tbody>, <thead> or <tfoot> to your code to match the DOM tree generated by the browser."),d("validateDOMNesting(...): %s cannot appear as a child of <%s>.%s%s",f,o,y,h)}else d("validateDOMNesting(...): %s cannot appear as a descendant of <%s>.",f,o)}}}}var Ns="suppressHydrationWarning",Ms="$",As="/$",Gu="$?",Qu="$!",ix="style",Md=null,Ad=null;function lx(e){var t,n,a=e.nodeType;switch(a){case ir:case vf:{t=a===ir?"#document":"#fragment";var r=e.documentElement;n=r?r.namespaceURI:df(null,"");break}default:{var i=a===yt?e.parentNode:e,l=i.namespaceURI||null;t=i.tagName,n=df(l,t);break}}{var u=t.toLowerCase(),o=qu(null,u);return{namespace:n,ancestorInfo:o}}}function ux(e,t,n){{var a=e,r=df(a.namespace,t),i=qu(a.ancestorInfo,t);return{namespace:r,ancestorInfo:i}}}function s0(e){return e}function ox(e){Md=iR(),Ad=CT();var t=null;return Sm(!1),t}function sx(e){RT(Ad),Sm(Md),Md=null,Ad=null}function cx(e,t,n,a,r){var i;{var l=a;if(Pu(e,null,l.ancestorInfo),typeof t.children=="string"||typeof t.children=="number"){var u=""+t.children,o=qu(l.ancestorInfo,e);Pu(null,u,o)}i=l.namespace}var c=qT(e,t,n,i);return Xu(r,c),Vd(c,t),c}function fx(e,t){e.appendChild(t)}function dx(e,t,n,a,r){switch(QT(e,t,n,a),t){case"button":case"input":case"select":case"textarea":return!!n.autoFocus;case"img":return!0;default:return!1}}function vx(e,t,n,a,r,i){{var l=i;if(typeof a.children!=typeof n.children&&(typeof a.children=="string"||typeof a.children=="number")){var u=""+a.children,o=qu(l.ancestorInfo,t);Pu(null,u,o)}}return WT(e,t,n,a)}function Ld(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}function px(e,t,n,a){{var r=n;Pu(null,e,r.ancestorInfo)}var i=GT(e,t);return Xu(a,i),i}function hx(){var e=window.event;return e===void 0?dr:Em(e.type)}var Ud=typeof setTimeout=="function"?setTimeout:void 0,mx=typeof clearTimeout=="function"?clearTimeout:void 0,jd=-1,vy=typeof Promise=="function"?Promise:void 0,yx=typeof queueMicrotask=="function"?queueMicrotask:typeof vy<"u"?function(e){return vy.resolve(null).then(e).catch(gx)}:Ud;function gx(e){setTimeout(function(){throw e})}function bx(e,t,n,a){switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&e.focus();return;case"img":{n.src&&(e.src=n.src);return}}}function Sx(e,t,n,a,r,i){IT(e,t,n,a,r),Vd(e,r)}function py(e){Ko(e,"")}function Ex(e,t,n){e.nodeValue=n}function Cx(e,t){e.appendChild(t)}function Rx(e,t){var n;e.nodeType===yt?(n=e.parentNode,n.insertBefore(t,e)):(n=e,n.appendChild(t));var a=e._reactRootContainer;a==null&&n.onclick===null&&Os(n)}function Tx(e,t,n){e.insertBefore(t,n)}function xx(e,t,n){e.nodeType===yt?e.parentNode.insertBefore(t,n):e.insertBefore(t,n)}function wx(e,t){e.removeChild(t)}function Dx(e,t){e.nodeType===yt?e.parentNode.removeChild(t):e.removeChild(t)}function kd(e,t){var n=t,a=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===yt){var i=r.data;if(i===As)if(a===0){e.removeChild(r),Ou(t);return}else a--;else(i===Ms||i===Gu||i===Qu)&&a++}n=r}while(n);Ou(t)}function _x(e,t){e.nodeType===yt?kd(e.parentNode,t):e.nodeType===Dn&&kd(e,t),Ou(e)}function Ox(e){e=e;var t=e.style;typeof t.setProperty=="function"?t.setProperty("display","none","important"):t.display="none"}function Nx(e){e.nodeValue=""}function Mx(e,t){e=e;var n=t[ix],a=n!=null&&n.hasOwnProperty("display")?n.display:null;e.style.display=pf("display",a)}function Ax(e,t){e.nodeValue=t}function Lx(e){e.nodeType===Dn?e.textContent="":e.nodeType===ir&&e.documentElement&&e.removeChild(e.documentElement)}function Ux(e,t,n){return e.nodeType!==Dn||t.toLowerCase()!==e.nodeName.toLowerCase()?null:e}function jx(e,t){return t===""||e.nodeType!==rr?null:e}function kx(e){return e.nodeType!==yt?null:e}function hy(e){return e.data===Gu}function zd(e){return e.data===Qu}function zx(e){var t=e.nextSibling&&e.nextSibling.dataset,n,a,r;return t&&(n=t.dgst,a=t.msg,r=t.stck),{message:a,digest:n,stack:r}}function Hx(e,t){e._reactRetry=t}function Ls(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===Dn||t===rr)break;if(t===yt){var n=e.data;if(n===Ms||n===Qu||n===Gu)break;if(n===As)return null}}return e}function Wu(e){return Ls(e.nextSibling)}function Fx(e){return Ls(e.firstChild)}function Vx(e){return Ls(e.firstChild)}function Bx(e){return Ls(e.nextSibling)}function Yx(e,t,n,a,r,i,l){Xu(i,e),Vd(e,n);var u;{var o=r;u=o.namespace}var c=(i.mode&Te)!==ee;return KT(e,t,n,u,a,c,l)}function $x(e,t,n,a){return Xu(n,e),n.mode&Te,JT(e,t)}function Px(e,t){Xu(t,e)}function qx(e){for(var t=e.nextSibling,n=0;t;){if(t.nodeType===yt){var a=t.data;if(a===As){if(n===0)return Wu(t);n--}else(a===Ms||a===Qu||a===Gu)&&n++}t=t.nextSibling}return null}function my(e){for(var t=e.previousSibling,n=0;t;){if(t.nodeType===yt){var a=t.data;if(a===Ms||a===Qu||a===Gu){if(n===0)return t;n--}else a===As&&n++}t=t.previousSibling}return null}function Gx(e){Ou(e)}function Qx(e){Ou(e)}function Wx(e){return e!=="head"&&e!=="body"}function Ix(e,t,n,a){var r=!0;_s(t.nodeValue,n,a,r)}function Xx(e,t,n,a,r,i){if(t[Ns]!==!0){var l=!0;_s(a.nodeValue,r,i,l)}}function Kx(e,t){t.nodeType===Dn?Dd(e,t):t.nodeType===yt||_d(e,t)}function Jx(e,t){{var n=e.parentNode;n!==null&&(t.nodeType===Dn?Dd(n,t):t.nodeType===yt||_d(n,t))}}function Zx(e,t,n,a,r){(r||t[Ns]!==!0)&&(a.nodeType===Dn?Dd(n,a):a.nodeType===yt||_d(n,a))}function ew(e,t,n){Od(e,t)}function tw(e,t){Nd(e,t)}function nw(e,t,n){{var a=e.parentNode;a!==null&&Od(a,t)}}function aw(e,t){{var n=e.parentNode;n!==null&&Nd(n,t)}}function rw(e,t,n,a,r,i){(i||t[Ns]!==!0)&&Od(n,a)}function iw(e,t,n,a,r){(r||t[Ns]!==!0)&&Nd(n,a)}function lw(e){d("An error occurred during hydration. The server HTML was replaced with client content in <%s>.",e.nodeName.toLowerCase())}function uw(e){Vu(e)}var hl=Math.random().toString(36).slice(2),ml="__reactFiber$"+hl,Hd="__reactProps$"+hl,Iu="__reactContainer$"+hl,Fd="__reactEvents$"+hl,ow="__reactListeners$"+hl,sw="__reactHandles$"+hl;function cw(e){delete e[ml],delete e[Hd],delete e[Fd],delete e[ow],delete e[sw]}function Xu(e,t){t[ml]=e}function Us(e,t){t[Iu]=e}function yy(e){e[Iu]=null}function Ku(e){return!!e[Iu]}function Ti(e){var t=e[ml];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Iu]||n[ml],t){var a=t.alternate;if(t.child!==null||a!==null&&a.child!==null)for(var r=my(e);r!==null;){var i=r[ml];if(i)return i;r=my(r)}return t}e=n,n=e.parentNode}return null}function Br(e){var t=e[ml]||e[Iu];return t&&(t.tag===G||t.tag===ye||t.tag===se||t.tag===W)?t:null}function yl(e){if(e.tag===G||e.tag===ye)return e.stateNode;throw new Error("getNodeFromInstance: Invalid argument.")}function js(e){return e[Hd]||null}function Vd(e,t){e[Hd]=t}function fw(e){var t=e[Fd];return t===void 0&&(t=e[Fd]=new Set),t}var gy={},by=J.ReactDebugCurrentFrame;function ks(e){if(e){var t=e._owner,n=Or(e.type,e._source,t?t.type:null);by.setExtraStackFrame(n)}else by.setExtraStackFrame(null)}function ba(e,t,n,a,r){{var i=Function.call.bind(Jt);for(var l in e)if(i(e,l)){var u=void 0;try{if(typeof e[l]!="function"){var o=Error((a||"React class")+": "+n+" type `"+l+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[l]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw o.name="Invariant Violation",o}u=e[l](t,l,a,n,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(c){u=c}u&&!(u instanceof Error)&&(ks(r),d("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",a||"React class",n,l,typeof u),ks(null)),u instanceof Error&&!(u.message in gy)&&(gy[u.message]=!0,ks(r),d("Failed %s type: %s",n,u.message),ks(null))}}}var Bd=[],zs;zs=[];var vr=-1;function Yr(e){return{current:e}}function an(e,t){if(vr<0){d("Unexpected pop.");return}t!==zs[vr]&&d("Unexpected Fiber popped."),e.current=Bd[vr],Bd[vr]=null,zs[vr]=null,vr--}function rn(e,t,n){vr++,Bd[vr]=e.current,zs[vr]=n,e.current=t}var Yd;Yd={};var Qn={};Object.freeze(Qn);var pr=Yr(Qn),Va=Yr(!1),$d=Qn;function gl(e,t,n){return n&&Ba(t)?$d:pr.current}function Sy(e,t,n){{var a=e.stateNode;a.__reactInternalMemoizedUnmaskedChildContext=t,a.__reactInternalMemoizedMaskedChildContext=n}}function bl(e,t){{var n=e.type,a=n.contextTypes;if(!a)return Qn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={};for(var l in a)i[l]=t[l];{var u=de(e)||"Unknown";ba(a,i,"context",u)}return r&&Sy(e,t,i),i}}function Hs(){return Va.current}function Ba(e){{var t=e.childContextTypes;return t!=null}}function Fs(e){an(Va,e),an(pr,e)}function Pd(e){an(Va,e),an(pr,e)}function Ey(e,t,n){{if(pr.current!==Qn)throw new Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");rn(pr,t,e),rn(Va,n,e)}}function Cy(e,t,n){{var a=e.stateNode,r=t.childContextTypes;if(typeof a.getChildContext!="function"){{var i=de(e)||"Unknown";Yd[i]||(Yd[i]=!0,d("%s.childContextTypes is specified but there is no getChildContext() method on the instance. You can either define getChildContext() on %s or remove childContextTypes from it.",i,i))}return n}var l=a.getChildContext();for(var u in l)if(!(u in r))throw new Error((de(e)||"Unknown")+'.getChildContext(): key "'+u+'" is not defined in childContextTypes.');{var o=de(e)||"Unknown";ba(r,l,"child context",o)}return Ce({},n,l)}}function Vs(e){{var t=e.stateNode,n=t&&t.__reactInternalMemoizedMergedChildContext||Qn;return $d=pr.current,rn(pr,n,e),rn(Va,Va.current,e),!0}}function Ry(e,t,n){{var a=e.stateNode;if(!a)throw new Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");if(n){var r=Cy(e,t,$d);a.__reactInternalMemoizedMergedChildContext=r,an(Va,e),an(pr,e),rn(pr,r,e),rn(Va,n,e)}else an(Va,e),rn(Va,n,e)}}function dw(e){{if(!WE(e)||e.tag!==B)throw new Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var t=e;do{switch(t.tag){case W:return t.stateNode.context;case B:{var n=t.type;if(Ba(n))return t.stateNode.__reactInternalMemoizedMergedChildContext;break}}t=t.return}while(t!==null);throw new Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.")}}var $r=0,Bs=1,hr=null,qd=!1,Gd=!1;function Ty(e){hr===null?hr=[e]:hr.push(e)}function vw(e){qd=!0,Ty(e)}function xy(){qd&&Pr()}function Pr(){if(!Gd&&hr!==null){Gd=!0;var e=0,t=ga();try{var n=!0,a=hr;for(Ft(Pn);e<a.length;e++){var r=a[e];do r=r(n);while(r!==null)}hr=null,qd=!1}catch(i){throw hr!==null&&(hr=hr.slice(e+1)),Ih(as,Pr),i}finally{Ft(t),Gd=!1}}return null}var Sl=[],El=0,Ys=null,$s=0,aa=[],ra=0,xi=null,mr=1,yr="";function pw(e){return Di(),(e.flags&Bh)!==Z}function hw(e){return Di(),$s}function mw(){var e=yr,t=mr,n=t&~yw(t);return n.toString(32)+e}function wi(e,t){Di(),Sl[El++]=$s,Sl[El++]=Ys,Ys=e,$s=t}function wy(e,t,n){Di(),aa[ra++]=mr,aa[ra++]=yr,aa[ra++]=xi,xi=e;var a=mr,r=yr,i=Ps(a)-1,l=a&~(1<<i),u=n+1,o=Ps(t)+i;if(o>30){var c=i-i%5,f=(1<<c)-1,y=(l&f).toString(32),h=l>>c,S=i-c,E=Ps(t)+S,T=u<<S,F=T|h,X=y+r;mr=1<<E|F,yr=X}else{var Q=u<<i,we=Q|l,be=r;mr=1<<o|we,yr=be}}function Qd(e){Di();var t=e.return;if(t!==null){var n=1,a=0;wi(e,n),wy(e,n,a)}}function Ps(e){return 32-tm(e)}function yw(e){return 1<<Ps(e)-1}function Wd(e){for(;e===Ys;)Ys=Sl[--El],Sl[El]=null,$s=Sl[--El],Sl[El]=null;for(;e===xi;)xi=aa[--ra],aa[ra]=null,yr=aa[--ra],aa[ra]=null,mr=aa[--ra],aa[ra]=null}function gw(){return Di(),xi!==null?{id:mr,overflow:yr}:null}function bw(e,t){Di(),aa[ra++]=mr,aa[ra++]=yr,aa[ra++]=xi,mr=t.id,yr=t.overflow,xi=e}function Di(){$t()||d("Expected to be hydrating. This is a bug in React. Please file an issue.")}var Yt=null,ia=null,Sa=!1,_i=!1,qr=null;function Sw(){Sa&&d("We should not be hydrating here. This is a bug in React. Please file a bug.")}function Dy(){_i=!0}function Ew(){return _i}function Cw(e){var t=e.stateNode.containerInfo;return ia=Vx(t),Yt=e,Sa=!0,qr=null,_i=!1,!0}function Rw(e,t,n){return ia=Bx(t),Yt=e,Sa=!0,qr=null,_i=!1,n!==null&&bw(e,n),!0}function _y(e,t){switch(e.tag){case W:{Kx(e.stateNode.containerInfo,t);break}case G:{var n=(e.mode&Te)!==ee;Zx(e.type,e.memoizedProps,e.stateNode,t,n);break}case se:{var a=e.memoizedState;a.dehydrated!==null&&Jx(a.dehydrated,t);break}}}function Oy(e,t){_y(e,t);var n=wO();n.stateNode=t,n.return=e;var a=e.deletions;a===null?(e.deletions=[n],e.flags|=di):a.push(n)}function Id(e,t){{if(_i)return;switch(e.tag){case W:{var n=e.stateNode.containerInfo;switch(t.tag){case G:var a=t.type;t.pendingProps,ew(n,a);break;case ye:var r=t.pendingProps;tw(n,r);break}break}case G:{var i=e.type,l=e.memoizedProps,u=e.stateNode;switch(t.tag){case G:{var o=t.type,c=t.pendingProps,f=(e.mode&Te)!==ee;rw(i,l,u,o,c,f);break}case ye:{var y=t.pendingProps,h=(e.mode&Te)!==ee;iw(i,l,u,y,h);break}}break}case se:{var S=e.memoizedState,E=S.dehydrated;if(E!==null)switch(t.tag){case G:var T=t.type;t.pendingProps,nw(E,T);break;case ye:var F=t.pendingProps;aw(E,F);break}break}default:return}}}function Ny(e,t){t.flags=t.flags&~ur|gt,Id(e,t)}function My(e,t){switch(e.tag){case G:{var n=e.type;e.pendingProps;var a=Ux(t,n);return a!==null?(e.stateNode=a,Yt=e,ia=Fx(a),!0):!1}case ye:{var r=e.pendingProps,i=jx(t,r);return i!==null?(e.stateNode=i,Yt=e,ia=null,!0):!1}case se:{var l=kx(t);if(l!==null){var u={dehydrated:l,treeContext:gw(),retryLane:Yn};e.memoizedState=u;var o=DO(l);return o.return=e,e.child=o,Yt=e,ia=null,!0}return!1}default:return!1}}function Xd(e){return(e.mode&Te)!==ee&&(e.flags&je)===Z}function Kd(e){throw new Error("Hydration failed because the initial UI does not match what was rendered on the server.")}function Jd(e){if(Sa){var t=ia;if(!t){Xd(e)&&(Id(Yt,e),Kd()),Ny(Yt,e),Sa=!1,Yt=e;return}var n=t;if(!My(e,t)){Xd(e)&&(Id(Yt,e),Kd()),t=Wu(n);var a=Yt;if(!t||!My(e,t)){Ny(Yt,e),Sa=!1,Yt=e;return}Oy(a,n)}}}function Tw(e,t,n){var a=e.stateNode,r=!_i,i=Yx(a,e.type,e.memoizedProps,t,n,e,r);return e.updateQueue=i,i!==null}function xw(e){var t=e.stateNode,n=e.memoizedProps,a=$x(t,n,e);if(a){var r=Yt;if(r!==null)switch(r.tag){case W:{var i=r.stateNode.containerInfo,l=(r.mode&Te)!==ee;Ix(i,t,n,l);break}case G:{var u=r.type,o=r.memoizedProps,c=r.stateNode,f=(r.mode&Te)!==ee;Xx(u,o,c,t,n,f);break}}}return a}function ww(e){var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");Px(n,e)}function Dw(e){var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");return qx(n)}function Ay(e){for(var t=e.return;t!==null&&t.tag!==G&&t.tag!==W&&t.tag!==se;)t=t.return;Yt=t}function qs(e){if(e!==Yt)return!1;if(!Sa)return Ay(e),Sa=!0,!1;if(e.tag!==W&&(e.tag!==G||Wx(e.type)&&!Ld(e.type,e.memoizedProps))){var t=ia;if(t)if(Xd(e))Ly(e),Kd();else for(;t;)Oy(e,t),t=Wu(t)}return Ay(e),e.tag===se?ia=Dw(e):ia=Yt?Wu(e.stateNode):null,!0}function _w(){return Sa&&ia!==null}function Ly(e){for(var t=ia;t;)_y(e,t),t=Wu(t)}function Cl(){Yt=null,ia=null,Sa=!1,_i=!1}function Uy(){qr!==null&&(_b(qr),qr=null)}function $t(){return Sa}function Zd(e){qr===null?qr=[e]:qr.push(e)}var Ow=J.ReactCurrentBatchConfig,Nw=null;function Mw(){return Ow.transition}var Ea={recordUnsafeLifecycleWarnings:function(e,t){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(e,t){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}};{var Aw=function(e){for(var t=null,n=e;n!==null;)n.mode&bt&&(t=n),n=n.return;return t},Oi=function(e){var t=[];return e.forEach(function(n){t.push(n)}),t.sort().join(", ")},Ju=[],Zu=[],eo=[],to=[],no=[],ao=[],Ni=new Set;Ea.recordUnsafeLifecycleWarnings=function(e,t){Ni.has(e.type)||(typeof t.componentWillMount=="function"&&t.componentWillMount.__suppressDeprecationWarning!==!0&&Ju.push(e),e.mode&bt&&typeof t.UNSAFE_componentWillMount=="function"&&Zu.push(e),typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps.__suppressDeprecationWarning!==!0&&eo.push(e),e.mode&bt&&typeof t.UNSAFE_componentWillReceiveProps=="function"&&to.push(e),typeof t.componentWillUpdate=="function"&&t.componentWillUpdate.__suppressDeprecationWarning!==!0&&no.push(e),e.mode&bt&&typeof t.UNSAFE_componentWillUpdate=="function"&&ao.push(e))},Ea.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;Ju.length>0&&(Ju.forEach(function(h){e.add(de(h)||"Component"),Ni.add(h.type)}),Ju=[]);var t=new Set;Zu.length>0&&(Zu.forEach(function(h){t.add(de(h)||"Component"),Ni.add(h.type)}),Zu=[]);var n=new Set;eo.length>0&&(eo.forEach(function(h){n.add(de(h)||"Component"),Ni.add(h.type)}),eo=[]);var a=new Set;to.length>0&&(to.forEach(function(h){a.add(de(h)||"Component"),Ni.add(h.type)}),to=[]);var r=new Set;no.length>0&&(no.forEach(function(h){r.add(de(h)||"Component"),Ni.add(h.type)}),no=[]);var i=new Set;if(ao.length>0&&(ao.forEach(function(h){i.add(de(h)||"Component"),Ni.add(h.type)}),ao=[]),t.size>0){var l=Oi(t);d(`Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.

Please update the following components: %s`,l)}if(a.size>0){var u=Oi(a);d(`Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state

Please update the following components: %s`,u)}if(i.size>0){var o=Oi(i);d(`Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.

Please update the following components: %s`,o)}if(e.size>0){var c=Oi(e);fe(`componentWillMount has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.
* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,c)}if(n.size>0){var f=Oi(n);fe(`componentWillReceiveProps has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state
* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,f)}if(r.size>0){var y=Oi(r);fe(`componentWillUpdate has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,y)}};var Gs=new Map,jy=new Set;Ea.recordLegacyContextWarning=function(e,t){var n=Aw(e);if(n===null){d("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.");return}if(!jy.has(e.type)){var a=Gs.get(n);(e.type.contextTypes!=null||e.type.childContextTypes!=null||t!==null&&typeof t.getChildContext=="function")&&(a===void 0&&(a=[],Gs.set(n,a)),a.push(e))}},Ea.flushLegacyContextWarning=function(){Gs.forEach(function(e,t){if(e.length!==0){var n=e[0],a=new Set;e.forEach(function(i){a.add(de(i)||"Component"),jy.add(i.type)});var r=Oi(a);try{lt(n),d(`Legacy context API has been detected within a strict-mode tree.

The old API will be supported in all 16.x releases, but applications using it should migrate to the new version.

Please update the following components: %s

Learn more about this warning here: https://reactjs.org/link/legacy-context`,r)}finally{jt()}}})},Ea.discardPendingWarnings=function(){Ju=[],Zu=[],eo=[],to=[],no=[],ao=[],Gs=new Map}}function Ca(e,t){if(e&&e.defaultProps){var n=Ce({},t),a=e.defaultProps;for(var r in a)n[r]===void 0&&(n[r]=a[r]);return n}return t}var ev=Yr(null),tv;tv={};var Qs=null,Rl=null,nv=null,Ws=!1;function Is(){Qs=null,Rl=null,nv=null,Ws=!1}function ky(){Ws=!0}function zy(){Ws=!1}function Hy(e,t,n){rn(ev,t._currentValue,e),t._currentValue=n,t._currentRenderer!==void 0&&t._currentRenderer!==null&&t._currentRenderer!==tv&&d("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer=tv}function av(e,t){var n=ev.current;an(ev,t),e._currentValue=n}function rv(e,t,n){for(var a=e;a!==null;){var r=a.alternate;if(ol(a.childLanes,t)?r!==null&&!ol(r.childLanes,t)&&(r.childLanes=he(r.childLanes,t)):(a.childLanes=he(a.childLanes,t),r!==null&&(r.childLanes=he(r.childLanes,t))),a===n)break;a=a.return}a!==n&&d("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.")}function Lw(e,t,n){Uw(e,t,n)}function Uw(e,t,n){var a=e.child;for(a!==null&&(a.return=e);a!==null;){var r=void 0,i=a.dependencies;if(i!==null){r=a.child;for(var l=i.firstContext;l!==null;){if(l.context===t){if(a.tag===B){var u=Cu(n),o=gr(Ke,u);o.tag=Ks;var c=a.updateQueue;if(c!==null){var f=c.shared,y=f.pending;y===null?o.next=o:(o.next=y.next,y.next=o),f.pending=o}}a.lanes=he(a.lanes,n);var h=a.alternate;h!==null&&(h.lanes=he(h.lanes,n)),rv(a.return,n,e),i.lanes=he(i.lanes,n);break}l=l.next}}else if(a.tag===He)r=a.type===e.type?null:a.child;else if(a.tag===ht){var S=a.return;if(S===null)throw new Error("We just came from a parent so we must have had a parent. This is a bug in React.");S.lanes=he(S.lanes,n);var E=S.alternate;E!==null&&(E.lanes=he(E.lanes,n)),rv(S,n,e),r=a.sibling}else r=a.child;if(r!==null)r.return=a;else for(r=a;r!==null;){if(r===e){r=null;break}var T=r.sibling;if(T!==null){T.return=r.return,r=T;break}r=r.return}a=r}}function Tl(e,t){Qs=e,Rl=null,nv=null;var n=e.dependencies;if(n!==null){var a=n.firstContext;a!==null&&($n(n.lanes,t)&&go(),n.firstContext=null)}}function St(e){Ws&&d("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");var t=e._currentValue;if(nv!==e){var n={context:e,memoizedValue:t,next:null};if(Rl===null){if(Qs===null)throw new Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");Rl=n,Qs.dependencies={lanes:_,firstContext:n}}else Rl=Rl.next=n}return t}var Mi=null;function iv(e){Mi===null?Mi=[e]:Mi.push(e)}function jw(){if(Mi!==null){for(var e=0;e<Mi.length;e++){var t=Mi[e],n=t.interleaved;if(n!==null){t.interleaved=null;var a=n.next,r=t.pending;if(r!==null){var i=r.next;r.next=a,n.next=i}t.pending=n}}Mi=null}}function Fy(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,iv(t)):(n.next=r.next,r.next=n),t.interleaved=n,Xs(e,a)}function kw(e,t,n,a){var r=t.interleaved;r===null?(n.next=n,iv(t)):(n.next=r.next,r.next=n),t.interleaved=n}function zw(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,iv(t)):(n.next=r.next,r.next=n),t.interleaved=n,Xs(e,a)}function On(e,t){return Xs(e,t)}var Hw=Xs;function Xs(e,t){e.lanes=he(e.lanes,t);var n=e.alternate;n!==null&&(n.lanes=he(n.lanes,t)),n===null&&(e.flags&(gt|ur))!==Z&&Vb(e);for(var a=e,r=e.return;r!==null;)r.childLanes=he(r.childLanes,t),n=r.alternate,n!==null?n.childLanes=he(n.childLanes,t):(r.flags&(gt|ur))!==Z&&Vb(e),a=r,r=r.return;if(a.tag===W){var i=a.stateNode;return i}else return null}var Vy=0,By=1,Ks=2,lv=3,Js=!1,uv,Zs;uv=!1,Zs=null;function ov(e){var t={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:_},effects:null};e.updateQueue=t}function Yy(e,t){var n=t.updateQueue,a=e.updateQueue;if(n===a){var r={baseState:a.baseState,firstBaseUpdate:a.firstBaseUpdate,lastBaseUpdate:a.lastBaseUpdate,shared:a.shared,effects:a.effects};t.updateQueue=r}}function gr(e,t){var n={eventTime:e,lane:t,tag:Vy,payload:null,callback:null,next:null};return n}function Gr(e,t,n){var a=e.updateQueue;if(a===null)return null;var r=a.shared;if(Zs===r&&!uv&&(d("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback."),uv=!0),H_()){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Hw(e,n)}else return zw(e,r,t,n)}function ec(e,t,n){var a=t.updateQueue;if(a!==null){var r=a.shared;if(im(n)){var i=r.lanes;i=um(i,e.pendingLanes);var l=he(i,n);r.lanes=l,id(e,l)}}}function sv(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null){var r=a.updateQueue;if(n===r){var i=null,l=null,u=n.firstBaseUpdate;if(u!==null){var o=u;do{var c={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};l===null?i=l=c:(l.next=c,l=c),o=o.next}while(o!==null);l===null?i=l=t:(l.next=t,l=t)}else i=l=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}}var f=n.lastBaseUpdate;f===null?n.firstBaseUpdate=t:f.next=t,n.lastBaseUpdate=t}function Fw(e,t,n,a,r,i){switch(n.tag){case By:{var l=n.payload;if(typeof l=="function"){ky();var u=l.call(i,a,r);{if(e.mode&bt){zt(!0);try{l.call(i,a,r)}finally{zt(!1)}}zy()}return u}return l}case lv:e.flags=e.flags&~fn|je;case Vy:{var o=n.payload,c;if(typeof o=="function"){ky(),c=o.call(i,a,r);{if(e.mode&bt){zt(!0);try{o.call(i,a,r)}finally{zt(!1)}}zy()}}else c=o;return c==null?a:Ce({},a,c)}case Ks:return Js=!0,a}return a}function tc(e,t,n,a){var r=e.updateQueue;Js=!1,Zs=r.shared;var i=r.firstBaseUpdate,l=r.lastBaseUpdate,u=r.shared.pending;if(u!==null){r.shared.pending=null;var o=u,c=o.next;o.next=null,l===null?i=c:l.next=c,l=o;var f=e.alternate;if(f!==null){var y=f.updateQueue,h=y.lastBaseUpdate;h!==l&&(h===null?y.firstBaseUpdate=c:h.next=c,y.lastBaseUpdate=o)}}if(i!==null){var S=r.baseState,E=_,T=null,F=null,X=null,Q=i;do{var we=Q.lane,be=Q.eventTime;if(ol(a,we)){if(X!==null){var x={eventTime:be,lane:Ht,tag:Q.tag,payload:Q.payload,callback:Q.callback,next:null};X=X.next=x}S=Fw(e,r,Q,S,t,n);var b=Q.callback;if(b!==null&&Q.lane!==Ht){e.flags|=wf;var M=r.effects;M===null?r.effects=[Q]:M.push(Q)}}else{var g={eventTime:be,lane:we,tag:Q.tag,payload:Q.payload,callback:Q.callback,next:null};X===null?(F=X=g,T=S):X=X.next=g,E=he(E,we)}if(Q=Q.next,Q===null){if(u=r.shared.pending,u===null)break;var V=u,k=V.next;V.next=null,Q=k,r.lastBaseUpdate=V,r.shared.pending=null}}while(!0);X===null&&(T=S),r.baseState=T,r.firstBaseUpdate=F,r.lastBaseUpdate=X;var ne=r.shared.interleaved;if(ne!==null){var ue=ne;do E=he(E,ue.lane),ue=ue.next;while(ue!==ne)}else i===null&&(r.shared.lanes=_);No(E),e.lanes=E,e.memoizedState=S}Zs=null}function Vw(e,t){if(typeof e!="function")throw new Error("Invalid argument passed as callback. Expected a function. Instead "+("received: "+e));e.call(t)}function $y(){Js=!1}function nc(){return Js}function Py(e,t,n){var a=t.effects;if(t.effects=null,a!==null)for(var r=0;r<a.length;r++){var i=a[r],l=i.callback;l!==null&&(i.callback=null,Vw(l,n))}}var cv={},qy=new w.Component().refs,fv,dv,vv,pv,hv,Gy,ac,mv,yv,gv;{fv=new Set,dv=new Set,vv=new Set,pv=new Set,mv=new Set,hv=new Set,yv=new Set,gv=new Set;var Qy=new Set;ac=function(e,t){if(!(e===null||typeof e=="function")){var n=t+"_"+e;Qy.has(n)||(Qy.add(n),d("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e))}},Gy=function(e,t){if(t===void 0){var n=Le(e)||"Component";hv.has(n)||(hv.add(n),d("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",n))}},Object.defineProperty(cv,"_processChildContext",{enumerable:!1,value:function(){throw new Error("_processChildContext is not available in React 16+. This likely means you have multiple copies of React and are attempting to nest a React 15 tree inside a React 16 tree using unstable_renderSubtreeIntoContainer, which isn't supported. Try to make sure you have only one copy of React (and ideally, switch to ReactDOM.createPortal).")}}),Object.freeze(cv)}function bv(e,t,n,a){var r=e.memoizedState,i=n(a,r);{if(e.mode&bt){zt(!0);try{i=n(a,r)}finally{zt(!1)}}Gy(t,i)}var l=i==null?r:Ce({},r,i);if(e.memoizedState=l,e.lanes===_){var u=e.updateQueue;u.baseState=l}}var Sv={isMounted:IE,enqueueSetState:function(e,t,n){var a=el(e),r=pn(),i=ei(a),l=gr(r,i);l.payload=t,n!=null&&(ac(n,"setState"),l.callback=n);var u=Gr(a,l,i);u!==null&&(Mt(u,a,i,r),ec(u,a,i)),Uf(a,i)},enqueueReplaceState:function(e,t,n){var a=el(e),r=pn(),i=ei(a),l=gr(r,i);l.tag=By,l.payload=t,n!=null&&(ac(n,"replaceState"),l.callback=n);var u=Gr(a,l,i);u!==null&&(Mt(u,a,i,r),ec(u,a,i)),Uf(a,i)},enqueueForceUpdate:function(e,t){var n=el(e),a=pn(),r=ei(n),i=gr(a,r);i.tag=Ks,t!=null&&(ac(t,"forceUpdate"),i.callback=t);var l=Gr(n,i,r);l!==null&&(Mt(l,n,r,a),ec(l,n,r)),wC(n,r)}};function Wy(e,t,n,a,r,i,l){var u=e.stateNode;if(typeof u.shouldComponentUpdate=="function"){var o=u.shouldComponentUpdate(a,i,l);{if(e.mode&bt){zt(!0);try{o=u.shouldComponentUpdate(a,i,l)}finally{zt(!1)}}o===void 0&&d("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",Le(t)||"Component")}return o}return t.prototype&&t.prototype.isPureReactComponent?!zu(n,a)||!zu(r,i):!0}function Bw(e,t,n){var a=e.stateNode;{var r=Le(t)||"Component",i=a.render;i||(t.prototype&&typeof t.prototype.render=="function"?d("%s(...): No `render` method found on the returned component instance: did you accidentally return an object from the constructor?",r):d("%s(...): No `render` method found on the returned component instance: you may have forgotten to define `render`.",r)),a.getInitialState&&!a.getInitialState.isReactClassApproved&&!a.state&&d("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",r),a.getDefaultProps&&!a.getDefaultProps.isReactClassApproved&&d("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",r),a.propTypes&&d("propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.",r),a.contextType&&d("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",r),a.contextTypes&&d("contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.",r),t.contextType&&t.contextTypes&&!yv.has(t)&&(yv.add(t),d("%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.",r)),typeof a.componentShouldUpdate=="function"&&d("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",r),t.prototype&&t.prototype.isPureReactComponent&&typeof a.shouldComponentUpdate<"u"&&d("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",Le(t)||"A pure component"),typeof a.componentDidUnmount=="function"&&d("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",r),typeof a.componentDidReceiveProps=="function"&&d("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",r),typeof a.componentWillRecieveProps=="function"&&d("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",r),typeof a.UNSAFE_componentWillRecieveProps=="function"&&d("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",r);var l=a.props!==n;a.props!==void 0&&l&&d("%s(...): When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",r,r),a.defaultProps&&d("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",r,r),typeof a.getSnapshotBeforeUpdate=="function"&&typeof a.componentDidUpdate!="function"&&!vv.has(t)&&(vv.add(t),d("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",Le(t))),typeof a.getDerivedStateFromProps=="function"&&d("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof a.getDerivedStateFromError=="function"&&d("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof t.getSnapshotBeforeUpdate=="function"&&d("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",r);var u=a.state;u&&(typeof u!="object"||Ue(u))&&d("%s.state: must be set to an object or null",r),typeof a.getChildContext=="function"&&typeof t.childContextTypes!="object"&&d("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",r)}}function Iy(e,t){t.updater=Sv,e.stateNode=t,PE(t,e),t._reactInternalInstance=cv}function Xy(e,t,n){var a=!1,r=Qn,i=Qn,l=t.contextType;if("contextType"in t){var u=l===null||l!==void 0&&l.$$typeof===U&&l._context===void 0;if(!u&&!gv.has(t)){gv.add(t);var o="";l===void 0?o=" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":typeof l!="object"?o=" However, it is set to a "+typeof l+".":l.$$typeof===O?o=" Did you accidentally pass the Context.Provider instead?":l._context!==void 0?o=" Did you accidentally pass the Context.Consumer instead?":o=" However, it is set to an object with keys {"+Object.keys(l).join(", ")+"}.",d("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",Le(t)||"Component",o)}}if(typeof l=="object"&&l!==null)i=St(l);else{r=gl(e,t,!0);var c=t.contextTypes;a=c!=null,i=a?bl(e,r):Qn}var f=new t(n,i);if(e.mode&bt){zt(!0);try{f=new t(n,i)}finally{zt(!1)}}var y=e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null;Iy(e,f);{if(typeof t.getDerivedStateFromProps=="function"&&y===null){var h=Le(t)||"Component";dv.has(h)||(dv.add(h),d("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",h,f.state===null?"null":"undefined",h))}if(typeof t.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"){var S=null,E=null,T=null;if(typeof f.componentWillMount=="function"&&f.componentWillMount.__suppressDeprecationWarning!==!0?S="componentWillMount":typeof f.UNSAFE_componentWillMount=="function"&&(S="UNSAFE_componentWillMount"),typeof f.componentWillReceiveProps=="function"&&f.componentWillReceiveProps.__suppressDeprecationWarning!==!0?E="componentWillReceiveProps":typeof f.UNSAFE_componentWillReceiveProps=="function"&&(E="UNSAFE_componentWillReceiveProps"),typeof f.componentWillUpdate=="function"&&f.componentWillUpdate.__suppressDeprecationWarning!==!0?T="componentWillUpdate":typeof f.UNSAFE_componentWillUpdate=="function"&&(T="UNSAFE_componentWillUpdate"),S!==null||E!==null||T!==null){var F=Le(t)||"Component",X=typeof t.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";pv.has(F)||(pv.add(F),d(`Unsafe legacy lifecycles will not be called for components using new component APIs.

%s uses %s but also contains the following legacy lifecycles:%s%s%s

The above lifecycles should be removed. Learn more about this warning here:
https://reactjs.org/link/unsafe-component-lifecycles`,F,X,S!==null?`
  `+S:"",E!==null?`
  `+E:"",T!==null?`
  `+T:""))}}}return a&&Sy(e,r,i),f}function Yw(e,t){var n=t.state;typeof t.componentWillMount=="function"&&t.componentWillMount(),typeof t.UNSAFE_componentWillMount=="function"&&t.UNSAFE_componentWillMount(),n!==t.state&&(d("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",de(e)||"Component"),Sv.enqueueReplaceState(t,t.state,null))}function Ky(e,t,n,a){var r=t.state;if(typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==r){{var i=de(e)||"Component";fv.has(i)||(fv.add(i),d("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",i))}Sv.enqueueReplaceState(t,t.state,null)}}function Ev(e,t,n,a){Bw(e,t,n);var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs=qy,ov(e);var i=t.contextType;if(typeof i=="object"&&i!==null)r.context=St(i);else{var l=gl(e,t,!0);r.context=bl(e,l)}{if(r.state===n){var u=Le(t)||"Component";mv.has(u)||(mv.add(u),d("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",u))}e.mode&bt&&Ea.recordLegacyContextWarning(e,r),Ea.recordUnsafeLifecycleWarnings(e,r)}r.state=e.memoizedState;var o=t.getDerivedStateFromProps;if(typeof o=="function"&&(bv(e,t,o,n),r.state=e.memoizedState),typeof t.getDerivedStateFromProps!="function"&&typeof r.getSnapshotBeforeUpdate!="function"&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(Yw(e,r),tc(e,n,r,a),r.state=e.memoizedState),typeof r.componentDidMount=="function"){var c=_e;c|=hi,(e.mode&Ha)!==ee&&(c|=or),e.flags|=c}}function $w(e,t,n,a){var r=e.stateNode,i=e.memoizedProps;r.props=i;var l=r.context,u=t.contextType,o=Qn;if(typeof u=="object"&&u!==null)o=St(u);else{var c=gl(e,t,!0);o=bl(e,c)}var f=t.getDerivedStateFromProps,y=typeof f=="function"||typeof r.getSnapshotBeforeUpdate=="function";!y&&(typeof r.UNSAFE_componentWillReceiveProps=="function"||typeof r.componentWillReceiveProps=="function")&&(i!==n||l!==o)&&Ky(e,r,n,o),$y();var h=e.memoizedState,S=r.state=h;if(tc(e,n,r,a),S=e.memoizedState,i===n&&h===S&&!Hs()&&!nc()){if(typeof r.componentDidMount=="function"){var E=_e;E|=hi,(e.mode&Ha)!==ee&&(E|=or),e.flags|=E}return!1}typeof f=="function"&&(bv(e,t,f,n),S=e.memoizedState);var T=nc()||Wy(e,t,i,n,h,S,o);if(T){if(!y&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"){var F=_e;F|=hi,(e.mode&Ha)!==ee&&(F|=or),e.flags|=F}}else{if(typeof r.componentDidMount=="function"){var X=_e;X|=hi,(e.mode&Ha)!==ee&&(X|=or),e.flags|=X}e.memoizedProps=n,e.memoizedState=S}return r.props=n,r.state=S,r.context=o,T}function Pw(e,t,n,a,r){var i=t.stateNode;Yy(e,t);var l=t.memoizedProps,u=t.type===t.elementType?l:Ca(t.type,l);i.props=u;var o=t.pendingProps,c=i.context,f=n.contextType,y=Qn;if(typeof f=="object"&&f!==null)y=St(f);else{var h=gl(t,n,!0);y=bl(t,h)}var S=n.getDerivedStateFromProps,E=typeof S=="function"||typeof i.getSnapshotBeforeUpdate=="function";!E&&(typeof i.UNSAFE_componentWillReceiveProps=="function"||typeof i.componentWillReceiveProps=="function")&&(l!==o||c!==y)&&Ky(t,i,a,y),$y();var T=t.memoizedState,F=i.state=T;if(tc(t,a,i,r),F=t.memoizedState,l===o&&T===F&&!Hs()&&!nc()&&!Na)return typeof i.componentDidUpdate=="function"&&(l!==e.memoizedProps||T!==e.memoizedState)&&(t.flags|=_e),typeof i.getSnapshotBeforeUpdate=="function"&&(l!==e.memoizedProps||T!==e.memoizedState)&&(t.flags|=vi),!1;typeof S=="function"&&(bv(t,n,S,a),F=t.memoizedState);var X=nc()||Wy(t,n,u,a,T,F,y)||Na;return X?(!E&&(typeof i.UNSAFE_componentWillUpdate=="function"||typeof i.componentWillUpdate=="function")&&(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,F,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,F,y)),typeof i.componentDidUpdate=="function"&&(t.flags|=_e),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=vi)):(typeof i.componentDidUpdate=="function"&&(l!==e.memoizedProps||T!==e.memoizedState)&&(t.flags|=_e),typeof i.getSnapshotBeforeUpdate=="function"&&(l!==e.memoizedProps||T!==e.memoizedState)&&(t.flags|=vi),t.memoizedProps=a,t.memoizedState=F),i.props=a,i.state=F,i.context=y,X}var Cv,Rv,Tv,xv,wv,Jy=function(e,t){};Cv=!1,Rv=!1,Tv={},xv={},wv={},Jy=function(e,t){if(!(e===null||typeof e!="object")&&!(!e._store||e._store.validated||e.key!=null)){if(typeof e._store!="object")throw new Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");e._store.validated=!0;var n=de(t)||"Component";xv[n]||(xv[n]=!0,d('Each child in a list should have a unique "key" prop. See https://reactjs.org/link/warning-keys for more information.'))}};function ro(e,t,n){var a=n.ref;if(a!==null&&typeof a!="function"&&typeof a!="object"){if((e.mode&bt||jn)&&!(n._owner&&n._self&&n._owner.stateNode!==n._self)){var r=de(e)||"Component";Tv[r]||(d('A string ref, "%s", has been found within a strict mode tree. String refs are a source of potential bugs and should be avoided. We recommend using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',a),Tv[r]=!0)}if(n._owner){var i=n._owner,l;if(i){var u=i;if(u.tag!==B)throw new Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref");l=u.stateNode}if(!l)throw new Error("Missing owner for string ref "+a+". This error is likely caused by a bug in React. Please file an issue.");var o=l;Rn(a,"ref");var c=""+a;if(t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c)return t.ref;var f=function(y){var h=o.refs;h===qy&&(h=o.refs={}),y===null?delete h[c]:h[c]=y};return f._stringRef=c,f}else{if(typeof a!="string")throw new Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!n._owner)throw new Error("Element ref was specified as a string ("+a+`) but no owner was set. This could happen for one of the following reasons:
1. You may be adding a ref to a function component
2. You may be adding a ref to a component that was not created inside a component's render method
3. You have multiple copies of React loaded
See https://reactjs.org/link/refs-must-have-owner for more information.`)}}return a}function rc(e,t){var n=Object.prototype.toString.call(t);throw new Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}function ic(e){{var t=de(e)||"Component";if(wv[t])return;wv[t]=!0,d("Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.")}}function Zy(e){var t=e._payload,n=e._init;return n(t)}function eg(e){function t(g,x){if(e){var b=g.deletions;b===null?(g.deletions=[x],g.flags|=di):b.push(x)}}function n(g,x){if(!e)return null;for(var b=x;b!==null;)t(g,b),b=b.sibling;return null}function a(g,x){for(var b=new Map,M=x;M!==null;)M.key!==null?b.set(M.key,M):b.set(M.index,M),M=M.sibling;return b}function r(g,x){var b=Fi(g,x);return b.index=0,b.sibling=null,b}function i(g,x,b){if(g.index=b,!e)return g.flags|=Bh,x;var M=g.alternate;if(M!==null){var V=M.index;return V<x?(g.flags|=gt,x):V}else return g.flags|=gt,x}function l(g){return e&&g.alternate===null&&(g.flags|=gt),g}function u(g,x,b,M){if(x===null||x.tag!==ye){var V=Zp(b,g.mode,M);return V.return=g,V}else{var k=r(x,b);return k.return=g,k}}function o(g,x,b,M){var V=b.type;if(V===Ua)return f(g,x,b.props.children,M,b.key);if(x!==null&&(x.elementType===V||Pb(x,b)||typeof V=="object"&&V!==null&&V.$$typeof===K&&Zy(V)===x.type)){var k=r(x,b.props);return k.ref=ro(g,x,b),k.return=g,k._debugSource=b._source,k._debugOwner=b._owner,k}var ne=Jp(b,g.mode,M);return ne.ref=ro(g,x,b),ne.return=g,ne}function c(g,x,b,M){if(x===null||x.tag!==oe||x.stateNode.containerInfo!==b.containerInfo||x.stateNode.implementation!==b.implementation){var V=eh(b,g.mode,M);return V.return=g,V}else{var k=r(x,b.children||[]);return k.return=g,k}}function f(g,x,b,M,V){if(x===null||x.tag!==gn){var k=ni(b,g.mode,M,V);return k.return=g,k}else{var ne=r(x,b);return ne.return=g,ne}}function y(g,x,b){if(typeof x=="string"&&x!==""||typeof x=="number"){var M=Zp(""+x,g.mode,b);return M.return=g,M}if(typeof x=="object"&&x!==null){switch(x.$$typeof){case La:{var V=Jp(x,g.mode,b);return V.ref=ro(g,null,x),V.return=g,V}case ha:{var k=eh(x,g.mode,b);return k.return=g,k}case K:{var ne=x._payload,ue=x._init;return y(g,ue(ne),b)}}if(Ue(x)||er(x)){var qe=ni(x,g.mode,b,null);return qe.return=g,qe}rc(g,x)}return typeof x=="function"&&ic(g),null}function h(g,x,b,M){var V=x!==null?x.key:null;if(typeof b=="string"&&b!==""||typeof b=="number")return V!==null?null:u(g,x,""+b,M);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case La:return b.key===V?o(g,x,b,M):null;case ha:return b.key===V?c(g,x,b,M):null;case K:{var k=b._payload,ne=b._init;return h(g,x,ne(k),M)}}if(Ue(b)||er(b))return V!==null?null:f(g,x,b,M,null);rc(g,b)}return typeof b=="function"&&ic(g),null}function S(g,x,b,M,V){if(typeof M=="string"&&M!==""||typeof M=="number"){var k=g.get(b)||null;return u(x,k,""+M,V)}if(typeof M=="object"&&M!==null){switch(M.$$typeof){case La:{var ne=g.get(M.key===null?b:M.key)||null;return o(x,ne,M,V)}case ha:{var ue=g.get(M.key===null?b:M.key)||null;return c(x,ue,M,V)}case K:var qe=M._payload,Ne=M._init;return S(g,x,b,Ne(qe),V)}if(Ue(M)||er(M)){var pt=g.get(b)||null;return f(x,pt,M,V,null)}rc(x,M)}return typeof M=="function"&&ic(x),null}function E(g,x,b){{if(typeof g!="object"||g===null)return x;switch(g.$$typeof){case La:case ha:Jy(g,b);var M=g.key;if(typeof M!="string")break;if(x===null){x=new Set,x.add(M);break}if(!x.has(M)){x.add(M);break}d("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version.",M);break;case K:var V=g._payload,k=g._init;E(k(V),x,b);break}}return x}function T(g,x,b,M){for(var V=null,k=0;k<b.length;k++){var ne=b[k];V=E(ne,V,g)}for(var ue=null,qe=null,Ne=x,pt=0,Me=0,st=null;Ne!==null&&Me<b.length;Me++){Ne.index>Me?(st=Ne,Ne=null):st=Ne.sibling;var un=h(g,Ne,b[Me],M);if(un===null){Ne===null&&(Ne=st);break}e&&Ne&&un.alternate===null&&t(g,Ne),pt=i(un,pt,Me),qe===null?ue=un:qe.sibling=un,qe=un,Ne=st}if(Me===b.length){if(n(g,Ne),$t()){var Xt=Me;wi(g,Xt)}return ue}if(Ne===null){for(;Me<b.length;Me++){var In=y(g,b[Me],M);In!==null&&(pt=i(In,pt,Me),qe===null?ue=In:qe.sibling=In,qe=In)}if($t()){var hn=Me;wi(g,hn)}return ue}for(var mn=a(g,Ne);Me<b.length;Me++){var on=S(mn,g,Me,b[Me],M);on!==null&&(e&&on.alternate!==null&&mn.delete(on.key===null?Me:on.key),pt=i(on,pt,Me),qe===null?ue=on:qe.sibling=on,qe=on)}if(e&&mn.forEach(function(Bl){return t(g,Bl)}),$t()){var Tr=Me;wi(g,Tr)}return ue}function F(g,x,b,M){var V=er(b);if(typeof V!="function")throw new Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");{typeof Symbol=="function"&&b[Symbol.toStringTag]==="Generator"&&(Rv||d("Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. Keep in mind you might need to polyfill these features for older browsers."),Rv=!0),b.entries===V&&(Cv||d("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Cv=!0);var k=V.call(b);if(k)for(var ne=null,ue=k.next();!ue.done;ue=k.next()){var qe=ue.value;ne=E(qe,ne,g)}}var Ne=V.call(b);if(Ne==null)throw new Error("An iterable object provided no iterator.");for(var pt=null,Me=null,st=x,un=0,Xt=0,In=null,hn=Ne.next();st!==null&&!hn.done;Xt++,hn=Ne.next()){st.index>Xt?(In=st,st=null):In=st.sibling;var mn=h(g,st,hn.value,M);if(mn===null){st===null&&(st=In);break}e&&st&&mn.alternate===null&&t(g,st),un=i(mn,un,Xt),Me===null?pt=mn:Me.sibling=mn,Me=mn,st=In}if(hn.done){if(n(g,st),$t()){var on=Xt;wi(g,on)}return pt}if(st===null){for(;!hn.done;Xt++,hn=Ne.next()){var Tr=y(g,hn.value,M);Tr!==null&&(un=i(Tr,un,Xt),Me===null?pt=Tr:Me.sibling=Tr,Me=Tr)}if($t()){var Bl=Xt;wi(g,Bl)}return pt}for(var jo=a(g,st);!hn.done;Xt++,hn=Ne.next()){var Ia=S(jo,g,Xt,hn.value,M);Ia!==null&&(e&&Ia.alternate!==null&&jo.delete(Ia.key===null?Xt:Ia.key),un=i(Ia,un,Xt),Me===null?pt=Ia:Me.sibling=Ia,Me=Ia)}if(e&&jo.forEach(function(r0){return t(g,r0)}),$t()){var a0=Xt;wi(g,a0)}return pt}function X(g,x,b,M){if(x!==null&&x.tag===ye){n(g,x.sibling);var V=r(x,b);return V.return=g,V}n(g,x);var k=Zp(b,g.mode,M);return k.return=g,k}function Q(g,x,b,M){for(var V=b.key,k=x;k!==null;){if(k.key===V){var ne=b.type;if(ne===Ua){if(k.tag===gn){n(g,k.sibling);var ue=r(k,b.props.children);return ue.return=g,ue._debugSource=b._source,ue._debugOwner=b._owner,ue}}else if(k.elementType===ne||Pb(k,b)||typeof ne=="object"&&ne!==null&&ne.$$typeof===K&&Zy(ne)===k.type){n(g,k.sibling);var qe=r(k,b.props);return qe.ref=ro(g,k,b),qe.return=g,qe._debugSource=b._source,qe._debugOwner=b._owner,qe}n(g,k);break}else t(g,k);k=k.sibling}if(b.type===Ua){var Ne=ni(b.props.children,g.mode,M,b.key);return Ne.return=g,Ne}else{var pt=Jp(b,g.mode,M);return pt.ref=ro(g,x,b),pt.return=g,pt}}function we(g,x,b,M){for(var V=b.key,k=x;k!==null;){if(k.key===V)if(k.tag===oe&&k.stateNode.containerInfo===b.containerInfo&&k.stateNode.implementation===b.implementation){n(g,k.sibling);var ne=r(k,b.children||[]);return ne.return=g,ne}else{n(g,k);break}else t(g,k);k=k.sibling}var ue=eh(b,g.mode,M);return ue.return=g,ue}function be(g,x,b,M){var V=typeof b=="object"&&b!==null&&b.type===Ua&&b.key===null;if(V&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case La:return l(Q(g,x,b,M));case ha:return l(we(g,x,b,M));case K:var k=b._payload,ne=b._init;return be(g,x,ne(k),M)}if(Ue(b))return T(g,x,b,M);if(er(b))return F(g,x,b,M);rc(g,b)}return typeof b=="string"&&b!==""||typeof b=="number"?l(X(g,x,""+b,M)):(typeof b=="function"&&ic(g),n(g,x))}return be}var xl=eg(!0),tg=eg(!1);function qw(e,t){if(e!==null&&t.child!==e.child)throw new Error("Resuming work not yet implemented.");if(t.child!==null){var n=t.child,a=Fi(n,n.pendingProps);for(t.child=a,a.return=t;n.sibling!==null;)n=n.sibling,a=a.sibling=Fi(n,n.pendingProps),a.return=t;a.sibling=null}}function Gw(e,t){for(var n=e.child;n!==null;)EO(n,t),n=n.sibling}var io={},Qr=Yr(io),lo=Yr(io),lc=Yr(io);function uc(e){if(e===io)throw new Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");return e}function ng(){var e=uc(lc.current);return e}function Dv(e,t){rn(lc,t,e),rn(lo,e,e),rn(Qr,io,e);var n=lx(t);an(Qr,e),rn(Qr,n,e)}function wl(e){an(Qr,e),an(lo,e),an(lc,e)}function _v(){var e=uc(Qr.current);return e}function ag(e){uc(lc.current);var t=uc(Qr.current),n=ux(t,e.type);t!==n&&(rn(lo,e,e),rn(Qr,n,e))}function Ov(e){lo.current===e&&(an(Qr,e),an(lo,e))}var Qw=0,rg=1,ig=1,uo=2,Ra=Yr(Qw);function Nv(e,t){return(e&t)!==0}function Dl(e){return e&rg}function Mv(e,t){return e&rg|t}function Ww(e,t){return e|t}function Wr(e,t){rn(Ra,t,e)}function _l(e){an(Ra,e)}function Iw(e,t){var n=e.memoizedState;return n!==null?n.dehydrated!==null:(e.memoizedProps,!0)}function oc(e){for(var t=e;t!==null;){if(t.tag===se){var n=t.memoizedState;if(n!==null){var a=n.dehydrated;if(a===null||hy(a)||zd(a))return t}}else if(t.tag===Xe&&t.memoizedProps.revealOrder!==void 0){var r=(t.flags&je)!==Z;if(r)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)return null;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Nn=0,Tt=1,Ya=2,xt=4,Pt=8,Av=[];function Lv(){for(var e=0;e<Av.length;e++){var t=Av[e];t._workInProgressVersionPrimary=null}Av.length=0}function Xw(e,t){var n=t._getVersion,a=n(t._source);e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[t,a]:e.mutableSourceEagerHydrationData.push(t,a)}var H=J.ReactCurrentDispatcher,oo=J.ReactCurrentBatchConfig,Uv,Ol;Uv=new Set;var Ai=_,Pe=null,wt=null,Dt=null,sc=!1,so=!1,co=0,Kw=0,Jw=25,D=null,la=null,Ir=-1,jv=!1;function Fe(){{var e=D;la===null?la=[e]:la.push(e)}}function L(){{var e=D;la!==null&&(Ir++,la[Ir]!==e&&Zw(e))}}function Nl(e){e!=null&&!Ue(e)&&d("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",D,typeof e)}function Zw(e){{var t=de(Pe);if(!Uv.has(t)&&(Uv.add(t),la!==null)){for(var n="",a=30,r=0;r<=Ir;r++){for(var i=la[r],l=r===Ir?e:i,u=r+1+". "+i;u.length<a;)u+=" ";u+=l+`
`,n+=u}d(`React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://reactjs.org/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
`,t,n)}}}function ln(){throw new Error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`)}function kv(e,t){if(jv)return!1;if(t===null)return d("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",D),!1;e.length!==t.length&&d(`The final argument passed to %s changed size between renders. The order and size of this array must remain constant.

Previous: %s
Incoming: %s`,D,"["+t.join(", ")+"]","["+e.join(", ")+"]");for(var n=0;n<t.length&&n<e.length;n++)if(!Gn(e[n],t[n]))return!1;return!0}function Ml(e,t,n,a,r,i){Ai=i,Pe=t,la=e!==null?e._debugHookTypes:null,Ir=-1,jv=e!==null&&e.type!==t.type,t.memoizedState=null,t.updateQueue=null,t.lanes=_,e!==null&&e.memoizedState!==null?H.current=Dg:la!==null?H.current=wg:H.current=xg;var l=n(a,r);if(so){var u=0;do{if(so=!1,co=0,u>=Jw)throw new Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");u+=1,jv=!1,wt=null,Dt=null,t.updateQueue=null,Ir=-1,H.current=_g,l=n(a,r)}while(so)}H.current=Cc,t._debugHookTypes=la;var o=wt!==null&&wt.next!==null;if(Ai=_,Pe=null,wt=null,Dt=null,D=null,la=null,Ir=-1,e!==null&&(e.flags&sr)!==(t.flags&sr)&&(e.mode&Te)!==ee&&d("Internal React error: Expected static flag was missing. Please notify the React team."),sc=!1,o)throw new Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return l}function Al(){var e=co!==0;return co=0,e}function lg(e,t,n){t.updateQueue=e.updateQueue,(t.mode&Ha)!==ee?t.flags&=~(ns|or|ma|_e):t.flags&=~(ma|_e),e.lanes=ss(e.lanes,n)}function ug(){if(H.current=Cc,sc){for(var e=Pe.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}sc=!1}Ai=_,Pe=null,wt=null,Dt=null,la=null,Ir=-1,D=null,Sg=!1,so=!1,co=0}function $a(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Dt===null?Pe.memoizedState=Dt=e:Dt=Dt.next=e,Dt}function ua(){var e;if(wt===null){var t=Pe.alternate;t!==null?e=t.memoizedState:e=null}else e=wt.next;var n;if(Dt===null?n=Pe.memoizedState:n=Dt.next,n!==null)Dt=n,n=Dt.next,wt=e;else{if(e===null)throw new Error("Rendered more hooks than during the previous render.");wt=e;var a={memoizedState:wt.memoizedState,baseState:wt.baseState,baseQueue:wt.baseQueue,queue:wt.queue,next:null};Dt===null?Pe.memoizedState=Dt=a:Dt=Dt.next=a}return Dt}function og(){return{lastEffect:null,stores:null}}function zv(e,t){return typeof t=="function"?t(e):t}function Hv(e,t,n){var a=$a(),r;n!==void 0?r=n(t):r=t,a.memoizedState=a.baseState=r;var i={pending:null,interleaved:null,lanes:_,dispatch:null,lastRenderedReducer:e,lastRenderedState:r};a.queue=i;var l=i.dispatch=aD.bind(null,Pe,i);return[a.memoizedState,l]}function Fv(e,t,n){var a=ua(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=wt,l=i.baseQueue,u=r.pending;if(u!==null){if(l!==null){var o=l.next,c=u.next;l.next=c,u.next=o}i.baseQueue!==l&&d("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."),i.baseQueue=l=u,r.pending=null}if(l!==null){var f=l.next,y=i.baseState,h=null,S=null,E=null,T=f;do{var F=T.lane;if(ol(Ai,F)){if(E!==null){var Q={lane:Ht,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null};E=E.next=Q}if(T.hasEagerState)y=T.eagerState;else{var we=T.action;y=e(y,we)}}else{var X={lane:F,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null};E===null?(S=E=X,h=y):E=E.next=X,Pe.lanes=he(Pe.lanes,F),No(F)}T=T.next}while(T!==null&&T!==f);E===null?h=y:E.next=S,Gn(y,a.memoizedState)||go(),a.memoizedState=y,a.baseState=h,a.baseQueue=E,r.lastRenderedState=y}var be=r.interleaved;if(be!==null){var g=be;do{var x=g.lane;Pe.lanes=he(Pe.lanes,x),No(x),g=g.next}while(g!==be)}else l===null&&(r.lanes=_);var b=r.dispatch;return[a.memoizedState,b]}function Vv(e,t,n){var a=ua(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=r.dispatch,l=r.pending,u=a.memoizedState;if(l!==null){r.pending=null;var o=l.next,c=o;do{var f=c.action;u=e(u,f),c=c.next}while(c!==o);Gn(u,a.memoizedState)||go(),a.memoizedState=u,a.baseQueue===null&&(a.baseState=u),r.lastRenderedState=u}return[u,i]}function c0(e,t,n){}function f0(e,t,n){}function Bv(e,t,n){var a=Pe,r=$a(),i,l=$t();if(l){if(n===void 0)throw new Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");i=n(),Ol||i!==n()&&(d("The result of getServerSnapshot should be cached to avoid an infinite loop"),Ol=!0)}else{if(i=t(),!Ol){var u=t();Gn(i,u)||(d("The result of getSnapshot should be cached to avoid an infinite loop"),Ol=!0)}var o=Vc();if(o===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");os(o,Ai)||sg(a,t,i)}r.memoizedState=i;var c={value:i,getSnapshot:t};return r.queue=c,pc(fg.bind(null,a,c,e),[e]),a.flags|=ma,fo(Tt|Pt,cg.bind(null,a,c,i,t),void 0,null),i}function cc(e,t,n){var a=Pe,r=ua(),i=t();if(!Ol){var l=t();Gn(i,l)||(d("The result of getSnapshot should be cached to avoid an infinite loop"),Ol=!0)}var u=r.memoizedState,o=!Gn(u,i);o&&(r.memoizedState=i,go());var c=r.queue;if(po(fg.bind(null,a,c,e),[e]),c.getSnapshot!==t||o||Dt!==null&&Dt.memoizedState.tag&Tt){a.flags|=ma,fo(Tt|Pt,cg.bind(null,a,c,i,t),void 0,null);var f=Vc();if(f===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");os(f,Ai)||sg(a,t,i)}return i}function sg(e,t,n){e.flags|=ts;var a={getSnapshot:t,value:n},r=Pe.updateQueue;if(r===null)r=og(),Pe.updateQueue=r,r.stores=[a];else{var i=r.stores;i===null?r.stores=[a]:i.push(a)}}function cg(e,t,n,a){t.value=n,t.getSnapshot=a,dg(t)&&vg(e)}function fg(e,t,n){var a=function(){dg(t)&&vg(e)};return n(a)}function dg(e){var t=e.getSnapshot,n=e.value;try{var a=t();return!Gn(n,a)}catch{return!0}}function vg(e){var t=On(e,ie);t!==null&&Mt(t,e,ie,Ke)}function fc(e){var t=$a();typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e;var n={pending:null,interleaved:null,lanes:_,dispatch:null,lastRenderedReducer:zv,lastRenderedState:e};t.queue=n;var a=n.dispatch=rD.bind(null,Pe,n);return[t.memoizedState,a]}function Yv(e){return Fv(zv)}function $v(e){return Vv(zv)}function fo(e,t,n,a){var r={tag:e,create:t,destroy:n,deps:a,next:null},i=Pe.updateQueue;if(i===null)i=og(),Pe.updateQueue=i,i.lastEffect=r.next=r;else{var l=i.lastEffect;if(l===null)i.lastEffect=r.next=r;else{var u=l.next;l.next=r,r.next=u,i.lastEffect=r}}return r}function Pv(e){var t=$a();{var n={current:e};return t.memoizedState=n,n}}function dc(e){var t=ua();return t.memoizedState}function vo(e,t,n,a){var r=$a(),i=a===void 0?null:a;Pe.flags|=e,r.memoizedState=fo(Tt|t,n,void 0,i)}function vc(e,t,n,a){var r=ua(),i=a===void 0?null:a,l=void 0;if(wt!==null){var u=wt.memoizedState;if(l=u.destroy,i!==null){var o=u.deps;if(kv(i,o)){r.memoizedState=fo(t,n,l,i);return}}}Pe.flags|=e,r.memoizedState=fo(Tt|t,n,l,i)}function pc(e,t){return(Pe.mode&Ha)!==ee?vo(ns|ma|Of,Pt,e,t):vo(ma|Of,Pt,e,t)}function po(e,t){return vc(ma,Pt,e,t)}function qv(e,t){return vo(_e,Ya,e,t)}function hc(e,t){return vc(_e,Ya,e,t)}function Gv(e,t){var n=_e;return n|=hi,(Pe.mode&Ha)!==ee&&(n|=or),vo(n,xt,e,t)}function mc(e,t){return vc(_e,xt,e,t)}function pg(e,t){if(typeof t=="function"){var n=t,a=e();return n(a),function(){n(null)}}else if(t!=null){var r=t;r.hasOwnProperty("current")||d("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(r).join(", ")+"}");var i=e();return r.current=i,function(){r.current=null}}}function Qv(e,t,n){typeof t!="function"&&d("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null,r=_e;return r|=hi,(Pe.mode&Ha)!==ee&&(r|=or),vo(r,xt,pg.bind(null,t,e),a)}function yc(e,t,n){typeof t!="function"&&d("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null;return vc(_e,xt,pg.bind(null,t,e),a)}function eD(e,t){}var gc=eD;function Wv(e,t){var n=$a(),a=t===void 0?null:t;return n.memoizedState=[e,a],e}function bc(e,t){var n=ua(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(kv(a,i))return r[0]}return n.memoizedState=[e,a],e}function Iv(e,t){var n=$a(),a=t===void 0?null:t,r=e();return n.memoizedState=[r,a],r}function Sc(e,t){var n=ua(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(kv(a,i))return r[0]}var l=e();return n.memoizedState=[l,a],l}function Xv(e){var t=$a();return t.memoizedState=e,e}function hg(e){var t=ua(),n=wt,a=n.memoizedState;return yg(t,a,e)}function mg(e){var t=ua();if(wt===null)return t.memoizedState=e,e;var n=wt.memoizedState;return yg(t,n,e)}function yg(e,t,n){var a=!kC(Ai);if(a){if(!Gn(n,t)){var r=lm();Pe.lanes=he(Pe.lanes,r),No(r),e.baseState=!0}return t}else return e.baseState&&(e.baseState=!1,go()),e.memoizedState=n,n}function tD(e,t,n){var a=ga();Ft(qC(a,fr)),e(!0);var r=oo.transition;oo.transition={};var i=oo.transition;oo.transition._updatedFibers=new Set;try{e(!1),t()}finally{if(Ft(a),oo.transition=r,r===null&&i._updatedFibers){var l=i._updatedFibers.size;l>10&&fe("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),i._updatedFibers.clear()}}}function Kv(){var e=fc(!1),t=e[0],n=e[1],a=tD.bind(null,n),r=$a();return r.memoizedState=a,[t,a]}function gg(){var e=Yv(),t=e[0],n=ua(),a=n.memoizedState;return[t,a]}function bg(){var e=$v(),t=e[0],n=ua(),a=n.memoizedState;return[t,a]}var Sg=!1;function nD(){return Sg}function Jv(){var e=$a(),t=Vc(),n=t.identifierPrefix,a;if($t()){var r=mw();a=":"+n+"R"+r;var i=co++;i>0&&(a+="H"+i.toString(32)),a+=":"}else{var l=Kw++;a=":"+n+"r"+l.toString(32)+":"}return e.memoizedState=a,a}function Ec(){var e=ua(),t=e.memoizedState;return t}function aD(e,t,n){typeof arguments[3]=="function"&&d("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=ei(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Eg(e))Cg(t,r);else{var i=Fy(e,t,r,a);if(i!==null){var l=pn();Mt(i,e,a,l),Rg(i,t,a)}}Tg(e,a)}function rD(e,t,n){typeof arguments[3]=="function"&&d("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=ei(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Eg(e))Cg(t,r);else{var i=e.alternate;if(e.lanes===_&&(i===null||i.lanes===_)){var l=t.lastRenderedReducer;if(l!==null){var u;u=H.current,H.current=Ta;try{var o=t.lastRenderedState,c=l(o,n);if(r.hasEagerState=!0,r.eagerState=c,Gn(c,o)){kw(e,t,r,a);return}}catch{}finally{H.current=u}}}var f=Fy(e,t,r,a);if(f!==null){var y=pn();Mt(f,e,a,y),Rg(f,t,a)}}Tg(e,a)}function Eg(e){var t=e.alternate;return e===Pe||t!==null&&t===Pe}function Cg(e,t){so=sc=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Rg(e,t,n){if(im(n)){var a=t.lanes;a=um(a,e.pendingLanes);var r=he(a,n);t.lanes=r,id(e,r)}}function Tg(e,t,n){Uf(e,t)}var Cc={readContext:St,useCallback:ln,useContext:ln,useEffect:ln,useImperativeHandle:ln,useInsertionEffect:ln,useLayoutEffect:ln,useMemo:ln,useReducer:ln,useRef:ln,useState:ln,useDebugValue:ln,useDeferredValue:ln,useTransition:ln,useMutableSource:ln,useSyncExternalStore:ln,useId:ln,unstable_isNewReconciler:Vt},xg=null,wg=null,Dg=null,_g=null,Pa=null,Ta=null,Rc=null;{var Zv=function(){d("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")},le=function(){d("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://reactjs.org/link/rules-of-hooks")};xg={readContext:function(e){return St(e)},useCallback:function(e,t){return D="useCallback",Fe(),Nl(t),Wv(e,t)},useContext:function(e){return D="useContext",Fe(),St(e)},useEffect:function(e,t){return D="useEffect",Fe(),Nl(t),pc(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",Fe(),Nl(n),Qv(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",Fe(),Nl(t),qv(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",Fe(),Nl(t),Gv(e,t)},useMemo:function(e,t){D="useMemo",Fe(),Nl(t);var n=H.current;H.current=Pa;try{return Iv(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",Fe();var a=H.current;H.current=Pa;try{return Hv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",Fe(),Pv(e)},useState:function(e){D="useState",Fe();var t=H.current;H.current=Pa;try{return fc(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",Fe(),void 0},useDeferredValue:function(e){return D="useDeferredValue",Fe(),Xv(e)},useTransition:function(){return D="useTransition",Fe(),Kv()},useMutableSource:function(e,t,n){return D="useMutableSource",Fe(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",Fe(),Bv(e,t,n)},useId:function(){return D="useId",Fe(),Jv()},unstable_isNewReconciler:Vt},wg={readContext:function(e){return St(e)},useCallback:function(e,t){return D="useCallback",L(),Wv(e,t)},useContext:function(e){return D="useContext",L(),St(e)},useEffect:function(e,t){return D="useEffect",L(),pc(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",L(),Qv(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",L(),qv(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",L(),Gv(e,t)},useMemo:function(e,t){D="useMemo",L();var n=H.current;H.current=Pa;try{return Iv(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",L();var a=H.current;H.current=Pa;try{return Hv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",L(),Pv(e)},useState:function(e){D="useState",L();var t=H.current;H.current=Pa;try{return fc(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",L(),void 0},useDeferredValue:function(e){return D="useDeferredValue",L(),Xv(e)},useTransition:function(){return D="useTransition",L(),Kv()},useMutableSource:function(e,t,n){return D="useMutableSource",L(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",L(),Bv(e,t,n)},useId:function(){return D="useId",L(),Jv()},unstable_isNewReconciler:Vt},Dg={readContext:function(e){return St(e)},useCallback:function(e,t){return D="useCallback",L(),bc(e,t)},useContext:function(e){return D="useContext",L(),St(e)},useEffect:function(e,t){return D="useEffect",L(),po(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",L(),yc(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",L(),hc(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",L(),mc(e,t)},useMemo:function(e,t){D="useMemo",L();var n=H.current;H.current=Ta;try{return Sc(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",L();var a=H.current;H.current=Ta;try{return Fv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",L(),dc()},useState:function(e){D="useState",L();var t=H.current;H.current=Ta;try{return Yv(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",L(),gc()},useDeferredValue:function(e){return D="useDeferredValue",L(),hg(e)},useTransition:function(){return D="useTransition",L(),gg()},useMutableSource:function(e,t,n){return D="useMutableSource",L(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",L(),cc(e,t)},useId:function(){return D="useId",L(),Ec()},unstable_isNewReconciler:Vt},_g={readContext:function(e){return St(e)},useCallback:function(e,t){return D="useCallback",L(),bc(e,t)},useContext:function(e){return D="useContext",L(),St(e)},useEffect:function(e,t){return D="useEffect",L(),po(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",L(),yc(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",L(),hc(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",L(),mc(e,t)},useMemo:function(e,t){D="useMemo",L();var n=H.current;H.current=Rc;try{return Sc(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",L();var a=H.current;H.current=Rc;try{return Vv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",L(),dc()},useState:function(e){D="useState",L();var t=H.current;H.current=Rc;try{return $v(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",L(),gc()},useDeferredValue:function(e){return D="useDeferredValue",L(),mg(e)},useTransition:function(){return D="useTransition",L(),bg()},useMutableSource:function(e,t,n){return D="useMutableSource",L(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",L(),cc(e,t)},useId:function(){return D="useId",L(),Ec()},unstable_isNewReconciler:Vt},Pa={readContext:function(e){return Zv(),St(e)},useCallback:function(e,t){return D="useCallback",le(),Fe(),Wv(e,t)},useContext:function(e){return D="useContext",le(),Fe(),St(e)},useEffect:function(e,t){return D="useEffect",le(),Fe(),pc(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",le(),Fe(),Qv(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",le(),Fe(),qv(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",le(),Fe(),Gv(e,t)},useMemo:function(e,t){D="useMemo",le(),Fe();var n=H.current;H.current=Pa;try{return Iv(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",le(),Fe();var a=H.current;H.current=Pa;try{return Hv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",le(),Fe(),Pv(e)},useState:function(e){D="useState",le(),Fe();var t=H.current;H.current=Pa;try{return fc(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",le(),Fe(),void 0},useDeferredValue:function(e){return D="useDeferredValue",le(),Fe(),Xv(e)},useTransition:function(){return D="useTransition",le(),Fe(),Kv()},useMutableSource:function(e,t,n){return D="useMutableSource",le(),Fe(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",le(),Fe(),Bv(e,t,n)},useId:function(){return D="useId",le(),Fe(),Jv()},unstable_isNewReconciler:Vt},Ta={readContext:function(e){return Zv(),St(e)},useCallback:function(e,t){return D="useCallback",le(),L(),bc(e,t)},useContext:function(e){return D="useContext",le(),L(),St(e)},useEffect:function(e,t){return D="useEffect",le(),L(),po(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",le(),L(),yc(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",le(),L(),hc(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",le(),L(),mc(e,t)},useMemo:function(e,t){D="useMemo",le(),L();var n=H.current;H.current=Ta;try{return Sc(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",le(),L();var a=H.current;H.current=Ta;try{return Fv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",le(),L(),dc()},useState:function(e){D="useState",le(),L();var t=H.current;H.current=Ta;try{return Yv(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",le(),L(),gc()},useDeferredValue:function(e){return D="useDeferredValue",le(),L(),hg(e)},useTransition:function(){return D="useTransition",le(),L(),gg()},useMutableSource:function(e,t,n){return D="useMutableSource",le(),L(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",le(),L(),cc(e,t)},useId:function(){return D="useId",le(),L(),Ec()},unstable_isNewReconciler:Vt},Rc={readContext:function(e){return Zv(),St(e)},useCallback:function(e,t){return D="useCallback",le(),L(),bc(e,t)},useContext:function(e){return D="useContext",le(),L(),St(e)},useEffect:function(e,t){return D="useEffect",le(),L(),po(e,t)},useImperativeHandle:function(e,t,n){return D="useImperativeHandle",le(),L(),yc(e,t,n)},useInsertionEffect:function(e,t){return D="useInsertionEffect",le(),L(),hc(e,t)},useLayoutEffect:function(e,t){return D="useLayoutEffect",le(),L(),mc(e,t)},useMemo:function(e,t){D="useMemo",le(),L();var n=H.current;H.current=Ta;try{return Sc(e,t)}finally{H.current=n}},useReducer:function(e,t,n){D="useReducer",le(),L();var a=H.current;H.current=Ta;try{return Vv(e,t,n)}finally{H.current=a}},useRef:function(e){return D="useRef",le(),L(),dc()},useState:function(e){D="useState",le(),L();var t=H.current;H.current=Ta;try{return $v(e)}finally{H.current=t}},useDebugValue:function(e,t){return D="useDebugValue",le(),L(),gc()},useDeferredValue:function(e){return D="useDeferredValue",le(),L(),mg(e)},useTransition:function(){return D="useTransition",le(),L(),bg()},useMutableSource:function(e,t,n){return D="useMutableSource",le(),L(),void 0},useSyncExternalStore:function(e,t,n){return D="useSyncExternalStore",le(),L(),cc(e,t)},useId:function(){return D="useId",le(),L(),Ec()},unstable_isNewReconciler:Vt}}var Xr=N.unstable_now,Og=0,Tc=-1,ho=-1,xc=-1,ep=!1,wc=!1;function Ng(){return ep}function iD(){wc=!0}function lD(){ep=!1,wc=!1}function uD(){ep=wc,wc=!1}function Mg(){return Og}function Ag(){Og=Xr()}function tp(e){ho=Xr(),e.actualStartTime<0&&(e.actualStartTime=Xr())}function Lg(e){ho=-1}function Dc(e,t){if(ho>=0){var n=Xr()-ho;e.actualDuration+=n,t&&(e.selfBaseDuration=n),ho=-1}}function qa(e){if(Tc>=0){var t=Xr()-Tc;Tc=-1;for(var n=e.return;n!==null;){switch(n.tag){case W:var a=n.stateNode;a.effectDuration+=t;return;case it:var r=n.stateNode;r.effectDuration+=t;return}n=n.return}}}function np(e){if(xc>=0){var t=Xr()-xc;xc=-1;for(var n=e.return;n!==null;){switch(n.tag){case W:var a=n.stateNode;a!==null&&(a.passiveEffectDuration+=t);return;case it:var r=n.stateNode;r!==null&&(r.passiveEffectDuration+=t);return}n=n.return}}}function Ga(){Tc=Xr()}function ap(){xc=Xr()}function rp(e){for(var t=e.child;t;)e.actualDuration+=t.actualDuration,t=t.sibling}function Li(e,t){return{value:e,source:t,stack:nu(t),digest:null}}function ip(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function oD(e,t){return!0}function lp(e,t){try{var n=oD(e,t);if(n===!1)return;var a=t.value,r=t.source,i=t.stack,l=i!==null?i:"";if(a!=null&&a._suppressLogging){if(e.tag===B)return;console.error(a)}var u=r?de(r):null,o=u?"The above error occurred in the <"+u+"> component:":"The above error occurred in one of your React components:",c;if(e.tag===W)c=`Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.`;else{var f=de(e)||"Anonymous";c="React will try to recreate this component tree from scratch "+("using the error boundary you provided, "+f+".")}var y=o+`
`+l+`

`+(""+c);console.error(y)}catch(h){setTimeout(function(){throw h})}}var sD=typeof WeakMap=="function"?WeakMap:Map;function Ug(e,t,n){var a=gr(Ke,n);a.tag=lv,a.payload={element:null};var r=t.value;return a.callback=function(){tO(r),lp(e,t)},a}function up(e,t,n){var a=gr(Ke,n);a.tag=lv;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;a.payload=function(){return r(i)},a.callback=function(){qb(e),lp(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(a.callback=function(){qb(e),lp(e,t),typeof r!="function"&&Z_(this);var o=t.value,c=t.stack;this.componentDidCatch(o,{componentStack:c!==null?c:""}),typeof r!="function"&&($n(e.lanes,ie)||d("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",de(e)||"Unknown"))}),a}function jg(e,t,n){var a=e.pingCache,r;if(a===null?(a=e.pingCache=new sD,r=new Set,a.set(t,r)):(r=a.get(t),r===void 0&&(r=new Set,a.set(t,r))),!r.has(n)){r.add(n);var i=nO.bind(null,e,t,n);ya&&Mo(e,n),t.then(i,i)}}function cD(e,t,n,a){var r=e.updateQueue;if(r===null){var i=new Set;i.add(n),e.updateQueue=i}else r.add(n)}function fD(e,t){var n=e.tag;if((e.mode&Te)===ee&&(n===ae||n===Se||n===De)){var a=e.alternate;a?(e.updateQueue=a.updateQueue,e.memoizedState=a.memoizedState,e.lanes=a.lanes):(e.updateQueue=null,e.memoizedState=null)}}function kg(e){var t=e;do{if(t.tag===se&&Iw(t))return t;t=t.return}while(t!==null);return null}function zg(e,t,n,a,r){if((e.mode&Te)===ee){if(e===t)e.flags|=fn;else{if(e.flags|=je,n.flags|=Df,n.flags&=~(qE|pu),n.tag===B){var i=n.alternate;if(i===null)n.tag=Sn;else{var l=gr(Ke,ie);l.tag=Ks,Gr(n,l,ie)}}n.lanes=he(n.lanes,ie)}return e}return e.flags|=fn,e.lanes=r,e}function dD(e,t,n,a,r){if(n.flags|=pu,ya&&Mo(e,r),a!==null&&typeof a=="object"&&typeof a.then=="function"){var i=a;fD(n),$t()&&n.mode&Te&&Dy();var l=kg(t);if(l!==null){l.flags&=~lr,zg(l,t,n,e,r),l.mode&Te&&jg(e,i,r),cD(l,e,i);return}else{if(!jC(r)){jg(e,i,r),Vp();return}var u=new Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");a=u}}else if($t()&&n.mode&Te){Dy();var o=kg(t);if(o!==null){(o.flags&fn)===Z&&(o.flags|=lr),zg(o,t,n,e,r),Zd(Li(a,n));return}}a=Li(a,n),q_(a);var c=t;do{switch(c.tag){case W:{var f=a;c.flags|=fn;var y=Cu(r);c.lanes=he(c.lanes,y);var h=Ug(c,f,y);sv(c,h);return}case B:var S=a,E=c.type,T=c.stateNode;if((c.flags&je)===Z&&(typeof E.getDerivedStateFromError=="function"||T!==null&&typeof T.componentDidCatch=="function"&&!kb(T))){c.flags|=fn;var F=Cu(r);c.lanes=he(c.lanes,F);var X=up(c,S,F);sv(c,X);return}break}c=c.return}while(c!==null)}function vD(){return null}var mo=J.ReactCurrentOwner,xa=!1,op,yo,sp,cp,fp,Ui,dp,_c;op={},yo={},sp={},cp={},fp={},Ui=!1,dp={},_c={};function dn(e,t,n,a){e===null?t.child=tg(t,null,n,a):t.child=xl(t,e.child,n,a)}function pD(e,t,n,a){t.child=xl(t,e.child,null,a),t.child=xl(t,null,n,a)}function Hg(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&ba(i,a,"prop",Le(n))}var l=n.render,u=t.ref,o,c;Tl(t,r),mu(t);{if(mo.current=t,Bn(!0),o=Ml(e,t,l,a,u,r),c=Al(),t.mode&bt){zt(!0);try{o=Ml(e,t,l,a,u,r),c=Al()}finally{zt(!1)}}Bn(!1)}return rl(),e!==null&&!xa?(lg(e,t,r),br(e,t,r)):($t()&&c&&Qd(t),t.flags|=tl,dn(e,t,o,r),t.child)}function Fg(e,t,n,a,r){if(e===null){var i=n.type;if(bO(i)&&n.compare===null&&n.defaultProps===void 0){var l=i;return l=Vl(i),t.tag=De,t.type=l,hp(t,i),Vg(e,t,l,a,r)}{var u=i.propTypes;u&&ba(u,a,"prop",Le(i))}var o=Kp(n.type,null,a,t,t.mode,r);return o.ref=t.ref,o.return=t,t.child=o,o}{var c=n.type,f=c.propTypes;f&&ba(f,a,"prop",Le(c))}var y=e.child,h=Ep(e,r);if(!h){var S=y.memoizedProps,E=n.compare;if(E=E!==null?E:zu,E(S,a)&&e.ref===t.ref)return br(e,t,r)}t.flags|=tl;var T=Fi(y,a);return T.ref=t.ref,T.return=t,t.child=T,T}function Vg(e,t,n,a,r){if(t.type!==t.elementType){var i=t.elementType;if(i.$$typeof===K){var l=i,u=l._payload,o=l._init;try{i=o(u)}catch{i=null}var c=i&&i.propTypes;c&&ba(c,a,"prop",Le(i))}}if(e!==null){var f=e.memoizedProps;if(zu(f,a)&&e.ref===t.ref&&t.type===e.type)if(xa=!1,t.pendingProps=a=f,Ep(e,r))(e.flags&Df)!==Z&&(xa=!0);else return t.lanes=e.lanes,br(e,t,r)}return vp(e,t,n,a,r)}function Bg(e,t,n){var a=t.pendingProps,r=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"||Xn)if((t.mode&Te)===ee){var l={baseLanes:_,cachePool:null,transitions:null};t.memoizedState=l,Bc(t,n)}else if($n(n,Yn)){var y={baseLanes:_,cachePool:null,transitions:null};t.memoizedState=y;var h=i!==null?i.baseLanes:n;Bc(t,h)}else{var u=null,o;if(i!==null){var c=i.baseLanes;o=he(c,n)}else o=n;t.lanes=t.childLanes=Yn;var f={baseLanes:o,cachePool:u,transitions:null};return t.memoizedState=f,t.updateQueue=null,Bc(t,o),null}else{var S;i!==null?(S=he(i.baseLanes,n),t.memoizedState=null):S=n,Bc(t,S)}return dn(e,t,r,n),t.child}function hD(e,t,n){var a=t.pendingProps;return dn(e,t,a,n),t.child}function mD(e,t,n){var a=t.pendingProps.children;return dn(e,t,a,n),t.child}function yD(e,t,n){{t.flags|=_e;{var a=t.stateNode;a.effectDuration=0,a.passiveEffectDuration=0}}var r=t.pendingProps,i=r.children;return dn(e,t,i,n),t.child}function Yg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=Ur,t.flags|=_f)}function vp(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&ba(i,a,"prop",Le(n))}var l;{var u=gl(t,n,!0);l=bl(t,u)}var o,c;Tl(t,r),mu(t);{if(mo.current=t,Bn(!0),o=Ml(e,t,n,a,l,r),c=Al(),t.mode&bt){zt(!0);try{o=Ml(e,t,n,a,l,r),c=Al()}finally{zt(!1)}}Bn(!1)}return rl(),e!==null&&!xa?(lg(e,t,r),br(e,t,r)):($t()&&c&&Qd(t),t.flags|=tl,dn(e,t,o,r),t.child)}function $g(e,t,n,a,r){{switch(UO(t)){case!1:{var i=t.stateNode,l=t.type,u=new l(t.memoizedProps,i.context),o=u.state;i.updater.enqueueSetState(i,o,null);break}case!0:{t.flags|=je,t.flags|=fn;var c=new Error("Simulated error coming from DevTools"),f=Cu(r);t.lanes=he(t.lanes,f);var y=up(t,Li(c,t),f);sv(t,y);break}}if(t.type!==t.elementType){var h=n.propTypes;h&&ba(h,a,"prop",Le(n))}}var S;Ba(n)?(S=!0,Vs(t)):S=!1,Tl(t,r);var E=t.stateNode,T;E===null?(Nc(e,t),Xy(t,n,a),Ev(t,n,a,r),T=!0):e===null?T=$w(t,n,a,r):T=Pw(e,t,n,a,r);var F=pp(e,t,n,T,S,r);{var X=t.stateNode;T&&X.props!==a&&(Ui||d("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",de(t)||"a component"),Ui=!0)}return F}function pp(e,t,n,a,r,i){Yg(e,t);var l=(t.flags&je)!==Z;if(!a&&!l)return r&&Ry(t,n,!1),br(e,t,i);var u=t.stateNode;mo.current=t;var o;if(l&&typeof n.getDerivedStateFromError!="function")o=null,Lg();else{mu(t);{if(Bn(!0),o=u.render(),t.mode&bt){zt(!0);try{u.render()}finally{zt(!1)}}Bn(!1)}rl()}return t.flags|=tl,e!==null&&l?pD(e,t,o,i):dn(e,t,o,i),t.memoizedState=u.state,r&&Ry(t,n,!0),t.child}function Pg(e){var t=e.stateNode;t.pendingContext?Ey(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ey(e,t.context,!1),Dv(e,t.containerInfo)}function gD(e,t,n){if(Pg(t),e===null)throw new Error("Should have a current fiber. This is a bug in React.");var a=t.pendingProps,r=t.memoizedState,i=r.element;Yy(e,t),tc(t,a,null,n);var l=t.memoizedState;t.stateNode;var u=l.element;if(r.isDehydrated){var o={element:u,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},c=t.updateQueue;if(c.baseState=o,t.memoizedState=o,t.flags&lr){var f=Li(new Error("There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering."),t);return qg(e,t,u,n,f)}else if(u!==i){var y=Li(new Error("This root received an early update, before anything was able hydrate. Switched the entire root to client rendering."),t);return qg(e,t,u,n,y)}else{Cw(t);var h=tg(t,null,u,n);t.child=h;for(var S=h;S;)S.flags=S.flags&~gt|ur,S=S.sibling}}else{if(Cl(),u===i)return br(e,t,n);dn(e,t,u,n)}return t.child}function qg(e,t,n,a,r){return Cl(),Zd(r),t.flags|=lr,dn(e,t,n,a),t.child}function bD(e,t,n){ag(t),e===null&&Jd(t);var a=t.type,r=t.pendingProps,i=e!==null?e.memoizedProps:null,l=r.children,u=Ld(a,r);return u?l=null:i!==null&&Ld(a,i)&&(t.flags|=vu),Yg(e,t),dn(e,t,l,n),t.child}function SD(e,t){return e===null&&Jd(t),null}function ED(e,t,n,a){Nc(e,t);var r=t.pendingProps,i=n,l=i._payload,u=i._init,o=u(l);t.type=o;var c=t.tag=SO(o),f=Ca(o,r),y;switch(c){case ae:return hp(t,o),t.type=o=Vl(o),y=vp(null,t,o,f,a),y;case B:return t.type=o=qp(o),y=$g(null,t,o,f,a),y;case Se:return t.type=o=Gp(o),y=Hg(null,t,o,f,a),y;case Je:{if(t.type!==t.elementType){var h=o.propTypes;h&&ba(h,f,"prop",Le(o))}return y=Fg(null,t,o,Ca(o.type,f),a),y}}var S="";throw o!==null&&typeof o=="object"&&o.$$typeof===K&&(S=" Did you wrap a component in React.lazy() more than once?"),new Error("Element type is invalid. Received a promise that resolves to: "+o+". "+("Lazy element type must resolve to a class or function."+S))}function CD(e,t,n,a,r){Nc(e,t),t.tag=B;var i;return Ba(n)?(i=!0,Vs(t)):i=!1,Tl(t,r),Xy(t,n,a),Ev(t,n,a,r),pp(null,t,n,!0,i,r)}function RD(e,t,n,a){Nc(e,t);var r=t.pendingProps,i;{var l=gl(t,n,!1);i=bl(t,l)}Tl(t,a);var u,o;mu(t);{if(n.prototype&&typeof n.prototype.render=="function"){var c=Le(n)||"Unknown";op[c]||(d("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",c,c),op[c]=!0)}t.mode&bt&&Ea.recordLegacyContextWarning(t,null),Bn(!0),mo.current=t,u=Ml(null,t,n,r,i,a),o=Al(),Bn(!1)}if(rl(),t.flags|=tl,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0){var f=Le(n)||"Unknown";yo[f]||(d("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",f,f,f),yo[f]=!0)}if(typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0){{var y=Le(n)||"Unknown";yo[y]||(d("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",y,y,y),yo[y]=!0)}t.tag=B,t.memoizedState=null,t.updateQueue=null;var h=!1;return Ba(n)?(h=!0,Vs(t)):h=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,ov(t),Iy(t,u),Ev(t,n,r,a),pp(null,t,n,!0,h,a)}else{if(t.tag=ae,t.mode&bt){zt(!0);try{u=Ml(null,t,n,r,i,a),o=Al()}finally{zt(!1)}}return $t()&&o&&Qd(t),dn(null,t,u,a),hp(t,n),t.child}}function hp(e,t){{if(t&&t.childContextTypes&&d("%s(...): childContextTypes cannot be defined on a function component.",t.displayName||t.name||"Component"),e.ref!==null){var n="",a=Nr();a&&(n+=`

Check the render method of \``+a+"`.");var r=a||"",i=e._debugSource;i&&(r=i.fileName+":"+i.lineNumber),fp[r]||(fp[r]=!0,d("Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?%s",n))}if(typeof t.getDerivedStateFromProps=="function"){var l=Le(t)||"Unknown";cp[l]||(d("%s: Function components do not support getDerivedStateFromProps.",l),cp[l]=!0)}if(typeof t.contextType=="object"&&t.contextType!==null){var u=Le(t)||"Unknown";sp[u]||(d("%s: Function components do not support contextType.",u),sp[u]=!0)}}}var mp={dehydrated:null,treeContext:null,retryLane:Ht};function yp(e){return{baseLanes:e,cachePool:vD(),transitions:null}}function TD(e,t){var n=null;return{baseLanes:he(e.baseLanes,t),cachePool:n,transitions:e.transitions}}function xD(e,t,n,a){if(t!==null){var r=t.memoizedState;if(r===null)return!1}return Nv(e,uo)}function wD(e,t){return ss(e.childLanes,t)}function Gg(e,t,n){var a=t.pendingProps;jO(t)&&(t.flags|=je);var r=Ra.current,i=!1,l=(t.flags&je)!==Z;if(l||xD(r,e)?(i=!0,t.flags&=~je):(e===null||e.memoizedState!==null)&&(r=Ww(r,ig)),r=Dl(r),Wr(t,r),e===null){Jd(t);var u=t.memoizedState;if(u!==null){var o=u.dehydrated;if(o!==null)return MD(t,o)}var c=a.children,f=a.fallback;if(i){var y=DD(t,c,f,n),h=t.child;return h.memoizedState=yp(n),t.memoizedState=mp,y}else return gp(t,c)}else{var S=e.memoizedState;if(S!==null){var E=S.dehydrated;if(E!==null)return AD(e,t,l,a,E,S,n)}if(i){var T=a.fallback,F=a.children,X=OD(e,t,F,T,n),Q=t.child,we=e.child.memoizedState;return Q.memoizedState=we===null?yp(n):TD(we,n),Q.childLanes=wD(e,n),t.memoizedState=mp,X}else{var be=a.children,g=_D(e,t,be,n);return t.memoizedState=null,g}}}function gp(e,t,n){var a=e.mode,r={mode:"visible",children:t},i=bp(r,a);return i.return=e,e.child=i,i}function DD(e,t,n,a){var r=e.mode,i=e.child,l={mode:"hidden",children:t},u,o;return(r&Te)===ee&&i!==null?(u=i,u.childLanes=_,u.pendingProps=l,e.mode&$e&&(u.actualDuration=0,u.actualStartTime=-1,u.selfBaseDuration=0,u.treeBaseDuration=0),o=ni(n,r,a,null)):(u=bp(l,r),o=ni(n,r,a,null)),u.return=e,o.return=e,u.sibling=o,e.child=u,o}function bp(e,t,n){return Qb(e,t,_,null)}function Qg(e,t){return Fi(e,t)}function _D(e,t,n,a){var r=e.child,i=r.sibling,l=Qg(r,{mode:"visible",children:n});if((t.mode&Te)===ee&&(l.lanes=a),l.return=t,l.sibling=null,i!==null){var u=t.deletions;u===null?(t.deletions=[i],t.flags|=di):u.push(i)}return t.child=l,l}function OD(e,t,n,a,r){var i=t.mode,l=e.child,u=l.sibling,o={mode:"hidden",children:n},c;if((i&Te)===ee&&t.child!==l){var f=t.child;c=f,c.childLanes=_,c.pendingProps=o,t.mode&$e&&(c.actualDuration=0,c.actualStartTime=-1,c.selfBaseDuration=l.selfBaseDuration,c.treeBaseDuration=l.treeBaseDuration),t.deletions=null}else c=Qg(l,o),c.subtreeFlags=l.subtreeFlags&sr;var y;return u!==null?y=Fi(u,a):(y=ni(a,i,r,null),y.flags|=gt),y.return=t,c.return=t,c.sibling=y,t.child=c,y}function Oc(e,t,n,a){a!==null&&Zd(a),xl(t,e.child,null,n);var r=t.pendingProps,i=r.children,l=gp(t,i);return l.flags|=gt,t.memoizedState=null,l}function ND(e,t,n,a,r){var i=t.mode,l={mode:"visible",children:n},u=bp(l,i),o=ni(a,i,r,null);return o.flags|=gt,u.return=t,o.return=t,u.sibling=o,t.child=u,(t.mode&Te)!==ee&&xl(t,e.child,null,r),o}function MD(e,t,n){return(e.mode&Te)===ee?(d("Cannot hydrate Suspense in legacy mode. Switch from ReactDOM.hydrate(element, container) to ReactDOMClient.hydrateRoot(container, <App />).render(element) or remove the Suspense components from the server rendered components."),e.lanes=ie):zd(t)?e.lanes=gi:e.lanes=Yn,null}function AD(e,t,n,a,r,i,l){if(n)if(t.flags&lr){t.flags&=~lr;var g=ip(new Error("There was an error while hydrating this Suspense boundary. Switched to client rendering."));return Oc(e,t,l,g)}else{if(t.memoizedState!==null)return t.child=e.child,t.flags|=je,null;var x=a.children,b=a.fallback,M=ND(e,t,x,b,l),V=t.child;return V.memoizedState=yp(l),t.memoizedState=mp,M}else{if(Sw(),(t.mode&Te)===ee)return Oc(e,t,l,null);if(zd(r)){var u,o,c;{var f=zx(r);u=f.digest,o=f.message,c=f.stack}var y;o?y=new Error(o):y=new Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering.");var h=ip(y,u,c);return Oc(e,t,l,h)}var S=$n(l,e.childLanes);if(xa||S){var E=Vc();if(E!==null){var T=$C(E,l);if(T!==Ht&&T!==i.retryLane){i.retryLane=T;var F=Ke;On(e,T),Mt(E,e,T,F)}}Vp();var X=ip(new Error("This Suspense boundary received an update before it finished hydrating. This caused the boundary to switch to client rendering. The usual way to fix this is to wrap the original update in startTransition."));return Oc(e,t,l,X)}else if(hy(r)){t.flags|=je,t.child=e.child;var Q=aO.bind(null,e);return Hx(r,Q),null}else{Rw(t,r,i.treeContext);var we=a.children,be=gp(t,we);return be.flags|=ur,be}}}function Wg(e,t,n){e.lanes=he(e.lanes,t);var a=e.alternate;a!==null&&(a.lanes=he(a.lanes,t)),rv(e.return,t,n)}function LD(e,t,n){for(var a=t;a!==null;){if(a.tag===se){var r=a.memoizedState;r!==null&&Wg(a,n,e)}else if(a.tag===Xe)Wg(a,n,e);else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;a=a.return}a.sibling.return=a.return,a=a.sibling}}function UD(e){for(var t=e,n=null;t!==null;){var a=t.alternate;a!==null&&oc(a)===null&&(n=t),t=t.sibling}return n}function jD(e){if(e!==void 0&&e!=="forwards"&&e!=="backwards"&&e!=="together"&&!dp[e])if(dp[e]=!0,typeof e=="string")switch(e.toLowerCase()){case"together":case"forwards":case"backwards":{d('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',e,e.toLowerCase());break}case"forward":case"backward":{d('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',e,e.toLowerCase());break}default:d('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e);break}else d('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e)}function kD(e,t){e!==void 0&&!_c[e]&&(e!=="collapsed"&&e!=="hidden"?(_c[e]=!0,d('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',e)):t!=="forwards"&&t!=="backwards"&&(_c[e]=!0,d('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',e)))}function Ig(e,t){{var n=Ue(e),a=!n&&typeof er(e)=="function";if(n||a){var r=n?"array":"iterable";return d("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",r,t,r),!1}}return!0}function zD(e,t){if((t==="forwards"||t==="backwards")&&e!==void 0&&e!==null&&e!==!1)if(Ue(e)){for(var n=0;n<e.length;n++)if(!Ig(e[n],n))return}else{var a=er(e);if(typeof a=="function"){var r=a.call(e);if(r)for(var i=r.next(),l=0;!i.done;i=r.next()){if(!Ig(i.value,l))return;l++}}else d('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',t)}}function Sp(e,t,n,a,r){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=n,i.tailMode=r)}function Xg(e,t,n){var a=t.pendingProps,r=a.revealOrder,i=a.tail,l=a.children;jD(r),kD(i,r),zD(l,r),dn(e,t,l,n);var u=Ra.current,o=Nv(u,uo);if(o)u=Mv(u,uo),t.flags|=je;else{var c=e!==null&&(e.flags&je)!==Z;c&&LD(t,t.child,n),u=Dl(u)}if(Wr(t,u),(t.mode&Te)===ee)t.memoizedState=null;else switch(r){case"forwards":{var f=UD(t.child),y;f===null?(y=t.child,t.child=null):(y=f.sibling,f.sibling=null),Sp(t,!1,y,f,i);break}case"backwards":{var h=null,S=t.child;for(t.child=null;S!==null;){var E=S.alternate;if(E!==null&&oc(E)===null){t.child=S;break}var T=S.sibling;S.sibling=h,h=S,S=T}Sp(t,!0,h,null,i);break}case"together":{Sp(t,!1,null,null,void 0);break}default:t.memoizedState=null}return t.child}function HD(e,t,n){Dv(t,t.stateNode.containerInfo);var a=t.pendingProps;return e===null?t.child=xl(t,null,a,n):dn(e,t,a,n),t.child}var Kg=!1;function FD(e,t,n){var a=t.type,r=a._context,i=t.pendingProps,l=t.memoizedProps,u=i.value;{"value"in i||Kg||(Kg=!0,d("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?"));var o=t.type.propTypes;o&&ba(o,i,"prop","Context.Provider")}if(Hy(t,r,u),l!==null){var c=l.value;if(Gn(c,u)){if(l.children===i.children&&!Hs())return br(e,t,n)}else Lw(t,r,n)}var f=i.children;return dn(e,t,f,n),t.child}var Jg=!1;function VD(e,t,n){var a=t.type;a._context===void 0?a!==a.Consumer&&(Jg||(Jg=!0,d("Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?"))):a=a._context;var r=t.pendingProps,i=r.children;typeof i!="function"&&d("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),Tl(t,n);var l=St(a);mu(t);var u;return mo.current=t,Bn(!0),u=i(l),Bn(!1),rl(),t.flags|=tl,dn(e,t,u,n),t.child}function go(){xa=!0}function Nc(e,t){(t.mode&Te)===ee&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=gt)}function br(e,t,n){return e!==null&&(t.dependencies=e.dependencies),Lg(),No(t.lanes),$n(n,t.childLanes)?(qw(e,t),t.child):null}function BD(e,t,n){{var a=t.return;if(a===null)throw new Error("Cannot swap the root fiber.");if(e.alternate=null,t.alternate=null,n.index=t.index,n.sibling=t.sibling,n.return=t.return,n.ref=t.ref,t===a.child)a.child=n;else{var r=a.child;if(r===null)throw new Error("Expected parent to have a child.");for(;r.sibling!==t;)if(r=r.sibling,r===null)throw new Error("Expected to find the previous sibling.");r.sibling=n}var i=a.deletions;return i===null?(a.deletions=[e],a.flags|=di):i.push(e),n.flags|=gt,n}}function Ep(e,t){var n=e.lanes;return!!$n(n,t)}function YD(e,t,n){switch(t.tag){case W:Pg(t),t.stateNode,Cl();break;case G:ag(t);break;case B:{var a=t.type;Ba(a)&&Vs(t);break}case oe:Dv(t,t.stateNode.containerInfo);break;case He:{var r=t.memoizedProps.value,i=t.type._context;Hy(t,i,r);break}case it:{var l=$n(n,t.childLanes);l&&(t.flags|=_e);{var u=t.stateNode;u.effectDuration=0,u.passiveEffectDuration=0}}break;case se:{var o=t.memoizedState;if(o!==null){if(o.dehydrated!==null)return Wr(t,Dl(Ra.current)),t.flags|=je,null;var c=t.child,f=c.childLanes;if($n(n,f))return Gg(e,t,n);Wr(t,Dl(Ra.current));var y=br(e,t,n);return y!==null?y.sibling:null}else Wr(t,Dl(Ra.current));break}case Xe:{var h=(e.flags&je)!==Z,S=$n(n,t.childLanes);if(h){if(S)return Xg(e,t,n);t.flags|=je}var E=t.memoizedState;if(E!==null&&(E.rendering=null,E.tail=null,E.lastEffect=null),Wr(t,Ra.current),S)break;return null}case ge:case Ae:return t.lanes=_,Bg(e,t,n)}return br(e,t,n)}function Zg(e,t,n){if(t._debugNeedsRemount&&e!==null)return BD(e,t,Kp(t.type,t.key,t.pendingProps,t._debugOwner||null,t.mode,t.lanes));if(e!==null){var a=e.memoizedProps,r=t.pendingProps;if(a!==r||Hs()||t.type!==e.type)xa=!0;else{var i=Ep(e,n);if(!i&&(t.flags&je)===Z)return xa=!1,YD(e,t,n);(e.flags&Df)!==Z?xa=!0:xa=!1}}else if(xa=!1,$t()&&pw(t)){var l=t.index,u=hw();wy(t,u,l)}switch(t.lanes=_,t.tag){case q:return RD(e,t,t.type,n);case bn:{var o=t.elementType;return ED(e,t,o,n)}case ae:{var c=t.type,f=t.pendingProps,y=t.elementType===c?f:Ca(c,f);return vp(e,t,c,y,n)}case B:{var h=t.type,S=t.pendingProps,E=t.elementType===h?S:Ca(h,S);return $g(e,t,h,E,n)}case W:return gD(e,t,n);case G:return bD(e,t,n);case ye:return SD(e,t);case se:return Gg(e,t,n);case oe:return HD(e,t,n);case Se:{var T=t.type,F=t.pendingProps,X=t.elementType===T?F:Ca(T,F);return Hg(e,t,T,X,n)}case gn:return hD(e,t,n);case Ln:return mD(e,t,n);case it:return yD(e,t,n);case He:return FD(e,t,n);case Kt:return VD(e,t,n);case Je:{var Q=t.type,we=t.pendingProps,be=Ca(Q,we);if(t.type!==t.elementType){var g=Q.propTypes;g&&ba(g,be,"prop",Le(Q))}return be=Ca(Q.type,be),Fg(e,t,Q,be,n)}case De:return Vg(e,t,t.type,t.pendingProps,n);case Sn:{var x=t.type,b=t.pendingProps,M=t.elementType===x?b:Ca(x,b);return CD(e,t,x,M,n)}case Xe:return Xg(e,t,n);case Un:break;case ge:return Bg(e,t,n)}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function Ll(e){e.flags|=_e}function eb(e){e.flags|=Ur,e.flags|=_f}var tb,Cp,nb,ab;tb=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===G||r.tag===ye)fx(e,r.stateNode);else if(r.tag!==oe){if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Cp=function(e,t){},nb=function(e,t,n,a,r){var i=e.memoizedProps;if(i!==a){var l=t.stateNode,u=_v(),o=vx(l,n,i,a,r,u);t.updateQueue=o,o&&Ll(t)}},ab=function(e,t,n,a){n!==a&&Ll(t)};function bo(e,t){if(!$t())switch(e.tailMode){case"hidden":{for(var n=e.tail,a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e.tail=null:a.sibling=null;break}case"collapsed":{for(var r=e.tail,i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?!t&&e.tail!==null?e.tail.sibling=null:e.tail=null:i.sibling=null;break}}}function qt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=_,a=Z;if(t){if((e.mode&$e)!==ee){for(var o=e.selfBaseDuration,c=e.child;c!==null;)n=he(n,he(c.lanes,c.childLanes)),a|=c.subtreeFlags&sr,a|=c.flags&sr,o+=c.treeBaseDuration,c=c.sibling;e.treeBaseDuration=o}else for(var f=e.child;f!==null;)n=he(n,he(f.lanes,f.childLanes)),a|=f.subtreeFlags&sr,a|=f.flags&sr,f.return=e,f=f.sibling;e.subtreeFlags|=a}else{if((e.mode&$e)!==ee){for(var r=e.actualDuration,i=e.selfBaseDuration,l=e.child;l!==null;)n=he(n,he(l.lanes,l.childLanes)),a|=l.subtreeFlags,a|=l.flags,r+=l.actualDuration,i+=l.treeBaseDuration,l=l.sibling;e.actualDuration=r,e.treeBaseDuration=i}else for(var u=e.child;u!==null;)n=he(n,he(u.lanes,u.childLanes)),a|=u.subtreeFlags,a|=u.flags,u.return=e,u=u.sibling;e.subtreeFlags|=a}return e.childLanes=n,t}function $D(e,t,n){if(_w()&&(t.mode&Te)!==ee&&(t.flags&je)===Z)return Ly(t),Cl(),t.flags|=lr|pu|fn,!1;var a=qs(t);if(n!==null&&n.dehydrated!==null)if(e===null){if(!a)throw new Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");if(ww(t),qt(t),(t.mode&$e)!==ee){var r=n!==null;if(r){var i=t.child;i!==null&&(t.treeBaseDuration-=i.treeBaseDuration)}}return!1}else{if(Cl(),(t.flags&je)===Z&&(t.memoizedState=null),t.flags|=_e,qt(t),(t.mode&$e)!==ee){var l=n!==null;if(l){var u=t.child;u!==null&&(t.treeBaseDuration-=u.treeBaseDuration)}}return!1}else return Uy(),!0}function rb(e,t,n){var a=t.pendingProps;switch(Wd(t),t.tag){case q:case bn:case De:case ae:case Se:case gn:case Ln:case it:case Kt:case Je:return qt(t),null;case B:{var r=t.type;return Ba(r)&&Fs(t),qt(t),null}case W:{var i=t.stateNode;if(wl(t),Pd(t),Lv(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),e===null||e.child===null){var l=qs(t);if(l)Ll(t);else if(e!==null){var u=e.memoizedState;(!u.isDehydrated||(t.flags&lr)!==Z)&&(t.flags|=vi,Uy())}}return Cp(e,t),qt(t),null}case G:{Ov(t);var o=ng(),c=t.type;if(e!==null&&t.stateNode!=null)nb(e,t,c,a,o),e.ref!==t.ref&&eb(t);else{if(!a){if(t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return qt(t),null}var f=_v(),y=qs(t);if(y)Tw(t,o,f)&&Ll(t);else{var h=cx(c,a,o,f,t);tb(h,t,!1,!1),t.stateNode=h,dx(h,c,a,o)&&Ll(t)}t.ref!==null&&eb(t)}return qt(t),null}case ye:{var S=a;if(e&&t.stateNode!=null){var E=e.memoizedProps;ab(e,t,E,S)}else{if(typeof S!="string"&&t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");var T=ng(),F=_v(),X=qs(t);X?xw(t)&&Ll(t):t.stateNode=px(S,T,F,t)}return qt(t),null}case se:{_l(t);var Q=t.memoizedState;if(e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){var we=$D(e,t,Q);if(!we)return t.flags&fn?t:null}if((t.flags&je)!==Z)return t.lanes=n,(t.mode&$e)!==ee&&rp(t),t;var be=Q!==null,g=e!==null&&e.memoizedState!==null;if(be!==g&&be){var x=t.child;if(x.flags|=pi,(t.mode&Te)!==ee){var b=e===null&&(t.memoizedProps.unstable_avoidThisFallback!==!0||!ca);b||Nv(Ra.current,ig)?P_():Vp()}}var M=t.updateQueue;if(M!==null&&(t.flags|=_e),qt(t),(t.mode&$e)!==ee&&be){var V=t.child;V!==null&&(t.treeBaseDuration-=V.treeBaseDuration)}return null}case oe:return wl(t),Cp(e,t),e===null&&uw(t.stateNode.containerInfo),qt(t),null;case He:var k=t.type._context;return av(k,t),qt(t),null;case Sn:{var ne=t.type;return Ba(ne)&&Fs(t),qt(t),null}case Xe:{_l(t);var ue=t.memoizedState;if(ue===null)return qt(t),null;var qe=(t.flags&je)!==Z,Ne=ue.rendering;if(Ne===null)if(qe)bo(ue,!1);else{var pt=G_()&&(e===null||(e.flags&je)===Z);if(!pt)for(var Me=t.child;Me!==null;){var st=oc(Me);if(st!==null){qe=!0,t.flags|=je,bo(ue,!1);var un=st.updateQueue;return un!==null&&(t.updateQueue=un,t.flags|=_e),t.subtreeFlags=Z,Gw(t,n),Wr(t,Mv(Ra.current,uo)),t.child}Me=Me.sibling}ue.tail!==null&&kt()>xb()&&(t.flags|=je,qe=!0,bo(ue,!1),t.lanes=nm)}else{if(!qe){var Xt=oc(Ne);if(Xt!==null){t.flags|=je,qe=!0;var In=Xt.updateQueue;if(In!==null&&(t.updateQueue=In,t.flags|=_e),bo(ue,!0),ue.tail===null&&ue.tailMode==="hidden"&&!Ne.alternate&&!$t())return qt(t),null}else kt()*2-ue.renderingStartTime>xb()&&n!==Yn&&(t.flags|=je,qe=!0,bo(ue,!1),t.lanes=nm)}if(ue.isBackwards)Ne.sibling=t.child,t.child=Ne;else{var hn=ue.last;hn!==null?hn.sibling=Ne:t.child=Ne,ue.last=Ne}}if(ue.tail!==null){var mn=ue.tail;ue.rendering=mn,ue.tail=mn.sibling,ue.renderingStartTime=kt(),mn.sibling=null;var on=Ra.current;return qe?on=Mv(on,uo):on=Dl(on),Wr(t,on),mn}return qt(t),null}case Un:break;case ge:case Ae:{Fp(t);var Tr=t.memoizedState,Bl=Tr!==null;if(e!==null){var jo=e.memoizedState,Ia=jo!==null;Ia!==Bl&&!Xn&&(t.flags|=pi)}return!Bl||(t.mode&Te)===ee?qt(t):$n(Wa,Yn)&&(qt(t),t.subtreeFlags&(gt|_e)&&(t.flags|=pi)),null}case ct:return null;case ft:return null}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function PD(e,t,n){switch(Wd(t),t.tag){case B:{var a=t.type;Ba(a)&&Fs(t);var r=t.flags;return r&fn?(t.flags=r&~fn|je,(t.mode&$e)!==ee&&rp(t),t):null}case W:{t.stateNode,wl(t),Pd(t),Lv();var i=t.flags;return(i&fn)!==Z&&(i&je)===Z?(t.flags=i&~fn|je,t):null}case G:return Ov(t),null;case se:{_l(t);var l=t.memoizedState;if(l!==null&&l.dehydrated!==null){if(t.alternate===null)throw new Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");Cl()}var u=t.flags;return u&fn?(t.flags=u&~fn|je,(t.mode&$e)!==ee&&rp(t),t):null}case Xe:return _l(t),null;case oe:return wl(t),null;case He:var o=t.type._context;return av(o,t),null;case ge:case Ae:return Fp(t),null;case ct:return null;default:return null}}function ib(e,t,n){switch(Wd(t),t.tag){case B:{var a=t.type.childContextTypes;a!=null&&Fs(t);break}case W:{t.stateNode,wl(t),Pd(t),Lv();break}case G:{Ov(t);break}case oe:wl(t);break;case se:_l(t);break;case Xe:_l(t);break;case He:var r=t.type._context;av(r,t);break;case ge:case Ae:Fp(t);break}}var lb=null;lb=new Set;var Mc=!1,Gt=!1,qD=typeof WeakSet=="function"?WeakSet:Set,$=null,Ul=null,jl=null;function GD(e){Tf(null,function(){throw e}),xf()}var QD=function(e,t){if(t.props=e.memoizedProps,t.state=e.memoizedState,e.mode&$e)try{Ga(),t.componentWillUnmount()}finally{qa(e)}else t.componentWillUnmount()};function ub(e,t){try{Kr(xt,e)}catch(n){Ie(e,t,n)}}function Rp(e,t,n){try{QD(e,n)}catch(a){Ie(e,t,a)}}function WD(e,t,n){try{n.componentDidMount()}catch(a){Ie(e,t,a)}}function ob(e,t){try{cb(e)}catch(n){Ie(e,t,n)}}function kl(e,t){var n=e.ref;if(n!==null)if(typeof n=="function"){var a;try{if(fa&&da&&e.mode&$e)try{Ga(),a=n(null)}finally{qa(e)}else a=n(null)}catch(r){Ie(e,t,r)}typeof a=="function"&&d("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",de(e))}else n.current=null}function Ac(e,t,n){try{n()}catch(a){Ie(e,t,a)}}var sb=!1;function ID(e,t){ox(e.containerInfo),$=t,XD();var n=sb;return sb=!1,n}function XD(){for(;$!==null;){var e=$,t=e.child;(e.subtreeFlags&Nf)!==Z&&t!==null?(t.return=e,$=t):KD()}}function KD(){for(;$!==null;){var e=$;lt(e);try{JD(e)}catch(n){Ie(e,e.return,n)}jt();var t=e.sibling;if(t!==null){t.return=e.return,$=t;return}$=e.return}}function JD(e){var t=e.alternate,n=e.flags;if((n&vi)!==Z){switch(lt(e),e.tag){case ae:case Se:case De:break;case B:{if(t!==null){var a=t.memoizedProps,r=t.memoizedState,i=e.stateNode;e.type===e.elementType&&!Ui&&(i.props!==e.memoizedProps&&d("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",de(e)||"instance"),i.state!==e.memoizedState&&d("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",de(e)||"instance"));var l=i.getSnapshotBeforeUpdate(e.elementType===e.type?a:Ca(e.type,a),r);{var u=lb;l===void 0&&!u.has(e.type)&&(u.add(e.type),d("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",de(e)))}i.__reactInternalSnapshotBeforeUpdate=l}break}case W:{{var o=e.stateNode;Lx(o.containerInfo)}break}case G:case ye:case oe:case Sn:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}jt()}}function wa(e,t,n){var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var i=r.next,l=i;do{if((l.tag&e)===e){var u=l.destroy;l.destroy=void 0,u!==void 0&&((e&Pt)!==Nn?pC(t):(e&xt)!==Nn&&Kh(t),(e&Ya)!==Nn&&Ao(!0),Ac(t,n,u),(e&Ya)!==Nn&&Ao(!1),(e&Pt)!==Nn?hC():(e&xt)!==Nn&&Jh())}l=l.next}while(l!==i)}}function Kr(e,t){var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var r=a.next,i=r;do{if((i.tag&e)===e){(e&Pt)!==Nn?dC(t):(e&xt)!==Nn&&mC(t);var l=i.create;(e&Ya)!==Nn&&Ao(!0),i.destroy=l(),(e&Ya)!==Nn&&Ao(!1),(e&Pt)!==Nn?vC():(e&xt)!==Nn&&yC();{var u=i.destroy;if(u!==void 0&&typeof u!="function"){var o=void 0;(i.tag&xt)!==Z?o="useLayoutEffect":(i.tag&Ya)!==Z?o="useInsertionEffect":o="useEffect";var c=void 0;u===null?c=" You returned null. If your effect does not require clean up, return undefined (or nothing).":typeof u.then=="function"?c=`

It looks like you wrote `+o+`(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:

`+o+`(() => {
  async function fetchData() {
    // You can await here
    const response = await MyAPI.getData(someId);
    // ...
  }
  fetchData();
}, [someId]); // Or [] if effect doesn't need props or state

Learn more about data fetching with Hooks: https://reactjs.org/link/hooks-data-fetching`:c=" You returned: "+u,d("%s must not return anything besides a function, which is used for clean-up.%s",o,c)}}}i=i.next}while(i!==r)}}function ZD(e,t){if((t.flags&_e)!==Z)switch(t.tag){case it:{var n=t.stateNode.passiveEffectDuration,a=t.memoizedProps,r=a.id,i=a.onPostCommit,l=Mg(),u=t.alternate===null?"mount":"update";Ng()&&(u="nested-update"),typeof i=="function"&&i(r,u,n,l);var o=t.return;e:for(;o!==null;){switch(o.tag){case W:var c=o.stateNode;c.passiveEffectDuration+=n;break e;case it:var f=o.stateNode;f.passiveEffectDuration+=n;break e}o=o.return}break}}}function e_(e,t,n,a){if((n.flags&hu)!==Z)switch(n.tag){case ae:case Se:case De:{if(!Gt)if(n.mode&$e)try{Ga(),Kr(xt|Tt,n)}finally{qa(n)}else Kr(xt|Tt,n);break}case B:{var r=n.stateNode;if(n.flags&_e&&!Gt)if(t===null)if(n.type===n.elementType&&!Ui&&(r.props!==n.memoizedProps&&d("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",de(n)||"instance"),r.state!==n.memoizedState&&d("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",de(n)||"instance")),n.mode&$e)try{Ga(),r.componentDidMount()}finally{qa(n)}else r.componentDidMount();else{var i=n.elementType===n.type?t.memoizedProps:Ca(n.type,t.memoizedProps),l=t.memoizedState;if(n.type===n.elementType&&!Ui&&(r.props!==n.memoizedProps&&d("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",de(n)||"instance"),r.state!==n.memoizedState&&d("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",de(n)||"instance")),n.mode&$e)try{Ga(),r.componentDidUpdate(i,l,r.__reactInternalSnapshotBeforeUpdate)}finally{qa(n)}else r.componentDidUpdate(i,l,r.__reactInternalSnapshotBeforeUpdate)}var u=n.updateQueue;u!==null&&(n.type===n.elementType&&!Ui&&(r.props!==n.memoizedProps&&d("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",de(n)||"instance"),r.state!==n.memoizedState&&d("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",de(n)||"instance")),Py(n,u,r));break}case W:{var o=n.updateQueue;if(o!==null){var c=null;if(n.child!==null)switch(n.child.tag){case G:c=n.child.stateNode;break;case B:c=n.child.stateNode;break}Py(n,o,c)}break}case G:{var f=n.stateNode;if(t===null&&n.flags&_e){var y=n.type,h=n.memoizedProps;bx(f,y,h)}break}case ye:break;case oe:break;case it:{{var S=n.memoizedProps,E=S.onCommit,T=S.onRender,F=n.stateNode.effectDuration,X=Mg(),Q=t===null?"mount":"update";Ng()&&(Q="nested-update"),typeof T=="function"&&T(n.memoizedProps.id,Q,n.actualDuration,n.treeBaseDuration,n.actualStartTime,X);{typeof E=="function"&&E(n.memoizedProps.id,Q,F,X),K_(n);var we=n.return;e:for(;we!==null;){switch(we.tag){case W:var be=we.stateNode;be.effectDuration+=F;break e;case it:var g=we.stateNode;g.effectDuration+=F;break e}we=we.return}}}break}case se:{o_(e,n);break}case Xe:case Sn:case Un:case ge:case Ae:case ft:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}Gt||n.flags&Ur&&cb(n)}function t_(e){switch(e.tag){case ae:case Se:case De:{if(e.mode&$e)try{Ga(),ub(e,e.return)}finally{qa(e)}else ub(e,e.return);break}case B:{var t=e.stateNode;typeof t.componentDidMount=="function"&&WD(e,e.return,t),ob(e,e.return);break}case G:{ob(e,e.return);break}}}function n_(e,t){for(var n=null,a=e;;){if(a.tag===G){if(n===null){n=a;try{var r=a.stateNode;t?Ox(r):Mx(a.stateNode,a.memoizedProps)}catch(l){Ie(e,e.return,l)}}}else if(a.tag===ye){if(n===null)try{var i=a.stateNode;t?Nx(i):Ax(i,a.memoizedProps)}catch(l){Ie(e,e.return,l)}}else if(!((a.tag===ge||a.tag===Ae)&&a.memoizedState!==null&&a!==e)){if(a.child!==null){a.child.return=a,a=a.child;continue}}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;n===a&&(n=null),a=a.return}n===a&&(n=null),a.sibling.return=a.return,a=a.sibling}}function cb(e){var t=e.ref;if(t!==null){var n=e.stateNode,a;switch(e.tag){case G:a=n;break;default:a=n}if(typeof t=="function"){var r;if(e.mode&$e)try{Ga(),r=t(a)}finally{qa(e)}else r=t(a);typeof r=="function"&&d("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",de(e))}else t.hasOwnProperty("current")||d("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().",de(e)),t.current=a}}function a_(e){var t=e.alternate;t!==null&&(t.return=null),e.return=null}function fb(e){var t=e.alternate;t!==null&&(e.alternate=null,fb(t));{if(e.child=null,e.deletions=null,e.sibling=null,e.tag===G){var n=e.stateNode;n!==null&&cw(n)}e.stateNode=null,e._debugOwner=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}}function r_(e){for(var t=e.return;t!==null;){if(db(t))return t;t=t.return}throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.")}function db(e){return e.tag===G||e.tag===W||e.tag===oe}function vb(e){var t=e;e:for(;;){for(;t.sibling===null;){if(t.return===null||db(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==G&&t.tag!==ye&&t.tag!==ht;){if(t.flags&gt||t.child===null||t.tag===oe)continue e;t.child.return=t,t=t.child}if(!(t.flags&gt))return t.stateNode}}function i_(e){var t=r_(e);switch(t.tag){case G:{var n=t.stateNode;t.flags&vu&&(py(n),t.flags&=~vu);var a=vb(e);xp(e,a,n);break}case W:case oe:{var r=t.stateNode.containerInfo,i=vb(e);Tp(e,i,r);break}default:throw new Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}}function Tp(e,t,n){var a=e.tag,r=a===G||a===ye;if(r){var i=e.stateNode;t?xx(n,i,t):Rx(n,i)}else if(a!==oe){var l=e.child;if(l!==null){Tp(l,t,n);for(var u=l.sibling;u!==null;)Tp(u,t,n),u=u.sibling}}}function xp(e,t,n){var a=e.tag,r=a===G||a===ye;if(r){var i=e.stateNode;t?Tx(n,i,t):Cx(n,i)}else if(a!==oe){var l=e.child;if(l!==null){xp(l,t,n);for(var u=l.sibling;u!==null;)xp(u,t,n),u=u.sibling}}}var Qt=null,Da=!1;function l_(e,t,n){{var a=t;e:for(;a!==null;){switch(a.tag){case G:{Qt=a.stateNode,Da=!1;break e}case W:{Qt=a.stateNode.containerInfo,Da=!0;break e}case oe:{Qt=a.stateNode.containerInfo,Da=!0;break e}}a=a.return}if(Qt===null)throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");pb(e,t,n),Qt=null,Da=!1}a_(n)}function Jr(e,t,n){for(var a=n.child;a!==null;)pb(e,t,a),a=a.sibling}function pb(e,t,n){switch(oC(n),n.tag){case G:Gt||kl(n,t);case ye:{{var a=Qt,r=Da;Qt=null,Jr(e,t,n),Qt=a,Da=r,Qt!==null&&(Da?Dx(Qt,n.stateNode):wx(Qt,n.stateNode))}return}case ht:{Qt!==null&&(Da?_x(Qt,n.stateNode):kd(Qt,n.stateNode));return}case oe:{{var i=Qt,l=Da;Qt=n.stateNode.containerInfo,Da=!0,Jr(e,t,n),Qt=i,Da=l}return}case ae:case Se:case Je:case De:{if(!Gt){var u=n.updateQueue;if(u!==null){var o=u.lastEffect;if(o!==null){var c=o.next,f=c;do{var y=f,h=y.destroy,S=y.tag;h!==void 0&&((S&Ya)!==Nn?Ac(n,t,h):(S&xt)!==Nn&&(Kh(n),n.mode&$e?(Ga(),Ac(n,t,h),qa(n)):Ac(n,t,h),Jh())),f=f.next}while(f!==c)}}}Jr(e,t,n);return}case B:{if(!Gt){kl(n,t);var E=n.stateNode;typeof E.componentWillUnmount=="function"&&Rp(n,t,E)}Jr(e,t,n);return}case Un:{Jr(e,t,n);return}case ge:{if(n.mode&Te){var T=Gt;Gt=T||n.memoizedState!==null,Jr(e,t,n),Gt=T}else Jr(e,t,n);break}default:{Jr(e,t,n);return}}}function u_(e){e.memoizedState}function o_(e,t){var n=t.memoizedState;if(n===null){var a=t.alternate;if(a!==null){var r=a.memoizedState;if(r!==null){var i=r.dehydrated;i!==null&&Qx(i)}}}}function hb(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new qD),t.forEach(function(a){var r=rO.bind(null,e,a);if(!n.has(a)){if(n.add(a),ya)if(Ul!==null&&jl!==null)Mo(jl,Ul);else throw Error("Expected finished root and lanes to be set. This is a bug in React.");a.then(r,r)}})}}function s_(e,t,n){Ul=n,jl=e,lt(t),mb(t,e),lt(t),Ul=null,jl=null}function _a(e,t,n){var a=t.deletions;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r];try{l_(e,t,i)}catch(o){Ie(i,t,o)}}var l=$o();if(t.subtreeFlags&Mf)for(var u=t.child;u!==null;)lt(u),mb(u,e),u=u.sibling;lt(l)}function mb(e,t,n){var a=e.alternate,r=e.flags;switch(e.tag){case ae:case Se:case Je:case De:{if(_a(t,e),Qa(e),r&_e){try{wa(Ya|Tt,e,e.return),Kr(Ya|Tt,e)}catch(ne){Ie(e,e.return,ne)}if(e.mode&$e){try{Ga(),wa(xt|Tt,e,e.return)}catch(ne){Ie(e,e.return,ne)}qa(e)}else try{wa(xt|Tt,e,e.return)}catch(ne){Ie(e,e.return,ne)}}return}case B:{_a(t,e),Qa(e),r&Ur&&a!==null&&kl(a,a.return);return}case G:{_a(t,e),Qa(e),r&Ur&&a!==null&&kl(a,a.return);{if(e.flags&vu){var i=e.stateNode;try{py(i)}catch(ne){Ie(e,e.return,ne)}}if(r&_e){var l=e.stateNode;if(l!=null){var u=e.memoizedProps,o=a!==null?a.memoizedProps:u,c=e.type,f=e.updateQueue;if(e.updateQueue=null,f!==null)try{Sx(l,f,c,o,u,e)}catch(ne){Ie(e,e.return,ne)}}}}return}case ye:{if(_a(t,e),Qa(e),r&_e){if(e.stateNode===null)throw new Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");var y=e.stateNode,h=e.memoizedProps,S=a!==null?a.memoizedProps:h;try{Ex(y,S,h)}catch(ne){Ie(e,e.return,ne)}}return}case W:{if(_a(t,e),Qa(e),r&_e&&a!==null){var E=a.memoizedState;if(E.isDehydrated)try{Gx(t.containerInfo)}catch(ne){Ie(e,e.return,ne)}}return}case oe:{_a(t,e),Qa(e);return}case se:{_a(t,e),Qa(e);var T=e.child;if(T.flags&pi){var F=T.stateNode,X=T.memoizedState,Q=X!==null;if(F.isHidden=Q,Q){var we=T.alternate!==null&&T.alternate.memoizedState!==null;we||$_()}}if(r&_e){try{u_(e)}catch(ne){Ie(e,e.return,ne)}hb(e)}return}case ge:{var be=a!==null&&a.memoizedState!==null;if(e.mode&Te){var g=Gt;Gt=g||be,_a(t,e),Gt=g}else _a(t,e);if(Qa(e),r&pi){var x=e.stateNode,b=e.memoizedState,M=b!==null,V=e;if(x.isHidden=M,M&&!be&&(V.mode&Te)!==ee){$=V;for(var k=V.child;k!==null;)$=k,f_(k),k=k.sibling}n_(V,M)}return}case Xe:{_a(t,e),Qa(e),r&_e&&hb(e);return}case Un:return;default:{_a(t,e),Qa(e);return}}}function Qa(e){var t=e.flags;if(t&gt){try{i_(e)}catch(n){Ie(e,e.return,n)}e.flags&=~gt}t&ur&&(e.flags&=~ur)}function c_(e,t,n){Ul=n,jl=t,$=e,yb(e,t,n),Ul=null,jl=null}function yb(e,t,n){for(var a=(e.mode&Te)!==ee;$!==null;){var r=$,i=r.child;if(r.tag===ge&&a){var l=r.memoizedState!==null,u=l||Mc;if(u){wp(e,t,n);continue}else{var o=r.alternate,c=o!==null&&o.memoizedState!==null,f=c||Gt,y=Mc,h=Gt;Mc=u,Gt=f,Gt&&!h&&($=r,d_(r));for(var S=i;S!==null;)$=S,yb(S,t,n),S=S.sibling;$=r,Mc=y,Gt=h,wp(e,t,n);continue}}(r.subtreeFlags&hu)!==Z&&i!==null?(i.return=r,$=i):wp(e,t,n)}}function wp(e,t,n){for(;$!==null;){var a=$;if((a.flags&hu)!==Z){var r=a.alternate;lt(a);try{e_(t,r,a,n)}catch(l){Ie(a,a.return,l)}jt()}if(a===e){$=null;return}var i=a.sibling;if(i!==null){i.return=a.return,$=i;return}$=a.return}}function f_(e){for(;$!==null;){var t=$,n=t.child;switch(t.tag){case ae:case Se:case Je:case De:{if(t.mode&$e)try{Ga(),wa(xt,t,t.return)}finally{qa(t)}else wa(xt,t,t.return);break}case B:{kl(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Rp(t,t.return,a);break}case G:{kl(t,t.return);break}case ge:{var r=t.memoizedState!==null;if(r){gb(e);continue}break}}n!==null?(n.return=t,$=n):gb(e)}}function gb(e){for(;$!==null;){var t=$;if(t===e){$=null;return}var n=t.sibling;if(n!==null){n.return=t.return,$=n;return}$=t.return}}function d_(e){for(;$!==null;){var t=$,n=t.child;if(t.tag===ge){var a=t.memoizedState!==null;if(a){bb(e);continue}}n!==null?(n.return=t,$=n):bb(e)}}function bb(e){for(;$!==null;){var t=$;lt(t);try{t_(t)}catch(a){Ie(t,t.return,a)}if(jt(),t===e){$=null;return}var n=t.sibling;if(n!==null){n.return=t.return,$=n;return}$=t.return}}function v_(e,t,n,a){$=t,p_(t,e,n,a)}function p_(e,t,n,a){for(;$!==null;){var r=$,i=r.child;(r.subtreeFlags&nl)!==Z&&i!==null?(i.return=r,$=i):h_(e,t,n,a)}}function h_(e,t,n,a){for(;$!==null;){var r=$;if((r.flags&ma)!==Z){lt(r);try{m_(t,r,n,a)}catch(l){Ie(r,r.return,l)}jt()}if(r===e){$=null;return}var i=r.sibling;if(i!==null){i.return=r.return,$=i;return}$=r.return}}function m_(e,t,n,a){switch(t.tag){case ae:case Se:case De:{if(t.mode&$e){ap();try{Kr(Pt|Tt,t)}finally{np(t)}}else Kr(Pt|Tt,t);break}}}function y_(e){$=e,g_()}function g_(){for(;$!==null;){var e=$,t=e.child;if(($.flags&di)!==Z){var n=e.deletions;if(n!==null){for(var a=0;a<n.length;a++){var r=n[a];$=r,E_(r,e)}{var i=e.alternate;if(i!==null){var l=i.child;if(l!==null){i.child=null;do{var u=l.sibling;l.sibling=null,l=u}while(l!==null)}}}$=e}}(e.subtreeFlags&nl)!==Z&&t!==null?(t.return=e,$=t):b_()}}function b_(){for(;$!==null;){var e=$;(e.flags&ma)!==Z&&(lt(e),S_(e),jt());var t=e.sibling;if(t!==null){t.return=e.return,$=t;return}$=e.return}}function S_(e){switch(e.tag){case ae:case Se:case De:{e.mode&$e?(ap(),wa(Pt|Tt,e,e.return),np(e)):wa(Pt|Tt,e,e.return);break}}}function E_(e,t){for(;$!==null;){var n=$;lt(n),R_(n,t),jt();var a=n.child;a!==null?(a.return=n,$=a):C_(e)}}function C_(e){for(;$!==null;){var t=$,n=t.sibling,a=t.return;if(fb(t),t===e){$=null;return}if(n!==null){n.return=a,$=n;return}$=a}}function R_(e,t){switch(e.tag){case ae:case Se:case De:{e.mode&$e?(ap(),wa(Pt,e,t),np(e)):wa(Pt,e,t);break}}}function T_(e){switch(e.tag){case ae:case Se:case De:{try{Kr(xt|Tt,e)}catch(n){Ie(e,e.return,n)}break}case B:{var t=e.stateNode;try{t.componentDidMount()}catch(n){Ie(e,e.return,n)}break}}}function x_(e){switch(e.tag){case ae:case Se:case De:{try{Kr(Pt|Tt,e)}catch(t){Ie(e,e.return,t)}break}}}function w_(e){switch(e.tag){case ae:case Se:case De:{try{wa(xt|Tt,e,e.return)}catch(n){Ie(e,e.return,n)}break}case B:{var t=e.stateNode;typeof t.componentWillUnmount=="function"&&Rp(e,e.return,t);break}}}function D_(e){switch(e.tag){case ae:case Se:case De:try{wa(Pt|Tt,e,e.return)}catch(t){Ie(e,e.return,t)}}}if(typeof Symbol=="function"&&Symbol.for){var So=Symbol.for;So("selector.component"),So("selector.has_pseudo_class"),So("selector.role"),So("selector.test_id"),So("selector.text")}var __=[];function O_(){__.forEach(function(e){return e()})}var N_=J.ReactCurrentActQueue;function M_(e){{var t=typeof IS_REACT_ACT_ENVIRONMENT<"u"?IS_REACT_ACT_ENVIRONMENT:void 0,n=typeof jest<"u";return n&&t!==!1}}function Sb(){{var e=typeof IS_REACT_ACT_ENVIRONMENT<"u"?IS_REACT_ACT_ENVIRONMENT:void 0;return!e&&N_.current!==null&&d("The current testing environment is not configured to support act(...)"),e}}var A_=Math.ceil,Dp=J.ReactCurrentDispatcher,_p=J.ReactCurrentOwner,Wt=J.ReactCurrentBatchConfig,Oa=J.ReactCurrentActQueue,_t=0,Eb=1,It=2,oa=4,Sr=0,Eo=1,ji=2,Lc=3,Co=4,Cb=5,Op=6,xe=_t,vn=null,ut=null,Ot=_,Wa=_,Np=Yr(_),Nt=Sr,Ro=null,Uc=_,To=_,jc=_,xo=null,Mn=null,Mp=0,Rb=500,Tb=1/0,L_=500,Er=null;function wo(){Tb=kt()+L_}function xb(){return Tb}var kc=!1,Ap=null,zl=null,ki=!1,Zr=null,Do=_,Lp=[],Up=null,U_=50,_o=0,jp=null,kp=!1,zc=!1,j_=50,Hl=0,Hc=null,Oo=Ke,Fc=_,wb=!1;function Vc(){return vn}function pn(){return(xe&(It|oa))!==_t?kt():(Oo!==Ke||(Oo=kt()),Oo)}function ei(e){var t=e.mode;if((t&Te)===ee)return ie;if((xe&It)!==_t&&Ot!==_)return Cu(Ot);var n=Mw()!==Nw;if(n){if(Wt.transition!==null){var a=Wt.transition;a._updatedFibers||(a._updatedFibers=new Set),a._updatedFibers.add(e)}return Fc===Ht&&(Fc=lm()),Fc}var r=ga();if(r!==Ht)return r;var i=hx();return i}function k_(e){var t=e.mode;return(t&Te)===ee?ie:FC()}function Mt(e,t,n,a){lO(),wb&&d("useInsertionEffect must not schedule updates."),kp&&(zc=!0),Ru(e,n,a),(xe&It)!==_&&e===vn?sO(t):(ya&&sm(e,t,n),cO(t),e===vn&&((xe&It)===_t&&(To=he(To,n)),Nt===Co&&ti(e,Ot)),An(e,a),n===ie&&xe===_t&&(t.mode&Te)===ee&&!Oa.isBatchingLegacy&&(wo(),xy()))}function z_(e,t,n){var a=e.current;a.lanes=t,Ru(e,t,n),An(e,n)}function H_(e){return(xe&It)!==_t}function An(e,t){var n=e.callbackNode;LC(e,t);var a=us(e,e===vn?Ot:_);if(a===_){n!==null&&Yb(n),e.callbackNode=null,e.callbackPriority=Ht;return}var r=Si(a),i=e.callbackPriority;if(i===r&&!(Oa.current!==null&&n!==$p)){n==null&&i!==ie&&d("Expected scheduled callback to exist. This error is likely caused by a bug in React. Please file an issue.");return}n!=null&&Yb(n);var l;if(r===ie)e.tag===$r?(Oa.isBatchingLegacy!==null&&(Oa.didScheduleLegacyUpdate=!0),vw(Ob.bind(null,e))):Ty(Ob.bind(null,e)),Oa.current!==null?Oa.current.push(Pr):yx(function(){(xe&(It|oa))===_t&&Pr()}),l=null;else{var u;switch(dm(a)){case Pn:u=as;break;case fr:u=Af;break;case dr:u=yi;break;case cs:u=Lf;break;default:u=yi;break}l=Pp(u,Db.bind(null,e))}e.callbackPriority=r,e.callbackNode=l}function Db(e,t){if(lD(),Oo=Ke,Fc=_,(xe&(It|oa))!==_t)throw new Error("Should not already be working.");var n=e.callbackNode,a=Rr();if(a&&e.callbackNode!==n)return null;var r=us(e,e===vn?Ot:_);if(r===_)return null;var i=!os(e,r)&&!HC(e,r)&&!t,l=i?W_(e,r):Yc(e,r);if(l!==Sr){if(l===ji){var u=td(e);u!==_&&(r=u,l=zp(e,u))}if(l===Eo){var o=Ro;throw zi(e,_),ti(e,r),An(e,kt()),o}if(l===Op)ti(e,r);else{var c=!os(e,r),f=e.current.alternate;if(c&&!V_(f)){if(l=Yc(e,r),l===ji){var y=td(e);y!==_&&(r=y,l=zp(e,y))}if(l===Eo){var h=Ro;throw zi(e,_),ti(e,r),An(e,kt()),h}}e.finishedWork=f,e.finishedLanes=r,F_(e,l,r)}}return An(e,kt()),e.callbackNode===n?Db.bind(null,e):null}function zp(e,t){var n=xo;if(fs(e)){var a=zi(e,t);a.flags|=lr,lw(e.containerInfo)}var r=Yc(e,t);if(r!==ji){var i=Mn;Mn=n,i!==null&&_b(i)}return r}function _b(e){Mn===null?Mn=e:Mn.push.apply(Mn,e)}function F_(e,t,n){switch(t){case Sr:case Eo:throw new Error("Root did not complete. This is a bug in React.");case ji:{Hi(e,Mn,Er);break}case Lc:{if(ti(e,n),rm(n)&&!$b()){var a=Mp+Rb-kt();if(a>10){var r=us(e,_);if(r!==_)break;var i=e.suspendedLanes;if(!ol(i,n)){pn(),om(e,i);break}e.timeoutHandle=Ud(Hi.bind(null,e,Mn,Er),a);break}}Hi(e,Mn,Er);break}case Co:{if(ti(e,n),zC(n))break;if(!$b()){var l=MC(e,n),u=l,o=kt()-u,c=iO(o)-o;if(c>10){e.timeoutHandle=Ud(Hi.bind(null,e,Mn,Er),c);break}}Hi(e,Mn,Er);break}case Cb:{Hi(e,Mn,Er);break}default:throw new Error("Unknown root exit status.")}}function V_(e){for(var t=e;;){if(t.flags&ts){var n=t.updateQueue;if(n!==null){var a=n.stores;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r],l=i.getSnapshot,u=i.value;try{if(!Gn(l(),u))return!1}catch{return!1}}}}var o=t.child;if(t.subtreeFlags&ts&&o!==null){o.return=t,t=o;continue}if(t===e)return!0;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}return!0}function ti(e,t){t=ss(t,jc),t=ss(t,To),BC(e,t)}function Ob(e){if(uD(),(xe&(It|oa))!==_t)throw new Error("Should not already be working.");Rr();var t=us(e,_);if(!$n(t,ie))return An(e,kt()),null;var n=Yc(e,t);if(e.tag!==$r&&n===ji){var a=td(e);a!==_&&(t=a,n=zp(e,a))}if(n===Eo){var r=Ro;throw zi(e,_),ti(e,t),An(e,kt()),r}if(n===Op)throw new Error("Root did not complete. This is a bug in React.");var i=e.current.alternate;return e.finishedWork=i,e.finishedLanes=t,Hi(e,Mn,Er),An(e,kt()),null}function B_(e,t){t!==_&&(id(e,he(t,ie)),An(e,kt()),(xe&(It|oa))===_t&&(wo(),Pr()))}function Hp(e,t){var n=xe;xe|=Eb;try{return e(t)}finally{xe=n,xe===_t&&!Oa.isBatchingLegacy&&(wo(),xy())}}function Y_(e,t,n,a,r){var i=ga(),l=Wt.transition;try{return Wt.transition=null,Ft(Pn),e(t,n,a,r)}finally{Ft(i),Wt.transition=l,xe===_t&&wo()}}function Cr(e){Zr!==null&&Zr.tag===$r&&(xe&(It|oa))===_t&&Rr();var t=xe;xe|=Eb;var n=Wt.transition,a=ga();try{return Wt.transition=null,Ft(Pn),e?e():void 0}finally{Ft(a),Wt.transition=n,xe=t,(xe&(It|oa))===_t&&Pr()}}function Nb(){return(xe&(It|oa))!==_t}function Bc(e,t){rn(Np,Wa,e),Wa=he(Wa,t)}function Fp(e){Wa=Np.current,an(Np,e)}function zi(e,t){e.finishedWork=null,e.finishedLanes=_;var n=e.timeoutHandle;if(n!==jd&&(e.timeoutHandle=jd,mx(n)),ut!==null)for(var a=ut.return;a!==null;){var r=a.alternate;ib(r,a),a=a.return}vn=e;var i=Fi(e.current,null);return ut=i,Ot=Wa=t,Nt=Sr,Ro=null,Uc=_,To=_,jc=_,xo=null,Mn=null,jw(),Ea.discardPendingWarnings(),i}function Mb(e,t){do{var n=ut;try{if(Is(),ug(),jt(),_p.current=null,n===null||n.return===null){Nt=Eo,Ro=t,ut=null;return}if(fa&&n.mode&$e&&Dc(n,!0),Ma)if(rl(),t!==null&&typeof t=="object"&&typeof t.then=="function"){var a=t;bC(n,a,Ot)}else gC(n,t,Ot);dD(e,n.return,n,t,Ot),jb(n)}catch(r){t=r,ut===n&&n!==null?(n=n.return,ut=n):n=ut;continue}return}while(!0)}function Ab(){var e=Dp.current;return Dp.current=Cc,e===null?Cc:e}function Lb(e){Dp.current=e}function $_(){Mp=kt()}function No(e){Uc=he(e,Uc)}function P_(){Nt===Sr&&(Nt=Lc)}function Vp(){(Nt===Sr||Nt===Lc||Nt===ji)&&(Nt=Co),vn!==null&&(nd(Uc)||nd(To))&&ti(vn,Ot)}function q_(e){Nt!==Co&&(Nt=ji),xo===null?xo=[e]:xo.push(e)}function G_(){return Nt===Sr}function Yc(e,t){var n=xe;xe|=It;var a=Ab();if(vn!==e||Ot!==t){if(ya){var r=e.memoizedUpdaters;r.size>0&&(Mo(e,Ot),r.clear()),cm(e,t)}Er=fm(),zi(e,t)}Zh(t);do try{Q_();break}catch(i){Mb(e,i)}while(!0);if(Is(),xe=n,Lb(a),ut!==null)throw new Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");return em(),vn=null,Ot=_,Nt}function Q_(){for(;ut!==null;)Ub(ut)}function W_(e,t){var n=xe;xe|=It;var a=Ab();if(vn!==e||Ot!==t){if(ya){var r=e.memoizedUpdaters;r.size>0&&(Mo(e,Ot),r.clear()),cm(e,t)}Er=fm(),wo(),zi(e,t)}Zh(t);do try{I_();break}catch(i){Mb(e,i)}while(!0);return Is(),Lb(a),xe=n,ut!==null?(TC(),Sr):(em(),vn=null,Ot=_,Nt)}function I_(){for(;ut!==null&&!JE();)Ub(ut)}function Ub(e){var t=e.alternate;lt(e);var n;(e.mode&$e)!==ee?(tp(e),n=Bp(t,e,Wa),Dc(e,!0)):n=Bp(t,e,Wa),jt(),e.memoizedProps=e.pendingProps,n===null?jb(e):ut=n,_p.current=null}function jb(e){var t=e;do{var n=t.alternate,a=t.return;if((t.flags&pu)===Z){lt(t);var r=void 0;if((t.mode&$e)===ee?r=rb(n,t,Wa):(tp(t),r=rb(n,t,Wa),Dc(t,!1)),jt(),r!==null){ut=r;return}}else{var i=PD(n,t);if(i!==null){i.flags&=GE,ut=i;return}if((t.mode&$e)!==ee){Dc(t,!1);for(var l=t.actualDuration,u=t.child;u!==null;)l+=u.actualDuration,u=u.sibling;t.actualDuration=l}if(a!==null)a.flags|=pu,a.subtreeFlags=Z,a.deletions=null;else{Nt=Op,ut=null;return}}var o=t.sibling;if(o!==null){ut=o;return}t=a,ut=t}while(t!==null);Nt===Sr&&(Nt=Cb)}function Hi(e,t,n){var a=ga(),r=Wt.transition;try{Wt.transition=null,Ft(Pn),X_(e,t,n,a)}finally{Wt.transition=r,Ft(a)}return null}function X_(e,t,n,a){do Rr();while(Zr!==null);if(uO(),(xe&(It|oa))!==_t)throw new Error("Should not already be working.");var r=e.finishedWork,i=e.finishedLanes;if(fC(i),r===null)return Xh(),null;if(i===_&&d("root.finishedLanes should not be empty during a commit. This is a bug in React."),e.finishedWork=null,e.finishedLanes=_,r===e.current)throw new Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");e.callbackNode=null,e.callbackPriority=Ht;var l=he(r.lanes,r.childLanes);YC(e,l),e===vn&&(vn=null,ut=null,Ot=_),((r.subtreeFlags&nl)!==Z||(r.flags&nl)!==Z)&&(ki||(ki=!0,Up=n,Pp(yi,function(){return Rr(),null})));var u=(r.subtreeFlags&(Nf|Mf|hu|nl))!==Z,o=(r.flags&(Nf|Mf|hu|nl))!==Z;if(u||o){var c=Wt.transition;Wt.transition=null;var f=ga();Ft(Pn);var y=xe;xe|=oa,_p.current=null,ID(e,r),Ag(),s_(e,r,i),sx(e.containerInfo),e.current=r,SC(i),c_(r,e,i),EC(),ZE(),xe=y,Ft(f),Wt.transition=c}else e.current=r,Ag();var h=ki;if(ki?(ki=!1,Zr=e,Do=i):(Hl=0,Hc=null),l=e.pendingLanes,l===_&&(zl=null),h||Fb(e.current,!1),lC(r.stateNode,a),ya&&e.memoizedUpdaters.clear(),O_(),An(e,kt()),t!==null)for(var S=e.onRecoverableError,E=0;E<t.length;E++){var T=t[E],F=T.stack,X=T.digest;S(T.value,{componentStack:F,digest:X})}if(kc){kc=!1;var Q=Ap;throw Ap=null,Q}return $n(Do,ie)&&e.tag!==$r&&Rr(),l=e.pendingLanes,$n(l,ie)?(iD(),e===jp?_o++:(_o=0,jp=e)):_o=0,Pr(),Xh(),null}function Rr(){if(Zr!==null){var e=dm(Do),t=GC(dr,e),n=Wt.transition,a=ga();try{return Wt.transition=null,Ft(t),J_()}finally{Ft(a),Wt.transition=n}}return!1}function K_(e){Lp.push(e),ki||(ki=!0,Pp(yi,function(){return Rr(),null}))}function J_(){if(Zr===null)return!1;var e=Up;Up=null;var t=Zr,n=Do;if(Zr=null,Do=_,(xe&(It|oa))!==_t)throw new Error("Cannot flush passive effects while already rendering.");kp=!0,zc=!1,CC(n);var a=xe;xe|=oa,y_(t.current),v_(t,t.current,n,e);{var r=Lp;Lp=[];for(var i=0;i<r.length;i++){var l=r[i];ZD(t,l)}}RC(),Fb(t.current,!0),xe=a,Pr(),zc?t===Hc?Hl++:(Hl=0,Hc=t):Hl=0,kp=!1,zc=!1,uC(t);{var u=t.current.stateNode;u.effectDuration=0,u.passiveEffectDuration=0}return!0}function kb(e){return zl!==null&&zl.has(e)}function Z_(e){zl===null?zl=new Set([e]):zl.add(e)}function eO(e){kc||(kc=!0,Ap=e)}var tO=eO;function zb(e,t,n){var a=Li(n,t),r=Ug(e,a,ie),i=Gr(e,r,ie),l=pn();i!==null&&(Ru(i,ie,l),An(i,l))}function Ie(e,t,n){if(GD(n),Ao(!1),e.tag===W){zb(e,e,n);return}var a=null;for(a=t;a!==null;){if(a.tag===W){zb(a,e,n);return}else if(a.tag===B){var r=a.type,i=a.stateNode;if(typeof r.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&!kb(i)){var l=Li(n,e),u=up(a,l,ie),o=Gr(a,u,ie),c=pn();o!==null&&(Ru(o,ie,c),An(o,c));return}}a=a.return}d(`Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Likely causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.

Error message:

%s`,n)}function nO(e,t,n){var a=e.pingCache;a!==null&&a.delete(t);var r=pn();om(e,n),fO(e),vn===e&&ol(Ot,n)&&(Nt===Co||Nt===Lc&&rm(Ot)&&kt()-Mp<Rb?zi(e,_):jc=he(jc,n)),An(e,r)}function Hb(e,t){t===Ht&&(t=k_(e));var n=pn(),a=On(e,t);a!==null&&(Ru(a,t,n),An(a,n))}function aO(e){var t=e.memoizedState,n=Ht;t!==null&&(n=t.retryLane),Hb(e,n)}function rO(e,t){var n=Ht,a;switch(e.tag){case se:a=e.stateNode;var r=e.memoizedState;r!==null&&(n=r.retryLane);break;case Xe:a=e.stateNode;break;default:throw new Error("Pinged unknown suspense boundary type. This is probably a bug in React.")}a!==null&&a.delete(t),Hb(e,n)}function iO(e){return e<120?120:e<480?480:e<1080?1080:e<1920?1920:e<3e3?3e3:e<4320?4320:A_(e/1960)*1960}function lO(){if(_o>U_)throw _o=0,jp=null,new Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");Hl>j_&&(Hl=0,Hc=null,d("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render."))}function uO(){Ea.flushLegacyContextWarning(),Ea.flushPendingUnsafeLifecycleWarnings()}function Fb(e,t){lt(e),$c(e,or,w_),t&&$c(e,ns,D_),$c(e,or,T_),t&&$c(e,ns,x_),jt()}function $c(e,t,n){for(var a=e,r=null;a!==null;){var i=a.subtreeFlags&t;a!==r&&a.child!==null&&i!==Z?a=a.child:((a.flags&t)!==Z&&n(a),a.sibling!==null?a=a.sibling:a=r=a.return)}}var Pc=null;function Vb(e){{if((xe&It)!==_t||!(e.mode&Te))return;var t=e.tag;if(t!==q&&t!==W&&t!==B&&t!==ae&&t!==Se&&t!==Je&&t!==De)return;var n=de(e)||"ReactComponent";if(Pc!==null){if(Pc.has(n))return;Pc.add(n)}else Pc=new Set([n]);var a=tn;try{lt(e),d("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.")}finally{a?lt(e):jt()}}}var Bp;{var oO=null;Bp=function(e,t,n){var a=Wb(oO,t);try{return Zg(e,t,n)}catch(i){if(Ew()||i!==null&&typeof i=="object"&&typeof i.then=="function")throw i;if(Is(),ug(),ib(e,t),Wb(t,a),t.mode&$e&&tp(t),Tf(null,Zg,null,e,t,n),YE()){var r=xf();typeof r=="object"&&r!==null&&r._suppressLogging&&typeof i=="object"&&i!==null&&!i._suppressLogging&&(i._suppressLogging=!0)}throw i}}}var Bb=!1,Yp;Yp=new Set;function sO(e){if(si&&!nD())switch(e.tag){case ae:case Se:case De:{var t=ut&&de(ut)||"Unknown",n=t;if(!Yp.has(n)){Yp.add(n);var a=de(e)||"Unknown";d("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render",a,t,t)}break}case B:{Bb||(d("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),Bb=!0);break}}}function Mo(e,t){if(ya){var n=e.memoizedUpdaters;n.forEach(function(a){sm(e,a,t)})}}var $p={};function Pp(e,t){{var n=Oa.current;return n!==null?(n.push(t),$p):Ih(e,t)}}function Yb(e){if(e!==$p)return KE(e)}function $b(){return Oa.current!==null}function cO(e){{if(e.mode&Te){if(!Sb())return}else if(!M_()||xe!==_t||e.tag!==ae&&e.tag!==Se&&e.tag!==De)return;if(Oa.current===null){var t=tn;try{lt(e),d(`An update to %s inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`,de(e))}finally{t?lt(e):jt()}}}}function fO(e){e.tag!==$r&&Sb()&&Oa.current===null&&d(`A suspended resource finished loading inside a test, but the event was not wrapped in act(...).

When testing, code that resolves suspended data should be wrapped into act(...):

act(() => {
  /* finish loading suspended data */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`)}function Ao(e){wb=e}var sa=null,Fl=null,dO=function(e){sa=e};function Vl(e){{if(sa===null)return e;var t=sa(e);return t===void 0?e:t.current}}function qp(e){return Vl(e)}function Gp(e){{if(sa===null)return e;var t=sa(e);if(t===void 0){if(e!=null&&typeof e.render=="function"){var n=Vl(e.render);if(e.render!==n){var a={$$typeof:Y,render:n};return e.displayName!==void 0&&(a.displayName=e.displayName),a}}return e}return t.current}}function Pb(e,t){{if(sa===null)return!1;var n=e.elementType,a=t.type,r=!1,i=typeof a=="object"&&a!==null?a.$$typeof:null;switch(e.tag){case B:{typeof a=="function"&&(r=!0);break}case ae:{(typeof a=="function"||i===K)&&(r=!0);break}case Se:{(i===Y||i===K)&&(r=!0);break}case Je:case De:{(i===pe||i===K)&&(r=!0);break}default:return!1}if(r){var l=sa(n);if(l!==void 0&&l===sa(a))return!0}return!1}}function qb(e){{if(sa===null||typeof WeakSet!="function")return;Fl===null&&(Fl=new WeakSet),Fl.add(e)}}var vO=function(e,t){{if(sa===null)return;var n=t.staleFamilies,a=t.updatedFamilies;Rr(),Cr(function(){Qp(e.current,a,n)})}},pO=function(e,t){{if(e.context!==Qn)return;Rr(),Cr(function(){Lo(t,e,null,null)})}};function Qp(e,t,n){{var a=e.alternate,r=e.child,i=e.sibling,l=e.tag,u=e.type,o=null;switch(l){case ae:case De:case B:o=u;break;case Se:o=u.render;break}if(sa===null)throw new Error("Expected resolveFamily to be set during hot reload.");var c=!1,f=!1;if(o!==null){var y=sa(o);y!==void 0&&(n.has(y)?f=!0:t.has(y)&&(l===B?f=!0:c=!0))}if(Fl!==null&&(Fl.has(e)||a!==null&&Fl.has(a))&&(f=!0),f&&(e._debugNeedsRemount=!0),f||c){var h=On(e,ie);h!==null&&Mt(h,e,ie,Ke)}r!==null&&!f&&Qp(r,t,n),i!==null&&Qp(i,t,n)}}var hO=function(e,t){{var n=new Set,a=new Set(t.map(function(r){return r.current}));return Wp(e.current,a,n),n}};function Wp(e,t,n){{var a=e.child,r=e.sibling,i=e.tag,l=e.type,u=null;switch(i){case ae:case De:case B:u=l;break;case Se:u=l.render;break}var o=!1;u!==null&&t.has(u)&&(o=!0),o?mO(e,n):a!==null&&Wp(a,t,n),r!==null&&Wp(r,t,n)}}function mO(e,t){{var n=yO(e,t);if(n)return;for(var a=e;;){switch(a.tag){case G:t.add(a.stateNode);return;case oe:t.add(a.stateNode.containerInfo);return;case W:t.add(a.stateNode.containerInfo);return}if(a.return===null)throw new Error("Expected to reach root first.");a=a.return}}}function yO(e,t){for(var n=e,a=!1;;){if(n.tag===G)a=!0,t.add(n.stateNode);else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)return a;for(;n.sibling===null;){if(n.return===null||n.return===e)return a;n=n.return}n.sibling.return=n.return,n=n.sibling}return!1}var Ip;{Ip=!1;try{var Gb=Object.preventExtensions({})}catch{Ip=!0}}function gO(e,t,n,a){this.tag=e,this.key=n,this.elementType=null,this.type=null,this.stateNode=null,this.return=null,this.child=null,this.sibling=null,this.index=0,this.ref=null,this.pendingProps=t,this.memoizedProps=null,this.updateQueue=null,this.memoizedState=null,this.dependencies=null,this.mode=a,this.flags=Z,this.subtreeFlags=Z,this.deletions=null,this.lanes=_,this.childLanes=_,this.alternate=null,this.actualDuration=Number.NaN,this.actualStartTime=Number.NaN,this.selfBaseDuration=Number.NaN,this.treeBaseDuration=Number.NaN,this.actualDuration=0,this.actualStartTime=-1,this.selfBaseDuration=0,this.treeBaseDuration=0,this._debugSource=null,this._debugOwner=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,!Ip&&typeof Object.preventExtensions=="function"&&Object.preventExtensions(this)}var Wn=function(e,t,n,a){return new gO(e,t,n,a)};function Xp(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function bO(e){return typeof e=="function"&&!Xp(e)&&e.defaultProps===void 0}function SO(e){if(typeof e=="function")return Xp(e)?B:ae;if(e!=null){var t=e.$$typeof;if(t===Y)return Se;if(t===pe)return Je}return q}function Fi(e,t){var n=e.alternate;n===null?(n=Wn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n._debugSource=e._debugSource,n._debugOwner=e._debugOwner,n._debugHookTypes=e._debugHookTypes,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=Z,n.subtreeFlags=Z,n.deletions=null,n.actualDuration=0,n.actualStartTime=-1),n.flags=e.flags&sr,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue;var a=e.dependencies;switch(n.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.selfBaseDuration=e.selfBaseDuration,n.treeBaseDuration=e.treeBaseDuration,n._debugNeedsRemount=e._debugNeedsRemount,n.tag){case q:case ae:case De:n.type=Vl(e.type);break;case B:n.type=qp(e.type);break;case Se:n.type=Gp(e.type);break}return n}function EO(e,t){e.flags&=sr|gt;var n=e.alternate;if(n===null)e.childLanes=_,e.lanes=t,e.child=null,e.subtreeFlags=Z,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null,e.selfBaseDuration=0,e.treeBaseDuration=0;else{e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=Z,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type;var a=n.dependencies;e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},e.selfBaseDuration=n.selfBaseDuration,e.treeBaseDuration=n.treeBaseDuration}return e}function CO(e,t,n){var a;return e===Bs?(a=Te,t===!0&&(a|=bt,a|=Ha)):a=ee,ya&&(a|=$e),Wn(W,null,null,a)}function Kp(e,t,n,a,r,i){var l=q,u=e;if(typeof e=="function")Xp(e)?(l=B,u=qp(u)):u=Vl(u);else if(typeof e=="string")l=G;else e:switch(e){case Ua:return ni(n.children,r,i,t);case li:l=Ln,r|=bt,(r&Te)!==ee&&(r|=Ha);break;case p:return RO(n,r,i,t);case Ee:return TO(n,r,i,t);case Oe:return xO(n,r,i,t);case nt:return Qb(n,r,i,t);case en:case Rt:case ja:case Yi:case tt:default:{if(typeof e=="object"&&e!==null)switch(e.$$typeof){case O:l=He;break e;case U:l=Kt;break e;case Y:l=Se,u=Gp(u);break e;case pe:l=Je;break e;case K:l=bn,u=null;break e}var o="";{(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(o+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var c=a?de(a):null;c&&(o+=`

Check the render method of \``+c+"`.")}throw new Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) "+("but got: "+(e==null?e:typeof e)+"."+o))}}var f=Wn(l,n,t,r);return f.elementType=e,f.type=u,f.lanes=i,f._debugOwner=a,f}function Jp(e,t,n){var a=null;a=e._owner;var r=e.type,i=e.key,l=e.props,u=Kp(r,i,l,a,t,n);return u._debugSource=e._source,u._debugOwner=e._owner,u}function ni(e,t,n,a){var r=Wn(gn,e,a,t);return r.lanes=n,r}function RO(e,t,n,a){typeof e.id!="string"&&d('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.',typeof e.id);var r=Wn(it,e,a,t|$e);return r.elementType=p,r.lanes=n,r.stateNode={effectDuration:0,passiveEffectDuration:0},r}function TO(e,t,n,a){var r=Wn(se,e,a,t);return r.elementType=Ee,r.lanes=n,r}function xO(e,t,n,a){var r=Wn(Xe,e,a,t);return r.elementType=Oe,r.lanes=n,r}function Qb(e,t,n,a){var r=Wn(ge,e,a,t);r.elementType=nt,r.lanes=n;var i={isHidden:!1};return r.stateNode=i,r}function Zp(e,t,n){var a=Wn(ye,e,null,t);return a.lanes=n,a}function wO(){var e=Wn(G,null,null,ee);return e.elementType="DELETED",e}function DO(e){var t=Wn(ht,null,null,ee);return t.stateNode=e,t}function eh(e,t,n){var a=e.children!==null?e.children:[],r=Wn(oe,a,e.key,t);return r.lanes=n,r.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},r}function Wb(e,t){return e===null&&(e=Wn(q,null,null,ee)),e.tag=t.tag,e.key=t.key,e.elementType=t.elementType,e.type=t.type,e.stateNode=t.stateNode,e.return=t.return,e.child=t.child,e.sibling=t.sibling,e.index=t.index,e.ref=t.ref,e.pendingProps=t.pendingProps,e.memoizedProps=t.memoizedProps,e.updateQueue=t.updateQueue,e.memoizedState=t.memoizedState,e.dependencies=t.dependencies,e.mode=t.mode,e.flags=t.flags,e.subtreeFlags=t.subtreeFlags,e.deletions=t.deletions,e.lanes=t.lanes,e.childLanes=t.childLanes,e.alternate=t.alternate,e.actualDuration=t.actualDuration,e.actualStartTime=t.actualStartTime,e.selfBaseDuration=t.selfBaseDuration,e.treeBaseDuration=t.treeBaseDuration,e._debugSource=t._debugSource,e._debugOwner=t._debugOwner,e._debugNeedsRemount=t._debugNeedsRemount,e._debugHookTypes=t._debugHookTypes,e}function _O(e,t,n,a,r){this.tag=t,this.containerInfo=e,this.pendingChildren=null,this.current=null,this.pingCache=null,this.finishedWork=null,this.timeoutHandle=jd,this.context=null,this.pendingContext=null,this.callbackNode=null,this.callbackPriority=Ht,this.eventTimes=rd(_),this.expirationTimes=rd(Ke),this.pendingLanes=_,this.suspendedLanes=_,this.pingedLanes=_,this.expiredLanes=_,this.mutableReadLanes=_,this.finishedLanes=_,this.entangledLanes=_,this.entanglements=rd(_),this.identifierPrefix=a,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null,this.effectDuration=0,this.passiveEffectDuration=0;{this.memoizedUpdaters=new Set;for(var i=this.pendingUpdatersLaneMap=[],l=0;l<jf;l++)i.push(new Set)}switch(t){case Bs:this._debugRootType=n?"hydrateRoot()":"createRoot()";break;case $r:this._debugRootType=n?"hydrate()":"render()";break}}function Ib(e,t,n,a,r,i,l,u,o,c){var f=new _O(e,t,n,u,o),y=CO(t,i);f.current=y,y.stateNode=f;{var h={element:a,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null};y.memoizedState=h}return ov(y),f}var th="18.2.0";function OO(e,t,n){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;return Jn(a),{$$typeof:ha,key:a==null?null:""+a,children:e,containerInfo:t,implementation:n}}var nh,ah;nh=!1,ah={};function Xb(e){if(!e)return Qn;var t=el(e),n=dw(t);if(t.tag===B){var a=t.type;if(Ba(a))return Cy(t,a,n)}return n}function NO(e,t){{var n=el(e);if(n===void 0){if(typeof e.render=="function")throw new Error("Unable to find node on an unmounted component.");var a=Object.keys(e).join(",");throw new Error("Argument appears to not be a ReactComponent. Keys: "+a)}var r=Gh(n);if(r===null)return null;if(r.mode&bt){var i=de(n)||"Component";if(!ah[i]){ah[i]=!0;var l=tn;try{lt(r),n.mode&bt?d("%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i):d("%s is deprecated in StrictMode. %s was passed an instance of %s which renders StrictMode children. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i)}finally{l?lt(l):jt()}}}return r.stateNode}}function Kb(e,t,n,a,r,i,l,u){var o=!1,c=null;return Ib(e,t,o,c,n,a,r,i,l)}function Jb(e,t,n,a,r,i,l,u,o,c){var f=!0,y=Ib(n,a,f,e,r,i,l,u,o);y.context=Xb(null);var h=y.current,S=pn(),E=ei(h),T=gr(S,E);return T.callback=t??null,Gr(h,T,E),z_(y,E,S),y}function Lo(e,t,n,a){iC(t,e);var r=t.current,i=pn(),l=ei(r);xC(l);var u=Xb(n);t.context===null?t.context=u:t.pendingContext=u,si&&tn!==null&&!nh&&(nh=!0,d(`Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.

Check the render method of %s.`,de(tn)||"Unknown"));var o=gr(i,l);o.payload={element:e},a=a===void 0?null:a,a!==null&&(typeof a!="function"&&d("render(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",a),o.callback=a);var c=Gr(r,o,l);return c!==null&&(Mt(c,r,l,i),ec(c,r,l)),l}function qc(e){var t=e.current;if(!t.child)return null;switch(t.child.tag){case G:return t.child.stateNode;default:return t.child.stateNode}}function MO(e){switch(e.tag){case W:{var t=e.stateNode;if(fs(t)){var n=UC(t);B_(t,n)}break}case se:{Cr(function(){var r=On(e,ie);if(r!==null){var i=pn();Mt(r,e,ie,i)}});var a=ie;rh(e,a);break}}}function Zb(e,t){var n=e.memoizedState;n!==null&&n.dehydrated!==null&&(n.retryLane=VC(n.retryLane,t))}function rh(e,t){Zb(e,t);var n=e.alternate;n&&Zb(n,t)}function AO(e){if(e.tag===se){var t=bu,n=On(e,t);if(n!==null){var a=pn();Mt(n,e,t,a)}rh(e,t)}}function LO(e){if(e.tag===se){var t=ei(e),n=On(e,t);if(n!==null){var a=pn();Mt(n,e,t,a)}rh(e,t)}}function eS(e){var t=XE(e);return t===null?null:t.stateNode}var tS=function(e){return null};function UO(e){return tS(e)}var nS=function(e){return!1};function jO(e){return nS(e)}var aS=null,rS=null,iS=null,lS=null,uS=null,oS=null,sS=null,cS=null,fS=null;{var dS=function(e,t,n){var a=t[n],r=Ue(e)?e.slice():Ce({},e);return n+1===t.length?(Ue(r)?r.splice(a,1):delete r[a],r):(r[a]=dS(e[a],t,n+1),r)},vS=function(e,t){return dS(e,t,0)},pS=function(e,t,n,a){var r=t[a],i=Ue(e)?e.slice():Ce({},e);if(a+1===t.length){var l=n[a];i[l]=i[r],Ue(i)?i.splice(r,1):delete i[r]}else i[r]=pS(e[r],t,n,a+1);return i},hS=function(e,t,n){if(t.length!==n.length){fe("copyWithRename() expects paths of the same length");return}else for(var a=0;a<n.length-1;a++)if(t[a]!==n[a]){fe("copyWithRename() expects paths to be the same except for the deepest key");return}return pS(e,t,n,0)},mS=function(e,t,n,a){if(n>=t.length)return a;var r=t[n],i=Ue(e)?e.slice():Ce({},e);return i[r]=mS(e[r],t,n+1,a),i},yS=function(e,t,n){return mS(e,t,0,n)},ih=function(e,t){for(var n=e.memoizedState;n!==null&&t>0;)n=n.next,t--;return n};aS=function(e,t,n,a){var r=ih(e,t);if(r!==null){var i=yS(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Ce({},e.memoizedProps);var l=On(e,ie);l!==null&&Mt(l,e,ie,Ke)}},rS=function(e,t,n){var a=ih(e,t);if(a!==null){var r=vS(a.memoizedState,n);a.memoizedState=r,a.baseState=r,e.memoizedProps=Ce({},e.memoizedProps);var i=On(e,ie);i!==null&&Mt(i,e,ie,Ke)}},iS=function(e,t,n,a){var r=ih(e,t);if(r!==null){var i=hS(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Ce({},e.memoizedProps);var l=On(e,ie);l!==null&&Mt(l,e,ie,Ke)}},lS=function(e,t,n){e.pendingProps=yS(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=On(e,ie);a!==null&&Mt(a,e,ie,Ke)},uS=function(e,t){e.pendingProps=vS(e.memoizedProps,t),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var n=On(e,ie);n!==null&&Mt(n,e,ie,Ke)},oS=function(e,t,n){e.pendingProps=hS(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=On(e,ie);a!==null&&Mt(a,e,ie,Ke)},sS=function(e){var t=On(e,ie);t!==null&&Mt(t,e,ie,Ke)},cS=function(e){tS=e},fS=function(e){nS=e}}function kO(e){var t=Gh(e);return t===null?null:t.stateNode}function zO(e){return null}function HO(){return tn}function FO(e){var t=e.findFiberByHostInstance,n=J.ReactCurrentDispatcher;return rC({bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:aS,overrideHookStateDeletePath:rS,overrideHookStateRenamePath:iS,overrideProps:lS,overridePropsDeletePath:uS,overridePropsRenamePath:oS,setErrorHandler:cS,setSuspenseHandler:fS,scheduleUpdate:sS,currentDispatcherRef:n,findHostInstanceByFiber:kO,findFiberByHostInstance:t||zO,findHostInstancesForRefresh:hO,scheduleRefresh:vO,scheduleRoot:pO,setRefreshHandler:dO,getCurrentFiber:HO,reconcilerVersion:th})}var gS=typeof reportError=="function"?reportError:function(e){console.error(e)};function lh(e){this._internalRoot=e}Gc.prototype.render=lh.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw new Error("Cannot update an unmounted root.");{typeof arguments[1]=="function"?d("render(...): does not support the second callback argument. To execute a side effect after rendering, declare it in a component body with useEffect()."):Qc(arguments[1])?d("You passed a container to the second argument of root.render(...). You don't need to pass it again since you already passed it to create the root."):typeof arguments[1]<"u"&&d("You passed a second argument to root.render(...) but it only accepts one argument.");var n=t.containerInfo;if(n.nodeType!==yt){var a=eS(t.current);a&&a.parentNode!==n&&d("render(...): It looks like the React-rendered content of the root container was removed without using React. This is not supported and will cause errors. Instead, call root.unmount() to empty a root's container.")}}Lo(e,t,null,null)},Gc.prototype.unmount=lh.prototype.unmount=function(){typeof arguments[0]=="function"&&d("unmount(...): does not support a callback argument. To execute a side effect after rendering, declare it in a component body with useEffect().");var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nb()&&d("Attempted to synchronously unmount a root while React was already rendering. React cannot finish unmounting the root until the current render has completed, which may lead to a race condition."),Cr(function(){Lo(null,e,null,null)}),yy(t)}};function VO(e,t){if(!Qc(e))throw new Error("createRoot(...): Target container is not a DOM element.");bS(e);var n=!1,a=!1,r="",i=gS;t!=null&&(t.hydrate?fe("hydrate through createRoot is deprecated. Use ReactDOMClient.hydrateRoot(container, <App />) instead."):typeof t=="object"&&t!==null&&t.$$typeof===La&&d(`You passed a JSX element to createRoot. You probably meant to call root.render instead. Example usage:

  let root = createRoot(domContainer);
  root.render(<App />);`),t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError),t.transitionCallbacks!==void 0&&t.transitionCallbacks);var l=Kb(e,Bs,null,n,a,r,i);Us(l.current,e);var u=e.nodeType===yt?e.parentNode:e;return Vu(u),new lh(l)}function Gc(e){this._internalRoot=e}function BO(e){e&&aR(e)}Gc.prototype.unstable_scheduleHydration=BO;function YO(e,t,n){if(!Qc(e))throw new Error("hydrateRoot(...): Target container is not a DOM element.");bS(e),t===void 0&&d("Must provide initial children as second argument to hydrateRoot. Example usage: hydrateRoot(domContainer, <App />)");var a=n??null,r=n!=null&&n.hydratedSources||null,i=!1,l=!1,u="",o=gS;n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError));var c=Jb(t,null,e,Bs,a,i,l,u,o);if(Us(c.current,e),Vu(e),r)for(var f=0;f<r.length;f++){var y=r[f];Xw(c,y)}return new Gc(c)}function Qc(e){return!!(e&&(e.nodeType===Dn||e.nodeType===ir||e.nodeType===vf||!At))}function Uo(e){return!!(e&&(e.nodeType===Dn||e.nodeType===ir||e.nodeType===vf||e.nodeType===yt&&e.nodeValue===" react-mount-point-unstable "))}function bS(e){e.nodeType===Dn&&e.tagName&&e.tagName.toUpperCase()==="BODY"&&d("createRoot(): Creating roots directly with document.body is discouraged, since its children are often manipulated by third-party scripts and browser extensions. This may lead to subtle reconciliation issues. Try using a container element created for your app."),Ku(e)&&(e._reactRootContainer?d("You are calling ReactDOMClient.createRoot() on a container that was previously passed to ReactDOM.render(). This is not supported."):d("You are calling ReactDOMClient.createRoot() on a container that has already been passed to createRoot() before. Instead, call root.render() on the existing root instead if you want to update it."))}var $O=J.ReactCurrentOwner,SS;SS=function(e){if(e._reactRootContainer&&e.nodeType!==yt){var t=eS(e._reactRootContainer.current);t&&t.parentNode!==e&&d("render(...): It looks like the React-rendered content of this container was removed without using React. This is not supported and will cause errors. Instead, call ReactDOM.unmountComponentAtNode to empty a container.")}var n=!!e._reactRootContainer,a=uh(e),r=!!(a&&Br(a));r&&!n&&d("render(...): Replacing React-rendered children with a new root component. If you intended to update the children of this node, you should instead have the existing children update their state and render the new components instead of calling ReactDOM.render."),e.nodeType===Dn&&e.tagName&&e.tagName.toUpperCase()==="BODY"&&d("render(): Rendering components directly into document.body is discouraged, since its children are often manipulated by third-party scripts and browser extensions. This may lead to subtle reconciliation issues. Try rendering into a container element created for your app.")};function uh(e){return e?e.nodeType===ir?e.documentElement:e.firstChild:null}function ES(){}function PO(e,t,n,a,r){if(r){if(typeof a=="function"){var i=a;a=function(){var h=qc(l);i.call(h)}}var l=Jb(t,a,e,$r,null,!1,!1,"",ES);e._reactRootContainer=l,Us(l.current,e);var u=e.nodeType===yt?e.parentNode:e;return Vu(u),Cr(),l}else{for(var o;o=e.lastChild;)e.removeChild(o);if(typeof a=="function"){var c=a;a=function(){var h=qc(f);c.call(h)}}var f=Kb(e,$r,null,!1,!1,"",ES);e._reactRootContainer=f,Us(f.current,e);var y=e.nodeType===yt?e.parentNode:e;return Vu(y),Cr(function(){Lo(t,f,n,a)}),f}}function qO(e,t){e!==null&&typeof e!="function"&&d("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e)}function Wc(e,t,n,a,r){SS(n),qO(r===void 0?null:r,"render");var i=n._reactRootContainer,l;if(!i)l=PO(n,t,e,r,a);else{if(l=i,typeof r=="function"){var u=r;r=function(){var o=qc(l);u.call(o)}}Lo(t,l,e,r)}return qc(l)}function GO(e){{var t=$O.current;if(t!==null&&t.stateNode!==null){var n=t.stateNode._warnedAboutRefsInRender;n||d("%s is accessing findDOMNode inside its render(). render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",Le(t.type)||"A component"),t.stateNode._warnedAboutRefsInRender=!0}}return e==null?null:e.nodeType===Dn?e:NO(e,"findDOMNode")}function QO(e,t,n){if(d("ReactDOM.hydrate is no longer supported in React 18. Use hydrateRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot"),!Uo(t))throw new Error("Target container is not a DOM element.");{var a=Ku(t)&&t._reactRootContainer===void 0;a&&d("You are calling ReactDOM.hydrate() on a container that was previously passed to ReactDOMClient.createRoot(). This is not supported. Did you mean to call hydrateRoot(container, element)?")}return Wc(null,e,t,!0,n)}function WO(e,t,n){if(d("ReactDOM.render is no longer supported in React 18. Use createRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot"),!Uo(t))throw new Error("Target container is not a DOM element.");{var a=Ku(t)&&t._reactRootContainer===void 0;a&&d("You are calling ReactDOM.render() on a container that was previously passed to ReactDOMClient.createRoot(). This is not supported. Did you mean to call root.render(element)?")}return Wc(null,e,t,!1,n)}function IO(e,t,n,a){if(d("ReactDOM.unstable_renderSubtreeIntoContainer() is no longer supported in React 18. Consider using a portal instead. Until you switch to the createRoot API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot"),!Uo(n))throw new Error("Target container is not a DOM element.");if(e==null||!$E(e))throw new Error("parentComponent must be a valid React Component");return Wc(e,t,n,!1,a)}function XO(e){if(!Uo(e))throw new Error("unmountComponentAtNode(...): Target container is not a DOM element.");{var t=Ku(e)&&e._reactRootContainer===void 0;t&&d("You are calling ReactDOM.unmountComponentAtNode() on a container that was previously passed to ReactDOMClient.createRoot(). This is not supported. Did you mean to call root.unmount()?")}if(e._reactRootContainer){{var n=uh(e),a=n&&!Br(n);a&&d("unmountComponentAtNode(): The node you're attempting to unmount was rendered by another copy of React.")}return Cr(function(){Wc(null,null,e,!1,function(){e._reactRootContainer=null,yy(e)})}),!0}else{{var r=uh(e),i=!!(r&&Br(r)),l=e.nodeType===Dn&&Uo(e.parentNode)&&!!e.parentNode._reactRootContainer;i&&d("unmountComponentAtNode(): The node you're attempting to unmount was rendered by React and is not a top-level container. %s",l?"You may have accidentally passed in a React root node instead of its container.":"Instead, have the parent component update its state and rerender in order to remove this component.")}return!1}}QC(MO),IC(AO),XC(LO),KC(ga),JC(PC),(typeof Map!="function"||Map.prototype==null||typeof Map.prototype.forEach!="function"||typeof Set!="function"||Set.prototype==null||typeof Set.prototype.clear!="function"||typeof Set.prototype.forEach!="function")&&d("React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),AE(ZT),jE(Hp,Y_,Cr);function KO(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!Qc(t))throw new Error("Target container is not a DOM element.");return OO(e,t,null,n)}function JO(e,t,n,a){return IO(e,t,n,a)}var oh={usingClientEntryPoint:!1,Events:[Br,yl,js,Uh,jh,Hp]};function ZO(e,t){return oh.usingClientEntryPoint||d('You are importing createRoot from "react-dom" which is not supported. You should instead import it from "react-dom/client".'),VO(e,t)}function e0(e,t,n){return oh.usingClientEntryPoint||d('You are importing hydrateRoot from "react-dom" which is not supported. You should instead import it from "react-dom/client".'),YO(e,t,n)}function t0(e){return Nb()&&d("flushSync was called from inside a lifecycle method. React cannot flush when React is already rendering. Consider moving this call to a scheduler task or micro task."),Cr(e)}var n0=FO({findFiberByHostInstance:Ti,bundleType:1,version:th,rendererPackageName:"react-dom"});if(!n0&&mt&&window.top===window.self&&(navigator.userAgent.indexOf("Chrome")>-1&&navigator.userAgent.indexOf("Edge")===-1||navigator.userAgent.indexOf("Firefox")>-1)){var CS=window.location.protocol;/^(https?|file):$/.test(CS)&&console.info("%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools"+(CS==="file:"?`
You might need to use a local HTTP server (instead of file://): https://reactjs.org/link/react-devtools-faq`:""),"font-weight:bold")}yn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=oh,yn.createPortal=KO,yn.createRoot=ZO,yn.findDOMNode=GO,yn.flushSync=t0,yn.hydrate=QO,yn.hydrateRoot=e0,yn.render=WO,yn.unmountComponentAtNode=XO,yn.unstable_batchedUpdates=Hp,yn.unstable_renderSubtreeIntoContainer=JO,yn.version=th,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)}()),yn}vh.exports=_S();var OS=vh.exports,Zc=OS;{var ko=Zc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;Xc.createRoot=function(w,N){ko.usingClientEntryPoint=!0;try{return Zc.createRoot(w,N)}finally{ko.usingClientEntryPoint=!1}},Xc.hydrateRoot=function(w,N,J){ko.usingClientEntryPoint=!0;try{return Zc.hydrateRoot(w,N,J)}finally{ko.usingClientEntryPoint=!1}}}const i0="";function ef({heading:w,details:N}){return m.jsxs("div",{className:"subscription-details",children:[m.jsx("h2",{className:"tw-bg-blue-950 tw-text-white tw-font-bold tw-p-2",children:w}),m.jsx("table",{className:"tw-w-full",children:m.jsx("tbody",{children:N&&N.map(J=>m.jsxs("tr",{children:[m.jsx("td",{className:"tw-p-2 tw-align-top tw-font-semibold tw-w-[50%]",children:m.jsx("span",{children:J.label})}),m.jsx("td",{className:"tw-p-2 tw-align-top",children:m.jsx("span",{children:J.value})})]},J.label))})})]})}function NS({heading:w,listItems:N}){return m.jsxs("div",{className:"subscription-details",children:[m.jsx("h2",{className:"tw-bg-blue-950 tw-text-white tw-font-bold tw-p-2",children:w}),m.jsx("div",{children:m.jsx("ul",{className:"tw-list-disc tw-ml-10 tw-flex tw-flex-col",children:N.map((J,ce)=>m.jsx("li",{className:"tw-mt-3",children:J.includes("http")?m.jsx(m.Fragment,{children:J.split(/(\bhttps?:\/\/\S+)/gi).map((ke,fe)=>ke.match(/^https?:\/\//)?m.jsx("a",{href:ke,className:"tw-underline tw-text-blue-600",children:ke},fe):ke)}):J},ce))})})]})}const MS=({tableData:w,previewMode:N})=>m.jsxs("table",{className:"tw-border-collapse tw-border "+(N?"!tw-w-full":"!tw-w-[734px]"),children:[m.jsx("thead",{children:m.jsx("tr",{children:w[0].map((J,ce)=>m.jsx("th",{className:"tw-border tw-border-black tw-p-2 tw-border-solid",style:J.styles,colSpan:J.colspan||1,children:J.content},ce))})}),m.jsx("tbody",{children:w.slice(1).map((J,ce)=>m.jsx("tr",{children:J.map((ke,fe)=>m.jsx("td",{className:"tw-border tw-border-black tw-p-2 tw-border-solid",style:ke.styles,colSpan:ke.colspan||1,children:ke.content},fe))},ce))})]}),AS=({html:w,className:N=""})=>m.jsx("div",{style:{all:"initial"},className:N,dangerouslySetInnerHTML:{__html:w}});function LS({customer:w}){return m.jsxs("table",{className:"signature-table tw-mt-5",children:[m.jsxs("tr",{children:[m.jsx("th",{className:"!tw-border-b-0",children:"Everstage Inc."}),m.jsx("th",{className:"!tw-border-l-0 !tw-border-b-0",children:w})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"!tw-p-0 tw-align-top !tw-border-0 tw-w-1/2",children:m.jsxs("table",{className:"tw-w-full",children:[m.jsxs("tr",{className:"tw-align-top",children:[m.jsx("td",{className:"tw-w-[75px]",children:"Signature:"}),m.jsx("td",{})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"tw-w-[75px]",children:"Name:"}),m.jsx("td",{})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"tw-w-[75px]",children:"Title:"}),m.jsx("td",{})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"tw-w-[75px]",children:"Date:"}),m.jsx("td",{})]})]})}),m.jsx("td",{className:"!tw-p-0 tw-align-top !tw-border-0",children:m.jsxs("table",{className:"tw-w-full",children:[m.jsxs("tr",{className:"tw-align-top",children:[m.jsx("td",{className:"!tw-border-l-0 tw-w-[75px]",children:"Signature:"}),m.jsx("td",{className:"!tw-border-l-0"})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"!tw-border-l-0 tw-w-[75px]",children:"Name:"}),m.jsx("td",{className:"!tw-border-l-0"})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"!tw-border-l-0 tw-w-[75px]",children:"Title:"}),m.jsx("td",{className:"!tw-border-l-0"})]}),m.jsxs("tr",{children:[m.jsx("td",{className:"!tw-border-l-0 tw-w-[75px]",children:"Date:"}),m.jsx("td",{className:"!tw-border-l-0"})]})]})})]})]})}const l0="";function US(w){const N=w==null?void 0:w.isDraft,J=w==null?void 0:w.final,ce=w==null?void 0:w.previewMode,ke=w==null?void 0:w.customerName,fe=w==null?void 0:w.customerInformation,d=w==null?void 0:w.subscriptionDetails,ze=w==null?void 0:w.paymentTerms,ae=w==null?void 0:w.tableData;let B=[];const q=w==null?void 0:w.otherData;return(q==null?void 0:q.changeAddress)==="true"?fe[0].value=q==null?void 0:q.newAddress:fe[0].value=q==null?void 0:q.originalAddress,fe[0].value=ke+`
`+fe[0].value,(q==null?void 0:q.termsOfUse)==="MSA"?B=[...B,`By signing this Order Form where indicated below, Customer identified below and Everstage Inc are entering into a binding legal contract consisting of this Order Form ${q==null?void 0:q.dpaWording} and the Master Services Agreement.`,`All payments are due and payable within ${q==null?void 0:q.creditTerms} days of the date of the invoice.`,"Recurring charges for Additional licenses purchased during the course of subscription term will be pro-rated.","Any applicable sales tax relevant to the particular State / Country will be levied depending on the billing address."]:B=[...B,`By signing this Order Form where indicated below, Customer identified below and Everstage Inc are entering into a binding legal contract consisting of this Order Form ${q==null?void 0:q.dpaWording} and the Terms of Use at https://www.everstage.com/terms`,`All payments are due and payable within ${q==null?void 0:q.creditTerms} days of the date of the invoice.`,"Recurring charges for Additional licenses purchased during the course of subscription term will be pro-rated.","Any applicable sales tax relevant to the particular State / Country will be levied depending on the billing address."],(q==null?void 0:q.support)==="Include (Limit)"&&B.push("White-glove service & support provided as a part of this bundle shall be limited to 100 hours/year. Support hours over 100 hours shall be charged at an hourly rate"),(q==null?void 0:q.connectorsWording)!==""&&B.push(`${q==null?void 0:q.connectorsWording}.`),B.push("The fees specified in this Order Form exclude the cost of any required DocuSign licenses."),m.jsxs("div",{className:"watermark-container",children:[!ce&&m.jsx(m.Fragment,{children:N?m.jsx("div",{className:"watermark"}):!J&&m.jsx("div",{className:"final-watermark"})}),m.jsx("div",{className:"tw-flex tw-flex-col tw-gap-5 tw-px-10",children:m.jsxs("div",{className:"tw-flex tw-flex-col tw-gap-5 tw-whitespace-pre-line",children:[m.jsx("h1",{className:"tw-text-xbig tw-font-semibold tw-p-2 tw-flex tw-justify-center tw-text-blue-950 tw-underline",children:"Service Order Form"}),m.jsxs("div",{children:[m.jsxs("div",{className:"tw-pb-1",children:[m.jsx("span",{className:"tw-font-semibold",children:"Order ID:"})," ",w==null?void 0:w.orderId]}),m.jsx(ef,{heading:"Customer Information",details:fe})]}),m.jsx(ef,{heading:"Subscription Details",details:d}),Object.keys(ae).map((W,oe)=>m.jsxs("div",{children:[m.jsx("h2",{className:"tw-py-2 tw-font-semibold",children:W}),m.jsx(MS,{tableData:ae[W],previewMode:ce},oe)]},oe)),m.jsx(ef,{heading:"Payment Terms",details:ze}),m.jsx(NS,{heading:"Other Terms",listItems:B}),m.jsx("hr",{}),m.jsx(LS,{customer:ke})]})})]})}const jS=({signatureData:w})=>m.jsx("div",{className:"tw-grid tw-grid-cols-2 tw-gap-16 !break-inside-auto tw-p-2",children:w.map((N,J)=>m.jsxs("div",{children:[m.jsxs("h3",{className:"tw-font-semibold tw-text-[13px] tw-mb-7",children:[N.company,","]}),N.signatories.map((ce,ke)=>m.jsxs("div",{className:"tw-mb-10",children:[m.jsxs("div",{className:"tw-mb-3 tw-flex tw-items-end",children:[m.jsx("span",{className:"tw-font-medium tw-mr-2",children:"Signature:"})," ",m.jsxs("span",{className:"tw-border-b tw-border-gray3 tw-inline-block tw-w-2/3",children:[(ce==null?void 0:ce.signatureUrl)&&m.jsx("img",{className:"!tw-w-[140px] !tw-h-10 tw-object-contain tw-mx-auto",src:ce.signatureUrl,alt:"signature"}),(ce==null?void 0:ce.signatureTag)&&m.jsx("div",{className:"!tw-w-[140px] !tw-h-10 tw-object-contain tw-mx-auto tw-flex tw-items-end tw-text-white",children:ce.signatureTag})]})]}),m.jsxs("div",{className:"tw-mb-1.5",children:[m.jsx("span",{className:"tw-font-medium tw-mr-6",children:"Name:"})," ",ce.name||""]}),m.jsxs("div",{className:"tw-mb-1.5",children:[m.jsx("span",{className:"tw-font-medium tw-mr-8",children:"Title:"})," ",ce.title||""]})]},ke))]},J))}),u0="",kS=({tableData:w})=>m.jsx("div",{className:"tw-overflow-x-auto tw-h-full",children:m.jsxs("table",{className:"tw-min-w-full tw-table-auto tw-text-left !tw-border-collapse",children:[m.jsx("thead",{children:m.jsxs("tr",{className:"tw-text-gray2 tw-uppercase",children:[m.jsx("th",{className:"tw-p-2 tw-pb-2 tw-text-[11px] tw-align-middle !tw-font-medium tw-min-w-[135px]",children:w.primaryColumn}),w.columns.map((N,J)=>m.jsx("th",{className:`tw-p-2 tw-text-[12px] tw-align-middle !tw-font-medium tw-min-w-[135px] ${N.align==="right"?"tw-text-right":""}`,children:N.label},J))]})}),m.jsx("tbody",{children:w.items.map((N,J)=>m.jsxs(m.Fragment,{children:[J>0&&m.jsx("tr",{children:m.jsx("td",{colSpan:w.columns.length+1,children:m.jsx("div",{className:"tw-border-t tw-border-gray3"})})}),N.rows.map((ce,ke)=>m.jsxs("tr",{children:[ke===0&&m.jsxs("td",{rowSpan:N.rows.length,className:"tw-px-2 tw-py-2 tw-text-black1 tw-align-top tw-font-medium tw-min-w-[135px]",children:[m.jsx("div",{children:N.title}),N.description&&m.jsx("div",{className:"tw-text-gray2 tw-mt-1 !tw-font-normal",dangerouslySetInnerHTML:{__html:N.description}})]}),w.columns.map((fe,d)=>{const ze=ce[fe.key],ae=typeof ze=="object"&&ze!==null,B=Object.keys(ce).length===0;return m.jsx("td",{className:`tw-min-w-[135px] ${fe.align==="right"?"!tw-text-right":""}`,children:B?m.jsx("div",{className:"tw-border-t tw-border-gray3 tw-w-full tw-block"}):ae?m.jsx("div",{className:"tw-p-2",children:m.jsxs("span",{className:"tw-space-x-0.5",children:[ze.sup&&m.jsx("span",{className:"tw-font-medium",children:ze.sup}),ze.strike&&m.jsx("span",{className:"tw-line-through tw-text-gray2 tw-font-normal",children:ze.strike}),ze.text&&m.jsx("span",{className:"tw-font-normal",children:ze.text}),ze.sub&&m.jsx("span",{className:"tw-font-normal tw-text-gray2",children:ze.sub}),ze.mark&&m.jsx("span",{className:"tw-font-normal tw-text-info",children:ze.mark})]})}):m.jsx("span",{className:"tw-p-2",children:ze})},d)})]},`${J}-${ke}`))]}))})]})}),zS="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACnklEQVR4AYVSz08TQRR+szvb0qU/lpZSsLUEASsGSghJL17wIBflzN2oN89e+Q/8G7yQePJKvMDRA5qUpiJG1FZaY2iNSNtdd3dmnDfrtjExcSaTN/Pmve9975shIMfOzlOr9fPi/sxM/rRee/NpfnHpjsncZ+2ev5nLpf1vzebrZG76buP4cH/mWnnJ6V9mr+TSL3aePG5TBJibyxWLXmI9lSmQzrvDXxuV5dnGx5PptfXibN/2/YR7/uHmWmnh5dfj+r1by3P196c3NOG+kqltIoTQnz942NxvNq2CleqNc5F86wxaFctKHJ13RCaZZBOcT1Zt+8taPJH/POgznVJ/wTTh0e7uhGKQMcfim9cXOTBmxQDElG1PZUyTpsdi3PX9aDoWG0w6TjZpUJq3UjoHIHFKGeYqAMKFeXV+nhqJBPw4OYEcIVEFbFlAdB18x0nmx8eBuy6MZbPgdLuGYMweAhS3t4FoGkQjEZhYWQEifVwI0AgBzoW8kx55lqWkEeB5LoCMHwKkSiUQMjBmxmQCl3faPy0hCARgO3awDwEYYwoZLQbiPrTo0zV9CMAFV76/AHQ9CEBL5NR0TQVgZbS4hJxqz4NEwzD+w0BWwrYYD6qFMWFLg/4AICulCBlgEFqkq1NdCqgpJtofsYQSMYj1PA+qR9URA7wkEFATRARnjahq4ULxzlpnUKvVoNFoQDweHwEoioIFAPhslIDv+8rvez5Qg0L3ewf29vag3W5DSb5apVIZASiFYdQnJodVsR087x8cQLfTVYmr5VXITGZGANinz2QlSsGVnwTPCBSNRKHX70G9XleVN25vQHmlrBgh8BBAjgspGMUkFFE+EYkYhrjs9aBarUKn04GtrS0oFAoQkb/Vk1+aEM3BxN8U+2HffwAmqQAAAABJRU5ErkJggg==",HS=w=>{const N=w.tableData;return m.jsxs("div",{className:"tw-mt-6 tw-rounded-lg tw-text-black1 tw-h-full",children:[N.title&&m.jsxs("div",{className:"tw-flex tw-items-center tw-py-2 tw-px-2.5 tw-mb-2 tw-bg-gray3/40 tw-rounded-lg tw-border-t tw-border-gray3",children:[N.periodStart&&m.jsx("img",{src:zS,alt:"calendar",className:"tw-w-3.5 tw-h-3.5 tw-mr-1.5"}),m.jsx("span",{className:"tw-font-semibold",children:N.title}),N.periodStart&&N.periodEnd&&m.jsxs("div",{className:"tw-flex tw-ml-[10px] tw-gap-[10px] ",children:[m.jsx("span",{className:"tw-text-gray2/50",children:"|"}),m.jsxs("span",{className:"tw-text-gray2 tw-font-medium",children:[N.periodStart,"  →  ",N.periodEnd]})]})]}),m.jsx(kS,{tableData:N}),N.subtotal&&m.jsxs("div",{className:"tw-mt-1 tw-border-t tw-border-gray3 tw-font-medium tw-p-2 tw-pt-3 tw-flex tw-justify-between",children:[m.jsx("span",{className:"tw-text-[11px]",children:"SUBTOTAL"}),m.jsx("span",{children:N.subtotal})]})]})},FS=({data:w,theme:N,previewMode:J})=>{const{orderId:ce,validTill:ke,createdBy:fe,createdFor:d,supplierAddress:ze,customerAddress:ae,subscriptionTerm:B,startDate:q,endDate:W,currency:oe,quoteTotal:G}=w,{headerText:ye="SERVICE ORDER FORM",logoUrl:gn="",backgroundFrom:Ln="#FFFBF3",backgroundTo:Kt="#FFFBF3",textColor:He="#1A2131",stripColor:Se="#F59E0B",name:it="Company Name"}=N;return m.jsxs("div",{children:[J&&m.jsx("div",{className:"tw-h-1.5",style:{background:Se}}),m.jsxs("div",{className:"tw-rounded-b-xl tw-px-10 tw-pt-7 tw-pb-8 tw-shadow-sm",style:{background:Ln,color:He},children:[m.jsxs("div",{className:"tw-flex tw-justify-between tw-items-start tw-mb-8",children:[gn?m.jsx("img",{src:gn,alt:"logo",className:"!tw-w-[140px] !tw-h-8 tw-object-contain"}):m.jsx("div",{className:"tw-text-xl tw-font-semibold tw-text-right tw-opacity-40",children:it}),m.jsx("div",{className:"tw-text-xl tw-font-semibold tw-text-right tw-opacity-40",children:ye})]}),m.jsxs("div",{className:"tw-flex tw-gap-12 tw-mb-6",children:[m.jsxs("div",{className:"tw-grid tw-grid-cols-[90px_auto] tw-min-w-[200px]",children:[m.jsx("div",{className:"tw-opacity-80 tw-p-1",children:"ORDER ID"}),m.jsx("div",{className:"tw-font-medium tw-p-1",children:ce}),m.jsx("div",{className:"tw-opacity-80 tw-p-1",children:"VALID TILL"}),m.jsx("div",{className:"tw-font-medium tw-p-1",children:ke}),m.jsx("div",{className:"tw-opacity-80 tw-p-1",children:"CREATED BY"}),m.jsx("div",{className:"tw-font-medium tw-p-1",children:fe}),m.jsx("div",{className:"tw-opacity-80 tw-p-1",children:"CREATED FOR"}),m.jsxs("div",{className:"tw-p-1",children:[m.jsx("div",{className:"tw-font-medium",children:d.name}),m.jsx("div",{children:d.email})]})]}),m.jsxs("div",{className:"tw-p-1 tw-min-w-[170px]",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-1",children:"FROM"}),m.jsx("div",{className:"tw-font-semibold tw-mb-0.5",children:ze.name}),m.jsx("div",{className:"tw-whitespace-pre-line",children:ze.address})]}),m.jsxs("div",{className:"tw-p-1 tw-min-w-[170px]",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-1",children:"TO"}),m.jsx("div",{className:"tw-font-semibold tw-mb-0.5",children:ae.name}),m.jsx("div",{className:"tw-whitespace-pre-line",children:ae.address})]})]}),m.jsxs("div",{className:"tw-grid tw-grid-cols-5 tw-items-end",children:[m.jsxs("div",{className:"tw-p-1",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-1",children:"TERM"}),m.jsx("div",{className:"tw-font-medium !tw-min-h-[18px]",children:B})]}),m.jsxs("div",{className:"tw-p-1",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-1",children:"START DATE"}),m.jsx("div",{className:"tw-font-medium !tw-min-h-[18px]",children:q})]}),m.jsxs("div",{className:"tw-p-1",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-1",children:"END DATE"}),m.jsx("div",{className:"tw-font-medium !tw-min-h-[18px]",children:W})]}),m.jsxs("div",{className:"tw-p-1",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-1",children:"CURRENCY"}),m.jsx("div",{className:"tw-font-medium !tw-min-h-[18px]",children:oe})]}),m.jsxs("div",{className:"tw-text-right",children:[m.jsx("div",{className:"tw-opacity-80 tw-mb-0.5",children:"QUOTE TOTAL"}),m.jsx("div",{className:"tw-font-extrabold tw-text-[16px] !tw-min-h-[24px]",style:{color:Se},children:G})]})]})]})]})};function VS(w){const N=w==null?void 0:w.isDraft,J=w==null?void 0:w.final,ce=w==null?void 0:w.previewMode,{basicData:ke,theme:fe,tableData:d,signatureData:ze,termsAndConditions:ae}=w;return m.jsx("div",{className:"font-ibm tw-text-black1",children:m.jsxs("div",{className:"watermark-container",children:[!ce&&m.jsx(m.Fragment,{children:N?m.jsx("div",{className:"watermark"}):!J&&m.jsx("div",{className:"final-watermark"})}),m.jsx(FS,{data:ke,theme:fe,previewMode:ce}),m.jsx("div",{className:"tw-flex tw-flex-col tw-gap-5 tw-px-10",children:m.jsxs("div",{className:"tw-flex tw-flex-col tw-gap-1 tw-whitespace-pre-line",children:[d.map((B,q)=>m.jsx("div",{children:m.jsx(HS,{tableData:B,previewMode:ce},q)},q)),ae&&m.jsxs("div",{className:"tw-flex tw-flex-col tw-gap-3 tw-my-8",children:[m.jsx("div",{className:"tw-flex tw-items-center tw-text-[14px] tw-mb-2",children:m.jsxs("span",{className:"tw-font-semibold",children:["Terms and Conditions"," "]})}),ae.map(B=>m.jsx("div",{children:m.jsx(AS,{html:B.value,className:"ck-content font-ibm"})},B.term_id))]}),m.jsx(jS,{signatureData:ze})]})})]})})}function BS(w){const N=(w==null?void 0:w.customTemplate)===!0;return m.jsx("div",{children:N?m.jsx(VS,{...w}):m.jsx(US,{...w})})}function YS(w){return m.jsx(m.Fragment,{children:m.jsx(BS,{...w})})}function $S(w){Xc.createRoot(document.getElementById("root")).render(m.jsx(TS.StrictMode,{children:m.jsx(YS,{...w})}))}Vi.renderHtml=$S,Object.defineProperty(Vi,Symbol.toStringTag,{value:"Module"})});
