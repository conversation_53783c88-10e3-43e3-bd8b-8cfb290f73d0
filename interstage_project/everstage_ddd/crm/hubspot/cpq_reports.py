from argparse import ArgumentParser
from typing import Any, Dict, List

from requests import post

parser = ArgumentParser()
parser.add_argument("--api-key", type=str, required=True)
args = parser.parse_args()

HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {args.api_key}",
}

TIMEOUT = 10

OBJECT_DEFINITIONS = [
    {
        "object_name": "everstage_quote",
        "labels": {
            "singular": "Everstage Quote",
            "plural": "Everstage Quotes",
        },
        "properties": [
            {
                "name": "name",
                "label": "Name",
                "type": "string",
                "fieldType": "text",
                "hasUniqueValue": True,
            },
            {
                "name": "quote_id",
                "label": "Quote ID",
                "type": "string",
                "fieldType": "text",
                "hasUniqueValue": True,
            },
            {
                "name": "owner_email",
                "label": "Owner Email",
                "type": "string",
                "fieldType": "text",
            },
            {
                "name": "net_total",
                "label": "Net Total",
                "type": "number",
                "fieldType": "number",
            },
            {
                "name": "primary_quote",
                "label": "Primary Quote",
                "type": "bool",
                "fieldType": "booleancheckbox",
                "options": [
                    {"label": "True", "value": "true"},
                    {"label": "False", "value": "false"},
                ],
            },
            {
                "name": "won_date",
                "label": "Won Date",
                "type": "datetime",
                "fieldType": "date",
            },
        ],
    },
    {
        "object_name": "everstage_quote_line_item",
        "labels": {
            "singular": "Everstage Quote Line Item",
            "plural": "Everstage Quote Line Items",
        },
        "properties": [
            {
                "name": "product_name",
                "label": "Product Name",
                "type": "string",
                "fieldType": "text",
            },
            {
                "name": "quote_id",
                "label": "Quote ID",
                "type": "string",
                "fieldType": "text",
            },
            {
                "name": "line_item_id",
                "label": "Line Item ID",
                "type": "string",
                "fieldType": "text",
                "hasUniqueValue": True,
            },
            {
                "name": "phase_name",
                "label": "Phase Name",
                "type": "string",
                "fieldType": "text",
            },
            {
                "name": "quantity",
                "label": "Quantity",
                "type": "number",
                "fieldType": "number",
            },
            {
                "name": "total",
                "label": "Total",
                "type": "number",
                "fieldType": "number",
            },
        ],
    },
    {
        "object_name": "everstage_quote_line_item_tier",
        "labels": {
            "singular": "Everstage Quote Line Item Tier",
            "plural": "Everstage Quote Line Item Tiers",
        },
        "properties": [
            {
                "name": "product_name",
                "label": "Product Name",
                "type": "string",
                "fieldType": "text",
            },
            {
                "name": "quote_id",
                "label": "Quote ID",
                "type": "string",
                "fieldType": "text",
            },
            {
                "name": "tier_id",
                "label": "Tier ID",
                "type": "string",
                "fieldType": "text",
                "hasUniqueValue": True,
            },
            {
                "name": "tier_number",
                "label": "Tier Number",
                "type": "number",
                "fieldType": "number",
            },
            {
                "name": "tier_quantity",
                "label": "Tier Quantity",
                "type": "number",
                "fieldType": "number",
            },
            {
                "name": "tier_total",
                "label": "Tier Total",
                "type": "number",
                "fieldType": "number",
            },
        ],
    },
]


def create_custom_object(
    object_name: str,
    labels: Dict[str, str],
    properties: List[Dict[str, Any]],
) -> None:
    url = "https://api.hubapi.com/crm/v3/schemas"
    payload = {
        "name": object_name,
        "labels": labels,
        "primaryDisplayProperty": properties[0]["name"],
        "requiredProperties": [properties[0]["name"]],
        "properties": properties,
        "metaType": "PORTAL_SPECIFIC",
    }
    response = post(url, json=payload, headers=HEADERS, timeout=TIMEOUT)
    name = response.json().get("fullyQualifiedName")
    if response.ok:
        print(f"Created object '{name}' with {len(properties)} properties.")
    else:
        print(f"Failed to create object '{object_name}': {response.text}")


if __name__ == "__main__":
    for object_definition in OBJECT_DEFINITIONS:
        create_custom_object(
            object_definition["object_name"],
            object_definition["labels"],
            object_definition["properties"],
        )
