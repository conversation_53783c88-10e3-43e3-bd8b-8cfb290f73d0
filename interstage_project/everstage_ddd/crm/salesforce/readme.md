## Creating custom objects in Salesforce

- Install the Salesforce CLI.
```bash
node --version
npm install @salesforce/cli --global
sf --version
```
- <PERSON>gin to the Salesforce organization.
```bash
sf org login web --alias my-org
sf org display --target-org my-org --verbose --json > authFile.json
```
- Modify the object definitions in `force-app/main/default/objects` to match the required schema.
- Deploy the metadata to the Salesforce organization.
```zsh
sf project deploy start --target-org my-org
```
