from datetime import timed<PERSON><PERSON>
from json import loads
from os import getenv
from typing import List
from uuid import UUID

import jwt
from django.db import connection, transaction
from django.http import Http404
from django.utils.timezone import now
from ninja import Router

from .routers import dealroom_templates_router, dealrooms_router
from .schema import (
    AccessLink,
    DealRoom,
    DealRoomPage,
    GeneratePreviewResponse,
    PublishPayload,
    PublishResponse,
)
from .utils import dict_fetch_all, dict_fetch_one

dealroom_router = Router(tags=["dealroom"])


# Get all deal rooms
dealroom_router.add_router("manage_templates/", dealroom_templates_router)
dealroom_router.add_router("manage_rooms/", dealrooms_router)


@dealroom_router.get("/", response=List[DealRoom])
def get_all_dealrooms(request, limit_value: int = 10, offset_value: int = 0):
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT
                *
            FROM deal_room
            WHERE
                client_id = %s
            ORDER BY
                updated_at DESC
            LIMIT %s
            OFFSET %s
        """,
            [request.client_id, limit_value, offset_value],
        )
        dealrooms = dict_fetch_all(cursor)
    return dealrooms


# Get a deal room by opportunity id or deal_room_id
@dealroom_router.get("/{id}", response=DealRoom)
def get_dealroom_by_id(request, id: str):
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT
                *
            FROM deal_room
            WHERE
                client_id = %s
                AND (deal_room_id::text = %s OR opportunity_id = %s)
        """,
            [request.client_id, str(id), str(id)],
        )
        dealroom = dict_fetch_one(cursor)
        if not dealroom:
            raise Http404
    return dealroom


# Get all pages for a deal room
@dealroom_router.get("/{deal_room_id}/pages", response=List[DealRoomPage])
def get_dealroom_pages(request, deal_room_id: UUID):
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT
                *
            FROM deal_room_page
            WHERE
                client_id = %s
                AND deal_room_id = %s
            ORDER BY page_number
        """,
            [request.client_id, str(deal_room_id)],
        )
        pages = dict_fetch_all(cursor)
        for page in pages:
            if page.get("sections"):
                page["sections"] = loads(page["sections"])
        if not pages:
            cursor.execute(
                """
                SELECT
                    1
                FROM deal_room
                WHERE
                    client_id = %s
                    AND deal_room_id = %s
            """,
                [request.client_id, str(deal_room_id)],
            )
            result = dict_fetch_one(cursor)
            if not result:
                raise Http404
    return pages


# Publish a deal room and generate access tokens
@dealroom_router.post("/{deal_room_id}/publish", response=PublishResponse)
def publish_dealroom(request, deal_room_id: UUID, payload: PublishPayload):
    with connection.cursor() as cursor:  # noqa: SIM117
        with transaction.atomic():
            # Check if deal room exists and belongs to client
            cursor.execute(
                """
                SELECT
                    *
                FROM deal_room
                WHERE
                    client_id = %s
                    AND deal_room_id::text = %s
                """,
                [request.client_id, str(deal_room_id)],
            )
            dealroom = dict_fetch_one(cursor)
            if not dealroom:
                raise Http404

            # Delete existing access tokens
            cursor.execute(
                """
                DELETE 
                    FROM deal_room_access
                WHERE
                    client_id = %s
                    AND deal_room_id::text = %s
                """,
                [request.client_id, str(deal_room_id)],
            )

            # Set deal room as published
            cursor.execute(
                """
                UPDATE deal_room
                SET 
                    is_published = TRUE,
                    updated_by = %s
                WHERE
                    client_id = %s
                    AND deal_room_id::text = %s
                """,
                [request.user.username, request.client_id, str(deal_room_id)],
            )

            # Generate new access tokens
            links = []
            dealroom_secret = getenv("DEALROOM_JWT_SECRET", "everstage_dealroom_secret")

            for email in payload.emails:
                token_payload = {
                    "email": email,
                    "deal_room_id": str(deal_room_id),
                    "exp": int(payload.expiry_at.timestamp()),
                }
                access_token = jwt.encode(
                    token_payload, dealroom_secret, algorithm="HS256"
                )

                # Insert new access token
                cursor.execute(
                    """
                    INSERT INTO deal_room_access (
                        deal_room_id,
                        client_id,
                        opportunity_id,
                        email,
                        access_token,
                        expires_at,
                        created_at,
                        created_by
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    [
                        str(deal_room_id),
                        request.client_id,
                        dealroom["opportunity_id"],
                        email,
                        access_token,
                        payload.expiry_at,
                        now().isoformat(),
                        request.user.username,
                    ],
                )

                # Generate link for response
                url = f"/deal-rooms/{deal_room_id}/preview?access_token={access_token}"
                links.append(AccessLink(email=email, url=url))

    return PublishResponse(links=links)


# Generate a deal room preview
@dealroom_router.get(
    "/{opportunity_id}/generate_preview", response=GeneratePreviewResponse
)
def generate_preview(request, opportunity_id: str):
    with connection.cursor() as cursor:
        email = request.user.username
        with transaction.atomic():
            # Check if deal room exists and belongs to client
            cursor.execute(
                """
                SELECT
                    *
                FROM deal_room
                WHERE
                    client_id = %s
                    AND opportunity_id = %s
                """,
                [request.client_id, opportunity_id],
            )
            dealroom = dict_fetch_one(cursor)
            if not dealroom:
                raise Http404

            deal_room_id = dealroom["deal_room_id"]

            # Check if there is an existing valid access token
            cursor.execute(
                """
                SELECT
                    access_token
                FROM deal_room_access
                WHERE
                    deal_room_id = %s
                    AND client_id = %s
                    AND email = %s
                    AND expires_at > %s
                """,
                [str(deal_room_id), request.client_id, email, now().isoformat()],
            )
            existing_token = dict_fetch_one(cursor)

            if existing_token:
                access_token = existing_token["access_token"]
            else:
                # Create a new access token
                dealroom_secret = getenv(
                    "DEALROOM_JWT_SECRET", "everstage_dealroom_secret"
                )
                expiry_time = now() + timedelta(days=365)

                token_payload = {
                    "email": email,
                    "deal_room_id": str(deal_room_id),
                    "exp": int(expiry_time.timestamp()),
                }
                access_token = jwt.encode(
                    token_payload, dealroom_secret, algorithm="HS256"
                )

                # Insert new access token
                cursor.execute(
                    """
                    INSERT INTO deal_room_access (
                        deal_room_id,
                        client_id,
                        opportunity_id,
                        email,
                        access_token,
                        expires_at,
                        created_at,
                        created_by
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                    [
                        str(deal_room_id),
                        request.client_id,
                        dealroom["opportunity_id"],
                        email,
                        access_token,
                        expiry_time.isoformat(),
                        now().isoformat(),
                        request.user.username,
                    ],
                )

            # Generate link for response
            url = f"/deal-rooms/{deal_room_id}/preview?access_token={access_token}"

    return GeneratePreviewResponse(deal_room_id=deal_room_id, preview_url=url)
