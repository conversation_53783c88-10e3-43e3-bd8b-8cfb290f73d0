import logging
import os
import uuid
from typing import Any, Dict, List

from commission_engine.utils.s3_utils import S3Uploader
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor


def dict_fetch_all(cursor) -> List[Dict[str, Any]]:
    """Returns all rows from a cursor as a list of dictionaries"""
    columns = [col[0] for col in cursor.description] if cursor.description else []
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def dict_fetch_one(cursor) -> Dict[str, Any]:
    """Returns one row from a cursor as a dictionary"""
    row = cursor.fetchone()
    if not row:
        return {}
    columns = [col[0] for col in cursor.description] if cursor.description else []
    return dict(zip(columns, row))


def _upload_file_to_s3(file, s3_path, bucket_name, cloudfront_cdn):
    """
    Helper function to upload a file to S3 and return the document URL and name.
    """
    logging.info("BlockDocument ready to upload to S3")
    document_name = file.name
    final_s3_path = f"{s3_path}/{uuid.uuid4().hex}_{document_name}"
    S3Uploader(bucket_name).upload_file(file, final_s3_path)
    logging.info("BlockDocument uploaded to S3")
    document_url = f"{cloudfront_cdn}/{final_s3_path}"
    return document_url, document_name


def _process_block_file(block, block_files, s3_path):
    """
    Process a block and upload any file_content to S3.
    """
    cloudfront_cdn = os.environ.get("S3_AVATAR_CDN")
    bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    if block_files:
        block_type = block["type"]

        if block_type == "logo":
            # Handle logo block type
            block_id = block["block_id"]
            if (
                "background" in block
                and "background_image" in block["background"]
                and block_id in block_files
            ):
                file = block_files[block_id]
                document_url, document_name = _upload_file_to_s3(
                    file, s3_path, bucket_name, cloudfront_cdn
                )
                block["background"]["background_image"]["file_url"] = document_url
                block["background"]["background_image"]["file_name"] = document_name
        else:
            # Handle other block types
            for item in block[block_type]:
                if "file" in item and item["key"] in block_files:
                    file = block_files[item["key"]]
                    document_url, document_name = _upload_file_to_s3(
                        file, s3_path, bucket_name, cloudfront_cdn
                    )
                    item["file"]["file_url"] = document_url
                    item["file"]["file_name"] = document_name

    return block


def _get_employee_details_from_email_id(client_id, email_id):
    return EmployeeAccessor(client_id).get_employee_by_email_id(email_id)
