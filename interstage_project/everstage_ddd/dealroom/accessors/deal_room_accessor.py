import logging

from django.utils import timezone

from ..models import DealRoom

logger = logging.getLogger(__name__)


class DealRoomAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DealRoom.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def persist_deal_room_fields(self, data):
        data.save()

    def update_deal_room(self, deal_room_id, deal_room_data):
        self.client_kd_aware().filter(deal_room_id=deal_room_id).update(
            **deal_room_data
        )

    def get_deal_room_by_id(self, deal_room_id):
        return self.client_kd_aware().filter(deal_room_id=deal_room_id).first()

    def get_all_deal_rooms(self, status=None, sort_by=None):
        query = self.client_kd_aware()
        if status:
            query = query.filter(deal_room_status=status)
        if sort_by == "name":
            query = query.order_by("deal_room_name", "-knowledge_begin_date")
        elif sort_by == "not_recent":
            query = query.order_by("knowledge_begin_date")
        else:
            query = query.order_by("-knowledge_begin_date")
        return query

    def deal_room_exists(self, deal_room_id):
        return self.client_kd_aware().filter(deal_room_id=deal_room_id).exists()

    def delete_deal_room(self, deal_room_id, email):
        return (
            self.client_kd_aware()
            .filter(deal_room_id=deal_room_id)
            .update(
                knowledge_end_date=timezone.now(),
                is_deleted=True,
                updated_by=email,
                updated_at=timezone.now(),
            )
        )

    def get_deal_room_owner(self, deal_room_id):
        deal_room = self.client_kd_aware().filter(deal_room_id=deal_room_id).first()
        return deal_room.deal_room_owner if deal_room else ""
