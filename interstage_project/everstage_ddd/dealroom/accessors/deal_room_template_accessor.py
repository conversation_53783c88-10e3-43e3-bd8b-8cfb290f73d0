import logging

from django.utils import timezone

from ..models import DealRoomTemplate

logger = logging.getLogger(__name__)


class DealRoomTemplateAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DealRoomTemplate.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def persist_deal_room_template_fields(self, data):
        data.save()

    def update_template(self, template_id, template_data):
        self.client_kd_aware().filter(deal_room_template_id=template_id).update(
            **template_data
        )

    def get_template(self, template_id):
        return self.client_kd_aware().filter(deal_room_template_id=template_id).first()

    def get_templates(self, status=None, sort_by=None):
        query = self.client_kd_aware()
        if status:
            query = query.filter(deal_room_template_status=status)
        if sort_by == "name":
            query = query.order_by("deal_room_template_name", "-knowledge_begin_date")
        elif sort_by == "not_recent":
            query = query.order_by("knowledge_begin_date")
        else:
            query = query.order_by("-knowledge_begin_date")
        return query

    def delete_template(self, template_id, email):
        return (
            self.client_kd_aware()
            .filter(deal_room_template_id=template_id)
            .update(
                knowledge_end_date=timezone.now(),
                is_deleted=True,
                updated_by=email,
                updated_at=timezone.now(),
            )
        )
