import uuid

from django.db.models import Index

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import (
    EsCharField,
    EsDateTimeField,
    EsJSONField,
    EsUUIDField,
)


class DealRoom(MultiTenantTemporal):
    deal_room_id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    deal_room_name = EsCharField(max_length=255, null=False, is_sensitive=False)
    deal_room_status = EsCharField(max_length=50, null=False, is_sensitive=False)
    deal_room_opportunity_name = EsCharField(
        max_length=255, null=False, is_sensitive=False
    )
    deal_room_opportunity_id = EsCharField(
        max_length=255, null=False, is_sensitive=False
    )
    deal_room_account_id = EsCharField(max_length=255, null=False, is_sensitive=False)
    deal_room_owner = EsCharField(max_length=255, null=False, is_sensitive=False)
    deal_room_meta_data = EsJSONField(null=True, is_sensitive=False)
    deal_room_spec = EsJSONField(null=True, is_sensitive=False)
    deal_room_template_id = EsUUIDField(
        null=False, is_sensitive=False
    )  # Maps to parent template
    created_by = EsCharField(max_length=255, null=True, is_sensitive=False)
    updated_by = EsCharField(max_length=255, null=True, is_sensitive=False)
    updated_at = EsDateTimeField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "deal_room"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "knowledge_end_date",
                    "deal_room_id",
                ],
                name="deal_room_index",
            )
        ]
