import uuid

from django.db.models import Index

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import (
    EsCharField,
    EsDateTimeField,
    EsJSONField,
    EsTextField,
    EsUUIDField,
)


class DealRoomTemplate(MultiTenantTemporal):
    deal_room_template_id = EsUUIDField(
        default=uuid.uuid4, null=False, is_sensitive=False
    )
    deal_room_template_name = EsCharField(
        max_length=255, null=False, blank=False, is_sensitive=False
    )
    deal_room_template_status = EsCharField(
        max_length=50, null=False, is_sensitive=False
    )
    deal_room_template_description = EsTextField(null=True, is_sensitive=False)
    deal_room_template_meta_data = EsJSONField(null=True, is_sensitive=False)
    deal_room_template_spec = EsJSONField(null=True, is_sensitive=False)
    created_by = EsCharField(max_length=255, null=True, is_sensitive=False)
    updated_by = EsCharField(max_length=255, null=True, is_sensitive=False)
    updated_at = EsDateTimeField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "deal_room_template"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "knowledge_end_date",
                    "deal_room_template_id",
                ],
                name="deal_room_template_index",
            ),
        ]
