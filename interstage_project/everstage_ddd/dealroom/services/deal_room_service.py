import logging
import os
import uuid

from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_settings
from everstage_ddd.cpq.price_book import (
    PriceBookDatasheetTable,
    PriceBookDatasheetTableParams,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector
from everstage_ddd.cpq.quote.utils import get_table_variables
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor

from ..accessors import DealRoomAccessor, DealRoomTemplateAccessor
from ..enum import DealRoomCustomLogoSourceTypeEnum, DealRoomStatusEnum
from ..exceptions import (
    DealRoomBadRequestError,
    DealRoomNotFoundError,
    DealRoomTemplateNotFoundError,
)
from ..serializers import DealRoomSerializer
from ..utils import (
    _get_employee_details_from_email_id,
    _process_block_file,
    _upload_file_to_s3,
)

logger = logging.getLogger(__name__)


def process_customer_logo_files(
    type, s3_path, deal_room_meta_data, customer_logo_files, customer_logo
):
    cloudfront_cdn = os.environ.get("S3_AVATAR_CDN")
    bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    if customer_logo_files:
        for customer_logo_file_key in customer_logo_files:
            file = customer_logo_files[customer_logo_file_key]
            document_url, document_name = _upload_file_to_s3(
                file, s3_path, bucket_name, cloudfront_cdn
            )
            customer_logo["type"] = type
            customer_logo["url"] = document_url
            customer_logo["name"] = document_name
            deal_room_meta_data["customer_logo"] = customer_logo


def get_dealroom_opportunity_for_client(client_id, limit, offset):
    cpq_settings = get_client_settings(client_id).get("cpq_settings", {})
    deal_room_databook_id = cpq_settings.get("deal_room_databook_id", None)
    deal_room_datasheet_id = cpq_settings.get("deal_room_datasheet_id", None)
    deal_name_col_system_name = cpq_settings.get("deal_name_col_system_name", None)
    deal_id_col_system_name = cpq_settings.get("deal_id_col_system_name", None)
    deal_account_id_col_system_name = cpq_settings.get(
        "deal_account_id_col_system_name", None
    )
    opportunity_details = []
    if (
        deal_room_databook_id
        and deal_room_datasheet_id
        and deal_name_col_system_name
        and deal_id_col_system_name
        and deal_account_id_col_system_name
    ):
        pricebook_variables = get_table_variables(client_id, deal_room_datasheet_id)
        pricebook_variables = [
            column
            for column in pricebook_variables
            if column.system_name
            in {
                deal_name_col_system_name,
                deal_id_col_system_name,
                deal_account_id_col_system_name,
            }
        ]
        matrix = PriceBookDatasheetTable(
            params=PriceBookDatasheetTableParams(
                client_id=client_id,
                table_columns=pricebook_variables,
                primary_key_column="row_key",
                unique_table_id=deal_room_datasheet_id,
                unique_schema_id=deal_room_databook_id,
            ),
        )
        records = matrix.get_table_df(use_semantic_tag=True)
        records = records.execute().to_dict(orient="records")
        opportunity_details = [
            {
                "opportunity_name": record.get(deal_name_col_system_name),
                "opportunity_id": record.get(deal_id_col_system_name),
                "account_id": record.get(deal_account_id_col_system_name),
            }
            for record in records[offset : offset + limit]
        ]

    return {"status": "SUCCESS", "data": opportunity_details}


def create_deal_room_from_template(
    client_id,
    email,
    customer_logo_files,
    customer_logo,
    **kwargs,
):
    deal_room_template_id = kwargs.get("deal_room_template_id")
    deal_room_name = kwargs.get("deal_room_name")
    deal_room_opportunity_id = kwargs.get("deal_room_opportunity_id")
    deal_room_opportunity_name = kwargs.get("deal_room_opportunity_name")
    deal_room_owner = kwargs.get("deal_room_owner")
    deal_room_account_id = kwargs.get("deal_room_account_id")
    template_data = DealRoomTemplateAccessor(client_id).get_template(
        deal_room_template_id
    )

    if not template_data:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)
    deal_room_id = uuid.uuid4()
    deal_room_meta_data = {}
    deal_room_meta_data["clone_count"] = 0
    if customer_logo.get("type") == DealRoomCustomLogoSourceTypeEnum.BRAND_FETCH.value:
        deal_room_meta_data["customer_logo"] = customer_logo
    elif customer_logo.get("type") == DealRoomCustomLogoSourceTypeEnum.UPLOAD.value:
        customer_logo["url"] = ""
        customer_logo["name"] = ""
        deal_room_meta_data["customer_logo"] = customer_logo
        s3_path = f"deal_room_customer_logo/{client_id}/{deal_room_id}"
        process_customer_logo_files(
            customer_logo["type"],
            s3_path,
            deal_room_meta_data,
            customer_logo_files,
            customer_logo,
        )
    deal_room_data = {
        "client": client_id,
        "knowledge_begin_date": timezone.now(),
        "deal_room_id": deal_room_id,
        "deal_room_template_id": deal_room_template_id,
        "deal_room_name": deal_room_name,
        "deal_room_status": DealRoomStatusEnum.DRAFT.value,
        "deal_room_opportunity_id": deal_room_opportunity_id,
        "deal_room_opportunity_name": deal_room_opportunity_name,
        "deal_room_account_id": deal_room_account_id,
        "deal_room_owner": deal_room_owner,
        "deal_room_meta_data": deal_room_meta_data,
        "deal_room_spec": template_data.deal_room_template_spec,
        "created_by": email,
    }

    deal_room_serializer = DealRoomSerializer(data=deal_room_data)
    if deal_room_serializer.is_valid():
        DealRoomAccessor(client_id).persist_deal_room_fields(deal_room_serializer)
    else:
        message = "Error while persisting deal room details"
        raise DealRoomBadRequestError(message)

    return {"status": "SUCCESS", "deal_room_id": str(deal_room_data["deal_room_id"])}


def clone_deal_room(client_id, deal_room_id, email):
    deal_room_accessor = DealRoomAccessor(client_id)
    parent_deal_room = deal_room_accessor.get_deal_room_by_id(deal_room_id)

    if not parent_deal_room:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)

    clone_count = parent_deal_room.deal_room_meta_data.get("clone_count", 0)
    clone_name = f"{parent_deal_room.deal_room_name}_Copy({clone_count + 1})"

    clone_meta_data = parent_deal_room.deal_room_meta_data.copy()
    clone_meta_data["clone_count"] = 0

    clone_data = {
        "client": client_id,
        "deal_room_id": uuid.uuid4(),
        "deal_room_template_id": parent_deal_room.deal_room_template_id,
        "deal_room_name": clone_name,
        "deal_room_status": DealRoomStatusEnum.DRAFT.value,
        "deal_room_opportunity_id": parent_deal_room.deal_room_opportunity_id,
        "deal_room_opportunity_name": parent_deal_room.deal_room_opportunity_name,
        "deal_room_account_id": parent_deal_room.deal_room_account_id,
        "deal_room_owner": parent_deal_room.deal_room_owner,
        "deal_room_meta_data": clone_meta_data,
        "deal_room_spec": parent_deal_room.deal_room_spec,
        "created_by": email,
        "knowledge_begin_date": timezone.now(),
    }

    clone_deal_room_serializer = DealRoomSerializer(data=clone_data)
    if clone_deal_room_serializer.is_valid():
        deal_room_accessor.persist_deal_room_fields(clone_deal_room_serializer)
    else:
        message = "Error while persisting deal room details"
        raise DealRoomBadRequestError(message)

    parent_meta_data = parent_deal_room.deal_room_meta_data.copy()
    parent_meta_data["clone_count"] = clone_count + 1

    update_data = {
        "deal_room_meta_data": parent_meta_data,
        "updated_by": email,
        "updated_at": timezone.now(),
    }

    deal_room_accessor.update_deal_room(deal_room_id, update_data)

    return {
        "status": "SUCCESS",
        "deal_room_id": str(clone_data["deal_room_id"]),
    }


def update_pages_to_deal_room(
    client_id,
    email,
    deal_room_id,
    deal_room_spec,
):
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_room_data = deal_room_accessor.get_deal_room_by_id(deal_room_id)

    if not deal_room_data:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)
    update_data = {
        "updated_by": email,
        "updated_at": timezone.now(),
    }
    if deal_room_spec is not None:
        update_data["deal_room_spec"] = deal_room_spec
        deal_room_accessor.update_deal_room(deal_room_id, update_data)

    return {"status": "SUCCESS", "deal_room_id": str(deal_room_id)}


def update_block_to_deal_room(
    client_id,
    email,
    deal_room_id,
    deal_room_block_files,
    **kwargs,
):
    deal_room_page_id = kwargs.get("deal_room_page_id")
    deal_room_page_block_id = kwargs.get("deal_room_page_block_id")
    deal_room_page_block = kwargs.get("deal_room_page_block")
    deal_room_page_block_order = kwargs.get("deal_room_page_block_order")
    accessor = DealRoomAccessor(client_id)
    deal_room_data = accessor.get_deal_room_by_id(deal_room_id)
    if not deal_room_data:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)
    update_data = {
        "updated_by": email,
        "updated_at": timezone.now(),
    }
    if deal_room_page_block is not None:
        deal_room_spec = deal_room_data.deal_room_spec or {}
        if "pages" not in deal_room_spec:
            deal_room_spec["pages"] = {}
            deal_room_spec["pages_order"] = [deal_room_page_id]
        if deal_room_page_id not in deal_room_spec["pages"]:
            deal_room_spec["pages"][deal_room_page_id] = {
                "blocks": {},
                "blocks_order": [deal_room_page_block_id],
            }
        if "blocks" not in deal_room_spec["pages"][deal_room_page_id]:
            deal_room_spec["pages"][deal_room_page_id]["blocks"] = {}
        s3_path = (
            f"deal_room_pages/{client_id}/{deal_room_id}/{deal_room_page_block_id}"
        )
        deal_room_spec["pages"][deal_room_page_id]["blocks"][
            deal_room_page_block_id
        ] = _process_block_file(deal_room_page_block, deal_room_block_files, s3_path)
        deal_room_spec["pages"][deal_room_page_id][
            "blocks_order"
        ] = deal_room_page_block_order
        update_data["deal_room_spec"] = deal_room_spec
        accessor.update_deal_room(deal_room_id, update_data)

    return {
        "status": "SUCCESS",
        "deal_room_id": str(deal_room_id),
    }


def get_deal_room_by_id(client_id, deal_room_id):
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_room = deal_room_accessor.get_deal_room_by_id(deal_room_id)

    if not deal_room:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)
    quote = QuoteSelector(client_id).get_primary_quote_for_opportunity(
        deal_room.deal_room_opportunity_id
    )
    quote_amount = quote.net_quote_total if quote and quote.net_quote_total else "0"
    currency_code_symbol_map = {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }
    employee = (
        _get_employee_details_from_email_id(client_id, deal_room.deal_room_owner)
        if deal_room.deal_room_owner
        else ""
    )
    deal_room_meta_data = deal_room.deal_room_meta_data or {}
    customer_logo = deal_room_meta_data.get("customer_logo")
    return {
        "status": "SUCCESS",
        "deal_room_id": str(deal_room.deal_room_id),
        "deal_room_template_id": str(deal_room.deal_room_template_id),
        "deal_room_name": deal_room.deal_room_name,
        "deal_room_status": deal_room.deal_room_status,
        "deal_room_opportunity_id": deal_room.deal_room_opportunity_id,
        "deal_room_opportunity_name": deal_room.deal_room_opportunity_name,
        "deal_room_account_id": deal_room.deal_room_account_id,
        "deal_room_owner_name": (
            f"{employee[0].first_name} {employee[0].last_name}" if employee else ""
        ),
        "deal_room_owner": deal_room.deal_room_owner or "",
        "deal_room_owner_profile_pic": (
            employee[0].profile_picture
            if employee and employee[0].profile_picture
            else ""
        ),
        "deal_room_page_spec": deal_room.deal_room_spec,
        "deal_room_quote_amount": str(quote_amount),
        "deal_room_quote_currency": (
            (currency_code_symbol_map.get(quote.quote_currency, "")) if quote else ""
        ),
        "customer_logo": customer_logo or {},
    }


def list_all_deal_rooms(client_id, sort_by=None, status=None):
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_rooms = deal_room_accessor.get_all_deal_rooms(status=status, sort_by=sort_by)
    deal_rooms_data = []
    for deal_room in deal_rooms or []:
        quote = QuoteSelector(client_id).get_primary_quote_for_opportunity(
            deal_room.deal_room_opportunity_id
        )
        quote_amount = quote.net_quote_total if quote and quote.net_quote_total else "0"
        currency_code_symbol_map = {
            country.currency_code: country.currency_symbol
            for country in list(CountriesAccessor(client_id).get_all_countries())
        }
        employee = (
            _get_employee_details_from_email_id(client_id, deal_room.deal_room_owner)
            if deal_room.deal_room_owner
            else ""
        )
        deal_room_meta_data = deal_room.deal_room_meta_data or {}
        customer_logo = deal_room_meta_data.get("customer_logo")
        deal_rooms_data.append(
            {
                "deal_room_id": str(deal_room.deal_room_id),
                "deal_room_template_id": str(deal_room.deal_room_template_id),
                "deal_room_name": deal_room.deal_room_name,
                "deal_room_status": deal_room.deal_room_status,
                "deal_room_opportunity_id": deal_room.deal_room_opportunity_id,
                "deal_room_opportunity_name": deal_room.deal_room_opportunity_name,
                "deal_room_account_id": deal_room.deal_room_account_id,
                "deal_room_owner_name": (
                    f"{employee[0].first_name} {employee[0].last_name}"
                    if employee
                    else ""
                ),
                "deal_room_owner": deal_room.deal_room_owner or "",
                "deal_room_owner_profile_pic": (
                    employee[0].profile_picture
                    if employee and employee[0].profile_picture
                    else ""
                ),
                "deal_room_quote_amount": str(quote_amount),
                "deal_room_quote_currency": (
                    (currency_code_symbol_map.get(quote.quote_currency, ""))
                    if quote
                    else ""
                ),
                "customer_logo": customer_logo or {},
                "last_updated_at": (
                    str(deal_room.updated_at) if deal_room.updated_at else ""
                ),
            }
        )

    return {"status": "success", "data": deal_rooms_data}


def update_deal_room_data(
    client_id,
    deal_room_id,
    email,
    customer_logo_files,
    **kwargs,
):
    customer_logo = kwargs.get("customer_logo")
    deal_room_name = kwargs.get("deal_room_name")
    deal_room_status = kwargs.get("deal_room_status")
    deal_room_opportunity_id = kwargs.get("deal_room_opportunity_id")
    deal_room_opportunity_name = kwargs.get("deal_room_opportunity_name")
    deal_room_owner = kwargs.get("deal_room_owner")
    deal_room_account_id = kwargs.get("deal_room_account_id")
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_room_data = deal_room_accessor.get_deal_room_by_id(deal_room_id)
    if not deal_room_data:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)
    deal_room_meta_data = deal_room_data.deal_room_meta_data
    update_data = {"updated_by": email, "updated_at": timezone.now()}
    if deal_room_name is not None:
        update_data["deal_room_name"] = deal_room_name.strip()
    if deal_room_status is not None:
        update_data["deal_room_status"] = deal_room_status.strip()
    if (
        deal_room_opportunity_id is not None
        and deal_room_opportunity_name is not None
        and deal_room_account_id is not None
    ):
        update_data["deal_room_opportunity_id"] = deal_room_opportunity_id
        update_data["deal_room_opportunity_name"] = deal_room_opportunity_name
        update_data["deal_room_account_id"] = deal_room_account_id
    if deal_room_owner is not None:
        update_data["deal_room_owner"] = deal_room_owner
    if customer_logo is not None:
        if (
            customer_logo.get("type")
            == DealRoomCustomLogoSourceTypeEnum.BRAND_FETCH.value
        ):
            deal_room_meta_data["customer_logo"] = customer_logo
        elif customer_logo.get("type") == DealRoomCustomLogoSourceTypeEnum.UPLOAD.value:
            s3_path = f"deal_room_customer_logo/{client_id}/{deal_room_id}"
            process_customer_logo_files(
                customer_logo["type"],
                s3_path,
                deal_room_meta_data,
                customer_logo_files,
                customer_logo,
            )
        update_data["deal_room_meta_data"] = deal_room_meta_data

    deal_room_accessor.update_deal_room(deal_room_id, update_data)

    return {
        "status": "SUCCESS",
        "deal_room_id": str(deal_room_id),
    }


def delete_deal_room(client_id, deal_room_id, email):
    DealRoomAccessor(client_id).delete_deal_room(deal_room_id, email)
    return {"status": "success", "deal_room_id": str(deal_room_id)}


def update_page_to_deal_room(
    client_id,
    email,
    deal_room_id,
    deal_room_page_id,
    deal_room_page_spec,
):
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_room_data = deal_room_accessor.get_deal_room_by_id(deal_room_id)
    if not deal_room_data:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)

    update_data = {
        "updated_by": email,
        "updated_at": timezone.now(),
    }

    if deal_room_page_spec is not None:
        deal_room_spec = deal_room_data.deal_room_spec or {}
        if "pages" not in deal_room_spec:
            deal_room_spec["pages"] = {}

        deal_room_spec["pages"][deal_room_page_id] = deal_room_page_spec

        update_data["deal_room_spec"] = deal_room_spec
        deal_room_accessor.update_deal_room(deal_room_id, update_data)

    return {"status": "SUCCESS", "deal_room_id": str(deal_room_id)}


def sync_deal_room_with_template(client_id, deal_room_id, email):
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_room_data = deal_room_accessor.get_deal_room_by_id(deal_room_id)

    if not deal_room_data:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)

    template_data = DealRoomTemplateAccessor(client_id).get_template(
        deal_room_data.deal_room_template_id
    )

    if not template_data:
        message = f"Template with ID '{deal_room_data.deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)

    update_data = {
        "deal_room_spec": template_data.deal_room_template_spec,
        "updated_by": email,
        "updated_at": timezone.now(),
    }

    deal_room_accessor.update_deal_room(deal_room_id, update_data)

    return {"status": "SUCCESS", "deal_room_id": str(deal_room_id)}


def fetch_link_preview(url: str):
    """
    Fetches Open Graph and standard metadata from a given URL.
    Returns a dictionary with title, description, image, and url.
    """
    from urllib.parse import urljoin

    import requests
    from bs4 import BeautifulSoup

    headers = {"User-Agent": "Mozilla/5.0 (compatible; EverstageBot/1.0)"}
    resp = requests.get(url, headers=headers, timeout=10)
    resp.raise_for_status()
    soup = BeautifulSoup(resp.text, "html.parser")

    def get_og(tag):
        tag_val = soup.find("meta", property=f"og:{tag}")
        if tag_val and tag_val.get("content"):
            return tag_val["content"]
        return None

    def get_name(tag):
        tag_val = soup.find("meta", attrs={"name": tag})
        if tag_val and tag_val.get("content"):
            return tag_val["content"]
        return None

    def get_og_image():
        # Try property and name
        og = soup.find("meta", property="og:image") or soup.find(
            "meta", attrs={"name": "og:image"}
        )
        if og and og.get("content"):
            return og["content"]
        return None

    def get_largest_img():
        imgs = soup.find_all("img")
        max_area = 0
        best_img = None
        for img in imgs:
            src = img.get("src")
            if not src:
                continue
            width = img.get("width")
            height = img.get("height")
            try:
                area = int(width) * int(height)
            except (TypeError, ValueError):
                area = 0
            if area > max_area:
                max_area = area
                best_img = src
        return best_img

    def make_absolute_url(relative_url, base_url):
        """Convert relative URL to absolute URL"""
        if not relative_url:
            return None
        if relative_url.startswith(("http://", "https://")):
            return relative_url
        return urljoin(base_url, relative_url)

    title = (
        get_og("title")
        or (soup.title.string if soup.title else None)
        or get_name("title")
    )
    description = get_og("description") or get_name("description")

    # Get image URL and make it absolute
    image = get_og_image()
    if not image:
        image = get_largest_img()
    if not image:
        # fallback to first img
        img_tag = soup.find("img")
        if img_tag and img_tag.get("src"):
            image = img_tag["src"]
    if image:
        image = make_absolute_url(image, url)

    result_url = get_og("url") or url

    return {
        "status": "SUCCESS",
        "title": title,
        "description": description,
        "image": image,
        "url": result_url,
    }


def get_deal_room_owner_email(client_id, deal_room_id):
    deal_room_accessor = DealRoomAccessor(client_id)
    email_id = deal_room_accessor.get_deal_room_owner(deal_room_id)
    return email_id


def get_welcome_page_data(client_id, deal_room_id):
    deal_room_accessor = DealRoomAccessor(client_id)
    deal_room_data = deal_room_accessor.get_deal_room_by_id(deal_room_id)
    if not deal_room_data:
        message = f"Deal room with ID '{deal_room_id}' not found for client {client_id}"
        raise DealRoomNotFoundError(message)
    return deal_room_data.deal_room_spec.get("pages", {}).get("welcome_page", {})
