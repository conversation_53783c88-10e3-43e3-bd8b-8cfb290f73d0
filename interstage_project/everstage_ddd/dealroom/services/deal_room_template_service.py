import uuid

from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_meta_details

from ..accessors import DealR<PERSON>TemplateAccessor
from ..enum import DealRoomTemplateStatusEnum
from ..exceptions import Deal<PERSON>oomTemplateBadRequestError, DealRoomTemplateNotFoundError
from ..serializers import DealRoomTemplateSerializer
from ..utils import _get_employee_details_from_email_id, _process_block_file


def create_deal_room_template(
    client_id,
    email,
    deal_room_template_name,
    deal_room_template_description,
):
    # Initialize meta_data with clone_count
    deal_room_template_meta_data = {}
    deal_room_template_meta_data["clone_count"] = 0

    # Get client name from client settings
    client_meta_details = get_client_meta_details([client_id])
    client_name = client_meta_details.get(client_id, {}).get("name", "Client")

    template_data = {
        "client": client_id,
        "knowledge_begin_date": timezone.now(),
        "deal_room_template_id": uuid.uuid4(),
        "deal_room_template_name": deal_room_template_name.strip(),
        "deal_room_template_status": DealRoomTemplateStatusEnum.INACTIVE.value,
        "deal_room_template_description": (
            deal_room_template_description.strip()
            if deal_room_template_description
            else None
        ),
        "deal_room_template_spec": {
            "pages": {
                "welcome_page": {
                    "page_id": "welcome_page",
                    "name": "Welcome Page",
                    "title": f"{client_name} has shared a deal room with you",
                    "description": "A welcome screen to introduce this space and tell your customers what they can find here.",
                },
                "header": {
                    "page_id": "header",
                    "name": "Header",
                    "background": {
                        "background_color": "#072A6C",
                    },
                },
            },
            "pages_order": [],
        },
        "deal_room_template_meta_data": deal_room_template_meta_data,
        "created_by": email,
    }
    template_serializer = DealRoomTemplateSerializer(data=template_data)
    if template_serializer.is_valid():
        DealRoomTemplateAccessor(client_id).persist_deal_room_template_fields(
            template_serializer
        )
    else:
        message = "Error while persisting deal room template details"
        raise DealRoomTemplateBadRequestError(message)

    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(template_data["deal_room_template_id"]),
    }


def clone_deal_room_template(
    client_id,
    deal_room_template_id,
    email,
):
    """
    Clone an existing deal room template.
    Creates new template with same data but different UUID and name with clone suffix.
    """

    accessor = DealRoomTemplateAccessor(client_id)
    parent_template = accessor.get_template(deal_room_template_id)
    if not parent_template:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)

    parent_meta_data = parent_template.deal_room_template_meta_data or {}
    current_clone_count = parent_meta_data.get("clone_count", 0)

    parent_name = parent_template.deal_room_template_name
    clone_name = f"{parent_name}_Copy({current_clone_count+1})"
    clone_deal_room_template_meta_data = parent_meta_data.copy()
    clone_deal_room_template_meta_data["clone_count"] = 0

    # Prepare clone template data
    current_time = timezone.now()
    clone_template_data = {
        "client": client_id,
        "knowledge_begin_date": timezone.now(),
        "deal_room_template_id": uuid.uuid4(),
        "deal_room_template_name": clone_name,
        "deal_room_template_status": DealRoomTemplateStatusEnum.INACTIVE.value,
        "deal_room_template_description": parent_template.deal_room_template_description,
        "deal_room_template_meta_data": clone_deal_room_template_meta_data,  # New clone starts with count 1
        "deal_room_template_spec": parent_template.deal_room_template_spec,
        "created_by": email,
    }

    # Create cloned template
    clone_template_serializer = DealRoomTemplateSerializer(data=clone_template_data)
    if clone_template_serializer.is_valid():
        accessor.persist_deal_room_template_fields(clone_template_serializer)
    else:
        message = "Error while persisting deal room template details"
        raise DealRoomTemplateBadRequestError(message)

    # Update parent template's clone count
    updated_parent_meta_data = parent_meta_data.copy()
    updated_parent_meta_data["clone_count"] = current_clone_count + 1

    accessor.update_template(
        deal_room_template_id,
        {
            "deal_room_template_meta_data": updated_parent_meta_data,
            "updated_by": email,
            "updated_at": current_time,
        },
    )

    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(clone_template_data["deal_room_template_id"]),
    }


def update_pages_to_deal_room_template(
    client_id, email, deal_room_template_id, deal_room_template_spec
):
    accessor = DealRoomTemplateAccessor(client_id)
    template_data = accessor.get_template(deal_room_template_id)
    if not template_data:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)
    update_data = {
        "updated_by": email,
        "updated_at": timezone.now(),
    }
    if deal_room_template_spec is not None:
        update_data["deal_room_template_spec"] = deal_room_template_spec
        accessor.update_template(deal_room_template_id, update_data)

    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(deal_room_template_id),
    }


def update_block_to_deal_room_template(
    client_id,
    email,
    deal_room_template_id,
    template_block_files,
    **kwargs,
):
    deal_room_template_page_id = kwargs.get("deal_room_template_page_id")
    deal_room_template_page_block_id = kwargs.get("deal_room_template_page_block_id")
    deal_room_template_page_block = kwargs.get("deal_room_template_page_block")
    deal_room_template_page_block_order = kwargs.get(
        "deal_room_template_page_block_order"
    )
    accessor = DealRoomTemplateAccessor(client_id)
    template_data = accessor.get_template(deal_room_template_id)
    if not template_data:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)
    update_data = {
        "updated_by": email,
        "updated_at": timezone.now(),
    }
    if deal_room_template_page_block is not None:
        deal_room_template_spec = template_data.deal_room_template_spec or {}
        if "pages" not in deal_room_template_spec:
            deal_room_template_spec["pages"] = {}
            deal_room_template_spec["pages_order"] = [deal_room_template_page_id]
        if deal_room_template_page_id not in deal_room_template_spec["pages"]:
            deal_room_template_spec["pages"][deal_room_template_page_id] = {
                "blocks": {},
                "blocks_order": [deal_room_template_page_block_id],
            }
        if "blocks" not in deal_room_template_spec["pages"][deal_room_template_page_id]:
            deal_room_template_spec["pages"][deal_room_template_page_id]["blocks"] = {}

        s3_path = f"deal_room_template_pages/{client_id}/{deal_room_template_id}/{deal_room_template_page_block_id}"

        deal_room_template_spec["pages"][deal_room_template_page_id]["blocks"][
            deal_room_template_page_block_id
        ] = _process_block_file(
            deal_room_template_page_block, template_block_files, s3_path
        )
        deal_room_template_spec["pages"][deal_room_template_page_id][
            "blocks_order"
        ] = deal_room_template_page_block_order
        update_data["deal_room_template_spec"] = deal_room_template_spec
        accessor.update_template(deal_room_template_id, update_data)
    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(deal_room_template_id),
    }


def get_deal_room_template_by_id(client_id, deal_room_template_id):
    accessor = DealRoomTemplateAccessor(client_id)
    template = accessor.get_template(deal_room_template_id)

    if not template:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)

    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(template.deal_room_template_id),
        "deal_room_template_name": template.deal_room_template_name,
        "deal_room_template_status": template.deal_room_template_status,
        "deal_room_template_description": template.deal_room_template_description,
        "deal_room_page_spec": template.deal_room_template_spec or {},
    }


def list_all_deal_room_templates(client_id, sort_by=None, status=None):
    accessor = DealRoomTemplateAccessor(client_id)
    templates = accessor.get_templates(status=status, sort_by=sort_by)

    deal_room_templates = []
    for template in templates or []:
        employee = _get_employee_details_from_email_id(client_id, template.created_by)
        deal_room_templates.append(
            {
                "deal_room_template_id": str(template.deal_room_template_id),
                "deal_room_template_name": template.deal_room_template_name,
                "deal_room_template_status": template.deal_room_template_status,
                "deal_room_template_description": template.deal_room_template_description,
                "owner": (
                    f"{employee[0].first_name} {employee[0].last_name}"
                    if employee
                    else ""
                ),
                "owner_email_id": template.created_by or "",
            }
        )
    return {
        "status": "SUCCESS",
        "deal_room_templates": deal_room_templates,
    }


def update_deal_room_template_data(
    client_id, email, deal_room_template_id, deal_room_template_name=None, **kwargs
):
    deal_room_template_status = kwargs.get("deal_room_template_status")
    deal_room_template_description = kwargs.get("deal_room_template_description")
    accessor = DealRoomTemplateAccessor(client_id)
    template_data = accessor.get_template(deal_room_template_id)

    if not template_data:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)

    update_data = {"updated_by": email, "updated_at": timezone.now()}
    if deal_room_template_name is not None:
        update_data["deal_room_template_name"] = deal_room_template_name.strip()
    if deal_room_template_status is not None:
        update_data["deal_room_template_status"] = deal_room_template_status.strip()
    if deal_room_template_description is not None:
        update_data["deal_room_template_description"] = (
            deal_room_template_description.strip()
        )

    accessor.update_template(deal_room_template_id, update_data)

    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(deal_room_template_id),
    }


def delete_deal_room_template(client_id, deal_room_template_id, email):
    DealRoomTemplateAccessor(client_id).delete_template(deal_room_template_id, email)
    return {"status": "SUCCESS", "deal_room_template_id": str(deal_room_template_id)}


def update_page_to_deal_room_template(
    client_id,
    email,
    deal_room_template_id,
    deal_room_template_page_id,
    deal_room_template_page_spec,
):
    accessor = DealRoomTemplateAccessor(client_id)
    template_data = accessor.get_template(deal_room_template_id)
    if not template_data:
        message = f"Template with ID '{deal_room_template_id}' not found for client {client_id}"
        raise DealRoomTemplateNotFoundError(message)

    update_data = {
        "updated_by": email,
        "updated_at": timezone.now(),
    }

    if deal_room_template_page_spec is not None:
        deal_room_template_spec = template_data.deal_room_template_spec or {}
        if "pages" not in deal_room_template_spec:
            deal_room_template_spec["pages"] = {}

        # Update the specific page
        deal_room_template_spec["pages"][
            deal_room_template_page_id
        ] = deal_room_template_page_spec

        update_data["deal_room_template_spec"] = deal_room_template_spec
        accessor.update_template(deal_room_template_id, update_data)

    return {
        "status": "SUCCESS",
        "deal_room_template_id": str(deal_room_template_id),
    }
