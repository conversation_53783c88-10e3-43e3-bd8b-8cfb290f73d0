import json
import logging

from django.db import transaction
from ninja import Form, Router
from ninja.decorators import decorate_view
from ninja.errors import HttpError
from rest_framework import status

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope

from ..exceptions import (
    DealRoomBadRequestError,
    DealRoomNotFoundError,
    DealRoomTemplateNotFoundError,
)
from ..schemas.deal_room_schema import (
    CloneDealRoomPayload,
    CloneDealRoomResponse,
    CreateDealRoomPayload,
    CreateDealRoomResponse,
    DeleteDealRoomPayload,
    DeleteDealRoomResponse,
    GetAllDealRoomsPayload,
    GetAllDealRoomsResponse,
    GetDealroomOpportunityPayload,
    GetDealroomOpportunityResponse,
    GetDealRoomPayload,
    GetDealRoomResponse,
    LinkPreviewPayload,
    LinkPreviewResponse,
    SyncDealRoomWithTemplatePayload,
    SyncDealRoomWithTemplateResponse,
    UpdateBlockPayload,
    UpdateBlockResponse,
    UpdateDealRoomPagePayload,
    UpdateDealRoomPageResponse,
    UpdateDealRoomPayload,
    UpdateDealRoomResponse,
    UpdatePagesPayload,
    UpdatePagesResponse,
)
from ..services import (
    clone_deal_room,
    create_deal_room_from_template,
    delete_deal_room,
    fetch_link_preview,
    get_deal_room_by_id,
    get_dealroom_opportunity_for_client,
    list_all_deal_rooms,
    sync_deal_room_with_template,
    update_block_to_deal_room,
    update_deal_room_data,
    update_page_to_deal_room,
    update_pages_to_deal_room,
)

dealrooms_router = Router(tags=["dealrooms"])


def handle_dealroom_errors(e: Exception, api_name: str):
    if isinstance(e, DealRoomBadRequestError):
        logging.exception(f"{api_name} - Bad request error", exc_info=e)
        return HttpError(e.status.value, str(e))
    if isinstance(e, DealRoomNotFoundError):
        logging.exception(f"{api_name} - Not found error", exc_info=e)
        return HttpError(e.status.value, str(e))
    if isinstance(e, DealRoomTemplateNotFoundError):
        logging.exception(f"{api_name} - Template not found error", exc_info=e)
        return HttpError(e.status.value, str(e))
    if isinstance(e, json.JSONDecodeError):
        logging.exception(f"{api_name} - Malformed JSON in request", exc_info=e)
        return HttpError(
            status.HTTP_400_BAD_REQUEST, "Malformed JSON in request payload"
        )

    logging.exception(f"{api_name} - Unexpected error", exc_info=e)
    return HttpError(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@dealrooms_router.post(
    "get_dealroom_opportunity", response=GetDealroomOpportunityResponse
)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_dealroom_opportunity(
    request, payload: GetDealroomOpportunityPayload
) -> GetDealroomOpportunityResponse:
    try:
        client_id = request.client_id
        limit = payload.limit_value
        offset = payload.offset_value
        res = get_dealroom_opportunity_for_client(client_id, limit, offset)
        return GetDealroomOpportunityResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "get_dealroom_opportunity") from e


@dealrooms_router.post("create_dealroom", response=CreateDealRoomResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def create_dealrooms(
    request, payload: Form[CreateDealRoomPayload]
) -> CreateDealRoomResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        customer_logo_files = request.FILES
        email = str(request.user)
        deal_room_template_id = payload.deal_room_template_id
        deal_room_name = payload.deal_room_name
        deal_room_opportunity_id = payload.deal_room_opportunity_id
        deal_room_opportunity_name = payload.deal_room_opportunity_name
        deal_room_account_id = payload.account_id
        deal_room_owner = payload.deal_room_owner
        customer_logo = json.loads(payload.customer_logo)
        res = create_deal_room_from_template(
            client_id=client_id,
            email=email,
            customer_logo_files=customer_logo_files,
            customer_logo=customer_logo,
            deal_room_template_id=deal_room_template_id,
            deal_room_name=deal_room_name,
            deal_room_opportunity_id=deal_room_opportunity_id,
            deal_room_opportunity_name=deal_room_opportunity_name,
            deal_room_owner=deal_room_owner,
            deal_room_account_id=deal_room_account_id,
        )
        return CreateDealRoomResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "create_dealrooms") from e


@dealrooms_router.post("clone_dealroom", response=CloneDealRoomResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def clone_dealrooms(request, payload: CloneDealRoomPayload) -> CloneDealRoomResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_id = payload.deal_room_id
        res = clone_deal_room(client_id, deal_room_id, email)
        return CloneDealRoomResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "clone_dealrooms") from e


@dealrooms_router.post("update_pages", response=UpdatePagesResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def update_pages(request, payload: UpdatePagesPayload) -> UpdatePagesResponse:
    try:
        client_id = request.client_id
        deal_room_id = payload.deal_room_id
        deal_room_spec = payload.deal_room_page_spec
        email = str(request.user)
        res = update_pages_to_deal_room(
            client_id=client_id,
            email=email,
            deal_room_id=deal_room_id,
            deal_room_spec=deal_room_spec,
        )
        return UpdatePagesResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "update_pages_and_blocks") from e


@dealrooms_router.post("update_block", response=UpdateBlockResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def update_block(request, payload: Form[UpdateBlockPayload]) -> UpdateBlockResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_block_files = request.FILES
        deal_room_id = payload.deal_room_id
        deal_room_page_id = payload.deal_room_page_id
        deal_room_page_block_id = payload.deal_room_page_block_id
        deal_room_page_block = (
            json.loads(payload.deal_room_page_block)
            if payload.deal_room_page_block
            else None
        )
        deal_room_page_block_order = json.loads(payload.deal_room_page_block_order)
        res = update_block_to_deal_room(
            client_id=client_id,
            email=email,
            deal_room_id=deal_room_id,
            deal_room_block_files=deal_room_block_files,
            deal_room_page_id=deal_room_page_id,
            deal_room_page_block_id=deal_room_page_block_id,
            deal_room_page_block=deal_room_page_block,
            deal_room_page_block_order=deal_room_page_block_order,
        )
        return UpdateBlockResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "update_block") from e


@dealrooms_router.post("get_dealroom", response=GetDealRoomResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_dealroom(request, payload: GetDealRoomPayload) -> GetDealRoomResponse:
    try:
        client_id = request.client_id
        deal_room_id = payload.deal_room_id
        res = get_deal_room_by_id(client_id, deal_room_id)
        return GetDealRoomResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "get_dealroom") from e


@dealrooms_router.post("get_all_dealrooms", response=GetAllDealRoomsResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_all_dealrooms(
    request, payload: GetAllDealRoomsPayload
) -> GetAllDealRoomsResponse:
    try:
        client_id = request.client_id
        status = payload.status
        sort_by = payload.sort_by
        res = list_all_deal_rooms(client_id, sort_by, status)
        return GetAllDealRoomsResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "get_all_dealrooms") from e


@dealrooms_router.post("update_dealroom", response=UpdateDealRoomResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def update_dealroom(
    request, payload: Form[UpdateDealRoomPayload]
) -> UpdateDealRoomResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_id = payload.deal_room_id
        deal_room_name = payload.deal_room_name
        deal_room_status = payload.deal_room_status
        deal_room_opportunity_id = payload.deal_room_opportunity_id
        deal_room_opportunity_name = payload.deal_room_opportunity_name
        deal_room_account_id = payload.account_id
        deal_room_owner = payload.deal_room_owner
        customer_logo = (
            json.loads(payload.customer_logo) if payload.customer_logo else None
        )
        customer_logo_files = request.FILES
        res = update_deal_room_data(
            client_id=client_id,
            deal_room_id=deal_room_id,
            email=email,
            customer_logo_files=customer_logo_files,
            customer_logo=customer_logo,
            deal_room_name=deal_room_name,
            deal_room_status=deal_room_status,
            deal_room_opportunity_id=deal_room_opportunity_id,
            deal_room_opportunity_name=deal_room_opportunity_name,
            deal_room_account_id=deal_room_account_id,
            deal_room_owner=deal_room_owner,
        )
        return UpdateDealRoomResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "update_dealroom") from e


@dealrooms_router.post("delete_dealroom", response=DeleteDealRoomResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def delete_dealroom(request, payload: DeleteDealRoomPayload) -> DeleteDealRoomResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_id = payload.deal_room_id
        res = delete_deal_room(client_id, deal_room_id, email)
        return DeleteDealRoomResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "delete_dealroom") from e


@dealrooms_router.post("update_page", response=UpdateDealRoomPageResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def update_page(
    request, payload: UpdateDealRoomPagePayload
) -> UpdateDealRoomPageResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_id = payload.deal_room_id
        deal_room_page_id = payload.deal_room_page_id
        deal_room_page_spec = payload.deal_room_page_spec
        res = update_page_to_deal_room(
            client_id=client_id,
            email=email,
            deal_room_id=deal_room_id,
            deal_room_page_id=deal_room_page_id,
            deal_room_page_spec=deal_room_page_spec,
        )
        return UpdateDealRoomPageResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "update_page") from e


@dealrooms_router.post(
    "sync_dealroom_with_template", response=SyncDealRoomWithTemplateResponse
)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def sync_dealroom_with_template(
    request, payload: SyncDealRoomWithTemplatePayload
) -> SyncDealRoomWithTemplateResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_id = payload.deal_room_id
        res = sync_deal_room_with_template(client_id, deal_room_id, email)
        return SyncDealRoomWithTemplateResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "sync_dealroom_with_template") from e


@dealrooms_router.post("link_preview", response=LinkPreviewResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def get_link_preview(request, payload: LinkPreviewPayload) -> LinkPreviewResponse:
    try:
        client_id = request.client_id
        url = payload.url
        logging.info(f"Fetching link preview for client_id={client_id}, url={url}")
        res = fetch_link_preview(url)
        return LinkPreviewResponse(**res)
    except Exception as e:
        raise handle_dealroom_errors(e, "get_link_preview") from e
