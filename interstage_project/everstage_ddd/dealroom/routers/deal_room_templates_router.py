import json
import logging

from django.db import transaction
from ninja import Form, Router
from ninja.decorators import decorate_view
from ninja.errors import HttpError
from rest_framework import status

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope

from ..exceptions import DealRoomTemplateBadRequestError, DealRoomTemplateNotFoundError
from ..schemas.deal_room_template_schema import (
    CloneDealRoomTemplatePayload,
    CloneDealRoomTemplateResponse,
    CreateDealRoomTemplatePayload,
    CreateDealRoomTemplateResponse,
    DeleteDealRoomTemplatePayload,
    DeleteDealRoomTemplateResponse,
    GetAllDealRoomTemplatesPayload,
    GetAllDealRoomTemplatesResponse,
    GetDealRoomTemplatePayload,
    GetDealRoomTemplateResponse,
    UpdateBlockPayload,
    UpdateBlockResponse,
    UpdateDealRoomTemplatePayload,
    UpdateDealRoomTemplateResponse,
    UpdatePagePayload,
    UpdatePageResponse,
    UpdatePagesPayload,
    UpdatePagesResponse,
)
from ..services import (
    clone_deal_room_template,
    create_deal_room_template,
    delete_deal_room_template,
    get_deal_room_template_by_id,
    list_all_deal_room_templates,
    update_block_to_deal_room_template,
    update_deal_room_template_data,
    update_page_to_deal_room_template,
    update_pages_to_deal_room_template,
)

dealroom_templates_router = Router(tags=["dealroom_templates"])


def handle_template_errors(e: Exception, api_name: str):
    if isinstance(e, DealRoomTemplateBadRequestError):
        logging.exception(f"{api_name} - Bad request error", exc_info=e)
        return HttpError(e.status.value, str(e))

    if isinstance(e, DealRoomTemplateNotFoundError):
        logging.exception(f"{api_name} - Not found error", exc_info=e)
        return HttpError(e.status.value, str(e))

    if isinstance(e, json.JSONDecodeError):
        logging.exception(f"{api_name} - Malformed JSON in request", exc_info=e)
        return HttpError(
            status.HTTP_400_BAD_REQUEST, "Malformed JSON in request payload"
        )
    logging.exception(f"{api_name} - Unexpected error", exc_info=e)
    return HttpError(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@dealroom_templates_router.post(
    "create_template", response=CreateDealRoomTemplateResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def create_templates(
    request, payload: CreateDealRoomTemplatePayload
) -> CreateDealRoomTemplateResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_template_name = payload.deal_room_template_name
        deal_room_template_description = payload.deal_room_template_description

        res = create_deal_room_template(
            client_id,
            email,
            deal_room_template_name,
            deal_room_template_description,
        )
        return CreateDealRoomTemplateResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "create_templates") from e


@dealroom_templates_router.post(
    "clone_template", response=CloneDealRoomTemplateResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def clone_templates(
    request, payload: CloneDealRoomTemplatePayload
) -> CloneDealRoomTemplateResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_template_id = payload.deal_room_template_id
        res = clone_deal_room_template(
            client_id,
            deal_room_template_id,
            email,
        )
        return CloneDealRoomTemplateResponse(**res)

    except Exception as e:
        raise handle_template_errors(e, "clone_templates") from e


@dealroom_templates_router.post("update_pages", response=UpdatePagesResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def update_pages(request, payload: UpdatePagesPayload) -> UpdatePagesResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_template_id = payload.deal_room_template_id
        deal_room_template_spec = payload.deal_room_page_spec
        res = update_pages_to_deal_room_template(
            client_id=client_id,
            email=email,
            deal_room_template_id=deal_room_template_id,
            deal_room_template_spec=deal_room_template_spec,
        )
        return UpdatePagesResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "add_spec_to_templates") from e


@dealroom_templates_router.post("update_block", response=UpdateBlockResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def update_block(request, payload: Form[UpdateBlockPayload]) -> UpdateBlockResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        template_block_files = request.FILES
        deal_room_template_id = payload.deal_room_template_id
        deal_room_template_page_id = payload.deal_room_template_page_id
        deal_room_template_page_block_id = payload.deal_room_template_page_block_id
        deal_room_template_page_block = (
            json.loads(payload.deal_room_template_page_block)
            if payload.deal_room_template_page_block
            else None
        )
        deal_room_template_page_block_order = json.loads(
            payload.deal_room_template_page_block_order
        )

        res = update_block_to_deal_room_template(
            client_id=client_id,
            email=email,
            deal_room_template_id=deal_room_template_id,
            template_block_files=template_block_files,
            deal_room_template_page_id=deal_room_template_page_id,
            deal_room_template_page_block_id=deal_room_template_page_block_id,
            deal_room_template_page_block=deal_room_template_page_block,
            deal_room_template_page_block_order=deal_room_template_page_block_order,
        )
        return UpdateBlockResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "update_block") from e


@dealroom_templates_router.post("get_template", response=GetDealRoomTemplateResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_template(
    request, payload: GetDealRoomTemplatePayload
) -> GetDealRoomTemplateResponse:
    try:
        client_id = request.client_id
        deal_room_template_id = payload.deal_room_template_id
        res = get_deal_room_template_by_id(client_id, deal_room_template_id)
        return GetDealRoomTemplateResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "get_templates") from e


@dealroom_templates_router.post(
    "get_all_templates", response=GetAllDealRoomTemplatesResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def get_all_templates(
    request, payload: GetAllDealRoomTemplatesPayload
) -> GetAllDealRoomTemplatesResponse:
    try:
        client_id = request.client_id
        sort_by = payload.sort_by
        status = payload.status
        res = list_all_deal_room_templates(client_id, sort_by, status)
        return GetAllDealRoomTemplatesResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "get_all_templates") from e


@dealroom_templates_router.post(
    "update_template", response=UpdateDealRoomTemplateResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def update_deal_room_template(
    request, payload: UpdateDealRoomTemplatePayload
) -> UpdateDealRoomTemplateResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_template_id = payload.deal_room_template_id
        deal_room_template_name = payload.deal_room_template_name
        deal_room_template_status = payload.deal_room_template_status
        deal_room_template_description = payload.deal_room_template_description
        res = update_deal_room_template_data(
            client_id=client_id,
            email=email,
            deal_room_template_id=deal_room_template_id,
            deal_room_template_name=deal_room_template_name,
            deal_room_template_status=deal_room_template_status,
            deal_room_template_description=deal_room_template_description,
        )
        return UpdateDealRoomTemplateResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "update_deal_room_template") from e


@dealroom_templates_router.post(
    "delete_template", response=DeleteDealRoomTemplateResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def delete_templates(
    request, payload: DeleteDealRoomTemplatePayload
) -> DeleteDealRoomTemplateResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_template_id = payload.deal_room_template_id
        res = delete_deal_room_template(client_id, deal_room_template_id, email)
        return DeleteDealRoomTemplateResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "delete_templates") from e


@dealroom_templates_router.post("update_page", response=UpdatePageResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CPQ_SETTINGS.value))
@transaction.atomic
def update_page(request, payload: UpdatePagePayload) -> UpdatePageResponse:
    try:
        client_id = request.client_id
        email = str(request.user)
        deal_room_template_id = payload.deal_room_template_id
        deal_room_template_page_id = payload.deal_room_template_page_id
        deal_room_template_page_spec = payload.deal_room_template_page_spec

        res = update_page_to_deal_room_template(
            client_id=client_id,
            email=email,
            deal_room_template_id=deal_room_template_id,
            deal_room_template_page_id=deal_room_template_page_id,
            deal_room_template_page_spec=deal_room_template_page_spec,
        )
        return UpdatePageResponse(**res)
    except Exception as e:
        raise handle_template_errors(e, "update_page") from e
