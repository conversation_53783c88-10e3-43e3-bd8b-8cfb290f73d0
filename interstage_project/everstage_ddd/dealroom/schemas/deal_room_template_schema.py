from typing import Any, Dict, List, Optional

from ninja import Schema
from pydantic import BaseModel, field_validator, model_validator


class DealRoomTemplatePageSpec(BaseModel):
    pages: Dict[str, Any]
    pages_order: List[str]

    @model_validator(mode="before")
    def validate_pages_and_blocks(cls, values):  # noqa: N805, PLR0912
        pages = values.get("pages", {})
        required_keys = {"header", "welcome_page"}
        page_keys = set(pages.keys())

        # Step 1: Required page keys
        missing = required_keys - page_keys
        if missing:
            raise ValueError(f"Missing required pages: {missing}")

        # Step 2: Dynamic page keys should start with 'page_'
        for key in page_keys - required_keys:
            if not key.startswith("page_"):
                raise ValueError(f"Invalid page key '{key}': must start with 'page_'")

        # Step 3: Validate blocks inside each page
        for page_key, page_content in pages.items():
            blocks = page_content.get("blocks")
            blocks_order = page_content.get("blocks_order")

            if blocks is not None or blocks_order is not None:
                if blocks is None or blocks_order is None:
                    raise ValueError(
                        f"For page '{page_key}', both 'blocks' and 'blocks_order' must be present if either is."
                    )

                if not isinstance(blocks, dict):
                    raise ValueError(f"'{page_key}.blocks' must be a dictionary.")

                if not isinstance(blocks_order, list):
                    raise ValueError(f"'{page_key}.blocks_order' must be a list.")
                # Check block keys
                for block_key in blocks:
                    if not block_key.startswith("block_"):
                        raise ValueError(
                            f"Invalid block key '{block_key}' in page '{page_key}': must start with 'block_'"
                        )

                # Check all block keys are in blocks_order
                block_keys = set(blocks.keys())
                blocks_order_set = set(blocks_order)

                missing_in_order = block_keys - blocks_order_set
                if missing_in_order:
                    raise ValueError(
                        f"In page '{page_key}', the following blocks are missing in blocks_order: {missing_in_order}"
                    )

                # Check blocks_order doesn't contain unknown blocks
                extra_in_order = blocks_order_set - block_keys
                if extra_in_order:
                    raise ValueError(
                        f"In page '{page_key}', blocks_order has unknown blocks not in blocks: {extra_in_order}"
                    )

                # Check blocks_order has no duplicates
                if len(blocks_order) != len(set(blocks_order)):
                    raise ValueError(
                        f"In page '{page_key}', blocks_order contains duplicate entries."
                    )

        return values

    @model_validator(mode="before")
    def validate_pages_order(cls, values):  # noqa: N805
        pages = values.get("pages", {})
        pages_order = values.get("pages_order", [])

        dynamic_keys = {k for k in pages if k.startswith("page_")}
        missing_in_order = [k for k in dynamic_keys if k not in pages_order]
        if missing_in_order:
            raise ValueError(
                f"The following page_ keys are missing in pages_order: {missing_in_order}"
            )
            # Ensure no duplicate pages in order
        if len(pages_order) != len(set(pages_order)):
            raise ValueError("pages_order contains duplicate entries.")

        return values


class CreateDealRoomTemplatePayload(Schema):
    deal_room_template_name: str
    deal_room_template_description: Optional[str] = None


class CloneDealRoomTemplatePayload(Schema):
    deal_room_template_id: str


class UpdatePagesPayload(Schema):
    deal_room_template_id: str
    deal_room_page_spec: Optional[Dict[str, Any]] = None

    @field_validator("deal_room_page_spec", mode="before")
    @classmethod
    def validate_spec(cls, value):
        # If provided, validate it using nested Pydantic model
        if value is not None:
            DealRoomTemplatePageSpec(**value)  # Will raise on invalid data
        return value  # Keep it a dict for easy JSON serialization


class GetDealRoomTemplatePayload(Schema):
    deal_room_template_id: str


class GetAllDealRoomTemplatesPayload(Schema):
    sort_by: Optional[str] = None
    status: Optional[str] = None


class UpdateDealRoomTemplatePayload(Schema):
    deal_room_template_id: str
    deal_room_template_name: Optional[str] = None
    deal_room_template_status: Optional[str] = None
    deal_room_template_description: Optional[str] = None


class DeleteDealRoomTemplatePayload(Schema):
    deal_room_template_id: str


class UpdateBlockPayload(Schema):
    deal_room_template_id: str
    deal_room_template_page_id: str
    deal_room_template_page_block_id: str
    deal_room_template_page_block: Optional[str] = None
    deal_room_template_page_block_order: str


class DealRoomTemplateBasicResponse(Schema):
    status: str
    deal_room_template_id: str


class CreateDealRoomTemplateResponse(DealRoomTemplateBasicResponse):
    pass


class CloneDealRoomTemplateResponse(DealRoomTemplateBasicResponse):
    pass


class UpdatePagesResponse(DealRoomTemplateBasicResponse):
    pass


class UpdateBlockResponse(DealRoomTemplateBasicResponse):
    pass


class UpdateDealRoomTemplateResponse(DealRoomTemplateBasicResponse):
    pass


class DeleteDealRoomTemplateResponse(DealRoomTemplateBasicResponse):
    pass


class GetDealRoomTemplateResponse(Schema):
    status: str
    deal_room_template_id: str
    deal_room_template_name: str
    deal_room_template_status: str
    deal_room_template_description: str
    deal_room_page_spec: Dict[str, Any] = {}


class DealRoomTemplateListItem(Schema):
    deal_room_template_id: str
    deal_room_template_name: str
    deal_room_template_status: str
    deal_room_template_description: str
    owner: str
    owner_email_id: str


class GetAllDealRoomTemplatesResponse(Schema):
    status: str
    deal_room_templates: List[DealRoomTemplateListItem]


class UpdatePagePayload(Schema):
    deal_room_template_id: str
    deal_room_template_page_id: str
    deal_room_template_page_spec: Optional[Dict[str, Any]] = None


class UpdatePageResponse(DealRoomTemplateBasicResponse):
    pass
