from http import HTTPStatus

from everstage_ddd.common import BaseError


class DealRoomTemplateBadRequestError(BaseError):
    def __init__(self, message) -> None:
        code = "DEAL_ROOM_TEMPLATE_BAD_REQUEST"
        super().__init__(code=code, message=message, status=HTTPStatus.BAD_REQUEST)


class DealRoomTemplateNotFoundError(BaseError):
    def __init__(self, message) -> None:
        code = "DEAL_ROOM_TEMPLATE_NOT_FOUND"
        super().__init__(code=code, message=message, status=HTTPStatus.NOT_FOUND)


class DealRoomBadRequestError(BaseError):
    def __init__(self, message) -> None:
        code = "DEAL_ROOM_BAD_REQUEST"
        super().__init__(code=code, message=message, status=HTTPStatus.BAD_REQUEST)


class DealRoomNotFoundError(BaseError):
    def __init__(self, message) -> None:
        code = "DEAL_ROOM_NOT_FOUND"
        super().__init__(code=code, message=message, status=HTTPStatus.NOT_FOUND)
