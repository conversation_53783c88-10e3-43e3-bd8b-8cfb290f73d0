from unittest.mock import patch

import pytest

from everstage_ddd.datasheet_builder.constants import SheetBuilderStatus, SkillTags
from everstage_ddd.datasheet_builder.exceptions import DatasheetBuilderError
from everstage_ddd.datasheet_builder.services.datasheet_builder_service import (
    create_datasheets_using_agent,
)


@pytest.fixture
def mock_mannai_interface():
    with patch(
        "everstage_ddd.datasheet_builder.services.datasheet_builder_service.mannai_interface"
    ) as mock:
        yield mock


@pytest.fixture
def mock_utils():
    with patch(
        "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils"
    ) as mock:
        yield mock


@pytest.fixture
def sample_params():
    return {
        "client_id": 123,
        "params": {
            "databook_id": 456,
            "databook_name": "Test Databook",
            "nodes": [
                {
                    "id": "node1",
                    "data": {
                        "fields": [{"name": "field1", "is_selected": True}],
                        "label": "Node 1",
                        "node_order": 0,
                    },
                },
                {
                    "id": "node2",
                    "data": {
                        "fields": [{"name": "field2", "is_selected": True}],
                        "label": "Node 2",
                        "node_order": 1,
                    },
                },
            ],
        },
        "client_info": {"email": "<EMAIL>", "auth": "test_auth"},
    }


@pytest.fixture
def sample_params_with_level_1():
    """Sample params with nodes that will be processed (level 1 and above)"""
    return {
        "client_id": 123,
        "params": {
            "databook_id": 456,
            "databook_name": "Test Databook",
            "nodes": [
                {
                    "id": "node1",
                    "data": {
                        "fields": [{"name": "field1", "is_selected": True}],
                        "label": "Node 1",
                        "node_order": 1,
                    },
                },
                {
                    "id": "node2",
                    "data": {
                        "fields": [{"name": "field2", "is_selected": True}],
                        "label": "Node 2",
                        "node_order": 2,
                    },
                },
            ],
        },
        "client_info": {"email": "<EMAIL>", "auth": "test_auth"},
    }


class TestCreateDatasheetsUsingAgent:
    def test_create_datasheets_using_agent_success(
        self, mock_mannai_interface, mock_utils, sample_params_with_level_1
    ):
        # Arrange
        mock_utils.group_nodes_by_order.return_value = {
            0: [],  # Level 0 nodes are skipped
            1: [sample_params_with_level_1["params"]["nodes"][0]],
            2: [sample_params_with_level_1["params"]["nodes"][1]],
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )
        mock_mannai_interface.execute_skill.return_value = (
            {
                "ai_generated_content": {
                    "json_response": {"status": SheetBuilderStatus.SUCCESS.value}
                }
            },
            SheetBuilderStatus.SUCCESS.value,
        )

        # Act
        create_datasheets_using_agent(**sample_params_with_level_1)

        # Assert
        mock_utils.group_nodes_by_order.assert_called_once_with(
            sample_params_with_level_1["params"]["nodes"]
        )
        assert (
            mock_utils.construct_prompt_for_datasheet_creation_agents.call_count == 2
        )  # Called for both level 1 and 2
        assert (
            mock_mannai_interface.execute_skill.call_count == 2
        )  # Called for both nodes

    def test_create_datasheets_using_agent_v2(
        self, mock_mannai_interface, mock_utils, sample_params_with_level_1
    ):
        # Arrange
        mock_utils.group_nodes_by_order.return_value = {
            0: [],  # Level 0 nodes are skipped
            1: [sample_params_with_level_1["params"]["nodes"][0]],
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )
        mock_mannai_interface.execute_skill.return_value = (
            {
                "ai_generated_content": {
                    "json_response": {"status": SheetBuilderStatus.SUCCESS.value}
                }
            },
            SheetBuilderStatus.SUCCESS.value,
        )

        # Act
        create_datasheets_using_agent(
            **sample_params_with_level_1, show_data_sources_v2=True
        )

        # Assert
        mock_utils.group_nodes_by_order.assert_called_once_with(
            sample_params_with_level_1["params"]["nodes"]
        )
        assert mock_utils.construct_prompt_for_datasheet_creation_agents.call_count == 1
        assert mock_mannai_interface.execute_skill.call_count == 1
        mock_mannai_interface.execute_skill.assert_called_with(
            client_id=sample_params_with_level_1["client_id"],
            skill_tag=SkillTags.DATASHEET_CREATION_AGENT_V2.value,
            employee_email_id=sample_params_with_level_1["client_info"]["email"],
            user_prompt="test prompt",
            return_result=True,
        )

    def test_create_datasheets_using_agent_retry_success(
        self, mock_mannai_interface, mock_utils
    ):
        # Arrange - Create a node with node_order=0 but in level 1 to test retry logic
        params_with_retry_node = {
            "client_id": 123,
            "params": {
                "databook_id": 456,
                "databook_name": "Test Databook",
                "nodes": [
                    {
                        "id": "node1",
                        "data": {
                            "fields": [{"name": "field1", "is_selected": True}],
                            "label": "Node 1",
                            "node_order": -1,
                        },
                    },
                    {
                        "id": "node1",
                        "data": {
                            "fields": [{"name": "field1", "is_selected": True}],
                            "label": "Node 1",
                            "node_order": 0,
                        },
                    },
                ],
            },
            "client_info": {"email": "<EMAIL>", "auth": "test_auth"},
        }

        # The key issue: we need to use the node from params_with_retry_node, not sample_params_with_level_1
        mock_utils.group_nodes_by_order.return_value = {
            0: [params_with_retry_node["params"]["nodes"][0]],
            1: [params_with_retry_node["params"]["nodes"][1]],
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )
        mock_mannai_interface.execute_skill.side_effect = [
            (
                {"ai_generated_content": {"json_response": {"status": "failed"}}},
                "Failure",
            ),
            (
                {
                    "ai_generated_content": {
                        "json_response": {"status": SheetBuilderStatus.SUCCESS.value}
                    }
                },
                "Success",
            ),
        ]

        # Act
        create_datasheets_using_agent(**params_with_retry_node)

        # Assert
        assert mock_mannai_interface.execute_skill.call_count == 2

    def test_create_datasheets_using_agent_max_retries_exceeded(
        self, mock_mannai_interface, mock_utils
    ):
        # Arrange - Create a node that will be processed and fail all retries
        params_with_failing_node = {
            "client_id": 123,
            "params": {
                "databook_id": 456,
                "databook_name": "Test Databook",
                "nodes": [
                    {
                        "id": "node1",
                        "data": {
                            "fields": [{"name": "field1", "is_selected": True}],
                            "label": "Node 1",
                            "node_order": 1,  # This triggers 1 retry (non-zero node_order)
                        },
                    }
                ],
            },
            "client_info": {"email": "<EMAIL>", "auth": "test_auth"},
        }

        mock_utils.group_nodes_by_order.return_value = {
            0: [params_with_failing_node["params"]["nodes"][0]],
            1: [params_with_failing_node["params"]["nodes"][0]],
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )
        mock_mannai_interface.execute_skill.return_value = (
            {"ai_generated_content": {"json_response": {"status": "failed"}}},
            "Failure",
        )

        # Act & Assert
        with pytest.raises(DatasheetBuilderError, match="Failed"):
            create_datasheets_using_agent(**params_with_failing_node)

    def test_create_datasheets_using_agent_empty_nodes(
        self, mock_mannai_interface, mock_utils, sample_params
    ):
        # Arrange - Return only level 0 nodes (which are skipped) or empty
        mock_utils.group_nodes_by_order.return_value = {
            0: [sample_params["params"]["nodes"][0]]  # Only level 0, which gets skipped
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )

        # Act
        create_datasheets_using_agent(**sample_params)

        # Assert
        mock_utils.group_nodes_by_order.assert_called_once_with(
            sample_params["params"]["nodes"]
        )
        mock_utils.construct_prompt_for_datasheet_creation_agents.assert_not_called()
        mock_mannai_interface.execute_skill.assert_not_called()

    def test_create_datasheets_using_agent_invalid_response(
        self, mock_mannai_interface, mock_utils
    ):
        # Arrange - Create a node that will be processed and return invalid response
        params_with_invalid_response = {
            "client_id": 123,
            "params": {
                "databook_id": 456,
                "databook_name": "Test Databook",
                "nodes": [
                    {
                        "id": "node1",
                        "data": {
                            "fields": [{"name": "field1", "is_selected": True}],
                            "label": "Node 1",
                            "node_order": 1,
                        },
                    }
                ],
            },
            "client_info": {"email": "<EMAIL>", "auth": "test_auth"},
        }

        mock_utils.group_nodes_by_order.return_value = {
            0: [params_with_invalid_response["params"]["nodes"][0]],
            1: [params_with_invalid_response["params"]["nodes"][0]],
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )
        mock_mannai_interface.execute_skill.return_value = (
            {"ai_generated_content": {"json_response": {"status": 123}}},
            "Success",
        )

        # Act & Assert
        with pytest.raises(DatasheetBuilderError, match="Failed"):
            create_datasheets_using_agent(**params_with_invalid_response)

    def test_create_datasheets_using_agent_json_string_response(
        self, mock_mannai_interface, mock_utils, sample_params_with_level_1
    ):
        # Arrange
        mock_utils.group_nodes_by_order.return_value = {
            0: [],
            1: [sample_params_with_level_1["params"]["nodes"][0]],
        }
        mock_utils.construct_prompt_for_datasheet_creation_agents.return_value = (
            "test prompt"
        )
        mock_mannai_interface.execute_skill.return_value = (
            {"ai_generated_content": {"json_response": '{"status": "success"}'}},
            "success",
        )

        # Act
        create_datasheets_using_agent(**sample_params_with_level_1)

        # Assert
        mock_utils.group_nodes_by_order.assert_called_once_with(
            sample_params_with_level_1["params"]["nodes"]
        )
        mock_utils.construct_prompt_for_datasheet_creation_agents.assert_called_once()
        mock_mannai_interface.execute_skill.assert_called_once()
