from unittest.mock import patch

import pytest

from everstage_ddd.datasheet_builder.constants import SheetBuilderStatus
from everstage_ddd.datasheet_builder.exceptions import DatasheetBuilderError
from everstage_ddd.datasheet_builder.models import GenerateNodesParams
from everstage_ddd.datasheet_builder.services.datasheet_builder_service import (
    generate_nodes_service,
)

# ruff: noqa: PLR0913


def make_node(
    node_id,
    node_order,
    label="TestObj",
    fields=None,
    primary_key=None,
    transformations=None,
    sources=None,
):
    if fields is None:
        fields = [
            {
                "name": "Id",
                "display_name": "Id",
                "object_id": node_id,
                "type": "String",
                "is_primary_key": True,
                "is_selected": True,
            }
        ]
    if primary_key is None:
        primary_key = ["Id"]
    data = {
        "label": label,
        "alias_name": "A",
        "nodeOrder": node_order,
        "destination_name": label,
        "fields": fields,
        "primary_key": primary_key,
    }
    if transformations is not None:
        data["transformations"] = transformations
    if sources is not None:
        data["sources"] = sources
    return {
        "id": node_id,
        "type": "tableNode",
        "position": {"x": 0, "y": 0},
        "data": data,
    }


@pytest.fixture
def generate_nodes_input():
    return GenerateNodesParams(
        client_id=1,
        user_prompt="Test prompt",
        nodes=[make_node("1", 0)],
        edges=[],
        access_token_config_id=123,
        quickpicks=[],
        email_id="<EMAIL>",
        service_name="service",
        report_data=None,
        is_report_builder=False,
    )


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_nodeorder_0(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [make_node("1", 0)]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {
            "json_response": {
                "outputData": [{"operationType": "ADD_NODE", "node": make_node("1", 0)}]
            },
            "str_response": "dummy text",
        },
        SheetBuilderStatus.SUCCESS.value,
    )
    mock_parse_node_operations.return_value = {
        "outputData": [{"operationType": "ADD_NODE", "node": make_node("1", 0)}]
    }
    mock_generate_nodes_for_sheet_builder.return_value = [
        {"operationType": "ADD_NODE", "node": make_node("1", 0)}
    ]
    result = generate_nodes_service(generate_nodes_input)
    assert result["status"] == SheetBuilderStatus.SUCCESS.value
    assert result["data"][0]["node"]["data"]["nodeOrder"] == 0


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_nodeorder_1_join(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    join_transformation = [
        {"type": "JOIN", "left_field": "Id", "right_field": "Id", "join_type": "INNER"}
    ]
    node = make_node("2", 1, transformations=join_transformation, sources=["1"])
    mock_filter_selected_fields.return_value = [node]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {
            "json_response": {
                "outputData": [{"operationType": "ADD_NODE", "node": node}]
            },
            "str_response": "dummy text",
        },
        SheetBuilderStatus.SUCCESS.value,
    )
    mock_parse_node_operations.return_value = {
        "outputData": [{"operationType": "ADD_NODE", "node": node}]
    }
    mock_generate_nodes_for_sheet_builder.return_value = [
        {"operationType": "ADD_NODE", "node": node}
    ]
    params = generate_nodes_input
    params.nodes = [node]
    result = generate_nodes_service(params)
    assert result["status"] == SheetBuilderStatus.SUCCESS.value
    assert result["data"][0]["node"]["data"]["nodeOrder"] == 1
    assert result["data"][0]["node"]["data"]["transformations"][0]["type"] == "JOIN"


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_nodeorder_2_filter(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    filter_transformation = [
        {"type": "FILTER", "field": "Id", "operator": "EQUALS", "value": "123"}
    ]
    node = make_node("3", 2, transformations=filter_transformation, sources=["2"])
    mock_filter_selected_fields.return_value = [node]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {
            "json_response": {
                "outputData": [{"operationType": "ADD_NODE", "node": node}]
            },
            "str_response": "dummy text",
        },
        SheetBuilderStatus.SUCCESS.value,
    )
    mock_parse_node_operations.return_value = {
        "outputData": [{"operationType": "ADD_NODE", "node": node}]
    }
    mock_generate_nodes_for_sheet_builder.return_value = [
        {"operationType": "ADD_NODE", "node": node}
    ]
    params = generate_nodes_input
    params.nodes = [node]
    result = generate_nodes_service(params)
    assert result["status"] == SheetBuilderStatus.SUCCESS.value
    assert result["data"][0]["node"]["data"]["nodeOrder"] == 2
    assert result["data"][0]["node"]["data"]["transformations"][0]["type"] == "FILTER"


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_nodeorder_2_aggregate(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    aggregate_transformation = [
        {"type": "AGGREGATE", "field": "Amount", "agg_func": "SUM"}
    ]
    node = make_node("4", 2, transformations=aggregate_transformation, sources=["2"])
    mock_filter_selected_fields.return_value = [node]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {
            "json_response": {
                "outputData": [{"operationType": "ADD_NODE", "node": node}]
            },
            "str_response": "dummy text",
        },
        SheetBuilderStatus.SUCCESS.value,
    )
    mock_parse_node_operations.return_value = {
        "outputData": [{"operationType": "ADD_NODE", "node": node}]
    }
    mock_generate_nodes_for_sheet_builder.return_value = [
        {"operationType": "ADD_NODE", "node": node}
    ]
    params = generate_nodes_input
    params.nodes = [node]
    result = generate_nodes_service(params)
    assert result["status"] == SheetBuilderStatus.SUCCESS.value
    assert result["data"][0]["node"]["data"]["nodeOrder"] == 2
    assert (
        result["data"][0]["node"]["data"]["transformations"][0]["type"] == "AGGREGATE"
    )


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_nodeorder_2_project(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    project_transformation = [{"type": "PROJECT", "fields": ["Id", "Amount"]}]
    node = make_node("5", 2, transformations=project_transformation, sources=["2"])
    mock_filter_selected_fields.return_value = [node]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {
            "json_response": {
                "outputData": [{"operationType": "ADD_NODE", "node": node}]
            },
            "str_response": "dummy text",
        },
        SheetBuilderStatus.SUCCESS.value,
    )
    mock_parse_node_operations.return_value = {
        "outputData": [{"operationType": "ADD_NODE", "node": node}]
    }
    mock_generate_nodes_for_sheet_builder.return_value = [
        {"operationType": "ADD_NODE", "node": node}
    ]
    params = generate_nodes_input
    params.nodes = [node]
    result = generate_nodes_service(params)
    assert result["status"] == SheetBuilderStatus.SUCCESS.value
    assert result["data"][0]["node"]["data"]["nodeOrder"] == 2
    assert result["data"][0]["node"]["data"]["transformations"][0]["type"] == "PROJECT"


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
def test_generate_nodes_service_failure(
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {"not_json_response": {}, "str_response": "dummy text"},
        "FAILED",
    )
    with pytest.raises(DatasheetBuilderError) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Something went wrong. Please try again later!" in str(exc.value)


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
def test_generate_nodes_service_no_json_key(
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {},
        SheetBuilderStatus.SUCCESS.value,
    )
    with pytest.raises(DatasheetBuilderError) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Something went wrong. Please try again later!" in str(exc.value)


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
def test_generate_nodes_service_llm_status_not_success(
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {"json_response": {"outputData": ["node_op"]}, "str_response": "dummy text"},
        "FAILED",
    )
    with pytest.raises(DatasheetBuilderError) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Something went wrong. Please try again later!" in str(exc.value)


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
def test_generate_nodes_service_success_status_but_json_missing(
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {},
        "success",
    )
    with pytest.raises(DatasheetBuilderError) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Something went wrong. Please try again later!" in str(exc.value)
    mock_generate_nodes_using_skill_with_retry.return_value = (
        None,
        "success",
    )
    with pytest.raises(DatasheetBuilderError) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Something went wrong. Please try again later!" in str(exc.value)


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_outputdata_missing(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {"json_response": {"foo": "bar"}, "str_response": "dummy text"},
        "success",
    )
    mock_parse_node_operations.return_value = {"foo": "bar"}
    with pytest.raises(KeyError):
        generate_nodes_service(generate_nodes_input)


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
def test_generate_nodes_service_utility_raises(
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.side_effect = Exception("Utility failed")
    with pytest.raises(Exception) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Utility failed" in str(exc.value)


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_outputdata_empty(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {"json_response": {"outputData": []}, "str_response": "dummy text"},
        "success",
    )
    mock_parse_node_operations.return_value = {"outputData": []}
    mock_generate_nodes_for_sheet_builder.return_value = []
    result = generate_nodes_service(generate_nodes_input)
    assert result["status"] == "success"
    assert result["data"] == []


@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.filter_selected_fields"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.construct_prompt_for_node_creation_agent"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.generate_nodes_using_skill_with_retry"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.utils.parse_node_operations"
)
@patch(
    "everstage_ddd.datasheet_builder.services.datasheet_builder_service.generate_nodes_for_sheet_builder"
)
def test_generate_nodes_service_generate_nodes_for_sheet_builder_raises(
    mock_generate_nodes_for_sheet_builder,
    mock_parse_node_operations,
    mock_generate_nodes_using_skill_with_retry,
    mock_construct_prompt,
    mock_filter_selected_fields,
    generate_nodes_input,
):
    mock_filter_selected_fields.return_value = [{"id": 1, "data": {}}]
    mock_construct_prompt.return_value = "constructed prompt"
    mock_generate_nodes_using_skill_with_retry.return_value = (
        {"json_response": {"outputData": ["node_op"]}, "str_response": "dummy text"},
        "success",
    )
    mock_parse_node_operations.return_value = {"outputData": ["node_op"]}
    mock_generate_nodes_for_sheet_builder.side_effect = Exception("Downstream error")
    with pytest.raises(Exception) as exc:
        generate_nodes_service(generate_nodes_input)
    assert "Downstream error" in str(exc.value)
