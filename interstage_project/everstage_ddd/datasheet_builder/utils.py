import json
import logging
from collections import defaultdict

from django.core.cache import cache

from commission_engine.third_party_connections.salesforce import Salesforce
from everstage_ddd.datasheet_builder.constants import (
    DEFAULT_SELECTED_SALESFORCE_FIELDS,
    SkillTags,
)
from everstage_ddd.datasheet_builder.exceptions import (
    JoinValidationFieldNotFoundError,
    JoinValidationTypeMismatchError,
    SalesforceReportError,
)
from everstage_ddd.datasheet_builder.models import SheetBuilderConfig
from everstage_ddd.llm_agent import mannai_interface

logger = logging.getLogger(__name__)

# Cache timeout in seconds (1 hour)
CACHE_TIMEOUT = 3600


def get_cached_sobject_fields(
    client_id: int, access_token_config_id: int, sobject: str
) -> dict:
    """
    Get Salesforce object fields from cache or fetch from API if not cached.

    Args:
        client_id: ID representing the Salesforce org client
        access_token_config_id: Config ID required for Salesforce API calls
        sobject: Salesforce object name

    Returns:
        dict: Object fields response from Salesforce API
    """
    cache_key = f"salesforce_sobject_fields_{client_id}_{access_token_config_id}_{sobject.lower()}"
    cached_response = cache.get(cache_key)

    if cached_response:
        return cached_response

    salesforce = Salesforce()
    response = salesforce.get_sobject_details(
        client_id,
        access_token_config_id,
        sobject,
        is_report_builder=True,
    )

    if response:
        cache.set(cache_key, response, CACHE_TIMEOUT)

    return response


def filter_selected_fields(nodes):
    """Filter nodes to only include selected fields."""
    for node in nodes:
        node["data"]["fields"] = [
            field for field in node["data"]["fields"] if field["is_selected"]
        ]
    return nodes


def compare_alias_names(name1, name2):
    """Compare two alias names lexicographically."""
    if len(name1) < len(name2):
        return -1
    elif len(name1) > len(name2):
        return 1

    if name1 < name2:
        return -1
    elif name1 > name2:
        return 1
    else:
        return 0


def get_highest_alias_name(alias_names):
    """Get the highest alias name from a set of alias names."""
    if not alias_names:
        return None

    highest = next(iter(alias_names))
    for name in alias_names:
        if compare_alias_names(highest, name) < 0:
            highest = name

    return highest


def get_next_alias_name(alias):
    """Generate the next alias name in sequence."""
    if not alias:
        return "A"

    chars = list(alias)
    pos = len(chars) - 1

    while pos >= 0:
        if chars[pos] < "Z":
            chars[pos] = chr(ord(chars[pos]) + 1)
            return "".join(chars)
        else:
            chars[pos] = "A"
            pos -= 1

    return "A" + "".join(chars)


def construct_prompt_for_node_creation_agent(
    user_prompt, nodes, edges, access_token_config_id, quickpicks
):
    """Construct prompt for node creation agent."""
    return f"""
    Goal/user-prompt: {user_prompt}
    Context:
    accessTokenConfigId: {access_token_config_id}
    nodes: {nodes}
    edges: {edges}
    Additional context use this to create only when needed:
    quickpicks: {[obj["label"] for obj in quickpicks]}
    """


def generate_nodes_using_skill_with_retry(
    client_id,
    user_prompt,
    email_id,
):
    """Generate nodes using skill with retry mechanism."""
    retry = 2
    while True:
        ai_generated_content, llm_status = mannai_interface.execute_skill(
            client_id=client_id,
            skill_tag=SkillTags.NODES_GENERATION_AGENT.value,
            employee_email_id=email_id,
            user_prompt=user_prompt,
            return_result=True,
        )
        if (
            "outputData"
            in ai_generated_content["ai_generated_content"]["json_response"]
            or retry == 0
        ):
            break
        retry -= 1
    return ai_generated_content["ai_generated_content"], llm_status


def construct_prompt_for_datasheet_creation_agents(
    current_step: int, node: dict, databook_id: int, databook_name: str
):
    """Construct prompt for datasheet creation agents."""
    return (
        f"This node below belongs to nodeOrder = {current_step}"
        f"node: {node}"
        f"databookId: {databook_id}"
        f"databookName: {databook_name}"
    )


def group_nodes_by_order(nodes: list[dict]):
    """Group nodes by their node order."""
    grouped_nodes = defaultdict(list)

    for node in nodes:
        node_order = node.get("data", {}).get("node_order")
        if node_order is not None:
            grouped_nodes[node_order].append(node)

    return dict(grouped_nodes)


def parse_node_operations(node_operations):
    """Parse node operations from string or dict format."""
    if isinstance(node_operations, str):
        return json.loads(node_operations)
    return node_operations


def combine_fields(parent_datasheets, nodes):
    """Combine fields from parent datasheets with node information."""
    nodes_by_label = {node["data"]["label"]: node for node in nodes}

    return {
        parent_datasheet: [
            {
                **field,
                "name": f"{parent_datasheet}::{field['name']}",
                "display_name": f"{parent_datasheet}::{field['display_name']}",
            }
            for field in nodes_by_label[parent_datasheet]["data"]["fields"]
            if field["is_selected"]
        ]
        for parent_datasheet in parent_datasheets
        if parent_datasheet in nodes_by_label
    }


def get_parent_datasheets(sources, nodes):
    """Get parent datasheet labels from source IDs."""
    return [
        node["data"]["label"]
        for source_id in sources
        for node in nodes
        if node["id"] == source_id
    ]


def set_selected_fields(field, report_fields=None, *, report_data=None, object_id):
    """Set whether a field is selected based on report data and default fields."""
    if report_data:
        report_fields = report_data.get("objects", {}).get(object_id) or {}
    else:
        report_fields = {}

    return (
        field["name"] in DEFAULT_SELECTED_SALESFORCE_FIELDS
        or field["type"] == "reference"
        or field["name"] in report_fields
    )


def remove_unused_fields_from_report(config: SheetBuilderConfig, node_data: dict):
    """Remove unused fields from report based on transformations."""
    transformations = node_data["transformations"]
    transformations_fields = {
        field
        for transformation in transformations
        for field in (transformation["lhsField"], transformation["rhsField"])
    }

    report_data = config.report_data or {}

    for fields in node_data["fields"].values():
        for field in fields:
            if field["name"] in transformations_fields:
                continue

            parent = field["object_id"]
            report_fields_dict = report_data.get("objects", {}).get(parent, {}) or {}
            field_split = field["name"].split("::")
            if len(field_split) != 2:  # noqa: PLR2004
                continue

            field_name = field_split[1]
            is_field_in_report = field_name in report_fields_dict

            if not is_field_in_report and not field["is_primary_key"]:
                field["is_selected"] = False


def validate_join_condition(
    transformation: dict,
    field_names: set,
    parent_datasheet_name: str,
):
    """Validate join condition by checking field existence and data type compatibility."""
    if transformation["lhsField"] not in field_names:
        raise JoinValidationFieldNotFoundError(
            {
                "field": transformation["lhsField"],
                "source_type": parent_datasheet_name,
            }
        )
    if transformation["rhsField"] not in field_names:
        raise JoinValidationFieldNotFoundError(
            {
                "field": transformation["rhsField"],
                "source_type": parent_datasheet_name,
            }
        )
    if transformation["lhsDataType"] != transformation["rhsDataType"]:
        raise JoinValidationTypeMismatchError()


def is_custom_report_type(report_metadata: dict) -> bool:
    """
    Check whether a Salesforce report is based on a custom report type.

    Args:
        report_metadata: Dict returned from report describe API under 'reportMetadata'.

    Returns:
        True if custom report type, False if standard report type.
    """
    report_type = report_metadata.get("reportMetadata", {}).get("reportType", {})
    report_type_api_name = report_type.get("type", "")

    # Custom Report Types usually end with "__c"
    return report_type_api_name.endswith("__c")


def extract_join_transformations(
    report_response: dict, extracted_sobject_details: dict
):
    """
    Extract object join hierarchy and join types from report metadata.

    Args:
        report_response: Full response from report describe API.

    Returns:
        Dict with:
            - objects: base objects
            - joins: list of joins with lhs, rhs, joinField, joinType
    """
    report_type_metadata = report_response.get("reportTypeMetadata", {})
    objects = report_type_metadata.get("objects", [])

    if not objects or objects[0].get("joinType") != "ROOT":
        raise SalesforceReportError(
            message="Missing root object in reportTypeMetadata.objects"
        )

    root_object = objects[0]["apiName"]
    crt_objects = {root_object}
    join_prompt = root_object
    extracted_sobject_details["objects"][root_object] = dict()

    for i in range(1, len(objects)):
        rhs_obj = objects[i]
        rhs_name = rhs_obj["apiName"]
        join_type = "INNER JOIN" if rhs_obj.get("joinType") == "INNER" else "LEFT JOIN"
        join_prompt = f"{join_prompt} {join_type} {rhs_name}"
        extracted_sobject_details["objects"][rhs_name] = dict()
        crt_objects.add(rhs_name)

    extracted_sobject_details["join_prompt"] = join_prompt
    return crt_objects


def extract_fields_info(  # noqa: PLR0912, PLR0915
    client_id: int,
    access_token_config_id: int,
    sreport_object_detail: dict,
    extracted_sobject_details: dict,
):
    """
    Map each report field to the chain of sObjects it traverses based on fullyQualifiedName.

    This method extracts field information and inferred join relationships by parsing each field's
    `fullyQualifiedName` and `entityColumnName`. It traces the lookup relationships between
    intermediate objects to identify the chain of related sObjects.

    Args:
        client_id: ID representing the Salesforce org client.
        access_token_config_id: Config ID required to make authenticated Salesforce API calls.
        sreport_object_detail: Report describe API response containing report metadata.
        extracted_sobject_details: Output dictionary to update. Expected format:

    Returns:
        Updates the `extracted_sobject_details` dict with:
            - objects: each object mapped to its selected fields
            - join_prompts: inferred joins used to reach the fields
        Also builds:
            - missing_fields: fields without a fullyQualifiedName or entityColumnName
            - invalid_paths: fields where lookups could not be resolved

    Example:
        For fullyQualifiedName = "Opportunity.Owner.UserRole.RollupDescription"
        and entityColumnName = "UserRole.RollupDescription"

        This implies a join path:
            Opportunity LEFT JOIN User (via Owner)
            User LEFT JOIN UserRole (via UserRole)
        Final field: RollupDescription (from UserRole)
    """

    missing_fields, invalid_paths, ambiguous_reference_fields = [], [], []
    field_objects = set()

    # detailColumns is a dictionary with keys like "Opportunity.Name" and metadata about the field
    detail_columns = sreport_object_detail.get("reportExtendedMetadata", {}).get(
        "detailColumnInfo", {}
    )

    for field_name, field_detail in detail_columns.items():
        # Fully Qualified Name (like Opportunity.Owner.UserRole.RollupDescription)
        fqn = field_detail.get("fullyQualifiedName")
        # Entity Column Name (like UserRole.RollupDescription) => base field reference
        ecn = field_detail.get("entityColumnName")

        # If either is missing, log and skip
        if not fqn or not ecn:
            missing_fields.append(field_name)
            continue

        segments = fqn.split(
            "."
        )  # Break down path (e.g., ['Opportunity', 'Owner', 'UserRole', 'RollupDescription'])

        if len(segments) < 2:  # noqa: PLR2004
            invalid_paths.append(field_name)
            continue

        current_sobject = segments[0]  # Root object (e.g., 'Opportunity')
        # Iterate through each intermediate lookup step, excluding the last field
        valid_path = True
        for segment in segments[1:-1]:
            try:
                sobject_response = get_cached_sobject_fields(
                    client_id, access_token_config_id, current_sobject
                )
            except Exception:
                logger.exception(
                    f"Failed to get sobject details for '{current_sobject}' while processing segment '{segment}' in field '{field_name}'"
                )
                invalid_paths.append(field_name)
                valid_path = False
                break

            if not sobject_response:
                logger.warning(
                    f"Empty response for sobject '{current_sobject}' while processing segment '{segment}' in field '{field_name}'"
                )
                invalid_paths.append(field_name)
                valid_path = False
                break

            found = False
            field_objects.add(current_sobject)
            for field in sobject_response.get("fields", []):
                # Match either by field name or relationshipName (used in FQN)
                if (
                    field.get("name") == segment
                    or field.get("relationshipName") == segment
                ):
                    reference_to = field.get("referenceTo")
                    if reference_to and len(reference_to) == 1:
                        current_sobject = reference_to[
                            0
                        ]  # Pick first target in referenceTo
                        field_objects.add(current_sobject)
                        found = True
                    else:
                        ambiguous_reference_fields.append(field_name)
                        valid_path = False
                    break

            if not found:
                invalid_paths.append(field_name)
                valid_path = False
                break

        # Final check to ensure destination sObject exists
        try:
            sobject_response = get_cached_sobject_fields(
                client_id, access_token_config_id, current_sobject
            )

            if not sobject_response:
                logger.warning(
                    f"Empty response for final sobject '{current_sobject}' while processing field '{field_name}'"
                )
                valid_path = False
                invalid_paths.append(field_name)
        except Exception:
            logger.exception(
                f"Failed to get final sobject details for '{current_sobject}' while processing field '{field_name}'"
            )
            valid_path = False
            invalid_paths.append(field_name)

        # Only store if path is valid and field exists in final object
        if valid_path:
            final_object = current_sobject
            final_field = segments[-1]
            final_sobject_response = get_cached_sobject_fields(
                client_id, access_token_config_id, final_object
            )
            if any(
                f.get("name") == final_field
                for f in final_sobject_response.get("fields", [])
            ):
                extracted_sobject_details["objects"][final_object][final_field] = (
                    field_detail["label"]
                )
            else:
                logger.warning(
                    f"[MISSING_FIELD] Final field '{final_field}' not found in sObject '{final_object}'"
                )
            invalid_paths.append(field_name)
    logger.info(
        f"[FIELDS] Processed: {len(detail_columns)}, Missing: {missing_fields}, Invalid: {invalid_paths}, Ambiguous: {ambiguous_reference_fields}"
    )
    return field_objects


def recreate_salesforce_report(
    client_id: int, access_token_config_id: int, sreport_object: dict
):
    extracted_sobject_details = {"objects": defaultdict(dict), "join_prompt": ""}

    try:
        crt_objects = set()
        if is_custom_report_type(sreport_object):
            logger.info("Processing custom report type")
            crt_objects = extract_join_transformations(
                sreport_object, extracted_sobject_details
            )

        logger.info("Extracting fields info from Salesforce report")
        field_objects = extract_fields_info(
            client_id, access_token_config_id, sreport_object, extracted_sobject_details
        )

        lookup_objects = field_objects - crt_objects

        join_type = " LEFT JOIN "
        lookup_object_prompt = (
            join_type.join(lookup_objects) if len(lookup_objects) > 0 else ""
        )
        if extracted_sobject_details["join_prompt"]:
            extracted_sobject_details["join_prompt"] = (
                extracted_sobject_details["join_prompt"]
                + join_type
                + lookup_object_prompt
            )
        else:
            extracted_sobject_details["join_prompt"] = lookup_object_prompt

        logger.info(
            f"Successfully processed Salesforce report. Found {len(extracted_sobject_details['objects'])} objects and {extracted_sobject_details['join_prompt']} join prompt"
        )
        return extracted_sobject_details

    except Exception as e:
        logger.exception("Error processing Salesforce report")
        raise SalesforceReportError(
            message=f"Failed to recreate Salesforce report: {str(e)}"
        ) from e
