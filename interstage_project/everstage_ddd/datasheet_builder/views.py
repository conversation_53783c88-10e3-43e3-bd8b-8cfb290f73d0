import json
import logging

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.accessors.client_accessor import check_everai_access
from commission_engine.third_party_connections.salesforce import Salesforce
from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.datasheet_builder.constants import SheetBuilder<PERSON>tatus
from everstage_ddd.datasheet_builder.exceptions import (
    DatasheetBuilderError,
    DatasheetBuilderTaskRunningError,
    JoinValidationFieldNotFoundError,
    JoinValidationTypeMismatchError,
)
from everstage_ddd.datasheet_builder.models import GenerateNodesParams
from everstage_ddd.datasheet_builder.services import datasheet_builder_service
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view

logger = logging.getLogger(__name__)


class SalesforceReportsView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("SalesforceReports")
    def get(self, request):
        """
        Fetch all available Salesforce reports for the client
        """
        try:
            client_id = request.client_id
            if not check_everai_access(client_id):
                return Response(
                    {"message": "You are not authorized to access this feature."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            access_token_config_id = request.GET.get("access_token_config_id")
            if not access_token_config_id:
                return Response(
                    {"message": "access_token_config_id is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            salesforce_service = Salesforce()
            reports = salesforce_service.get_report_objects(
                client_id=client_id, access_token_config_id=access_token_config_id
            )
            return Response(
                {"reports": reports},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.exception(
                "Error fetching Salesforce reports",
                extra={"client_id": request.client_id, "error": str(e)},
            )
            return Response(
                {
                    "message": "An error occurred while fetching reports",
                    "reason": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SalesforceReportDetailsView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("SalesforceReportDetails")
    def get(self, request):
        """
        Fetch details of a specific Salesforce report
        """
        try:
            client_id = request.client_id
            if not check_everai_access(client_id):
                return Response(
                    {"message": "You are not authorized to access this feature."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            access_token_config_id = request.GET.get("access_token_config_id")
            report_id = request.GET.get("report_id")

            if not access_token_config_id:
                return Response(
                    {"message": "access_token_config_id is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not report_id:
                return Response(
                    {"message": "report_id is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            report_details = datasheet_builder_service.get_report_object_details(
                client_id=client_id,
                report_id=report_id,
                access_token_config_id=access_token_config_id,
            )

            return Response(
                json.dumps(report_details),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.exception(
                "Error fetching Salesforce report details",
                extra={
                    "client_id": request.client_id,
                    "report_id": report_id,
                    "error": str(e),
                },
            )
            return Response(
                {
                    "message": "An error occurred while fetching report details",
                    "reason": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GenerateNodesView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("GenerateNodes")
    def post(self, request):
        client_id = request.client_id
        if not check_everai_access(client_id):
            return Response(
                {"message": "You are not authorized to access this feature."},
                status=status.HTTP_403_FORBIDDEN,
            )
        generate_nodes_input = {
            "client_id": client_id,
            "email_id": request.user.username,
            "nodes": request.data.get("nodes"),
            "edges": request.data.get("edges"),
            "user_prompt": request.data.get("user_prompt"),
            "service_name": request.data.get("service_name"),
            "access_token_config_id": request.data.get("access_token_config_id"),
            "quickpicks": request.data.get("quickpicks"),
            "is_report_builder": request.data.get("is_report_builder", False),
            "report_data": json.loads(request.data.get("report_data") or "null"),
        }

        try:
            response = datasheet_builder_service.generate_nodes_service(
                GenerateNodesParams(**generate_nodes_input)
            )
            return Response(
                response,
                status=status.HTTP_200_OK,
            )
        except JoinValidationFieldNotFoundError as e:
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "reason": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except JoinValidationTypeMismatchError as e:
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "reason": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except DatasheetBuilderError as e:
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "reason": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception:
            logger.exception("Error generating nodes")
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "reason": "An error occurred while processing your request. Please try again later!",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetParentDatasheetVariablesView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("GetParentDatasheetVariables")
    def post(self, request):
        try:
            client_id = request.client_id

            if not check_everai_access(client_id):
                return Response(
                    {
                        "status": "error",
                        "message": "You are not authorized to access this feature.",
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            datasheet_id = request.data.get("datasheet_id")
            parent_datasheet_variables = (
                datasheet_builder_service.get_parent_datasheet_variables(
                    client_id=client_id,
                    datasheet_id=datasheet_id,
                )
            )
            return Response(
                {
                    "status": SheetBuilderStatus.SUCCESS.value,
                    "message": "Parent datasheets fetched successfully",
                    "parent_datasheet_variables": parent_datasheet_variables,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.exception("Error fetching parent datasheet variables")
            return Response(
                {"message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SheetBuilderView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("Sheet Builder")
    def post(self, request):
        try:
            client_id = request.client_id
            created_by = request.user.username

            client_info = {
                "client_id": client_id,
                "created_by": created_by,
                "email": request.user.username,
                "audit": request.audit,
                "logger": request.logger,
                "user": request.user,
            }

            if not check_everai_access(client_id):
                return Response(
                    {
                        "status": SheetBuilderStatus.ERROR.value,
                        "message": "You are not authorized to access this feature.",
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            realtime_task_id = datasheet_builder_service.sheet_builder_service(
                client_info=client_info, params=request.data
            )

            return Response(
                {
                    "status": SheetBuilderStatus.SUCCESS.value,
                    "task_id": realtime_task_id,
                },
                status=status.HTTP_201_CREATED,
            )

        except DatasheetBuilderError as e:
            if e.message == "DATABOOK_NAME_EXISTS":
                return Response(
                    {"status": "DATABOOK_NAME_EXISTS"},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "message": str(e),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except DatasheetBuilderTaskRunningError as e:
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "message": str(e),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            logger.exception(
                "Error in RunSyncView",
                extra={
                    "client_id": request.client_id,
                    "error": str(e),
                },
            )
            return Response(
                {
                    "status": SheetBuilderStatus.ERROR.value,
                    "message": "An unexpected error occurred while processing the request",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
