import datetime
import json
import logging
import uuid
from typing import Any, Dict, List

import interstage_project.utils as iputils
from async_tasks.accessor import Async<PERSON><PERSON><PERSON>ccessor
from async_tasks.config import AsyncTaskConfig
from async_tasks.models import TaskStatus
from commission_engine.accessors.client_accessor import get_client_subscription_plan
from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.databook_accessor import (
    DatabookAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import IntegrationAccessor
from commission_engine.services import etl_sync_status_service
from commission_engine.services.etl_global_sync_status_service import (
    EtlGlobalSyncStatusService,
)
from commission_engine.services.etl_tasks_service import (
    ETLSync,
    run_databook_commission_async,
)
from commission_engine.third_party_connections.salesforce import Salesforce
from commission_engine.utils import ETL_ACTIVITY, ETL_STATUS, make_aware
from everstage_ddd.datasheet_builder import utils
from everstage_ddd.datasheet_builder.constants import (
    DATATYPE_ID_MAPPINGS,
    VARIABLES_DATA_TYPE_MAP,
    EdgeOperations,
    NodeOperations,
    SheetBuilderStatus,
    SkillTags,
)
from everstage_ddd.datasheet_builder.databook_builder_status_manager import (
    initialize_status_manager,
)
from everstage_ddd.datasheet_builder.exceptions import (
    DatasheetBuilderError,
    DatasheetBuilderTaskRunningError,
    SalesforceReportError,
)
from everstage_ddd.datasheet_builder.models import (
    GenerateNodesParams,
    SheetBuilderConfig,
)
from everstage_ddd.llm_agent import mannai_interface
from everstage_ddd.self_service_integration.services.self_service_integration_service import (
    create_self_serve_integrated_object,
    get_fields_for_an_object,
)
from everstage_ddd.upstream.extraction.utils import set_upstream_timestamps
from interstage_project.celery import TaskGroupEnum

logger = logging.getLogger(__name__)


def generate_nodes_service(generate_nodes_input: GenerateNodesParams):
    """Generate nodes for datasheet builder using AI agent."""
    nodes = utils.filter_selected_fields(generate_nodes_input.nodes)
    user_prompt = utils.construct_prompt_for_node_creation_agent(
        user_prompt=generate_nodes_input.user_prompt,
        nodes=nodes,
        edges=generate_nodes_input.edges,
        access_token_config_id=generate_nodes_input.access_token_config_id,
        quickpicks=generate_nodes_input.quickpicks,
    )

    ai_generated_content, llm_status = utils.generate_nodes_using_skill_with_retry(
        client_id=generate_nodes_input.client_id,
        user_prompt=user_prompt,
        email_id=generate_nodes_input.email_id,
    )

    if llm_status.lower() == SheetBuilderStatus.SUCCESS.value and ai_generated_content:
        json_response = ai_generated_content["json_response"]
        text_response = ai_generated_content["str_response"]
        parsed_node_operations = utils.parse_node_operations(json_response)
        config = SheetBuilderConfig(
            client_id=generate_nodes_input.client_id,
            node_operations=parsed_node_operations["outputData"],
            service_name=generate_nodes_input.service_name,
            access_token_config_id=generate_nodes_input.access_token_config_id,
            nodes=nodes,
            report_data=generate_nodes_input.report_data,
            is_report_builder=generate_nodes_input.is_report_builder,
        )
        node_operations = generate_nodes_for_sheet_builder(config)
        return {
            "status": SheetBuilderStatus.SUCCESS.value,
            "data": node_operations,
            "text": text_response,
        }
    raise DatasheetBuilderError(
        message="Something went wrong. Please try again later!",
    )


def _process_node_alias(operation: Dict[str, Any], alias_names: set) -> None:
    """Process and set alias name for a node operation."""
    if alias_names:
        highest_letter = utils.get_highest_alias_name(alias_names)
        next_alias_name = utils.get_next_alias_name(highest_letter)
    else:
        next_alias_name = "A"
    alias_names.add(next_alias_name)
    operation["node"]["data"]["alias_name"] = next_alias_name


def _process_root_node(
    config: SheetBuilderConfig, operation: Dict[str, Any], node_data: Dict[str, Any]
) -> None:
    """Process a root node (nodeOrder = 0)."""
    new_object_name = operation["node"]["data"]["label"]
    itr = 1
    while CustomObjectAccessor(
        client_id=config.client_id
    ).does_custom_object_name_exists(new_object_name):
        new_object_name = f"{operation['node']['data']['label']}_{str(itr)}"
        itr += 1
    operation["node"]["data"]["destination_name"] = new_object_name

    object_id = operation["node"]["id"]
    all_fields = get_fields_for_an_object(
        client_id=config.client_id,
        object_id=object_id,
        service_name=config.service_name,
        access_token_config_id=config.access_token_config_id,
    )

    node_data["primary_key"] = []
    node_data["fields"] = []
    report_field_dict = (config.report_data or {}).get("objects", {}).get(object_id, {})
    for field in all_fields:
        report_field_name = None
        if field["name"] in report_field_dict:
            report_field_name = report_field_dict[field["name"]]
        node_data["fields"].append(
            {
                "name": field["name"],
                "display_name": report_field_name or field["name"],
                "object_id": object_id,
                "type": VARIABLES_DATA_TYPE_MAP.get(field["type"], "String"),
                "is_primary_key": field["type"] == "id",
                "is_selected": utils.set_selected_fields(
                    field, report_data=config.report_data, object_id=object_id
                ),
            }
        )
        if field["type"] == "id":
            node_data["primary_key"].append(field["name"])
    if not node_data["primary_key"]:
        raise DatasheetBuilderError(
            message=f"Primary key is not found for the custom object {node_data['label']}"
        )


def _process_non_root_node(
    config: SheetBuilderConfig, node_data: Dict[str, Any], nodes: List[Dict[str, Any]]
) -> None:
    """Process a non-root node (nodeOrder > 0)."""
    if "sources" in node_data:
        node_data["parentDatasheets"] = utils.get_parent_datasheets(
            node_data["sources"], nodes
        )
        node_data["fields"] = utils.combine_fields(node_data["parentDatasheets"], nodes)

    field_names = set()
    for parent_datasheet in node_data["fields"]:
        for field in node_data["fields"][parent_datasheet]:
            field_names.add(field["name"])

    for transformation in node_data["transformations"]:
        if transformation["type"] == "JOIN":
            for parent_datasheet_name in node_data["fields"]:
                utils.validate_join_condition(
                    transformation,
                    field_names,
                    parent_datasheet_name,
                )

    if config.is_report_builder:
        utils.remove_unused_fields_from_report(config, node_data)


def _process_edge(operation: Dict[str, Any]) -> None:
    """Process an edge operation."""
    operation["edge"]["type"] = "customEdge"
    operation["edge"]["key"] = (
        operation["edge"]["source"] + "_to_" + operation["edge"]["target"]
    )


def _process_node_operation(
    config: SheetBuilderConfig,
    operation: Dict[str, Any],
    nodes: List[Dict[str, Any]],
    alias_names: set,
) -> None:
    """Process a node operation (ADD_NODE or UPDATE_NODE)."""
    if operation["operationType"] == NodeOperations.ADD_NODE.value:
        _process_node_alias(operation, alias_names)

    operation["node"]["type"] = "tableNode"
    node_data = operation["node"]["data"]

    if node_data["nodeOrder"] == 0:
        _process_root_node(config, operation, node_data)
    else:
        _process_non_root_node(config, node_data, nodes)


def generate_nodes_for_sheet_builder(
    config: SheetBuilderConfig,
) -> List[Dict[str, Any]]:
    """
    Generate nodes for sheet builder based on the provided configuration.

    Args:
        config: SheetBuilderConfig object containing all necessary parameters

    Returns:
        List of node operations
    """
    nodes = config.nodes
    alias_names = {node["data"]["alias_name"] for node in nodes}

    for operation in config.node_operations:
        if operation["operationType"] in {
            NodeOperations.ADD_NODE.value,
            NodeOperations.UPDATE_NODE.value,
        }:
            _process_node_operation(config, operation, nodes, alias_names)
        elif operation["operationType"] in {
            EdgeOperations.ADD_EDGE.value,
            EdgeOperations.UPDATE_EDGE.value,
        }:
            _process_edge(operation)

    return config.node_operations


def get_parent_datasheet_variables(client_id, datasheet_id):
    """Retrieve variables from parent datasheet for databook builder."""
    variables = DatasheetVariableAccessor(
        client_id=client_id
    ).get_ds_variables_for_databook_builder(datasheet_id)
    variables = list(variables)
    for variable in variables:
        variable["data_type"] = variable.pop("data_type__data_type")
        variable["data_type_id"] = variable.pop("data_type__id")
    return variables


def create_custom_objects(
    client_id, nodes, access_token_config_id, request
) -> list[int]:
    """Create custom objects for each node in the datasheet builder."""
    created_custom_object_ids = []
    for node in nodes:
        field_mappings = [
            {
                "source_field": field["name"],
                "destination_field": field["display_name"],
                "field_type": field["type"],
                "is_association": False,
                "applied_function": "",
            }
            for field in node["data"]["fields"]
            if field["is_selected"]
        ]
        service_name = "salesforce"
        object_id = node["id"]
        pipeline_type = ""
        has_associations = False
        hyperlinked_field = None
        hyperlinked_url = None
        associated_objects = []
        include_hard_delete_config = False
        changes_sync_field = "{}"
        delete_sync_field = "{}"
        sync_type = "changes"
        object_data = {
            "name": node["data"]["destination_name"],
            "primary_key": node["data"]["primary_key"],
            "snapshot_key": node["data"]["primary_key"],
            "variables": [
                {
                    "display_name": field["display_name"],
                    "id": DATATYPE_ID_MAPPINGS[field["type"]],
                }
                for field in node["data"]["fields"]
                if field["is_selected"]
            ],
        }
        custom_object_id = create_self_serve_integrated_object(
            client_id=client_id,
            request=request,
            create_custom_object=True,
            service_name=service_name,
            object_id=object_id,
            field_mappings=field_mappings,
            access_token_config_id=access_token_config_id,
            object_data=object_data,
            pipeline_type=pipeline_type,
            has_associations=has_associations,
            hyperlinked_field=hyperlinked_field,
            hyperlinked_url=hyperlinked_url,
            include_hard_delete_config=include_hard_delete_config,
            changes_sync_field=changes_sync_field,
            delete_sync_field=delete_sync_field,
            associated_objects=associated_objects,
            syncType=sync_type,
        )
        created_custom_object_ids.append(custom_object_id)
    return created_custom_object_ids


def get_integration_ids(client_id, destination_object_ids):
    """Get integration IDs for given destination object IDs."""
    integrations = IntegrationAccessor(
        client_id=client_id
    ).get_records_by_destination_object_ids(
        destination_object_ids=destination_object_ids
    )
    integration_ids = [integration.integration_id for integration in integrations]
    return integration_ids


def set_upstream_timestamp(client_id, integration_ids):
    """Set upstream timestamps for integrations to 90 days ago."""
    yrt = datetime.datetime.now() - datetime.timedelta(days=90)  # noqa: DTZ005
    for integration_id in integration_ids:
        set_upstream_timestamps(
            client_id=client_id,
            integration_id=integration_id,
            timestamps={
                "api_changes_synced_till": yrt,
                "api_deletes_synced_till": yrt,
                "upstream_source_synced_till": yrt,
                "historic_sync_date": yrt,
            },
        )


def can_run_sync(client_id: int):
    """Check if sync operations can be run for the client."""
    return (
        EtlGlobalSyncStatusService.is_global_sync_enabled_for_client(client_id)
        and not etl_sync_status_service.is_end_to_end_sync_running(client_id)
        and not etl_sync_status_service.is_upstream_sync_running(client_id)
    )


def refresh_databook(
    client_id: int, databook_params: dict, queue_name: str, audit: dict
) -> list | None:
    """Refresh databook by triggering commission sync."""
    if not can_run_sync(client_id=client_id):
        logger.info("Unable to refresh databook")
        return

    e2e_sync_run_id = databook_params["e2e_sync_run_id"]
    activity = ETL_ACTIVITY.REFRESH_DATABOOK.value
    databook_ids = databook_params["databook_ids"]

    etl_status_params = dict()
    etl_status_params["run_report_sync"] = 0
    etl_status_params["databook_ids"] = (
        [str(databook_id) for databook_id in databook_ids] if databook_ids else None
    )
    additional_info = {
        "is_triggered_from_datasheet_builder": True,
    }

    etl_sync_status_service.insert_etl_sync_status(
        client_id,
        e2e_sync_run_id,
        activity,
        ETL_STATUS.STARTED.value,
        make_aware(datetime.datetime.now()),  # noqa: DTZ005
        audit,
        etl_status_params,
        additional_info=additional_info,
    )

    refresh_databook = run_databook_commission_async.si(
        params=databook_params, is_flow_eligible_for_trigger_only_associated_ds=True
    ).set(queue=queue_name)

    refresh_databook.apply_async(compression="lzma", serializer="pickle")


def run_upstream_sync(
    client_id: int,
    e2e_sync_run_id: uuid.UUID,
    log_context: dict,
    client_info: dict,
    integration_ids: list[uuid.UUID],
) -> None:
    """Run upstream sync for specified integrations."""
    if not can_run_sync(client_id=client_id):
        logger.info("Unable to run upstream sync")
        return

    ETLSync(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        log_context=log_context,
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=False,
        post_upstream_disabled=True,
    ).run_daily_sync_wrapper(
        all_objects_selected=False,
        integration_ids=list(map(str, integration_ids)),
        include_upstream_hard_delete_sync=False,
        audit=client_info["audit"],
    )


def create_datasheets_using_agent(
    client_id: int,
    params: dict,
    client_info: dict,
    *,
    show_data_sources_v2: bool = False,
) -> None:
    """Create datasheets using AI agent for each node in the workflow."""
    email_id: str = client_info["email"]
    databook_id: int = params["databook_id"]
    databook_name: str = params["databook_name"]
    nodes: list[dict] = params["nodes"]

    if show_data_sources_v2:
        skill_tag = SkillTags.DATASHEET_CREATION_AGENT_V2.value
    else:
        skill_tag = SkillTags.DATASHEET_CREATION_AGENT.value

    logger.info(
        f"BEGIN: Datasheet Builder for client_id={client_id}, databook_id={databook_id}",
    )
    grouped_nodes = utils.group_nodes_by_order(nodes)
    levels = sorted(grouped_nodes.keys())[1:]

    for level in levels:
        logger.info(f"Processing step {level} with {len(grouped_nodes[level])} nodes.")
        for node in grouped_nodes[level]:
            logger.info(f"Creating datasheet name: {node['data']['label']}")
            node["data"]["fields"] = [
                {
                    k: v
                    for k, v in field.items()
                    if k not in ("destination_name", "object_id")
                }
                for field in node["data"]["fields"]
                if field["is_selected"]
            ]
            user_prompt = utils.construct_prompt_for_datasheet_creation_agents(
                current_step=level,
                node=node,
                databook_id=databook_id,
                databook_name=databook_name,
            )
            node_order = node["data"]["node_order"]
            retry, failed_response = 3 if node_order == 0 else 1, True
            while retry > 0:
                ai_generated_content, llm_status = mannai_interface.execute_skill(
                    client_id=client_id,
                    skill_tag=skill_tag,
                    employee_email_id=email_id,
                    user_prompt=user_prompt,
                    return_result=True,
                )
                json_response = ai_generated_content["ai_generated_content"][
                    "json_response"
                ]
                if isinstance(json_response, dict):
                    final_json_status = json_response["status"]
                else:
                    final_json_status = json.loads(json_response)["status"]
                failed_response = (
                    llm_status.lower() != SheetBuilderStatus.SUCCESS.value
                    or not isinstance(final_json_status, str)
                    or final_json_status.lower() != SheetBuilderStatus.SUCCESS.value
                )

                if not failed_response:
                    break
                retry -= 1

            if failed_response:
                logger.error(f"Step {level} failed: LLM status: {llm_status}")
                raise DatasheetBuilderError("Failed")

        logger.info(f"Step {level} processed successfully.")

    logger.info(
        f"END: Datasheet Builder for client_id={client_id}, databook_id={databook_id}",
    )


def sheet_builder_service(
    client_info: dict,
    params: dict,
) -> str:
    """
    Runs the datasheet builder sync process with five main tasks:
    1. Create Custom Object
    2. Create Databook
    3. Upstream sync for custom objects
    4. Databook sync for the created objects
    5. Create datasheets for the objects
    """
    from everstage_ddd.datasheet_builder.tasks.sheet_builder_wrapper import (
        sheet_builder_wrapper,
    )

    all_databooks = list(
        DatabookAccessor(client_info["client_id"]).client_latest_kd_aware()
    )
    db_names = [db.name.lower() for db in all_databooks]
    if params["databook"]["name"].lower() in db_names:
        logger.info(
            "Databook with name {} Already exist ".format(
                params["databook"]["name"].lower()
            )
        )
        raise DatasheetBuilderError("DATABOOK_NAME_EXISTS")

    active_tasks = AsyncTaskAccessor(client_info["client_id"]).get_active_tasks_by_type(
        task_type=AsyncTaskConfig.DATASHEET_BUILDER_TASK.value["name"],
        status_list=[TaskStatus.PROCESSING.value, TaskStatus.PENDING.value],
    )
    if active_tasks.exists():
        raise DatasheetBuilderTaskRunningError()

    try:
        client_id = client_info["client_id"]
        upstream_e2e_sync_run_id = uuid.uuid4()
        audit = client_info["audit"]

        logger_context = {
            "client_id": client_id,
            "task_id": upstream_e2e_sync_run_id,
            "task": "Datasheet Builder",
        }

        upstream_params = {
            "client_id": client_id,
            "log_context": logger_context,
            "e2e_sync_run_id": upstream_e2e_sync_run_id,
            "all_objects_selected": False,
        }
        databook_e2e_sync_run_id = uuid.uuid4()
        databook_params = {
            "client_id": client_id,
            "e2e_sync_run_id": databook_e2e_sync_run_id,
            "log_context": {
                "client_id": client_id,
                "e2e_sync_run_id": databook_e2e_sync_run_id,
                "run_databook_sync_only": True,
                "task": "Datasheet Builder",
            },
            "notification_email_id": None,
            "refresh_databook": False,
            "payee_list": None,
            "audit": audit,
            "curr_date": None,
            "end_date": None,
            "skip_archived_books": False,
            "run_databook_sync_only": True,
            "run_previous_period_sync": False,
            "logger": logger,
        }

        subscription_plan = get_client_subscription_plan(client_id)
        queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MISC.value
        )
        params["queue_name"] = queue_name

        realtime_task_id = str(uuid.uuid4())
        status_manager_params = {
            "realtime_task_id": realtime_task_id,
            "upstream_e2e_sync_id": str(upstream_e2e_sync_run_id),
            "databook_refresh_e2e_sync_id": str(databook_e2e_sync_run_id),
            "access_token_config_id": params["custom_object"]["access_token_config_id"],
            "client_id": client_id,
        }
        realtime_task_id, async_task_id = initialize_status_manager(
            client_id,
            status_manager_params,
            client_info["email"],
        )

        builder_task = sheet_builder_wrapper.si(
            client_id=client_id,
            params=params,
            databook_params=databook_params,
            upstream_params=upstream_params,
            client_info=client_info,
            realtime_task_id=realtime_task_id,
            async_task_id=async_task_id,
        ).set(queue=queue_name)

        builder_task.apply_async(compression="lzma", serializer="pickle")
        return realtime_task_id

    except DatasheetBuilderError as e:
        logger.exception(
            f"Datasheet builder sync error: {e.message}",
            extra={
                "client_id": client_id,
            },
        )
        raise DatasheetBuilderError(str(e)) from e
    except Exception as e:
        logger.exception(
            "Unexpected error in datasheet builder sync",
            extra={
                "client_id": client_id,
            },
        )
        raise DatasheetBuilderError(str(e)) from e


def get_report_object_details(
    client_id: int, report_id: str, access_token_config_id: int
) -> dict:
    """Get report object details from Salesforce."""
    try:
        salesforce_service = Salesforce()

        report_detail_response = salesforce_service.get_report_details(
            client_id=client_id,
            report_id=report_id,
            access_token_config_id=access_token_config_id,
        )
        return utils.recreate_salesforce_report(
            client_id, access_token_config_id, report_detail_response
        )
    except Exception as e:
        raise SalesforceReportError(
            message=f"Unable to import report from Salesforce: {str(e)}"
        ) from e
