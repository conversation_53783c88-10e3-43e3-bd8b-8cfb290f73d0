from dataclasses import dataclass
from typing import Any, Dict

from pydantic import BaseModel, Field


class GenerateNodesParams(BaseModel):
    client_id: int
    email_id: str
    nodes: list[dict]
    edges: list[dict]
    user_prompt: str
    service_name: str
    access_token_config_id: int
    quickpicks: list[dict]
    is_report_builder: bool = False
    report_data: dict | None = None


class DataBookUser(BaseModel):
    username: str


class DatabookRequest(BaseModel):
    client_id: int
    data: Dict[str, Any]
    audit: Any
    user: DataBookUser
    logger: Any = Field(exclude=True)


class CustomObjectRequest(BaseModel):
    user: Any
    logger: Any = Field(exclude=True)
    audit: Dict


@dataclass
class SheetBuilderConfig:
    """Configuration class for sheet builder operations."""

    client_id: int
    node_operations: list[Dict[str, Any]]
    service_name: str
    access_token_config_id: int
    nodes: list[dict]
    report_data: dict | None
    is_report_builder: bool = False
