from logging import Logger, getLogger
from uuid import UUID

from celery import group, shared_task

from common.celery.celery_base_task import Ever<PERSON>eleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group

from .data_envoy import DataEnvoy
from .utils import create_etl_status, mark_etl_sync_failed

logger: Logger = getLogger(__name__)


@shared_task(base=EverCeleryBaseTask)
def get_downstream_wrapper_sync_task(
    client_id: int, integration_ids: list[UUID], e2e_sync_run_id: UUID
) -> group:
    from commission_engine.accessors.client_accessor import get_client_subscription_plan

    logger.info("BEGIN: Downstream Wrapper Sync")
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.DOWNSTREAM.value
    )
    sync_tasks = [
        downstream_sync.si(  # type: ignore
            client_id=client_id,
            integration_id=integration_id,
            e2e_sync_run_id=e2e_sync_run_id,
        ).set(queue=queue_name)
        for integration_id in integration_ids
    ]
    logger.info("END: Downstream Wrapper Sync")
    return group(sync_tasks)


@shared_task(base=EverCeleryBaseTask)
def downstream_sync(
    client_id: int, integration_id: UUID, e2e_sync_run_id: UUID
) -> None:
    try:
        sync_run_id = create_etl_status(client_id, integration_id, e2e_sync_run_id)
        logger.info(f"Triggering downstream sync for client_id: {client_id}")
        DataEnvoy(client_id, integration_id, e2e_sync_run_id, sync_run_id).sync()
    except Exception:
        logger.exception("Error occurred during downstream sync")
        mark_etl_sync_failed(client_id, e2e_sync_run_id)
