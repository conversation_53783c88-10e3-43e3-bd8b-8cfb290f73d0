from copy import deepcopy
from datetime import datetime
from uuid import UUID

from commission_engine.accessors.client_accessor import (
    can_run_auto_enrich_report,
    get_approvals_report_object_enabled,
    get_payout_approvals_flag,
    get_payout_report_object_enabled,
)
from everstage_ddd.cpq.reports import get_cpq_report_strategies
from everstage_ddd.snowflake_reports.approvals_report_snowflake import (
    StatementApprovalsReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.base_snowflake_report import BaseSnowflakeReport
from everstage_ddd.snowflake_reports.comm_report_auto_enrich import (
    CommReportAutoEnrichStrategy,
)
from everstage_ddd.snowflake_reports.commission_report_snowflake import (
    CommissionReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.commission_summary_report_snowflake import (
    CommissionSummaryReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.inter_comm_report_auto_enrich import (
    InterCommReportAutoEnrichStrategy,
)
from everstage_ddd.snowflake_reports.inter_commission_report_snowflake import (
    InterCommissionReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.inter_quota_attainment_report_snowflake import (
    InterQuotaAttainmentReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.payout_report_snowflake import (
    PayoutReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.quota_attainment_report_snowflake import (
    QuotaAttainmentReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.settlement_report_snowflake import (
    SettlementReportSnowflakeStrategy,
)
from everstage_ddd.snowflake_reports.snowflake_report_utils import ReportSyncMode

global_object_id_to_strategy_map = {
    "settlement": SettlementReportSnowflakeStrategy,
    "commission": CommissionReportSnowflakeStrategy,
    "inter_commission": InterCommissionReportSnowflakeStrategy,
    "quota_attainment": QuotaAttainmentReportSnowflakeStrategy,
    "inter_quota_attainment": InterQuotaAttainmentReportSnowflakeStrategy,
    "commission_summary": CommissionSummaryReportSnowflakeStrategy,
    **get_cpq_report_strategies(),
}


def get_snowflake_report_strategy_instance(
    object_id: str,
    client_id: int,
    e2e_sync_run_id: UUID,
    sync_run_id: UUID,
    sync_mode: ReportSyncMode,
    knowledge_date: datetime,
    log_context: dict,
) -> BaseSnowflakeReport:
    """
    Get the snowflake report strategy instance based on the object_id
    """

    # Copy the global map to avoid mutating the global map
    # Mutating the global variable will have unexpected behaviour
    object_id_to_strategy_map = deepcopy(global_object_id_to_strategy_map)

    if can_run_auto_enrich_report(client_id):
        object_id_to_strategy_map.update(
            {
                "commission": CommReportAutoEnrichStrategy,
                "inter_commission": InterCommReportAutoEnrichStrategy,
            }
        )
    if get_payout_report_object_enabled(client_id):
        object_id_to_strategy_map.update(
            {
                "payout": PayoutReportSnowflakeStrategy,
            }
        )

    if get_approvals_report_object_enabled(client_id) and get_payout_approvals_flag(
        client_id
    ):
        object_id_to_strategy_map.update(
            {
                "statement_approvals": StatementApprovalsReportSnowflakeStrategy,
            }
        )

    strategy = object_id_to_strategy_map.get(object_id)
    if not strategy:
        raise ValueError(f"Invalid object_id: {object_id}")
    return strategy(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=sync_run_id,
        sync_mode=sync_mode,
        knowledge_date=knowledge_date,
        log_context=log_context,
    )
