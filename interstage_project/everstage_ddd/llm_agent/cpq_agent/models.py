"""
contains the django models used by the cpq agent
"""

from django.db import models


class GongCall(models.Model):
    """
    metadata related to gong calls
    """

    gong_call_id = models.Char<PERSON>ield(null=False, max_length=100, unique=True)
    call_date = models.DateTimeField(auto_now_add=True)
    call_start_time = models.TimeField(auto_now=True)
    call_end_time = models.TimeField(auto_now=True)
    # the file path in the common bucket where the gong call is stored
    s3_file_path = models.Char<PERSON>ield(null=False, max_length=500)
    client_id = models.IntegerField(null=False)
    # the opportunity id of the client the gong call and the deal is associated with
    opportunity_id = models.Char<PERSON>ield(null=False, max_length=500)
    # tracks whether a gong call is already loaded to the vector database
    is_call_loaded = models.BooleanField(default=False)

    class Meta:
        db_table = "cpq_gong_calls"


class VectorCollection(models.Model):
    """
    store metadata of aws opensearch serverless vector collections for each client
    """

    collection_name = models.Char<PERSON>ield(null=False, max_length=100)
    collection_arn = models.CharField(null=False, max_length=1000, unique=True)
    collection_id = models.CharField(null=False, max_length=500, unique=True)
    # name of the aws access data policy that contains permissions of roles that can access the collection
    access_data_policy_name = models.CharField(null=True, max_length=500)
    # name of the aws access network policy that allows public access to collection
    network_policy_name = models.CharField(null=True, max_length=500)
    # name of the aws access encryption policy that contains encryption details for data in the cluster
    encryption_policy_name = models.CharField(null=True, max_length=500)
    client_id = models.IntegerField(null=False, primary_key=True)

    class Meta:
        db_table = "cpq_vector_collections"


class VectorCollectionMeta(models.Model):
    """
    store the metadata of a single vector collection
    """

    collection_name = models.CharField(null=False, max_length=100)
    collection_arn = models.CharField(null=False, max_length=1000, unique=True)
    collection_id = models.CharField(null=False, max_length=500, unique=True)
    # name of the aws access data policy that contains permissions of roles that can access the collection
    access_data_policy_name = models.CharField(null=True, max_length=500)
    # name of the aws access network policy that allows public access to collection
    network_policy_name = models.CharField(null=True, max_length=500)
    # name of the aws access encryption policy that contains encryption details for data in the cluster
    encryption_policy_name = models.CharField(null=True, max_length=500)

    class Meta:
        db_table = "cpq_vector_collection_meta"


class KnowledgeBase(models.Model):
    """
    store metadata of knowledge bases -
    1. currently the schema supports one knowledge based for each query
    """

    knowledge_base_id = models.CharField(null=False, max_length=100, unique=True)
    knowledge_base_name = models.CharField(null=False, max_length=500)
    knowledge_base_arn = models.CharField(null=False, max_length=500, unique=True)
    # the arn of the aws role that has access to gong call in s3, vector collection
    bedrock_execution_arn = models.CharField(null=False, max_length=500, unique=True)
    # the id of the gong call that is mapped to the knowledge base
    entity_id = models.CharField(null=False, max_length=100)
    # one of gong_call, hubspot email, notes
    entity_type = models.CharField(null=False, max_length=100)
    # the id of the data source in aws that contains the chunking metadata of the gong call
    data_source_id = models.CharField(null=False, max_length=200)
    # details of the vector collection and the index in which the gong call of this knowledge based will be indexed
    vector_collection_details = models.JSONField(default=dict)
    client_id = models.IntegerField(null=False)
    # the opportunity id of the client the gong call and the deal is associated with
    opportunity_id = models.CharField(null=False, max_length=500)

    class Meta:
        db_table = "cpq_gong_call_knowledge_base"
        unique_together = ("client_id", "opportunity_id", "entity_id")


class KnowledgeBaseMeta(models.Model):
    """
    store metadata of the singular knowledge base
    """

    knowledge_base_id = models.CharField(null=False, max_length=100, unique=True)
    knowledge_base_name = models.CharField(null=False, max_length=500)
    knowledge_base_arn = models.CharField(null=False, max_length=500, unique=True)
    # the arn of the aws role that has access to gong call in s3, vector collection
    bedrock_execution_arn = models.CharField(null=False, max_length=500, unique=True)
    # the id of the gong call that is mapped to the knowledge base
    entity_id = models.CharField(null=False, max_length=100, unique=True)
    # one of gong_call, hubspot email, notes
    entity_type = models.CharField(null=False, max_length=100)
    # the id of the data source in aws that contains the chunking metadata of the gong call
    data_source_id = models.CharField(null=False, max_length=200)
    # details of the vector collection and the index in which the gong call of this knowledge based will be indexed
    vector_collection_details = models.JSONField(default=dict)

    class Meta:
        db_table = "cpq_gong_call_knowledge_base_meta"


class MetadataBedrock(models.Model):
    """
    store the mapping of dates to sequence number as stored in bedrock
    """

    event_date = models.CharField(null=False, unique=True)
    sequence_number = models.AutoField(primary_key=True)
