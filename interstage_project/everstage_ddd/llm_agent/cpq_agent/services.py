# pylint: disable=logging-fstring-interpolation
# ruff: noqa: G004

import json
import logging
import time
from datetime import date, datetime

from commission_engine.accessors.client_accessor import (
    get_client,
    is_bedrock_loading_enabled,
)
from commission_engine.services.client_feature_service import has_feature

from .accessor import (
    Gong<PERSON>allAccessor,
    KnowledgeBaseAccessor,
    KnowledgeBaseMetaAccessor,
    VectorCollectionAccessor,
    VectorCollectionMetaAccessor,
)
from .aws_utils import (
    check_and_create_single_vector_collection,
    check_and_create_vector_collection_for_client,
    create_knowledge_base_and_related_entities,
    sync_s3_data_source,
)
from .graph_rag_utils import index_files
from .models import <PERSON><PERSON>all, KnowledgeBase, KnowledgeBaseMeta
from .utility import (
    ENTITY_TYPE_TO_ID,
    GONG_CALL_CHUNKING_STRATEGY,
    EntityType,
    GongCallChunkingStrategy,
    add_graph_rag_details_to_typesense,
    add_knowledge_base_details_to_typesense,
    add_llm_rag_details_to_typesense,
    attach_file_to_long_context_prompt,
    attach_file_to_long_context_prompt_hubspot_entity,
    convert_chunk_to_text,
    convert_hubspot_chunk_obj_to_text,
    create_metadata_file_for_entity,
    delete_gong_call_file,
    get_gong_call_graph_rag_s3_path,
    get_gong_call_long_rag_s3_pth,
    get_gong_call_s3_path,
    get_hubspost_chunk_suffix_path,
    get_hubspot_entity_graph_rag_s3_path,
    get_hubspot_entity_long_context_s3_path,
    get_hubspot_entity_s3_path,
    get_knowledge_base_name,
    get_knowledge_base_name_by_entity,
    get_knowledge_base_name_hubspost,
    get_vector_index_name,
    get_vector_index_name_hubspot_chunk,
    manual_chunking_of_gong_call_before_data_sync,
    preprocess_hubspot_chunk,
    upload_chunk_to_s3,
    upload_hubspot_chunk_to_s3,
    upload_hubspot_chunk_to_s3_long_context,
    upload_upload_hubspot_chunk_to_s3_graph_rag,
)

logger = logging.getLogger(__name__)


UPLOAD_CHUNKS_SEPARATELY = False

SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS = True


def write_to_bedrock_aws(
    client_id,
    update_objs,
    integration_config_record,
    integration_id,
):
    from commission_engine.accessors.etl_config_accessor import (
        TransformationConfigAccessor,
    )

    """
    Process vector database updates for the given objects.

    Args:
        client_id: The client identifier
        update_objs: List of objects to update
        integration_config_record: Integration configuration record
        integration_id: Integration identifier
    """

    if len(update_objs) == 0:
        logger.info(
            f"No update_objs found for client_id: {client_id}, integration_id: {integration_id}"
        )
        return

    entity_type = integration_config_record.additional_data.get("entity_type", None)
    opportunity_field = integration_config_record.additional_data.get(
        "opportunity_field", None
    )
    is_terminal_call_field = integration_config_record.additional_data.get(
        "is_terminal_call_field", None
    )
    transformation_config_record = TransformationConfigAccessor(
        client_id=client_id
    ).get_objects_by_integration_id(integration_id)
    system_name_to_source_name_map = {
        obj.destination_field: obj.source_field for obj in transformation_config_record
    }
    for i in range(len(update_objs)):
        construct_data_for_processing_gong_call(
            client_id=client_id,
            record=update_objs[i],
            entity_type=entity_type,
            system_name_to_source_name_map=system_name_to_source_name_map,
            opportunity_field=opportunity_field,
            is_terminal_call_field=is_terminal_call_field,
        )


def construct_data_for_processing_gong_call(
    client_id,
    record,
    entity_type,
    system_name_to_source_name_map,
    opportunity_field,
    is_terminal_call_field,
):
    data = {
        system_name_to_source_name_map[key]: value
        for key, value in record["data"].items()
    }

    if not data.get(opportunity_field):
        return
    elif entity_type == EntityType.GONG_CALL.value:
        data["sentences"] = json.loads(data["sentences"])
        process_gong_call_chunk(
            client_id=client_id,
            gong_call_id=data["callId"],
            chunk_data=data,
            call_date=data["scheduled"],
            opportunity_id=data.get(opportunity_field, ""),
            is_terminal_call=data.get(is_terminal_call_field, False),
        )
    elif entity_type in [
        EntityType.HUBSPOT_EMAIL.value,
        EntityType.HUBSPOT_NOTES.value,
    ]:
        process_hubspot_data(
            client_id=client_id,
            chunk_data=data,
            entity_type=entity_type,
            opportunity_id=data.get(opportunity_field, ""),
            is_terminal_chunk=True,
        )
    else:
        raise ValueError(f"Invalid entity type: {entity_type}")


def process_gong_call_chunk(
    client_id: int,
    opportunity_id: str,
    gong_call_id: str,
    chunk_data: dict,
    call_date: datetime,  # Changed back to datetime
    is_terminal_call: bool = False,
    is_first_chunk_of_call: bool = False,
) -> None:
    """
    Process a single chunk of a Gong call recording for RAG (Retrieval Augmented Generation) systems.

    This function handles the ingestion of Gong call data into multiple RAG systems:
    1. AWS Bedrock Knowledge Base for vector search
    2. Long context RAG for LLM processing
    3. Graph RAG for relationship-based queries (if enabled)

    The function processes chunks sequentially, creating necessary AWS resources on the first chunk
    and syncing data sources when the terminal (last) chunk is processed.

    Args:
        opportunity_id (str): Salesforce opportunity ID associated with the call
        is_terminal_call (bool): True if this is the last chunk of a gong call
        is_first_chunk_of_call (bool): True if this is the first chunk of a gong call

    Workflow:
        1. Converts chunk data to different formats for various RAG systems
        2. Uploads chunks to S3 in appropriate directory structures
        3. Creates AWS Bedrock resources (vector collections, knowledge bases) if needed
        4. On terminal chunk: syncs data sources and enables search functionality

    Returns:
        None: Function persists the gong call to S3 and indexes it in the vector database in AWS Bedrock.
    """

    if not is_bedrock_loading_enabled(client_id=client_id):
        print(f"RAG loading is not enabled for the client {client_id}")
        return

    # if the call exists and it's already loaded, no need to proceed further
    if GongCallAccessor(
        client_id=client_id, opportunity_id=opportunity_id
    ).check_if_call_is_loaded(call_id=gong_call_id):
        return

    client = get_client(id=client_id, fields=["name"])
    client_name = client.name

    is_gong_call_in_sync = GongCallAccessor(
        client_id=client_id, opportunity_id=opportunity_id
    ).check_if_call_exists(call_id=gong_call_id)

    # Ensure call_date is a datetime object
    if isinstance(call_date, str):
        # Handle ISO format with Z suffix
        if call_date.endswith("Z"):
            call_date_normalized = call_date[:-1] + "+00:00"
        else:
            call_date_normalized = call_date
        call_date = datetime.fromisoformat(call_date_normalized)

    call_date_str = call_date.date().isoformat()

    logger.info(f"call_date_str: {call_date_str}")

    call_folder_path, call_file_path, data_source_path = get_gong_call_s3_path(
        client_id=client_id,
        opportunity_id=opportunity_id,
        client_name=client_name,
        call_date=call_date_str,
        call_id=gong_call_id,
        single_knowledge_base=SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS,
    )

    # if this is the first chunk of the call but the sync has already begun, then this is a
    # retry on failure, so delete the existing chunks and upload them again
    if is_gong_call_in_sync and is_first_chunk_of_call:
        print(f"Sync retry, deleting file - {call_file_path}")
        delete_gong_call_file(file_path=call_file_path)

    # chunk data to be converted for bedrock rag
    chunk_data_bedrock = convert_chunk_to_text(
        chunk=chunk_data, chunking_strategy=GONG_CALL_CHUNKING_STRATEGY
    )
    # upload the chunk to s3 for bedrock chunking
    upload_chunk_to_s3(
        file_path=call_file_path,
        chunk_data=chunk_data_bedrock,
    )

    # chunk data for long context rag
    chunk_data_long_context = convert_chunk_to_text(chunk=chunk_data)
    # upload the chunk to s3 for long context RAG chunking
    long_context_prompt_path, long_context_file_path = get_gong_call_long_rag_s3_pth(
        client_id=client_id,
        opportunity_id=opportunity_id,
        client_name=client_name,
        call_date=call_date_str,
        call_id=gong_call_id,
    )
    upload_chunk_to_s3(
        file_path=long_context_file_path, chunk_data=chunk_data_long_context
    )
    is_graph_rag_enabled = has_feature(
        client_id=client_id, feature="is_graph_rag_enabled"
    )
    # upload the chunk to s3 for graph_rag
    if is_graph_rag_enabled:
        logger.info("Graph RAG is enabled, uploading chunk to s3 for graph_rag")
        (
            graph_rag_call_folder_path,
            graph_rag_call_file_path,
        ) = get_gong_call_graph_rag_s3_path(
            client_id=client_id,
            opportunity_id=opportunity_id,
            client_name=client_name,
            call_date=call_date_str,
            call_id=gong_call_id,
        )
        # the chunk created for aws bedrock is used here.
        upload_chunk_to_s3(
            file_path=graph_rag_call_file_path, chunk_data=chunk_data_bedrock
        )

    # return graph_rag_call_folder_path, graph_rag_call_file_path
    # create the gong call for client if it doesn't exist
    if not is_gong_call_in_sync:
        gong_object = GongCall(
            gong_call_id=gong_call_id,
            call_date=call_date,  # Use datetime directly
            s3_file_path=call_file_path,
            client_id=client_id,
            opportunity_id=opportunity_id,
        )
        gong_object.save()

        # add the metadata file if all calls are indexed in same knowledge base
        metadata = {
            "event_time": str(call_date_str),
            "client_id": int(client_id),
            "opportunity_id": str(opportunity_id),
            "gong_call_title": chunk_data.get("title", ""),
            "speakerAffiliation": chunk_data.get("speakerAffiliation", ""),
            "gong_call_link": chunk_data.get("url", ""),
        }
        create_metadata_file_for_entity(file_path=call_file_path, metadata=metadata)

        logger.info(f"Saving gong call {gong_call_id} metadata to database......")

    # if each gong call is indexed in a separate knowledge base
    if not SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS:
        # create vector collection for client if it doesn't exist
        check_and_create_vector_collection_for_client(
            client_id=client_id,
        )
        # vector collections and knowledge bases are created only when bedrock loading is enabled
        entity_id = EntityType.GONG_CALL.value

        # create the knowledge base for the gong_call_id if it doesn't exist
        if not KnowledgeBaseAccessor(
            client_id=client_id, opportunity_id=opportunity_id
        ).check_if_kb_exists_for_entity(entity_id=entity_id):
            vector_collection = (
                VectorCollectionAccessor(client_id=client_id).client_aware().first()
            )
            vector_collection_id = vector_collection.collection_id
            vector_collection_arn = vector_collection.collection_arn
            vector_collection_data_policy_name = (
                vector_collection.access_data_policy_name
            )

            vector_index_name = get_vector_index_name(opportunity_id=opportunity_id)
            knowledge_base_name = get_knowledge_base_name(
                client_id=client_id, opportunity_id=opportunity_id
            )

            (
                knowledge_base_meta,
                data_source_id,
                bedrock_role_arn,
            ) = create_knowledge_base_and_related_entities(
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "vector_collection_arn": vector_collection_arn,
                    "vector_collection_data_policy_name": vector_collection_data_policy_name,
                },
                data_source_s3_path=call_folder_path,
                knowledge_base_source_details={
                    "entity_type": EntityType.GONG_CALL.value,
                    "entity_id": entity_id,
                },
                vector_index_name=vector_index_name,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_description=f"Knowledge base for client_id: {client_id}, for entity: {EntityType.GONG_CALL.value}",
            )

            knowledge_base_object = KnowledgeBase(
                **knowledge_base_meta,
                bedrock_execution_arn=bedrock_role_arn,
                data_source_id=data_source_id,
                entity_id=entity_id,
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "index_name": vector_index_name,
                    "fields_data": {
                        "vectorField": "vector",
                        "textField": "text",
                        "metadataField": "text-metadata",
                    },
                },
                client_id=client_id,
                opportunity_id=opportunity_id,
                entity_type=EntityType.GONG_CALL.value,
            )
            knowledge_base_object.save()

            # add the details of the knowledge base to typesense
            add_knowledge_base_details_to_typesense(
                client_id=client_id,
                opportunity_id=opportunity_id,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_id=knowledge_base_meta["knowledge_base_id"],
                entity_id=entity_id,
                entity_type=EntityType.GONG_CALL.value,
                single_knowledge_base=SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS,
            )

            logger.info(
                f"Saving knowledge base {knowledge_base_meta['knowledge_base_name']} metadata to database......"
            )

    else:
        # create vector collection for client if it doesn't exist
        check_and_create_single_vector_collection()
        # vector collections and knowledge bases are created only when bedrock loading is enabled
        entity_id = EntityType.GONG_CALL.value
        # for gong calls, the entity id is same as the entity type
        entity_type = entity_id

        # create the knowledge base for the gong_call_id if it doesn't exist
        if not KnowledgeBaseMetaAccessor().check_if_kb_exists(entity_id=entity_id):
            vector_collection = VectorCollectionMetaAccessor().get_vector_collection()
            vector_collection_id = vector_collection.collection_id
            vector_collection_arn = vector_collection.collection_arn
            vector_collection_data_policy_name = (
                vector_collection.access_data_policy_name
            )

            vector_index_name = get_vector_index_name(opportunity_id=None)
            knowledge_base_name = get_knowledge_base_name_by_entity(entity_id=entity_id)
            (
                knowledge_base_meta,
                data_source_id,
                bedrock_role_arn,
            ) = create_knowledge_base_and_related_entities(
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "vector_collection_arn": vector_collection_arn,
                    "vector_collection_data_policy_name": vector_collection_data_policy_name,
                },
                data_source_s3_path=data_source_path,
                knowledge_base_source_details={
                    "entity_type": entity_type,
                    "entity_id": entity_id,
                },
                vector_index_name=vector_index_name,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_description="single knowledge base for gong calls",
            )

            knowledge_base_object = KnowledgeBaseMeta(
                **knowledge_base_meta,
                bedrock_execution_arn=bedrock_role_arn,
                data_source_id=data_source_id,
                entity_id=entity_id,
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "index_name": vector_index_name,
                    "fields_data": {
                        "vectorField": "vector",
                        "textField": "text",
                        "metadataField": "text-metadata",
                    },
                },
                entity_type=entity_type,
            )
            knowledge_base_object.save()

            # add the details of the knowledge base to typesense
            add_knowledge_base_details_to_typesense(
                client_id=client_id,
                opportunity_id=opportunity_id,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_id=knowledge_base_meta["knowledge_base_id"],
                entity_id=entity_id,
                entity_type=entity_type,
                single_knowledge_base=SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS,
            )

            logger.info(
                f"Saving knowledge base {knowledge_base_meta['knowledge_base_name']} metadata to database......"
            )

    # if the chunk is the last chunk of call, then sync the data soure
    if is_terminal_call:
        # before loading the file, if the calls are chunked manually, do manual chunking and upload the manually chunked files
        if (
            GONG_CALL_CHUNKING_STRATEGY
            == GongCallChunkingStrategy.MANUAL_CHUNKING.value
        ):
            print("Syncing the changes from files to the knowledge base")
            manual_chunking_of_gong_call_before_data_sync(
                call_file_path=call_file_path,
                call_folder_path=call_folder_path,
                gong_call_id=gong_call_id,
                metadata={
                    "call_time": str(call_date_str),
                    "client_id": int(client_id),
                    "opportunity_id": str(opportunity_id),
                    "gong_call_title": chunk_data.get("title", ""),
                    "speakerAffiliation": chunk_data.get("speakerAffiliation", ""),
                    "gong_call_link": chunk_data.get("url", ""),
                },
            )

        # get the knowledge base for the call
        knowledge_base = KnowledgeBaseMetaAccessor().entity_aware(entity_id=entity_id)
        # knowledge_base = KnowledgeBaseAccessor(
        #     client_id=client_id,
        #     opportunity_id=opportunity_id,
        # ).get_knowledge_base_for_entity(entity_id=entity_id)

        # sync the data from s3 to the knowledge base
        sync_s3_data_source(
            knowledge_base_id=knowledge_base.knowledge_base_id,
            data_source_id=knowledge_base.data_source_id,
        )

        # change the sync status of the gong call to loaded
        gong_call = GongCallAccessor(
            client_id=client_id, opportunity_id=opportunity_id
        ).get_gong_call_object(call_id=gong_call_id)
        if gong_call:
            gong_call.is_call_loaded = True
            gong_call.save()

        # attach the file to the long context prompt to be used by LLM as RAG tool
        attach_file_to_long_context_prompt(
            call_file_path=long_context_file_path,
            long_context_prompt_path=long_context_prompt_path,
            call_date=call_date_str,
        )
        # add the long context details to typesense
        add_llm_rag_details_to_typesense(
            client_id=client_id,
            opportunity_id=opportunity_id,
            entity_type=EntityType.GONG_CALL.value,
            long_context_prompt_path=long_context_prompt_path,
        )
        # index the files for graph_rag
        if is_graph_rag_enabled:
            logger.info("Graph RAG is enabled, indexing the files for graph_rag")
            graph_rag_indexed_parquet_path = index_files(
                s3_path=graph_rag_call_folder_path,
                input_type=EntityType.GONG_CALL.value,
            )
            print("indexing done")
            add_graph_rag_details_to_typesense(
                client_id=client_id,
                opportunity_id=opportunity_id,
                entity_type=EntityType.GONG_CALL.value,
                graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,
            )
            logger.info("Graph RAG indexing done and added meta data to typesense")


def process_hubspot_data(
    client_id: int,
    opportunity_id: str,
    chunk_data: dict,
    is_terminal_chunk: bool,
    entity_type: str,
) -> None:
    """
    Process HubSpot data (emails, notes) for integration into RAG systems.

    This function ingests HubSpot CRM data into multiple RAG systems :-
    1. AWS Bedrock Knowledge Base for semantic vector search
    2. Long context RAG for comprehensive LLM analysis
    3. Graph RAG for relationship and entity-based queries (if enabled)

    Args:
        opportunity_id (str): Salesforce opportunity ID that this HubSpot data relates to
        is_terminal_chunk (bool): True if this is the last chunk for this entity type/opportunity
        entity_type (str): Type of HubSpot entity being processed (e.g., 'email', 'note')

    Workflow:
        1. Preprocesses HubSpot data to extract relevant content and timestamps
        2. Uploads processed data to S3 in organized directory structures
        3. Creates or updates AWS Bedrock knowledge bases and vector collections
        4. On terminal chunk: syncs data sources to make content searchable

    Returns:
        None: Function persists the hubspot data to S3 and indexes it in the vector database in AWS Bedrock.

    """

    if not is_bedrock_loading_enabled(client_id=client_id):
        print("RAG loading is not enabled for the client")
        return

    chunk_obj, chunk_time = preprocess_hubspot_chunk(
        chunk_data=chunk_data, entity_type=entity_type
    )
    chunk_data_long_context = convert_hubspot_chunk_obj_to_text(
        chunk_data=chunk_obj, entity_type=entity_type
    )

    client = get_client(id=client_id, fields=["name"])
    client_name = client.name

    metadata = {
        "event_time": str(chunk_time),
        "client_id": int(client_id),
        "opportunity_id": str(opportunity_id),
    }

    # upload to bedrock based path
    file_suffix = get_hubspost_chunk_suffix_path(
        entity_type=entity_type, chunk_data=chunk_data
    )
    chunk_s3_path, upload_path = get_hubspot_entity_s3_path(
        client_id=client_id,
        opportunity_id=opportunity_id,
        client_name=client_name,
        entity_type=entity_type,
    )
    upload_hubspot_chunk_to_s3(
        file_path=chunk_s3_path,
        chunk_data=chunk_obj,
        entity_type=entity_type,
        suffix=file_suffix,
        metadata_json=metadata,
    )

    file_suffix_graph_rag = get_hubspost_chunk_suffix_path(
        entity_type=entity_type, chunk_data=chunk_data
    )
    # upload to graph rag based chunks
    is_graph_rag_enabled = has_feature(
        client_id=client_id, feature="is_graph_rag_enabled"
    )
    if is_graph_rag_enabled:
        logger.info("Graph RAG is enabled, uploading chunk to s3 for graph_rag")
        chunk_s3_graph_rag_path = get_hubspot_entity_graph_rag_s3_path(
            client_id=client_id,
            opportunity_id=opportunity_id,
            client_name=client_name,
            entity_type=entity_type,
        )
        upload_upload_hubspot_chunk_to_s3_graph_rag(
            file_path=chunk_s3_graph_rag_path,
            chunk_data=chunk_obj,
            entity_type=entity_type,
            suffix=file_suffix_graph_rag,
        )

    file_suffix_long_context = get_hubspost_chunk_suffix_path(
        entity_type=entity_type, chunk_data=chunk_data
    )

    # upload to long context rag based chunks
    (
        chunk_s3_long_context_file_path,
        chunk_s3_long_context_prompt_path,
    ) = get_hubspot_entity_long_context_s3_path(
        client_id=client_id,
        opportunity_id=opportunity_id,
        client_name=client_name,
        entity_type=entity_type,
        file_suffix=file_suffix_long_context,
    )
    upload_hubspot_chunk_to_s3_long_context(
        file_path=chunk_s3_long_context_file_path,
        chunk_data_long_context=chunk_data_long_context,
        entity_type=entity_type,
        suffix=file_suffix_long_context,
    )
    # return chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path
    # return chunk_s3_graph_rag_path #please remove this line
    # has only been added to test the graph rag

    # if different knowledge bases are used for each client
    if not SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS:
        # create vector collection for client if it doesn't exist
        check_and_create_vector_collection_for_client(
            client_id=client_id,
        )

        entity_id = ENTITY_TYPE_TO_ID.get(entity_type, "")

        # create the knowledge base for the email if it doesn't exist
        if not KnowledgeBaseAccessor(
            client_id=client_id, opportunity_id=opportunity_id
        ).check_if_kb_exists_for_entity(entity_id=entity_id):
            vector_collection = (
                VectorCollectionAccessor(client_id=client_id).client_aware().first()
            )
            vector_collection_id = vector_collection.collection_id
            vector_collection_arn = vector_collection.collection_arn
            vector_collection_data_policy_name = (
                vector_collection.access_data_policy_name
            )

            vector_index_name = get_vector_index_name_hubspot_chunk(
                entity_type=entity_type, opportunity_id=opportunity_id
            )
            knowledge_base_name = get_knowledge_base_name_hubspost(
                client_id=client_id,
                entity_type=entity_type,
                opportunity_id=opportunity_id,
            )
            logger.info(
                f"Creating a knowledge base {knowledge_base_name} with data indexed in {vector_index_name}"
            )

            (
                knowledge_base_meta,
                data_source_id,
                bedrock_role_arn,
            ) = create_knowledge_base_and_related_entities(
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "vector_collection_arn": vector_collection_arn,
                    "vector_collection_data_policy_name": vector_collection_data_policy_name,
                },
                data_source_s3_path=chunk_s3_path,
                knowledge_base_source_details={
                    "entity_type": entity_type,
                    "entity_id": entity_id,
                },
                vector_index_name=vector_index_name,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_description=f"Knowledge base for client_id: {client_id}, for entity: {entity_type}",
            )

            knowledge_base_object = KnowledgeBase(
                **knowledge_base_meta,
                bedrock_execution_arn=bedrock_role_arn,
                data_source_id=data_source_id,
                entity_id=entity_id,
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "index_name": vector_index_name,
                    "fields_data": {
                        "vectorField": "vector",
                        "textField": "text",
                        "metadataField": "text-metadata",
                    },
                },
                client_id=client_id,
                opportunity_id=opportunity_id,
                entity_type=entity_type,
            )
            knowledge_base_object.save()

            # add the details of the knowledge base to typesense
            add_knowledge_base_details_to_typesense(
                client_id=client_id,
                opportunity_id=opportunity_id,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_id=knowledge_base_meta["knowledge_base_id"],
                entity_id=entity_id,
                entity_type=entity_type,
                single_knowledge_base=SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS,
            )

            logger.info(
                f"Saving knowledge base {knowledge_base_meta['knowledge_base_name']} metadata to database......"
            )

    # single knowledge base for all clients
    else:
        # create vector collection for client if it doesn't exist
        check_and_create_single_vector_collection()

        # create the knowledge base for the gong_call_id if it doesn't exist
        if not KnowledgeBaseMetaAccessor().check_if_kb_exists(entity_id=entity_type):
            vector_collection = VectorCollectionMetaAccessor().get_vector_collection()
            vector_collection_id = vector_collection.collection_id
            vector_collection_arn = vector_collection.collection_arn
            vector_collection_data_policy_name = (
                vector_collection.access_data_policy_name
            )

            vector_index_name = get_vector_index_name_hubspot_chunk(
                entity_type=entity_type, opportunity_id=None
            )
            knowledge_base_name = get_knowledge_base_name_by_entity(
                entity_id=entity_type
            )
            (
                knowledge_base_meta,
                data_source_id,
                bedrock_role_arn,
            ) = create_knowledge_base_and_related_entities(
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "vector_collection_arn": vector_collection_arn,
                    "vector_collection_data_policy_name": vector_collection_data_policy_name,
                },
                data_source_s3_path=upload_path,
                knowledge_base_source_details={
                    "entity_type": entity_type,
                    "entity_id": entity_type,
                },
                vector_index_name=vector_index_name,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_description=f"single knowledge base for {entity_type}",
            )

            knowledge_base_object = KnowledgeBaseMeta(
                **knowledge_base_meta,
                bedrock_execution_arn=bedrock_role_arn,
                data_source_id=data_source_id,
                entity_id=entity_type,
                vector_collection_details={
                    "vector_collection_id": vector_collection_id,
                    "index_name": vector_index_name,
                    "fields_data": {
                        "vectorField": "vector",
                        "textField": "text",
                        "metadataField": "text-metadata",
                    },
                },
                entity_type=entity_type,
            )
            knowledge_base_object.save()

            # add the details of the knowledge base to typesense
            add_knowledge_base_details_to_typesense(
                client_id=client_id,
                opportunity_id=opportunity_id,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_id=knowledge_base_meta["knowledge_base_id"],
                entity_id=entity_type,
                entity_type=entity_type,
                single_knowledge_base=SINGLE_KNOWLEDGE_BASE_FOR_ALL_CLIENTS,
            )

            logger.info(
                f"Saving knowledge base {knowledge_base_meta['knowledge_base_name']} metadata to database......"
            )

    # if the chunk is the last chunk of call, then sync the data soure
    if is_terminal_chunk:
        knowledge_base = KnowledgeBaseMetaAccessor().entity_aware(entity_id=entity_type)

        # sync the data from s3 to the knowledge base
        sync_s3_data_source(
            knowledge_base_id=knowledge_base.knowledge_base_id,
            data_source_id=knowledge_base.data_source_id,
        )
        attach_file_to_long_context_prompt_hubspot_entity(
            entity_file_path=chunk_s3_long_context_file_path,
            long_context_prompt_path=chunk_s3_long_context_prompt_path,
        )

        add_llm_rag_details_to_typesense(
            client_id=client_id,
            opportunity_id=opportunity_id,
            entity_type=entity_type,
            long_context_prompt_path=chunk_s3_long_context_prompt_path,
        )
        if is_graph_rag_enabled:
            logger.info("Graph RAG is enabled, indexing the files for graph_rag")
            graph_rag_indexed_parquet_path = index_files(
                s3_path=chunk_s3_graph_rag_path, input_type=entity_type
            )
            if entity_type == EntityType.HUBSPOT_EMAIL.value:
                add_graph_rag_details_to_typesense(
                    client_id=client_id,
                    opportunity_id=opportunity_id,
                    entity_type=EntityType.HUBSPOT_EMAIL.value,
                    graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,
                )
            else:
                add_graph_rag_details_to_typesense(
                    client_id=client_id,
                    opportunity_id=opportunity_id,
                    entity_type=EntityType.HUBSPOT_NOTES.value,
                    graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,
                )
