{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4e3d9cf7", "metadata": {}, "outputs": [], "source": ["import django\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 21, "id": "4158ef23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting boto3==1.35.65\n", "  Obtaining dependency information for boto3==1.35.65 from https://files.pythonhosted.org/packages/b5/27/a16e04e751c00b9993e81bd5e381873a36523ad645a3d2b91323676c262d/boto3-1.35.65-py3-none-any.whl.metadata\n", "  Downloading boto3-1.35.65-py3-none-any.whl.metadata (6.7 kB)\n", "Requirement already satisfied: botocore<1.36.0,>=1.35.65 in /Users/<USER>/opt/anaconda3/envs/everstage_env/lib/python3.10/site-packages (from boto3==1.35.65) (1.35.66)\n", "Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /Users/<USER>/opt/anaconda3/envs/everstage_env/lib/python3.10/site-packages (from boto3==1.35.65) (1.0.1)\n", "Requirement already satisfied: s3transfer<0.11.0,>=0.10.0 in /Users/<USER>/opt/anaconda3/envs/everstage_env/lib/python3.10/site-packages (from boto3==1.35.65) (0.10.2)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /Users/<USER>/opt/anaconda3/envs/everstage_env/lib/python3.10/site-packages (from botocore<1.36.0,>=1.35.65->boto3==1.35.65) (2.9.0.post0)\n", "Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /Users/<USER>/opt/anaconda3/envs/everstage_env/lib/python3.10/site-packages (from botocore<1.36.0,>=1.35.65->boto3==1.35.65) (1.26.19)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/opt/anaconda3/envs/everstage_env/lib/python3.10/site-packages (from python-dateutil<3.0.0,>=2.1->botocore<1.36.0,>=1.35.65->boto3==1.35.65) (1.16.0)\n", "Downloading boto3-1.35.65-py3-none-any.whl (139 kB)\n", "   25l   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 0.0/139.2 kB ? eta -:--:--━━━━━━━━━━━╸━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 41.0/139.2 kB 1.2 MB/s eta 0:00:01━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╺━ 133.1/139.2 kB 2.0 MB/s eta 0:00:01━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.2/139.2 kB 1.4 MB/s eta 0:00:00\n", "\u001b[?25hInstalling collected packages: boto3\n", "  Attempting uninstall: boto3\n", "    Found existing installation: boto3 1.35.66\n", "    Uninstalling boto3-1.35.66:\n", "      Successfully uninstalled boto3-1.35.66\n", "Successfully installed boto3-1.35.65\n"]}], "source": ["!pip install boto3==1.35.65"]}, {"cell_type": "code", "execution_count": 13, "id": "6abfe6f3", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['S3_GONG_CALL_BUCKET'] = 'everstage-ai-infra-assets-test'\n", "\n", "\n", "aws_access_key_id=\"********************\"\n", "aws_secret_access_key=\"UMHPV28KvdE8fgo7RlkltVG/DwnwJxCrpcNqmIy2\"\n", "aws_session_token=\"IQoJb3JpZ2luX2VjEAgaCXVzLXdlc3QtMiJHMEUCIQCL2HWkfFCj80GF/spcWWN0erbHR2lKQn/GdjycPkgo/AIgF2I7YnR/KIAafK6R5OCQkwrCMqi47ErjjEhqczc4apsqkQMIof//////////ARADGgw5MTkyMTUzODY4MDEiDJhMXJqqPFDcFYrb3SrlAjGfkDo4y8IsyBGA7/Mf3irpjTLe2aRvI0VxN8Cb5brELYjR5xdQkVN0gzmIb9QVp8ur8b7mAUApunnUBf/HHHjqWfUjHfvN41URZRD5BXlCoxmcpJRNuSGt/swb2Rak2aOA8fzuZ6TRTYNQrkz34gvJrguboqI+kbw7nF5nlLQPGpAMYJ8HraQiPr6GGqdRwyp8pfCEmQW2lyb0Tv4uj5bD8nTZgZ3XKGJJwxx1UrdElqbssEy6cZIazn8zvKV6Uynz/Fc8obYTLB+hQb+IyNE6XP8t17Q/kYP8HXTsQ2ci9GQ5K834mTHA16PwfJ6LPbhILRFi3EXS0HTR7RNxVl5Ero9GIssELwYeaXwbSOxF5UQXoIAW3ifikz+90ZvF9F1PUG1iLaQtzgUoak6bqF7KcncCEoENHnwuTU/Lu4eLnXyKsKFhjXVDECIO89eSgnosZTNmrJIfdzOxSXBmFrdSNhYYaTCH1vu5BjqmAY5koDpHDidt0Hfg5CVXXDt9Rm1Vs3SuGS5eQN5cy7oBCsW2VeNfci1tswi/W4IZMyYTk9Fa+v9Xz9Dy7Zzhl6pyv/yLMjZXaHn0Jya8HIpcrpBYJuVd/WLrcIHL8lgLH8SXw/Zf+UHrz5uwcPYKxHHPRYB+kWxY9rvAxYCLzTBEhEvyrx+tj/O0k5NcKEOQ2Zx7vRz/s4EOuWj4DCOl6g7tj8MeSkU=\"\n", "\n", "\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_ACCESS_KEY_ID'] = aws_access_key_id\n", "os.environ['AWS_SECRET_ACCESS_KEY'] = aws_secret_access_key\n", "os.environ['AWS_SESSION_TOKEN'] = aws_session_token\n", "\n", "aws_session_creds = {\n", "    'region_name' : 'ap-south-1',\n", "    'aws_access_key_id' : aws_access_key_id,\n", "    'aws_secret_access_key' : aws_secret_access_key,\n", "    'aws_session_token' : aws_session_token\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cdf393f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.35.65\n"]}], "source": ["import boto3\n", "print(boto3.__version__)"]}, {"cell_type": "code", "execution_count": 4, "id": "6a3ac007", "metadata": {}, "outputs": [], "source": ["# upload a chunk to s3\n", "\n", "from datetime import datetime, timedelta\n", "import os\n", "import boto3\n", "\n", "\n", "def convert_millisecods_to_time(milliseconds):\n", "    \"\"\"\n", "    convert milliseconds to a time in the call\n", "    \"\"\"\n", "    # Convert milliseconds to a timed<PERSON><PERSON>\n", "    time_delta = timedelta(milliseconds=milliseconds)\n", "\n", "    # Format the time as HH:MM:SS.sss\n", "    formatted_time = (datetime.min + time_delta).strftime(\"%H:%M:%S.%f\")[:-3]\n", "    \n", "    return formatted_time\n", "\n", "\n", "def get_gong_call_s3_path(client_id: int, client_name: str, call_date: datetime, call_id: str):\n", "    \"\"\"\n", "    get the s3 path to upload the chunk\n", "    \"\"\"\n", "    return f\"{client_name}_{client_id}/{call_id}/{call_date}\"\n", "    \n", "\n", "def convert_chunk_to_text(chunk_data: dict):\n", "    \"\"\"\n", "    convert the gong chunk to a text\n", "    \"\"\"\n", "    \n", "    call_text = \"\"\n", "    for chunk in chunk_data.get(\"transcript\", []):\n", "        speaker = chunk.get(\"speaker_name\", \"\")\n", "        for sentence in chunk.get(\"sentences\", []):\n", "            start_time = convert_millisecods_to_time(sentence.get(\"start\", 0))\n", "            end_time = convert_millisecods_to_time(sentence.get(\"end\", 0))\n", "            text = sentence.get(\"text\")\n", "            \n", "            sentence_text = f\"{start_time} | {speaker} \\n {text}\"\n", "            call_text += f\"\\n {sentence_text}\"\n", "            \n", "    return call_text\n", "            \n", "\n", "def upload_chunk_to_s3(client_id: int, client_name: str, chunk_data:dict, chunk_index: int, gong_call_id: str, call_date: datetime):\n", "    \"\"\"\n", "    upload the chunk to s3\n", "    \"\"\"\n", "    \n", "    chunk_text = convert_chunk_to_text(chunk_data)\n", "    file_path = get_gong_call_s3_path(client_id, client_name, call_date, gong_call_id)\n", "    chunk_path = f\"{file_path}/{chunk_index}.txt\"\n", "    \n", "    print(chunk_text)\n", "\n", "    # Initialize S3 client\n", "    s3_client = boto3.client('s3', **aws_session_creds)\n", "\n", "    # Define parameters\n", "    bucket_name = os.getenv('S3_GONG_CALL_BUCKET')\n", "\n", "    # Upload the string as a .txt file\n", "    s3_client.put_object(Bucket=bucket_name, Key=chunk_path, Body=chunk_text)\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 23, "id": "f95d5807", "metadata": {}, "outputs": [], "source": ["# check and create a vector store for the client\n", "\"\"\"\n", "1. create a vector store collection\n", "2. create a vector index\n", "\"\"\"\n", "\n", "from uuid import uuid4\n", "import json\n", "import random\n", "from opensearchpy import OpenSearch, RequestsHttpConnection, AWSV4SignerAuth, RequestError\n", "import time\n", "# from retrying import retry\n", "\n", "\n", "# mapping of embedding models present in each region\n", "region_to_embedding_model_arns_mapping = {\n", "    \"ap-south-1\": [\n", "        \"arn:aws:bedrock:ap-south-1::foundation-model/cohere.embed-english-v3\",\n", "        \"arn:aws:bedrock:ap-south-1::foundation-model/amazon.titan-embed-text-v1\",\n", "        \"arn:aws:bedrock:ap-south-1::foundation-model/amazon.titan-embed-text-v2:0\"\n", "    ]\n", "}\n", "\n", "# mapping of chosen embedding model for each region\n", "default_embedding_model_arn_mapping = {\n", "    \"ap-south-1\" : region_to_embedding_model_arns_mapping[ \"ap-south-1\"][0]\n", "}\n", "\n", "# mapping of foundational models for parsing data in each region\n", "region_to_foundational_model_arns_mapping = {\n", "    \"ap-south-1\" : [\n", "        \"arn:aws:bedrock:ap-south-1::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0\",\n", "        \"arn:aws:bedrock:ap-south-1::foundation-model/anthropic.claude-3-haiku-20240307-v1:0\"\n", "    ]\n", "}\n", "\n", "# mapping of chosen foundational model for each region\n", "default_foundational_model_arn_mapping = {\n", "    \"ap-south-1\" : region_to_foundational_model_arns_mapping[\"ap-south-1\"][0]\n", "}\n", "\n", "\n", "def create_bedrock_execution_role(bucket_name: str, vector_collecion_arn: str):\n", "    \n", "    suffix = f\"{uuid4()}\"\n", "    bedrock_execution_role_name = f'BedrockKnowledgeBase_{suffix}'\n", "    fm_policy_name = f'BedrockFoundationalPolicy_{suffix}'\n", "    s3_policy_name = f'BedrockS3Policy_{suffix}'\n", "    sm_policy_name = f'BedrockSecretPolicy_{suffix}'\n", "    oss_policy_name = f'BedrockOSSPolicy_{suffix}'\n", "    \n", "    region_name = os.getenv('AWS_REGION')\n", "    \n", "    account_number = boto3.client('sts', **aws_session_creds).get_caller_identity().get('Account')\n", "    \n", "    iam_client = boto3.client('iam', **aws_session_creds)\n", "\n", "    foundation_model_policy_document = {\n", "        \"Version\": \"2012-10-17\",\n", "        \"Statement\": [\n", "            {\n", "                \"Effect\": \"Allow\",\n", "                \"Action\": [\n", "                    \"bedrock:InvokeModel\",\n", "                ],\n", "                \"Resource\": region_to_embedding_model_arns_mapping[region_name] + region_to_foundational_model_arns_mapping[region_name]\n", "            }\n", "        ]\n", "    }\n", "\n", "\n", "    s3_policy_document = {\n", "        \"Version\": \"2012-10-17\",\n", "        \"Statement\": [\n", "            {\n", "                \"Effect\": \"Allow\",\n", "                \"Action\": [\n", "                    \"s3:GetObject\",\n", "                    \"s3:ListBucket\"\n", "                ],\n", "                \"Resource\": [\n", "                    f\"arn:aws:s3:::{bucket_name}\",\n", "                    f\"arn:aws:s3:::{bucket_name}/*\"\n", "                ],\n", "                \"Condition\": {\n", "                    \"StringEquals\": {\n", "                        \"aws:ResourceAccount\": f\"{account_number}\"\n", "                    }\n", "                }\n", "            }\n", "        ]\n", "    }\n", "    \n", "    oss_policy_document = {\n", "        \"Version\": \"2012-10-17\",\n", "        \"Statement\": [\n", "            {\n", "                \"Sid\": \"OpenSearchServerlessAPIAccessAllStatement\",\n", "                \"Effect\": \"Allow\",\n", "                \"Action\": [\n", "                    \"aoss:APIAccessAll\"\n", "                ],\n", "                \"Resource\": [\n", "                    vector_collecion_arn\n", "                ]\n", "            }\n", "        ]\n", "    }\n", "\n", "    assume_role_policy_document = {\n", "        \"Version\": \"2012-10-17\",\n", "        \"Statement\": [\n", "            {\n", "                \"Effect\": \"Allow\",\n", "                \"Principal\": {\n", "                    \"Service\": \"bedrock.amazonaws.com\"\n", "                },\n", "                \"Action\": \"sts:AssumeRole\"\n", "            }\n", "        ]\n", "    }\n", "    \n", "    fm_policy = iam_client.create_policy(\n", "        PolicyName=fm_policy_name,\n", "        PolicyDocument=json.dumps(foundation_model_policy_document),\n", "        Description='Policy for accessing foundation model',\n", "    )\n", "\n", "    s3_policy = iam_client.create_policy(\n", "        PolicyName=s3_policy_name,\n", "        PolicyDocument=json.dumps(s3_policy_document),\n", "        Description='Policy for reading documents from s3'\n", "    )\n", "    \n", "    oss_policy = iam_client.create_policy(\n", "        PolicyName=oss_policy_name,\n", "        PolicyDocument=json.dumps(oss_policy_document),\n", "        Description='Policy for reading opensearch collections'\n", "    )\n", "\n", "    # create bedrock execution role\n", "    bedrock_kb_execution_role = iam_client.create_role(\n", "        RoleName=bedrock_execution_role_name,\n", "        AssumeRolePolicyDocument=json.dumps(assume_role_policy_document),\n", "        Description='Amazon Bedrock Knowledge Base Execution Role for accessing OSS and S3',\n", "        MaxSessionDuration=3600\n", "    )\n", "\n", "    # fetch arn of the policies and role created above\n", "    bedrock_kb_execution_role_arn = bedrock_kb_execution_role['Role']['Arn']\n", "    s3_policy_arn = s3_policy[\"Policy\"][\"Arn\"]\n", "    fm_policy_arn = fm_policy[\"Policy\"][\"Arn\"]\n", "    oss_policy_arn = oss_policy[\"Policy\"][\"Arn\"]\n", "    \n", "    # attach policies to Amazon Bedrock execution role\n", "    iam_client.attach_role_policy(\n", "        RoleName=bedrock_kb_execution_role[\"Role\"][\"RoleName\"],\n", "        PolicyArn=fm_policy_arn\n", "    )\n", "    iam_client.attach_role_policy(\n", "        RoleName=bedrock_kb_execution_role[\"Role\"][\"RoleName\"],\n", "        PolicyArn=s3_policy_arn\n", "    )\n", "    iam_client.attach_role_policy(\n", "        RoleName=bedrock_kb_execution_role[\"Role\"][\"RoleName\"],\n", "        PolicyArn=oss_policy_arn\n", "    )\n", "\n", "    return bedrock_kb_execution_role\n", "\n", "\n", "\n", "def create_policies_in_oss(client_id, vector_store_name, aoss_client):\n", "    \"\"\"\n", "    create policies to enable creation of opensearch servless collection\n", "    \"\"\"\n", "    \n", "    suffix = f\"{uuid4().hex[:16]}\"\n", "    encryption_policy_name = f\"bedrockossp{suffix}\"\n", "    network_policy_name = f\"bedrocknp{suffix}\"\n", "    access_policy_name = f\"bedrockdap{suffix}\"\n", "    identity = boto3.client('sts', **aws_session_creds).get_caller_identity()['Arn']\n", "    \n", "    encryption_policy = aoss_client.create_security_policy(\n", "        name=encryption_policy_name,\n", "        policy=json.dumps(\n", "            {\n", "                'Rules': [{'Resource': ['collection/' + vector_store_name],\n", "                           'ResourceType': 'collection'}],\n", "                'AWSOwned<PERSON>ey': True\n", "            }),\n", "        type='encryption'\n", "    )\n", "\n", "    network_policy = aoss_client.create_security_policy(\n", "        name=network_policy_name,\n", "        policy=json.dumps(\n", "            [\n", "                {'Rules': [{'Resource': ['collection/' + vector_store_name],\n", "                            'ResourceType': 'collection'}],\n", "                 'AllowFromPublic': True}\n", "            ]),\n", "        type='network'\n", "    )\n", "    access_policy = aoss_client.create_access_policy(\n", "        name=access_policy_name,\n", "        policy=json.dumps(\n", "            [\n", "                {\n", "                    'Rules': [\n", "                        {\n", "                            'Resource': ['collection/' + vector_store_name],\n", "                            'Permission': [\n", "                                'aoss:CreateCollectionItems',\n", "                                'aoss:DeleteCollectionItems',\n", "                                'aoss:UpdateCollectionItems',\n", "                                'aoss:DescribeCollectionItems'],\n", "                            'ResourceType': 'collection'\n", "                        },\n", "                        {\n", "                            'Resource': ['index/' + vector_store_name + '/*'],\n", "                            'Permission': [\n", "                                'aoss:CreateIndex',\n", "                                'aoss:DeleteIndex',\n", "                                'aoss:UpdateIndex',\n", "                                'aoss:DescribeIndex',\n", "                                'aoss:ReadDocument',\n", "                                'aoss:WriteDocument'],\n", "                            'ResourceType': 'index'\n", "                        }\n", "                    ],\n", "                    'Principal': [identity],\n", "                    'Description': 'Easy data policy'\n", "                }\n", "            ]\n", "        ),\n", "        type='data'\n", "    )\n", "    return encryption_policy, network_policy, access_policy\n", "\n", "\n", "def attach_arn_to_data_policy_for_collection(data_policy_name: str, bedrock_execution_role_arn: str):\n", "    \"\"\"\n", "    attach a new bedrock execution role ARN to the data access policy of the collection\n", "    aoss_client -> amazon open search serverless collection client\n", "    \"\"\"\n", "    # check if the policy present before upading it\n", "    try:\n", "        policy_details = aoss_client.get_access_policy(\n", "            name=data_policy_name,\n", "            type='data'\n", "        )\n", "        changes = False\n", "        policy_version = policy_details['accessPolicyDetail']['policyVersion']\n", "        for policy in policy_details['accessPolicyDetail']['policy']:\n", "            if 'Principal' in policy and bedrock_execution_role_arn not in policy['Principal']:\n", "                # attach the bedrock execution role to every data policy\n", "                policy['Principal'].append(bedrock_execution_role_arn)\n", "                changes = True\n", "\n", "        if changes:\n", "            updated_policy = json.dumps(policy_details['accessPolicyDetail'].get('policy', []))\n", "            response = aoss_client.update_access_policy(\n", "                name=data_policy_name,\n", "                type='data',\n", "                policy=updated_policy,\n", "                policyVersion=policy_version\n", "            )\n", "\n", "    except Exception as e:\n", "        print(f\"Error in updating the data access policy {e}\")\n", "\n", "\n", "def interactive_sleep(seconds: int):\n", "    dots = ''\n", "    for i in range(seconds):\n", "        dots += '.'\n", "        print(dots, end='\\r')\n", "        time.sleep(1)\n", "\n", "\n", "def create_vector_store_for_client(vector_store_name):\n", "    \"\"\"\n", "    \"\"\"\n", "    collection = aoss_client.create_collection(name=vector_store_name, type='VECTORSEARCH')\n", "    # since vector collection creation takes time, sleep for 30s\n", "    interactive_sleep(30)\n", "    return collection\n", "\n", "\n", "def create_index_for_knowledge_base(collection_name, collection_id):\n", "    \"\"\"\n", "    create an index in opensearch for the new knowledge base to be created\n", "    \"\"\"\n", "\n", "    # Create the vector index in Opensearch serverless, with the knn_vector field index mapping, specifying the dimension size, name and engine.\n", "    credentials = boto3.Session(**aws_session_creds).get_credentials()\n", "    region_name = os.getenv('AWS_REGION')\n", "    service = 'aoss'\n", "    awsauth = auth = AWSV4SignerAuth(credentials, region_name, service)\n", "    \n", "    index_name = f\"bedrock-index-{collection_name}\"\n", "    body_json = {\n", "       \"settings\": {\n", "          \"index.knn\": \"true\",\n", "           \"number_of_shards\": 1,\n", "           \"knn.algo_param.ef_search\": 512,\n", "           \"number_of_replicas\": 0,\n", "       },\n", "       \"mappings\": {\n", "          \"properties\": {\n", "             \"vector\": {\n", "                \"type\": \"knn_vector\",\n", "                \"dimension\": 1024,\n", "                 \"method\": {\n", "                     \"name\": \"hnsw\",\n", "                     \"engine\": \"faiss\",\n", "                     \"space_type\": \"l2\"\n", "                 },\n", "             },\n", "             \"text\": {\n", "                \"type\": \"text\"\n", "             },\n", "             \"text-metadata\": {\n", "                \"type\": \"text\"\n", "             }\n", "          }\n", "       }\n", "    }\n", "\n", "    # Build the OpenSearch client\n", "    opensearch_host_url = collection_id + '.' + region_name + f'.{service}' + '.amazonaws.com'\n", "    oss_client = OpenSearch(\n", "        hosts=[{'host': opensearch_host_url, 'port': 443}],\n", "        http_auth=awsauth,\n", "        use_ssl=True,\n", "        verify_certs=True,\n", "        connection_class=RequestsHttpConnection,\n", "        timeout=300\n", "    )\n", "    \n", "    # Create index\n", "    try:\n", "        response = oss_client.indices.create(index=index_name, body=json.dumps(body_json))\n", "        return response\n", "        # since vector index creation takes time, sleep for 30s\n", "        interactive_sleep(30)\n", "    except RequestError as e:\n", "        print(f'Error while trying to create the index, with error {e.error}\\nyou may unmark the delete above to delete, and recreate the index')\n", "\n", "\n", "\n", "# # @retry(wait_random_min=1000, wait_random_max=2000, stop_max_attempt_number=1)\n", "def create_knowledge_base_for_gong_call(client_id: int, gong_call_id: str, bedrock_execution_role_arn: str, opensearch_collection_arn: str, vector_index_name: str):\n", "    \"\"\"\n", "    creates a knowledge base for the gong call with retry mechanism\n", "    \"\"\"\n", "    \n", "    knowledge_base_name = f\"everstage-kb-{client_id}-{gong_call_id}\"\n", "    bedrock_agent_client = boto3.client('bedrock-agent', **aws_session_creds)\n", "    \n", "    opensearchServerlessConfiguration = {\n", "        \"collectionArn\": opensearch_collection_arn,\n", "        \"vectorIndexName\": vector_index_name,\n", "        \"fieldMapping\": {\n", "            \"vectorField\": \"vector\",\n", "            \"textField\": \"text\",\n", "            \"metadataField\": \"text-metadata\"\n", "        }\n", "    }\n", "    \n", "    region_name = os.getenv('AWS_REGION')\n", "    \n", "    create_kb_response = bedrock_agent_client.create_knowledge_base(\n", "        name = knowledge_base_name,\n", "        description = f\"Knowledge base created for client {client_id} for the gong call {gong_call_id}\",\n", "        roleArn = bedrock_execution_role_arn,\n", "        knowledgeBaseConfiguration = {\n", "            \"type\": \"VECTOR\",\n", "            \"vectorKnowledgeBaseConfiguration\": {\n", "                \"embeddingModelArn\": default_embedding_model_arn_mapping[region_name]\n", "            }\n", "        },\n", "        storageConfiguration = {\n", "            \"type\": \"OPENSEARCH_SERVERLESS\",\n", "            \"opensearchServerlessConfiguration\":opensearchServerlessConfiguration\n", "        },\n", "    )\n", "    \n", "    return create_kb_response[\"knowledgeBase\"]\n", "\n", "\n", "def add_data_source_to_knowledge_base(knowledge_base, bucket_name, s3_file_path):\n", "    \"\"\"\n", "    given the arn of a knowledge base, add the given data source to the knowledge base\n", "    \"\"\"\n", "\n", "    bedrock_agent_client = boto3.client('bedrock-agent', **aws_session_creds)\n", "    data_source_name = \"bedrock-default-knowledge-base\"\n", "    s3Configuration = {\n", "        \"bucketArn\": f\"arn:aws:s3:::{bucket_name}\",\n", "        'inclusionPrefixes': [\n", "            s3_file_path,\n", "        ]\n", "    }\n", "    region_name = os.getenv('AWS_REGION')\n", "    \n", "    # Ingest strategy - How to ingest data from the data source\n", "    # default choice is semnatic chunking using claude sonnet for parsing\n", "    vectorIngestionConfiguration={\n", "        'chunkingConfiguration': {\n", "            'chunkingStrategy': 'SEMANTIC',\n", "            'semanticChunkingConfiguration': {\n", "                'breakpointPercentileThreshold': 95,\n", "                'bufferSize': 0,\n", "                'maxTokens': 300\n", "            }\n", "        },\n", "        'parsingConfiguration': {\n", "            'bedrockFoundationModelConfiguration': {\n", "                'modelArn': default_foundational_model_arn_mapping[region_name],\n", "            },\n", "            'parsingStrategy': 'BEDROCK_FOUNDATION_MODEL'\n", "        }\n", "    }\n", "    \n", "    create_ds_response = bedrock_agent_client.create_data_source(\n", "        name = data_source_name,\n", "        description = \"Gong call data source for knowledge base\",\n", "        knowledgeBaseId = knowledge_base['knowledgeBaseId'],\n", "        dataSourceConfiguration = {\n", "            \"type\": \"S3\",\n", "            \"s3Configuration\" : s3Configuration\n", "        },\n", "        vectorIngestionConfiguration = vectorIngestionConfiguration\n", "    )\n", "    ds = create_ds_response[\"dataSource\"]\n", "    return ds\n", "\n", "\n", "def sync_s3_data_source(knowledge_base_id: str, data_source_id: str):\n", "    \"\"\"\n", "    after all the chunks of the gong call are added to s3, sync the data source to chunk and embed them in the vector index\n", "    \"\"\"\n", "    \n", "    bedrock_agent_client = boto3.client('bedrock-agent', **aws_session_creds)\n", "    sync_job_response = bedrock_agent_client.start_ingestion_job(knowledgeBaseId = knowledge_base_id, dataSourceId = data_source_id)\n", "    job = sync_job_response[\"ingestionJob\"]\n", "    while(job['status'] != 'COMPLETE' ):\n", "        get_job_response = bedrock_agent_client.get_ingestion_job(\n", "          knowledgeBaseId = knowledge_base_id,\n", "            dataSourceId = data_source_id,\n", "            ingestionJobId = job[\"ingestionJobId\"]\n", "      )\n", "        job = get_job_response[\"ingestionJob\"]\n", "        print(job)\n", "        interactive_sleep(10)\n", "\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 12, "id": "8e5d4704", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:opensearch:PUT https://i709tmcmd9e3anir3mzk.ap-south-1.aoss.amazonaws.com:443/bedrock-index-gong-dev-test [status:200 request:0.969s]\n"]}], "source": ["# create the bedrock execution role, the vector store collection and index and the security policies\n", "\n", "aoss_client = boto3.client('opensearchserverless', **aws_session_creds)\n", "collection_name = 'gong-dev-test'\n", "\n", "# encryption_policy, network_policy, access_policy = create_policies_in_oss(\n", "#     1, vector_store_name=collection_name, aoss_client=aoss_client\n", "# )\n", "\n", "# collection = create_vector_store_for_client(collection_name)\n", "collection_arn = collection['createCollectionDetail']['arn']\n", "\n", "# bedrock_kb_execution_role = create_bedrock_execution_role('everstage-ai-infra-assets-test', collection_arn)\n", "# bedrock_kb_execution_role_arn = bedrock_kb_execution_role['Role']['Arn']\n", "\n", "# data_policy_name = access_policy['accessPolicyDetail']['name']\n", "\n", "# attach_arn_to_data_policy_for_collection(data_policy_name, bedrock_kb_execution_role_arn)\n", "\n", "index = create_index_for_knowledge_base(collection_name, collection['createCollectionDetail']['id'])\n", "index_name = index['index']"]}, {"cell_type": "code", "execution_count": 24, "id": "72e2037b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'dataSourceId': 'CRBU9NAUCK', 'ingestionJobId': 'JRGWNBJEIX', 'knowledgeBaseId': 'DABGGFKUMK', 'startedAt': datetime.datetime(2024, 11, 21, 8, 21, 20, 160324, tzinfo=tzlocal()), 'statistics': {'numberOfDocumentsDeleted': 0, 'numberOfDocumentsFailed': 0, 'numberOfDocumentsScanned': 0, 'numberOfMetadataDocumentsModified': 0, 'numberOfMetadataDocumentsScanned': 0, 'numberOfModifiedDocumentsIndexed': 0, 'numberOfNewDocumentsIndexed': 0}, 'status': 'STARTING', 'updatedAt': datetime.datetime(2024, 11, 21, 8, 21, 20, 160324, tzinfo=tzlocal())}\n", "{'dataSourceId': 'CRBU9NAUCK', 'ingestionJobId': 'JRGWNBJEIX', 'knowledgeBaseId': 'DABGGFKUMK', 'startedAt': datetime.datetime(2024, 11, 21, 8, 21, 20, 160324, tzinfo=tzlocal()), 'statistics': {'numberOfDocumentsDeleted': 0, 'numberOfDocumentsFailed': 0, 'numberOfDocumentsScanned': 6, 'numberOfMetadataDocumentsModified': 0, 'numberOfMetadataDocumentsScanned': 4, 'numberOfModifiedDocumentsIndexed': 0, 'numberOfNewDocumentsIndexed': 0}, 'status': 'COMPLETE', 'updatedAt': datetime.datetime(2024, 11, 21, 8, 21, 20, 911564, tzinfo=tzlocal())}\n", "..........\r"]}], "source": ["# create a new knowledge base, data source for the knowledge base and sync data source to knowledge base\n", "\n", "bedrock_kb_execution_role_arn = 'arn:aws:iam::919215386801:role/BedrockKnowledgeBase_b25210c7-ec76-4ecd-9f72-8354fb7c785e'\n", "collection_arn = 'arn:aws:aoss:ap-south-1:919215386801:collection/i709tmcmd9e3anir3mzk'\n", "index_name = 'bedrock-index-gong-dev-test'\n", "\n", "# knowledge_base = create_knowledge_base_for_gong_call(1, '123456', bedrock_kb_execution_role_arn, collection_arn, index_name)\n", "# data_source = add_data_source_to_knowledge_base(knowledge_base, 'everstage-ai-infra-assets-test', 'gong_calls/')\n", "knowledge_base_id = knowledge_base['knowledgeBaseId']\n", "\n", "data_source_id = 'CRBU9NAUCK'\n", "sync_s3_data_source(knowledge_base_id, data_source_id)"]}, {"cell_type": "code", "execution_count": 15, "id": "556bc2a5", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'aoss_client' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[15], line 6\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;66;03m# Retrieve the list of security policies\u001b[39;00m\n\u001b[0;32m----> 6\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43maoss_client\u001b[49m\u001b[38;5;241m.\u001b[39mlist_access_policies(\n\u001b[1;32m      7\u001b[0m     \u001b[38;5;28mtype\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m'\u001b[39m,  \u001b[38;5;66;03m# Specify 'network' or 'data' if applicable\u001b[39;00m\n\u001b[1;32m      8\u001b[0m )\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# Fetch details of the desired policy\u001b[39;00m\n\u001b[1;32m     12\u001b[0m policy_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbedrockdap2129bc395ba74e61\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'aoss_client' is not defined"]}], "source": ["import boto3\n", "import json\n", "\n", "\n", "# Retrieve the list of security policies\n", "response = aoss_client.list_access_policies(\n", "    type='data',  # Specify 'network' or 'data' if applicable\n", ")\n", "\n", "\n", "# Fetch details of the desired policy\n", "policy_name = 'bedrockdap2129bc395ba74e61'\n", "policy_json = next((item for item in response['accessPolicySummaries'] if item['name'] == policy_name), None)\n", "\n", "if policy_json:\n", "    \n", "    policy_details = aoss_client.get_access_policy(\n", "        name=policy_name,\n", "        type='data'  # Adjust type as needed: 'encryption', 'network', or 'data'\n", "    )\n", "    policy_version = policy_json['policyVersion']\n", "    for policy in policy_details['accessPolicyDetail']['policy']:\n", "        if 'Principal' in policy:\n", "            policy['Principal'].append('arn:aws:iam::919215386801:role/service-role/AmazonBedrockExecutionRoleForKnowledgeBase_uik4t')\n", "            \n", "    updated_policy = json.dumps(policy_details['accessPolicyDetail']['policy'])\n", "\n", "    response = aoss_client.update_access_policy(\n", "        name=policy_name,\n", "        type='data',\n", "        policy=updated_policy,\n", "        policyVersion=policy_version\n", "    )\n", "\n", "else:\n", "    raise Exception(\"Policy not found\")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "a467ed5d", "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}