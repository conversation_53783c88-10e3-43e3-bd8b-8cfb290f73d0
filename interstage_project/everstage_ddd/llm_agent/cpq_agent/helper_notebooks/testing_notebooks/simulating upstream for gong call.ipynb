{"cells": [{"cell_type": "code", "execution_count": 62, "id": "2befb896", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%reload_ext autoreload"]}, {"cell_type": "code", "execution_count": 63, "id": "e33375ea", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import sys\n", "\n", "from everstage_ddd.llm_agent.cpq_agent.services import process_gong_call_chunk, process_hubspot_data\n", "from everstage_ddd.llm_agent.cpq_agent.graph_rag_utils import index_files\n", "import uuid\n", "from everstage_ddd.llm_agent.cpq_agent.utility import EntityType, add_graph_rag_details_to_typesense, add_hubspot_email_graph_rag_details_to_typesense, add_hubspot_notes_graph_rag_details_to_typesense, attach_file_to_long_context_prompt, add_llm_rag_details_to_typesense\n", "import boto3\n", "\n"]}, {"cell_type": "code", "execution_count": 64, "id": "0d9ed962", "metadata": {}, "outputs": [], "source": ["os.chdir('/Users/<USER>/everstage-spm/interstage_project')"]}, {"cell_type": "code", "execution_count": 65, "id": "d0eeece7", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, set_key\n", "\n", "os.environ['AWS_REGION'] = 'AP-SOUTH-1'\n", "os.environ['S3_GONG_CALL_BUCKET'] = 'everstage-cpq-ai-assets'\n", "\n", "os.environ['OPENAI_API_KEY'] = '***************************************************'\n", "\n", "aws_access_key_id=\"********************\"\n", "aws_secret_access_key=\"JVBaUy5nFKgGnvvJFwFRm4xqkrXxMdbGyycOly1U\"\n", "aws_session_token=\"IQoJb3JpZ2luX2VjEC8aCXVzLXdlc3QtMiJHMEUCIQC7zOEaHBA/ynVWwsjw93DR8Pe2tw1FWHe5t8DU11sBvwIgbW+9mVC1OOO9z6lDXgEWJ15Wu+FEdZOUt5fcPqIupsEqrAMI5///////////ARADGgw5MTkyMTUzODY4MDEiDAzl1B1F5/fkGe9H8yqAA/5ie41PtKVQs/cBYzAGr58yOSoJbKa3iG/3SNVaplJCcN/UnRlNewDaqVOa45rRRFtOXjtrf4bCcKYKhdudpKB9kU84mp+XhinDfB5ZffSd4YPyxZFinQx74ysGQzoYe5zwMfuRlBmUomFdTN8FP5obqX659fP5n8fsnUfr20HP4i0w0XIlqeGftFc6i66HOcRWpKTDng9JGG3zQkTzKDCd6As4lE6Smxe8+i0AaQt3Ux9a6WKqoUx/wwo3gX1GLQdsgGUCqyKQrsFsNwTBrVK8HNZdTK9Jori/TDnOVReRGofl+IRd8IO++mLMPuNfxsgTAnt6PVGuPwDqRB+KveHz9A2CWIZbvMQI93ejRTqE8A7jiAZzcp26WDJgagqWjQiM9wxC5dDNO9sa1oSSLciumRfSEyxkR5MX1vGb9hQtlMYYkLH66xSR1afUqARrD91urefRq8KRKS1herJyA+StLvSyo56VMr8IQAMqYSPlfWh7caYBKo/bgCWoUjevTDCgp8DBBjqmAQ4f8FS2BZYQJws50haTAukS+JnrGyXVsJupnYZOJvDQfxbyzloY3BPokuaATmFFwzMrkVaoNrrSMv8I6WG2UCwAjEfsIn0MVgHZezWArgQec+yT+gydXJBwreYlgUaHDpATShhgJ33VwZLT2qFW0413nx/OHsF0xE2dOt80kPYPp69TOo4HJGM0I3Y9mIDZyU5DUo/izGgUY6m3JQGNIOJk0PDyaAo=\"\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_ACCESS_KEY_ID'] = aws_access_key_id\n", "os.environ['AWS_SECRET_ACCESS_KEY'] = aws_secret_access_key\n", "os.environ['AWS_SESSION_TOKEN'] = aws_session_token\n", "\n", "# # Update .env file directly\n", "set_key('.env', 'AWS_ACCESS_KEY_ID', aws_access_key_id)\n", "set_key('.env', 'AWS_SECRET_ACCESS_KEY', aws_secret_access_key)\n", "set_key('.env', 'AWS_SESSION_TOKEN', aws_session_token)\n", "set_key('.env', 'AWS_DEFAULT_REGION', 'ap-south-1')\n", "\n", "# # Reload environment\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 66, "id": "e82e9ca4", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_DEFAULT_REGION'] = 'ap-south-1'"]}, {"cell_type": "code", "execution_count": null, "id": "444580ee", "metadata": {}, "outputs": [], "source": ["export AWS_ACCESS_KEY_ID=\"********************\"\n", "export AWS_SECRET_ACCESS_KEY=\"JVBaUy5nFKgGnvvJFwFRm4xqkrXxMdbGyycOly1U\"\n", "export AWS_SESSION_TOKEN=\"IQoJb3JpZ2luX2VjEC8aCXVzLXdlc3QtMiJHMEUCIQC7zOEaHBA/ynVWwsjw93DR8Pe2tw1FWHe5t8DU11sBvwIgbW+9mVC1OOO9z6lDXgEWJ15Wu+FEdZOUt5fcPqIupsEqrAMI5///////////ARADGgw5MTkyMTUzODY4MDEiDAzl1B1F5/fkGe9H8yqAA/5ie41PtKVQs/cBYzAGr58yOSoJbKa3iG/3SNVaplJCcN/UnRlNewDaqVOa45rRRFtOXjtrf4bCcKYKhdudpKB9kU84mp+XhinDfB5ZffSd4YPyxZFinQx74ysGQzoYe5zwMfuRlBmUomFdTN8FP5obqX659fP5n8fsnUfr20HP4i0w0XIlqeGftFc6i66HOcRWpKTDng9JGG3zQkTzKDCd6As4lE6Smxe8+i0AaQt3Ux9a6WKqoUx/wwo3gX1GLQdsgGUCqyKQrsFsNwTBrVK8HNZdTK9Jori/TDnOVReRGofl+IRd8IO++mLMPuNfxsgTAnt6PVGuPwDqRB+KveHz9A2CWIZbvMQI93ejRTqE8A7jiAZzcp26WDJgagqWjQiM9wxC5dDNO9sa1oSSLciumRfSEyxkR5MX1vGb9hQtlMYYkLH66xSR1afUqARrD91urefRq8KRKS1herJyA+StLvSyo56VMr8IQAMqYSPlfWh7caYBKo/bgCWoUjevTDCgp8DBBjqmAQ4f8FS2BZYQJws50haTAukS+JnrGyXVsJupnYZOJvDQfxbyzloY3BPokuaATmFFwzMrkVaoNrrSMv8I6WG2UCwAjEfsIn0MVgHZezWArgQec+yT+gydXJBwreYlgUaHDpATShhgJ33VwZLT2qFW0413nx/OHsF0xE2dOt80kPYPp69TOo4HJGM0I3Y9mIDZyU5DUo/izGgUY6m3JQGNIOJk0PDyaAo=\""]}, {"cell_type": "code", "execution_count": null, "id": "4c0000e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 67, "id": "56c549e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Endpoint User Activity Monitoring Software and Everstage Introduction Call\n", "\n", "<PERSON> with <PERSON><PERSON><PERSON>\n", "Recorded on Sep 13, 2024 via Zoom, 55m\n", "\n", "\n", "\n", "Participants\n", "\n", "Everstage\n", "<PERSON>\n", "\n", "<PERSON><PERSON><PERSON>\n", "<PERSON>, VP of Sales\n", "<PERSON>, VP of Customer Success\n", "<PERSON>, VP of Business Operations\n", "\n", "\n", "\n", "Transcript\n", ";;\n", "0:00 | <PERSON>\n", "It's been busy. Yeah. I'll just say it.\n", ";;\n", "\n", "0:04 | <PERSON>\n", "Clean face.\n", ";;\n", "\n", "0:06 | <PERSON>\n", "I had moments last night where I was like, it's the not drinking enough water when you have a beard and you don't drink enough water. Steel wool. Yeah. I figure instead of just drinking more water, I'll just take it off my face.\n", ";;\n", "\n", "0:26 | <PERSON>\n", "there you go hey easy stuff it looks matt the first time we're meeting but it It looks great.\n", ";;\n", "\n", "0:32 | <PERSON>\n", "yeah I am I'm on the west coast I've been on early mornings the early morning train for a while now so I don't look like I yeah I'm still half asleep it's fine.\n", ";;\n", "\n", "0:45 | <PERSON>\n", "All right, well, I'll try to maybe get you a little bit energized and hopefully not put you more to sleep for sure but it's funny I had a colleague or a client that the guy's beard was like I don't know what yours used to look like but it was like down to almost like a couple inches and then one time he got on the call and I thought it was somebody new who started and they're there in a conference room and I was like holy shit like that was insane so So I'm happy that you got a clean shaven look, but it sounds like it wasn't as intense as that gentleman's.\n", ";;\n", "\n", "1:12 | <PERSON>\n", "Yeah, it's back and forth. This is probably the fourth time since I've worked at TerraMind I've gone. Hey, yeah.\n", ";;\n", "\n", "1:20 | <PERSON>\n", "Well, there you go. Awesome. Well, it's a pleasure to be reconnecting with you all. I know it's Friday, so we don't want to have to spend, too much time with it. But ultimately, really what I like to do with these meetings, keep them straightforward. Obviously, I know we have some CS leaders, we have sales leaders, operations leaders. So first, I had a few questions to start off. We'll spend most of our time on the platform. And then at the end, really determine mutual next steps, see if it makes sense to continue down our sales process. That sounds good with the three of you. Sweet. Perfect. So I always like to start off <PERSON> is I see right you being in kind of biz ops, rev ops a little bit at the team. If you could start off, but it's giving me a better understanding of, you know, where your role fits into the sales commissions and compensation process. And then <PERSON>, if you want to go next to <PERSON>, that would be great.\n", ";;\n", "\n", "2:09 | <PERSON>\n", "Yeah, sure. So my reason for being on this meeting is more just from the implementation side. So I manage all of our systems here. So it's taking a look, making sure it integrates well with what we have. And then I also manage HR and recruiting and we are looking to onboard a new HRIS in Q4, most likely. And so I'm just kind of keeping an eye on that as well. We don't know quite which one we're going to use yet. So I'm kind of using these meetings as like figuring out what makes sense for that.\n", ";;\n", "\n", "2:44 | <PERSON>\n", "Okay. Very cool. That's totally makes sense. And what CRM are you leveraging today that the HubSpot. Okay, perfect. We have a native integration with HubSpot and actually use that internally here as well. So not a problem. And then <PERSON>, I see obviously you overseeing customer success at TerraMind. So I'd love to get a better understanding of maybe, you know, kind of where your interest and role comes in with the sales comp side of things.\n", ";;\n", "\n", "3:08 | <PERSON>\n", "Yeah, for sure. So I am officially now two weeks into my TerraMind tenure. There we go. Yeah, a lot of what I'm working on right now is on top of getting a lay of the land is trying to work on really defining what we're going to be going forward with our strategy, our overarching strategy for CS, what that looks like working backwards from our target KPIs and company goals. So that's a lot of where I've been spending the time right now. The way that we operate from a specific to this particular call, CS has its own comp plan, similar to how sales would, that has a couple of different components. And as we get into it, I'll have some more specific questions because there are factors that could influence it. And just a little caveat, I haven't put it in stone yet either. That's another thing that I'm working on is revamping what that comp plan looks like based on the strategy, of course. But yeah, All that to say, there are some nuances in the CS version that take into account some revenue items, but also some maybe non revenue items that can impact the actual payout. So we can talk about that more when we get into it, just a heads up there.\n", ";;\n", "\n", "4:15 | <PERSON>\n", "Yeah, no, for sure. And that's totally makes sense. Like we've seen it, whether it's renewals or whether it's churn or also could be activity or how much did this kind of grow because of it? There can be things that are pulled from HubSpot or things that are more kind of MBO facing, more behavioral based. So the great thing at every stage is we can support those, and I've done so with countless clients across CS orgs, sales orgs as well. So that's exciting. I've got some new role, and it's always great to join an organization. Yeah, we definitely can help shape those CS plans moving forward. And then finally, last but not least, the clean shaven masks.\n", ";;\n", "\n", "4:48 | <PERSON>\n", "Yes. So I've been at the helm of the sales organization now for two and a half, three weeks, I think. Yeah. Was here in an enablement function prior. So I'm not new to Terramind, just new to running the sales organization. And comp was probably the first and foremost thing that I wanted to kind of. get sorted under control and find a new or better solution. So I kind of got thrown into the middle of a comp cycle and just watched what happened and I went, this can be better. So I put it on my list, basically top of my list of things that need to get done. And so, yeah, we're, teams probably like on the sales side, I would imagine by the end of next year, there's probably going to be 10, 10, 12 sellers like, in our mid market enterprise space. We've got, you know, some SMB reps. We've got people in, in MSP potential end of business. So different types of comp plans, different segments, But pretty support sales type comp structure stuff. So nothing too fancy.\n", ";;\n", "\n", "5:56 | <PERSON>\n", "Yeah, for sure. That's, that's super helpful. And thank you all for walking me through that. So it sounds like, and I guess across the board, like CS, you know, commissionable employees, roughly, what is that range looking like for the team?\n", ";;\n", "\n", "6:10 | <PERSON>\n", "I think right now we're probably between 20 and 30 people who would end up on a plan. Okay. Like within the time we would buy a piece of software and have it like going, it's going to be between 20 and 30 people, including the leaders. I carry plans too.\n", ";;\n", "\n", "6:25 | <PERSON>\n", "Yep, absolutely. Now that totally makes sense. And we definitely can help streamline that processes internally. It's always say if it's 20 and under, we can get away with it maybe in Excel, but there's still some issues, some headaches, some errors that can come about. So we definitely can walk through what we're able to streamline, how we connect the systems, just make that process easier. And <PERSON>, it sounds like you maybe have seen the pain points of the current process today. So I guess one like what would be some of the things that are giving the team headaches that you're hoping a solution can solve for?\n", ";;\n", "\n", "6:55 | <PERSON>\n", "I'm just, I hate spreadsheets. I mean, I love them and I hate them at the same time. And I think when it comes to comp, the problem, is errors are a problem, right? If you can't, you need to minimize errors. You need to improve the efficiency from the point of something happening. the point of being able to pay the rep and like making that like automated is to me low hanging fruit in any comp process. So, you know, this idea of a rep, you know, closing a deal on the CRM or whatever action has happened, it's the data is there. I should not have to export that data from that system to then make a compensation happen, right? There should be a workflow. And this is what <PERSON> does. Like she has workflowed most of our systems to make them all magical. So now it's like, oh.\n", ";;\n", "\n", "7:50 | <PERSON>\n", "Okay, perfect. That's super helpful. And to your point, <PERSON>, we're going to be able to natively integrate with HubSpot anywhere else, potentially other systems that it's pulling from. And to your point, let's just make that much more automated, but also give the team maybe more visibility, right? Boost motivation, things like that as well. So that's definitely super helpful context to provide. And then just final two questions would be, have you explored any other solutions or have maybe the three of you leveraged any other solutions in the past?\n", ";;\n", "\n", "8:19 | <PERSON>\n", "I've worked with <PERSON><PERSON><PERSON><PERSON> before. I had experience with them a couple of years ago, implemented from scratch. We were a zero to 10 size startup. And so, yeah. And full disclosure, the three we're kind of looking at right now are you guys, <PERSON><PERSON><PERSON>path and Captivate. Yeah. And probably the single biggest piece of decision criteria that our CFO, who's not on the phone, brought up on the last call. just to make sure this is in your purview, is dealing with the complexity of the different types of plan structures that CS will likely have to deal with. Cal's world, right? It's less tangible than the sales side in terms of the data in a system. And we have a lot of systems or potential ways that data is coming in. So his concern is around, does it have the complexity? Is it too simplified? Or yeah.\n", ";;\n", "\n", "9:17 | <PERSON>\n", "Yeah, definitely. That's super helpful. Is the integrations to that end a little bit. So curious to know about that as <PERSON> mentioned, right. There's some other data points, right. That are not revenue related. However, they are captured in our systems and other places. Like we use churn zero for a lot of our CS motion. There's an internal project right now. And I don't know, <PERSON> might have more information on this, but I think we're trying to build out a data lake internally on a I think it's big query or something along those lines. So that might be a possible option is we're going to be pushing all data points there anyway, just for your cash.\n", ";;\n", "\n", "9:52 | <PERSON>\n", "Yeah, definitely. And we can connect to churn zero as well. Like basically how that would work is if there's native integrations we have directly in systems or we just connect right through like S3 buckets, snowflake connection, things like that to make it that much more streamlined. Obviously churn zero is one of those. It's pretty common too as well. So definitely have our boxes checked out there. And then last but not least, right. I know there's never like a magic type of date, but is there an ideal date you know, target date that y 'all would want to have this problem try to be solved for and have a solution up and running by.\n", ";;\n", "\n", "10:27 | <PERSON>\n", "six months ago well i in terms of software like i put this at the top of priority of things we need to spend money on and i actually called it out about six months ago as well it was like this is something we should probably do as an organization yeah so we've got the buy in to make the change yeah so it's as fast as we can determine the right solution yeah to put it.\n", ";;\n", "\n", "10:56 | <PERSON>\n", "Definitely. That's super helpful. And I think the other thing I just wanted to call out is the complexities right with the CS plans. Like we have clients that range all shapes and sizes somehow today.\n", ";;\n", "\n", "11:17 | <PERSON>\n", "Assuming that the three of you see value, we can actually explore that CS plan. Even though you're going to make changes that happen, we can kind of explore it almost like a solution sign off or proof of concept type of atmosphere to really obviously show it directly in the system how we'd be able to support those as well. So definitely have our boxes check off and do the proper due diligence on your end. Sweet. Perfect. Anything else top of mind or anything else worth noting before we jump in? All good. All righty. I will quickly share my screen. And just so everyone knows, please do not hesitate to interrupt, ask questions as we go throughout the meeting, making sure that you're fully understanding the value adds. And I'll be able to articulate some unique aspects to like a quota path or to captivate IQ. I know you used it in the past for old map, but <PERSON>, <PERSON>, have you guys seen any other demos as of right now or the first one?\n", ";;\n", "\n", "12:11 | <PERSON>\n", "Only one other one. Yeah, you're second.\n", ";;\n", "\n", "12:14 | <PERSON>\n", "Okay. Who have you already looked at? Captivate. Okay, perfect. Yeah, I actually used to use Captivate in a previous role as a sales manager. So I'm very familiar with them as well. Perfect. So two quick slides, not going to spend a ton of time here, but really what it comes down to it, we are the market leader when it comes to everything sales comp related, social proof across Gartner, G Tube, CapPair, when it comes down to really streamline the processes. And where we're unique is that our CEO actually used to run global RavOps and sales comp. at an organization called Freshworks. Not sure if you're all familiar with, but he was doing that for over 3,000 people. What he realized is there's not a solution that's flexible enough to connect with multiple different sources, easy enough to use without needing a professional services team and gives the reps in depth visibility and motivation to, of course, that's what incentives are for boosting behaviors, driving performance. Hence why he created EverStage. So we were founded in 20 20, quote unquote, the newer player, we were able to grow and, very great success over the past few years when we've been selling our product for just over two years and now have over 210 customers globally. So really been able to shape and size. Regardless of that, we're able to be flexible to meet your needs. And what it comes down to are four key reasons. Number one, we're 100 percent no code. Now, you probably already heard that from Captivate or have heard that in the market. What we mean by that is you probably noticed with Captivate, it looks just like Excel, right? It's going to be coming in SQL, going to be all Excel tabs, various functionality. We're just going to be connecting directly with your systems and pulling in your relevant fields you need to calculate commissions. Not going to have to pull it in through lines and lines of coding and things like that. But also that makes us very customizable and allows you to scale as an organization as well. And then finally, everything that we do is in house, right? So we don't outsource implementation or support. Why that's very much important is one, better costs, quicker turnaround times associated with it. And once again, the three solutions you're looking at are all very good solutions. I know from conversations with clients who've moved off of QuotaPath, I think the support model, sometimes they can offer it, sometimes they don't. So just know that we do provide that for you as part of all of our relationships with Eversage. Any initial questions before we jump into the platform itself? Alrighty. Well, I will jump in here and I'll try to make this as an interactive as possible knowing it is Friday morning. We're probably all looking forward to potentially some fun stuff this weekend. Cool. So what I like to do in these conversations based off of your needs, right? Like manual Excel spells, spreadsheets, things like that. We're going to walk through a three step process. One, how do I actually get data into the system so hillary on your end would kind of dive in there from there how do i organize and manage that data just automate my comp process and then how does that flow through my plans get my team paid out from there time allotting will end with not your side of the house kind of the end user viewpoint like what do my sales team see how do i motivate that things like that if there's any other scenarios use cases please don't hesitate to interrupt for sure here too as we go throughout the conversation so to start off what we're going to do is jump into what we call our connectors, right? So this is the first step. How do we connect directly to your systems? How does this process actually work? For this demo account, right, it's going to be looking at Salesforce, but we just know we obviously have, right, those native integrations with the HubSpot of the world, some other systems, right, churn zero, we're able to connect with here. But ultimately, what we're doing that's unique with EverStage is we're just selecting what systems do I need to be connected with. And then from those systems, what are these specific fields that I need to be pulling in that are relevant to calculating commissions today? So deal names, opportunities, account owners, things like that. What you'll notice here though, is you're not pulling this in through like SQL, right? You're not having to pull in, rename, learn a new syntax. This is all just coming from your direct systems. You don't have to worry about trying to fit it in to meet a system's needs. We're just going to do it the same exact way that you have it set up. Now, once again, this will be updated every single day for you. And when it comes down to your different systems, depending on it, we give you flexibility on how to upload data. We have some clients, and Kyle, to your point, maybe this is CS, where maybe you're trying to boost more meetings that your CS team has with your clients. So that's maybe something that's not living in HubSpot or in churn zero. It might be even just... a one off. You can just add that into EverStage and mark that as part of your comp plan because we give you the flexibility, whether it's a direct connection, once again, through like an S3 bucket or through an even a flat file CSV upload, we give you that flexibility because we know there obviously can be different sources that the data's coming from to calculate comp. From here, once again, right, we connect directly to your systems and then where all this is going to be organized and managed is in what we call our data books. So I know we're not Excel fans, either are we. I compare this to almost Excel workbooks and Excel worksheets, but they're not going to have tab by tab, line item by line item across each rep. This is going to be your single source of everything all in one place. So you can just pull that as you're running through your plans, building out reports, things like that. So to start off, right, you can have as many of these as you'd want. So you can have your zero data book. You can have your HubSpot data book. But as you jump in here, you're just going to have all the information that you need that's relevant for calculating commissions today. You have your deal ID, your invoices, your opportunities, your reps on the specific project that's associated with it. All of this information, once again, is going to be coming directly from your systems. Not having to rename it, not having to fit into a new syntax. What's great about this, though, to your point with Excel, there's no audit logs, right? There's multiple versions that can happen. Who did what? What if someone leaves? What's happening here? Everything's auto logged with EverStage number one. But at any point, if you needed to join multiple data sets together, like maybe churn zero with HubSpot, things like that, it's extremely easy to do. You just Come in, add a simple and easy transformation. So let's just say you want to look at opportunities with invoices, make that process extremely easy to do. What's also great, and Kyle, this might be something on your side of things, where maybe there's something that's not coming in from Churn Zero or HubSpot that you're looking to add into your CS components with the complexities, like more meetings. If you meet with them four times a month, that's just going to be like a yes or no checkbox that will be added to your comp plan. You can easily add a simpler conditional function or formula to the data book that you're able to easily pull into your comp plan. So in short... All of this data is going to be updated every single day for you, depending on where we're pulling it from. And this is where all the information that's going to house your comp plan is going to be living. So we keep this separate so that it can just automatically flow through how your team's getting paid out and into their specific commission plans.\n", ";;\n", "\n", "19:08 | <PERSON>\n", "How frequently is it syncing?\n", ";;\n", "\n", "19:11 | <PERSON>\n", "So the automatic sync is every single day. You can also come in here and run more frequent syncs if you would like. There's also options if you want it to do more frequently. The reason why we typically recommend just once a day is how frequently CRMs change. We want to make sure that the data is accurate and that your reps aren't seeing one thing and then they come in two minutes later and it's different because something happens. That's typically our recommendation is end of each day, once a day. Perfect. <PERSON>, any questions on like the connection side of things, the data side of things currently?\n", ";;\n", "\n", "19:47 | <PERSON>\n", "No, pretty straightforward. Okay, perfect. So next, right, we have the data into the system. Now, how do we actually understand, like, who gets paid what, things like that? And this is where our user module comes into play here. So we support, once again, right, territories, hierarchies associated with it. You can have a VP, you have your managers, you have the reps below them. What's also great about this is this information is also going to house all of the relevant fields that you need in an information that's important to you, whether they're custom fields, payout currency, variable pay that's associated with it. Everything's auto logged, effective dated here. Let's say you have someone who gets promoted, someone moves from rep to manager, everything's going to be effective dated here. And then if they maybe change plans because of the role, that's all going to be associated and in the system here as well. So I know the question was, you guys are looking at an HRIS system. So we do have native integrations with the Workday, connect to ADP. The one caveat to it, and this is something that maybe you're going through evaluation would be helpful to know is, We can connect to your HRIS system. However, sometimes that's the last thing that gets up to date. If someone leaves, gets promoted, maybe moves teams. So there's easy way for us to bulk upload all user information. We can connect to your HRIS system with the caveat that we just need to make sure that that's up to date on everything. Because if that's the system that happens, the last thing that gets up to date, that can obviously throw off who got promoted, what's their new plan and things like that.\n", ";;\n", "\n", "21:21 | <PERSON>\n", "<PERSON> doesn't make mistakes like that. It happens exactly when it's supposed to. always it's a you know what it's an interesting point of like the right and as a company i mean we're growing in terms of process everybody kind of goes through this evolution but i think that idea of like yeah if we have changes if we know changes are done in one place and this is when they're done then they waterfall to all the other systems i think that's where we want to be right like that format of flow for sure so yeah.\n", ";;\n", "\n", "21:56 | <PERSON>\n", "Yeah, that's I always preface with it, because we have a full user management that literally you can just like bulk upload in the system, we have all the custom fields, our implementation team will do that right for you. But just know, there's also obviously ways that you can adjust without needing to directly connect to your HR system, because you'd be surprised how frequently I speak with organizations, right, even larger size, let's say the same thing that ideally, we'd want it, but it takes a week, two weeks to get up to date, but that's going to impact compensation. So there's do it directly in every stage. And all of this will be auto logged too, keep in mind too.\n", ";;\n", "\n", "22:27 | <PERSON>\n", "And you can assign a plan to a team and it would waterfall to everyone below it or you're still...\n", ";;\n", "\n", "22:34 | <PERSON>\n", "Like you mean like this is like a basically like, all right, sales manager, your waterfall to your team. So you're like your team numbers, how you get paid out.\n", ";;\n", "\n", "22:43 | <PERSON>\n", "Yeah, I mean, I was just thinking of like, oh, I've got this bundle of AEs who should all be carrying the same plan. So it's like it's rolling to that whole team. You've got a regional... <PERSON> is potentially carrying a team. We're at a stage right now. It's not necessarily a constraint at all. I'm just thinking of different ways that the plans can be applied.\n", ";;\n", "\n", "23:06 | <PERSON>\n", "Yeah, for sure. That's 100 percent how we'd have it set up. And I can jump into that really quickly here as well. Like this is probably from a EverStage standpoint, right? The first part, not the sexiest, like this is all the data, but important to note that we keep it separate and then it's on an automatically flow through your various plans. But this is a strong issue with EverStage, especially when it comes down to whether it's a captivator or a quota path of How easy is it to actually manage potential plans, changes, things like that? And we make this incredibly easy, right? Publish plan, teams going live. These people are getting paid out on these today because they're live. What we also have is a draft plan, right? And I was going to ask you how frequent, I guess, one, do you make changes to plans? And then two, do you run any sort of like temporary incentives or spiffs for the team?\n", ";;\n", "\n", "23:50 | <PERSON>\n", "I think those are all on the table. So changes to plans right now, I think I'm trying to, from my side, I'm trying to reduce the changes in some plans that are frequent and let's just say really manual in terms of the decision criteria for the plan. And then others, we want more stable plans with the vehicle to be able to change them depending on, because we're essentially, starting new yeah and so when we think of these plans you know we might lick our finger put it in the wind say this is what we want to build get a quarter in and realize we've you know made an error in direction and navigation and we've got to adjust and so I think that's the natural idea is we'd love to have something that's stable but the reality of it is we want the flexibility to be.\n", ";;\n", "\n", "24:46 | <PERSON>\n", "Absolutely. Well, that,\n", ";;\n", "\n", "24:47 | <PERSON>\n", "<PERSON>, if you've got something else, like Headspace wise...\n", ";;\n", "\n", "24:54 | <PERSON>\n", "No, that's pretty on par. Exactly what you just mentioned, <PERSON>, is probably the scenario that we'll go through as well. I've got my initial thoughts on what we want to roll out, especially your benefit. We're reworking the way that expansion is handled across the business and getting sales more involved, where historically it's been a CS motion and we're also implementing the new concept of CSQLs. So there's like some new mechanisms in the business that have not existed. And so to <PERSON>'s point, we're making assumptions on what those should be based on best practices and prior experience, but it could be the case where we get a quarter in, as you mentioned, we're like, this is not directing in the way that we want it to. So maybe we need to make a tweak or two.\n", ";;\n", "\n", "25:34 | <PERSON>\n", "Yeah. So that's great because that's 100 percent where you're able to easily do within our system and keep in mind the flexibility with it. Because let's just say, for example, you already have your plans as they're currently stand. We can upload all of these today. And as you're going through that planning process, our CS team not only has success backgrounds, but they have compensation backgrounds. So we help to shape your plans, not only with the data we have in the system by modeling and forecasting, but also there's multiple times we've called out errors in plans or, hey, did you notice this? Able to strategically suggest those as well. So just know that's 100 percent something we're able to provide, but how easy that would be. Like, let's say it's a quarter in Kyle and you realize, you know, this isn't driving the behavior I care about. We already have this plan live. You literally can just come in, clone this plan, make changes and see maybe what the impact could be before going live to it. That's with historical data and that's also potentially, you know, renewals upcoming, things like that are in the system. And I'll show you how easy that actually is to do. Now, we're going to jump into an account executive plan copy. I know you mentioned previously, like, hey, is it too simple, right? For the associated with it, you can have as many of these components as you would want. As I mentioned, we have some that have 15 to 20. Just know for this example, it's pretty straightforward to understand because the same way would work is the same way to be built in regardless of the complexities associated with it. So let's use your example, <PERSON>. You have all six reps or six AEs that are on the same plan. We already have all their information, their OTE, their variable, whatever that might be. These are the people on the plan. Here's their payout frequency. Here's that plan duration that's associated with it. At any point, maybe they got promoted, they changed, everything's effective dated, so you can either do it on the user level or the plan. level so that this plan stops and then they will go into the new plan starting x date you'll have it all be effective data so you don't need to worry about all right well in excel this was that this was here everything will be auto logged and it'll be effective data directly here within ever stage and that's the same way with spiffs right the spiffs can be effective data so let's say you're running a temporary incentive on multi year deals and we're going to have it be for q one once q one stops you'll be you don't have to worry about having to pause that in excel it's all going to be done within a system So we already have a lot of pre built out templates for you here, right? We have your simple one, which looks at just any amount you're getting a certain percentage. You have your conditional ones, right? Maybe if it's a multi year agreement, you can have your tiers, you have your quota based. And then, which I think it sounds like you guys are paying on bookings today, not on collections. Is that correct?\n", ";;\n", "\n", "28:06 | <PERSON>\n", "You can't buy our software without... You can't own your software or, like, close a deal here, basically, unless you've paid us. It's kind of like there's, like, an exchange. There's a bit of this... Yeah, we don't really do net terms or anything. So I'm pretty sure it's all on, like... Is it collection, <PERSON>? Like, do you know offhand if it's actually money in that's driving the <PERSON><PERSON> decision? I mean, we'll send this recording to <PERSON><PERSON> after so he can catch up on kind of what we're at.\n", ";;\n", "\n", "28:36 | <PERSON>\n", "Yeah, honestly, I think it's something that we have to truly come to terms on ourselves. But typically, it's not necessarily on paid, but it's on activation. So did they actually activate the software? So we do actually have terms for some customers. And certainly, if we work through a partner, they have terms as well. And in some instances, we'll activate their account first. So I think internally, we just have to figure out what we want that definition to really be.\n", ";;\n", "\n", "29:06 | <PERSON>\n", "Okay, perfect. And that's what makes it super simple here. Let's say it was invoice status equals paid. This for you could be activation status equals whatever that is, on, off, whatever that is. Then they actually earn the commission from there. So we make it incredibly easy to really manage that. For this example, we're looking at three components here. A revenue attainment one, which is looking at quota. If you're zero to 100 percent of your quota, you'll get 10 percent. Anything over 100 percent, you'll get 15 percent. Next one's looking at a multi year, right? Contract two years, one and a half percent, three years, four percent, then an implementation fee component, anything over 2000, you get two percent on top of it. What's important to note here, though, is that there's no front end back end. This is not going to be hard coded lines and lines of SQL coming in. You keep in mind, we already organized the data in step one as we saw. So you're just pulling from your HubSpot information, your churn zero information to actually have these plans in place and make it extremely easy to manage and maintain. Because if you look here, right, we see tiered value, you get 10 percent. Look at how easy this actually is to do. Tiered value, there's the time sign, you type in the 12 percent. That's how easy it is. Now, you also mentioned, <PERSON>, with <PERSON><PERSON>, there's always errors. There can be human errors that come into play. Everything function informed is validated right here within the system. So even, <PERSON>, when you're thinking about making specific changes or looking at different measures for your CS plans, you can make sure that the logic doesn't make sense. Our team will be there guiding you, but let's say it's you actually making the changes, you'll know that these are valid because of how we have it set up. And we're just pulling from the data that we already have managed and organized in your system. So it's all pulled from your fields, your objects associated with it. So in this one, you notice contract duration is two years. All these fields are already going to be populated from the data that we're pulling in. So we make it incredibly easy rather than You might have seen, and sometimes other solutions won't show this, like the actual plan builder itself. But with how frequently you're making changes, you don't want to have to try to learn SQL and do all hard coded. You want to be able to easily do so. And that's how our system is able to be set up because of it.\n", ";;\n", "\n", "31:21 | <PERSON>\n", "Slick. Perfect. All right.\n", ";;\n", "\n", "31:24 | <PERSON>\n", "I appreciate that. That's a lot of the feedback we do get because you'll might notice other people won't show this. There's like a front end and a back end. It's hard code. And they're like, all right, let's avoid this at all costs. But thinking about what you guys are going through right now, you probably want to be able to easily make changes or other solutions. You have to like reach out to <PERSON>. This is very intuitive. It's the first time you're seeing it. Let's even say that, you know, one of your team members is out. Right. And you need to make this change. you're already able to understand it because you see, all right, this is revenue attainment. I can pretty much understand zero to 100 percent, 12 percent, 100 percent over is 15 percent. And maybe they've never seen this before. There's also all of these guides here for you on what each thing is.\n", ";;\n", "\n", "32:08 | <PERSON>\n", "Yeah. I just think about the headache that I had, no matter what the tool, was, you know, rolling out quota. Like basically as you're coming to the end of the quarter, you're starting to do the evaluation of like where we're at. Do quotas need to change? Right. And then it's like jump back into the system and then have to do all the administrative work of the job, which is never a fun part of the job. Right. Yeah. And it's trying to reduce the headache of all the, you know, the potential kind of change that has to happen. Yeah. process of that. Yeah.\n", ";;\n", "\n", "32:44 | <PERSON>\n", "Yeah. And that's why we make everything in one, like you're going to house all of that. Cause like literally <PERSON>, you would come in and I'll share our reports in a second. Let's say it's midway. And do you guys pay, you pay quarterly or pay monthly?\n", ";;\n", "\n", "32:57 | <PERSON>\n", "months will be I think for.\n", ";;\n", "\n", "32:59 | <PERSON>\n", "We pay monthly.\n", ";;\n", "\n", "33:01 | <PERSON>\n", "Yeah. Okay. Perfect. Sounds like probably quarterly, maybe quotas, monthly payouts. Let's say it's the middle of a quarter. You just want to see like, how are we doing? You can literally have the report. You come in, see where you're going at. And then you're like, all right, well, let's say only 10 percent are even close to hitting their quota. Maybe next quarter we got to make adjustments. Look at how easy this would be. And once again, I know it's not your exact plan, but let's just say, you know, we're going to make the zero to 100 percent because we want attainment and morale to go up. We want to change this to 16 percent. And then for the top performers, we want to give them a higher accelerator to 20 percent. What you can actually leverage is our modeling where you can actually see if we do this, how is that going to impact commissions and targets across our various teams? And that'll take you about 10 seconds to do. As you see right here, this is what the commissions would be across the team. This is where they'd be at their targets or their quotas associated with it. Now, the value add here is when you're thinking about making changes, you can look at historical performance and say, if we did this based off of what previous quarters, months, whatever that looked like, How does that impact commissions? But you can also take <PERSON> for your end. You'll have your active and <PERSON>, obviously on the sales side, you'll have a pipeline data in here as well. So you can forecast out like, okay, this is where all these deals are coming from. Hey, this is where the potential renewals are. What if we made this change roughly? What would our, what would be our team if we wanted to go live with this? So this is where you'll have some data to back up. If you were to make any changes to plans by looking how it actually will impact the bottom line numbers, which obviously something that three of you and also your CFO would probably care about as well.\n", ";;\n", "\n", "34:35 | <PERSON>\n", "What's the quarter erosion?\n", ";;\n", "\n", "34:37 | <PERSON>\n", "Yeah, that's just it basically means how you're doing towards your quote on an annual basis. So basically, hey, if we made this 15 percent, like I think for this example demo account, their quote is like a 1,000,000 bucks for the year. That's just saying like, all right, you'd be at four, four, two, seven, four out of the whatever 1,000,000 bucks is. And all of these phrases up top here, you'll notice like users quote erosion. We have a thing called like use your own terminology. You can determine like what you want those to be, because some people don't use commutations. they use like bonus or they use like targets versus quotas. You can always change those out. Perfect. Let me take a quick pause though. I know obviously plan changes, potentially having some data to back up seems like it's relatively top of mind. How do you think this would be helpful when you're going through those processes today?\n", ";;\n", "\n", "35:26 | <PERSON>\n", "Yeah. I mean, I like anything that helps me make better decisions. like that and i think from our perspective being able to back some of those decisions up when we're trying to get buy in from the other leadership to say hey this is what we need to make adjustments to the plan and here's kind of we've run the numbers we put them through the system and this is kind of what looks good based on you know any of that because right now it's all spreadsheet guesswork based on other spreadsheets that we have and we're trying to make trying to take data and turn it into something that's actually an actual plan.\n", ";;\n", "\n", "36:05 | <PERSON>\n", "Yeah, for sure. And the other thing I'm thinking about too, <PERSON>, you've been there, right, two weeks now. probably don't have all of the historical context around, like maybe you do already, but like churn rates, renewal rates, things like that. We have that all. We'll be able to be pulled that in historically so that as you're going through, like, what do I want to be driving for my CS plans? It sounds like you have like SQLs associated with CS now, which is obviously going to be super helpful for them. But like what behaviors do I want to be having? We'll have that historical insight in here so you can model out potential changes, work with our team to do so as well.\n", ";;\n", "\n", "36:40 | <PERSON>\n", "Yeah. Well, actually, scenario question for you, because I'm working through this right now. From a CSQL perspective, as I mentioned, it's not something we've done in the business. So I'm trying to figure out, working backwards from what I know we need to hit from an expansion top line perspective, what does that back into in terms of how many CSQLs we would expect a CSM to have on a quarterly basis? And my thinking is the best way to work into that is to find what our ASP for expansion deals are. Does that scenario, would this tool help me work through that scenario?\n", ";;\n", "\n", "37:14 | <PERSON>\n", "Yeah, so just to confirm, so you haven't tracked CSQLs previously at the organization?\n", ";;\n", "\n", "37:18 | <PERSON>\n", "no but we've closed expansion deals like all right so i could yeah i could have the data to go look and say like what is the asp by segment for example on some of these expansion deals i don't know <PERSON><PERSON> correct me here i don't know if we have the data to be able to say like which was organic versus one that we went and found i e. like which ones were somebody just asking for some additional licenses or overused versus like we went and sold to I don't know that I have that delineation.\n", ";;\n", "\n", "37:45 | <PERSON>\n", "yeah I don't my gut would say we don't we nobody went and sold but they were answering they were answering the phone yeah the.\n", ";;\n", "\n", "37:56 | <PERSON>\n", "answered the phone call or taken the orders. We are very good at taking orders.\n", ";;\n", "\n", "38:03 | <PERSON>\n", "Exactly.\n", ";;\n", "\n", "38:04 | <PERSON>\n", "Well, yes, it could be something that's looking at total expansion deals closed versus total for that segment. Divide the two, right? And what is the average expansion deal size? And then how many do I need to get to the top line number? And split it out between the team. That's the way I'm thinking about it right now. Yes.\n", ";;\n", "\n", "38:22 | <PERSON>\n", "So the fact that like all those calculations and stuff is probably not what you would do in like this modeling portion. of it. But with that, you can have it be based off of, okay, let's say that you ballparked out, you know, X amount of SQL, see SQLs that you think the quota should be like, hey, you should be getting five to 10 a quarter, or, maybe you're doing based off of ASP, like, hey if you had to upsell or renewal and you cross sell for 10,000 plus you're gonna get x percentage of that or y percentage of that those are things you're able to model out because of it and that's something obviously we can take a deeper dive into as well but really that flexibility and once again what's great about our system we have custom role based access so kyle you could only do that for the cs team your own reports only be cs based matt maybe you just wanted it to be for the sales side of things your cfo could have an overarching Hillary, you can be like, obviously, a power admin as well. We give you those custom roles and flexibility to have so that everything's in one place, but each person is doing whatever is relevant to their specific teams and responsibilities.\n", ";;\n", "\n", "39:19 | <PERSON>\n", "Yeah, I think what I'm hearing, it's this idea of, you know, if we had the system in place today, <PERSON>, it'd be not because then you could kind of whiteboard out your like, hey, if I put this plan in place and we've got all these data points historically, this is what it would look like if we actually like ran this. Yes. Numbers. Right. And really, this is what the payouts would have been like. That's kind of the time machine. Like this is where, you know, <PERSON>, the other Kyle. this idea of being able to say, if I've crafted a plan in here, I can use historical data to see what that would have done with the plan, right? So that we could actually vet out, you know, what we would have ended up paying out on that plan design, or is it really just taking the existing or current data set, you know, and forecasting when you say time machine, like forecasting forward based.\n", ";;\n", "\n", "40:09 | <PERSON>\n", "on. Yeah.\n", ";;\n", "\n", "40:12 | <PERSON>\n", "I don't know if my question is clear.\n", ";;\n", "\n", "40:15 | <PERSON>\n", "No, I got you for sure. So basically the way to think about this is you don't have the plans live today. That's not a problem at all. In EverStage, that's not a problem at all. Because what we do, we bring in at least six to 12 months of historical data already into the system. Because then you're not live on it, but you can still do the modeling. You can still do the time machine aspects. You think a time machine that looks at historical data. When you think about forecasting, that looks at the pipeline data. Like, all right, we look at a time machine. Let's look at past performances. If we made these changes, this is what roughly I would impact payouts. When you're thinking about forecasting, well, what if we made this change right now based off of what we currently have in our system and in our deals and in our pipeline? That's how you're able to forecast out moving forward. And we also will do that, right? So you don't have to worry about, oh, we don't have our plans live here today. This won't be valuable. it would still be valuable because that's the way that obviously implementation works within our system. Perfect. Cool. So last but not least is, right, we organize, manage the data easily, have it flow through your plans. Now, how do we actually get our team paid out? So we have a payout screen, make it very simple. You just export this to your payroll system, send out for payouts. You'll notice all of these fields right here are going to be exactly what's in your payroll system. You can customize these fields as well. And I think you might be a global organization. I think I saw there were some maybe reps outside of the US. We support global currencies as well. And then a question I have is, do you happen to have, actually two questions. Do you have an approval process today with commissions? Like, hey, the rep needs to approve, the manager needs to approve or anything like that?\n", ";;\n", "\n", "41:50 | <PERSON>\n", "Okay. We can support better and it will likely change, but yes, there will still be some level of approval process. Yep. We have,\n", ";;\n", "\n", "41:58 | <PERSON>\n", "We can support custom approval workflows as well. So you can have it go to, it needs to go to the rep first, then it needs to go to their manager or the finance team, whatever that is. That can be part of every single payout before it actually hits to their payroll system. That's very easy to set up here. And then with the plan, I know you're making changes somewhat frequently, but each year, do they sign a document or is it more, hey, this is a plan laid out? So.\n", ";;\n", "\n", "42:22 | <PERSON>\n", "So the way it's worked in sales is we had a quarterly commission quota letter, essentially, that outlined kind of what they were going to be kind of comped on.\n", ";;\n", "\n", "42:35 | <PERSON>\n", "Okay. Perfect. Because if at any point you need to like, maybe you want to have them actually acknowledge it and sign it. We also have a connection with DocuSign. We can just send it out through every stage.\n", ";;\n", "\n", "42:47 | <PERSON>\n", "That might make life a little easier, <PERSON>, on the keeping track of paper.\n", ";;\n", "\n", "42:52 | <PERSON>\n", "work. Yeah.\n", ";;\n", "\n", "42:54 | <PERSON>\n", "Cause I know historically it's been a pain in the butt, but it's super simple to do, right? You already have all their user information in here. Maybe there's multiple people and then you can just upload that document right here. And then it will also be living in their own user instance as well. All those fields will be pulled in that we already have.\n", ";;\n", "\n", "43:13 | <PERSON>\n", "Perfect. But once again, with the payouts, right, export out, maybe, you know, whoever might be the power admin, <PERSON> will call you for today. Let's just say it's, you know, middle of the month or, you know, something like that. And you just want to take a look at, all right, how are we doing? What's going on? You notice this <PERSON> guy's at 51,000 bucks and everyone else is much less. Let me just quickly click in here and make sure this is adding up. OK, got 51,000. He's the VP of sales. This is his component. hear all of his relevant transactions, activations, whatever that might be. Okay, this is good. We're all fine right now. Rather than you probably having, or whoever runs it today, having to do it at the end of each pay period. And then there's questions come about, you're running crazy at the end of each pay period. You're able to do so very easily. Perfect. Now, last but at least from an admin portion, <PERSON>, you kind of hit on it is, all right, I don't need to understand how's my quota, target, things like that performing. So with EveryStage, we almost serve as your own BI solution where you can have these custom dashboards reports across various teams, members, control who sees what. This is a pretty standard one. Like let's say for your CFO, you budgeted out X amount for commissions this quarter, this year. How are we tracking towards that, right? And you can do this off of the entire team, new business, CS, right? Things like that. You also can have looking at just specific teams and their attainment. So like, let's say <PERSON>am or <PERSON>, your team. Hey, how's <PERSON>'s team's doing this month, this quarter towards their specific quota? How are each individual's doing towards their specific quota as well? You can compare those also in here one by one. And then the other aspect you mentioned was around just, you know, overall quota attainment and distribution. Like, Hey, are people actually going to be, you know, hitting their numbers? Are they actually hitting this specific threshold? You can actually see across teams across the organization. Are we doing right? Are we close to quota? Are the reps not far away? Cause you really wanted a balance of not everyone over exceeding it, not everyone under exceeding it. You want to find that common ground. You'll have that visibility right here at your fingertips. And then we also have some more executive level dashboards right around, hey, overall, how is the business doing, right? How are we doing revenue wise? How are we doing by new business versus CS versus when there's leaderboards or contests we're running, team attainments, quota attainments, and then obviously the forecasting that comes into play based off of what we have in our pipeline that's associated with it.\n", ";;\n", "\n", "45:37 | <PERSON>\n", "Perfect. Let me take a quick pause because that really wraps up some of the things I wanted to cover from an end user viewpoint and really how we're able to strategically support that. I know we technically, we're supposed to have a hard stop at 10 15. Do you have like five more minutes to jump into the end user view? I mean,\n", ";;\n", "\n", "45:56 | <PERSON>\n", "I have to connect with <PERSON> after this call before I jump on the next call. So as long as we keep a bit of time, I'm good for probably five.\n", ";;\n", "\n", "46:03 | <PERSON>\n", "Yeah, same.\n", ";;\n", "\n", "46:06 | <PERSON>\n", "Okay, perfect. I'll just quickly high level review that with you here as well. So in short, with <PERSON><PERSON><PERSON>, right, what you're able to do is really control what you want your team to have visibility into. And let me just jump in as if I was <PERSON>. So do Do you guys happen to use Slack or Teams internally by chance?\n", ";;\n", "\n", "46:24 | <PERSON>\n", "Slideshow.\n", ";;\n", "\n", "46:26 | <PERSON>\n", "Slack. Okay. We have a connection directly with <PERSON><PERSON><PERSON> as well. So if I'm a rep, I could literally come in, type in the word commissions or target or quota attainment. And I can actually see like, what am I going to be earning for that pay period because of what I've closed out? So really meeting them when they spend a lot of their time. But with our dashboards, right? You have probably have two types of reps. You probably have one who just, all right, I'm gonna look at my payout. That's it. Then you have one who probably has a fine tooth comb and a magnifying glass goes through every single transaction. We're able to have that flexibility to support both. So to start from <PERSON>, this is my payout. This is how I'm tracking. This is my quota attainment. I want more advanced dashboards as well, which is, you know, pretty unique in the space diversity versus a more like of strict limited one. You're able to have access to it here as well, right? Revenue book, president's club, criteria, whatever you'd want them to have access to, it's completely flexible. Then they're also able to jump into their statement view. All right, this is my payout. This is my plan. Here are my components. And I'm able to also jump deeper into the specific transactions, deals, line items associated with it. And at any point, they can always trace back to the How did I make this amount? Oh, I get 12 percent off of these. There's the 540 dollars that's associated with it can raise queries in the system here. Right. So you have an autolog of all those. And then that plan document also will be living directly within EverStage here as well.\n", ";;\n", "\n", "47:51 | <PERSON>\n", "Perfect. Now, last but not least, this has been a huge impact for a lot of salespeople because to your point, they're probably doing a lot of shadow accounting where they have their own Excel file. What if I close this? What if I did this? What if I did that? We have an aspect that we call crystal. It actually pulls in their pipeline data. what they're actually able to do here is see, okay, this is my payout as it currently stands. This is my quota attainment, but you probably have multiple components associated with it. So this could also work the same way with you, <PERSON>. What if I get four more CSQLs this quarter? But for this example, what if I close out these deals? I think it's going to come at this amount. And I think I'm actually going to close it as a two year deal. They can apply those projections and actually see how it's going to impact their payouts and their targets that they earn. So this is unique in the space. And this is something that has been a huge game changer for clients who are looking to boost motivation, performance, and behavior specifically. You can actually see the proof in the pudding and see the bottom line impact that would have.\n", ";;\n", "\n", "48:49 | <PERSON>\n", "I have a question, and maybe this was answered when I was multitasking. I'm sorry. Do we have the option to select any number field, essentially, as the thing that's calculating commissions, or does it have to be the amount field?\n", ";;\n", "\n", "49:05 | <PERSON>\n", "What would be another field maybe that you would I'm looking for. Oh, yeah. Yes, it was As long as we're pulling that from one of your systems, that's something you're able to calculate. Perfect. All righty. Let me take a quick pause. That wraps up the end user view, admin viewpoint. <PERSON>, maybe I'll turn it over to you first, right? Overall, what are your thoughts? You think this could be a potential fit for the team and the organization?\n", ";;\n", "\n", "49:33 | <PERSON>\n", "Yeah, I think it's slick. You know, from my perspective, I just want to make sure, because I know overselling the integration elements is something I don't want to find ourselves in, right? Like if it integrates, great. If it's, yeah, it integrates because we've done it once and it was this big hack job to make it work. We just want to go in, I think, eyes wide open with all the different types of systems. I know you guys are relatively new in the space, so some of those integrations get built. It's like building a plane while you're flying it. So that would be kind of my only thing is, you know, making sure that what we do plan to integrate it with, is fully made and not necessarily. Yeah. Be a hack.\n", ";;\n", "\n", "50:15 | <PERSON>\n", "Yeah, for sure. And no, and just so you know, like, we wouldn't move forward with you if that was the case, cause it would just be a bad implementation and like we wouldn't grow and it would just be a shitty situation across the board. So everything that's so far like zero churn, If it was NetSuite or excuse me, if it was HubSpot, those are easy integrations to have. If there's other ones, definitely send those over because what we even do before we move forward with the client, we either do a proof of concept or a solution signup that like we look into, all right, what systems are we connecting with? What data are we pulling in? How is it organized? Because there's been times we have to say no to clients because they're just not ready yet. So believe me, we're not going to move forward with you if that was the case. And we'll make sure that we cover that as we continue the evaluation.\n", ";;\n", "\n", "50:56 | <PERSON>\n", "Okay. And then what does a typical implementation timeline look like for you guys from the point of contract signing to we're up and going using the platform and any associated implementation cost to do that?\n", ";;\n", "\n", "51:07 | <PERSON>\n", "yeah yeah definitely so on average six to eight weeks and that is what's great about it is that one it's typically fast in the market that big advantage is doing everything in house and internally the cost associated with it without looking at your plans and things like that we obviously can get into like roughly what total cost would be you'd probably be looking somewhere at a one time fee of between like seven to five k one time And then I think you mentioned you have about like 30 commissionable reps, roughly, give or take. You might be there at the end of the year. Let's say that you're having three to four more admins across the board with like support, implementation and the cost per user. You'd probably be looking somewhere between like the 25,000 on an annual basis range. That's including the implementation as well. Standard agreements, two years in line and obviously year two would be the same minus just the implementation costs.\n", ";;\n", "\n", "52:04 | <PERSON>\n", "Cool.\n", ";;\n", "\n", "52:06 | <PERSON>\n", "Did you guys have a specific budget set aside for this project from your CFO?\n", ";;\n", "\n", "52:15 | <PERSON>\n", "You're in sales. You can make budget happen. That's what we do in sales. We make budget. The reason I.\n", ";;\n", "\n", "52:25 | <PERSON>\n", "say that is sometimes like, hey, we have this set aside and we always try to make that work. But ultimately, it should be relatively competitive. I know typically Captivate is a little bit higher on the implementation costs associated with it. I know Quotapath, typically they are a little bit less expensive than us. Just across the board, but given the fact that obviously you're trying to move somewhat quickly, I mean, what I would love to do is take a deeper dive, potentially either explore that proof of concept route or that solution sign off route by looking at some of the sample plans and data that you have. We, of course, can send out an MNNDA, and that's obviously something that typically we take as a next step to really determine if this is something that you all want to be moving forward with.\n", ";;\n", "\n", "53:05 | <PERSON>\n", "I think, from our perspective, part of what we want to be able to do is, and <PERSON>'s still, you know, kind of hammering the details out on his, but I think once we have that, you know, like this is what we want to do, that we could do that exercise to make sure that it's going to work. Like that's the most important thing is that we can actually, and what we want to implement.\n", ";;\n", "\n", "53:27 | <PERSON>\n", "Definitely. That totally makes sense. Especially the CS kind of being the more complex. I mean, <PERSON>, you're two weeks in and I want to put you on the spot here. Give any sense of like when you're hoping to try to have that maybe more ironed out by.\n", ";;\n", "\n", "53:39 | <PERSON>\n", "Today?\n", ";;\n", "\n", "53:42 | <PERSON>\n", "Yeah, no, I'm actually going to be out of the office all next week. So I'm trying to get some stuff moved and then I'll probably progress a few things even while I'm out because I want to keep this train moving. But I mean, I have an idea of what I want. It's just really trying to mold some of that around, again, the new structure. So like I mentioned, we have a CSQL plan in mind, right? But we don't have a process and structure built out for that on the team. So I need to flush that part out too, to make sure that like what I'm putting in plan that we're paying against, we actually have methodologies and enablement for CSMs to go do that thing. business today. So that's the other piece of it.\n", ";;\n", "\n", "54:22 | <PERSON>\n", "Yeah, absolutely. So what if we did this? I'll send out the recording after here. So you can obviously share with your CFO as well. Kind of send a little recap. What if we try to put some time maybe for the week of the 20 third of September, like maybe that Wednesday, the 20 fifth, just kind of see how things are progressing. We then can kind of mutually align on, hey, we want to move forward that proof of concept. We have some of the plans routed out. And that way we're obviously supporting your internal processes. Sounds good to me. Does that work for three of you?\n", ";;\n", "\n", "54:50 | <PERSON>\n", "Yeah. And full disclosure that we're kind of, you know, in the same boat with, you know, the other vendor right now is like until we have our kind of like ducks in a row in terms of Being able to see that plan materialize in the product. Yeah. So I think that timeline works. Yeah. I know you've got to connect with me here. So before I jump on the next call. Yeah.\n", ";;\n", "\n", "55:15 | <PERSON>\n", "I'll send out an invite. I don't know if there's a specific time that works. On the 20 fifth, I'm wide open. But if there's a specific time that works...\n", ";;\n", "\n", "55:24 | <PERSON>\n", "I'd say throw the base holder in and we can always go back and forth and you know it.\n", ";;\n", "\n", "55:29 | <PERSON>\n", "Perfect. That sounds great. Well, thank you both. Thank you three of you so much for obviously taking some time and excited at the opportunity. I don't talk to you sooner. Have a great weekend and we'll talk in a couple of weeks.\n", ";;\n", "\n", "55:39 | <PERSON>\n", "Thanks, <PERSON>. Thank you, <PERSON>. Thanks, everyone. Take care. Bye.\n", ";;\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["call_path = \"/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_1.txt\"\n", "def read_transcript(call_path):\n", "\n", "    with open(call_path, 'r') as file:\n", "        transcript = file.read()\n", "        file.close()\n", "    return transcript\n", "\n", "transcript = read_transcript(call_path)\n", "print(transcript)"]}, {"cell_type": "code", "execution_count": 68, "id": "eb4a5edb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-09-13 102\n", "<class 'str'>\n"]}], "source": ["import re\n", "import numpy as np\n", "from typing import List, Dict, Tuple\n", "from datetime import datetime\n", "\n", "def timestamp_to_ms(timestamp: str) -> int:\n", "    \"\"\"Convert timestamp like '0:06' or '12:34' into milliseconds.\"\"\"\n", "    parts = list(map(int, timestamp.strip().split(':')))\n", "    if len(parts) == 2:\n", "        minutes, seconds = parts\n", "        return (minutes * 60 + seconds) * 1000\n", "    elif len(parts) == 3:\n", "        hours, minutes, seconds = parts\n", "        return (hours * 3600 + minutes * 60 + seconds) * 1000\n", "    else:\n", "        raise ValueError(f\"Invalid timestamp: {timestamp}\")\n", "\n", "def parse_transcript(transcript: str, num_speakers: int) -> Tuple[str, List[Dict]]:\n", "    lines = transcript.strip().splitlines()\n", "\n", "    if len(lines) < 4:\n", "        raise ValueError(\"Transcript does not contain enough lines to extract call date.\")\n", "    \n", "    call_date_line = lines[3].strip()\n", "    call_date_match = re.search(r'\\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\\s+\\d{1,2},\\s+\\d{4}', call_date_line)\n", "    call_date = call_date_match.group(0) if call_date_match else \"Unknown\"\n", "\n", "    # Extract transcript content after the line containing 'Transcript'\n", "    try:\n", "        transcript_start = lines.index('Transcript') + 1\n", "    except ValueError:\n", "        raise ValueError(\"Could not find the 'Transcript' marker in the text.\")\n", "    \n", "    metadata = '\\n'.join(lines[:transcript_start - 1]).strip()\n", "    content = '\\n'.join(lines[transcript_start:])\n", "    entries = content.strip().split(';;')\n", "\n", "    speaker_map = {}\n", "    speaker_ids = list(np.random.choice(range(1000, 9999), size=num_speakers, replace=False))\n", "    speaker_index = 0\n", "    chunks = []\n", "\n", "    # Add metadata as the first chunk\n", "    chunks.append({\n", "        \"speakerId\": \"meta\",\n", "        \"speaker_name\": \"metadata\",\n", "        \"topic\": \"Call metadata\",\n", "        \"sentences\": [\n", "            {\n", "                \"start\": 0,\n", "                \"end\": 0,\n", "                \"text\": metadata\n", "            }\n", "        ]\n", "    })\n", "\n", "    for entry in entries:\n", "        entry = entry.strip()\n", "        if not entry:\n", "            continue\n", "\n", "        match = re.match(r'^([0-9:]+)\\s*\\|\\s*([A-Za-z ]+)\\n(.+)', entry, re.DOTALL)\n", "        if not match:\n", "            continue\n", "\n", "        raw_time = match.group(1)\n", "        speaker_name = match.group(2).strip()\n", "        text = match.group(3).strip().replace('\\n', ' ')\n", "\n", "        if speaker_name not in speaker_map:\n", "            if speaker_index >= len(speaker_ids):\n", "                raise ValueError(\"More unique speakers than speaker IDs allowed.\")\n", "            speaker_map[speaker_name] = str(speaker_ids[speaker_index])\n", "            speaker_index += 1\n", "        speaker_id = speaker_map[speaker_name]\n", "\n", "        chunk = {\n", "            \"speakerId\": speaker_id,\n", "            \"speaker_name\": speaker_name,\n", "            \"topic\": None,\n", "            \"sentences\": [\n", "                {\n", "                    \"start\": timestamp_to_ms(raw_time),\n", "                    \"end\": timestamp_to_ms(raw_time)+(len(text.split(\" \"))*200),\n", "                    \"text\": text\n", "                }\n", "            ]\n", "        }\n", "        chunks.append(chunk)\n", "        \n", "    call_date_obj = datetime.strptime(call_date, \"%b %d, %Y\")\n", "    call_date_str = call_date_obj.date().isoformat()\n", "\n", "    return call_date_str, chunks\n", "\n", "\n", "date, chunks=parse_transcript(transcript, 15)\n", "print(date, len(chunks))\n", "print(type(date))"]}, {"cell_type": "code", "execution_count": 16, "id": "4d7d4b67", "metadata": {}, "outputs": [], "source": ["# client_id = 10039\n", "# opportunity_id = 'upstream_sim_1.2'\n", "# call_date = '2024-09-13'\n", "# gong_call_id = uuid.uuid4()"]}, {"cell_type": "code", "execution_count": 69, "id": "61b60e30", "metadata": {}, "outputs": [], "source": ["def upstream_uploader(chunks, call_date, gong_call_id, client_id = 10039, opportunity_id = 'upstream_sim_1.2'):\n", "    for i in range(len(chunks)):\n", "        if i ==0:\n", "            is_first_chunk_of_call = True\n", "        else:\n", "            is_first_chunk_of_call = False\n", "\n", "        if i == len(chunks)-1:\n", "            is_terminal_call = True\n", "        else:\n", "            is_terminal_call = False\n", "\n", "\n", "#         graph_rag_call_folder_path, graph_rag_call_file_path, long_context_prompt_path, long_context_file_path=process_gong_call_chunk(client_id=client_id, opportunity_id=opportunity_id, gong_call_id=gong_call_id, \n", "#                             chunk_data=chunks[i], call_date=call_date, is_terminal_call=is_terminal_call, is_first_chunk_of_call=is_first_chunk_of_call)\n", "\n", "        process_gong_call_chunk(client_id=client_id, opportunity_id=opportunity_id, gong_call_id=gong_call_id, \n", "                            chunk_data=chunks[i], call_date=call_date, is_terminal_call=is_terminal_call, is_first_chunk_of_call=is_first_chunk_of_call)\n", "\n", "#         if is_terminal_call:\n", "#             attach_file_to_long_context_prompt(\n", "#                 call_file_path=long_context_file_path,\n", "#                 long_context_prompt_path=long_context_prompt_path,\n", "#                 call_date=call_date,\n", "#             )\n", "#             # add the long context details to typesense\n", "#             add_llm_rag_details_to_typesense(\n", "#                 client_id=client_id,\n", "#                 opportunity_id=opportunity_id,\n", "#                 entity_type=EntityType.GONG_CALL.value,\n", "#                 long_context_prompt_path=long_context_prompt_path,\n", "#             )\n", "#             print(\"added... llm rag details in typesense\")\n", "#             print(i, is_first_chunk_of_call, is_terminal_call)\n", "#     return graph_rag_call_folder_path, graph_rag_call_file_path\n", "\n", "        print(i, is_first_chunk_of_call, is_terminal_call)\n"]}, {"cell_type": "code", "execution_count": 71, "id": "9cedaa58", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_1.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:30:58 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:30:58 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:30:58 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:30:59 services.py 177 INFO MainProcess]: Saving gong call 19216e6e-cd11-4f47-8cae-72b6aadd181b metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "64 False False\n", "65 False False\n", "66 False False\n", "67 False False\n", "68 False False\n", "69 False False\n", "70 False False\n", "71 False False\n", "72 False False\n", "73 False False\n", "74 False False\n", "75 False False\n", "76 False False\n", "77 False False\n", "78 False False\n", "79 False False\n", "80 False False\n", "81 False False\n", "82 False False\n", "83 False False\n", "84 False False\n", "85 False False\n", "86 False False\n", "87 False False\n", "88 False False\n", "89 False False\n", "90 False False\n", "91 False False\n", "92 False False\n", "93 False False\n", "94 False False\n", "95 False False\n", "96 False False\n", "97 False False\n", "98 False False\n", "99 False False\n", "100 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:34:41 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n", "[2025-05-23 11:34:42 utility.py 838 INFO MainProcess]: File doesn't exist, creating it\n"]}, {"name": "stdout", "output_type": "stream", "text": ["101 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_2.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:34:44 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:34:45 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:34:45 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:34:45 services.py 177 INFO MainProcess]: Saving gong call fc50c4a8-82ac-4dc3-af5f-36fd3b1e6da8 metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "64 False False\n", "65 False False\n", "66 False False\n", "67 False False\n", "68 False False\n", "69 False False\n", "70 False False\n", "71 False False\n", "72 False False\n", "73 False False\n", "74 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:37:18 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["75 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_3.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:37:21 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:37:22 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:37:22 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:37:22 services.py 177 INFO MainProcess]: Saving gong call 9702d705-4541-479c-9572-26eec6b6d5d5 metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "64 False False\n", "65 False False\n", "66 False False\n", "67 False False\n", "68 False False\n", "69 False False\n", "70 False False\n", "71 False False\n", "72 False False\n", "73 False False\n", "74 False False\n", "75 False False\n", "76 False False\n", "77 False False\n", "78 False False\n", "79 False False\n", "80 False False\n", "81 False False\n", "82 False False\n", "83 False False\n", "84 False False\n", "85 False False\n", "86 False False\n", "87 False False\n", "88 False False\n", "89 False False\n", "90 False False\n", "91 False False\n", "92 False False\n", "93 False False\n", "94 False False\n", "95 False False\n", "96 False False\n", "97 False False\n", "98 False False\n", "99 False False\n", "100 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:40:31 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["101 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_7.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:40:34 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:40:34 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:40:34 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:40:35 services.py 177 INFO MainProcess]: Saving gong call 589759dd-0409-46d1-ae09-0eccb90d88ad metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:42:22 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["49 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_6.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:42:24 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:42:25 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:42:25 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:42:26 services.py 177 INFO MainProcess]: Saving gong call 69128c86-61af-4731-a3bc-c83bc26a0e6c metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "64 False False\n", "65 False False\n", "66 False False\n", "67 False False\n", "68 False False\n", "69 False False\n", "70 False False\n", "71 False False\n", "72 False False\n", "73 False False\n", "74 False False\n", "75 False False\n", "76 False False\n", "77 False False\n", "78 False False\n", "79 False False\n", "80 False False\n", "81 False False\n", "82 False False\n", "83 False False\n", "84 False False\n", "85 False False\n", "86 False False\n", "87 False False\n", "88 False False\n", "89 False False\n", "90 False False\n", "91 False False\n", "92 False False\n", "93 False False\n", "94 False False\n", "95 False False\n", "96 False False\n", "97 False False\n", "98 False False\n", "99 False False\n", "100 False False\n", "101 False False\n", "102 False False\n", "103 False False\n", "104 False False\n", "105 False False\n", "106 False False\n", "107 False False\n", "108 False False\n", "109 False False\n", "110 False False\n", "111 False False\n", "112 False False\n", "113 False False\n", "114 False False\n", "115 False False\n", "116 False False\n", "117 False False\n", "118 False False\n", "119 False False\n", "120 False False\n", "121 False False\n", "122 False False\n", "123 False False\n", "124 False False\n", "125 False False\n", "126 False False\n", "127 False False\n", "128 False False\n", "129 False False\n", "130 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:47:08 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["131 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_10.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:47:11 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:47:11 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:47:11 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:47:12 services.py 177 INFO MainProcess]: Saving gong call 6b8fce04-eeb9-4bef-88c4-dc90bb50d819 metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:49:06 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["57 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_4.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:49:09 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:49:10 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:49:10 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:49:10 services.py 177 INFO MainProcess]: Saving gong call 6e688651-5d45-4082-b733-5c4b44bad0ee metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:50:25 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["32 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_5.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:50:28 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:50:29 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:50:29 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:50:29 services.py 177 INFO MainProcess]: Saving gong call abb53d47-a944-4fb1-a5d2-fd5337031611 metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "64 False False\n", "65 False False\n", "66 False False\n", "67 False False\n", "68 False False\n", "69 False False\n", "70 False False\n", "71 False False\n", "72 False False\n", "73 False False\n", "74 False False\n", "75 False False\n", "76 False False\n", "77 False False\n", "78 False False\n", "79 False False\n", "80 False False\n", "81 False False\n", "82 False False\n", "83 False False\n", "84 False False\n", "85 False False\n", "86 False False\n", "87 False False\n", "88 False False\n", "89 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:53:24 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["90 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_8.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:53:26 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:53:27 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:53:27 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:53:28 services.py 177 INFO MainProcess]: Saving gong call ee207ad3-c6a1-4da8-bdb0-8f1a4338ca30 metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:55:37 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["64 False True\n", "/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input/call_9.txt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:55:39 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:55:39 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:55:40 utility.py 387 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 11:55:40 services.py 177 INFO MainProcess]: Saving gong call 2bb9a86f-8212-4556-84ca-890344be153d metadata to database......\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 True False\n", "1 False False\n", "2 False False\n", "3 False False\n", "4 False False\n", "5 False False\n", "6 False False\n", "7 False False\n", "8 False False\n", "9 False False\n", "10 False False\n", "11 False False\n", "12 False False\n", "13 False False\n", "14 False False\n", "15 False False\n", "16 False False\n", "17 False False\n", "18 False False\n", "19 False False\n", "20 False False\n", "21 False False\n", "22 False False\n", "23 False False\n", "24 False False\n", "25 False False\n", "26 False False\n", "27 False False\n", "28 False False\n", "29 False False\n", "30 False False\n", "31 False False\n", "32 False False\n", "33 False False\n", "34 False False\n", "35 False False\n", "36 False False\n", "37 False False\n", "38 False False\n", "39 False False\n", "40 False False\n", "41 False False\n", "42 False False\n", "43 False False\n", "44 False False\n", "45 False False\n", "46 False False\n", "47 False False\n", "48 False False\n", "49 False False\n", "50 False False\n", "51 False False\n", "52 False False\n", "53 False False\n", "54 False False\n", "55 False False\n", "56 False False\n", "57 False False\n", "58 False False\n", "59 False False\n", "60 False False\n", "61 False False\n", "62 False False\n", "63 False False\n", "64 False False\n", "65 False False\n", "66 False False\n", "67 False False\n", "Syncing the changes from files to the knowledge base\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:58:17 aws_utils.py 630 INFO MainProcess]: Synced data source 1DGKD3L9IO to knowledge base EMQ1ERYDJF\n"]}, {"name": "stdout", "output_type": "stream", "text": ["68 False True\n"]}], "source": ["root_dir = '/Users/<USER>/graphrag/Default_text_split_graph/default_3_calls/input'\n", "paths_list = os.listdir(root_dir)\n", "for call_i_path in paths_list:\n", "    gong_call_id_i = uuid.uuid4()\n", "    print(os.path.join(root_dir,call_i_path))\n", "    transcript_call_i = read_transcript(os.path.join(root_dir,call_i_path))\n", "    date_i, chunks_of_call_i = parse_transcript(transcript_call_i, 15)\n", "    upstream_uploader(chunks_of_call_i, date_i, gong_call_id_i,\n", "                      client_id = 10039, opportunity_id = 'upstream_sim_3.1')\n", "\n", "# graph_rag_indexed_parquet_path = index_files(s3_path=graph_rag_call_folder_path, input_type=EntityType.GONG_CALL.value)\n", "# print(\"indexing done\")\n", "# add_graph_rag_details_to_typesense(\n", "#     client_id=client_id,\n", "#     opportunity_id=opportunity_id,\n", "#     graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,\n", "# )\n", "# print(\"added... graphrag details in typsense\")"]}, {"cell_type": "code", "execution_count": 17, "id": "31f0e3b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["is_local_env: False\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-22 11:45:35 graph_rag_utils.py 23 INFO MainProcess]: Invoking the lambda function for graph RAG indexing\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Payload: {'s3_path': 'gong_calls/graph_rag/Sidd_test_10039/upstream_sim_1.3', 'task_type': 'index', 'input_type': 'gong_call'}\n"]}, {"ename": "ReadTimeoutError", "evalue": "Read timeout on endpoint URL: \"https://lambda.ap-south-1.amazonaws.com/2015-03-31/functions/graph-rag-lambda/invocations\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTimeoutError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/connectionpool.py:468\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    464\u001b[0m         \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    465\u001b[0m             \u001b[38;5;66;03m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    466\u001b[0m             \u001b[38;5;66;03m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    467\u001b[0m             \u001b[38;5;66;03m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[0;32m--> 468\u001b[0m             \u001b[43msix\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_from\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    469\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError, SocketError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m<string>:3\u001b[0m, in \u001b[0;36mraise_from\u001b[0;34m(value, from_value)\u001b[0m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/connectionpool.py:463\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    462\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 463\u001b[0m     httplib_response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    464\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    465\u001b[0m     \u001b[38;5;66;03m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    466\u001b[0m     \u001b[38;5;66;03m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    467\u001b[0m     \u001b[38;5;66;03m# Otherwise it looks like a bug in the code.\u001b[39;00m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/http/client.py:1368\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1367\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1368\u001b[0m     \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1369\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/http/client.py:317\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    316\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 317\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    318\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/http/client.py:278\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    277\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_read_status\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 278\u001b[0m     line \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreadline\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_MAXLINE\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miso-8859-1\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    279\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(line) \u001b[38;5;241m>\u001b[39m _MAXLINE:\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py:705\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    704\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 705\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    706\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/ssl.py:1273\u001b[0m, in \u001b[0;36mSSLSocket.recv_into\u001b[0;34m(self, buffer, nbytes, flags)\u001b[0m\n\u001b[1;32m   1270\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mV<PERSON><PERSON><PERSON>rror\u001b[39;00m(\n\u001b[1;32m   1271\u001b[0m           \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnon-zero flags not allowed in calls to recv_into() on \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m   1272\u001b[0m           \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m)\n\u001b[0;32m-> 1273\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnbytes\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1274\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/ssl.py:1129\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1128\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m buffer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m-> 1129\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sslobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1130\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n", "\u001b[0;31mTimeoutError\u001b[0m: The read operation timed out", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mReadTimeoutError\u001b[0m                          Traceback (most recent call last)", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/httpsession.py:464\u001b[0m, in \u001b[0;36mURLLib3Session.send\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    463\u001b[0m request_target \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_request_target(request\u001b[38;5;241m.\u001b[39murl, proxy_url)\n\u001b[0;32m--> 464\u001b[0m urllib_response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    465\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    466\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest_target\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    467\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    468\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    469\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mRetry\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    470\u001b[0m \u001b[43m    \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    471\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    472\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    473\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_chunked\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    474\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    476\u001b[0m http_response \u001b[38;5;241m=\u001b[39m botocore\u001b[38;5;241m.\u001b[39mawsrequest\u001b[38;5;241m.\u001b[39mAWSResponse(\n\u001b[1;32m    477\u001b[0m     request\u001b[38;5;241m.\u001b[39murl,\n\u001b[1;32m    478\u001b[0m     urllib_response\u001b[38;5;241m.\u001b[39mstatus,\n\u001b[1;32m    479\u001b[0m     urllib_response\u001b[38;5;241m.\u001b[39mheaders,\n\u001b[1;32m    480\u001b[0m     urllib_response,\n\u001b[1;32m    481\u001b[0m )\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/connectionpool.py:802\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    800\u001b[0m     e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, e)\n\u001b[0;32m--> 802\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    803\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m    804\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    805\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/util/retry.py:527\u001b[0m, in \u001b[0;36mRetry.increment\u001b[0;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[1;32m    525\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtotal \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m error:\n\u001b[1;32m    526\u001b[0m     \u001b[38;5;66;03m# Disabled, indicate to re-raise the error.\u001b[39;00m\n\u001b[0;32m--> 527\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43msix\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreraise\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mtype\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    529\u001b[0m total \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtotal\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/packages/six.py:770\u001b[0m, in \u001b[0;36mreraise\u001b[0;34m(tp, value, tb)\u001b[0m\n\u001b[1;32m    769\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m value\u001b[38;5;241m.\u001b[39mwith_traceback(tb)\n\u001b[0;32m--> 770\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m value\n\u001b[1;32m    771\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/connectionpool.py:716\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    715\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 716\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    722\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    723\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    724\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    726\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    728\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    729\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/connectionpool.py:470\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    469\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError, SocketError) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 470\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_timeout\u001b[49m\u001b[43m(\u001b[49m\u001b[43merr\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout_value\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_timeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    471\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/urllib3/connectionpool.py:358\u001b[0m, in \u001b[0;36mHTTPConnectionPool._raise_timeout\u001b[0;34m(self, err, url, timeout_value)\u001b[0m\n\u001b[1;32m    357\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(err, SocketTimeout):\n\u001b[0;32m--> 358\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ReadTimeoutError(\n\u001b[1;32m    359\u001b[0m         \u001b[38;5;28mself\u001b[39m, url, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRead timed out. (read timeout=\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m timeout_value\n\u001b[1;32m    360\u001b[0m     )\n\u001b[1;32m    362\u001b[0m \u001b[38;5;66;03m# See the above comment about EAGAIN in Python 3. In Python 2 we have\u001b[39;00m\n\u001b[1;32m    363\u001b[0m \u001b[38;5;66;03m# to specifically catch it and throw the timeout error\u001b[39;00m\n", "\u001b[0;31mReadTimeoutError\u001b[0m: AWSHTTPSConnectionPool(host='lambda.ap-south-1.amazonaws.com', port=443): Read timed out. (read timeout=900)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mReadTimeoutError\u001b[0m                          Traceback (most recent call last)", "Cell \u001b[0;32mIn[17], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m graph_rag_indexed_parquet_path \u001b[38;5;241m=\u001b[39m \u001b[43mindex_files\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms3_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgraph_rag_call_folder_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minput_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mEntityType\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mGONG_CALL\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mindexing done\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      3\u001b[0m add_graph_rag_details_to_typesense(\n\u001b[1;32m      4\u001b[0m     client_id\u001b[38;5;241m=\u001b[39mclient_id,\n\u001b[1;32m      5\u001b[0m     opportunity_id\u001b[38;5;241m=\u001b[39mopportunity_id,\n\u001b[1;32m      6\u001b[0m     graph_rag_indexed_parquet_path\u001b[38;5;241m=\u001b[39mgraph_rag_indexed_parquet_path,\n\u001b[1;32m      7\u001b[0m )\n", "File \u001b[0;32m~/everstage-spm/interstage_project/everstage_ddd/llm_agent/cpq_agent/graph_rag_utils.py:35\u001b[0m, in \u001b[0;36mindex_files\u001b[0;34m(s3_path, input_type)\u001b[0m\n\u001b[1;32m     33\u001b[0m payload \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124ms3_path\u001b[39m\u001b[38;5;124m\"\u001b[39m: s3_path, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtask_type\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mindex\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minput_type\u001b[39m\u001b[38;5;124m\"\u001b[39m: input_type}\n\u001b[1;32m     34\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPayload: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpayload\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 35\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mlambda_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     36\u001b[0m \u001b[43m    \u001b[49m\u001b[43mFunctionName\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfunction_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     37\u001b[0m \u001b[43m    \u001b[49m\u001b[43mInvocationType\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mRequestResponse\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     38\u001b[0m \u001b[43m    \u001b[49m\u001b[43mPayload\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdumps\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     39\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     40\u001b[0m response \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(response[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPayload\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mread())\n\u001b[1;32m     41\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mResponse: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresponse\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/client.py:569\u001b[0m, in \u001b[0;36mClientCreator._create_api_method.<locals>._api_call\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    565\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m    566\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpy_operation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m() only accepts keyword arguments.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    567\u001b[0m     )\n\u001b[1;32m    568\u001b[0m \u001b[38;5;66;03m# The \"self\" in this scope is referring to the BaseClient.\u001b[39;00m\n\u001b[0;32m--> 569\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_api_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/client.py:1005\u001b[0m, in \u001b[0;36mBaseClient._make_api_call\u001b[0;34m(self, operation_name, api_params)\u001b[0m\n\u001b[1;32m   1001\u001b[0m     maybe_compress_request(\n\u001b[1;32m   1002\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmeta\u001b[38;5;241m.\u001b[39mconfig, request_dict, operation_model\n\u001b[1;32m   1003\u001b[0m     )\n\u001b[1;32m   1004\u001b[0m     apply_request_checksum(request_dict)\n\u001b[0;32m-> 1005\u001b[0m     http, parsed_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1006\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_context\u001b[49m\n\u001b[1;32m   1007\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1009\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmeta\u001b[38;5;241m.\u001b[39mevents\u001b[38;5;241m.\u001b[39memit(\n\u001b[1;32m   1010\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mafter-call.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mservice_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moperation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   1011\u001b[0m     http_response\u001b[38;5;241m=\u001b[39mhttp,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1014\u001b[0m     context\u001b[38;5;241m=\u001b[39mrequest_context,\n\u001b[1;32m   1015\u001b[0m )\n\u001b[1;32m   1017\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m http\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m300\u001b[39m:\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/client.py:1029\u001b[0m, in \u001b[0;36mBaseClient._make_request\u001b[0;34m(self, operation_model, request_dict, request_context)\u001b[0m\n\u001b[1;32m   1027\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_make_request\u001b[39m(\u001b[38;5;28mself\u001b[39m, operation_model, request_dict, request_context):\n\u001b[1;32m   1028\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1029\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_endpoint\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmake_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1030\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m   1031\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmeta\u001b[38;5;241m.\u001b[39mevents\u001b[38;5;241m.\u001b[39memit(\n\u001b[1;32m   1032\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mafter-call-error.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_service_model\u001b[38;5;241m.\u001b[39mservice_id\u001b[38;5;241m.\u001b[39mhyphenize()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moperation_model\u001b[38;5;241m.\u001b[39mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   1033\u001b[0m             exception\u001b[38;5;241m=\u001b[39me,\n\u001b[1;32m   1034\u001b[0m             context\u001b[38;5;241m=\u001b[39mrequest_context,\n\u001b[1;32m   1035\u001b[0m         )\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/endpoint.py:119\u001b[0m, in \u001b[0;36mEndpoint.make_request\u001b[0;34m(self, operation_model, request_dict)\u001b[0m\n\u001b[1;32m    113\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mmake_request\u001b[39m(\u001b[38;5;28mself\u001b[39m, operation_model, request_dict):\n\u001b[1;32m    114\u001b[0m     logger\u001b[38;5;241m.\u001b[39mdebug(\n\u001b[1;32m    115\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMaking request for \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m with params: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    116\u001b[0m         operation_model,\n\u001b[1;32m    117\u001b[0m         request_dict,\n\u001b[1;32m    118\u001b[0m     )\n\u001b[0;32m--> 119\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/endpoint.py:200\u001b[0m, in \u001b[0;36mEndpoint._send_request\u001b[0;34m(self, request_dict, operation_model)\u001b[0m\n\u001b[1;32m    196\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcreate_request(request_dict, operation_model)\n\u001b[1;32m    197\u001b[0m success_response, exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_response(\n\u001b[1;32m    198\u001b[0m     request, operation_model, context\n\u001b[1;32m    199\u001b[0m )\n\u001b[0;32m--> 200\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_needs_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    201\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattempts\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    202\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    203\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    204\u001b[0m \u001b[43m    \u001b[49m\u001b[43msuccess_response\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    205\u001b[0m \u001b[43m    \u001b[49m\u001b[43mexception\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    206\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m    207\u001b[0m     attempts \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m    208\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_update_retries_context(context, attempts, success_response)\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/endpoint.py:360\u001b[0m, in \u001b[0;36mEndpoint._needs_retry\u001b[0;34m(self, attempts, operation_model, request_dict, response, caught_exception)\u001b[0m\n\u001b[1;32m    358\u001b[0m service_id \u001b[38;5;241m=\u001b[39m operation_model\u001b[38;5;241m.\u001b[39mservice_model\u001b[38;5;241m.\u001b[39mservice_id\u001b[38;5;241m.\u001b[39mhyphenize()\n\u001b[1;32m    359\u001b[0m event_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mneeds-retry.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mservice_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moperation_model\u001b[38;5;241m.\u001b[39mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 360\u001b[0m responses \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_event_emitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43memit\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    361\u001b[0m \u001b[43m    \u001b[49m\u001b[43mevent_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    362\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    363\u001b[0m \u001b[43m    \u001b[49m\u001b[43mendpoint\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    364\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    365\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattempts\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mattempts\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    366\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcaught_exception\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcaught_exception\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    367\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    368\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    369\u001b[0m handler_response \u001b[38;5;241m=\u001b[39m first_non_none_response(responses)\n\u001b[1;32m    370\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m handler_response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/hooks.py:412\u001b[0m, in \u001b[0;36mEventAliaser.emit\u001b[0;34m(self, event_name, **kwargs)\u001b[0m\n\u001b[1;32m    410\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21memit\u001b[39m(\u001b[38;5;28mself\u001b[39m, event_name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    411\u001b[0m     aliased_event_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_alias_event_name(event_name)\n\u001b[0;32m--> 412\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_emitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43memit\u001b[49m\u001b[43m(\u001b[49m\u001b[43maliased_event_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/hooks.py:256\u001b[0m, in \u001b[0;36mHierarchicalEmitter.emit\u001b[0;34m(self, event_name, **kwargs)\u001b[0m\n\u001b[1;32m    245\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21memit\u001b[39m(\u001b[38;5;28mself\u001b[39m, event_name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    246\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    247\u001b[0m \u001b[38;5;124;03m    Emit an event by name with arguments passed as keyword args.\u001b[39;00m\n\u001b[1;32m    248\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    254\u001b[0m \u001b[38;5;124;03m             handlers.\u001b[39;00m\n\u001b[1;32m    255\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 256\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_emit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mevent_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/hooks.py:239\u001b[0m, in \u001b[0;36mHierarchicalEmitter._emit\u001b[0;34m(self, event_name, kwargs, stop_on_response)\u001b[0m\n\u001b[1;32m    237\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m handler \u001b[38;5;129;01min\u001b[39;00m handlers_to_call:\n\u001b[1;32m    238\u001b[0m     logger\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEvent \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m: calling handler \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m, event_name, handler)\n\u001b[0;32m--> 239\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mhandler\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    240\u001b[0m     responses\u001b[38;5;241m.\u001b[39mappend((handler, response))\n\u001b[1;32m    241\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m stop_on_response \u001b[38;5;129;01mand\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/retryhandler.py:207\u001b[0m, in \u001b[0;36mRetryHandler.__call__\u001b[0;34m(self, attempts, response, caught_exception, **kwargs)\u001b[0m\n\u001b[1;32m    204\u001b[0m     retries_context \u001b[38;5;241m=\u001b[39m kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrequest_dict\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontext\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mretries\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m    205\u001b[0m     checker_kwargs\u001b[38;5;241m.\u001b[39mupdate({\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mretries_context\u001b[39m\u001b[38;5;124m'\u001b[39m: retries_context})\n\u001b[0;32m--> 207\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_checker\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mchecker_kwargs\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m    208\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_action(attempts\u001b[38;5;241m=\u001b[39mattempts)\n\u001b[1;32m    209\u001b[0m     logger\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetry needed, action of: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, result)\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/retryhandler.py:284\u001b[0m, in \u001b[0;36mMaxAttemptsDecorator.__call__\u001b[0;34m(self, attempt_number, response, caught_exception, retries_context)\u001b[0m\n\u001b[1;32m    279\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m retries_context:\n\u001b[1;32m    280\u001b[0m     retries_context[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmax\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmax\u001b[39m(\n\u001b[1;32m    281\u001b[0m         retries_context\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmax\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m0\u001b[39m), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_max_attempts\n\u001b[1;32m    282\u001b[0m     )\n\u001b[0;32m--> 284\u001b[0m should_retry \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_should_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    285\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattempt_number\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaught_exception\u001b[49m\n\u001b[1;32m    286\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    287\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m should_retry:\n\u001b[1;32m    288\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m attempt_number \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_max_attempts:\n\u001b[1;32m    289\u001b[0m         \u001b[38;5;66;03m# explicitly set MaxAttemptsReached\u001b[39;00m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/retryhandler.py:320\u001b[0m, in \u001b[0;36mMaxAttemptsDecorator._should_retry\u001b[0;34m(self, attempt_number, response, caught_exception)\u001b[0m\n\u001b[1;32m    316\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m    317\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    318\u001b[0m     \u001b[38;5;66;03m# If we've exceeded the max attempts we just let the exception\u001b[39;00m\n\u001b[1;32m    319\u001b[0m     \u001b[38;5;66;03m# propagate if one has occurred.\u001b[39;00m\n\u001b[0;32m--> 320\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_checker\u001b[49m\u001b[43m(\u001b[49m\u001b[43mattempt_number\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaught_exception\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/retryhandler.py:363\u001b[0m, in \u001b[0;36mMultiChecker.__call__\u001b[0;34m(self, attempt_number, response, caught_exception)\u001b[0m\n\u001b[1;32m    361\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, attempt_number, response, caught_exception):\n\u001b[1;32m    362\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m checker \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_checkers:\n\u001b[0;32m--> 363\u001b[0m         checker_response \u001b[38;5;241m=\u001b[39m \u001b[43mchecker\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    364\u001b[0m \u001b[43m            \u001b[49m\u001b[43mattempt_number\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaught_exception\u001b[49m\n\u001b[1;32m    365\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    366\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m checker_response:\n\u001b[1;32m    367\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m checker_response\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/retryhandler.py:247\u001b[0m, in \u001b[0;36mBaseChecker.__call__\u001b[0;34m(self, attempt_number, response, caught_exception)\u001b[0m\n\u001b[1;32m    245\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_response(attempt_number, response)\n\u001b[1;32m    246\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m caught_exception \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 247\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_check_caught_exception\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    248\u001b[0m \u001b[43m        \u001b[49m\u001b[43mattempt_number\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaught_exception\u001b[49m\n\u001b[1;32m    249\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    250\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    251\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBoth response and caught_exception are None.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/retryhandler.py:416\u001b[0m, in \u001b[0;36mExceptionRaiser._check_caught_exception\u001b[0;34m(self, attempt_number, caught_exception)\u001b[0m\n\u001b[1;32m    408\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_check_caught_exception\u001b[39m(\u001b[38;5;28mself\u001b[39m, attempt_number, caught_exception):\n\u001b[1;32m    409\u001b[0m     \u001b[38;5;66;03m# This is implementation specific, but this class is useful by\u001b[39;00m\n\u001b[1;32m    410\u001b[0m     \u001b[38;5;66;03m# coordinating with the MaxAttemptsDecorator.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    414\u001b[0m     \u001b[38;5;66;03m# the MaxAttemptsDecorator is not interested in retrying the exception\u001b[39;00m\n\u001b[1;32m    415\u001b[0m     \u001b[38;5;66;03m# then this exception just propagates out past the retry code.\u001b[39;00m\n\u001b[0;32m--> 416\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m caught_exception\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/endpoint.py:279\u001b[0m, in \u001b[0;36mEndpoint._do_get_response\u001b[0;34m(self, request, operation_model, context)\u001b[0m\n\u001b[1;32m    277\u001b[0m     http_response \u001b[38;5;241m=\u001b[39m first_non_none_response(responses)\n\u001b[1;32m    278\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m http_response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 279\u001b[0m         http_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    280\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m HTTPClientError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    281\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[38;5;28;01mNone\u001b[39;00m, e)\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/endpoint.py:383\u001b[0m, in \u001b[0;36mEndpoint._send\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    382\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_send\u001b[39m(\u001b[38;5;28mself\u001b[39m, request):\n\u001b[0;32m--> 383\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhttp_session\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/botocore/httpsession.py:501\u001b[0m, in \u001b[0;36mURLLib3Session.send\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    499\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ConnectTimeoutError(endpoint_url\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39murl, error\u001b[38;5;241m=\u001b[39me)\n\u001b[1;32m    500\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m URLLib3ReadTimeoutError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 501\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ReadTimeoutError(endpoint_url\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39murl, error\u001b[38;5;241m=\u001b[39me)\n\u001b[1;32m    502\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ProtocolError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    503\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ConnectionClosedError(\n\u001b[1;32m    504\u001b[0m         error\u001b[38;5;241m=\u001b[39me, request\u001b[38;5;241m=\u001b[39mrequest, endpoint_url\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39murl\n\u001b[1;32m    505\u001b[0m     )\n", "\u001b[0;31mReadTimeoutError\u001b[0m: Read timeout on endpoint URL: \"https://lambda.ap-south-1.amazonaws.com/2015-03-31/functions/graph-rag-lambda/invocations\""]}], "source": ["graph_rag_indexed_parquet_path = index_files(s3_path=graph_rag_call_folder_path, input_type=EntityType.GONG_CALL.value)\n", "print(\"indexing done\")\n", "add_graph_rag_details_to_typesense(\n", "    client_id=client_id,\n", "    opportunity_id=opportunity_id,\n", "    graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,\n", ")\n", "print(\"added... graphrag details in typsense\")"]}, {"cell_type": "code", "execution_count": null, "id": "e9151a29", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "91ba1cf8", "metadata": {}, "outputs": [], "source": ["        attach_file_to_long_context_prompt(\n", "            call_file_path=long_context_file_path,\n", "            long_context_prompt_path=long_context_prompt_path,\n", "            call_date=call_date_str,\n", "        )\n", "        # add the long context details to typesense\n", "        add_llm_rag_details_to_typesense(\n", "            client_id=client_id,\n", "            opportunity_id=opportunity_id,\n", "            entity_type=EntityType.GONG_CALL.value,\n", "            long_context_prompt_path=long_context_prompt_path,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "751d7567", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b27b46d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "568e2331", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c390456a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "63defa49", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}