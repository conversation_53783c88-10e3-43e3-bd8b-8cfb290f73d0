{"cells": [{"cell_type": "code", "execution_count": 1, "id": "41b032a1", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import sys\n", "\n", "from everstage_ddd.llm_agent.cpq_agent.services import process_gong_call_chunk, process_hubspot_data\n", "\n", "import uuid\n", "\n", "import boto3"]}, {"cell_type": "code", "execution_count": 2, "id": "5afc6068", "metadata": {}, "outputs": [], "source": ["os.chdir('/Users/<USER>/everstage-spm/interstage_project')"]}, {"cell_type": "code", "execution_count": 3, "id": "a24b9938", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, set_key\n", "\n", "os.environ['AWS_REGION'] = 'AP-SOUTH-1'\n", "os.environ['S3_GONG_CALL_BUCKET'] = 'everstage-cpq-ai-assets'\n", "\n", "os.environ['OPENAI_API_KEY'] = '***************************************************'\n", "\n", "aws_access_key_id=\"********************\"\n", "aws_secret_access_key=\"X6ncH6xLto8saglU7l29sbpwFc2ZxokishkCmin+\"\n", "aws_session_token=\"IQoJb3JpZ2luX2VjEM///////////wEaCXVzLXdlc3QtMiJIMEYCIQCAUyeOnVw+PymyNrZqyQWH05/EsLKrWQ9NJP7LdRkevgIhANDZtfohzlLxUOnemNeGNuGBg5GO0NvAGH/j4TiBCR74KqwDCIj//////////wEQAxoMOTE5MjE1Mzg2ODAxIgztjHMJbNmr+bKq/qYqgAPNXulb8KOcUy3z4lHqz0OrcAchovGegDUYxsQwmXz9KQWSK+sEkR7lmSq9Aa3Ah3NsP6V5Xu9//uHJklAXKtZs924h1UTGdCfsLQ3yOlp0k5dWWPwfglPDsXUwIRKsWe0a7JhCn7J4l7GJa03UdCJ44bixL/jjXnNmT0oUk9Ql7omM2kKbAAMsiAnWTppHht6hUbBmHOYjgcRHIWSsxKrZqL7Q9c9Zyibpja/2NSL917yP3CMKeyOgIXxJ0bQO/qnadV1eSE1yAH+jPq54paFpId9ANBjlYhZUUEGBK1uf3abR0st7H2IpndE3Lkm+2VCpAogoVn5IRFa3QBt7bvn4dAI9LshU47DdtlEGGYAGUy+bY8QnGGHpQhtHBdlM4/5hedJWf/5Yz1/IE8Jin5j3SlQr/ihoSL45W9pb+HX6/GsjsTstQfGrGl6YCt8rZEMkpb5s+MQqhs+TMaE/xpEHpsO9FF5m8s3U7OY/4WgLg8u1YCWpWl7TMUs/ioqDktMwpJ6rwQY6pQF15nvhSdkJDZZgMrFcrkeJSRL7+o5LyKKvhb5NPz5JpVwoIrIbZJ9lwC8rVH5P+2K57nK8ojhGN8isv+jBFCMHlYau5MGKRo3pOW9Z6cD1FGhbiak1g5kL7epDPrmji+h6he4nUTcgCqFn/tyOZ3HCQaJ6SvTZJI+Prgk+Mk3WZyG0n8z3jWT8zh2B5LDvFMT4GWXwW8ghwSDD+HdH8Gx1qY1YFH0=\"\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_ACCESS_KEY_ID'] = aws_access_key_id\n", "os.environ['AWS_SECRET_ACCESS_KEY'] = aws_secret_access_key\n", "os.environ['AWS_SESSION_TOKEN'] = aws_session_token\n", "\n", "# # Update .env file directly\n", "set_key('.env', 'AWS_ACCESS_KEY_ID', aws_access_key_id)\n", "set_key('.env', 'AWS_SECRET_ACCESS_KEY', aws_secret_access_key)\n", "set_key('.env', 'AWS_SESSION_TOKEN', aws_session_token)\n", "set_key('.env', 'AWS_DEFAULT_REGION', 'ap-south-1')\n", "\n", "# # Reload environment\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f9e6a5e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "85172138", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_DEFAULT_REGION'] = 'ap-south-1'"]}, {"cell_type": "code", "execution_count": null, "id": "c390179f", "metadata": {}, "outputs": [], "source": ["export AWS_ACCESS_KEY_ID=\"********************\"\n", "export AWS_SECRET_ACCESS_KEY=\"X6ncH6xLto8saglU7l29sbpwFc2ZxokishkCmin+\"\n", "export AWS_SESSION_TOKEN=\"IQoJb3JpZ2luX2VjEM///////////wEaCXVzLXdlc3QtMiJIMEYCIQCAUyeOnVw+PymyNrZqyQWH05/EsLKrWQ9NJP7LdRkevgIhANDZtfohzlLxUOnemNeGNuGBg5GO0NvAGH/j4TiBCR74KqwDCIj//////////wEQAxoMOTE5MjE1Mzg2ODAxIgztjHMJbNmr+bKq/qYqgAPNXulb8KOcUy3z4lHqz0OrcAchovGegDUYxsQwmXz9KQWSK+sEkR7lmSq9Aa3Ah3NsP6V5Xu9//uHJklAXKtZs924h1UTGdCfsLQ3yOlp0k5dWWPwfglPDsXUwIRKsWe0a7JhCn7J4l7GJa03UdCJ44bixL/jjXnNmT0oUk9Ql7omM2kKbAAMsiAnWTppHht6hUbBmHOYjgcRHIWSsxKrZqL7Q9c9Zyibpja/2NSL917yP3CMKeyOgIXxJ0bQO/qnadV1eSE1yAH+jPq54paFpId9ANBjlYhZUUEGBK1uf3abR0st7H2IpndE3Lkm+2VCpAogoVn5IRFa3QBt7bvn4dAI9LshU47DdtlEGGYAGUy+bY8QnGGHpQhtHBdlM4/5hedJWf/5Yz1/IE8Jin5j3SlQr/ihoSL45W9pb+HX6/GsjsTstQfGrGl6YCt8rZEMkpb5s+MQqhs+TMaE/xpEHpsO9FF5m8s3U7OY/4WgLg8u1YCWpWl7TMUs/ioqDktMwpJ6rwQY6pQF15nvhSdkJDZZgMrFcrkeJSRL7+o5LyKKvhb5NPz5JpVwoIrIbZJ9lwC8rVH5P+2K57nK8ojhGN8isv+jBFCMHlYau5MGKRo3pOW9Z6cD1FGhbiak1g5kL7epDPrmji+h6he4nUTcgCqFn/tyOZ3HCQaJ6SvTZJI+Prgk+Mk3WZyG0n8z3jWT8zh2B5LDvFMT4GWXwW8ghwSDD+HdH8Gx1qY1YFH0=\""]}, {"cell_type": "code", "execution_count": 5, "id": "564e36bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["chunks = []\n", "chunks.append({\"speakerId\":\"6432345678555530055\",\n", "               \"speaker_name\" : \"<PERSON>\",\n", "               \"topic\": \"Objections\",\n", "               'sentences': [\n", "                   {\n", "                    \"start\": 460230,\n", "                    \"end\": 462343,\n", "                    \"text\" : '''Yeah, for sure. I'll kind of just jump here Hilary keep me honest. So getting into Everstage as a platform, what we understood was that you had a native integration to HubSpot. It's about being our CRM, It runs our entire business, right? It's the center of the universe here. That was the representation that essentially was made. Now, what it's kind of appeared to be… is that the integration with HubSpot is actually more of just raw API kind of pull. And so it's just a big data feed of information and connection… Realistically in a world where there's a native integration with a platform. Usually, it's not as hard as this was Like us going. Hey, we want to pull deals.\n", ";;\n", "Yeah, for sure. So I am officially now two weeks into my TerraMind tenure. There we go. Yeah, a lot of what I'm working on right now is on top of getting a lay of the land is trying to work on really defining what we're going to be going forward with our strategy, our overarching strategy for CS, what that looks like working backwards from our target KPIs and company goals. So that's a lot of where I've been spending the time right now. The way that we operate from a specific to this particular call, CS has its own comp plan, similar to how sales would, that has a couple of different components. And as we get into it, I'll have some more specific questions because there are factors that could influence it. And just a little caveat, I haven't put it in stone yet either. That's another thing that I'm working on is revamping what that comp plan looks like based on the strategy, of course. But yeah, All that to say, there are some nuances in the CS version that take into account some revenue items, but also some maybe non revenue items that can impact the actual payout. So we can talk about that more when we get into it, just a heads up there.\n", ";;\n", "'''             \n", "                   }\n", "               ]})\n", "\n", "chunks.append({\"speakerId\":\"6432345678555530057\",\n", "               \"speaker_name\" : \"<PERSON>\",\n", "               'sentences': [\n", "                   {\n", "                    \"start\": 463230,\n", "                    \"end\": 464343,\n", "                    \"text\" : '''Yeah, no, for sure. And that's totally makes sense. Like we've seen it, whether it's renewals or whether it's churn or also could be activity or how much did this kind of grow because of it? There can be things that are pulled from HubSpot or things that are more kind of MBO facing, more behavioral based. So the great thing at every stage is we can support those, and I've done so with countless clients across CS orgs, sales orgs as well. So that's exciting. I've got some new role, and it's always great to join an organization. Yeah, we definitely can help shape those CS plans moving forward. And then finally, last but not least, the clean shaven masks.\n", ";;\n", "Yes. So I've been at the helm of the sales organization now for two and a half, three weeks, I think. Yeah. Was here in an enablement function prior. So I'm not new to Terramind, just new to running the sales organization. And comp was probably the first and foremost thing that I wanted to kind of. get sorted under control and find a new or better solution. So I kind of got thrown into the middle of a comp cycle and just watched what happened and I went, this can be better. So I put it on my list, basically top of my list of things that need to get done. And so, yeah, we're, teams probably like on the sales side, I would imagine by the end of next year, there's probably going to be 10, 10, 12 sellers like, in our mid market enterprise space. We've got, you know, some SMB reps. We've got people in, in MSP potential end of business. So different types of comp plans, different segments, But pretty support sales type comp structure stuff. So nothing too fancy.\n", ";;\n", "'''             \n", "                   }\n", "               ]})\n", "\n", "\n", "\n", "chunks.append({\"speakerId\":\"64323456785555300556\",\n", "               \"speaker_name\" : \"<PERSON>\",\n", "               'sentences': [\n", "                   {\n", "                    \"start\": 470230,\n", "                    \"end\": 472343,\n", "                    'text' : '''Work Got it. <PERSON>. We can definitely make some recommendation Again, as I said earlier, this all seems to be in misalignment, right? Because if this is the case, if you just needed some recommendation on how this needs to be set up, we would definitely make those recommendations, But our team had a different understanding. We were simply waiting for your team to confirm on how this is going to be set up at your end.\n", ";;\n", "Yeah, no, for sure. And that's totally makes sense. Like we've seen it, whether it's renewals or whether it's churn or also could be activity or how much did this kind of grow because of it? There can be things that are pulled from HubSpot or things that are more kind of MBO facing, more behavioral based. So the great thing at every stage is we can support those, and I've done so with countless clients across CS orgs, sales orgs as well. So that's exciting. I've got some new role, and it's always great to join an organization. Yeah, we definitely can help shape those CS plans moving forward. And then finally, last but not least, the clean shaven masks.\n", ";;\n", "'''             \n", "                   },\n", "                   {\n", "                    \"start\": 470400,\n", "                    \"end\": 472403,\n", "                    'text' : '''We would make those recommendations. We just need to know that, you know, there is something some ticket item that needs our assistance or recommendation to be made. Our team would definitely extend that knowledge..\n", ";;'''                         \n", "                   }\n", "               ]})\n", "\n", "print(len(chunks))"]}, {"cell_type": "code", "execution_count": null, "id": "cd9cbf1d", "metadata": {}, "outputs": [], "source": ["chunks= [\n", "    {\n", "      \"speakerId\": \"6432345678555530055\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Initial Deal Discussion\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 460230,\n", "          \"end\": 462343,\n", "          \"text\": \"<PERSON> <PERSON>, thanks for taking the call. As discussed in our previous email, I'm here to walk you through our initial offer for the 100 Toyota Supra GR units. The base price we're looking at is $45,000 per unit, which brings the total to $4.5 million. We're proposing a delivery schedule of 25 units every 4 weeks, completing the full order in 16 weeks. We're also including the premium package, extended warranty, and dealer training in this offer. What are your initial thoughts on these terms?;;\\n\\nI understand your concern about the delivery timeline. The 16-week schedule is based on our current production capacity and quality control requirements. However, I can discuss with our production team about potential acceleration, though it might impact the price point. The premium package includes the 19-inch forged aluminum wheels, adaptive suspension, and the premium JBL audio system. Would you like me to break down the specific features included in the package?\"\n", "        }\n", "      ]\n", "    },\n", "    {\n", "      \"speakerId\": \"6432345678555530057\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Counter Offer Discussion\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 463230,\n", "          \"end\": 464343,\n", "          \"text\": \"Thanks for the detailed breakdown, <PERSON>. After reviewing with our team, we have some concerns. The 16-week delivery timeline is too long for our needs. We need all units within 8 weeks to meet our Q2 targets. We're willing to increase the upfront payment to 40% if you can accelerate the delivery. Also, we're looking at a price point of $42,000 per unit, which would bring the total to $4.2 million. We also need 50% of the units in Absolute Zero White, and we'd like the warranty extended to 7 years or 100,000 miles. Can you work with these terms?;;\\n\\nI understand the production constraints, but our dealership network has specific timing requirements. The 8-week delivery is crucial for our summer sales campaign. We're also concerned about the color distribution. Our market research shows strong demand for the white color option. The extended warranty is non-negotiable as it's a key selling point for our customers. Can we discuss these points further?\"\n", "        }\n", "      ]\n", "    },\n", "    {\n", "      \"speakerId\": \"6432345678555530055\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Final Terms Negotiation\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 465230,\n", "          \"end\": 466343,\n", "          \"text\": \"<PERSON>, I've discussed your requirements with our production and finance teams. Unfortunately, the 8-week delivery timeline is not feasible without compromising quality. However, we can propose a compromise: $43,500 per unit, with a modified delivery schedule of 40 units in 6 weeks, 30 units in 10 weeks, and the final 30 units in 14 weeks. We can offer 40% in Absolute Zero White, with the remaining units distributed across other colors. The warranty can be extended to 6 years or 75,000 miles, which is still above industry standard. How does this sound?;;\\n\\nI need to emphasize that the delivery timeline is firm due to our production constraints. We've already optimized the schedule as much as possible. The price of $43,500 is our best possible offer given the current market conditions and the premium features included. We can include the first-year maintenance package and 18 months of priority parts ordering to sweeten the deal. Would you like me to send over the detailed terms in writing?\"\n", "        }\n", "      ]\n", "    },\n", "    {\n", "      \"speakerId\": \"6432345678555530057\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Deal Finalization\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 467230,\n", "          \"end\": 468343,\n", "          \"text\": \"The modified delivery schedule is still longer than we'd like, but I understand the production constraints. The $43,500 price point is acceptable, and the 40% white color distribution works for us. However, we need to discuss the payment terms. The 35% upfront payment is higher than our standard practice. Could we structure it as 30% upfront, with the remaining 70% split across the delivery milestones? Also, can you confirm the exact specifications of the maintenance package?;;\\n\\nYes, I agree that the warranty terms are reasonable. The 6-year/75,000-mile coverage is competitive. I'll need to present these terms to our board next week. Can you hold this offer for 7 days? Also, could you provide more details about the dealer training program? We'd like to ensure our technicians are fully prepared for the new models.\"\n", "        }\n", "      ]\n", "    }\n", "  ]\n"]}, {"cell_type": "code", "execution_count": 7, "id": "a70607f3", "metadata": {}, "outputs": [], "source": ["client_id = 10039\n", "opportunity_id = 'lambda_2.3'\n", "call_date = '2025-04-07'\n", "is_terminal_call = False\n", "is_first_chunk_of_call = False\n", "gong_call_id = uuid.uuid4()"]}, {"cell_type": "code", "execution_count": 6, "id": "25d669ad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:botocore.credentials:Found credentials in environment variables.\n"]}], "source": ["import json\n", "import logging\n", "import os\n", "\n", "import boto3\n", "import requests\n", "from botocore.config import Config\n", "\n", "\n", "lambda_client = boto3.client(\n", "    \"lambda\",\n", "    region_name=os.environ.get(\"LAMBDA_REGION\", \"ap-south-1\"),\n", "    config=Config(\n", "        read_timeout=900, retries={\"max_attempts\": 0}, tcp_keepalive=True\n", "    ),\n", ")\n", "function_name = \"graph-rag-lambda\"\n", "\n", "s3_path = \"gong_calls/graph_rag/Sidd_test_10039/lambda_local_flask3\"\n", "payload = {\"s3_path\": s3_path, \"task_type\": \"index\"}"]}, {"cell_type": "code", "execution_count": 7, "id": "13dafa24", "metadata": {}, "outputs": [], "source": ["response = lambda_client.invoke(\n", "    FunctionName=function_name,\n", "    InvocationType=\"RequestResponse\",\n", "    Payload=json.dumps(payload),\n", ")\n", "response = json.loads(response[\"Payload\"].read())"]}, {"cell_type": "code", "execution_count": 8, "id": "7ce386aa", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'status_code': 200, 'body': 'Success', 'graph_rag_indexed_parquet_path': '/mnt/efs/output/gong_calls/graph_rag/Sidd_test_10039/lambda_local_flask3'}\n"]}], "source": ["print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "76caaac2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ff902880", "metadata": {}, "outputs": [], "source": ["email_chunk_data = {\n", "    \"hs_email_from_email\": \"<EMAIL>\",\n", "    \"hs_email_to_email\": \"<EMAIL>\",\n", "    \"hs_body_preview\": \"\"\"\n", "    Subject: Toyota Supra Bulk Order - 100 Units - Special Pricing & Terms\n", "    \n", "    <PERSON>,\n", "    \n", "    I hope this email finds you well. I'm following up on our detailed discussion about the bulk order of Toyota Supras for your dealership network. \n", "    After reviewing your requirements and consulting with our regional management, we're pleased to offer you the following comprehensive package:\n", "    \n", "    Order Details:\n", "    - Total Units: 100 Toyota Supra GR 2024\n", "    - Base Price per Unit: $45,000 (Special Bulk Rate)\n", "    - Total Order Value: $4,500,000\n", "    - Payment Terms: 30% upfront, 70% upon delivery of each batch\n", "    \n", "    Vehicle Specifications:\n", "    - Model: 2024 Toyota Supra GR\n", "    - Engine: 3.0L Turbocharged Inline-6\n", "    - Power: 382 HP @ 5,800-6,500 rpm\n", "    - Torque: 368 lb-ft @ 1,800-5,000 rpm\n", "    - Transmission: 8-Speed Automatic with paddle shifters\n", "    - 0-60 mph: 4.1 seconds\n", "    - Top Speed: 155 mph (electronically limited)\n", "    \n", "    Standard Features:\n", "    - Premium Package (included)\n", "    - 19-inch forged aluminum wheels\n", "    - Adaptive Variable Suspension\n", "    - Brembo® performance brakes\n", "    - Active Differential\n", "    - Heads-up <PERSON><PERSON><PERSON>\n", "    - Premium JBL® Audio System\n", "    - Wireless Apple CarPlay® and Android Auto™\n", "    \n", "    Delivery Schedule:\n", "    - Batch 1 (25 units): 4 weeks from order confirmation\n", "    - Batch 2 (25 units): 8 weeks from order confirmation\n", "    - Batch 3 (25 units): 12 weeks from order confirmation\n", "    - Batch 4 (25 units): 16 weeks from order confirmation\n", "    \n", "    Additional Benefits:\n", "    - Free dealer training for 5 technicians\n", "    - Extended warranty coverage (5 years/60,000 miles)\n", "    - Priority parts ordering for 12 months\n", "    - Marketing support package worth $50,000\n", "    - Exclusive launch event for your dealership\n", "    \n", "    Color Options Available:\n", "    - Absolute Zero White\n", "    - Renaissance Red 2.0\n", "    - Nitro Yellow\n", "    - Nocturnal Black\n", "    - <PERSON>ngsten <PERSON>\n", "    \n", "    We're also offering:\n", "    - Free transportation to your dealerships\n", "    - Pre-delivery inspection for all units\n", "    - Custom dealership badging option\n", "    - First-year maintenance package\n", "    \n", "    Please review these terms and let me know if you'd like to:\n", "    1. Proceed with the order\n", "    2. Schedule a test drive for your management team\n", "    3. Discuss financing options\n", "    4. Review the marketing support package\n", "    \n", "    I'm available for a call tomorrow between 10 AM and 4 PM EST to discuss any details.\n", "    \n", "    Best regards,\n", "    <PERSON>\n", "    Senior Sales Manager\n", "    Toyota North America\n", "    Direct: (555) 123-4567\n", "    Email: <EMAIL>\n", "    \"\"\",\n", "    \"hs_createdate\": \"2024-03-15T10:30:00Z\"\n", "}\n", "\n", "notes_chunk_data = {\n", "    \"properties\": {\n", "        \"hs_created_by_user_id\": \"12345\",\n", "        \"hs_createdate\": \"2024-03-15T11:45:00Z\",\n", "        \"hs_body_preview\": \"\"\"\n", "    Deal Update: Toyota Supra Bulk Order - Comprehensive Review\n", "    \n", "    Follow-up call with <PERSON> (Purchasing Director) from Dealership Inc. regarding the 100-unit Supra order.\n", "    \n", "    Meeting Participants:\n", "    - <PERSON> (Purchasing Director)\n", "    - <PERSON> (Finance Manager)\n", "    - <PERSON> (Sales Operations)\n", "    - Our Team: <PERSON>, <PERSON>\n", "    \n", "    Key Discussion Points:\n", "    1. Order Specifications\n", "       - Confirmed interest in the 2024 GR model\n", "       - Discussed color distribution preferences\n", "       - Agreed on standard features package\n", "       - Reviewed technical specifications in detail\n", "    \n", "    2. Financial Discussion\n", "       - Payment terms negotiation\n", "       - Bulk discount confirmation\n", "       - Financing options through Toyota Financial Services\n", "       - Tax implications for multi-state delivery\n", "    \n", "    3. Delivery & Logistics\n", "       - Agreed on phased delivery schedule\n", "       - Discussed transportation arrangements\n", "       - Confirmed PDI requirements\n", "       - Addressed storage solutions for incoming inventory\n", "    \n", "    4. Marketing & Support\n", "       - Reviewed marketing support package\n", "       - Discussed launch event planning\n", "       - Agreed on technician training schedule\n", "       - Confirmed parts ordering priority\n", "    \n", "    Deal Specifics:\n", "    - Total Units: 100\n", "    - Model: 2024 Toyota Supra GR\n", "    - Base Price: $45,000/unit\n", "    - Total Value: $4,500,000\n", "    - Payment Terms: 30/70 split\n", "    - Expected ROI: 22% within first year\n", "    \n", "    Next Steps:\n", "    1. Immediate Actions\n", "       - Send formal quote (by EOD today)\n", "       - Schedule contract review (March 18)\n", "       - Arrange test drive for management team (March 20)\n", "       - Prepare financing documentation\n", "    \n", "    2. Short-term (1-2 weeks)\n", "       - Finalize color distribution\n", "       - Confirm delivery schedule\n", "       - Set up dealer training\n", "       - Begin marketing material preparation\n", "    \n", "    3. Medium-term (1 month)\n", "       - Complete contract signing\n", "       - Process initial payment\n", "       - Begin production allocation\n", "       - Schedule launch event\n", "    \n", "    Risk Assessment:\n", "    - Market demand confirmed through regional analysis\n", "    - Supply chain stability verified\n", "    - Competitor activity monitored\n", "    - Price point validated against market\n", "    \n", "    Deal value: $4.5M\n", "    Expected close date: April 15, 2024\n", "    Probability of close: 85%\n", "    Key decision maker: <PERSON>\n", "    Next follow-up: March 18, 2024\n", "    \n", "    Notes:\n", "    - <PERSON> expressed particular interest in the marketing support\n", "    - <PERSON> needs additional financing details\n", "    - David to coordinate test drive schedule\n", "    - All parties agreed on the delivery timeline\n", "    \"\"\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "7f62193d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed346671", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f91d395", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}