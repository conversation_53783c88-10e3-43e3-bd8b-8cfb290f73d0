{"cells": [{"cell_type": "code", "execution_count": 29, "id": "e1d7e437", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%reload_ext autoreload"]}, {"cell_type": "code", "execution_count": 30, "id": "34524e64", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import sys\n", "\n", "from everstage_ddd.llm_agent.cpq_agent.services import process_gong_call_chunk, process_hubspot_data\n", "from everstage_ddd.llm_agent.cpq_agent.graph_rag_utils import index_files\n", "import uuid\n", "from everstage_ddd.llm_agent.cpq_agent.utility import EntityType, add_graph_rag_details_to_typesense, add_hubspot_email_graph_rag_details_to_typesense, add_hubspot_notes_graph_rag_details_to_typesense\n", "import boto3\n", "\n"]}, {"cell_type": "code", "execution_count": 31, "id": "6ad65f33", "metadata": {}, "outputs": [], "source": ["os.chdir('/Users/<USER>/everstage-spm/interstage_project')"]}, {"cell_type": "code", "execution_count": 32, "id": "89b9c7e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, set_key\n", "\n", "os.environ['AWS_REGION'] = 'AP-SOUTH-1'\n", "os.environ['S3_GONG_CALL_BUCKET'] = 'everstage-cpq-ai-assets'\n", "\n", "os.environ['OPENAI_API_KEY'] = '***************************************************'\n", "\n", "aws_access_key_id=\"********************\"\n", "aws_secret_access_key=\"JVBaUy5nFKgGnvvJFwFRm4xqkrXxMdbGyycOly1U\"\n", "aws_session_token=\"IQoJb3JpZ2luX2VjEC8aCXVzLXdlc3QtMiJHMEUCIQC7zOEaHBA/ynVWwsjw93DR8Pe2tw1FWHe5t8DU11sBvwIgbW+9mVC1OOO9z6lDXgEWJ15Wu+FEdZOUt5fcPqIupsEqrAMI5///////////ARADGgw5MTkyMTUzODY4MDEiDAzl1B1F5/fkGe9H8yqAA/5ie41PtKVQs/cBYzAGr58yOSoJbKa3iG/3SNVaplJCcN/UnRlNewDaqVOa45rRRFtOXjtrf4bCcKYKhdudpKB9kU84mp+XhinDfB5ZffSd4YPyxZFinQx74ysGQzoYe5zwMfuRlBmUomFdTN8FP5obqX659fP5n8fsnUfr20HP4i0w0XIlqeGftFc6i66HOcRWpKTDng9JGG3zQkTzKDCd6As4lE6Smxe8+i0AaQt3Ux9a6WKqoUx/wwo3gX1GLQdsgGUCqyKQrsFsNwTBrVK8HNZdTK9Jori/TDnOVReRGofl+IRd8IO++mLMPuNfxsgTAnt6PVGuPwDqRB+KveHz9A2CWIZbvMQI93ejRTqE8A7jiAZzcp26WDJgagqWjQiM9wxC5dDNO9sa1oSSLciumRfSEyxkR5MX1vGb9hQtlMYYkLH66xSR1afUqARrD91urefRq8KRKS1herJyA+StLvSyo56VMr8IQAMqYSPlfWh7caYBKo/bgCWoUjevTDCgp8DBBjqmAQ4f8FS2BZYQJws50haTAukS+JnrGyXVsJupnYZOJvDQfxbyzloY3BPokuaATmFFwzMrkVaoNrrSMv8I6WG2UCwAjEfsIn0MVgHZezWArgQec+yT+gydXJBwreYlgUaHDpATShhgJ33VwZLT2qFW0413nx/OHsF0xE2dOt80kPYPp69TOo4HJGM0I3Y9mIDZyU5DUo/izGgUY6m3JQGNIOJk0PDyaAo=\"\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_ACCESS_KEY_ID'] = aws_access_key_id\n", "os.environ['AWS_SECRET_ACCESS_KEY'] = aws_secret_access_key\n", "os.environ['AWS_SESSION_TOKEN'] = aws_session_token\n", "\n", "# # Update .env file directly\n", "set_key('.env', 'AWS_ACCESS_KEY_ID', aws_access_key_id)\n", "set_key('.env', 'AWS_SECRET_ACCESS_KEY', aws_secret_access_key)\n", "set_key('.env', 'AWS_SESSION_TOKEN', aws_session_token)\n", "set_key('.env', 'AWS_DEFAULT_REGION', 'ap-south-1')\n", "\n", "# # Reload environment\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 33, "id": "92e12a45", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['AWS_REGION'] = 'ap-south-1'\n", "os.environ['AWS_DEFAULT_REGION'] = 'ap-south-1'"]}, {"cell_type": "code", "execution_count": null, "id": "65ffbc1b", "metadata": {}, "outputs": [], "source": ["export AWS_ACCESS_KEY_ID=\"********************\"\n", "export AWS_SECRET_ACCESS_KEY=\"IeYHUmiXWoJSzPHhV8pOUL7SIHe/SVvJ4KAWrhns\"\n", "export AWS_SESSION_TOKEN=\"IQoJb3JpZ2luX2VjEOr//////////wEaCXVzLXdlc3QtMiJGMEQCIGNiJNj2pqFy/sTWAOoFUAT0rG3HBR91Xt6rUUSmdm4VAiA9WWZub5nPrqSJm6OsreWV14Ac0UL+XZ5seEQLDs50/CqsAwij//////////8BEAMaDDkxOTIxNTM4NjgwMSIMPmNjULjl5KsEdy3uKoADaTXKDgiTgsigIPIGIO10dznztdPdHnt9skyuhHE5jq5Rsz7qbEzXIzN0tJO4t1WIG0svpHLOO86bb3YECvQUpYoKKuzvn6pTfpJceyubMJ8bvQJLDY8Es0deYE0/Wvx1Sk/10AB4HF24Dn2UhG2MYI2q2xNhminpFkoxQPQWFqcjt38HqognyT6VN4NlA1EWiEOaqhR3ztJ4JnC4Y+InLy/hvGkV52bDOluOYi+zMCYm5hm1o40kATn1wYQwixWbNULOd7JQjjFtuUpjhyLumhIU6+P3+YqCEFc4BUhZiWjnmgTasMnd85mIWAVoeRw/3xyjbWjYAnEweK1F4Ua6GASRpeHN2PzyxkKKN9AEGGQURZFqu1+e/AEXF1SzCWGVzreaL6mg5T4tjUhH7k/nnKS98zuTBKvn4w5J0hGFpMibWng4q8eADsZf6c8LDOMYWovdaeAC5g6ECcQyS6YR6mwaRHA+9ImUIEc6eEvjVKFRv/8V/vXGI1se35HHBGkuMI+kscEGOqcBLqzMwHgHih9Uv+8Ktn+wH6PR9p9rNrZaOvnRRurLE5n5TdUSQlWCyatGCN3jDIiypukhzdotfwl+nbKJgwU/OX1U2uaLzxR4lStNh2eI/NGnm3HQLvNyN32vW+ROqYDIV041uiwrSGKr7kCLKf0Yy9xC3VnNYfuTHCuHvwDug0sW7hHrusamKyAZS17UpDEyPsV7RsXjqTpaA1WnGdyxtsIveh7YNJs=\""]}, {"cell_type": "code", "execution_count": 5, "id": "2cbdd2ed", "metadata": {}, "outputs": [], "source": ["chunks= [\n", "    {\n", "      \"speakerId\": \"6432345678555530055\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Initial Deal Discussion\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 460230,\n", "          \"end\": 462343,\n", "          \"text\": \"<PERSON> <PERSON>, thanks for taking the call. As discussed in our previous email, I'm here to walk you through our initial offer for the 100 Toyota Supra GR units. The base price we're looking at is $45,000 per unit, which brings the total to $4.5 million. We're proposing a delivery schedule of 25 units every 4 weeks, completing the full order in 16 weeks. We're also including the premium package, extended warranty, and dealer training in this offer. What are your initial thoughts on these terms?;;\\n\\nI understand your concern about the delivery timeline. The 16-week schedule is based on our current production capacity and quality control requirements. However, I can discuss with our production team about potential acceleration, though it might impact the price point. The premium package includes the 19-inch forged aluminum wheels, adaptive suspension, and the premium JBL audio system. Would you like me to break down the specific features included in the package?\"\n", "        }\n", "      ]\n", "    },\n", "    {\n", "      \"speakerId\": \"6432345678555530057\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Counter Offer Discussion\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 463230,\n", "          \"end\": 464343,\n", "          \"text\": \"Thanks for the detailed breakdown, <PERSON>. After reviewing with our team, we have some concerns. The 16-week delivery timeline is too long for our needs. We need all units within 8 weeks to meet our Q2 targets. We're willing to increase the upfront payment to 40% if you can accelerate the delivery. Also, we're looking at a price point of $42,000 per unit, which would bring the total to $4.2 million. We also need 50% of the units in Absolute Zero White, and we'd like the warranty extended to 7 years or 100,000 miles. Can you work with these terms?;;\\n\\nI understand the production constraints, but our dealership network has specific timing requirements. The 8-week delivery is crucial for our summer sales campaign. We're also concerned about the color distribution. Our market research shows strong demand for the white color option. The extended warranty is non-negotiable as it's a key selling point for our customers. Can we discuss these points further?\"\n", "        }\n", "      ]\n", "    },\n", "    {\n", "      \"speakerId\": \"6432345678555530055\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Final Terms Negotiation\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 465230,\n", "          \"end\": 466343,\n", "          \"text\": \"<PERSON>, I've discussed your requirements with our production and finance teams. Unfortunately, the 8-week delivery timeline is not feasible without compromising quality. However, we can propose a compromise: $43,500 per unit, with a modified delivery schedule of 40 units in 6 weeks, 30 units in 10 weeks, and the final 30 units in 14 weeks. We can offer 40% in Absolute Zero White, with the remaining units distributed across other colors. The warranty can be extended to 6 years or 75,000 miles, which is still above industry standard. How does this sound?;;\\n\\nI need to emphasize that the delivery timeline is firm due to our production constraints. We've already optimized the schedule as much as possible. The price of $43,500 is our best possible offer given the current market conditions and the premium features included. We can include the first-year maintenance package and 18 months of priority parts ordering to sweeten the deal. Would you like me to send over the detailed terms in writing?\"\n", "        }\n", "      ]\n", "    },\n", "    {\n", "      \"speakerId\": \"6432345678555530057\",\n", "      \"speaker_name\": \"<PERSON>\",\n", "      \"topic\": \"Deal Finalization\",\n", "      \"sentences\": [\n", "        {\n", "          \"start\": 467230,\n", "          \"end\": 468343,\n", "          \"text\": \"The modified delivery schedule is still longer than we'd like, but I understand the production constraints. The $43,500 price point is acceptable, and the 40% white color distribution works for us. However, we need to discuss the payment terms. The 35% upfront payment is higher than our standard practice. Could we structure it as 30% upfront, with the remaining 70% split across the delivery milestones? Also, can you confirm the exact specifications of the maintenance package?;;\\n\\nYes, I agree that the warranty terms are reasonable. The 6-year/75,000-mile coverage is competitive. I'll need to present these terms to our board next week. Can you hold this offer for 7 days? Also, could you provide more details about the dealer training program? We'd like to ensure our technicians are fully prepared for the new models.\"\n", "        }\n", "      ]\n", "    }\n", "  ]\n"]}, {"cell_type": "code", "execution_count": 5, "id": "227c3a11", "metadata": {}, "outputs": [], "source": ["client_id = 10039\n", "opportunity_id = 'lambda_2.3'\n", "call_date = '2025-04-07'\n", "is_terminal_call = False\n", "is_first_chunk_of_call = False\n", "gong_call_id = uuid.uuid4()"]}, {"cell_type": "code", "execution_count": 7, "id": "7fc10cd9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:botocore.credentials:Found credentials in environment variables.\n", "[2025-05-19 11:17:26 utility.py 324 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-19 11:17:26 utility.py 324 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-19 11:17:26 utility.py 324 INFO MainProcess]: File doesn't exist, creating it\n"]}], "source": ["process_gong_call_chunk(client_id=client_id, opportunity_id=opportunity_id, gong_call_id=gong_call_id, \n", "                        chunk_data=chunks[0], call_date=call_date, is_terminal_call=False, is_first_chunk_of_call=True)\n", "\n", "process_gong_call_chunk(client_id=client_id, opportunity_id=opportunity_id, gong_call_id=gong_call_id, \n", "                        chunk_data=chunks[1], call_date=call_date, is_terminal_call=False, is_first_chunk_of_call=False)\n", "\n", "process_gong_call_chunk(client_id=client_id, opportunity_id=opportunity_id, gong_call_id=gong_call_id, \n", "                        chunk_data=chunks[2], call_date=call_date, is_terminal_call=False, is_first_chunk_of_call=False)\n", "\n", "graph_rag_call_folder_path, graph_rag_call_file_path=process_gong_call_chunk(client_id=client_id, opportunity_id=opportunity_id, gong_call_id=gong_call_id, \n", "                        chunk_data=chunks[3], call_date=call_date, is_terminal_call=True, is_first_chunk_of_call=False)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "de6beaa9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["is_local_env: False\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-19 11:27:04 graph_rag_utils.py 23 INFO MainProcess]: Invoking the lambda function for graph RAG indexing\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Payload: {'s3_path': 'gong_calls/graph_rag/Sidd_test_10039/lambda_2.3', 'task_type': 'index', 'input_type': 'gong_call'}\n", "Response: {'status_code': 200, 'body': 'Success', 'graph_rag_indexed_parquet_path': '/mnt/efs/output/gong_calls/graph_rag/Sidd_test_10039/lambda_2.3'}\n", "indexing done\n"]}], "source": ["graph_rag_indexed_parquet_path = index_files(s3_path=graph_rag_call_folder_path, input_type=EntityType.GONG_CALL.value)\n", "print(\"indexing done\")\n", "add_graph_rag_details_to_typesense(\n", "    client_id=client_id,\n", "    opportunity_id=opportunity_id,\n", "    graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a551c0d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ec5e20f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 38, "id": "5c08c4ae", "metadata": {}, "outputs": [], "source": ["email_chunk_data = {\n", "    \"hs_email_from_email\": \"<EMAIL>\",\n", "    \"hs_email_to_email\": \"<EMAIL>\",\n", "    \"hs_body_preview\": \"\"\"\n", "    Subject: Toyota Supra Bulk Order - 100 Units - Special Pricing & Terms\n", "    \n", "    <PERSON>,\n", "    \n", "    I hope this email finds you well. I'm following up on our detailed discussion about the bulk order of Toyota Supras for your dealership network. \n", "    After reviewing your requirements and consulting with our regional management, we're pleased to offer you the following comprehensive package:\n", "    \n", "    Order Details:\n", "    - Total Units: 100 Toyota Supra GR 2024\n", "    - Base Price per Unit: $45,000 (Special Bulk Rate)\n", "    - Total Order Value: $4,500,000\n", "    - Payment Terms: 30% upfront, 70% upon delivery of each batch\n", "    \n", "    Vehicle Specifications:\n", "    - Model: 2024 Toyota Supra GR\n", "    - Engine: 3.0L Turbocharged Inline-6\n", "    - Power: 382 HP @ 5,800-6,500 rpm\n", "    - Torque: 368 lb-ft @ 1,800-5,000 rpm\n", "    - Transmission: 8-Speed Automatic with paddle shifters\n", "    - 0-60 mph: 4.1 seconds\n", "    - Top Speed: 155 mph (electronically limited)\n", "    \n", "    Standard Features:\n", "    - Premium Package (included)\n", "    - 19-inch forged aluminum wheels\n", "    - Adaptive Variable Suspension\n", "    - Brembo® performance brakes\n", "    - Active Differential\n", "    - Heads-up <PERSON><PERSON><PERSON>\n", "    - Premium JBL® Audio System\n", "    - Wireless Apple CarPlay® and Android Auto™\n", "    \n", "    Delivery Schedule:\n", "    - Batch 1 (25 units): 4 weeks from order confirmation\n", "    - Batch 2 (25 units): 8 weeks from order confirmation\n", "    - Batch 3 (25 units): 12 weeks from order confirmation\n", "    - Batch 4 (25 units): 16 weeks from order confirmation\n", "    \n", "    Additional Benefits:\n", "    - Free dealer training for 5 technicians\n", "    - Extended warranty coverage (5 years/60,000 miles)\n", "    - Priority parts ordering for 12 months\n", "    - Marketing support package worth $50,000\n", "    - Exclusive launch event for your dealership\n", "    \n", "    Color Options Available:\n", "    - Absolute Zero White\n", "    - Renaissance Red 2.0\n", "    - Nitro Yellow\n", "    - Nocturnal Black\n", "    - <PERSON>ngsten <PERSON>\n", "    \n", "    We're also offering:\n", "    - Free transportation to your dealerships\n", "    - Pre-delivery inspection for all units\n", "    - Custom dealership badging option\n", "    - First-year maintenance package\n", "    \n", "    Please review these terms and let me know if you'd like to:\n", "    1. Proceed with the order\n", "    2. Schedule a test drive for your management team\n", "    3. Discuss financing options\n", "    4. Review the marketing support package\n", "    \n", "    I'm available for a call tomorrow between 10 AM and 4 PM EST to discuss any details.\n", "    \n", "    Best regards,\n", "    <PERSON>\n", "    Senior Sales Manager\n", "    Toyota North America\n", "    Direct: (555) 123-4567\n", "    Email: <EMAIL>\n", "    \"\"\",\n", "    \"hs_createdate\": \"2024-03-15\"\n", "}\n", "\n", "notes_chunk_data_1 = {\n", "    \"id\": '001',\n", "    \"properties\": {\n", "        \"hs_created_by_user_id\": \"12345\",\n", "        \"hs_createdate\": \"2024-03-15\",\n", "        \"hs_body_preview\": \"\"\"\n", "    Deal Update: Toyota Supra Bulk Order - Comprehensive Review\n", "    \n", "    Follow-up call with <PERSON> (Purchasing Director) from Dealership Inc. regarding the 100-unit Supra order.\n", "    \n", "    Meeting Participants:\n", "    - <PERSON> (Purchasing Director)\n", "    - <PERSON> (Finance Manager)\n", "    - <PERSON> (Sales Operations)\n", "    - Our Team: <PERSON>, <PERSON>\n", "    \n", "    Key Discussion Points:\n", "    1. Order Specifications\n", "       - Confirmed interest in the 2024 GR model\n", "       - Discussed color distribution preferences\n", "       - Agreed on standard features package\n", "       - Reviewed technical specifications in detail\n", "    \n", "    2. Financial Discussion\n", "       - Payment terms negotiation\n", "       - Bulk discount confirmation\n", "       - Financing options through Toyota Financial Services\n", "       - Tax implications for multi-state delivery\n", "    \n", "    3. Delivery & Logistics\n", "       - Agreed on phased delivery schedule\n", "       - Discussed transportation arrangements\n", "       - Confirmed PDI requirements\n", "       - Addressed storage solutions for incoming inventory\n", "    \n", "    4. Marketing & Support\n", "       - Reviewed marketing support package\n", "       - Discussed launch event planning\n", "       - Agreed on technician training schedule\n", "       - Confirmed parts ordering priority\n", "    \n", "    Deal Specifics:\n", "    - Total Units: 100\n", "    - Model: 2024 Toyota Supra GR\n", "    - Base Price: $45,000/unit\n", "    - Total Value: $4,500,000\n", "    - Payment Terms: 30/70 split\n", "    - Expected ROI: 22% within first year\n", "    \n", "    Next Steps:\n", "    1. Immediate Actions\n", "       - Send formal quote (by EOD today)\n", "       - Schedule contract review (March 18)\n", "       - Arrange test drive for management team (March 20)\n", "       - Prepare financing documentation\n", "\n", "    \"\"\"\n", "    }\n", "}\n", "\n", "followup_email_chunk_data = {\n", "    \"hs_email_from_email\": \"<EMAIL>\",\n", "    \"hs_email_to_email\": \"<EMAIL>\",\n", "    \"hs_body_preview\": \"\"\"\n", "    Subject: Revised Terms for Toyota Supra Bulk Order - Updated Allocation & Pricing\n", "    \n", "    <PERSON>,\n", "    \n", "    Thank you for your patience while we completed our internal reviews. I wanted to provide you with an important update regarding the Toyota Supra bulk order we discussed yesterday.\n", "    \n", "    Due to an unexpected production adjustment at our manufacturing facility, we’ve had to revise both the **allocation volume** and **pricing structure**. Please find the updated offer details below:\n", "    \n", "    Revised Order Details:\n", "    - Total Units: 80 Toyota Supra GR 2024 (down from 100)\n", "    - Base Price per Unit: $47,500 (adjusted due to limited inventory)\n", "    - Total Order Value: $3,800,000\n", "    - Payment Terms: 50% upfront, 50% upon delivery of each batch (was 30%/70%)\n", "    \n", "    Updated Vehicle Specifications:\n", "    - Model: 2024 Toyota Supra GR (no changes)\n", "    - Engine: 3.0L Turbocharged Inline-6\n", "    - Power: 382 HP @ 5,800-6,500 rpm\n", "    - Torque: 368 lb-ft @ 1,800-5,000 rpm\n", "    - Transmission: 8-Speed Automatic with paddle shifters\n", "    - 0-60 mph: 4.1 seconds\n", "    - Top Speed: 155 mph\n", "    \n", "    Delivery Timeline (Compressed Schedule):\n", "    - Batch 1 (20 units): 6 weeks from confirmation\n", "    - Batch 2 (20 units): 10 weeks from confirmation\n", "    - Batch 3 (20 units): 14 weeks from confirmation\n", "    - Batch 4 (20 units): 18 weeks from confirmation\n", "    \n", "    Note: Production constraints have added 2 weeks to each delivery window.\n", "    \n", "    Benefit Adjustments:\n", "    - Dealer technician training now limited to 3 participants (was 5)\n", "    - Marketing support package revised to $25,000 (was $50,000)\n", "    - Priority parts ordering period reduced to 6 months (was 12 months)\n", "    - Extended warranty reduced to 3 years/36,000 miles (was 5 years/60,000 miles)\n", "    \n", "    Removed:\n", "    - Free transportation (now billed at standard Toyota logistics rate)\n", "    - Custom dealership badging (temporarily unavailable)\n", "    - First-year maintenance package (removed due to cost restructuring)\n", "    \n", "    Color Options (No Change):\n", "    - Absolute Zero White\n", "    - Renaissance Red 2.0\n", "    - Nitro Yellow\n", "    - Nocturnal Black\n", "    - <PERSON>ngsten <PERSON>\n", "    \n", "    I realize these changes may impact your planning, and I sincerely apologize for the shift. I’d be happy to work with you to find any additional accommodations or support we can provide.\n", "    \n", "    Please let me know if you'd like to:\n", "    1. Proceed with the updated offer\n", "    2. Revisit your unit requirement\n", "    3. Discuss other Toyota models available for bulk procurement\n", "    4. Set up a call to align on next steps\n", "    \n", "    I’m available tomorrow between 11 AM and 3 PM EST.\n", "    \n", "    Best regards,  \n", "    <PERSON>  \n", "    Senior Sales Manager  \n", "    Toyota North America  \n", "    Direct: (555) 123-4567  \n", "    Email: <EMAIL>\n", "    \"\"\",\n", "    \"hs_createdate\": \"2024-03-16\"\n", "}\n", "\n", "notes_chunk_data_2 = {\n", "    \"id\": '001',\n", "    \"properties\": {\n", "        \"hs_created_by_user_id\": \"12345\",\n", "        \"hs_createdate\": \"2024-03-15\",\n", "        \"hs_body_preview\": \"\"\"\n", "    2. Short-term (1-2 weeks)\n", "       - Finalize color distribution\n", "       - Confirm delivery schedule\n", "       - Set up dealer training\n", "       - Begin marketing material preparation\n", "    \n", "    3. Medium-term (1 month)\n", "       - Complete contract signing\n", "       - Process initial payment\n", "       - Begin production allocation\n", "       - Schedule launch event\n", "    \n", "    Risk Assessment:\n", "    - Market demand confirmed through regional analysis\n", "    - Supply chain stability verified\n", "    - Competitor activity monitored\n", "    - Price point validated against market\n", "    \n", "    Deal value: $4.5M\n", "    Expected close date: April 15, 2024\n", "    Probability of close: 85%\n", "    Key decision maker: <PERSON>\n", "    Next follow-up: March 18, 2024\n", "    \n", "    Notes:\n", "    - <PERSON> expressed particular interest in the marketing support\n", "    - <PERSON> needs additional financing details\n", "    - David to coordinate test drive schedule\n", "    - All parties agreed on the delivery timeline\n", "    \"\"\"\n", "    }\n", "}\n", "\n", "notes_chunk_data_3 = {\n", "    \"id\": '002',\n", "    \"properties\": {\n", "        \"hs_created_by_user_id\": \"12345\",\n", "        \"hs_createdate\": \"2024-03-18\",\n", "        \"hs_body_preview\": \"\"\"\n", "    Follow-Up Actions:\n", "    - Color distribution revised (Nitro Yellow reduced, new request for Phantom Gray)\n", "    - Delivery schedule adjustments required due to batch constraints (add 2 weeks to each)\n", "    - Dealer training rescheduled to mid-April to accommodate staff availability\n", "    - Marketing material draft reviewed; awaiting legal sign-off\n", "    \n", "    Contracting & Payment:\n", "    - Contract draft sent to legal on March 17\n", "    - Initial payment terms under negotiation (client hesitant on 50% upfront)\n", "    - Production allocation on temporary hold pending internal approvals\n", "    \n", "    Risks & Mitigations:\n", "    - Inventory pressure from West Coast demand spike (monitor allocations)\n", "    - Financing approval from <PERSON>’s team still pending (ETA: March 22)\n", "    - Minor delay expected in launch event due to venue change\n", "    \n", "    Updated Deal Parameters:\n", "    - Deal value remains $4.5M\n", "    - Probability of close revised to 75% due to payment term concerns\n", "    - New expected close date: April 22, 2024\n", "    - Key decision maker: <PERSON> (confirmed), with <PERSON> now co-signatory\n", "    \n", "    Next Steps:\n", "    - Legal to finalize contract terms by March 20\n", "    - Sales to renegotiate delivery and payment schedule by March 21\n", "    - David to confirm revised test drive schedule by March 19\n", "    - Marketing to present updated support plan draft by March 22\n", "    \n", "    Notes:\n", "    - Mike flagged concern on unit pricing after recent changes\n", "    - <PERSON> requested breakdown of revised training scope\n", "    - <PERSON> advised we retain original warranty terms if possible to maintain trust\n", "    - <PERSON> noted that dealership launch timing may slip to early May\n", "    \"\"\"\n", "    }\n", "}\n"]}, {"cell_type": "code", "execution_count": 9, "id": "21164452", "metadata": {}, "outputs": [], "source": ["client_id=10039\n", "opportunity_id=\"lambda_2.4\""]}, {"cell_type": "code", "execution_count": 35, "id": "9402e47a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 11:59:54 utility.py 449 INFO MainProcess]: Uploaded chunk to emails/single_kb/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 11:59:55 utility.py 497 INFO MainProcess]: Uploaded chunk to emails/graph_rag/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 11:59:55 utility.py 543 INFO MainProcess]: Uploaded chunk to emails/long_context/Sidd_test_10039/upstream_sim_3.1/no_chunk_emails/120596c2-1ad0-4e6c-b6b1-c3da26f0790f.txt\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 12:00:19 aws_utils.py 630 INFO MainProcess]: Synced data source MFCKH4NERV to knowledge base VMXZSIJ44E\n", "[2025-05-23 12:00:22 utility.py 449 INFO MainProcess]: Uploaded chunk to emails/single_kb/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:00:23 utility.py 497 INFO MainProcess]: Uploaded chunk to emails/graph_rag/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:00:23 utility.py 543 INFO MainProcess]: Uploaded chunk to emails/long_context/Sidd_test_10039/upstream_sim_3.1/no_chunk_emails/0e565b7e-8670-4e02-be28-3b822e47c0a6.txt\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 12:00:46 aws_utils.py 630 INFO MainProcess]: Synced data source MFCKH4NERV to knowledge base VMXZSIJ44E\n"]}], "source": ["process_hubspot_data(client_id=10039, opportunity_id=\"upstream_sim_3.1\", \n", "                     chunk_data=email_chunk_data, is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_EMAIL.value)\n", "\n", "\n", "process_hubspot_data(client_id=10039, opportunity_id=\"upstream_sim_3.1\", \n", "                     chunk_data=followup_email_chunk_data, is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_EMAIL.value)\n", "\n", "\n", "# graph_rag_indexed_parquet_path = index_files(s3_path=chunk_s3_graph_rag_path, input_type=EntityType.HUBSPOT_EMAIL.value)\n", "# add_hubspot_email_graph_rag_details_to_typesense(\n", "#     client_id=client_id,\n", "#     opportunity_id=opportunity_id,\n", "#     graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,\n", "# )"]}, {"cell_type": "code", "execution_count": 39, "id": "afa1092b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 12:02:11 utility.py 473 INFO MainProcess]: Uploaded chunk to notes/single_kb/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:02:12 utility.py 521 INFO MainProcess]: Uploaded chunk to notes/graph_rag/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:02:13 utility.py 568 INFO MainProcess]: Uploaded chunk to notes/long_context/Sidd_test_10039/upstream_sim_3.1/no_chunk_notes/001.txt\n", "[2025-05-23 12:02:13 utility.py 473 INFO MainProcess]: Uploaded chunk to notes/single_kb/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:02:14 utility.py 521 INFO MainProcess]: Uploaded chunk to notes/graph_rag/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:02:14 utility.py 568 INFO MainProcess]: Uploaded chunk to notes/long_context/Sidd_test_10039/upstream_sim_3.1/no_chunk_notes/001.txt\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 12:02:38 aws_utils.py 630 INFO MainProcess]: Synced data source 9AMCOGDYXL to knowledge base LF6RKRUK4R\n", "[2025-05-23 12:02:39 utility.py 876 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 12:02:41 utility.py 464 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 12:02:41 utility.py 473 INFO MainProcess]: Uploaded chunk to notes/single_kb/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:02:42 utility.py 512 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 12:02:42 utility.py 521 INFO MainProcess]: Uploaded chunk to notes/graph_rag/Sidd_test_10039/upstream_sim_3.1\n", "[2025-05-23 12:02:42 utility.py 554 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-23 12:02:42 utility.py 568 INFO MainProcess]: Uploaded chunk to notes/long_context/Sidd_test_10039/upstream_sim_3.1/no_chunk_notes/002.txt\n"]}, {"name": "stdout", "output_type": "stream", "text": ["..........\r"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-05-23 12:03:05 aws_utils.py 630 INFO MainProcess]: Synced data source 9AMCOGDYXL to knowledge base LF6RKRUK4R\n"]}], "source": ["process_hubspot_data(client_id=10039, opportunity_id=\"upstream_sim_3.1\", \n", "                     chunk_data=notes_chunk_data_1, is_terminal_chunk=False, entity_type=EntityType.HUBSPOT_NOTES.value)\n", "process_hubspot_data(client_id=10039, opportunity_id=\"upstream_sim_3.1\", \n", "                     chunk_data=notes_chunk_data_2, is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_NOTES.value)\n", "\n", "\n", "\n", "process_hubspot_data(client_id=10039, opportunity_id=\"upstream_sim_3.1\", \n", "                     chunk_data=notes_chunk_data_3, is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_NOTES.value)\n", "\n", "\n", "\n", "# graph_rag_indexed_parquet_path = index_files(s3_path=chunk_s3_graph_rag_path, input_type=EntityType.HUBSPOT_NOTES.value)\n", "# add_hubspot_notes_graph_rag_details_to_typesense(\n", "#     client_id=client_id,\n", "#     opportunity_id=opportunity_id,\n", "#     graph_rag_indexed_parquet_path=graph_rag_indexed_parquet_path,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "6d626e7b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56a4abeb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "19adc134", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dc6eb72c", "metadata": {}, "outputs": [], "source": ["##llm rag testing for hubspot"]}, {"cell_type": "code", "execution_count": 13, "id": "32d2957a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-05-20 11:05:40 utility.py 431 INFO MainProcess]: Uploaded chunk to emails/Sidd_test_10039/lambda_2.7\n", "[2025-05-20 11:05:40 utility.py 431 INFO MainProcess]: Uploaded chunk to emails/graph_rag/Sidd_test_10039/lambda_2.7\n", "[2025-05-20 11:05:41 utility.py 480 INFO MainProcess]: Uploaded chunk to emails/long_context/Sidd_test_10039/lambda_2.7/no_chunk_emails/11c118ab-5a45-4bb6-a508-6945232ba763.txt\n", "[2025-05-20 11:05:41 utility.py 824 INFO MainProcess]: File doesn't exist, creating it\n", "[2025-05-20 11:05:42 utility.py 431 INFO MainProcess]: Uploaded chunk to emails/Sidd_test_10039/lambda_2.7\n", "[2025-05-20 11:05:42 utility.py 431 INFO MainProcess]: Uploaded chunk to emails/graph_rag/Sidd_test_10039/lambda_2.7\n", "[2025-05-20 11:05:43 utility.py 480 INFO MainProcess]: Uploaded chunk to emails/long_context/Sidd_test_10039/lambda_2.7/no_chunk_emails/71a896b2-ab0f-4948-bb21-f0af92b51b79.txt\n"]}, {"ename": "RequestMalformed", "evalue": "[Errno 400] Could not find a filter field named `entity_type` in the schema.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRequestMalformed\u001b[0m                          <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[13], line 17\u001b[0m\n\u001b[1;32m     10\u001b[0m chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path \u001b[38;5;241m=\u001b[39m process_hubspot_data(client_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m10039\u001b[39m, \n\u001b[1;32m     11\u001b[0m                     opportunity_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlambda_2.7\u001b[39m\u001b[38;5;124m\"\u001b[39m, chunk_data\u001b[38;5;241m=\u001b[39mfollowup_email_chunk_data, \n\u001b[1;32m     12\u001b[0m                     is_terminal_chunk\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, entity_type\u001b[38;5;241m=\u001b[39mEntityType\u001b[38;5;241m.\u001b[39mHUBSPOT_EMAIL\u001b[38;5;241m.\u001b[39mvalue)\n\u001b[1;32m     14\u001b[0m attach_file_to_long_context_prompt_hubspot_entity(entity_file_path\u001b[38;5;241m=\u001b[39mchunk_s3_long_context_file_path, \n\u001b[1;32m     15\u001b[0m                                                   long_context_prompt_path\u001b[38;5;241m=\u001b[39mchunk_s3_long_context_prompt_path)\n\u001b[0;32m---> 17\u001b[0m \u001b[43madd_llm_rag_details_to_typesense\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     18\u001b[0m \u001b[43m            \u001b[49m\u001b[43mclient_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclient_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     19\u001b[0m \u001b[43m            \u001b[49m\u001b[43mopportunity_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mopportunity_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     20\u001b[0m \u001b[43m            \u001b[49m\u001b[43mentity_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mEntityType\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mHUBSPOT_EMAIL\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     21\u001b[0m \u001b[43m            \u001b[49m\u001b[43mlong_context_prompt_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunk_s3_long_context_prompt_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     22\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/everstage-spm/interstage_project/everstage_ddd/llm_agent/cpq_agent/utility.py:697\u001b[0m, in \u001b[0;36madd_llm_rag_details_to_typesense\u001b[0;34m(client_id, opportunity_id, entity_type, long_context_prompt_path)\u001b[0m\n\u001b[1;32m    689\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m is_collection_exists:\n\u001b[1;32m    690\u001b[0m     SearchEngine\u001b[38;5;241m.\u001b[39minstance()\u001b[38;5;241m.\u001b[39mcreate_collection(\n\u001b[1;32m    691\u001b[0m         {\n\u001b[1;32m    692\u001b[0m             \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mTYPESENSE_METADATA_COLLECTION_SCHEMA_LLM_RAG,\n\u001b[1;32m    693\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mname\u001b[39m\u001b[38;5;124m\"\u001b[39m: TYPESENSE_COLLECTION_NAME_LLM_RAG,\n\u001b[1;32m    694\u001b[0m         }\n\u001b[1;32m    695\u001b[0m     )\n\u001b[0;32m--> 697\u001b[0m collection \u001b[38;5;241m=\u001b[39m \u001b[43mSearchEngine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minstance\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msearch\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    698\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcollection\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mTYPESENSE_COLLECTION_NAME_LLM_RAG\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    699\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\n\u001b[1;32m    700\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mq\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m*\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    701\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfilter_by\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mclient_id:=\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mclient_id\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m && opportunity_id:=\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mopportunity_id\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m && entity_type:=\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mentity_type\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    702\u001b[0m \u001b[43m    \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    703\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    704\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(collection[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhits\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    705\u001b[0m     SearchEngine\u001b[38;5;241m.\u001b[39minstance()\u001b[38;5;241m.\u001b[39mupsert_document(\n\u001b[1;32m    706\u001b[0m         TYPESENSE_COLLECTION_NAME_LLM_RAG, document\n\u001b[1;32m    707\u001b[0m     )\n", "File \u001b[0;32m~/everstage-spm/interstage_project/everstage_ddd/global_search/services/proxies.py:90\u001b[0m, in \u001b[0;36mSearchEngine.search\u001b[0;34m(self, collection, params)\u001b[0m\n\u001b[1;32m     89\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msearch\u001b[39m(\u001b[38;5;28mself\u001b[39m, collection: \u001b[38;5;28mstr\u001b[39m, params: \u001b[38;5;28mdict\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mdict\u001b[39m:\n\u001b[0;32m---> 90\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcollections\u001b[49m\u001b[43m[\u001b[49m\u001b[43mcollection\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdocuments\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msearch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/typesense/documents.py:104\u001b[0m, in \u001b[0;36mDocuments.search\u001b[0;34m(self, search_parameters)\u001b[0m\n\u001b[1;32m    102\u001b[0m stringified_search_params \u001b[38;5;241m=\u001b[39m stringify_search_params(search_parameters)\n\u001b[1;32m    103\u001b[0m validate_search(stringified_search_params)\n\u001b[0;32m--> 104\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapi_call\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_endpoint_path\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msearch\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstringified_search_params\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/typesense/api_call.py:146\u001b[0m, in \u001b[0;36mApiCall.get\u001b[0;34m(self, endpoint, params, as_json)\u001b[0m\n\u001b[1;32m    144\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget\u001b[39m(\u001b[38;5;28mself\u001b[39m, endpoint, params\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, as_json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[1;32m    145\u001b[0m     params \u001b[38;5;241m=\u001b[39m params \u001b[38;5;129;01mor\u001b[39;00m {}\n\u001b[0;32m--> 146\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmake_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mendpoint\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mas_json\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    147\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    148\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnection_timeout_seconds\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.venvs/everstage/lib/python3.10/site-packages/typesense/api_call.py:116\u001b[0m, in \u001b[0;36mApiCall.make_request\u001b[0;34m(self, fn, endpoint, as_json, **kwargs)\u001b[0m\n\u001b[1;32m    114\u001b[0m             error_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAPI error.\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m    115\u001b[0m         \u001b[38;5;66;03m# Raised exception will be caught and retried\u001b[39;00m\n\u001b[0;32m--> 116\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m ApiCall\u001b[38;5;241m.\u001b[39mget_exception(r\u001b[38;5;241m.\u001b[39mstatus_code)(r\u001b[38;5;241m.\u001b[39mstatus_code, error_message)\n\u001b[1;32m    118\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m r\u001b[38;5;241m.\u001b[39mjson() \u001b[38;5;28;01mif\u001b[39;00m as_json \u001b[38;5;28;01melse\u001b[39;00m r\u001b[38;5;241m.\u001b[39mtext\n\u001b[1;32m    119\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (requests\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mTimeout, requests\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mConnectionError, requests\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mHTTPError,\n\u001b[1;32m    120\u001b[0m         requests\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mRequestException, requests\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mSSLError,\n\u001b[1;32m    121\u001b[0m         HTTPStatus0Error, ServerError, ServiceUnavailable) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    122\u001b[0m     \u001b[38;5;66;03m# Catch the exception and retry\u001b[39;00m\n", "\u001b[0;31mRequestMalformed\u001b[0m: [Errno 400] Could not find a filter field named `entity_type` in the schema."]}], "source": ["from everstage_ddd.llm_agent.cpq_agent.utility import EntityType, attach_file_to_long_context_prompt_hubspot_entity, add_llm_rag_details_to_typesense\n", "\n", "chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path = process_hubspot_data(client_id=10039, \n", "                    opportunity_id=\"lambda_2.8\", chunk_data=email_chunk_data, \n", "                    is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_EMAIL.value)\n", "attach_file_to_long_context_prompt_hubspot_entity(entity_file_path=chunk_s3_long_context_file_path, \n", "                                                  long_context_prompt_path=chunk_s3_long_context_prompt_path)\n", "\n", "\n", "chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path = process_hubspot_data(client_id=10039, \n", "                    opportunity_id=\"lambda_2.8\", chunk_data=followup_email_chunk_data, \n", "                    is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_EMAIL.value)\n", "\n", "attach_file_to_long_context_prompt_hubspot_entity(entity_file_path=chunk_s3_long_context_file_path, \n", "                                                  long_context_prompt_path=chunk_s3_long_context_prompt_path)\n", "\n", "add_llm_rag_details_to_typesense(\n", "            client_id=client_id,\n", "            opportunity_id=opportunity_id,\n", "            entity_type=EntityType.HUBSPOT_EMAIL.value,\n", "            long_context_prompt_path=chunk_s3_long_context_prompt_path,\n", "        )"]}, {"cell_type": "code", "execution_count": 24, "id": "0f17cd5a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-05-20 11:41:25 utility.py 457 INFO MainProcess]: Uploaded chunk to notes/Sidd_test_10039/lambda_2.8\n", "[2025-05-20 11:41:26 utility.py 457 INFO MainProcess]: Uploaded chunk to notes/graph_rag/Sidd_test_10039/lambda_2.8\n", "[2025-05-20 11:41:27 utility.py 505 INFO MainProcess]: Uploaded chunk to notes/long_context/Sidd_test_10039/lambda_2.8/no_chunk_notes/001.txt\n", "[2025-05-20 11:41:28 utility.py 457 INFO MainProcess]: Uploaded chunk to notes/Sidd_test_10039/lambda_2.8\n", "[2025-05-20 11:41:29 utility.py 457 INFO MainProcess]: Uploaded chunk to notes/graph_rag/Sidd_test_10039/lambda_2.8\n", "[2025-05-20 11:41:30 utility.py 505 INFO MainProcess]: Uploaded chunk to notes/long_context/Sidd_test_10039/lambda_2.8/no_chunk_notes/001.txt\n", "[2025-05-20 11:41:31 utility.py 457 INFO MainProcess]: Uploaded chunk to notes/Sidd_test_10039/lambda_2.8\n", "[2025-05-20 11:41:31 utility.py 457 INFO MainProcess]: Uploaded chunk to notes/graph_rag/Sidd_test_10039/lambda_2.8\n", "[2025-05-20 11:41:32 utility.py 505 INFO MainProcess]: Uploaded chunk to notes/long_context/Sidd_test_10039/lambda_2.8/no_chunk_notes/002.txt\n"]}], "source": ["chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path = process_hubspot_data(client_id=10039, \n", "                    opportunity_id=\"lambda_2.8\", chunk_data=notes_chunk_data_1, \n", "                    is_terminal_chunk=False, entity_type=EntityType.HUBSPOT_NOTES.value)\n", "attach_file_to_long_context_prompt_hubspot_entity(entity_file_path=chunk_s3_long_context_file_path, \n", "                                                  long_context_prompt_path=chunk_s3_long_context_prompt_path)\n", "\n", "chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path= process_hubspot_data(client_id=10039,\n", "                    opportunity_id=\"lambda_2.8\", chunk_data=notes_chunk_data_2, \n", "                    is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_NOTES.value)\n", "\n", "attach_file_to_long_context_prompt_hubspot_entity(entity_file_path=chunk_s3_long_context_file_path, \n", "                                                  long_context_prompt_path=chunk_s3_long_context_prompt_path)\n", "\n", "chunk_s3_long_context_file_path, chunk_s3_long_context_prompt_path= process_hubspot_data(client_id=10039,\n", "                    opportunity_id=\"lambda_2.8\", chunk_data=notes_chunk_data_3, \n", "                    is_terminal_chunk=True, entity_type=EntityType.HUBSPOT_NOTES.value)\n", "attach_file_to_long_context_prompt_hubspot_entity(entity_file_path=chunk_s3_long_context_file_path, \n", "                                                  long_context_prompt_path=chunk_s3_long_context_prompt_path)\n", "\n", "add_llm_rag_details_to_typesense(\n", "            client_id=client_id,\n", "            opportunity_id=opportunity_id,\n", "            entity_type=EntityType.HUBSPOT_NOTES.value,\n", "            long_context_prompt_path=chunk_s3_long_context_prompt_path,\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}