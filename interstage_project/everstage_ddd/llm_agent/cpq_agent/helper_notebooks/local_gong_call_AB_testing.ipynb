{"cells": [{"cell_type": "code", "execution_count": 1, "id": "25ea588e", "metadata": {}, "outputs": [], "source": ["import django\n", "django.setup()"]}, {"cell_type": "code", "execution_count": null, "id": "640c1524", "metadata": {}, "outputs": [], "source": ["!pip install langchain_openai\n", "!pip install langchain-text-splitters"]}, {"cell_type": "code", "execution_count": 7, "id": "7c8a181b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["476\n"]}], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "# Load example document\n", "with open(\"/Users/<USER>/Desktop/gong_calls/Call-1.txt\") as f:\n", "    text = f.read()\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    # Set a really small chunk size, just to show.\n", "    chunk_size=100,\n", "    chunk_overlap=20,\n", "    length_function=len,\n", "    is_separator_regex=False,\n", ")\n", "texts = text_splitter.create_documents([text])\n", "print(len(texts))"]}, {"cell_type": "code", "execution_count": 43, "id": "d187cdcf", "metadata": {}, "outputs": [], "source": ["import os\n", "from everstage_ddd.global_search.services.proxies import SearchEngine"]}, {"cell_type": "code", "execution_count": 44, "id": "1eb6cc93", "metadata": {}, "outputs": [], "source": ["# input file paths and collection names\n", "file1 = '/Users/<USER>/Desktop/gong_calls/Call-1.txt'\n", "file2 = '/Users/<USER>/Desktop/gong_calls/Call-2.txt'\n", "file3 = '/Users/<USER>/Desktop/gong_calls/Call-3.txt'\n", "\n", "collection_name = 'test-gong-calls-connection'\n", "\n", "contextual_collection_name = 'test-gong-calls-connection-contextual-rag'\n"]}, {"cell_type": "code", "execution_count": 62, "id": "1b071722", "metadata": {}, "outputs": [], "source": ["# naive rag, upload entire doc as single typesense document\n", "\n", "# create a collection to store the 3 files\n", "collection_schema = {\n", "    \"name\": collection_name,\n", "    \"fields\": [\n", "        {\"name\": \"content\", \"type\": \"string\"},\n", "        {\n", "            \"name\": \"embedding\",\n", "            \"type\": \"float[]\",\n", "            \"embed\": {\n", "                \"from\": [\"content\"],\n", "                \"model_config\": {\n", "                    \"model_name\": \"openai/text-embedding-ada-002\",\n", "                    \"api_key\": os.getenv(\"OPENAI_API_KEY\", \"\"),\n", "                },\n", "            },\n", "        },\n", "    ],\n", "}\n", "\n", "# create a typesense collection\n", "SearchEngine.instance().create_collection(collection_schema)\n", "\n", "# add the contents of the 3 files to the document\n", "for file_name in [file2, file3]:\n", "    with open(file_name, 'r') as fd:\n", "        file_content = str(fd.readlines())\n", "        SearchEngine.instance().create_document(\n", "            collection=collection_name, document={\"content\": file_content}\n", "        )"]}, {"cell_type": "code", "execution_count": 5, "id": "25771cce", "metadata": {}, "outputs": [], "source": ["# contextual RAG -> upload semantic chunks of the document\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_experimental.text_splitter import SemanticChunker\n", "\n", "# create a collection to store the 3 files\n", "contextual_collection_name = 'test-gong-calls-connection-contextual-rag'\n", "collection_schema = {\n", "    \"name\": contextual_collection_name,\n", "    \"fields\": [\n", "        {\"name\": \"content\", \"type\": \"string\"},\n", "        {\n", "            \"name\": \"embedding\",\n", "            \"type\": \"float[]\",\n", "            \"embed\": {\n", "                \"from\": [\"content\"],\n", "                \"model_config\": {\n", "                    \"model_name\": \"openai/text-embedding-ada-002\",\n", "                    \"api_key\": os.getenv(\"OPENAI_API_KEY\", \"\"),\n", "                },\n", "            },\n", "        },\n", "    ],\n", "}\n", "\n", "# create a typesense collection\n", "SearchEngine.instance().create_collection(collection_schema)"]}, {"cell_type": "code", "execution_count": null, "id": "8fcad2aa", "metadata": {}, "outputs": [], "source": ["from langchain_openai.embeddings import OpenAIEmbeddings\n", "\n", "# normal semantic split\n", "text_splitter = SemanticChunker(OpenAIEmbeddings())\n", "contextual_collection_name = 'test-gong-calls-connection-contextual-rag'\n", "\n", "for file_name in [file2, file3]:\n", "    docs = []\n", "    with open(file_name, 'r') as fd:\n", "        for line in fd.readlines():\n", "            docs.append(line)\n", "            \n", "    docs_normal_split = text_splitter.create_documents(docs)\n", "    for split in docs_normal_split:\n", "        text = split.page_content\n", "        SearchEngine.instance().create_document(\n", "            collection=contextual_collection_name, document={\"content\": text}\n", "        )\n", "\n", "# # percentile semantic chunking\n", "# text_splitter_percentile = SemanticChunker(\n", "#     OpenAIEmbeddings(), breakpoint_threshold_type=\"percentile\"\n", "# )\n", "# docs_percentile = text_splitter_percentile.create_documents(doc_strings)\n", "\n", "\n", "# # standard deviation semantic chunking\n", "# text_splitter_deviation = SemanticChunker(\n", "#     OpenAIEmbeddings(), breakpoint_threshold_type=\"standard_deviation\"\n", "# )\n", "# docs_std = text_splitter_deviation.create_documents(doc_strings)\n"]}, {"cell_type": "code", "execution_count": 76, "id": "12f7af0a", "metadata": {}, "outputs": [], "source": ["# functions to fetch data from langchain RAG\n", "\n", "from os import getenv\n", "\n", "from typesense import Client\n", "from typesense import exceptions as typesense_exceptions\n", "\n", "\n", "def fetch_relevant_documents(\n", "    user_query: str, collection_names: list[str], k: int = 5, extra_filters=None\n", ") -> None:\n", "    typsense_client = TypsenseClient()\n", "    results = []\n", "    for collection_name in collection_names:\n", "        search_parameters = {\n", "            \"q\": user_query,\n", "            \"query_by\": \"embedding\",\n", "            \"vector_query\": f\"embedding:([], alpha: 0.5 ,distance_threshold:0.30, k:{k})\",\n", "            \"exclude_fields\": \"embedding\",\n", "        }\n", "        if extra_filters:\n", "            search_parameters = {**search_parameters, **extra_filters}\n", "        search_results = typsense_client.search(\n", "            collection=collection_name, search_parameters=search_parameters\n", "        )\n", "        current_results = []\n", "        for hit in search_results[\"hits\"]:\n", "            current_results.append(hit[\"document\"])\n", "        results.extend(current_results)\n", "    return results\n", "\n", "\n", "def fetch_relevant_documents_using_auto_embeddings(\n", "    user_query: str, collection_names: list[str], k: int = 5\n", "):\n", "    \"\"\"\n", "    this function is to be invokved when querying collections\n", "    that create auto embeddings for documents\n", "    \"\"\"\n", "\n", "    typsense_client = TypsenseClient()\n", "    results = []\n", "    for collection_name in collection_names:\n", "        search_parameters = {\n", "            \"q\": user_query,\n", "            \"query_by\": \"embedding\",\n", "        }\n", "        search_results = typsense_client.search(\n", "            collection=collection_name, search_parameters=search_parameters\n", "        )\n", "        current_results = []\n", "        for hit in search_results[\"hits\"]:\n", "            current_results.append(hit[\"document\"])\n", "        results.extend(current_results)\n", "    return results\n", "\n", "\n", "class TypsenseClient:\n", "    def __init__(self) -> None:\n", "        self.client = Client(\n", "            {\n", "                \"api_key\": getenv(\"TYPESENSE_API_KEY\"),\n", "                \"nodes\": [\n", "                    {\n", "                        \"host\": getenv(\"TYPESENSE_HOST\"),\n", "                        \"port\": getenv(\"TYPESENSE_PORT\"),\n", "                        \"protocol\": getenv(\"TYPESENSE_PROTOCOL\"),\n", "                    }\n", "                ],\n", "                \"connection_timeout_seconds\": int(\n", "                    getenv(\"TYPESENSE_CONNECTION_TIMEOUT\") or 2\n", "                ),\n", "            }\n", "        )\n", "\n", "    def is_collection_exists(self, collection_name: str) -> bool:\n", "        try:\n", "            # Attempt to retrieve the collection information\n", "            self.client.collections[collection_name].retrieve()\n", "            return True\n", "        except typesense_exceptions.ObjectNotFound:\n", "            return False\n", "        except typesense_exceptions.TypesenseClientError as e:\n", "            print(f\"Error: {e}\")\n", "            return False\n", "\n", "    def create_collection(self, schema: dict) -> dict:\n", "        return self.client.collections.create(schema)\n", "\n", "    def drop_collection(self, collection: str) -> dict:\n", "        return self.client.collections[collection].delete()\n", "\n", "    def create_document(self, collection: str, document) -> dict:\n", "        return self.client.collections[collection].documents.create(document)\n", "\n", "    def update_document(self, collection: str, document) -> dict:\n", "        return self.client.collections[collection].documents.update(document)\n", "\n", "    def upsert_documents(self, collection: str, **kwargs) -> dict:\n", "        return self.client.collections[collection].documents.import_(**kwargs)\n", "\n", "    def delete_documents(self, collection: str, **kwargs) -> dict:\n", "        return self.client.collections[collection].documents.delete(**kwargs)\n", "\n", "    def search(self, collection: str, search_parameters) -> dict:\n", "        return self.client.collections[collection].documents.search(search_parameters)\n", "    \n", "    \n", "def fetch_relevant_documents_using_auto_embeddings(\n", "    user_query: str, collection_names: list[str], k: int = 5\n", "):\n", "    \"\"\"\n", "    this function is to be invokved when querying collections\n", "    that create auto embeddings for documents\n", "    \"\"\"\n", "\n", "    typsense_client = TypsenseClient()\n", "    results = []\n", "    for collection_name in collection_names:\n", "        search_parameters = {\n", "            \"q\": user_query,\n", "            \"query_by\": \"embedding\",\n", "            \"prefix\" : False\n", "        }\n", "        search_results = typsense_client.search(\n", "            collection=collection_name, search_parameters=search_parameters\n", "        )\n", "        current_results = []\n", "        for hit in search_results[\"hits\"]:\n", "            current_results.append(hit[\"document\"][\"content\"])\n", "        results.extend(current_results)\n", "    return results\n"]}, {"cell_type": "code", "execution_count": null, "id": "e7ac728c", "metadata": {}, "outputs": [], "source": ["# aws knowledge base credentials\n", "\n", "knowledge_base_id = 'XYBEC6YIJS'\n", "knowledge_base_name = 'everstage-ai-test-gong-call-teramind-semantic-chunking'\n", "\n", "import boto3\n", "\n", "# Initialize the Boto3 client for Bedrock\n", "client = boto3.client('bedrock-agent-runtime', region_name='ap-south-1', aws_access_key_id='********************', aws_secret_access_key='c/6A6z0XFQmGFbrkYadnCxOGs8LfJb0hOktE2aK4', aws_session_token='IQoJb3JpZ2luX2VjEPf//////////wEaCXVzLXdlc3QtMiJHMEUCIEKnvUQ1TVdi21oywdVXKr8VAf3nUqL/TQPEUQ6K4/6CAiEAqqLXCAsD8/dC1OgUpxBWzbeh38vauHqSPOuM5xj4BNsqiAMIcBADGgw5MTkyMTUzODY4MDEiDJm3udptxttund+xlCrlAt5OINy0vXg1ypuoyY68BXKht2P8G/VstomNRTbzWsQ3xoqAADHdMalw+3nFGrWviww5porWaujc6BBfPfGgkj35eZVWAiJfaRtr3mZxZIBvdkdQREx6hut9V67Vvm/sJidOvKWKHzjbxgGNeI7N8wCrKHapps1Cio/JicfFWFpwnL/o+H5mESW5jZNLA7SankHDKyinqupUKEQ9yavzr9DaGsbxHG31QNORHXzbFc5nOjZOho/IdrKqJ6hjaKC+dewCLdvT3GSTjNCyzGTOyvEjz0cTx3K2DdJVJTdXfPyLdO8Eb6C8DyIsgjbxc0LnrisF8/g2boLdO9TerCkmLi5LeCXJNU6dFirMCiflPj35VK3SuBvPSyWEsRar7vCJcDjWdQRhxv9dzdVaKmgSNgjg8UmrchUDWAol/SWrU3/tTT6LUODKU8EVw4OTE9XCSGZRVjeYYoxX/h84+0gPeEHkzXLs+zCcq4e5BjqmAXCMBLp81JkZ+1OShGlH2WlPy+q9qnB99mY4f6Z8g1ZvScqRWHB/lWw+hQrhBPOeV5452zaJjgHhcAeijyWd2hUIiYekwaaMELvHLEK9soGvEMlzExAoYwZjrJVIZUdrIXXOp/XK1AkudmSBdmlcyuIU0Si40sF+8AjBTKyvHH/ihVZOYpKcf6hj6Sn2ufybOHTE9nKGcVw+peusg057UxFfLeZOE0c=')\n", "\n", "# Define your knowledge base ID and query\n", "query_text = 'What integrations are required for data flow between systems?'\n", "\n", "# Retrieve method\n", "def retrieve_from_kb(query_text):\n", "    global knowledge_base_id\n", "    response = client.retrieve(\n", "        knowledgeBaseId=knowledge_base_id,\n", "        retrievalQuery={'text': query_text},\n", "    )\n", "    response = response['retrievalResults']\n", "    result = list()\n", "    for answer in response:\n", "        result.append(answer['content']['text'])\n", "    return result\n"]}, {"cell_type": "code", "execution_count": 103, "id": "5c1fa001", "metadata": {}, "outputs": [], "source": ["# open ai chat requests and RAG benchmarking testing setup\n", "\n", "from openai import OpenAI\n", "import requests\n", "import json\n", "from typing import List, Dict, Optional\n", "import csv\n", "\n", "\n", "class OpenAIClient:\n", "    def __init__(self, api_key: str):\n", "        \"\"\"\n", "        Initialize OpenAI client with API key\n", "        \n", "        Parameters:\n", "        api_key (str): Your OpenAI API key\n", "        \"\"\"\n", "        self.api_key = api_key\n", "        openai.api_key = api_key\n", "        \n", "    def chat_completion(\n", "        self,\n", "        messages: List[Dict[str, str]],\n", "        model: str = \"gpt-4\",\n", "        temperature: float = 0.7,\n", "        max_tokens: Optional[int] = None\n", "    ) -> Dict:\n", "        \"\"\"\n", "        Make a chat completion API call\n", "        \n", "        Parameters:\n", "        messages (List[Dict]): List of message dictionaries\n", "        model (str): Model to use (default: gpt-4)\n", "        temperature (float): Randomness of output (0-1)\n", "        max_tokens (int): Maximum tokens in response\n", "        \n", "        Returns:\n", "        Dict: API response\n", "        \"\"\"\n", "        try:\n", "            client = OpenAI(\n", "                # This is the default and can be omitted\n", "                api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "            )\n", "\n", "            chat_completion = client.chat.completions.create(\n", "                messages=messages,\n", "                model=\"gpt-3.5-turbo\",\n", "            )\n", "            \n", "            response = []\n", "            for choice in chat_completion.choices:\n", "                response.append(choice.message.content)\n", "            \n", "            return response[0]\n", "\n", "        except Exception as e:\n", "            print(f\"Unexpected error: {str(e)}\")\n", "            return None\n", "        \n", "open_ai_client = OpenAIClient(api_key=os.getenv(\"OPENAI_API_KEY\", \"\"))\n", "rag_test_cases_file = \"/Users/<USER>/Desktop/gong_calls/gong-calls-rag-test-cases.csv\"\n", "\n", "question_and_answers = []\n", "\n", "with open(rag_test_cases_file, 'r', encoding='utf-8') as file:\n", "    # Create CSV reader\n", "    csv_reader = csv.DictReader(file)\n", "\n", "    # Convert each row into a dictionary and append to list\n", "    for row in csv_reader:\n", "        question_and_answers.append(row)\n"]}, {"cell_type": "markdown", "id": "df5cc312", "metadata": {}, "source": ["### A/B testing of langchain RAG and AWS bedrock RAG"]}, {"cell_type": "code", "execution_count": 110, "id": "0f75d650", "metadata": {}, "outputs": [], "source": ["# fetch_relevant_documents_using_auto_embeddings - langchain RAG\n", "# retrieve_from_kb - retrive from aws knowledge base\n", "\n", "import json\n", "\n", "rag_output_file = \"/Users/<USER>/Desktop/gong_calls/gong-calls-rag-test-cases-testing-output.csv\"\n", "\n", "rag_output_file_actual_response = \"/Users/<USER>/Desktop/gong_calls/gong-calls-rag-test-cases-testing-output-response-actual.csv\"\n", "\n", "test_prompt = \"\"\"You are a RAG pipeline response grader. You will be given a question and the expected answer.\n", "You will be given two responses from two different RAG pipelines :- Response A and B. Your task is to grade both the responses out of 5.\n", "Grade must be given by checking the using the RAG responses, we can get the expected answer.\n", "\n", "DO NOT check for actual match between expected answer and the response. Since the responses are RAG documents, only check if you can GET TO the expected answer using the responses. Rate the responses accordingly.\n", "\n", "Your answer must strictly follow the below json structure :-\n", "```\n", "{\n", "    \"A\" : grade of a,\n", "    \"B\" : grade of b\n", "}\n", "```\n", "For example :-\n", "```\n", "{\n", "    \"A\" : 4.15,\n", "    \"B\" : 4.29\n", "}\n", "```\n", "\"\"\"\n", "\n", "count = 10\n", "# grading_results = list()\n", "rag_responses = list()\n", "for test_case in  question_and_answers:\n", "    question = test_case['Question']\n", "    answer = test_case['Answer']\n", "    langchain_response = fetch_relevant_documents_using_auto_embeddings(question, [contextual_collection_name])\n", "    bedrock_response = retrieve_from_kb(question)\n", "    prompt = f\"\"\"\n", "    {test_prompt}\n", "    \n", "    Question :- {question}\n", "    \n", "    Expected answer :- {answer}\n", "    \n", "    Response A :- {langchain_response}\n", "    \n", "    Response B :- {bedrock_response}\n", "    \"\"\"\n", "    \n", "    rag_responses.append(\n", "        {\n", "            'question' : question,\n", "            'answer' : answer,\n", "            'langchain_response' : langchain_response,\n", "            'bedrock_response' : bedrock_response,\n", "            \n", "        }\n", "    )\n", "    \n", "    grades = open_ai_client.chat_completion(messages=[{\n", "        'role' : 'user',\n", "        'content' : prompt\n", "    }])\n", "    \n", "# #     print(question, \"\\n\", answer, \"\\n\", langchain_response, \"\\n\", bedrock_response, \"\\n\", grades)\n", "# #     count -= 1\n", "# #     if count == 7:\n", "# #         break\n", "\n", "#     grades = json.loads(grades)\n", "#     grade_a = grades['A']\n", "#     grade_b = grades['B']\n", "#     grading_results.append(\n", "#         {\n", "#             'question' : question,\n", "#             'langchain_response_grade' : grade_a,\n", "#             'bedrock_response_grade' : grade_b,\n", "#         }\n", "#     )\n", "        \n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 111, "id": "e2031366", "metadata": {}, "outputs": [], "source": ["import csv\n", "\n", "rag_output_file_actual_response = \"/Users/<USER>/Desktop/gong_calls/gong-calls-rag-test-cases-testing-output-response-actual.csv\"\n", "\n", "# Writing to CSV\n", "with open(rag_output_file_actual_response, mode='w', newline='') as file:\n", "    # Create a CSV writer object\n", "    writer = csv.DictWriter(file, fieldnames=rag_responses[0].keys())\n", "    \n", "    # Write header\n", "    writer.writeheader()\n", "    \n", "    # Write data rows\n", "    writer.writerows(rag_responses)\n"]}, {"cell_type": "code", "execution_count": null, "id": "0bebe76c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}