# pylint: disable=logging-fstring-interpolation
# ruff: noqa: G004

"""
utility module that contains helper functions to create resources in aws for the knowledge base and vector collections
"""

import json
import logging
import os
from uuid import uuid4

logger = logging.getLogger(__name__)

import boto3
from opensearchpy import (
    AWSV4SignerAuth,
    OpenSearch,
    RequestError,
    RequestsHttpConnection,
)

from .accessor import VectorCollectionAccessor, VectorCollectionMetaAccessor
from .models import VectorCollection, VectorCollectionMeta
from .utility import (
    GONG_CALL_CHUNKING_STRATEGY,
    EntityType,
    GongCallChunkingStrategy,
    generate_aws_safe_id,
    get_vector_collection_name,
    interactive_sleep,
)

# List of supported AWS Bedrock regions
regions = [
    "us-east-1",
    "us-east-2",
    "us-west-1",
    "us-west-2",
    "ap-south-1",
    "ap-northeast-3",
    "ap-northeast-2",
    "ap-southeast-1",
    "ap-southeast-2",
    "ap-northeast-1",
    "ca-central-1",
    "eu-central-1",
    "eu-west-1",
    "eu-west-2",
    "eu-west-3",
    "eu-north-1",
    "sa-east-1",
]

# Base ARNs for embedding models
base_embedding_arns = [
    "arn:aws:bedrock:{region}::foundation-model/cohere.embed-english-v3",
    "arn:aws:bedrock:{region}::foundation-model/amazon.titan-embed-text-v1",
    "arn:aws:bedrock:{region}::foundation-model/amazon.titan-embed-text-v2:0",
]

# Base ARNs for foundational models (Claude)
base_foundational_arns = [
    "arn:aws:bedrock:{region}::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0",
    "arn:aws:bedrock:{region}::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
]

# Mapping of embedding models available in each region
region_to_embedding_model_arns_mapping = {
    region: [arn.format(region=region) for arn in base_embedding_arns]
    for region in regions
}

default_embedding_model_arn_mapping = {
    region: region_to_embedding_model_arns_mapping[region][2] for region in regions
}

region_to_foundational_model_arns_mapping = {
    region: [arn.format(region=region) for arn in base_foundational_arns]
    for region in regions
}

default_foundational_model_arn_mapping = {
    region: region_to_foundational_model_arns_mapping[region][0] for region in regions
}


def create_iam_policies_in_aoss(vector_collection_name: str):
    """
    create IAM policies to enable creation of a vector store
    """

    # opensearchserverless client
    aoss_client = boto3.client("opensearchserverless")

    suffix = f"{uuid4().hex[:10]}".replace("-", "")
    encryption_policy_name = f"esbedrockep{suffix}"
    network_policy_name = f"esbedrocknp{suffix}"
    access_policy_name = f"esbedrockap{suffix}"
    # get current account arn
    identity = boto3.client("sts").get_caller_identity()["Arn"]

    # create the security policy to encrypt data at rest and in motion
    aoss_client.create_security_policy(
        name=encryption_policy_name,
        policy=json.dumps(
            {
                "Rules": [
                    {
                        "Resource": ["collection/" + vector_collection_name],
                        "ResourceType": "collection",
                    }
                ],
                "AWSOwnedKey": True,
            }
        ),
        type="encryption",
    )

    # security policy to allow public access to opensearch collection
    aoss_client.create_security_policy(
        name=network_policy_name,
        policy=json.dumps(
            [
                {
                    "Rules": [
                        {
                            "Resource": ["collection/" + vector_collection_name],
                            "ResourceType": "collection",
                        },
                        {
                            "Resource": [f"collection/{vector_collection_name}"],
                            "ResourceType": "dashboard",
                        },
                    ],
                    "AllowFromPublic": True,
                }
            ]
        ),
        type="network",
    )

    # data access policy to allow particular aws roles to access the collection
    aoss_client.create_access_policy(
        name=access_policy_name,
        policy=json.dumps(
            [
                {
                    "Rules": [
                        {
                            "Resource": ["collection/" + vector_collection_name],
                            "Permission": [
                                "aoss:CreateCollectionItems",
                                "aoss:DeleteCollectionItems",
                                "aoss:UpdateCollectionItems",
                                "aoss:DescribeCollectionItems",
                            ],
                            "ResourceType": "collection",
                        },
                        {
                            "Resource": ["index/" + vector_collection_name + "/*"],
                            "Permission": [
                                "aoss:CreateIndex",
                                "aoss:DeleteIndex",
                                "aoss:UpdateIndex",
                                "aoss:DescribeIndex",
                                "aoss:ReadDocument",
                                "aoss:WriteDocument",
                            ],
                            "ResourceType": "index",
                        },
                    ],
                    "Principal": [identity],
                    "Description": "Easy data policy",
                }
            ]
        ),
        type="data",
    )

    return encryption_policy_name, network_policy_name, access_policy_name


def create_vector_collection_for_client(vector_collection_name: str):
    """
    create the vector store for the client in opensearch serverless
    """
    # opensearchserverless client
    aoss_client = boto3.client("opensearchserverless")

    collection = aoss_client.create_collection(
        name=vector_collection_name, type="VECTORSEARCH"
    )
    # since vector collection creation takes time, sleep for 30s
    interactive_sleep(90)
    return collection


def create_vector_collection_and_related_entities(vector_collection_name: str) -> dict:
    """
    creates the IAM policies and vector collection for the client
    """

    # create policies for the collection before creating it
    (
        encryption_policy_name,
        network_policy_name,
        access_policy_name,
    ) = create_iam_policies_in_aoss(vector_collection_name=vector_collection_name)
    # create the collection
    collection = create_vector_collection_for_client(
        vector_collection_name=vector_collection_name
    )
    collection_arn = collection["createCollectionDetail"]["arn"]
    collection_id = collection["createCollectionDetail"]["id"]
    logger.info(f"Vector collection {vector_collection_name}")

    return {
        "collection_id": collection_id,
        "collection_arn": collection_arn,
        "collection_name": vector_collection_name,
        "encryption_policy_name": encryption_policy_name,
        "network_policy_name": network_policy_name,
        "access_data_policy_name": access_policy_name,
    }


def create_bedrock_execution_role(
    s3_file_path: str,
    client_vector_collecion_arn: str,
):
    """
    create the bedrock execution role that has access to the s3 path where the gong call is stored
    and the vector collection that has the indexed data
    """

    suffix = generate_aws_safe_id(input_string=str(uuid4()))
    bedrock_execution_role_name = f"ESBedrockKB_{suffix}"
    fm_policy_name = f"BedrockFoundationalPolicy_{suffix}"
    s3_policy_name = f"BedrockS3Policy_{suffix}"
    oss_policy_name = f"BedrockOSSPolicy_{suffix}"

    region_name = os.getenv("AWS_REGION", "ap-south-1").lower()
    bucket_name = os.getenv("S3_GONG_CALL_BUCKET")
    account_number = boto3.client("sts").get_caller_identity().get("Account")
    iam_client = boto3.client("iam")

    # IAM policy to allow the bedrock role to invoke foundational embedding models
    foundation_model_policy_document = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": [
                    "bedrock:InvokeModel",
                ],
                "Resource": region_to_embedding_model_arns_mapping[region_name]
                + region_to_foundational_model_arns_mapping[region_name],
            }
        ],
    }

    # IAM policy to allow bedrock role to access the s3 path where the gong call is stored
    s3_policy_document = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": ["s3:GetObject", "s3:ListBucket"],
                "Resource": [
                    f"arn:aws:s3:::{bucket_name}",
                    f"arn:aws:s3:::{bucket_name}/*",
                ],
                "Condition": {
                    "StringEquals": {"aws:ResourceAccount": f"{account_number}"}
                },
            }
        ],
    }

    # IAM policy to allow bedrock role to access the opensearch collection
    oss_policy_document = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "OpenSearchServerlessAPIAccessAllStatement",
                "Effect": "Allow",
                "Action": ["aoss:APIAccessAll"],
                "Resource": [client_vector_collecion_arn],
            }
        ],
    }

    # allow bedrock service to assume this role when using the knowledge base
    assume_role_policy_document = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {"Service": "bedrock.amazonaws.com"},
                "Action": "sts:AssumeRole",
            }
        ],
    }

    fm_policy = iam_client.create_policy(
        PolicyName=fm_policy_name,
        PolicyDocument=json.dumps(foundation_model_policy_document),
        Description="Policy for accessing foundation model",
    )

    s3_policy = iam_client.create_policy(
        PolicyName=s3_policy_name,
        PolicyDocument=json.dumps(s3_policy_document),
        Description="Policy for reading documents from s3",
    )

    oss_policy = iam_client.create_policy(
        PolicyName=oss_policy_name,
        PolicyDocument=json.dumps(oss_policy_document),
        Description="Policy for reading opensearch collections",
    )

    # create bedrock execution role
    bedrock_kb_execution_role = iam_client.create_role(
        RoleName=bedrock_execution_role_name,
        AssumeRolePolicyDocument=json.dumps(assume_role_policy_document),
        Description="Amazon Bedrock Knowledge Base Execution Role for accessing OSS and S3",
        MaxSessionDuration=3600,
    )
    logger.info(
        f"Bedrock execution role {bedrock_execution_role_name} created, data source - {s3_file_path}, vector collection - {client_vector_collecion_arn}"
    )

    # fetch arn of the policies and role created above
    s3_policy_arn = s3_policy["Policy"]["Arn"]
    fm_policy_arn = fm_policy["Policy"]["Arn"]
    oss_policy_arn = oss_policy["Policy"]["Arn"]

    # attach policies to Amazon Bedrock execution role
    iam_client.attach_role_policy(
        RoleName=bedrock_kb_execution_role["Role"]["RoleName"], PolicyArn=fm_policy_arn
    )
    iam_client.attach_role_policy(
        RoleName=bedrock_kb_execution_role["Role"]["RoleName"], PolicyArn=s3_policy_arn
    )
    iam_client.attach_role_policy(
        RoleName=bedrock_kb_execution_role["Role"]["RoleName"], PolicyArn=oss_policy_arn
    )
    logger.info(
        f"S3, OSS and FM policies attached to the bedrock execution role {bedrock_execution_role_name}"
    )

    return bedrock_kb_execution_role


def attach_arn_to_data_policy_for_collection(
    data_policy_name: str, bedrock_execution_role_arn: str
):
    """
    attach a new bedrock execution role ARN to the data access policy of the collection
    aoss_client -> amazon open search serverless collection client
    """

    # opensearchserverless client
    aoss_client = boto3.client("opensearchserverless")

    # check if the policy present before upading it
    try:
        policy_details = aoss_client.get_access_policy(
            name=data_policy_name, type="data"
        )
        changes = False
        policy_version = policy_details["accessPolicyDetail"]["policyVersion"]
        for policy in policy_details["accessPolicyDetail"]["policy"]:
            if (
                "Principal" in policy
                and bedrock_execution_role_arn not in policy["Principal"]
            ):
                # attach the bedrock execution role to every data policy
                policy["Principal"].append(bedrock_execution_role_arn)
                changes = True

        if changes:
            updated_policy = json.dumps(
                policy_details["accessPolicyDetail"].get("policy", [])
            )
            response = aoss_client.update_access_policy(
                name=data_policy_name,
                type="data",
                policy=updated_policy,
                policyVersion=policy_version,
            )
            logger.info(
                f"Attached the bedrock role {bedrock_execution_role_arn} to data policy {data_policy_name}"
            )

    except Exception as e:
        print(f"Error in updating the data access policy {e}")
        raise e


def check_and_create_vector_collection_for_client(client_id: int):
    """
    check if vector collection exists for the client and create it if it doesn't existt
    """

    if not VectorCollectionAccessor(client_id=client_id).client_aware().exists():
        logger.info(
            f"vector collection not present for the clent {client_id}, creaitng it......"
        )
        vector_collection_name = get_vector_collection_name(client_id=client_id)
        collection_entities = create_vector_collection_and_related_entities(
            vector_collection_name=vector_collection_name
        )
        vector_collection_obj = VectorCollection(
            client_id=client_id, **collection_entities
        )
        vector_collection_obj.save()
        logger.info(
            f"Saving vector collection {collection_entities['collection_name']} metadata to database......"
        )


def check_and_create_single_vector_collection():
    """
    creates the singluar vector collection to index all gong calls
    """
    if not VectorCollectionMetaAccessor().check_collection_exists():
        logger.info("vector collection not present, creaitng it......")
        collection_entities = create_vector_collection_and_related_entities(
            vector_collection_name=get_vector_collection_name(client_id=None),
        )
        vector_collection_obj = VectorCollectionMeta(**collection_entities)
        vector_collection_obj.save()
        logger.info(
            f"Saving vector collection {collection_entities['collection_name']} metadata to database......"
        )


def create_index_for_knowledge_base(vector_collection_id: str, index_name: str):
    """
    creates an index in opensearch for the new knowledge base to be created
    """

    # Create the vector index in Opensearch serverless, with the knn_vector field index mapping, specifying the dimension size, name and engine.
    credentials = boto3.Session().get_credentials()
    region_name = os.getenv("AWS_REGION", "ap-south-1").lower()
    service = "aoss"
    awsauth = AWSV4SignerAuth(credentials, region_name, service)

    body_json = {
        "settings": {
            "index.knn": "true",
            "number_of_shards": 1,
            "knn.algo_param.ef_search": 512,
            "number_of_replicas": 0,
        },
        "mappings": {
            "properties": {
                "vector": {
                    "type": "knn_vector",
                    "dimension": 1024,
                    "method": {"name": "hnsw", "engine": "faiss", "space_type": "l2"},
                },
                "text": {"type": "text"},
                "text-metadata": {"type": "text"},
            }
        },
    }

    # Build the OpenSearch client
    opensearch_host_url = (
        vector_collection_id + "." + region_name + f".{service}" + ".amazonaws.com"
    )
    oss_client = OpenSearch(
        hosts=[{"host": opensearch_host_url, "port": 443}],
        http_auth=awsauth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection,
        timeout=300,
    )

    # Create the index
    try:
        response = oss_client.indices.create(
            index=index_name, body=json.dumps(body_json)
        )
        # since vector index creation takes time, sleep for 10s
        interactive_sleep(90)
        logger.info(
            f"Created vector index {index_name} in the collection {vector_collection_id}"
        )

        return response

    except RequestError as e:
        print(
            f"Error while trying to create the index, with error {e.error}\nyou may unmark the delete above to delete, and recreate the index"
        )
        raise e


def create_knowledge_base_for_gong_call(
    entity_id: str,
    bedrock_execution_role_arn: str,
    vector_collection_arn: str,
    vector_index_name: str,
    knowledge_base_name: str,
    knowledge_base_description: str,
):
    """
    creates a knowledge base for the gong call
    """

    bedrock_agent_client = boto3.client("bedrock-agent")

    oss_serverless_configuration = {
        "collectionArn": vector_collection_arn,
        "vectorIndexName": vector_index_name,
        "fieldMapping": {
            "vectorField": "vector",
            "textField": "text",
            "metadataField": "text-metadata",
        },
    }

    region_name = os.getenv("AWS_REGION", "ap-south-1").lower()

    create_kb_response = bedrock_agent_client.create_knowledge_base(
        name=knowledge_base_name,
        description=knowledge_base_description,
        roleArn=bedrock_execution_role_arn,
        knowledgeBaseConfiguration={
            "type": "VECTOR",
            "vectorKnowledgeBaseConfiguration": {
                "embeddingModelArn": default_embedding_model_arn_mapping[region_name]
            },
        },
        storageConfiguration={
            "type": "OPENSEARCH_SERVERLESS",
            "opensearchServerlessConfiguration": oss_serverless_configuration,
        },
    )
    logger.info(
        f"Created knowledge base {knowledge_base_name} for the entity {entity_id}"
    )

    return create_kb_response["knowledgeBase"]


def get_vector_index_configuration_based_on_entity(entity_type: str):
    """
    depending upon the entity type - gong call, email, notes get the chunking configuration
    """
    region_name = os.getenv("AWS_REGION", "ap-south-1").lower()
    # hubspot emails are not chunked
    # hubspot notes are always semantically chunked as no atomic separation is required
    # gong calls are manually chunked to honor atomic blocks or chunked semantically chunked based on GONG_CALL_CHUNKING_STRATEGY flag value
    if (
        entity_type == EntityType.GONG_CALL.value
        and GONG_CALL_CHUNKING_STRATEGY
        == GongCallChunkingStrategy.SEMANTIC_CHUNKING.value
    ) or entity_type == EntityType.HUBSPOT_NOTES.value:
        return {
            "chunkingConfiguration": {
                "chunkingStrategy": "SEMANTIC",
                "semanticChunkingConfiguration": {
                    "breakpointPercentileThreshold": 95,
                    "bufferSize": 0,
                    "maxTokens": 300,
                },
            },
            "parsingConfiguration": {
                "bedrockFoundationModelConfiguration": {
                    "modelArn": default_foundational_model_arn_mapping[region_name],
                },
                "parsingStrategy": "BEDROCK_FOUNDATION_MODEL",
            },
        }

    elif (
        entity_type == EntityType.GONG_CALL.value
        and GONG_CALL_CHUNKING_STRATEGY
        == GongCallChunkingStrategy.MANUAL_CHUNKING.value
    ) or entity_type == EntityType.HUBSPOT_EMAIL.value:
        return {
            "chunkingConfiguration": {
                "chunkingStrategy": "NONE",
            },
            "parsingConfiguration": {
                "bedrockFoundationModelConfiguration": {
                    "modelArn": default_foundational_model_arn_mapping[region_name],
                },
                "parsingStrategy": "BEDROCK_FOUNDATION_MODEL",
            },
        }


def add_data_source_to_knowledge_base(
    knowledge_base_id: str, s3_file_path: str, entity_type: str
):
    """
    given the arn of a knowledge base, add the given data source to the knowledge base
    """

    bucket_name = os.getenv("S3_GONG_CALL_BUCKET")
    bedrock_agent_client = boto3.client("bedrock-agent")
    data_source_name = "bedrock-default-knowledge-base"
    s3_configuration = {
        "bucketArn": f"arn:aws:s3:::{bucket_name}",
        "inclusionPrefixes": [
            s3_file_path + "/",
        ],
    }

    # Ingest strategy - How to ingest data from the data source
    # default choice is semnatic chunking using claude sonnet for parsing
    vector_ingestion_configuration = get_vector_index_configuration_based_on_entity(
        entity_type=entity_type
    )

    create_ds_response = bedrock_agent_client.create_data_source(
        name=data_source_name,
        description="Gong call data source for knowledge base",
        knowledgeBaseId=knowledge_base_id,
        dataSourceConfiguration={"type": "S3", "s3Configuration": s3_configuration},
        vectorIngestionConfiguration=vector_ingestion_configuration,
        dataDeletionPolicy="RETAIN",
    )
    data_source = create_ds_response["dataSource"]
    logger.info(
        f"Created data source {data_source_name} for the knowledge base {knowledge_base_id}"
    )
    return data_source


def sync_s3_data_source(knowledge_base_id: str, data_source_id: str):
    """
    after all the chunks of the gong call are added to s3, sync the data source to chunk and embed them in the vector index
    """

    bedrock_agent_client = boto3.client("bedrock-agent")
    sync_job_response = bedrock_agent_client.start_ingestion_job(
        knowledgeBaseId=knowledge_base_id, dataSourceId=data_source_id
    )
    sync_job = sync_job_response["ingestionJob"]
    while sync_job["status"] != "COMPLETE":
        get_job_response = bedrock_agent_client.get_ingestion_job(
            knowledgeBaseId=knowledge_base_id,
            dataSourceId=data_source_id,
            ingestionJobId=sync_job["ingestionJobId"],
        )
        sync_job = get_job_response["ingestionJob"]
        interactive_sleep(10)

    logger.info(
        f"Synced data source {data_source_id} to knowledge base {knowledge_base_id}"
    )


def create_knowledge_base_and_related_entities(
    vector_collection_details: dict,
    data_source_s3_path: str,
    knowledge_base_source_details: dict,
    vector_index_name: str,
    knowledge_base_name: str,
    knowledge_base_description: str,
):
    """
    create the knowledge base, bedrock execution role and the vector index for the gong call
    """

    vector_collection_arn = vector_collection_details.get("vector_collection_arn", "")
    vector_collection_id = vector_collection_details.get("vector_collection_id", "")
    vector_collection_data_policy_name = vector_collection_details.get(
        "vector_collection_data_policy_name", ""
    )

    entity_type = knowledge_base_source_details.get("entity_type", "")
    entity_id = knowledge_base_source_details.get("entity_id", "")

    # create the execution role to be used by the bedrock service to manage the knowledge base
    bedrock_execution_role = create_bedrock_execution_role(
        s3_file_path=data_source_s3_path,
        client_vector_collecion_arn=vector_collection_arn,
    )
    bedrock_role_arn = bedrock_execution_role["Role"]["Arn"]

    # attach the bedrock execution role to the client's vector collection access policy
    attach_arn_to_data_policy_for_collection(
        data_policy_name=vector_collection_data_policy_name,
        bedrock_execution_role_arn=bedrock_role_arn,
    )

    # create an index for the knowledge base
    vector_index_for_knowledge_base = create_index_for_knowledge_base(
        vector_collection_id=vector_collection_id, index_name=vector_index_name
    )
    vector_index_name = vector_index_for_knowledge_base["index"]

    # create the knowledge base
    knowledge_base = create_knowledge_base_for_gong_call(
        entity_id=entity_id,
        bedrock_execution_role_arn=bedrock_role_arn,
        vector_collection_arn=vector_collection_arn,
        vector_index_name=vector_index_name,
        knowledge_base_name=knowledge_base_name,
        knowledge_base_description=knowledge_base_description,
    )
    knowledge_base_id = knowledge_base["knowledgeBaseId"]
    knowledge_base_arn = knowledge_base["knowledgeBaseArn"]

    # add a data source to the knowledge base
    data_source = add_data_source_to_knowledge_base(
        knowledge_base_id=knowledge_base_id,
        s3_file_path=data_source_s3_path,
        entity_type=entity_type,
    )
    data_source_id = data_source["dataSourceId"]

    knowledge_base_meta = {
        "knowledge_base_arn": knowledge_base_arn,
        "knowledge_base_id": knowledge_base_id,
        "knowledge_base_name": knowledge_base_name,
    }

    return knowledge_base_meta, data_source_id, bedrock_role_arn
