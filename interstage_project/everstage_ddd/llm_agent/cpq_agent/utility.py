"""
contains utility functions for mananging the aws knowledge bases
"""

import hashlib
import json
import logging
import os
import re
import time
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from uuid import uuid4

from langchain_experimental.text_splitter import Semantic<PERSON>hunker
from langchain_openai.embeddings import OpenAIEmbeddings

logger = logging.getLogger(__name__)

import boto3

HUBSPOT_EMAIL_ENTITY_ID = "hubspot_email"
HUBSPOT_NOTES_ENTITY_ID = "hubspot_notes"
GONG_CALL_ENTITY_ID = "gong_call"


class RAGStrategy(Enum):
    """
    stores the list of RAG strategies
    """

    LONG_CONTEXT_RAG = "long_context_rag"
    AWS_BEDROCK_RAG = "aws_bedrock_rag"
    GRAPH_RAG = "graph_rag"


class EntityType(Enum):
    """
    entity type
    """

    GONG_CALL = "gong_call"
    HUBSPOT_EMAIL = "hubspot_email"
    HUBSPOT_NOTES = "hubspot_notes"


class GongCallChunkingStrategy(Enum):
    """
    how gong calls are chunked
    """

    MANUAL_CHUNKING = "manual_chunking"
    SEMANTIC_CHUNKING = "semantic_chunking"


ENTITY_TYPE_TO_ID = {
    EntityType.HUBSPOT_EMAIL.value: HUBSPOT_EMAIL_ENTITY_ID,
    EntityType.HUBSPOT_NOTES.value: HUBSPOT_NOTES_ENTITY_ID,
    EntityType.GONG_CALL.value: GONG_CALL_ENTITY_ID,
}

TYPESENSE_METADATA_COLLECTION_SCHEMA = {
    "fields": [
        {"name": "knowledge_base_name", "type": "string"},
        {"name": "knowledge_base_id", "type": "string"},
        {"name": "entity_id", "type": "string"},
        {"name": "entity_type", "type": "string"},
    ]
}
TYPESENSE_METADATA_COLLECTION_SCHEMA_LLM_RAG = {
    "fields": [
        {"name": "client_id", "type": "int32"},
        {"name": "opportunity_id", "type": "string"},
        {"name": "entity_type", "type": "string"},
        {"name": "long_context_prompt_path", "type": "string"},
    ]
}
TYPESENSE_METADATA_COLLECTION_SCHEMA_GRAPH_RAG = {
    "fields": [
        {"name": "client_id", "type": "int32"},
        {"name": "opportunity_id", "type": "string"},
        {"name": "entity_type", "type": "string"},
        {"name": "graph_rag_indexed_parquet_path", "type": "string"},
    ]
}
TYPESENSE_BEDROCK_COLLECTION_NAME = os.getenv(
    "cpq_agent_bedrock_typesense_collection", "cpq_agent_metadata"
)
TYPESENSE_COLLECTION_NAME_LLM_RAG = os.getenv(
    "cpq_agent_typesense_collection_llm_rag", "cpq_agent_llm_rag"
)
TYPESENSE_COLLECTION_NAME_GRAPH_RAG = os.getenv(
    "cpq_agent_typesense_collection_graph_rag", "cpq_agent_graph_rag"
)
GONG_CALL_MANUAL_CHUNK_SEPARATOR = ";;"
GONG_CALL_CHUNKING_STRATEGY = GongCallChunkingStrategy.MANUAL_CHUNKING.value

DEFAULT_RAG_STRATEGY = RAGStrategy.LONG_CONTEXT_RAG.value


def get_gong_call_s3_path(
    client_id: int,
    opportunity_id: str,
    client_name: str,
    call_date: str,
    call_id: str,
    single_knowledge_base: bool,
):
    """
    get the s3 path to upload the chunk
    returns the path to
    1. the folder where all gong calls for this opportunity are stored
    2. the file where the chunks are stored
    3. to the upper level folder that has all gong calls to create the knowledge base
    """
    if single_knowledge_base:
        return (
            f"gong_calls/single_kb/{client_name}_{client_id}/{opportunity_id}",
            f"gong_calls/single_kb/{client_name}_{client_id}/{opportunity_id}/{call_date}_{call_id}.txt",
            "gong_calls/single_kb",
        )

    return (
        f"gong_calls/{client_name}_{client_id}/{opportunity_id}",
        f"gong_calls/{client_name}_{client_id}/{opportunity_id}/{call_date}_{call_id}.txt",
        "gong_calls",
    )


def get_gong_call_long_rag_s3_pth(
    client_id: int,
    opportunity_id: str,
    client_name: str,
    call_date: str,
    call_id: str,
):
    """
    get the s3 path to upload the entire file to be given to the RAG tool as full context
    """
    return (
        f"gong_calls/long_context/{client_name}_{client_id}/{opportunity_id}/llm_rag/long_context_prompt.txt",
        f"gong_calls/long_context/{client_name}_{client_id}/{opportunity_id}/no_chunk_calls/{call_date}_{call_id}.txt",
    )


def get_gong_call_graph_rag_s3_path(
    client_id: int,
    opportunity_id: str,
    client_name: str,
    call_date: str,
    call_id: str,
):
    """
    get the s3 path to upload the chunks from upstream for graphrag.
    """

    return (
        f"gong_calls/graph_rag/{client_name}_{client_id}/{opportunity_id}",
        f"gong_calls/graph_rag/{client_name}_{client_id}/{opportunity_id}/{call_date}_{call_id}.txt",
    )


def get_hubspot_entity_s3_path(
    client_id: int, opportunity_id: str, client_name: str, entity_type: str
):
    """
    get the s3 path to upload email to s3
    """
    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        return (
            f"emails/single_kb/{client_name}_{client_id}/{opportunity_id}",
            "emails/single_kb",
        )

    else:
        return (
            f"notes/single_kb/{client_name}_{client_id}/{opportunity_id}",
            "notes/single_kb",
        )


def get_hubspot_entity_graph_rag_s3_path(
    client_id: str, opportunity_id: str, client_name: str, entity_type: str
):
    """
    get the s3 path to upload email or notes to s3 in graphrag folder
    """
    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        return f"emails/graph_rag/{client_name}_{client_id}/{opportunity_id}"

    else:
        return f"notes/graph_rag/{client_name}_{client_id}/{opportunity_id}"


def get_hubspot_entity_long_context_s3_path(
    client_id: str,
    opportunity_id: str,
    client_name: str,
    entity_type: str,
    file_suffix: str,
):
    """
    get the s3 path to upload email or notes to s3 in long context folder
    """
    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        return (
            f"emails/long_context/{client_name}_{client_id}/{opportunity_id}/no_chunk_emails/{file_suffix}.txt",
            f"emails/long_context/{client_name}_{client_id}/{opportunity_id}/llm_rag/long_context_prompt.txt",
        )
    else:
        return (
            f"notes/long_context/{client_name}_{client_id}/{opportunity_id}/no_chunk_notes/{file_suffix}.txt",
            f"notes/long_context/{client_name}_{client_id}/{opportunity_id}/llm_rag/long_context_prompt.txt",
        )


def get_vector_collection_name(client_id):
    """
    return the name of the vector collection for the client
    """
    if not client_id:
        return f"es-bedrock-collection-{os.getenv('ENV','').lower()}"

    return f"es-bedrock-collection-{client_id}"


def get_vector_index_name(opportunity_id):
    """
    returns the name of the vector index in which the gong call will be indexed and stored for the given opportunity
    """
    if not opportunity_id:
        return "bedrock-index-gong-calls"
    return f"bedrock-index-gong-calls-{opportunity_id.lower()}"


def get_vector_index_name_hubspot_chunk(entity_type: str, opportunity_id):
    """
    returns the name of the vector index in which the hubspot emails will be indexed and stored
    """
    if not opportunity_id:
        return f"bedrock-index-{entity_type}"
    return f"bedrock-index-{entity_type}-{opportunity_id.lower()}"


def get_knowledge_base_name_by_entity(entity_id: str):
    """
    returns the name of the knowledge base by entity id
    """
    return f"everstage-kb-{entity_id}-{os.getenv('ENV','').lower()}"


def get_knowledge_base_name(client_id: int, opportunity_id: str):
    """
    returns the name of the knowledge base
    """
    return f"everstage-kb-gong-calls-{client_id}-{opportunity_id}"


def get_knowledge_base_name_hubspost(
    client_id: int, entity_type: str, opportunity_id: str
):
    """
    returns the name of the knowledge base
    """
    return f"everstage-kb-{entity_type}-{client_id}-{opportunity_id}"


def convert_millisecods_to_time(milliseconds):
    """
    convert milliseconds to a time in the call
    """
    # Convert milliseconds to a timedelta
    time_delta = timedelta(milliseconds=milliseconds)

    # Format the time as HH:MM:SS.sss
    formatted_time = (datetime.min + time_delta).strftime("%H:%M:%S.%f")[:-3]

    return formatted_time


def convert_chunk_to_text(
    chunk: dict, chunking_strategy=GongCallChunkingStrategy.SEMANTIC_CHUNKING.value
):
    """
    convert the gong chunk to a text
    """

    call_text = ""
    title = chunk.get("title", "")
    if title:
        call_text += f"{title}\n\n"
    speaker = chunk.get("speakerName", "")
    for sentence in chunk.get("sentences", []):
        start_time = convert_millisecods_to_time(sentence.get("start", 0))
        text = sentence.get("text", "")
        sentence_text = f"{start_time} | {speaker} \n {text}"

        # for manual chunking, add a custom separator to split it manually
        if chunking_strategy == GongCallChunkingStrategy.MANUAL_CHUNKING.value:
            call_text += f"\n {sentence_text}\n{GONG_CALL_MANUAL_CHUNK_SEPARATOR}"
        else:
            call_text += f"\n {sentence_text}"

    return call_text


def preprocess_hubspot_chunk(chunk_data: dict, entity_type: str):
    """
    remove unnecessary fields from hubspot email content and retain only the necessary field
    """

    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        email_obj = dict()
        email_obj["sender_email"] = chunk_data.get("hs_email_from_email", "")
        email_obj["receiver_email"] = chunk_data.get("hs_email_to_email", "")
        email_obj["subject"] = chunk_data.get("hs_email_subject", "")
        email_obj["email_content"] = chunk_data.get("hs_body_preview", "")
        email_obj["sent_time"] = chunk_data.get("hs_createdate", "")
        return email_obj, chunk_data.get("hs_createdate", "")

    else:
        notes_content = f"""
            Created by :- {chunk_data.get('hs_created_by_user_id', '')}
            Created at :- {chunk_data.get('hs_createdate', '')}

            Notes Content :-
            
            {chunk_data.get('hs_body_preview', '')}

        """
        return notes_content, chunk_data.get("hs_createdate", "")


def convert_hubspot_chunk_obj_to_text(chunk_data: dict, entity_type: str):
    """
    convert the hubspot chunk object to text
    """
    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        email_content = f"""
            Sender Email :- {chunk_data.get("sender_email", "")}
            Receiver Email :- {chunk_data.get("receiver_email", "")}
            Subject :- {chunk_data.get("subject", "")}
            Email Content :- {chunk_data.get("email_content", "")}
            Sent Time :- {chunk_data.get("sent_time", "")}
        """
        return email_content

    elif entity_type == EntityType.HUBSPOT_NOTES.value:
        return chunk_data


def upload_chunk_to_s3(file_path: str, chunk_data: str):
    """
    upload the chunk to the given path in the common aws bucket
    UPLOAD_CHUNKS_SEPARATELY -> controls whether each chunk has to be upload separately or appended to the existing file
    """

    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET")
    s3_client = boto3.client("s3")
    suffix = uuid4()

    current_dir = os.path.dirname(os.path.abspath(__file__))
    local_file = f"{current_dir}/{suffix}.txt"

    # download file if exists or
    try:
        s3_client.download_file(s3_bucket, file_path, local_file)
    except Exception as e:
        logger.info(f"File doesn't exist, creating it")

    # append data to file and upload to s3
    with open(local_file, "a") as f:
        f.write("\n" + chunk_data)
    s3_client.upload_file(local_file, s3_bucket, file_path)

    # delete the local file
    os.remove(local_file)


def get_chunk_start_time(gong_call_chunk: dict):
    """
    get the start and end timestamps from the gong call chunk
    """
    pass


def delete_gong_call_file(file_path: str):
    """
    delete the file present in the path to handle retries and failure
    """
    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")
    s3_client.delete_object(Bucket=s3_bucket, Key=file_path)


def create_metadata_file_for_entity(file_path: str, metadata: dict):
    """
    upload a metadata file for the gong call to add call date to each semantically split chunk
    """
    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")
    meta_file_path = f"{file_path}.metadata.json"
    metadata_json = json.dumps({"metadataAttributes": metadata})
    s3_client.put_object(
        Bucket=s3_bucket,
        Key=meta_file_path,
        Body=metadata_json,
    )


def get_hubspost_chunk_suffix_path(entity_type: str, chunk_data: dict):
    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        return uuid4()
    return chunk_data.get("hs_object_id", uuid4())


def upload_hubspot_chunk_to_s3(
    file_path: str, chunk_data, entity_type: str, suffix: str, metadata_json: dict
):
    """
    all emails are uploaded as separate chunks to aws bedrock
    as no chunking is done for emails and they're uploaded straight forwardly
    """

    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")

    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        chunk_path = f"{file_path}/{suffix}.txt"
        s3_client.put_object(Bucket=s3_bucket, Key=chunk_path, Body=str(chunk_data))
        logger.info(f"Uploaded chunk to {file_path}")
        # add the metadata file for entities indexed in same knowledge base
        metadata = metadata_json
        create_metadata_file_for_entity(file_path=chunk_path, metadata=metadata)

    elif entity_type == EntityType.HUBSPOT_NOTES.value:
        chunk_path = f"{file_path}/{suffix}.txt"
        temp_suffix = uuid4()

        current_dir = os.path.dirname(os.path.abspath(__file__))
        local_file = f"{current_dir}/{temp_suffix}.txt"
        # download file if exists or
        try:
            s3_client.download_file(s3_bucket, chunk_path, local_file)
        except Exception as e:
            logger.info(f"File doesn't exist, creating it")

        # append data to file and upload to s3
        with open(local_file, "a") as f:
            f.write("\n" + chunk_data)
        s3_client.upload_file(local_file, s3_bucket, chunk_path)

        # delete the local file
        os.remove(local_file)
        logger.info(f"Uploaded chunk to {file_path}")
        # add the metadata file for entities indexed in same knowledge base
        metadata = metadata_json
        create_metadata_file_for_entity(file_path=chunk_path, metadata=metadata)


def upload_upload_hubspot_chunk_to_s3_graph_rag(
    file_path: str, chunk_data, entity_type: str, suffix: str
):
    """
    all emails are uploaded as separate chunks to aws bedrock
    as no chunking is done for emails and they're uploaded straight forwardly
    """

    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")

    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        chunk_path = f"{file_path}/{suffix}.json"
        s3_client.put_object(
            Bucket=s3_bucket,
            Key=chunk_path,
            Body=json.dumps(chunk_data, default=str),
        )
        logger.info(f"Uploaded chunk to {file_path}")
        # add the metadata file for entities indexed in same knowledge base
        # metadata = metadata_json
        # create_metadata_file_for_entity(file_path=chunk_path, metadata=metadata)

    elif entity_type == EntityType.HUBSPOT_NOTES.value:
        chunk_path = f"{file_path}/{suffix}.txt"
        temp_suffix = uuid4()

        current_dir = os.path.dirname(os.path.abspath(__file__))
        local_file = f"{current_dir}/{temp_suffix}.txt"
        # download file if exists or
        try:
            s3_client.download_file(s3_bucket, chunk_path, local_file)
        except Exception as e:
            logger.info(f"File doesn't exist, creating it")

        # append data to file and upload to s3
        with open(local_file, "a") as f:
            f.write("\n" + chunk_data)
        s3_client.upload_file(local_file, s3_bucket, chunk_path)

        # delete the local file
        os.remove(local_file)
        logger.info(f"Uploaded chunk to {file_path}")
        # add the metadata file for entities indexed in same knowledge base
        # metadata = metadata_json
        # create_metadata_file_for_entity(file_path=chunk_path, metadata=metadata)


def upload_hubspot_chunk_to_s3_long_context(
    file_path: str, chunk_data_long_context: str, entity_type: str, suffix: str
):
    """
    upload the hubspot chunk to s3 in long context folder
    """
    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")

    if entity_type == EntityType.HUBSPOT_EMAIL.value:
        chunk_path = file_path
        s3_client.put_object(
            Bucket=s3_bucket,
            Key=chunk_path,
            Body=chunk_data_long_context,
        )
        logger.info(f"Uploaded chunk to {file_path}")

    elif entity_type == EntityType.HUBSPOT_NOTES.value:
        chunk_path = file_path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_suffix = uuid4()
        local_file = f"{current_dir}/{temp_suffix}.txt"
        # download file if exists or
        try:
            s3_client.download_file(s3_bucket, chunk_path, local_file)
        except Exception as e:
            logger.info(f"File doesn't exist, creating it")

        # append data to file and upload to s3
        with open(local_file, "a") as f:
            f.write("\n" + chunk_data_long_context)
        s3_client.upload_file(local_file, s3_bucket, chunk_path)

        # delete the local file
        os.remove(local_file)
        # s3_client.put_object(
        #     Bucket=s3_bucket,
        #     Key=chunk_path,
        #     Body=chunk_data_long_context,
        # )
        logger.info(f"Uploaded chunk to {file_path}")


def interactive_sleep(seconds: int):
    """
    sleep for seconds for time consuming activities
    """
    dots = ""
    for i in range(seconds):
        dots += "."
        print(dots, end="\r")
        time.sleep(1)


def add_knowledge_base_details_to_typesense(
    client_id: int,
    opportunity_id: str,
    knowledge_base_name: str,
    knowledge_base_id: str,
    entity_type: str,
    entity_id: str,
    single_knowledge_base: bool,
):
    """
    add the metadata of the knolwedge base to typesense based on the provided entity type
    """
    from everstage_ddd.global_search.services.proxies import SearchEngine

    document = {
        "knowledge_base_name": knowledge_base_name,
        "knowledge_base_id": knowledge_base_id,
        "entity_id": entity_id,
        "entity_type": entity_type,
    }
    # check if collection exists in typesense
    is_collection_exists = SearchEngine.instance().is_collection_exists(
        TYPESENSE_BEDROCK_COLLECTION_NAME
    )
    if not is_collection_exists:
        SearchEngine.instance().create_collection(
            {
                **TYPESENSE_METADATA_COLLECTION_SCHEMA,
                "name": TYPESENSE_BEDROCK_COLLECTION_NAME,
            }
        )
    SearchEngine.instance().upsert_document(TYPESENSE_BEDROCK_COLLECTION_NAME, document)


def add_graph_rag_details_to_typesense(
    client_id: int,
    opportunity_id: str,
    graph_rag_indexed_parquet_path: str,
    entity_type: str,
):
    """
    adds the metadata of the indexed parquet and lancedb files to typsense which will be
    used to query the graphrag from mannai side
    """
    from everstage_ddd.global_search.services.proxies import SearchEngine

    # adds the path of the parquet files to the typsesnse collection
    document = {
        "client_id": client_id,
        "opportunity_id": opportunity_id,
        "entity_type": entity_type,
        "graph_rag_indexed_parquet_path": graph_rag_indexed_parquet_path,
    }
    # checks if collection exists in typsense
    is_collection_exists = SearchEngine.instance().is_collection_exists(
        TYPESENSE_COLLECTION_NAME_GRAPH_RAG
    )
    if not is_collection_exists:
        SearchEngine.instance().create_collection(
            {
                **TYPESENSE_METADATA_COLLECTION_SCHEMA_GRAPH_RAG,
                "name": TYPESENSE_COLLECTION_NAME_GRAPH_RAG,
            }
        )

    collection = SearchEngine.instance().search(
        collection=TYPESENSE_COLLECTION_NAME_GRAPH_RAG,
        params={
            "q": "*",
            "filter_by": f"client_id:={client_id} && opportunity_id:={opportunity_id} && entity_type:={entity_type}",
        },
    )
    if len(collection["hits"]) == 0:
        SearchEngine.instance().upsert_document(
            TYPESENSE_COLLECTION_NAME_GRAPH_RAG, document
        )


def add_llm_rag_details_to_typesense(
    client_id: int, opportunity_id: str, entity_type: str, long_context_prompt_path: str
):
    """
    add the metadata of the long context rag prompt and details to typesense
    to be searched over from mannai
    """
    from everstage_ddd.global_search.services.proxies import SearchEngine

    document = {
        "client_id": client_id,
        "opportunity_id": opportunity_id,
        "entity_type": entity_type,
        "long_context_prompt_path": long_context_prompt_path,
    }
    # check if collection exists in typesense
    is_collection_exists = SearchEngine.instance().is_collection_exists(
        TYPESENSE_COLLECTION_NAME_LLM_RAG
    )
    if not is_collection_exists:
        SearchEngine.instance().create_collection(
            {
                **TYPESENSE_METADATA_COLLECTION_SCHEMA_LLM_RAG,
                "name": TYPESENSE_COLLECTION_NAME_LLM_RAG,
            }
        )

    collection = SearchEngine.instance().search(
        collection=TYPESENSE_COLLECTION_NAME_LLM_RAG,
        params={
            "q": "*",
            "filter_by": f"client_id:={client_id} && opportunity_id:={opportunity_id} && entity_type:={entity_type}",
        },
    )
    if len(collection["hits"]) == 0:
        SearchEngine.instance().upsert_document(
            TYPESENSE_COLLECTION_NAME_LLM_RAG, document
        )


def get_start_and_end_time_of_chunk(chunk_data: dict):
    """
    get the start and end time of the chunk
    """
    # code to parse list of times in string that are in format of MM:SS
    timestamps = re.findall(r"\b\d{2}:\d{2}\b", chunk_data)
    if len(timestamps) > 0:
        return timestamps[0], timestamps[-1]
    return "", ""


def manual_chunking_of_gong_call_before_data_sync(
    call_file_path: str, call_folder_path: str, gong_call_id: str, metadata: dict
):
    """
    manually chunk a gong call using langchain's custom separator before syncing data
    """

    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")
    suffix = uuid4()

    current_dir = os.path.dirname(os.path.abspath(__file__))
    local_file = f"{current_dir}/{suffix}.txt"

    # download the gong call file
    s3_client.download_file(s3_bucket, call_file_path, local_file)

    # append data to file and upload to s3
    with open(local_file, "r") as f:
        call_text = f.read()

    # semantically chunk honoring separator and upload chunks to S3
    text_splitter = SemanticChunker(
        OpenAIEmbeddings(), sentence_split_regex=GONG_CALL_MANUAL_CHUNK_SEPARATOR
    )
    chunks = text_splitter.create_documents([call_text])
    for chunk in chunks:
        chunk_id = uuid4()
        chunk_path = f"{call_folder_path}/{gong_call_id}_{chunk_id}.txt"
        s3_client.put_object(
            Bucket=s3_bucket,
            Key=chunk_path,
            Body=chunk.page_content,
        )
        start_time, end_time = get_start_and_end_time_of_chunk(chunk.page_content)
        metadata["chunk_start_time"] = start_time
        metadata["chunk_end_time"] = end_time

        meta_file_path = (
            f"{call_folder_path}/{gong_call_id}_{chunk_id}.txt.metadata.json"
        )
        metadata_json = json.dumps({"metadataAttributes": metadata})
        s3_client.put_object(
            Bucket=s3_bucket,
            Key=meta_file_path,
            Body=metadata_json,
        )

    # delete the original file
    s3_client.delete_object(Bucket=s3_bucket, Key=call_file_path)

    # delete the local file
    os.remove(local_file)


def generate_aws_safe_id(input_string):
    # Create a hash and get lowercase hex
    return hashlib.md5(input_string.encode()).hexdigest()


def attach_file_to_long_context_prompt(
    call_file_path: str, long_context_prompt_path: str, call_date: str
):
    """
    given a file path, attach it to the long context prompt
    """
    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")
    suffix = uuid4()

    current_dir = os.path.dirname(os.path.abspath(__file__))
    local_file = f"{current_dir}/{suffix}.txt"

    # download the long context prompt file if it exists
    try:
        s3_client.download_file(s3_bucket, long_context_prompt_path, local_file)
    except Exception as e:
        logger.info(f"File doesn't exist, creating it")

    # download the call transcript file
    call_transcript = s3_client.get_object(Bucket=s3_bucket, Key=call_file_path)
    file_content = call_transcript["Body"].read().decode("utf-8")

    text_to_write = f"""
    Transcript of call on date :- {call_date}

    {file_content}
    """

    # append data to file and upload to s3
    with open(local_file, "a") as f:
        f.write("\n" + text_to_write)
    s3_client.upload_file(local_file, s3_bucket, long_context_prompt_path)

    # delete the local file
    os.remove(local_file)


def attach_file_to_long_context_prompt_hubspot_entity(
    entity_file_path: str, long_context_prompt_path: str
):
    """
    given a file path, attach it to the long context prompt
    """
    s3_bucket = os.getenv("S3_GONG_CALL_BUCKET", "everstage-cpq-ai-assets")
    s3_client = boto3.client("s3")
    suffix = uuid4()

    current_dir = os.path.dirname(os.path.abspath(__file__))
    local_file = f"{current_dir}/{suffix}.txt"

    # download the long context prompt file if it exists
    try:
        s3_client.download_file(s3_bucket, long_context_prompt_path, local_file)
    except Exception as e:
        logger.info(f"File doesn't exist, creating it")

    hubspot_entity_file = s3_client.get_object(Bucket=s3_bucket, Key=entity_file_path)
    file_content = hubspot_entity_file["Body"].read().decode("utf-8")

    text_to_write = f"""
    {file_content}          
    """

    with open(local_file, "a") as f:
        f.write("\n" + text_to_write)
    s3_client.upload_file(local_file, s3_bucket, long_context_prompt_path)

    # delete the local file
    os.remove(local_file)
