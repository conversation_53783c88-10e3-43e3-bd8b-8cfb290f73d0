"""
contains accessors for all the models in the module
"""

from .models import (
    GongCall,
    KnowledgeBase,
    KnowledgeBaseMeta,
    MetadataBedrock,
    VectorCollection,
    VectorCollectionMeta,
)


class GongCallAccessor:
    """
    contains the accessor methods for the gong calls
    """

    def __init__(self, client_id: int, opportunity_id: str) -> None:
        self.client_id = client_id
        self.opportunity_id = opportunity_id

    def opportunity_aware(self):
        """
        returns gong calls that are for a single opportunity of a client
        """
        return GongCall.objects.filter(
            client_id=self.client_id, opportunity_id=self.opportunity_id
        )

    def get_gong_call_object(self, call_id: str):
        """
        returns the gong call object given the call ids
        """
        return self.opportunity_aware().filter(gong_call_id=call_id).first()

    def check_if_call_exists(self, call_id: str) -> bool:
        return self.opportunity_aware().filter(gong_call_id=call_id).exists()

    def check_if_call_is_loaded(self, call_id: str) -> bool:
        """
        retruns true if a gong call is already loaded to the vector database
        """
        gong_call = self.opportunity_aware().filter(gong_call_id=call_id).first()
        if not gong_call:
            return False

        return gong_call.is_call_loaded


class VectorCollectionAccessor:
    """
    accessor methods for vector collection
    """

    def __init__(self, client_id: int) -> None:
        self.client_id = client_id

    def client_aware(self):
        return VectorCollection.objects.filter(client_id=self.client_id)


class VectorCollectionMetaAccessor:
    """
    accessor method for vector collection
    """

    def get_vector_collection(self):
        return VectorCollectionMeta.objects.first()

    def check_collection_exists(self):
        return VectorCollectionMeta.objects.count() > 0


class KnowledgeBaseAccessor:
    """
    accessor methods for knowledge base creation
    """

    def __init__(self, client_id: int, opportunity_id: str) -> None:
        self.client_id = client_id
        self.opportunity_id = opportunity_id

    def opportunity_aware(self):
        """
        returns the knowledge base associated with the opportunity
        """
        return KnowledgeBase.objects.filter(
            client_id=self.client_id, opportunity_id=self.opportunity_id
        )

    def check_if_kb_exists_for_entity(self, entity_id: str) -> bool:
        return self.opportunity_aware().filter(entity_id=entity_id).exists()

    def get_knowledge_base_for_entity(self, entity_id: str):
        return self.opportunity_aware().filter(entity_id=entity_id).first()


class KnowledgeBaseMetaAccessor:
    """
    accessor method for the single knowledge base metadata creation
    """

    def entity_aware(self, entity_id: str):
        return KnowledgeBaseMeta.objects.filter(entity_id=entity_id).first()

    def check_if_kb_exists(self, entity_id) -> bool:
        """
        check if knowledge base exists
        """
        return self.entity_aware(entity_id=entity_id) is not None


class MetadataBedrockAccesor:
    """
    accessor methods for metadata mapping of event dates to their sequence numbers
    """

    def get_or_create_sequence_number_for_date(self, event_date: str):
        """
        get the sequence number for the event date or create one if it doesn't exists
        """

        event = MetadataBedrock.objects.filter(event_date=event_date).first()
        if not event:
            event = MetadataBedrock(event_date=event_date)
            event.save()

        return event.sequence_number
