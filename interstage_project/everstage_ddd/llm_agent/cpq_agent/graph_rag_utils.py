import json
import logging
import os

import boto3
import requests
from botocore.config import Config

logger = logging.getLogger(__name__)


def index_files(s3_path, input_type):
    """
    this function invokes the locally running flask application
    or the lambda function depending upon the execution envrionment
    """

    # if local environment make request to flask app , else call lambda function
    is_local_env = os.environ.get("ENV") == "LOCALDEV"
    # is_local_env = False
    print(f"is_local_env: {is_local_env}")
    if not is_local_env:
        logger.info("Invoking the lambda function for graph RAG indexing")
        lambda_client = boto3.client(
            "lambda",
            region_name=os.environ.get("LAMBDA_REGION", "ap-south-1"),
            config=Config(
                read_timeout=900, retries={"max_attempts": 0}, tcp_keepalive=True
            ),
        )
        function_name = "graph-rag-lambda"
        # payload = {"s3_path": s3_path} Ask anna and change
        payload = {"s3_path": s3_path, "task_type": "index", "input_type": input_type}
        print(f"Payload: {payload}")
        response = lambda_client.invoke(
            FunctionName=function_name,
            InvocationType="RequestResponse",
            Payload=json.dumps(payload),
        )
        response = json.loads(response["Payload"].read())
        print(f"Response: {response}")
        return response.get("graph_rag_indexed_parquet_path")
    else:
        logger.info(
            "Invoking the local flask application for lambda function execution"
        )
        # pass the args to the local flask app in json
        result = requests.post(
            "http://localhost:5052/graph-rag/index",
            json={"s3_path": s3_path, "input_type": input_type},
            timeout=1000,
        )
        print("request posted")
        if result.status_code == 200:
            data = result.json()
            print(f"Data: {data}")
            graph_rag_indexed_parquet_path = data.get("graph_rag_indexed_parquet_path")
            print(f"Graph RAG indexed parquet path: {graph_rag_indexed_parquet_path}")
            return graph_rag_indexed_parquet_path
        else:
            return None
