# ruff: noqa: S110, <PERSON><PERSON>105, <PERSON><PERSON>0912

import json
import logging
import os
import re
import uuid
from typing import Tuple

import requests
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage
from langsmith import traceable

from commission_engine.accessors.client_accessor import get_client
from everstage_ddd.llm_agent.agent_studio.services import (
    get_context_data,
    get_entity_details_with_tag,
    get_genui_with_session_id,
)
from everstage_ddd.llm_agent.agent_studio.types import ContextType, Entity, EntityType
from everstage_ddd.llm_agent.mannai_interface.llm_model import LLM_MODEL_PROVIDERS

MANNAI_ENDPOINT = os.getenv("MANNAI_ENDPOINT")
ES_BACKEND_API_KEY = os.getenv("ES_BACKEND_API_KEY")
ENV = os.getenv("ENV", "Testing")

logger = logging.getLogger(__name__)


def extract_json_from_string(response_string: str | None) -> dict | None:
    """
    Extracts a JSON object (as a Python dictionary) from a given response string.

    The function first looks for a JSON block enclosed in ```json ... ``` markdown.
    If found, its content is parsed. If the content is a valid JSON object,
    the corresponding Python dictionary is returned.

    If no such markdown block is found, or if the markdown block's content
    is not a valid JSON object (e.g., it's a JSON array, a primitive,
    malformed, or empty), the function then checks if the entire input string
    (after stripping whitespace) is a valid JSON object. If it is, the
    corresponding Python dictionary is returned.

    Args:
        response_string: The input string from which to extract the JSON object.

    Returns:
        A Python dictionary if a valid JSON object is extracted, otherwise None.
    """
    if not isinstance(response_string, str):
        return None

    # Attempt 1: Extract from ```json ... ``` markdown block
    # re.DOTALL allows '.' to match newline characters.
    # (.*?) is a non-greedy match for the content inside the backticks.
    markdown_pattern = r"```json\s*(.*?)\s*```"
    markdown_match = re.search(markdown_pattern, response_string, re.DOTALL)
    return_none = False
    if markdown_match:
        json_str_candidate = markdown_match.group(1).strip()
        # Ensure the extracted candidate is not empty
        if json_str_candidate:
            try:
                # Validate if the extracted string is valid JSON and a dictionary
                parsed_json = json.loads(json_str_candidate)
                if isinstance(parsed_json, dict):
                    return parsed_json
                else:
                    # Valid JSON, but not a dictionary (e.g., a list, string, number)
                    return_none = True
            except json.JSONDecodeError:
                # Content within markdown block is not valid JSON.
                return_none = True
        else:
            # Markdown block was found, but its content was empty after stripping.
            return_none = True
    else:
        # Attempt 2: No ```json``` block found.
        # Check if the entire stripped string itself is a JSON object.
        stripped_response = response_string.strip()

        if not stripped_response:
            # Input string was empty or contained only whitespace.
            return_none = True

        # For a dictionary, it must start with '{' and end with '}'
        if stripped_response.startswith("{") and stripped_response.endswith("}"):
            try:
                # Validate if the stripped string is valid JSON and a dictionary
                parsed_json = json.loads(stripped_response)
                if isinstance(parsed_json, dict):
                    return parsed_json
                else:
                    # This case is unlikely if it starts/ends with {} and parses
                    # successfully as standard JSON, as it would be an object.
                    # However, this check ensures it's explicitly a dictionary.
                    return_none = True
            except json.JSONDecodeError:
                # String looked like a JSON object by its ends, but is not valid.
                return_none = True
        else:
            # The stripped string does not start and end with curly braces,
            # so it's not considered a candidate for a JSON object here.
            return_none = True

    if return_none:
        return None

    # Explicit return for the case where return_none is False but no other return was executed
    return None


def get_meta(
    client_id: int,
    employee_email_id: str | None,
    session_id: str | None,
):
    """
    Returns user metadata for local usage and LangSmith metadata for tracing.
    """
    user_meta = {
        "client_id": client_id,
        "employee_email_id": employee_email_id,
    }

    client_name = get_client(client_id).name
    langsmith_meta = {
        "client_id": client_id,
        "client_name": client_name,
        "session_id": session_id,
        "employee_email_id": employee_email_id,
    }
    return user_meta, langsmith_meta


def get_entity_details(client_id: int, tag: str, entity_type: EntityType):
    technical_skill = False
    entity_details = get_entity_details_with_tag(
        entity_type=entity_type,
        client_id=client_id,
        tag=tag,
    )

    if not entity_details:
        technical_skill = True
        entity_details = get_entity_details_with_tag(
            entity_type=Entity.TECHNICALSKILL,
            client_id=client_id,
            tag=tag,
        )

    return entity_details, technical_skill


def execute_chain(  # noqa: PLR0913
    client_id: int,
    session_id: str | None,
    skill_tag: str,
    user_prompt: str,
    *,
    force_json: bool = False,
) -> Tuple[dict, str]:
    """
    Execute the local chain (no external Mannai call).
    """
    # For local chain, we only need minimal metadata. In practice, session_id can be used for logging/tracing.
    _, langsmith_meta = get_meta(
        client_id=client_id,
        employee_email_id=None,
        session_id=session_id,
    )

    entity_details = get_entity_details_with_tag(
        entity_type=Entity.SKILL,
        client_id=client_id,
        tag=skill_tag,
    )

    if not entity_details:
        entity_details_tech = get_entity_details_with_tag(
            entity_type=Entity.TECHNICALSKILL,
            client_id=client_id,
            tag=skill_tag,
        )

        if not entity_details_tech:
            return {"ai_generated_content": None}, "Failure"
        else:
            entity_details = entity_details_tech

    client_context_data = get_context_data(
        context_type=ContextType.CLIENT_CONTEXT.value,
        client_id=client_id,
    )

    client_context = client_context_data[0]["context"] if client_context_data else ""

    global_context_data = get_context_data(
        context_type=ContextType.GLOBAL_CONTEXT.value,
        client_id=0,
    )

    global_context = global_context_data[0]["context"] if global_context_data else ""

    business_prompt = entity_details.get("business_prompt", "")
    prepend_business_prompt = ""
    if business_prompt:
        prepend_business_prompt = f"""
        ### Business Prompt:
        {business_prompt}

        ### System Prompt:
        """

    system_prompt = prepend_business_prompt + entity_details.get(
        "core_prompt_prefix", ""
    )

    # Handle special cases here
    gen_ui_prompt = None
    if skill_tag == "GEN-UI":
        gen_ui_prompt, _ = get_genui_with_session_id(
            session_id=str(session_id),
            client_id=client_id,
        )
        system_prompt = (
            f"""
The prompt contains 2 sections. The first section contains the usecase dependent instructions. It sets the context on the expectation of the output.
The second part contains the primary system prompt.

### Usecase dependent instructions:
{gen_ui_prompt}

### Primary system prompt:\n
"""
            + system_prompt
        )

    model_name = entity_details["model_name"]
    model_provider = None
    model_kwargs = {}
    for provider, models in LLM_MODEL_PROVIDERS.items():
        model_keys = models.keys()
        if model_name in model_keys:
            model_provider = provider
            model_kwargs = models[model_name]
            break

    if global_context:
        system_prompt = (
            system_prompt
            + "\n\n"
            + f"""
        You are a part of the Everstage team, Everstage is a modern Sales Performance Management (SPM) software that helps businesses automate and optimize their sales commissions process. It replaces manual spreadsheets and legacy systems with a transparent, scalable solution that provides real-time visibility into commission data for both admins and sales teams..

        Company specific context:
        {global_context}
        """
        )
    if entity_details.get("include_client_context", False) and client_context:
        system_prompt = (
            system_prompt
            + "\n\n"
            + f"""
        Client specific context:
        {client_context}
        """
        )
    if global_context or client_context:
        system_prompt = (
            system_prompt
            + "\n\n"
            + " The company and client information is provided **for internal reference only**. Do not repeat or mention it in responses unless explicitly required to explain your reasoning."
        )
    llm = init_chat_model(
        model=model_name,
        model_provider=model_provider,
        **model_kwargs,
    )

    @traceable(
        name=entity_details["skill_name"],
        tags=[ENV, "Skill - Chain"],
        metadata=langsmith_meta,
        project_name=f"{ENV}-{get_client(client_id).name}",
    )
    def invoke_chain(llm, system_prompt, user_prompt):
        messages = [{"role": "system", "content": system_prompt}] + [
            HumanMessage(content=user_prompt)
        ]
        return llm.invoke(messages).content

    response = invoke_chain(
        llm=llm,
        system_prompt=system_prompt,
        user_prompt=user_prompt,
    )

    try:
        if force_json:
            response = extract_json_from_string(response)
            if response is None or not isinstance(response, dict):
                return {"ai_generated_content": None}, "Failure"
    except Exception:
        logger.exception("Failed to extract JSON from response, so storing as string")
        return {"ai_generated_content": None}, "Failure"

    if isinstance(response, dict):
        return {"ai_generated_content": {"json_response": response}}, "Success"

    return {"ai_generated_content": {"str_response": str(response)}}, "Success"


def execute_skill(  # noqa: PLR0913
    client_id: int,
    skill_tag: str,
    employee_email_id: str | None,
    user_prompt: str,
    *,
    llm_invocation: bool = False,
    message_id: str | None = None,
    # If session_id is None => local execution & no tool calls
    session_id: str | None = None,
    # These next fields are relevant only if session_id is present/ tool calling:
    files: dict | None = None,
    write_action_approval: bool = False,
    return_result: bool = True,
    thread_id: str | None = None,
) -> Tuple[dict, str]:
    """
    If session_id is None, execute skill locally (via `execute_chain`).
    Otherwise, call the Mannai endpoint.

    Return format:
    {
        "ai_generated_content": {
            "str_response": str,
            "json_response": dict,
            ...
        },
        "Success" or "Failure"
    }

    Select the appropriate structured items in agent studio.
    Access them with the same keys.
    """

    if not message_id:
        message_id = str(uuid.uuid4())

    entity_details, technical_skill = get_entity_details(
        client_id=client_id,
        tag=skill_tag,
        entity_type=Entity.SKILL,
    )

    if not entity_details:
        return {"ai_generated_content": None}, "Failure"

    show_tools = entity_details.get("show_tools", False)

    structure = entity_details.get("structure", [])
    structure_length = len(structure)

    # If session_id is None => local execution. Skip and go to next if tools_enabled
    standalone = False
    if not session_id or llm_invocation:
        if (
            not show_tools
            and structure_length == 1
            and structure[0] in ["str_response", "json_response"]
        ):
            return execute_chain(
                client_id=client_id,
                session_id=session_id,
                skill_tag=skill_tag,
                user_prompt=user_prompt,
                force_json=(structure_length == 1 and structure[0] == "json_response"),
            )
        else:
            session_id = str(uuid.uuid4())
            standalone = True
    client_context_data = get_context_data(
        context_type=ContextType.CLIENT_CONTEXT.value,
        client_id=client_id,
    )

    client_context = client_context_data[0]["context"] if client_context_data else ""

    global_context_data = get_context_data(
        context_type=ContextType.GLOBAL_CONTEXT.value,
        client_id=0,
    )

    global_context = global_context_data[0]["context"] if global_context_data else ""

    mannai_inference_endpoint = f"{MANNAI_ENDPOINT}/execute-skill"
    result = None

    try:
        timeout = 600 if return_result else 0.1
        result = requests.post(
            mannai_inference_endpoint,
            headers={"X-API-Key": ES_BACKEND_API_KEY},
            json={
                "client_id": client_id,
                "client_name": get_client(client_id).name,
                "employee_email_id": employee_email_id,
                "user_prompt": user_prompt,
                "client_context_prompt": client_context,
                "global_context_prompt": global_context,
                "session_id": session_id,
                "thread_id": thread_id,
                "files": files,
                "write_action_approval": write_action_approval,
                "skill_id": str(entity_details["skill_id"]),
                "skill_version": str(entity_details["version_id"]),
                "openai_thread_id": None,
                "standalone": standalone,
                "message_id": message_id,
                "return_structured": True,
                "technical_skill": technical_skill,
            },
            timeout=timeout,
        )
    except requests.exceptions.ReadTimeout:
        # We ignore read timeouts if return_result=False,
        # because it's expected to not wait for a response
        pass
    except Exception:
        logger.exception("Error calling Mannai")

    success_status_code = 200

    final_result = (
        result.json() if result and result.status_code == success_status_code else {}
    )
    return {"ai_generated_content": final_result}, "Success"


def execute_agent(  # noqa: PLR0913
    client_id: int,
    agent_tag: str,
    employee_email_id: str,
    *,
    message_id: str | None = None,
    # If session_id is None => local execution & no tool calls
    session_id: str | None = None,
    files: dict | None = None,
    # These next fields are relevant only if session_id is present/ tool calling:
    user_prompt: str | None = None,
    write_action_approval: bool = False,
    return_result: bool = True,
    thread_id: str | None = None,
) -> Tuple[dict, str]:
    """
    If session_id is None, execute skill locally (via `execute_chain`).
    Otherwise, call the Mannai endpoint.
    """

    if not message_id:
        message_id = str(uuid.uuid4())

    # If session_id is None => local execution. Skip and go to next if tools_enabled
    if not session_id:
        session_id = str(uuid.uuid4())

    entity_details, _ = get_entity_details(
        client_id=client_id,
        tag=agent_tag,
        entity_type=Entity.AGENT,
    )

    if not entity_details:
        return {"ai_generated_content": None}, "Failure"

    client_context_data = get_context_data(
        context_type=ContextType.CLIENT_CONTEXT.value,
        client_id=client_id,
    )

    client_context = client_context_data[0]["context"] if client_context_data else ""

    global_context_data = get_context_data(
        context_type=ContextType.GLOBAL_CONTEXT.value,
        client_id=0,
    )

    global_context = global_context_data[0]["context"] if global_context_data else ""

    mannai_inference_endpoint = f"{MANNAI_ENDPOINT}/execute-agent"
    result = None

    try:
        timeout = 600 if return_result else 0.1
        result = requests.post(
            mannai_inference_endpoint,
            headers={"X-API-Key": ES_BACKEND_API_KEY},
            json={
                "client_id": client_id,
                "client_name": get_client(client_id).name,
                "employee_email_id": employee_email_id,
                "user_prompt": user_prompt,
                "client_context_prompt": client_context,
                "global_context_prompt": global_context,
                "session_id": session_id,
                "thread_id": thread_id,
                "files": files,
                "write_action_approval": write_action_approval,
                "agent_id": str(entity_details["agent_id"]),
                "agent_version": str(entity_details["version_id"]),
                "openai_thread_id": None,
                "message_id": message_id,
                "return_structured": True,
            },
            timeout=timeout,
        )
    except requests.exceptions.ReadTimeout:
        # We ignore read timeouts if return_result=False,
        # because it's expected to not wait for a response
        pass
    except Exception:
        logger.exception("Error calling Mannai")

    final_result = result.json() if result else {}

    success_status_code = 200
    return {
        "ai_generated_content": final_result.get("str_response", None),
    }, (
        "Success" if result and result.status_code == success_status_code else "Failure"
    )


def execute_collection(  # noqa: PLR0913
    client_id: int,
    collection_tag: str,
    employee_email_id: str,
    *,
    message_id: str | None = None,
    # If session_id is None => local execution & no tool calls
    session_id: str | None = None,
    # These next fields are relevant only if session_id is present/ tool calling:
    files: dict | None = None,
    user_prompt: str | None = None,
    write_action_approval: bool = False,
    thread_id: str | None = None,
    return_result: bool = True,
) -> Tuple[dict, str]:
    """
    If session_id is None, execute skill locally (via `execute_chain`).
    Otherwise, call the Mannai endpoint.
    """
    # If session_id is None => local execution. Skip and go to next if tools_enabled
    if not session_id:
        session_id = str(uuid.uuid4())

    if not message_id:
        message_id = str(uuid.uuid4())

    entity_details, _ = get_entity_details(
        client_id=client_id,
        tag=collection_tag,
        entity_type=Entity.COLLECTION,
    )

    if not entity_details:
        return {"ai_generated_content": None}, "Failure"

    client_context_data = get_context_data(
        context_type=ContextType.CLIENT_CONTEXT.value,
        client_id=client_id,
    )

    client_context = client_context_data[0]["context"] if client_context_data else ""

    global_context_data = get_context_data(
        context_type=ContextType.GLOBAL_CONTEXT.value,
        client_id=0,
    )

    global_context = global_context_data[0]["context"] if global_context_data else ""

    mannai_inference_endpoint = f"{MANNAI_ENDPOINT}/execute-collection"
    result = None

    try:
        timeout = 600 if return_result else 0.1
        result = requests.post(
            mannai_inference_endpoint,
            headers={"X-API-Key": ES_BACKEND_API_KEY},
            json={
                "client_id": client_id,
                "client_name": get_client(client_id).name,
                "employee_email_id": employee_email_id,
                "user_prompt": user_prompt,
                "client_context_prompt": client_context,
                "global_context_prompt": global_context,
                "session_id": session_id,
                "thread_id": thread_id,
                "files": files,
                "write_action_approval": write_action_approval,
                "collection_id": str(entity_details["collection_id"]),
                "collection_version": str(entity_details["version_id"]),
                "openai_thread_id": None,
                "message_id": message_id,
                "return_structured": True,
            },
            timeout=timeout,
        )
    except requests.exceptions.ReadTimeout:
        # We ignore read timeouts if return_result=False,
        # because it's expected to not wait for a response
        pass
    except Exception:
        logger.exception("Error calling Mannai")

    final_result = result.json() if result else {}

    success_status_code = 200
    return {
        "ai_generated_content": final_result.get("str_response", None),
    }, (
        "Success" if result and result.status_code == success_status_code else "Failure"
    )
