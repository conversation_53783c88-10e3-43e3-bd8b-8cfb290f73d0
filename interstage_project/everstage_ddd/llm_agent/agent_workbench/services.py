# ruff: noqa: PLR0913, ERA001 Too many arguments in function definition (6 > 5)

import json
import logging
import os
import re
from datetime import datetime
from typing import Any

import pytz

from commission_engine.utils.general_data import RbacPermissions
from commission_engine.utils.s3_utils import S3Uploader
from common.everstage_supabase.services import get_supabase_client
from everstage_ddd.llm_agent.agent_studio.services import (
    get_agent_name,
    get_genui_with_session_id,
)
from everstage_ddd.llm_agent.agent_studio.types import Entity
from everstage_ddd.llm_agent.mannai_interface import (
    download_files_from_mannai_efs,
    execute_agent,
    execute_collection,
    execute_skill,
)
from spm.services.commission_plan_services import EmployeeAccessor
from spm.services.rbac_services import get_user_permissions

from .accessors import LLMAgentUserSessionAccessor, SessionMessageAccessor
from .models import LLMAgentUserSession, LLMAgentUserSessionMessages
from .types import (
    Sender,
    Session<PERSON>essage<PERSON>ist,
    SessionMessageMeta,
    SessionMessageMetaInit,
    UserAllSessionDetails,
    UserSessionMeta,
)

S3_PVT_ASSETS_BUCKET = os.environ.get("S3_PVT_ASSETS_BUCKET")

s3_uploader = S3Uploader(bucket_name=S3_PVT_ASSETS_BUCKET)


logger = logging.getLogger(__name__)


def check_if_admin_user(client_id: int, employee_email_id: str) -> bool:
    """
    checks if the employee is an admin user. currently, we have
    the following checks.
    1. if user's email ends with everstage.com they are an admin user
    """

    user_permissions = get_user_permissions(
        client_id=client_id, email_id=employee_email_id
    )["permissions"]
    return RbacPermissions.MANAGE_ALLADMINS.value in user_permissions


def check_if_session_exists_or_create_entry(client_id: int, session_id: str):
    user_session = LLMAgentUserSessionAccessor(
        client_id=client_id
    ).get_sessions_metadata_without_aware(session_id)

    return len(user_session) == 0


def update_session_messages(
    session_id,
    sender,
    message_content,
    thread_id,
    message_id,
    internal_tracking_message,
    approval_status,
):
    def replace_spaces(text: str) -> str:
        return re.sub(r"\s+", "_", text)

    input_files = message_content.get("file_urls", [])
    session_id_underscore = session_id.replace("-", "_")
    files = []
    for file in input_files:
        file_name = replace_spaces(file["name"])
        file_type = (
            "image"
            if file_name.endswith(".jpg") or file_name.endswith(".png")
            else "file"
        )
        files.append(
            {
                "url": f"everai_files_inputs/{session_id_underscore}/{file_type}s/{file_name}",
                "type": file_type,
            }
        )
    message_content_copy = message_content.copy()
    message_content_copy["file"] = files
    message_content_copy["file_urls"] = []

    approval_required = False
    if approval_status in ("APPROVED", "REJECTED"):
        approval_required = True

    user_session_message = LLMAgentUserSessionMessages(
        message_id=message_id,
        sender=sender,
        sent_time=datetime.now(tz=pytz.utc),
        sender_type=Sender.HUMAN.value,
        session_id=session_id,
        message_content=message_content_copy,
        thread_id=thread_id,
        internal_tracking_message=internal_tracking_message,
        approval_status=approval_status,
        approval_required=approval_required,
    )
    user_session_message.save()


def get_entity_info(employee_email_id, client_id, session_id):
    session_entity_info = LLMAgentUserSessionAccessor(
        client_id=client_id
    ).get_session_entity_info(employee_email_id, session_id)

    return session_entity_info


def update_session_information(client_id, session_id, sender, new_state=None):

    session_information = LLMAgentUserSessionAccessor(client_id=client_id)
    session = SessionMessageAccessor(session_id=session_id)
    session_messages_count = session.get_session_messages_count(internal_tracking=False)
    session_messages_users = session.get_users(internal_tracking=False)
    users = []

    for message in session_messages_users:
        if message["sender_type"] == Sender.HUMAN.value:
            user = {
                "name": EmployeeAccessor(client_id).get_employee_full_name(
                    message["sender"]
                ),
                "avatar": EmployeeAccessor(client_id).get_employee_profile_picture(
                    message["sender"]
                ),
                "sender_id": message["sender"],
                "type": Sender.HUMAN.value,
            }
            users.append(user)
        elif message["sender_type"] == Sender.SYSTEM.value:
            entity_information = session_information.get_session_entity_info(
                employee_email_id=sender, session_id=session_id
            )
            if entity_information:
                entity_type_dict = entity_information.get("entity_meta", {})
                entity_type = entity_type_dict.get("entity", Entity.AGENT)
                if entity_type == Entity.COLLECTION.value:
                    entity_type = Entity.AGENT
                else:
                    entity_type = Entity(entity_type)
            else:
                entity_type = Entity.AGENT

            entity_name, entity_avatar = get_agent_name(
                entity_type, client_id, message["sender"]
            )
            if entity_name != "SYSTEM":
                user = {
                    "name": entity_name,
                    "avatar": entity_avatar,
                    "sender_id": message["sender"],
                    "type": Sender.SYSTEM.value,
                }
                users.append(user)
            else:
                user = {
                    "name": "EverAI",
                    "avatar": entity_avatar,
                    "sender_id": message["sender"],
                    "type": Sender.SYSTEM.value,
                }
                users.append(user)

    unique_users = list({user["sender_id"]: user for user in users}.values())

    session_information.update_session_data(
        employee_email_id=sender,
        session_id=session_id,
        replies_count=session_messages_count - 1,
        users_in_thread={"users": unique_users},
    )

    if new_state:
        session_information.update_status(
            employee_email_id=sender,
            session_id=session_id,
            status=new_state,
        )

    # return the number of replies and the unique users in the thread
    return session_messages_count - 1, {"users": unique_users}


def check_session_belongs_to_user(
    client_id: int, employee_email_id: str, session_id: str
) -> bool:
    """
    in order to manipulate a user session(update/delete),
    check if session belongs to the user requesting the operation
    """

    user_session = LLMAgentUserSessionAccessor(
        client_id=client_id
    ).get_sessions_metadata(
        employee_email_id=employee_email_id, session_ids=[session_id]
    )
    return len(user_session) > 0


def get_user_sessions(
    client_id,
    employee_email_id,
    session_ids=None,
    sort_order="desc",
    offset_value=0,
    limit_value=10,
    pagination=False,
    entity_type=None,
    entity_id=None,
) -> dict:
    """
    If session ids are provided, it means the user is requesting for a shared chat. So used client_aware metadata instead of user_aware
    returns metadata of user sessions sorted in order given by sort_order
    ex :- {
        "session_name" : "",
        "session_description" : "",
        "last_interacted_time" : ""
    }
    """

    user_sessions_obj = LLMAgentUserSessionAccessor(client_id=client_id)

    user_sessions = (
        user_sessions_obj.get_sessions_metadata(
            employee_email_id=employee_email_id,
            sort_order=sort_order,
            offset_value=offset_value,
            limit_value=limit_value,
            pagination=pagination,
            entity_type=entity_type,
            entity_id=entity_id,
        )
        if session_ids is None
        else user_sessions_obj.get_sessions_metadata_client_aware(
            session_ids=session_ids, sort_order=sort_order
        )
    )

    user_sessions = list(user_sessions)

    all_user_sessions = list(
        map(lambda session: UserSessionMeta(**session), user_sessions)
    )

    session_details = UserAllSessionDetails(
        all_user_sessions=all_user_sessions
    ).model_dump()

    user_sessions = session_details.get("all_user_sessions")

    all_sessions_data = {}
    for session in user_sessions:
        replies_count, users_in_thread = update_session_information(
            client_id=client_id,
            session_id=session["session_id"],
            sender=employee_email_id,
        )
        message_content = get_session_messages(
            session["session_id"], all_messages_mode=True
        )
        if message_content:
            message_content["first_message"]["session_id"] = session["session_id"]
            message_content["first_message"]["message_id"] = message_content[
                "message_id"
            ]
            session["message_content"] = message_content["first_message"]
            session["last_interacted_time"] = message_content["last_message_time"]
            session["users_in_thread"] = users_in_thread
            session["replies_count"] = replies_count
            session["gen_ui_enabled"] = get_genui_with_session_id(
                session["session_id"], client_id
            )[1]

        all_sessions_data[str(session["session_id"])] = session

    return {
        "all_sessions_data": all_sessions_data,
        "current_user": {
            "name": EmployeeAccessor(client_id).get_employee_full_name(
                employee_email_id
            ),
            "avatar": EmployeeAccessor(client_id).get_employee_profile_picture(
                employee_email_id
            ),
            "senderId": employee_email_id,
            "type": Sender.HUMAN.value,
        },
    }


def add_user_session(
    client_id: int,
    employee_email_id: str,
    session_id: str,
    message_text: str | None,
    entity_info: dict[str, Any] | None,
) -> None:
    """
    create a new user session
    """

    session_data = {}
    session_data.update(session_id=session_id)
    session_data.update(client_id=client_id)
    session_data.update(employee_email_id=employee_email_id)
    session_data.update(status="todo")
    if entity_info:
        session_data.update(
            entity_meta={
                "entity": entity_info.get("entity", "collection"),
                "id": entity_info.get("id"),
                "version": entity_info.get("versions"),
                "tag": entity_info.get("tag", "CHAT-UI"),
            }
        )
    else:
        session_data.update(
            entity_meta={
                "entity": Entity.COLLECTION.value,
                "tag": "CHAT-UI",
            }
        )

    if message_text:
        # This is the first message in the thread
        # This task name will be updated in Kanban board. Name accordingly.
        content, _ = execute_skill(
            client_id,
            "CHAT-DESCRIPTION",
            employee_email_id,
            f"Task name to this: {message_text}",
        )
        logger.info(f"Kanban card content: {content}")
        if content["ai_generated_content"] != "Invalid Request":
            session_data.update(description=content["ai_generated_content"])
        else:
            session_data.update(description="Task")

    user_session = LLMAgentUserSession(**session_data)
    user_session.save()


def delete_user_session(
    client_id: int,
    employee_email_id: str,
    session_id: str,
    only_messages: bool = False,  # noqa: FBT001
):
    """
    delete the given user session and the conversation in the session
    """

    user_session = LLMAgentUserSessionAccessor(
        client_id=client_id
    ).get_sessions_metadata(
        employee_email_id=employee_email_id, session_ids=[session_id], values=False
    )

    if user_session:
        if not only_messages:
            user_session.delete()

        session_messages = SessionMessageAccessor(session_id=session_id).session_aware()
        session_messages.delete()


def get_session_messages(
    session_id: str, *, all_messages_mode: bool = False
) -> SessionMessageList | dict:
    """
    get offset:offset+limit messages in a session in desc
    order message sent timestamp.
    """

    messages = SessionMessageAccessor(session_id=session_id).get_session_messages(
        sort_order="desc", internal_tracking=False
    )
    if all_messages_mode:
        messages_list = list(
            map(lambda message: SessionMessageMetaInit(**message), messages)
        )
        # First message content and last message time
        return (
            {
                "first_message": messages_list[-1].model_dump(),
                "message_id": messages_list[-1].model_dump().get("message_id"),
                "last_message_time": messages_list[0].model_dump().get("sent_time"),
                "sender_type": messages_list[-1].model_dump().get("sender_type"),
            }
            if len(messages_list) > 0
            else {}
        )
    messages_list = list(map(lambda message: SessionMessageMeta(**message), messages))

    for message in messages_list:
        files = message.message_content.get("file", [])
        text = message.message_content.get("text")
        if text:
            try:
                message.message_content["text"] = json.loads(text)["str"]
            except (json.JSONDecodeError, KeyError):
                logger.info(f"{text} is not json parsable")

        for file in files:
            signed_file = s3_uploader.generate_presigned_url(
                file["url"], expiration=604800
            )
            file["url"] = signed_file

        message.message_content["file"] = files

    return SessionMessageList(all_messages_session=messages_list[::-1])


def update_like_status_for_message(
    session_id: str, message_id: str, like_status: bool  # noqa: FBT001
):
    """
    update the like status for a message
    """

    SessionMessageAccessor(session_id=session_id).set_like_status_for_message(
        message_id=message_id, like_status=like_status
    )


def _handle_approval_status(approval: dict) -> tuple[bool | None, str | None]:
    """Extract approval status and state from approval dict."""
    write_action_approval = None
    new_state = None

    if approval.get("approval_status") == "APPROVED":
        write_action_approval = True
    elif approval.get("approval_status") == "REJECTED":
        write_action_approval = False

    if approval.get("approval_status") is not None:
        new_state = "in_progress"

    return write_action_approval, new_state


# When a new message is updated in sessionMessages, mannai sends a request to spm and it adds it to Supabase client.
# Every session has an entry in the supabase table. When a new message is added, it is updated in the supabase table.
def update_session_on_message(
    client_id: int,
    employee_email_id: str,
    session_id: str,
    message_id: str,
    thread_id: str,
    message_content: dict,
    approval: dict,
    *,
    create: bool = False,
) -> None:
    """
    Update the session variables on new message.
    """

    entity_info = {
        "entity": "collection",
        "tag": "CHAT-UI",
    }
    if create:
        add_user_session(
            client_id=client_id,
            employee_email_id=employee_email_id,
            session_id=session_id,
            message_text=message_content.get("text"),
            entity_info=message_content.get("mention", {}),
        )
        if message_content.get("mention", {}):
            mention_dict = message_content.get("mention", {})
            entity_info = {
                "entity": mention_dict.get("entity", "collection"),
                "tag": mention_dict.get("tag", "CHAT-UI"),
            }
    else:
        entity_info = get_entity_info(
            employee_email_id=employee_email_id,
            client_id=client_id,
            session_id=session_id,
        ).get(
            "entity_meta",
            {
                "entity": "collection",
                "tag": "CHAT-UI",
            },
        )
    approval_status = approval.get("approval_status")

    write_action_approval = approval.get("approval_status", "REJECTED") == "APPROVED"
    new_state = "in_progress" if write_action_approval else None

    if write_action_approval:
        message_content["text"] = "APPROVED. You can go ahead and execute."

    update_session_messages(
        session_id=session_id,
        sender=employee_email_id,
        message_content=message_content,
        thread_id=thread_id,
        message_id=message_id,
        internal_tracking_message=False,
        approval_status=approval_status,
    )

    if not create:
        update_session_information(
            client_id=client_id,
            session_id=session_id,
            sender=employee_email_id,
            new_state=new_state,
        )

    logger.info("Messages updated for sessionId - %s", session_id)
    user_prompt = str(message_content.get("text"))
    additional_data = message_content.get("additional_data", None)
    if additional_data:
        user_prompt += (
            "\n" + "Here is the additional information:  " + str(additional_data)
        )
    files = message_content.get("file_urls", [])

    supabase = get_supabase_client()
    if create:
        supabase_data = {
            "task_name": "EVERAI_MESSAGE",
            "client_id": client_id,
            "task_id": session_id,
            "data": {
                "newMessagesFromSpm": True,
            },
        }
        supabase.insert(supabase_data).execute()
        logger.info("Supabase client created for sessionId - %s", session_id)
    else:
        supabase.update(
            {
                "data": {
                    "sessionId": str(session_id),
                }
            }
        ).eq("task_id", str(session_id)).execute()
        logger.info("Supabase client updated for sessionId - %s", session_id)

    if entity_info.get("entity") == "skill" and entity_info.get("tag"):
        execute_skill(
            client_id=client_id,
            skill_tag=entity_info.get("tag"),
            employee_email_id=employee_email_id,
            user_prompt=user_prompt,
            message_id=message_id,
            files=files,
            session_id=session_id,
            write_action_approval=write_action_approval,
            return_result=False,
            thread_id=session_id,
        )

    elif entity_info.get("entity") == "agent" and entity_info.get("tag"):
        execute_agent(
            client_id=client_id,
            agent_tag=entity_info.get("tag"),
            employee_email_id=employee_email_id,
            session_id=session_id,
            user_prompt=user_prompt,
            message_id=message_id,
            files=files,
            write_action_approval=write_action_approval,
            return_result=False,
            thread_id=session_id,
        )

    else:
        execute_collection(
            client_id=client_id,
            collection_tag="CHAT-UI",
            employee_email_id=employee_email_id,
            session_id=session_id,
            user_prompt=user_prompt,
            files=files,
            write_action_approval=write_action_approval,
            return_result=False,
            thread_id=session_id,
            message_id=message_id,
        )


def get_chain_of_thought(session_id: str, message_id: str | None) -> dict | None:
    """
    Get the chain of thoughts for a session
    """
    if message_id:
        chain_of_thought = SessionMessageAccessor(
            session_id=session_id
        ).get_chain_of_thought(message_id=message_id)
    else:
        chain_of_thought = SessionMessageAccessor(
            session_id=session_id
        ).get_chain_of_thought_with_session()
    return chain_of_thought


def download_message_files(
    session_id: str, message_id: str, file_type: str, file_name: str
):
    """
    Download the file from Mannai EFS
    """
    return download_files_from_mannai_efs(
        session_id=session_id,
        message_id=message_id,
        file_type=file_type,
        file_name=file_name,
    )


def update_genui_spec_for_message(
    session_id: str, message_id: str, genui_spec: dict
) -> None:
    """
    Update the GenUI specification for a specific message

    Args:
        message_id (str): The unique identifier of the message
        genui_spec (dict): The GenUI specification to update

    Returns:
        dict: The updated GenUI specification
    """
    try:
        message = SessionMessageAccessor(session_id=session_id).get_message_by_id(
            message_id=message_id
        )
        message["gen_ui_spec"] = genui_spec
        LLMAgentUserSessionMessages(**message).save()

    except Exception as exception:
        logger.exception("Error updating GenUI spec for message %s", message_id)
        raise ValueError from exception
