# pylint: disable=unused-argument

import logging

from django.http import FileResponse
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.accessors.client_accessor import check_everai_access
from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.llm_agent.agent_studio import (
    Entity,
    get_all_entities_with_versions,
    update_business_prompt,
)
from interstage_project.auth_utils import requires_scope

from .services import (
    check_if_session_exists_or_create_entry,
    check_session_belongs_to_user,
    delete_user_session,
    download_message_files,
    get_chain_of_thought,
    get_session_messages,
    get_user_sessions,
    update_genui_spec_for_message,
    update_like_status_for_message,
    update_session_information,
    update_session_on_message,
)

logger = logging.getLogger(__name__)


class HealthCheck(APIView):
    """
    for checking health of the llm skill product service
    """

    def get(self, request):
        """
        check if the llm skill product is up and running.
        """
        return Response(data="Success", status=status.HTTP_200_OK)


def check_if_everai_enabled_for_client(func):
    """
    decorator to check if the client has the llm skill feature enabled
    returns 403 if feature is not enabled
    """

    def check_access(self, request, **kwargs):
        """
        check and validate client feature
        """

        client_id = request.client_id
        if not check_everai_access(client_id=client_id):
            return Response(
                "This feature is not enabled for this client",
                status=status.HTTP_403_FORBIDDEN,
            )

        return func(self, request, **kwargs)

    return check_access


class UserSessions(APIView):
    """
    for fetching list of skill sessions for a user
    and creating new sessions.
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value),
        name="dispatch",
    )
    @check_if_everai_enabled_for_client
    def get(self, request):
        """
        get the list of meta data of sessions for a single user sorted in
        desc order of last interaction time.
        """
        client_id = request.client_id
        employee_email_id = str(request.user)
        entity_type = request.query_params.get("entityType", None)
        entity_id = request.query_params.get("entityId", None)
        user_sessions = get_user_sessions(
            client_id=client_id,
            employee_email_id=employee_email_id,
            offset_value=int(request.query_params.get("offsetValue", 0)),
            limit_value=int(request.query_params.get("limitValue", 10)),
            pagination=True,
            entity_type=entity_type,
            entity_id=entity_id,
        )
        return Response(data=user_sessions, status=status.HTTP_200_OK)


class UserSessionManipulate(APIView):
    """
    for the management of a given user session
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value),
        name="dispatch",
    )
    def get(self, request, **kwargs):
        """
        Get the session data for a given session.
        This endpoint is used to get the session data when the shared chat is opened.
        """
        client_id = request.client_id
        employee_email_id = str(request.user)
        session_id = kwargs["session_id"]

        if session_id:
            update_session_information(
                client_id=client_id,
                session_id=session_id,
                sender=employee_email_id,
            )

        session_data = get_user_sessions(
            client_id=client_id,
            employee_email_id=employee_email_id,
            session_ids=[session_id],
        )
        return Response(data=session_data, status=status.HTTP_200_OK)

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value),
        name="dispatch",
    )
    @check_if_everai_enabled_for_client
    def delete(self, request, **kwargs):
        """
        delete a user session
        """
        client_id = request.client_id
        employee_email_id = str(request.user)
        session_id = kwargs["session_id"]
        only_messages = request.query_params.get("onlyMessages") == "true"

        if not check_session_belongs_to_user(
            client_id=client_id,
            employee_email_id=employee_email_id,
            session_id=session_id,
        ):
            return Response(
                data="User does not have permission to delete the session",
                status=status.HTTP_403_FORBIDDEN,
            )

        delete_user_session(
            client_id=client_id,
            employee_email_id=employee_email_id,
            session_id=session_id,
            only_messages=only_messages,
        )
        return Response(data="Success", status=status.HTTP_200_OK)


class UserSessionMessages(APIView):
    """
    APIView for fetching list of messages sent in a user session
    sorted in desc order of timestamp.
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value),
        name="dispatch",
    )
    @check_if_everai_enabled_for_client
    def get(self, request, **kwargs):
        """
        get user session messages
        """
        client_id = request.client_id
        employee_email_id = str(request.user)
        session_id = kwargs["session_id"]

        if not check_session_belongs_to_user(
            client_id=client_id,
            employee_email_id=employee_email_id,
            session_id=session_id,
        ):
            return Response(
                data="User does not have permission to access the session",
                status=status.HTTP_403_FORBIDDEN,
            )

        messages = get_session_messages(session_id=session_id).model_dump()

        return Response(data=messages, status=status.HTTP_200_OK)


class UserSessionPrompt(APIView):
    """
    generate response for a user prompt
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value),
        name="dispatch",
    )
    @check_if_everai_enabled_for_client
    def post(self, request, **kwargs):
        """
        1. invokes mannai service to get the skill response
        2. persists the messages to session_messages table
        3. returns the message back to the user
        """
        client_id = request.client_id
        employee_email_id = str(request.user)
        session_id = kwargs["session_id"]
        message_id = request.data.get("message_id", None)
        thread_id = request.data.get("thread_id", None)
        message_content = request.data.get("message_content")
        create = False
        if check_if_session_exists_or_create_entry(
            client_id=client_id,
            session_id=session_id,
        ):
            create = True

        elif not check_session_belongs_to_user(
            client_id=client_id,
            employee_email_id=employee_email_id,
            session_id=session_id,
        ):
            return Response(
                data="User does not have permission to access the session",
                status=status.HTTP_403_FORBIDDEN,
            )

        approval_message_id = request.data.get("approval_message_id", None)
        approval_status = request.data.get("approval_status", None)
        approval_required = request.data.get("approval_required", False)
        approval = {
            "approval_message_id": approval_message_id,
            "approval_status": approval_status,
            "approval_required": approval_required,
        }

        update_session_on_message(
            client_id=client_id,
            session_id=session_id,
            employee_email_id=employee_email_id,
            message_id=message_id,
            thread_id=thread_id,
            message_content=message_content,
            create=create,
            approval=approval,
        )

        return Response(data="Success", status=status.HTTP_200_OK)


class Feedback(APIView):
    """
    API endpoint to update the core prefix on feedback
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value),
        name="dispatch",
    )
    def put(self, request, **kwargs):
        """
        update the core prefix on feedback
        """

        session_id = request.data["session_id"]
        feedback = request.data["feedback"]
        message_id = request.data["message_id"]
        like_status = request.data.get("like_status")
        update_like_status_for_message(session_id, message_id, like_status)

        # if the feedback is null, then the prompt is not updated
        if feedback:
            # Update prompt on feedback
            pass

        return Response(
            data="Feedback updated successfully",
            status=status.HTTP_200_OK,
        )


class GetChainOfThought(APIView):
    """
    API endpoint to get the chain of thought
    """

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_AGENT_WORKBENCH.value]),
        name="dispatch",
    )
    def get(self, request, **kwargs):
        """
        returns the chain of thought
        """
        session_id = kwargs["session_id"]
        message_id = kwargs.get("message_id", None)
        chain_of_thought = get_chain_of_thought(session_id, message_id)

        return Response(data=chain_of_thought, status=status.HTTP_200_OK)


class GetAllEntitiesByVersion(APIView):
    """
    API endpoint to get all entities with all versions
    """

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_AGENT_WORKBENCH.value]),
        name="dispatch",
    )
    def get(self, request, **kwargs):
        """
        returns all entities with all versions
        """
        entity_type = Entity(kwargs["entity_type"])
        client_id = request.client_id
        entities = get_all_entities_with_versions(entity_type, client_id)
        if entity_type == Entity.SKILL:
            technical_entities = get_all_entities_with_versions(
                Entity.TECHNICALSKILL, client_id
            )
            for entity in technical_entities["entity"]:
                entity["technical"] = True
            for entity in entities["entity"]:
                entity["technical"] = False
            entities["entity"] += technical_entities["entity"]
        return Response(data=entities, status=status.HTTP_200_OK)


class DownloadMessageFile(APIView):
    """
    API endpoint to download message file
    """

    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_AGENT_WORKBENCH.value]),
        name="dispatch",
    )
    def get(self, request, **kwargs):
        """
        returns all entities with all versions
        """
        session_id = request.query_params.get("session_id")
        message_id = request.query_params.get("message_id")
        file_type = request.query_params.get("file_type")
        file_name = request.query_params.get("file_name")
        file_response = download_message_files(
            session_id, message_id, file_type, file_name
        )
        success_status_code = 200
        if file_response.status_code == success_status_code:
            return FileResponse(
                file_response,
                as_attachment=True,
                filename=file_name,
            )
        else:
            return Response(
                data={"message": "Failed to download file"},
                status=status.HTTP_400_BAD_REQUEST,
            )


def _validate_genui_fields(message_id, session_id):
    """Helper function to validate required fields for GenUI spec update."""
    if not message_id or not session_id:
        logger.error("Message ID and session ID are required")
        raise ValueError from None


class GenUISpecView(APIView):
    """
    View for managing GenUI specifications for a specific message
    """

    @method_decorator(requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value))
    def put(self, request, **kwargs):
        """
        Update the GenUI specification for a specific message
        """
        try:
            gen_ui_spec = request.data.get("gen_ui_spec")
            session_id = request.data.get("session_id")
            message_id = request.data.get("message_id")

            _validate_genui_fields(message_id, session_id)

            update_genui_spec_for_message(session_id, message_id, gen_ui_spec)
            return Response(
                data={
                    "message": "GenUI specification updated successfully",
                },
                status=status.HTTP_200_OK,
            )
        except Exception:
            logger.exception("Error updating GenUI spec for message")
            return Response(
                data={"error": "Unable to update GenUI specification"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UpdateEntityPromptView(APIView):
    """
    Update the business prompt for an entity.
    """

    @method_decorator(requires_scope(RbacPermissions.MANAGE_AGENT_WORKBENCH.value))
    def put(self, request):
        """
        Update the business prompt for an entity.

        Args:
            request: The HTTP request containing the new prompt

        Returns:
            Response: Status code and response data
        """
        entity_id = request.data.get("entity_id")
        business_prompt = request.data.get("business_prompt")
        entity_type = Entity(request.data.get("entity_type"))

        update_business_prompt(
            request.client_id,
            entity_type,
            entity_id,
            business_prompt,
        )
        return Response(
            {"detail": "Business prompt updated successfully"},
            status=status.HTTP_200_OK,
        )
