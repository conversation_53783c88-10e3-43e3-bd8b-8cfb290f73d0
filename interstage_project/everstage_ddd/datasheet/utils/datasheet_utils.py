import functools
import logging
import traceback
from functools import wraps

from django.db import IntegrityError, transaction
from django.http import JsonResponse
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

from commission_engine.services.client_feature_service import has_feature
from commission_engine.utils.general_data import DATASHEET_FILTER_TYPE
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.datasheet.models.datasheet_migration_models import (
    DatasheetMigrationTracker,
)
from everstage_ddd.datasheet.services.datasheet_view import datasheet_view_delete
from interstage_project.auth_utils import ninja_rbac_scope
from spm.services.datasheet_variable_services import get_datasheet_variables

logger = logging.getLogger(__name__)


def find_changed_keys_in_datasheet_meta(updated_dict, existing_dict):
    changed_keys = []
    updated_dict.pop("datasheet_id")

    changed_keys = [
        key
        for key in updated_dict
        if key in existing_dict
        and updated_dict[key] is not None
        and updated_dict[key] != existing_dict[key]
    ]

    return changed_keys


def log_exception(
    exception,  # noqa: ARG001
    code="INTERNAL_SERVER_ERROR",
    message="Internal Server Error",
    payload=None,
):
    """Log the exception with structured information."""
    if payload is None:
        payload = {}
    logger.exception(message, extra={"code": code, "payload": payload})


def build_error_response(exception, *, use_json_response=True):
    """Build a structured error response from an exception.

    Parameters:
        exception (Exception): The exception that occurred.
        use_json_response (bool, optional): Determines the type of response to return.
            Defaults to True, returning a JsonResponse if True, else a DRF Response.
    """
    code = getattr(exception, "code", "UNSPECIFIED")
    message = getattr(exception, "message", "Internal Server Error")
    payload = getattr(exception, "payload", {})
    status_code = getattr(exception, "status", status.HTTP_500_INTERNAL_SERVER_ERROR)

    error_response = {"error": {"code": code, "payload": payload, "message": message}}

    if use_json_response:
        return JsonResponse(error_response, status=status_code)
    return Response(data=error_response, status=status_code)


def handle_ninja_rbac(request, permission):
    is_allowed = ninja_rbac_scope(request, permission)
    if not is_allowed:
        raise DatasheetException(
            code="FORBIDDEN",
            status=status.HTTP_403_FORBIDDEN,
            message="You are not allowed to perform this action.",
        )


def handle_datasheet_version(client_id, current_version):
    if (current_version == "v2") != bool(
        has_feature(client_id, "show_data_sources_v2")
    ):
        raise DatasheetException(
            code="FORBIDDEN",
            status=status.HTTP_403_FORBIDDEN,
            message="Datasheet version is not in sync with client version",
        )


def ninja_datasheet_exception(request, exp):  # noqa: ARG001
    if isinstance(exp, DatasheetException):
        log_exception(exp)
        return build_error_response(exp, use_json_response=True)

    log_exception(exp, message="A server error occurred.")
    return build_error_response(exp, use_json_response=True)


def handle_datasheet_exception(func):
    """
    Decorator to handle exceptions raised by datasheet view methods, returning an appropriate response.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DatasheetException as exp:
            log_exception(exp)
            return build_error_response(exp, use_json_response=False)
        except Exception as exp:  # noqa: BLE001
            log_exception(exp, message="A server error occurred.")
            return build_error_response(exp, use_json_response=False)

    return wrapper


def handle_datasheet_migration_failure(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            client_id, datasheet_id = extract_ids(args, kwargs)
            if has_feature(client_id, "datasheet_v2_migration"):
                raise DatasheetException(
                    code="ERROR",
                    message=f"Datasheet v2 migration failed for {datasheet_id}",
                ) from e
            log_error(func, e, client_id, datasheet_id)
            create_migration_tracker(func, e, client_id, datasheet_id)

    return wrapper


def extract_ids(args, kwargs):
    client_id = datasheet_id = None
    if args and hasattr(args[0], "client_id"):
        client_id = args[0].client_id
        datasheet_id = getattr(args[0], "datasheet_id", None)

    client_id = kwargs.get("client_id", client_id) or (args[0] if args else None)
    datasheet_id = kwargs.get("datasheet_id", datasheet_id) or (
        args[1] if len(args) > 1 else None
    )

    return client_id, datasheet_id


def log_error(func, e, client_id, datasheet_id):
    logger.exception(
        f"Datasheet migration v2 error for client_id: {client_id}, datasheet_id: {datasheet_id}"
    )
    logger.exception(f"Function: {func.__name__}, Exception: {str(e)}")
    logger.exception("Stack Trace:", exc_info=True)


def create_migration_tracker(func, e, client_id, datasheet_id):
    stack_trace = traceback.format_exc()
    additional_details = {"error_message": str(e), "stack_trace": stack_trace}
    logger.info(f"Creating DatasheetMigrationTracker: {additional_details}")

    DatasheetMigrationTracker.objects.create(
        client_id=client_id,
        datasheet_id=datasheet_id,
        failed_function=func.__name__,
        additional_details=additional_details,
    )


@handle_datasheet_migration_failure
def handle_datasheet_view_delete_from_v1(
    client_id, datasheet_id, view_id, current_user
):
    datasheet_view_delete(
        client_id=client_id,
        datasheet_id=datasheet_id,
        view_id=view_id,
        current_user=current_user,
    )


@handle_datasheet_migration_failure
@transaction.atomic
def convert_v1_ui_filter_spec_to_v2(  # noqa: PLR0915 PLR0912
    client_id, datasheet_id, filter_id, ignore_v2_enabled=False
):
    from commission_engine.accessors.databook_accessor import DatasheetFilterAccessor
    from everstage_ddd.datasheet.models import DatasheetView, DatasheetViewFilter

    if not has_feature(client_id, "datasheet_v2") and not ignore_v2_enabled:
        logger.info(
            "Datasheet v2 not enable for client %s; Skipping view update/create",
            client_id,
        )
        return

    logger.info(f"BEGIN: Converting UI filters for datasheet {datasheet_id}")
    filter_rec = (
        DatasheetFilterAccessor(client_id)
        .client_latest_kd_aware()
        .filter(
            datasheet_id=datasheet_id, config_type=DATASHEET_FILTER_TYPE.FILTER.value
        )
        .filter(filter_id=filter_id)
        .first()
    )
    variables = get_datasheet_variables(
        client_id,
        filter_rec.databook_id,
        filter_rec.datasheet_id,
        ["system_name", "variable_id"],
    )
    variable_id_map = {}

    for variable in variables:
        variable_id_map[variable["system_name"]] = variable["variable_id"]

    if filter_rec:
        expressions = filter_rec.filter_list
        token_list = []
        for index, expr in enumerate(expressions):
            if expr["operator"] in ["IS_EMPTY", "IS_NOT_EMPTY"]:
                operator = "IsEmpty" if expr["operator"] == "IS_EMPTY" else "IsNotEmpty"
                token_list.extend(
                    [
                        {
                            "token": {
                                "key": f"{operator}({expr['col_display_name']})",
                                "args": [
                                    {
                                        "token": {
                                            "key": expr["col_name"],
                                            "name": expr["col_display_name"],
                                            "system_name": expr["col_name"],
                                            "variable_id": variable_id_map.get(
                                                expr["col_name"]
                                            ),
                                        },
                                        "token_type": "DATASHEET_VARIABLES",
                                    }
                                ],
                                "name": f"{operator}({expr['col_display_name']})",
                                "type": "VARIABLE",
                                "data_type": "Boolean",
                                "function_name": operator,
                                "token_category": "DYNAMIC",
                            },
                            "token_type": "FUNCTIONS",
                        }
                    ]
                )
            elif expr["operator"] in [
                "IS_IN_CURRENT_MONTH",
                "IS_IN_LAST_MONTH",
                "IS_IN_CURRENT_FISCAL_QUARTER",
                "IS_IN_LAST_FISCAL_QUARTER",
                "IS_IN_CURRENT_FISCAL_HALFYEAR",
                "IS_IN_LAST_FISCAL_HALFYEAR",
                "IS_IN_CURRENT_FISCAL_YEAR",
                "IS_IN_LAST_FISCAL_YEAR",
            ]:
                params = expr["operator"].split("_")
                if len(params) < 5:  # noqa: PLR2004
                    params.insert(3, "CALENDAR")
                params[4] = "HALF-YEAR" if params[4] == "HALFYEAR" else params[4]
                token_list.extend(
                    [
                        {
                            "token": {
                                "key": (
                                    f"DateIsIn({expr['col_display_name']}, {params[2].lower().capitalize()}, {params[4].upper()}"
                                    + f", {params[3].lower().capitalize()})"
                                    if params[4].lower() != "month"
                                    else ")"
                                ),
                                "args": [
                                    {
                                        "token": {
                                            "key": expr["col_name"],
                                            "name": expr["col_display_name"],
                                            "system_name": expr["col_name"],
                                            "variable_id": variable_id_map.get(
                                                expr["col_name"]
                                            ),
                                        },
                                        "token_type": "DATASHEET_VARIABLES",
                                    },
                                    params[2].lower().capitalize(),
                                    params[4].lower().capitalize().replace("-", ""),
                                    params[3].lower().capitalize(),
                                ],
                                "name": f"DateIsIn({expr['col_display_name']}, {params[2].lower().capitalize()}, {params[4].upper()}, {params[3].lower().capitalize()})",
                                "type": "VARIABLE",
                                "data_type": "Boolean",
                                "function_name": "DateIsIn",
                                "token_category": "DYNAMIC",
                            },
                            "token_type": "FUNCTIONS",
                        }
                    ]
                )
            elif expr["operator"] in [
                "STARTS_WITH",
                "ENDS_WITH",
                "CONTAINS",
                "DOES_NOT_CONTAINS",
            ]:
                operator_v1_v2_map = {
                    "STARTS_WITH": "StartsWith",
                    "ENDS_WITH": "EndsWith",
                    "CONTAINS": "Contains",
                    "DOES_NOT_CONTAINS": "NotContains",
                }
                operator_v1 = expr["operator"]
                operator_v2 = operator_v1_v2_map[operator_v1]
                token_list.extend(
                    [
                        {
                            "token": {
                                "key": f"{operator_v2}({expr['col_display_name']}, {expr['value']})",
                                "args": [
                                    {
                                        "token": {
                                            "key": expr["col_name"],
                                            "name": expr["col_display_name"],
                                            "system_name": expr["col_name"],
                                            "variable_id": variable_id_map.get(
                                                expr["col_name"]
                                            ),
                                        },
                                        "token_type": "DATASHEET_VARIABLES",
                                    },
                                    {
                                        "token": {
                                            "key": expr["value"],
                                            "args": [
                                                expr["data_type"],
                                                expr["value"],
                                            ],
                                            "name": expr["value"],
                                            "data_type": expr["data_type"],
                                        },
                                        "token_type": "CONSTANT_VARIABLES",
                                    },
                                ],
                                "name": f"{operator_v2}({expr['col_display_name']}, {expr['value']})",
                                "type": "VARIABLE",
                                "data_type": "Boolean",
                                "function_name": operator_v2,
                                "token_category": "DYNAMIC",
                            },
                            "token_type": "FUNCTIONS",
                        }
                    ]
                )
            elif expr["operator"] in ["IN", "NOT_IN", "NOTIN"]:
                operator = "In" if expr["operator"] == "IN" else "NotIn"
                col_name = expr["col_name"]
                col_display_name = expr["col_display_name"]
                operator = expr["operator"]
                data_type = expr["data_type"]
                import ast

                value = ast.literal_eval(str(expr["value"]))
                name = value
                if expr["data_type"] in ["Integer", "String", "Email"] and isinstance(
                    value, list
                ):
                    data_type = (
                        "IntArray" if expr["data_type"] == "Integer" else "StringArray"
                    )
                    values_set = (
                        [ast.literal_eval(val) for val in value]
                        if data_type == "IntArray"
                        else value
                    )
                    value = values_set
                    name = "Array - " + ", ".join(map(str, value))

                token_list.extend(
                    [
                        {
                            "token": {
                                "key": col_name,
                                "name": col_display_name,
                                "system_name": col_name,
                                "variable_id": variable_id_map.get(col_name),
                            },
                            "token_type": "DATASHEET_VARIABLES",
                        },
                        {
                            "token": {"key": operator, "name": operator},
                            "token_type": "OPERATORS",
                        },
                        {
                            "token": {
                                "key": value,
                                "args": [data_type, value],
                                "name": name,
                                "data_type": data_type,
                            },
                            "token_type": "CONSTANT_VARIABLES",
                        },
                    ]
                )
            else:
                col_name = expr["col_name"]
                col_display_name = expr["col_display_name"]
                operator = expr["operator"]
                value = expr["value"]
                data_type = expr["data_type"]
                token_list.extend(
                    [
                        {
                            "token": {
                                "key": col_name,
                                "name": col_display_name,
                                "system_name": col_name,
                                "variable_id": variable_id_map.get(col_name),
                            },
                            "token_type": "DATASHEET_VARIABLES",
                        },
                        {
                            "token": {"key": operator, "name": operator},
                            "token_type": "OPERATORS",
                        },
                        {
                            "token": {
                                "key": str(value),
                                "args": [data_type, str(value)],
                                "name": str(value),
                                "data_type": data_type,
                            },
                            "token_type": "CONSTANT_VARIABLES",
                        },
                    ]
                )
            if index < len(expressions) - 1:
                token_list.append(
                    {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"}
                )

        filter_view_dict = {
            "filter_id": filter_id,
            "filter_data": token_list,
            "client_id": filter_rec.client.client_id,
            "knowledge_begin_date": filter_rec.knowledge_begin_date,
            "is_deleted": False,
            "knowledge_end_date": None,
            "additional_details": filter_rec.additional_details,
        }
        datasheet_view = {
            "client_id": filter_rec.client.client_id,
            "datasheet_id": filter_rec.datasheet_id,
            "filter_id": filter_id,
            "knowledge_begin_date": filter_rec.knowledge_begin_date,
            "is_deleted": False,
            "knowledge_end_date": None,
            "name": filter_rec.filter_name,
            "last_updated_by": filter_rec.last_updated_by,
            "created_at": filter_rec.knowledge_begin_date,
            "additional_details": filter_rec.additional_details,
            "view_id": filter_rec.filter_id,
        }
        datasheet_view_filter_object = DatasheetViewFilter(**filter_view_dict)
        datasheet_view_object = DatasheetView(**datasheet_view)

        # check if the view filter already exists, if true, then invalidate the existing view filter
        existing_view_filter = DatasheetViewFilter.objects.filter(
            filter_id=filter_id,
            knowledge_end_date__isnull=True,
            client_id=client_id,
        ).first()

        if existing_view_filter:
            existing_view_filter.knowledge_end_date = timezone.now()
            existing_view_filter.save()

        # create the new view filter
        datasheet_view_filter_object.save()

        # Check for existing view by view_id; Useful when we're renaming filter
        existing_view_by_id = DatasheetView.objects.filter(
            view_id=filter_id,
            knowledge_end_date__isnull=True,
            is_deleted=False,
            datasheet_id=datasheet_id,
            client_id=client_id,
        ).first()

        # Check for existing view by name; Useful when we're updating filter data
        existing_view_by_name = DatasheetView.objects.filter(
            name=filter_rec.filter_name,
            datasheet_id=datasheet_id,
            knowledge_end_date__isnull=True,
            is_deleted=False,
            client_id=client_id,
        ).first()

        existing_view = existing_view_by_id or existing_view_by_name

        # Invalidate existing view by view_id or name
        if existing_view:
            existing_view.knowledge_end_date = timezone.now()
            existing_view.is_deleted = True
            existing_view.save()

        # create the new view
        datasheet_view_object.save()

        if existing_view:
            logger.info(
                "Updating existing view %s for %s", existing_view.name, client_id
            )
        else:
            logger.info(
                "Creating view %s for %s", datasheet_view_object.name, client_id
            )

        logger.info(f"END: Converting UI filters for datasheet {datasheet_id}")
    else:
        logger.info(f"No filters found for datasheet {datasheet_id}")


@handle_datasheet_migration_failure
@transaction.atomic
def convert_v1_pivot_to_v2(client_id, datasheet_id, filter_id, ignore_v2_enabled=False):
    from commission_engine.accessors.databook_accessor import DatasheetFilterAccessor
    from everstage_ddd.datasheet.models import DatasheetView, DatasheetViewPivot

    if not has_feature(client_id, "datasheet_v2") and not ignore_v2_enabled:
        logger.info(
            "Datasheet v2 not enable for client %s; Skipping view update/create",
            client_id,
        )
        return

    logger.info("BEGIN: Converting pivot for datasheet %s", datasheet_id)
    filter_rec = (
        DatasheetFilterAccessor(client_id)
        .client_latest_kd_aware()
        .filter(
            datasheet_id=datasheet_id,
            config_type=DATASHEET_FILTER_TYPE.PIVOT.value,
            filter_id=filter_id,
        )
        .first()
    )

    if not filter_rec:
        logger.info("No pivot found for datasheet %s", datasheet_id)
        return

    pivot_data = filter_rec.pivot_object

    all_used_variables = set()
    all_used_variables.update(pivot_data["index"])
    all_used_variables.update(pivot_data["values"])
    all_used_variables.update(pivot_data["columns"])
    all_used_variables.update(pivot_data["aggfunc"].keys())

    # create a variable_id map for all used columns
    variable_id_map = {}

    variables = get_datasheet_variables(
        client_id,
        filter_rec.databook_id,
        filter_rec.datasheet_id,
        ["system_name", "variable_id"],
    )

    for variable in variables:
        if variable["system_name"] in all_used_variables:
            variable_id_map[variable["system_name"]] = variable["variable_id"]

    pivot_data["variable_id_map"] = variable_id_map

    # Create DatasheetViewPivot object
    pivot_view_dict = {
        "pivot_id": filter_id,
        "pivot_data": pivot_data,
        "client_id": client_id,
        "is_deleted": False,
    }
    datasheet_view_pivot_object = DatasheetViewPivot(**pivot_view_dict)

    # Create DatasheetView object
    datasheet_view = {
        "client_id": client_id,
        "datasheet_id": filter_rec.datasheet_id,
        "pivot_id": filter_id,
        "knowledge_begin_date": filter_rec.knowledge_begin_date,
        "is_deleted": False,
        "knowledge_end_date": None,
        "name": filter_rec.filter_name,
        "last_updated_by": filter_rec.last_updated_by,
        "created_at": filter_rec.knowledge_begin_date,
        "additional_details": filter_rec.additional_details,
        "view_id": filter_rec.filter_id,
    }
    datasheet_view_object = DatasheetView(**datasheet_view)

    # Check if the view pivot already exists, if true, then invalidate the existing view pivot
    existing_view_pivot = DatasheetViewPivot.objects.filter(
        pivot_id=filter_id,
        is_deleted=False,
        client_id=client_id,
    ).first()

    if existing_view_pivot:
        existing_view_pivot.is_deleted = True
        existing_view_pivot.save()

    # Create the new view pivot
    datasheet_view_pivot_object.save()

    # Check for existing view by view_id; Useful when we're renaming pivot
    existing_view_by_id = DatasheetView.objects.filter(
        view_id=filter_id,
        datasheet_id=datasheet_id,
        knowledge_end_date__isnull=True,
        is_deleted=False,
        client_id=client_id,
    ).first()

    # Check for existing view by name; Useful when we're updating pivot data
    existing_view_by_name = DatasheetView.objects.filter(
        name=filter_rec.filter_name,
        datasheet_id=datasheet_id,
        knowledge_end_date__isnull=True,
        is_deleted=False,
        client_id=client_id,
    ).first()

    existing_view = existing_view_by_id or existing_view_by_name

    if existing_view:
        existing_view.knowledge_end_date = timezone.now()
        existing_view.is_deleted = True
        existing_view.save()

    # Create the new view
    datasheet_view_object.save()

    if existing_view:
        logger.info("Updating existing view %s for %s", existing_view.name, client_id)
    else:
        logger.info("Creating view %s for %s", datasheet_view_object.name, client_id)

    logger.info("END: Converting pivot for datasheet %s", datasheet_id)


def handle_datasheet_unique_constraint(func):
    """
    Decorator to handle unique constraint errors for datasheets.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except IntegrityError as e:
            logger.exception("IntegrityError")
            if "unique_active_datasheet" in str(e):
                raise DatasheetException(
                    code="UNIQUE_CONSTRAINT_ERROR",
                    message="Rename already in progress",
                    status=status.HTTP_409_CONFLICT,
                ) from e
            raise

    return wrapper
