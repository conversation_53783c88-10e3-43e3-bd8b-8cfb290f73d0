"""
Views for llm_invocation
"""

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.llm_agent.agent_studio.types import Entity
from everstage_ddd.llm_agent.mannai_interface import (
    execute_agent,
    execute_collection,
    execute_skill,
)
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view


class LLMInvocationView(APIView):
    """
    Generic View for all Chain LLMInvocations
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_AUTOGEN_DESCRIPTION.value,
                RbacPermissions.MANAGE_COMMISSION_FORMULA_GENERATION.value,
                RbacPermissions.MANAGE_DATASHEET_AI_GENERATION.value,
            ]
        ),
        name="dispatch",
    )
    @add_log_context_view("Invoke<PERSON><PERSON>hain")
    def post(self, request):
        tag = request.data.get("tag")
        client_id = request.client_id
        email_id = request.user.username
        session_id = request.data.get("session_id", None)
        write_action_approval = request.data.get("write_action_approval", False)
        return_result = request.data.get("return_result")
        thread_id = request.data.get("thread_id", None)
        execution_strategy = request.data.get("execution_strategy")
        user_prompt = request.data.get("user_prompt")

        if tag is None or execution_strategy is None:
            return Response(
                {"error": "Invalid Request"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if execution_strategy == Entity.SKILL.value:
            ai_generated_content, llm_status = execute_skill(
                client_id=client_id,
                skill_tag=tag,
                employee_email_id=email_id,
                session_id=session_id,
                user_prompt=user_prompt,
                write_action_approval=write_action_approval,
                return_result=return_result,
                thread_id=thread_id,
                llm_invocation=True,
            )
        elif execution_strategy == Entity.AGENT.value:
            ai_generated_content, llm_status = execute_agent(
                client_id=client_id,
                agent_tag=tag,
                employee_email_id=email_id,
                session_id=session_id,
                user_prompt=user_prompt,
                write_action_approval=write_action_approval,
                return_result=return_result,
                thread_id=thread_id,
            )
        elif execution_strategy == Entity.COLLECTION.value:
            ai_generated_content, llm_status = execute_collection(
                client_id=client_id,
                collection_tag=tag,
                employee_email_id=email_id,
                session_id=session_id,
                user_prompt=user_prompt,
                write_action_approval=write_action_approval,
                return_result=return_result,
                thread_id=thread_id,
            )
        else:
            return Response(
                {"error": "Invalid execution strategy"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if llm_status == "Success":
            llm_status = status.HTTP_200_OK
        else:
            llm_status = status.HTTP_400_BAD_REQUEST
        return Response(
            ai_generated_content,
            status=llm_status,
        )
