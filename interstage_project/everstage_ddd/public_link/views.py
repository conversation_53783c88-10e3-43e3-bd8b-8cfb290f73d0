from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
from rest_framework.views import APIView

from commission_engine.accessors.client_accessor import get_client
from everstage_ddd.cpq.quote import get_quote_snapshot_data_for_pdf
from everstage_ddd.dealroom import get_deal_room_by_id, get_welcome_page_data

from .service import validate_public_link_access


class PublicLinkBaseView(APIView):
    """
    Base view for public link to be inherited by all public link views
    It uses default REST framework JSONRenderer for rendering the response
    instead of CamelCaseJSONRenderer to avoid conversions of keys to camel case
    """

    renderer_classes = [JSONRenderer]


class GetPublicPdf(PublicLinkBaseView):
    def get(self, request):
        client_id = request.client_id
        quote_id = request.payload["entity_id"]
        data = get_quote_snapshot_data_for_pdf(client_id, quote_id)
        return Response(data)


class ValidatePublicLinkAccess(PublicLinkBaseView):
    def post(self, request):
        client_id = request.payload["client_id"]
        entity = request.payload["entity"]
        entity_id = request.payload["entity_id"]
        email = request.data["email"]
        status, message = validate_public_link_access(
            client_id, entity, entity_id, email
        )
        if status:
            return Response({"isValid": status, "message": message}, status=HTTP_200_OK)
        else:
            return Response(
                {"isValid": status, "message": message}, status=HTTP_400_BAD_REQUEST
            )


class GetBasicClientInfo(PublicLinkBaseView):
    def get(self, request):
        client_id = request.client_id
        client = get_client(client_id)
        return Response(
            {
                "name": client.name,
                "logoUrl": client.logo_url,
                "metaInfo": client.meta_info,
            }
        )


class GetDealRoom(PublicLinkBaseView):
    def post(self, request):
        try:
            client_id = request.client_id
            deal_room_id = request.payload["entity_id"]
            res = get_deal_room_by_id(client_id, deal_room_id)
            return Response(res)
        except Exception as e:
            return Response({"error": str(e)}, status=HTTP_400_BAD_REQUEST)


class GetDealRoomWelcomePage(PublicLinkBaseView):
    def get(self, request):
        try:
            client_id = request.client_id
            deal_room_id = request.payload["entity_id"]
            res = get_welcome_page_data(client_id, deal_room_id)
            return Response(res)
        except Exception as e:
            return Response({"error": str(e)}, status=HTTP_400_BAD_REQUEST)
