"""
This helper module provides a factory function to generate template data
for various entities like quotes, deal rooms, etc., based on their type and ID.
"""

import logging
from enum import Enum
from typing import Any, Callable, Dict

from slack_everstage.utils.slack_utils import get_site_url

from .enums import public_url_map

logger = logging.getLogger(__name__)


class PublicLinkNotification(Enum):
    """
    Enum for the different types of public link notifications.
    """

    QUOTE = "d-fcc822c18b4445668cbf109184137ad6"
    DEALROOM = "d-4bb02b63ba5442588f2eebe173feebee"


# ==============================================================================
# Placeholder Accessors
# In a real implementation, these would be imported from their respective modules
# e.g., from everstage_ddd.cpq.quote.accessors import QuoteDataSelector
# ==============================================================================


class QuoteDataSelector:
    """Placeholder for the actual Quote accessor."""

    def __init__(self, client_id: int, public_link: dict):
        self.client_id = client_id
        self.public_link = public_link

    def get_quote_summary(self) -> Dict[str, Any]:
        """Fetches high-level details for a quote."""
        from everstage_ddd.cpq.quote import QuoteSelector

        from .service import get_user_name

        quote_selector = QuoteSelector(client_id=self.client_id)
        quote = quote_selector.get_quote_by_id(self.public_link["entity_id"])
        quote_summary = {
            "sender_name": get_user_name(self.client_id, quote.owner_id),
        }
        return quote_summary


class DealRoomDataSelector:
    """Placeholder for the actual DealRoom accessor."""

    def __init__(self, client_id: int, deal_room_id: Any):
        self.client_id = client_id
        self.deal_room_id = deal_room_id

    def get_deal_room_details(self) -> Dict[str, Any] | None:
        """Fetches details for a deal room."""
        from everstage_ddd.dealroom import get_deal_room_owner_email

        from .service import get_user_name

        # In a real implementation, this would query the database.
        logger.info(f"Fetching details for Deal Room ID: {self.deal_room_id}")
        sender_email = get_deal_room_owner_email(self.client_id, self.deal_room_id)
        deal_room_summary = {
            "sender_name": get_user_name(self.client_id, sender_email),
        }
        return deal_room_summary


# ==============================================================================
# Private Fetcher Functions
# Each function is responsible for fetching and structuring data for one entity.
# ==============================================================================


def _get_quote_template_data(
    client_id: int, public_link: dict, **kwargs
) -> tuple[dict[str, Any], str, str]:
    """
    Fetches and formats template data for a quote.
    """
    additional_message = kwargs.get("additional_message")
    subject = kwargs.get("subject")

    # In future if we need to fetch more data from quote, we can do it here
    quote_accessor = QuoteDataSelector(client_id, public_link)
    summary = quote_accessor.get_quote_summary()

    public_link_url = f"{get_site_url()}{public_url_map[public_link['entity']]}/{public_link['access_token']}"
    template_data = {
        "sales_rep_name": summary["sender_name"],
        "quote_public_url": public_link_url,
        "body": additional_message,
        "subject": subject,
        "client_logo_url": "",
        "client_name": "",
    }

    return template_data, PublicLinkNotification.QUOTE.value, summary["sender_name"]


def _get_dealroom_template_data(
    client_id: int, public_link: dict, **kwargs
) -> tuple[dict[str, Any], str, str]:
    """
    Fetches and formats template data for a deal room.
    """
    additional_message = kwargs.get("additional_message")
    subject = kwargs.get("subject")

    # In future if we need to fetch more data from deal room, we can do it here
    deal_room_accessor = DealRoomDataSelector(client_id, public_link["entity_id"])
    deal_room_details = deal_room_accessor.get_deal_room_details()
    sender_name = deal_room_details.get("sender_name")
    public_link_url = f"{get_site_url()}{public_url_map[public_link['entity']]}/{public_link['access_token']}"
    # Structure the data as needed for the template.
    template_data = {
        "subject": subject,
        "sales_rep_name": sender_name,
        "deal_room_public_url": public_link_url,
        "body": additional_message,
        "client_logo_url": "",
        "client_name": "",
    }
    return template_data, PublicLinkNotification.DEALROOM.value, sender_name


# ==============================================================================
# Entity Fetcher Registry
# This maps an entity string to its dedicated data-fetching function.
# To support a new entity, add an entry here.
# ==============================================================================

ENTITY_DATA_FETCHER_REGISTRY: Dict[
    str, Callable[..., tuple[dict[str, Any], str, str]]
] = {
    "quote": _get_quote_template_data,
    "dealroom": _get_dealroom_template_data,
}


# ==============================================================================
# Public Factory Function
# ==============================================================================


def get_template_data(
    client_id: int, public_link: dict, **kwargs
) -> tuple[dict[str, Any], str, str]:
    """
    Factory function to get template data for a given entity.

    This function uses a registry to find the appropriate data fetcher based
    on the 'entity' string and then executes it to get the data.

    Args:
        client_id: The ID of the client tenant.
        public_link: The public link dictionary containing entity and access_token.
        **kwargs: Additional keyword arguments for template data.

    Returns:
        A tuple containing the structured template data dictionary and the notification template ID.

    Raises:
        ValueError: If the provided entity type is not supported.
    """
    fetcher_function = ENTITY_DATA_FETCHER_REGISTRY.get(public_link["entity"])

    if not fetcher_function:
        error_msg = f"Unsupported entity type: '{public_link['entity']}'"
        logger.error(error_msg)
        raise ValueError(error_msg)

    logger.info(
        f"Fetching template data for entity '{public_link['entity']}' with ID '{public_link['entity_id']}'..."
    )
    return fetcher_function(client_id, public_link, **kwargs)
