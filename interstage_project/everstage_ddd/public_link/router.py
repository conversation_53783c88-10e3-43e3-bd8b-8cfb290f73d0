from django.db import transaction
from ninja import Router
from ninja.decorators import decorate_view

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope

from .schema import (
    PublicLinkPayload,
    PublicLinkResponse,
    PublicLinkSharePayload,
    PublicLinkShareResponse,
)
from .service import add_recipients_to_public_link, create_public_link_with_recipients

public_link_router = Router(tags=["public_link"])


@public_link_router.post("/create", response=PublicLinkResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def create_public_link_api(request, payload: PublicLinkPayload) -> PublicLinkResponse:
    """fetch public link for a quote"""
    client_id = request.client_id
    entity_id = payload.entity_id
    entity = payload.entity
    logged_in_user = request.user.username

    public_link = create_public_link_with_recipients(
        client_id, entity, entity_id, logged_in_user
    )
    return PublicLinkResponse(**public_link)


@public_link_router.post("/share", response=PublicLinkShareResponse)
@decorate_view(requires_scope(RbacPermissions.VIEW_QUOTES.value))
@transaction.atomic
def share_public_link_api(
    request, payload: PublicLinkSharePayload
) -> PublicLinkShareResponse:
    """fetch public link for a quote"""
    client_id = request.client_id
    logged_in_user = request.user.username
    add_recipients_to_public_link(client_id, logged_in_user, **payload.dict())
    return PublicLinkShareResponse(
        status="success",
    )
