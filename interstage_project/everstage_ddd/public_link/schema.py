from typing import List, Optional

from ninja import Schema


class PublicLinkPayload(Schema):
    entity_id: str
    entity: str
    shared_users: Optional[List[str]] = []


class PublicLinkResponse(Schema):
    public_link: str
    public_link_id: str
    shared_users: Optional[List[str]] = []


class PublicLinkSharePayload(Schema):
    public_link_id: str
    notify: bool
    subject: str
    additional_message: str
    added_emails: List[str]
    deleted_emails: List[str]


class PublicLinkShareResponse(Schema):
    status: str
