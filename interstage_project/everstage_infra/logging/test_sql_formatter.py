"""Tests for SQL formatting functionality."""

import pytest

from .sql_formatter import <PERSON>qlparse<PERSON><PERSON>atter
from .sql_formatter_utils import SqlQueryParser


@pytest.mark.parametrize(
    "test_description,input_query,expected_output",
    [
        (
            "Simple column with table alias",
            'SELECT u."user_id", u.name FROM "users" u',
            "SELECT user_id, name FROM users u",
        ),
        (
            "Multiple table aliases and joins",
            """
            SELECT 
                u."user_id",
                u.name,
                o."order_id",
                o.status
            FROM "users" u
            JOIN "orders" o ON u.user_id = o.user_id
            """,
            """
            SELECT 
                user_id,
                name,
                order_id,
                status
            FROM users u
            JOIN orders o ON user_id = user_id
            """,
        ),
        (
            "Identifiers with spaces and special characters",
            """
            SELECT 
                "User ID",
                "First Name",
                "Last-Name",
                "created_at"::timestamp as "Creation Date"
            FROM "users"
            """,
            """
            SELECT 
                "User ID",
                "First Name",
                "Last-Name",
                created_at::timestamp as "Creation Date"
            FROM users
            """,
        ),
        (
            "Table name with underscores",
            """
            SELECT 
                interstage_clients.client_id,
                interstage_clients.name,
                interstage_clients.domain,
                interstage_clients.logo_url,
                interstage_clients.auth_connection_name,
                interstage_clients.connection_type
            FROM interstage_clients
            WHERE interstage_clients.client_id = 4006
            """,
            """
            SELECT 
                client_id,
                name,
                domain,
                logo_url,
                auth_connection_name,
                connection_type
            FROM interstage_clients
            WHERE client_id = 4006
            """,
        ),
    ],
)
def test_sql_simplification(test_description, input_query, expected_output):
    """Test SQL query simplification with various cases."""
    formatter = SqlparseFormatter()
    simplified = formatter._simplify_query(input_query)

    # Normalize whitespace for comparison
    simplified = "\n".join(line.strip() for line in simplified.splitlines())
    expected = "\n".join(line.strip() for line in expected_output.splitlines())

    assert simplified == expected, f"Failed test: {test_description}"


def test_sql_query_parser_multiline_format():
    """Test SqlQueryParser with multiline format containing inline arguments."""
    parser = SqlQueryParser(simplify_query=False)

    # Test case based on the actual format from sample.sql
    multiline_msg = """(0.032)
SELECT
    emp.profile_picture,
    CONCAT(
        emp.first_name, ' ', emp.last_name
    ) full_name,
    pa.*,
    ps.payment_status,
    ps.total_payout,
    ps.paid_amount,
    ps.processed_amount,
    ps.ignored_amount,
    ps.pending_amount as payout_status_pending_amount,
    epd.pay_currency,
    epd.payout_frequency

FROM
    payout_arrear pa
    JOIN employee emp ON emp.employee_email_id = pa.payee_id
    AND emp.client_id = pa.client_id
    AND NOT emp.is_deleted
    AND emp.knowledge_end_date ISNULL
    AND NOT pa.is_deleted
    AND pa.knowledge_end_date ISNULL
WHERE
    pa.client_id = 7007
    AND pa.is_processed = FALSE
    AND pa.period_end_date <= '2025-07-01T00:00:00'::timestamp
 LIMIT 20 OFFSET 0 ; args={'client_id': 7007, 'current_date': datetime.datetime(2025, 7, 1, 0, 0), 'limit': 20, 'offset': 0}; alias=default"""

    result = parser.parse_postgres_log_message(multiline_msg)

    # Assert that parsing was successful
    assert (
        result is not None
    ), "Parser should successfully parse multiline format with inline args"

    # Check basic fields
    assert result["execution_time"] == 0.032, "Should extract correct execution time"
    assert (
        result["execution_time_ms"] == 32.0
    ), "Should convert execution time to milliseconds"
    assert result["query_type"] == "SELECT", "Should identify query type as SELECT"

    # Check that query text doesn't include the args part
    assert "; args=" not in result["query_text"], "Query text should not include args"
    assert (
        "; alias=default" not in result["query_text"]
    ), "Query text should not include alias"
    assert (
        result["query_text"].strip().endswith("LIMIT 20 OFFSET 0")
    ), "Query should end with LIMIT clause"

    # Check arguments parsing
    assert len(result["arguments"]) == 1, "Should parse arguments"
    # Arguments should contain the string representation since it contains complex objects
    assert "client_id" in str(
        result["arguments"][0]
    ), "Arguments should contain client_id"

    # Check table names extraction
    expected_tables = [
        "employee",
        "payout_arrear",
    ]  # These should be extracted from the query
    for table in expected_tables:
        assert table in result["table_names"], f"Should extract table name: {table}"


def test_sql_query_parser_multiline_format_separate_arguments():
    """Test SqlQueryParser with multiline format containing separate Arguments line."""
    parser = SqlQueryParser(simplify_query=False)

    # Test legacy format with separate Arguments line
    multiline_msg = """(0.025)
SELECT * FROM users WHERE id = %s
Arguments: [123]"""

    result = parser.parse_postgres_log_message(multiline_msg)

    # Assert that parsing was successful
    assert (
        result is not None
    ), "Parser should successfully parse multiline format with separate Arguments line"

    # Check basic fields
    assert result["execution_time"] == 0.025, "Should extract correct execution time"
    assert result["query_type"] == "SELECT", "Should identify query type as SELECT"

    # Check that query text doesn't include the Arguments part
    assert (
        "Arguments:" not in result["query_text"]
    ), "Query text should not include Arguments line"
    assert (
        result["query_text"].strip() == "SELECT * FROM users WHERE id = %s"
    ), "Query should be clean"

    # Check arguments parsing
    assert result["arguments"] == [123], "Should parse arguments correctly"


def test_sql_query_parser_multiline_format_no_arguments():
    """Test SqlQueryParser with multiline format containing no arguments."""
    parser = SqlQueryParser(simplify_query=False)

    # Test format with no arguments
    multiline_msg = """(0.015)
SELECT COUNT(*) FROM orders
WHERE status = 'completed'"""

    result = parser.parse_postgres_log_message(multiline_msg)

    # Assert that parsing was successful
    assert (
        result is not None
    ), "Parser should successfully parse multiline format with no arguments"

    # Check basic fields
    assert result["execution_time"] == 0.015, "Should extract correct execution time"
    assert result["query_type"] == "SELECT", "Should identify query type as SELECT"
    assert result["arguments"] == [], "Should have empty arguments list"

    # Check that the full query is preserved
    expected_query = """SELECT COUNT(*) FROM orders
WHERE status = 'completed'"""
    assert (
        result["query_text"].strip() == expected_query.strip()
    ), "Query should be preserved exactly"
