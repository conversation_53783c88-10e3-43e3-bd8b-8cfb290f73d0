from crum import get_current_request


def should_log_request() -> bool:
    """
    Skip logging for certain noisy paths that are invoked frequently from the client
    """
    request = get_current_request()
    if request is None:
        return False

    skip_paths = [
        "/ping/",
        "/health/",
        "/static/",
        "/admin/jsi18n/",
        "/manifest.json",
        "/common/get-supabase-token",
        "/spm/token-health-check",
        "/spm/analytics/event",
    ]

    should_log = not any(request.path.startswith(path) for path in skip_paths)
    return should_log


def request_path() -> str:
    """
    Get the path of the current request - if the request is a graphql request, return the query from the request header.
    Otherwise, return the path of the request.
    """
    request = get_current_request()
    if request is None:
        return "unknown"

    if request.path == "/graphql" and request.method == "POST":
        # Return Query from requesst header
        return request.headers.get("Query", "unknown")
    return request.path
