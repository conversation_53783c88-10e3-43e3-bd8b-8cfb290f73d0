"""Logging filters for Everstage.

This module provides logging filters that add context and metadata to log records,
such as correlation IDs, request information, and exception tags.
"""

import logging
from logging import <PERSON>g<PERSON><PERSON>ord
from typing import Any

from crum import get_current_request

from interstage_project.threadlocal_log_context import (
    LogContextKey,
    get_threadlocal_context,
)

from .log_utils import should_log_request


class CorrelationIdFilter(logging.Filter):
    """Injects a correlation id to track logs belonging to a single context.

    This filter adds:
    1. request_id: ID from the current request
    2. e2e_sync_run_id: ID from the current ETL sync
    3. correlation_id: Combined ID for tracking related logs
    4. log_context: Additional context from threadlocal storage
    5. celery_context: Context from Celery tasks
    """

    def filter(self, record: Any) -> bool:
        request = get_current_request()
        log_context = get_threadlocal_context()
        celery_context = get_threadlocal_context(
            context_key=LogContextKey.CELERY_CONTEXT.name
        )
        e2e_sync_run_id = log_context.get("e2e_sync_run_id", "")
        request_id = None
        if request:
            request_id = getattr(request, "request_id", "not-set")
        correlation_id = e2e_sync_run_id or request_id
        record.request_id = request_id
        record.e2e_sync_run_id = e2e_sync_run_id
        record.correlation_id = correlation_id
        record.log_context = log_context
        record.celery_context = celery_context
        return True


class EverWebContextFilter(logging.Filter):
    """Injects web request context into log records.

    This filter adds:
    1. client_id: ID of the current client
    2. domain: Domain of the request
    3. user: Username of the current user
    """

    def filter(self, record: Any) -> bool:
        request = get_current_request()
        # Attributes below are added by the SetClientMiddleware middleware
        if request:
            record.client_id = getattr(request, "client_id", None)
            record.domain = getattr(request, "domain", None)
            record.user = (
                getattr(getattr(request, "user", None), "username", None) or "unknown"
            )
        return True


class ExcludeLogLevelFilter(logging.Filter):
    """Removes log records that are not of the configured level.

    Args:
        level_name: The name of the log level to allow
    """

    def __init__(self, level_name: str) -> None:
        self.level_name = level_name

    def filter(self, record: Any) -> bool:
        return record.levelname != self.level_name


class AddExceptionTagFilter(logging.Filter):
    """Injects exception tags to exception logs.

    This filter looks for exception tags in the log record's extra data
    and adds them to the record for formatting.
    """

    def get_exception_tag(self, record):
        default_exception = "other"
        try:
            app_name = record.name.split(".")[0]
            return record.exception_tag if record.exception_tag else app_name
        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return default_exception

    def filter(self, record):
        if record.levelname in ("ERROR", "EXCEPTION"):
            exception_tag = self.get_exception_tag(record)
            record.exception = True
            record.exception_tag = exception_tag
        return True


class PostgresFilter(logging.Filter):
    """Filter to allow PostgreSQL query messages and request boundary logs.
    Request boundary logs are also passed on by this filter since otherwise we are not able to ensure that
    logs from 2 different handlers are written in the correct order to the same file.

    This filter ensures that only properly formatted PostgreSQL query messages from
    Django's database backend are logged. These messages start with a timing
    information in parentheses.
    """

    def filter(self, record: LogRecord) -> bool:
        """
        django sql logs as well as request boundary logs are handled by this filter
        """
        if not should_log_request():
            return False

        django_sql_log = (
            hasattr(record, "msg")
            and isinstance(record.msg, str)
            and record.msg.startswith("(")
        )
        request_boundary_log = hasattr(record, "is_request_boundary")
        return django_sql_log or request_boundary_log


class SnowflakeFilter(logging.Filter):
    """Filter to only allow Snowflake query messages.

    The log record we need looks like this:
    'msg': 'running query [%s]', 'args': ('use database EVERSTAGE_LOCAL_INDI...',)
    """

    def filter(self, record: LogRecord) -> bool:
        # Check should_log_request first
        if not should_log_request():
            return False

        if not hasattr(record, "msg"):
            return False

        # Handle Snowflake connector format
        if isinstance(record.msg, str) and record.msg.startswith("running query"):
            return True

        return False
