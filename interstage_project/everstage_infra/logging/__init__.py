"""
Everstage Logging System - Package Overview

This package provides a comprehensive, configurable logging system for the Everstage platform, supporting both production (JSON) and advanced SQL logging modes.

Main modules:
- package_loggers: Entrypoint for logger configuration. Use get_package_loggers(BASE_DIR, sql_formatting=True/False) in your Django settings.
- sql_logging.py: Orchestrates SQL-specific logging, merging advanced SQL handlers/formatters with standard logging.
- sql_logging_components.py: Core formatters and filters for SQL query logging (PostgresFormatter, SnowflakeFormatter, PostgresFilter, SnowflakeFilter).
- sql_formatter_utils.py: Shared utilities for query parsing, argument handling, and context extraction.
- filters.py: Context filters for request/correlation IDs, user/client info, and log level filtering.
- log_utils.py: Utility functions for request path detection and log filtering.
- color_providers.py: ANSI color formatting for SQL highlighting (configurable).
- sql_highlighter.py: SQL syntax highlighting and table name extraction.
- request_boundary_middleware.py: Middleware for request boundary logging and correlation context.

Usage:
- Import get_package_loggers from this package and call it in your Django settings to configure logging.
- See docs/backend/logging-system.md for full documentation and integration guide.
"""

from .package_loggers import get_package_loggers

__all__ = ["get_package_loggers"]
