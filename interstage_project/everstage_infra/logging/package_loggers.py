"""Package-level logging configuration.

This module provides functions to configure logging for different Django apps and packages
in the Everstage project.
"""

import os
from pathlib import Path
from typing import Dict

from everstage_infra.aws_infra.constants.environments import (
    PRODUCTION_CLONE_ENVIRONMENT,
    PRODUCTION_ENVIRONMENT,
)

from .sql_logging import get_loggers_sql_formatting


def get_package_loggers(base_dir: str, sql_formatting: bool = True) -> Dict:
    """Configure logging for all Everstage packages.

    This function:
    1. Sets up appropriate handlers based on environment
    2. Configures loggers for all Django apps
    3. Sets up SQL query logging in debug mode

    Args:
        base_dir: Base directory for log files
        sql_formatting: Whether to enable SQL query logging

    Returns:
        Dict containing the complete logging configuration
    """

    # Update file handler paths with correct base_dir and ensure directory exists
    logdir = Path(base_dir).resolve() / "logs"
    logdir.mkdir(parents=True, exist_ok=True)
    logging_config = None

    if sql_formatting is False:
        logging_config = get_loggers_v1()
    else:
        logging_base_config = _get_logging_base_config()
        # _set_package_loggers has side effect of modifying the logging_base_config
        _set_package_loggers(logging_base_config)
        logging_config = get_loggers_sql_formatting(logging_base_config, str(logdir))
    return logging_config


def get_loggers_v1():
    logging_config = _get_logging_base_config()
    _set_package_loggers(logging_config)
    return logging_config


def _get_logging_base_config():
    logging_base_conf = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "simple": {
                "level": "INFO",
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "format": "[%(asctime)s %(filename)s %(lineno)d %(levelname)s %(processName)s]: %(message)s",
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(message)s",
            },
            "json_extended": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(correlation_id)s %(e2e_sync_run_id)s %(request_id)s %(client_id)s %(log_context)s %(name)s %(levelname)s %(message)s %(filename)s %(lineno)d %(module)s %(process)d %(thread)d %(processName)s",
            },
            "json_exception": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "[%(asctime)s] %(request_id)s %(name)s %(levelname)s %(message)s %(filename)s %(exception)s %(exception_tag)s %(lineno)d %(module)s %(process)d %(thread)d %(processName)s %(exc_info)s",
            },
        },
        "filters": {
            "add_exception_tag": {
                "()": "everstage_infra.logging.filters.AddExceptionTagFilter",
            },
            "exclude_error_level": {
                "()": "everstage_infra.logging.filters.ExcludeLogLevelFilter",
                "level_name": "ERROR",
            },
            "ever_web_context": {
                "()": "everstage_infra.logging.filters.EverWebContextFilter",
            },
            "ever_log_context": {
                "()": "everstage_infra.logging.filters.CorrelationIdFilter",
            },
        },
        "handlers": {
            "console": {
                "level": "INFO",
                "class": "logging.StreamHandler",
                "formatter": "json",
            },
            "console_json_formatter": {
                "class": "logging.StreamHandler",
                "formatter": "json_extended",
                "filters": [
                    "ever_web_context",
                    "ever_log_context",
                    "add_exception_tag",
                    "exclude_error_level",
                ],
            },
            "console_simple_formatter": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter": "simple",
                "filters": [
                    "ever_web_context",
                    "ever_log_context",
                    "add_exception_tag",
                    "exclude_error_level",
                ],
            },
            "console_exception_json_formatter": {
                "level": "ERROR",
                "class": "logging.StreamHandler",
                "formatter": "json_exception",
            },
            "console_exception_simple_formatter": {
                "level": "ERROR",
                "class": "logging.StreamHandler",
                "formatter": "simple",
            },
        },
        "loggers": {},
    }
    return logging_base_conf


def _set_package_loggers(logging_config):
    everstage_logger = {
        "handlers": ["console_json_formatter", "console_exception_json_formatter"],
        "level": os.getenv("EVERSTAGE_LOG_LEVEL", "INFO"),
        "propagate": False,
    }

    # Handlers to use during local development - makes reading easier
    local_handlers = [
        "console_simple_formatter",
        "console_exception_simple_formatter",
    ]
    # For non-prod environments, use a simple text logger as json is hard to read
    # In prod environments, we have datadog to parse the json logs
    if (
        os.environ.get("ENV") not in PRODUCTION_ENVIRONMENT
        and os.environ.get("CLONE_ENV") not in PRODUCTION_CLONE_ENVIRONMENT
    ):
        everstage_logger["handlers"] = local_handlers

    # Set the modified logger for all apps (in alphabetical order)
    django_apps = [
        "async_tasks",
        "commission_engine",
        "common",
        "crystal",
        "everstage_admin_backend",
        "everstage_ddd",
        "everstage_etl",
        "everstage_infra",
        "kpi",
        "ms_teams_everstage",
        "slack_everstage",
        "spm",
        "superset",
        "interstage_project",
    ]
    for app in django_apps:
        logging_config["loggers"][app] = everstage_logger

    # assign the django logger to the same handlers as the everstage logger
    logging_config["loggers"]["django"] = everstage_logger.copy()
