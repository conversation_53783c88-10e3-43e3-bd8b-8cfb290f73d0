"""Logging middleware for request boundary tracking.

This module provides middleware to log request start/end events to provide
context around SQL queries in the sql.log file.
"""

import logging
import time
import uuid
from typing import Callable

from django.http import HttpRequest, HttpResponse

from .log_utils import request_path, should_log_request

# Any logs from this logger will be handled by the special request boundary handler
logger = logging.getLogger(__name__)


class RequestBoundaryLoggingMiddleware:
    """Middleware to log request start/end for SQL query context.

    This middleware logs the beginning and end of each web request to the
    SQL log file, providing context around the SQL queries that occur
    during the request lifecycle.
    """

    def __init__(self, get_response: Callable[[HttpRequest], HttpResponse]):
        self.get_response = get_response

    def __call__(self, request: HttpRequest) -> HttpResponse:
        """Process request and log boundaries."""

        should_log = should_log_request()

        if should_log:
            # Log request start
            log_request_start(request)

        # Process the request
        response = self.get_response(request)

        if should_log:
            # Log request end
            log_request_end(request, response)

        return response


def log_request_start(request: HttpRequest) -> None:
    """Log the start of a web request with context."""
    try:
        request_id = getattr(request, "request_id", None)
        if request_id is None:
            # Set the request id to a new uuid
            request_id = str(uuid.uuid4())
            request.request_id = request_id

        # Create a log record with request context
        extra = {
            "is_request_boundary": True,
            "event_type": "request_start",
            "request_id": request.request_id,
            "client_id": getattr(request, "client_id", "unknown"),
            "user": getattr(getattr(request, "user", None), "username", "unknown"),
            "method": request.method,
            "path": request_path(),
        }

        # Store start time for duration calculation
        request.sql_log_start_time = time.time()

        logger.info("Request started", extra=extra)

    except Exception:  # noqa: BLE001 - logging errors should not break application flow
        # Don't let logging errors break the request
        logger.exception("Failed to log request start")


def log_request_end(request: HttpRequest, response) -> None:
    """Log the end of a web request with context and duration."""
    try:
        # Calculate duration
        start_time = getattr(request, "sql_log_start_time", time.time())
        duration = round(time.time() - start_time, 3)

        extra = {
            "is_request_boundary": True,
            "event_type": "request_end",
            "request_id": getattr(request, "request_id", "unknown"),
            "client_id": getattr(request, "client_id", "unknown"),
            "user": getattr(getattr(request, "user", None), "username", "unknown"),
            "duration": duration,
            "method": request.method,
            "path": request_path(),
            "status_code": getattr(response, "status_code", "unknown"),
        }

        logger.info("Request ended", extra=extra)

    except Exception:  # noqa: BLE001 - logging errors should not break application flow
        # Don't let logging errors break the request
        logger.exception("Failed to log request end")
