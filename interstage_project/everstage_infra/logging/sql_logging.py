"""SQL Query Logging Configuration.

This module provides the main configuration function for SQL query logging.
The formatters and filters are implemented in sql_logging_components.py.
"""

import os
from pathlib import Path


def get_loggers_sql_formatting(logging_base_config: dict, log_dir: str):
    """
    This function returns a logging configuration dictionary for the Everstage application
    that includes both PostgreSQL and Snowflake queries. The configuration includes formatters, filters,
    handlers for both types of SQL queries with date-based rotation.

    Args:
        logging_base_config: The base logging configuration dictionary
        log_dir: The base directory path where log files will be created
    """

    # Create a new configuration dictionary starting with version and disable_existing_loggers
    logging_config = logging_base_config.copy()

    sql_logging_config = get_sql_logging_config(log_dir)

    # Merge SQL logging configuration with existing logging config
    logging_config["formatters"].update(sql_logging_config["formatters"])
    logging_config["filters"].update(sql_logging_config["filters"])
    logging_config["handlers"].update(sql_logging_config["handlers"])
    logging_config["loggers"].update(sql_logging_config["loggers"])

    return logging_config


def get_sql_logging_config(log_dir: str) -> dict:
    """Get SQL query logging configuration for both PostgreSQL and Snowflake.

    This function returns a logging configuration dictionary for both PostgreSQL
    and Snowflake queries. The configuration includes formatters, filters, and
    handlers for both types of SQL queries with date-based rotation.

    Supports dual-format (plaintext + JSON) via EVERSTAGE_SQL_JSON_LOGGING=true

    Args:
        log_dir: The base directory path where log files will be created

    Returns:
        dict: A logging configuration dictionary that can be used with logging.config.dictConfig
    """
    # make sure log_file sql.log exists before proceeding
    log_dir_path = Path(log_dir).resolve()
    sql_log_path = log_dir_path / "sql.log"
    json_log_path = log_dir_path / "sql.json"

    if not str(sql_log_path).startswith(str(log_dir_path)) or not str(
        json_log_path
    ).startswith(str(log_dir_path)):
        raise ValueError("Invalid log file path")  # noqa: TRY003
    Path(sql_log_path).touch(exist_ok=True)
    Path(json_log_path).touch(exist_ok=True)

    # Check logging configuration
    dual_format_enabled = (
        os.getenv("EVERSTAGE_SQL_JSON_LOGGING", "true").lower() == "true"
    )
    postgres_handlers = ["postgres_file"]
    snowflake_handlers = ["snowflake_file"]
    if dual_format_enabled:
        postgres_handlers.append("postgres_file_json")
        snowflake_handlers.append("snowflake_file_json")

    # Define a clean config without any unpicklable objects
    sql_config = {
        "formatters": {
            "postgres": {
                "()": "everstage_infra.logging.postgres_formatters.PostgresFormatter",
                "format": "%(message)s",
                "simplify_query": False,
                "tables_to_mute": {"interstage_clients"},
            },
            "postgres_json": {
                "()": "everstage_infra.logging.postgres_formatters.PostgresJsonFormatter",
                "format": "%(message)s",
                "simplify_query": False,
                "tables_to_mute": {"interstage_clients"},
            },
            "snowflake": {
                "()": "everstage_infra.logging.snowflake_formatters.SnowflakeFormatter",
                "format": "%(message)s",
            },
            "snowflake_json": {
                "()": "everstage_infra.logging.snowflake_formatters.SnowflakeJsonFormatter",
                "format": "%(message)s",
                "simplify_query": False,
                "tables_to_mute": {"interstage_clients"},
            },
        },
        "filters": {
            "postgres": {
                "()": "everstage_infra.logging.filters.PostgresFilter",
            },
            "snowflake": {
                "()": "everstage_infra.logging.filters.SnowflakeFilter",
            },
            "ever_web_context": {
                "()": "everstage_infra.logging.filters.EverWebContextFilter",
            },
            "ever_log_context": {
                "()": "everstage_infra.logging.filters.CorrelationIdFilter",
            },
        },
        "handlers": {
            "postgres_file": {
                "level": "DEBUG",
                "class": "logging.handlers.TimedRotatingFileHandler",
                "filename": str(sql_log_path),
                "when": "H",
                "interval": 4,
                "backupCount": 168,  # Keep 30 days of logs
                "formatter": "postgres",
                "filters": ["ever_web_context", "ever_log_context", "postgres"],
            },
            "postgres_file_json": {
                "level": "DEBUG",
                "class": "logging.handlers.TimedRotatingFileHandler",
                "filename": str(json_log_path),
                "when": "H",
                "interval": 4,
                "backupCount": 168,  # Keep 30 days of logs
                "formatter": "postgres_json",
                "filters": ["ever_web_context", "ever_log_context", "postgres"],
            },
            "snowflake_file": {
                "level": "DEBUG",
                "class": "logging.handlers.TimedRotatingFileHandler",
                "filename": str(sql_log_path),
                "when": "H",
                "interval": 4,
                "backupCount": 168,  # Keep 30 days of logs
                "formatter": "snowflake",
                "filters": ["ever_web_context", "ever_log_context", "snowflake"],
            },
            "snowflake_file_json": {
                "level": "DEBUG",
                "class": "logging.handlers.TimedRotatingFileHandler",
                "filename": str(json_log_path),
                "when": "H",
                "interval": 4,
                "backupCount": 168,  # Keep 30 days of logs
                "formatter": "snowflake_json",
                "filters": ["ever_web_context", "ever_log_context", "snowflake"],
            },
        },
        "loggers": {
            "django.db.backends": {
                "handlers": postgres_handlers,
                "level": "DEBUG",
                "propagate": False,
            },
            # Capture cursor messages
            "snowflake.connector.cursor": {
                "handlers": snowflake_handlers,
                "level": "DEBUG",
                "propagate": False,
            },
            # Capture request boundary events and handle with postgres_file handler itself
            "everstage_infra.logging.request_boundary_middleware": {
                "handlers": ["postgres_file"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }

    return sql_config
