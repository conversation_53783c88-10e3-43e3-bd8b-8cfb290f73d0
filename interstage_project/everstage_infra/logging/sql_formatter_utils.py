"""Shared utility functions for SQL formatters.

This module provides common functionality that can be used by both plaintext
and JSON SQL formatters to avoid code duplication.
"""

import ast
import hashlib
import re
import threading
from datetime import datetime
from typing import Any, Dict, Set
from zoneinfo import ZoneInfo

from .color_providers import ColorProviderFactory
from .log_utils import request_path
from .sql_formatter import get_sql_formatter


class QueryCounter:
    """Thread-safe query counter shared across formatters."""

    _query_count: int = 0
    _MAX_QUERY_COUNT: int = 999999
    _lock = threading.Lock()

    @classmethod
    def increment(cls) -> int:
        """Thread-safe increment of query counter.

        Returns:
            The new query count
        """
        with cls._lock:
            cls._query_count = (cls._query_count % cls._MAX_QUERY_COUNT) + 1
            return cls._query_count

    @classmethod
    def get_current(cls) -> int:
        """Get current query count without incrementing.

        Returns:
            The current query count
        """
        with cls._lock:
            return cls._query_count


class SqlQueryParser:
    """Parser for extracting information from SQL log messages."""

    def __init__(
        self, simplify_query: bool = False, tables_to_mute: Set[str] | None = None
    ):
        """Initialize the parser.

        Args:
            simplify_query: Whether to simplify queries during formatting
            tables_to_mute: Set of table names to mark as muted
        """
        self.simplify_query = simplify_query
        self.tables_to_mute = tables_to_mute or set()

    def parse_postgres_log_message(self, msg: str) -> Dict[str, Any] | None:
        """Parse a PostgreSQL log message and extract structured information.

        Supports both multiline and single-line Django PostgreSQL log formats:
        - Multiline: (time)\nQUERY\nArguments: (args)
        - Single-line: (time) QUERY; args=(args); alias=default

        Args:
            msg: The log message to parse

        Returns:
            Dictionary with parsed information or None if parsing fails
        """
        try:
            # Extract execution time
            time_match = re.search(r"\((\d+\.\d+)\)", msg)
            if not time_match:
                return None

            execution_time = float(time_match.group(1))

            # Determine format: multiline or single-line
            if "\n" in msg:
                parsed = self._parse_multiline_format(msg, execution_time)
                if parsed:
                    return parsed
                return None
            return self._parse_single_line_format(msg, execution_time)

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return None

    def _parse_multiline_format(
        self, msg: str, execution_time: float
    ) -> Dict[str, Any] | None:
        """Parse multiline format: (time) QUERY_START\nQUERY_CONTINUATION with inline args at end"""
        try:
            # Find the execution time pattern and extract everything after it
            time_match = re.search(r"\((\d+\.\d+)\)", msg)
            if not time_match:
                return None

            # Get everything after the execution time pattern
            time_end = time_match.end()
            # Skip any space after the closing parenthesis
            while time_end < len(msg) and msg[time_end] == " ":
                time_end += 1

            full_content = msg[time_end:].strip()
            if not full_content:
                return None

            # Check if this has inline arguments (; args=...)
            args_match = re.search(r"; args=(.+?)(?:; alias=|$)", full_content)
            arguments = []

            if args_match:
                # Extract query text (everything before "; args=")
                args_start = full_content.find("; args=")
                query_text = full_content[:args_start].strip()

                # Parse arguments
                args_text = args_match.group(1).strip()
                arguments = self._parse_arguments_safely(args_text)
            else:
                # No inline arguments, check for separate "Arguments:" line
                args_line_match = re.search(
                    r"Arguments: (.+)$", full_content, re.MULTILINE
                )
                if args_line_match:
                    # Remove the Arguments line from query text
                    query_text = re.sub(
                        r"\nArguments: .+$", "", full_content, flags=re.MULTILINE
                    ).strip()
                    args_text = args_line_match.group(1)
                    arguments = self._parse_arguments_safely(args_text)
                else:
                    # No arguments at all
                    query_text = full_content

            return self._create_parsed_result(query_text, execution_time, arguments)

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return None

    def _parse_single_line_format(
        self, msg: str, execution_time: float
    ) -> Dict[str, Any] | None:
        """Parse single-line Django format: (time) QUERY; args=(args); alias=default"""
        try:
            # Extract the main query
            query_start = msg.find(")") + 2  # After ") "
            query_end = msg.find("; args=")
            if query_end == -1:
                # No arguments, look for ; alias= or end of string
                alias_pos = msg.find("; alias=")
                query_end = alias_pos if alias_pos != -1 else len(msg)

            if query_start >= query_end:
                return None

            query_text = msg[query_start:query_end].strip()

            # Extract arguments if present
            arguments = []
            if "; args=" in msg:
                args_start = msg.find("; args=") + 7
                args_end = msg.find("; alias=")
                if args_end == -1:
                    args_end = len(msg)
                args_text = msg[args_start:args_end].strip()
                arguments = self._parse_arguments_safely(args_text)

            return self._create_parsed_result(query_text, execution_time, arguments)

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return None

    def _parse_arguments_safely(self, args_text: str) -> list:
        """Safely parse argument string without using eval().

        Args:
            args_text: The arguments string to parse

        Returns:
            List of parsed arguments or original string if parsing fails
        """
        if not args_text or args_text == "None":
            return []

        try:
            # First try ast.literal_eval for simple cases
            result = ast.literal_eval(args_text)
            if not isinstance(result, (list, tuple)):
                result = [result]
            return list(result)
        except (ValueError, SyntaxError):
            # If that fails, return the string representation for complex objects
            # This handles cases like UUID('...'), datetime objects, etc.
            return [args_text]

    def _create_parsed_result(
        self, query_text: str, execution_time: float, arguments: list
    ) -> Dict[str, Any]:
        """Create the standardized parsed result dictionary."""
        # Parse SQL to extract table names and query type
        formatted_query, table_names = get_sql_formatter("sqlparse").format_query(
            query_text, simplify=self.simplify_query
        )

        # Determine query type
        query_type = self._extract_query_type(query_text)

        # Generate query hash for deduplication
        query_hash = self._generate_query_hash(query_text)

        return {
            "execution_time": execution_time,
            "execution_time_ms": execution_time * 1000,
            "query_text": query_text,
            "formatted_query": formatted_query,
            "arguments": list(arguments),
            "table_names": sorted(list(table_names)),
            "query_type": query_type,
            "query_hash": query_hash,
            "is_muted": self._is_query_muted(table_names),
            "is_simplified": self.simplify_query,
        }

    def _extract_query_type(self, query: str) -> str:
        """Extract the type of SQL query (SELECT, INSERT, UPDATE, etc.)."""
        query_upper = query.strip().upper()

        # Dictionary mapping query prefixes to types
        query_types = {
            "SELECT": "SELECT",
            "INSERT": "INSERT",
            "UPDATE": "UPDATE",
            "DELETE": "DELETE",
            "CREATE": "CREATE",
            "ALTER": "ALTER",
            "DROP": "DROP",
        }

        # Find the first matching query type
        for prefix, query_type in query_types.items():
            if query_upper.startswith(prefix):
                return query_type

        return "OTHER"

    def _generate_query_hash(self, query: str) -> str:
        """Generate a hash for query deduplication.

        Args:
            query: The SQL query to hash

        Returns:
            16-character hash string
        """
        # Normalize the query by removing extra whitespace and converting to lowercase
        normalized = " ".join(query.strip().lower().split())
        return hashlib.md5(normalized.encode()).hexdigest()[:16]

    def _is_query_muted(self, table_names: Set[str]) -> bool:
        """Check if query should be muted based on table names.

        Args:
            table_names: Set of table names used in the query

        Returns:
            True if query should be muted, False otherwise
        """
        return bool(self.tables_to_mute.intersection(table_names))

    def parse_snowflake_query(self, query: str) -> Dict[str, Any]:
        """Parse a raw Snowflake SQL query and extract structured information.

        Args:
            query: The raw SQL query to parse

        Returns:
            Dictionary with parsed information
        """
        try:
            # For Snowflake, we don't have execution time, so set to 0
            execution_time = 0.0

            # No arguments for Snowflake queries in our current setup
            arguments = []

            return self._create_parsed_result(query, execution_time, arguments)
        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return {
                "execution_time": 0.0,
                "execution_time_ms": 0.0,
                "query_text": query,
                "formatted_query": query,
                "arguments": [],
                "table_names": [],
                "query_type": "OTHER",
                "query_hash": "",
                "is_muted": False,
                "is_simplified": False,
            }


class RequestContextProvider:
    """Provider for request context information."""

    @staticmethod
    def get_request_context() -> Dict[str, Any]:
        """Get current request context information.

        Returns:
            Dictionary with request context information
        """
        try:
            from crum import get_current_request

            request = get_current_request()
            return {
                "request_id": (
                    getattr(request, "request_id", "unknown") if request else "unknown"
                ),
                "endpoint": request_path(),
                "client_id": getattr(request, "client_id", None) if request else None,
            }
        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return {
                "request_id": "unknown",
                "endpoint": "unknown",
                "client_id": None,
            }


def format_thread_id(thread_id: int) -> str:
    """Format a thread ID to be shorter and more readable.

    Takes the last 4 digits of the thread ID to make it more compact.

    Args:
        thread_id: The thread ID to format

    Returns:
        Formatted thread ID string
    """
    return f"{thread_id % 10000:04d}"


def get_ist_timestamp() -> str:
    """Get current timestamp in IST timezone with timezone information.

    Returns:
        Formatted timestamp string in IST with timezone info
    """
    # Create timestamp in IST timezone
    ist_time = datetime.now(ZoneInfo("Asia/Kolkata"))
    return ist_time.isoformat()


def request_id_line():
    from crum import get_current_request

    request = get_current_request()
    if request and hasattr(request, "request_id"):
        color = ColorProviderFactory.create()
        return f"{color.cyan('Request ID:')}{color.yellow(request.request_id)}\n"
    return ""
