from logging import Log<PERSON><PERSON>ord
from unittest.mock import MagicMock, patch

from .filters import (
    AddExceptionTagFilter,
    CorrelationIdFilter,
    EverWeb<PERSON><PERSON>xtFilter,
    Exclude<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ilt<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ilter,
)


# --- CorrelationIdFilter ---
def test_correlation_id_filter_injects_context():
    filter_instance = CorrelationIdFilter()
    record = MagicMock()
    mock_request = MagicMock()
    mock_request.request_id = "req-123"
    mock_log_context = {"e2e_sync_run_id": "sync-456"}
    mock_celery_context = {"celery": True}
    with patch(
        "everstage_infra.logging.filters.get_current_request", return_value=mock_request
    ), patch(
        "everstage_infra.logging.filters.get_threadlocal_context",
        side_effect=[mock_log_context, mock_celery_context],
    ):
        result = filter_instance.filter(record)
    assert result is True
    assert record.request_id == "req-123"
    assert record.e2e_sync_run_id == "sync-456"
    assert record.correlation_id == "sync-456"
    assert record.log_context == mock_log_context
    assert record.celery_context == mock_celery_context


# --- EverWebContextFilter ---
def test_everweb_context_filter_injects_web_context():
    filter_instance = EverWebContextFilter()
    record = MagicMock()
    mock_request = MagicMock()
    mock_request.client_id = 42
    mock_request.domain = "example.com"
    mock_user = MagicMock()
    mock_user.username = "alice"
    mock_request.user = mock_user
    with patch(
        "everstage_infra.logging.filters.get_current_request", return_value=mock_request
    ):
        result = filter_instance.filter(record)
    assert result is True
    assert record.client_id == 42
    assert record.domain == "example.com"
    assert record.user == "alice"


def test_everweb_context_filter_handles_no_request():
    filter_instance = EverWebContextFilter()
    record = MagicMock()
    with patch(
        "everstage_infra.logging.filters.get_current_request", return_value=None
    ):
        result = filter_instance.filter(record)
    assert result is True
    # MagicMock always returns True for hasattr, so check __dict__
    assert "client_id" not in record.__dict__
    assert "domain" not in record.__dict__
    assert "user" not in record.__dict__


# --- ExcludeLogLevelFilter ---
def test_exclude_log_level_filter_excludes_level():
    filter_instance = ExcludeLogLevelFilter("INFO")
    record = MagicMock()
    record.levelname = "INFO"
    assert filter_instance.filter(record) is False


def test_exclude_log_level_filter_allows_other_levels():
    filter_instance = ExcludeLogLevelFilter("WARNING")
    record = MagicMock()
    record.levelname = "ERROR"
    assert filter_instance.filter(record) is True


# --- AddExceptionTagFilter ---
def test_add_exception_tag_filter_adds_tag():
    filter_instance = AddExceptionTagFilter()
    record = MagicMock()
    record.levelname = "ERROR"
    record.name = "myapp.module"
    record.exception_tag = "custom_tag"
    result = filter_instance.filter(record)
    assert result is True
    assert record.exception is True
    assert record.exception_tag == "custom_tag"


def test_add_exception_tag_filter_default_tag():
    filter_instance = AddExceptionTagFilter()
    record = MagicMock()
    record.levelname = "ERROR"
    record.name = "myapp.module"
    record.exception_tag = None
    result = filter_instance.filter(record)
    assert result is True
    assert record.exception is True
    assert record.exception_tag == "myapp"


def test_add_exception_tag_filter_handles_exception():
    filter_instance = AddExceptionTagFilter()
    record = MagicMock()
    record.levelname = "ERROR"
    record.name = None  # Will cause exception in get_exception_tag
    del record.exception_tag
    result = filter_instance.filter(record)
    assert result is True
    assert record.exception is True
    assert record.exception_tag == "other"


def test_add_exception_tag_filter_non_error_level():
    filter_instance = AddExceptionTagFilter()
    record = MagicMock()
    record.levelname = "INFO"
    result = filter_instance.filter(record)
    assert result is True
    # MagicMock always returns True for hasattr, so check __dict__
    assert "exception" not in record.__dict__


# --- PostgresFilter ---
def test_postgres_filter_allows_django_sql_log():
    filter_instance = PostgresFilter()
    record = MagicMock(spec=LogRecord)
    record.msg = "(0.123) SELECT * FROM table"
    with patch("everstage_infra.logging.filters.should_log_request", return_value=True):
        assert filter_instance.filter(record) is True


def test_postgres_filter_allows_request_boundary_log():
    filter_instance = PostgresFilter()
    record = MagicMock(spec=LogRecord)
    record.msg = "not a sql log"
    record.is_request_boundary = True
    with patch("everstage_infra.logging.filters.should_log_request", return_value=True):
        assert filter_instance.filter(record) is True


def test_postgres_filter_blocks_when_should_log_request_false():
    filter_instance = PostgresFilter()
    record = MagicMock(spec=LogRecord)
    record.msg = "(0.123) SELECT * FROM table"
    with patch(
        "everstage_infra.logging.filters.should_log_request", return_value=False
    ):
        assert filter_instance.filter(record) is False


def test_postgres_filter_blocks_non_sql_and_non_boundary():
    filter_instance = PostgresFilter()
    record = MagicMock(spec=LogRecord)
    record.msg = "not a sql log"
    if hasattr(record, "is_request_boundary"):
        delattr(record, "is_request_boundary")
    with patch("everstage_infra.logging.filters.should_log_request", return_value=True):
        assert filter_instance.filter(record) is False


# --- SnowflakeFilter (moved from test_sql_formatter.py) ---
def test_snowflake_filter():
    filter_instance = SnowflakeFilter()
    record = LogRecord(
        name="test",
        level=20,
        pathname="test.py",
        lineno=1,
        msg={"query": "SELECT * FROM table"},
        args=(),
        exc_info=None,
    )
    # Should reject dict with query key (current implementation only allows string msg)
    assert not filter_instance.filter(record)

    # Test Snowflake connector format (should be rejected by current implementation)
    record.msg = "running query [%s]"
    record.args = ("SELECT * FROM table",)
    assert not filter_instance.filter(record)

    # Test non-query string
    record.msg = "some other message"
    assert not filter_instance.filter(record)

    # Test dict without query key
    record.msg = {"other_key": "value"}
    assert not filter_instance.filter(record)

    # Test invalid message type
    record.msg = None
    assert not filter_instance.filter(record)

    # Test record without msg attribute
    delattr(record, "msg")
    assert not filter_instance.filter(record)


def test_snowflake_filter_additional_cases():
    filter_instance = SnowflakeFilter()
    with patch("everstage_infra.logging.filters.should_log_request", return_value=True):
        # Case: msg is exactly 'running query' (should be accepted)
        record = LogRecord(
            name="test",
            level=20,
            pathname="test.py",
            lineno=1,
            msg="running query",
            args=(),
            exc_info=None,
        )
        assert filter_instance.filter(record) is True

        # Case: msg is 'running query something else' (should be accepted)
        record.msg = "running query something else"
        assert filter_instance.filter(record) is True

        # Case: msg is 'RUNNING QUERY' (case-sensitive, should be rejected)
        record.msg = "RUNNING QUERY"
        assert filter_instance.filter(record) is False

        # Case: msg is ' running query' (leading space, should be rejected)
        record.msg = " running query"
        assert filter_instance.filter(record) is False

        # Case: msg is a string not starting with 'running query' but containing it (should be rejected)
        record.msg = "foo running query bar"
        assert filter_instance.filter(record) is False

        # Case: msg is an empty string (should be rejected)
        record.msg = ""
        assert filter_instance.filter(record) is False
