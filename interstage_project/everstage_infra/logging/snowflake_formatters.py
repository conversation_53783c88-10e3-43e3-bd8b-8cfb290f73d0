"""Snowflake SQL Query Formatters and Filters.

This module provides formatters and filters specifically for logging Snowflake
SQL queries, supporting both plaintext colored output and structured JSON output.
"""

import json
import logging
import threading
from logging import LogRecord
from typing import Any, Dict, Set

from .color_providers import ColorProviderFactory
from .log_utils import request_path, should_log_request
from .sql_formatter import get_sql_formatter
from .sql_formatter_utils import (
    QueryCounter,
    RequestContextProvider,
    SqlQueryParser,
    get_ist_timestamp,
    request_id_line,
)


class SnowflakeFormatter(logging.Formatter):
    """Format Snowflake queries with clear visual separation and syntax highlighting.

    This formatter processes Snowflake query logs and formats them with:
    1. Query number (incremented for each query)
    2. The SQL query with proper syntax highlighting
    3. Table names used in the query
    4. Clear visual separation between queries

    Handles both custom dictionary format and Snowflake connector's native format.
    """

    def __init__(self, *args: Any, simplify_query: bool = False, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self._style = logging.PercentStyle("%(message)s")
        self._color_provider = ColorProviderFactory.create()
        self._simplify_query = simplify_query

    def format(self, record: LogRecord) -> str:
        try:
            if not hasattr(record, "msg"):
                return super().format(record)

            # Extract query from the record
            # Snowflake logs come as: msg='running query [%s]', args=('actual query...',)
            if not record.args or len(record.args) == 0:
                return super().format(record)

            query = record.args[
                0
            ]  # type: ignore # Extract the first argument which contains the actual query
            if not query:
                return super().format(record)

            formatted_query, table_names = get_sql_formatter("sqlparse").format_query(
                query, simplify=self._simplify_query
            )

            # Get query number and IST timestamp
            query_num = QueryCounter.increment()
            timestamp = get_ist_timestamp()
            color = self._color_provider

            # Build the formatted output
            result = [
                f"\n{color.separator}",
                request_id_line(),
                f"{color.cyan('Path:')}{color.yellow(request_path())}\n",
                f"{color.magenta('Timestamp:')} {timestamp}",
                f"{color.bold(color.yellow(f'[Snowflake Query #{query_num}]'))}",
            ]

            # Add table names if any were found
            if table_names:
                result.append(
                    f"{color.cyan('Tables:')} {', '.join(sorted(table_names))}"
                )

            # Add the formatted query
            result.extend(
                [
                    f"{color.green(formatted_query)}",
                    f"{color.separator}\n",
                ]
            )

            return "\n".join(result)
        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            return super().format(record)


class SnowflakeJsonFormatter(logging.Formatter):
    """JSON-only Snowflake formatter for structured logging.

    This formatter processes Snowflake query logs and outputs them
    as structured JSON entries suitable for analysis and monitoring.
    Each log entry contains query metadata and request context information.

    Handles both custom dictionary format and Snowflake connector's native format:
    1. Dictionary format: {'query': 'SELECT ...'}
    2. String format: 'running query [SELECT ...]'
    """

    def __init__(
        self,
        *args: Any,
        simplify_query: bool = False,
        tables_to_mute: Set[str] | None = None,
        **kwargs: Any,
    ) -> None:
        """Initialize the JSON formatter.

        Args:
            simplify_query: Whether to simplify SQL queries during formatting
            tables_to_mute: Set of table names to mark as muted
        """
        super().__init__(*args, **kwargs)
        self._parser = SqlQueryParser(
            simplify_query=simplify_query, tables_to_mute=tables_to_mute
        )
        self._context_provider = RequestContextProvider()

    def format(self, record: logging.LogRecord) -> str:
        """Format the log record as JSON.

        Args:
            record: The log record to format

        Returns:
            JSON string representation of the log entry
        """
        try:
            # Process Snowflake queries
            if self._is_snowflake_query_record(record):
                return self._format_snowflake_query_json(record)

            # Fall back to default formatting for other records
            ist_time = get_ist_timestamp()
            return json.dumps(
                {
                    "timestamp": ist_time,
                    "level": record.levelname,
                    "message": str(record.getMessage()),
                    "logger": record.name,
                }
            )

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ) as e:
            # If JSON formatting fails, return a simple error record
            ist_time = get_ist_timestamp()
            return json.dumps(
                {
                    "timestamp": ist_time,
                    "level": "ERROR",
                    "message": f"JSON formatting error: {str(e)}",
                    "original_message": str(record.getMessage()),
                    "logger": record.name,
                }
            )

    def _is_snowflake_query_record(self, record: logging.LogRecord) -> bool:
        """Check if the record is a Snowflake query log.

        Args:
            record: The log record to check

        Returns:
            True if this is a Snowflake query record, False otherwise
        """
        if not should_log_request():
            return False

        if isinstance(record.msg, str) and record.msg.startswith("running query"):
            return True

        return False

    def _format_snowflake_query_json(self, record: logging.LogRecord) -> str:
        """Format a Snowflake query record as JSON.

        Args:
            record: The Snowflake query log record

        Returns:
            JSON string representation of the Snowflake query
        """
        try:
            # Extract query from the record
            # Snowflake logs come as: msg='running query [%s]', args=('actual query...',)
            if not record.args or len(record.args) == 0:
                return self._create_parse_error_json(record, "No query found in record")

            query = record.args[
                0
            ]  # Extract the first argument which contains the actual query
            if not query:
                return self._create_parse_error_json(
                    record, "Empty query found in record"
                )

            # Parse the Snowflake query
            parsed_data = self._parser.parse_snowflake_query(query)
            if not parsed_data:
                return self._create_parse_error_json(record)

            # Get request context
            context = self._context_provider.get_request_context()

            # Get query number
            query_number = QueryCounter.increment()

            # Create JSON log entry for Snowflake
            json_entry = create_snowflake_json_log_entry(
                parsed_data=parsed_data,
                record=record,
                query_number=query_number,
                context=context,
            )
            return json.dumps(json_entry)

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ) as e:
            return self._create_parse_error_json(record, str(e))

    def _create_parse_error_json(
        self, record: logging.LogRecord, error: str | None = None
    ) -> str:
        """Create a JSON entry for parsing errors.

        Args:
            record: The original log record
            error: Optional error message

        Returns:
            JSON string for the parse error
        """
        ist_time = get_ist_timestamp()
        error_data = {
            "timestamp": ist_time,
            "type": "parse_error",
            "level": "WARNING",
            "message": (
                f"Failed to parse Snowflake log: {error}"
                if error
                else "Failed to parse Snowflake log"
            ),
            "original_message": str(record.msg),
            "logger": record.name,
            "thread_id": str(threading.get_ident()),
        }

        return json.dumps(error_data)


def create_snowflake_json_log_entry(
    parsed_data: Dict[str, Any], record: Any, query_number: int, context: Dict[str, Any]
) -> Dict[str, Any]:
    """Create a structured JSON log entry for Snowflake queries.

    Args:
        parsed_data: Parsed SQL query data
        record: The log record
        query_number: The query number
        context: Request context information

    Returns:
        Dictionary ready for JSON serialization
    """
    # Create timestamp in IST timezone
    ist_time = get_ist_timestamp()

    return {
        "timestamp": ist_time,
        "request_id": context["request_id"],
        "endpoint": context["endpoint"],
        "query_number": query_number,
        "thread_id": str(threading.get_ident()),
        "execution_time_ms": parsed_data["execution_time_ms"],
        "database_type": "snowflake",
        "tables": parsed_data["table_names"],
        "query_type": parsed_data["query_type"],
        "query": parsed_data["formatted_query"],
        "query_simplified": parsed_data["formatted_query"],
        "is_simplified": parsed_data["is_simplified"],
        "query_hash": parsed_data["query_hash"],
        "client_id": context["client_id"],
        "log_level": record.levelname,
        "is_muted": parsed_data["is_muted"],
    }
