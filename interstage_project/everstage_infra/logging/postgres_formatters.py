"""JSON-only PostgreSQL formatter.

This module provides a PostgreSQL formatter that outputs only structured JSON
logs for analysis purposes, using shared utility functions.
"""

import json
import logging
import threading
from datetime import datetime
from logging import LogRecord
from typing import Any, Dict, Set
from zoneinfo import ZoneInfo

from rest_framework import status

from .color_providers import ColorProviderFactory
from .log_utils import request_path, should_log_request
from .sql_formatter_utils import (
    QueryCounter,
    RequestContextProvider,
    SqlQueryParser,
    format_thread_id,
    get_ist_timestamp,
    request_id_line,
)


class PostgresJsonFormatter(logging.Formatter):
    """JSON-only PostgreSQL formatter for structured logging.

    This formatter processes Django's database query logs and outputs them
    as structured JSON entries suitable for analysis and monitoring.
    Each log entry contains query metadata, execution statistics, and
    request context information.
    """

    def __init__(
        self,
        *args: Any,
        simplify_query: bool = False,
        tables_to_mute: Set[str] | None = None,
        **kwargs: Any,
    ) -> None:
        """Initialize the JSON formatter.

        Args:
            simplify_query: Whether to simplify SQL queries during formatting
            tables_to_mute: Set of table names to mark as muted
        """
        super().__init__(*args, **kwargs)
        self._parser = SqlQueryParser(
            simplify_query=simplify_query, tables_to_mute=tables_to_mute
        )
        self._context_provider = RequestContextProvider()

    def format(self, record: logging.LogRecord) -> str:
        """Format the log record as JSON.

        Args:
            record: The log record to format

        Returns:
            JSON string representation of the log entry
        """
        try:
            # Process SQL queries
            if self._is_sql_query_record(record):
                return self._format_sql_query_json(record)

            # Fall back to default formatting for other records
            ist_time = get_ist_timestamp()
            return json.dumps(
                {
                    "timestamp": ist_time,
                    "level": record.levelname,
                    "message": str(record.getMessage()),
                    "logger": record.name,
                }
            )

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ) as e:
            # If JSON formatting fails, return a simple error record
            ist_time = get_ist_timestamp()
            return json.dumps(
                {
                    "timestamp": ist_time,
                    "level": "ERROR",
                    "message": f"JSON formatting error: {str(e)}",
                    "original_message": str(record.getMessage()),
                    "logger": record.name,
                }
            )

    def _is_sql_query_record(self, record: logging.LogRecord) -> bool:
        """Check if the record is a SQL query log.

        Args:
            record: The log record to check

        Returns:
            True if this is a SQL query record, False otherwise
        """
        return (
            hasattr(record, "msg")
            and isinstance(record.msg, str)
            and record.msg.startswith("(")
            and should_log_request()
        )

    def _format_sql_query_json(self, record: logging.LogRecord) -> str:
        """Format a SQL query record as JSON.

        Args:
            record: The SQL query log record

        Returns:
            JSON string representation of the SQL query
        """
        try:
            # Handle % formatting in the message - same as PostgresFormatter
            msg = record.msg % record.args if record.args else record.msg

            # Parse the formatted SQL log message
            parsed_data = self._parser.parse_postgres_log_message(msg)
            if not parsed_data:
                return self._create_parse_error_json(record)

            # Get request context
            context = self._context_provider.get_request_context()

            # Get query number
            query_number = QueryCounter.increment()

            # Create JSON log entry
            json_entry = create_json_log_entry(
                parsed_data=parsed_data,
                record=record,
                query_number=query_number,
                context=context,
            )

            return json.dumps(json_entry)

        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ) as e:
            return self._create_parse_error_json(record, str(e))

    def _create_parse_error_json(
        self, record: logging.LogRecord, error: str | None = None
    ) -> str:
        """Create a JSON entry for parsing errors.

        Args:
            record: The original log record
            error: Optional error message

        Returns:
            JSON string for the parse error
        """
        ist_time = datetime.now(ZoneInfo("Asia/Kolkata"))
        error_data = {
            "timestamp": ist_time.isoformat(),
            "type": "parse_error",
            "level": "WARNING",
            "message": (
                f"Failed to parse SQL log: {error}"
                if error
                else "Failed to parse SQL log"
            ),
            "original_message": str(record.msg),
            "logger": record.name,
            "thread_id": str(threading.get_ident()),
        }

        return json.dumps(error_data)


class PostgresFormatter(logging.Formatter):
    """Format PostgreSQL queries with execution time and arguments.

    This formatter processes Django's database query logs and formats them with:
    1. Query number (incremented for each query)
    2. Execution time (extracted from the log message)
    3. The SQL query with proper syntax highlighting
    4. Query arguments (if present)
    5. Clear visual separation between queries

    Request boundary logs are also handled by this formatter since otherwise we are not able to ensure that
    logs from 2 different handlers are written in the correct order to the same file.
    """

    def __init__(
        self,
        *args: Any,
        simplify_query: bool = False,
        tables_to_mute: Set[str] | None = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(*args, **kwargs)
        self._color_provider = ColorProviderFactory.create()
        self._parser = SqlQueryParser(
            simplify_query=simplify_query, tables_to_mute=tables_to_mute
        )

    def format(self, record: LogRecord) -> str:
        if hasattr(record, "is_request_boundary"):
            return _format_request_boundary(record)
        try:
            # Handle % formatting in the message
            msg = record.msg % record.args if record.args else record.msg

            # Parse the SQL log message using shared utilities
            parsed_data = self._parser.parse_postgres_log_message(msg)
            if not parsed_data:
                # If parsing fails, return the original message
                return super().format(record)

            # Get the next query number and current thread ID
            query_num = QueryCounter.increment()
            thread_id = threading.get_ident()
            short_thread_id = format_thread_id(thread_id)

            # Get current IST timestamp
            timestamp = get_ist_timestamp()

            # Format the output with colors
            color = self._color_provider

            # Build thread info with proper string formatting
            thread_text = f"Thread-{short_thread_id}"
            colored_thread = color.thread_color(thread_text, thread_id)
            query_header = f"PostgreSQL Query #{query_num} [{colored_thread}]"

            # Build the result
            result = (
                f"\n{color.separator}\n"
                f"{request_id_line()}\n"
                f"{color.cyan('Path:')}{color.yellow(request_path())}\n"
                f"{color.magenta('Timestamp:')} {timestamp}\n"
                f"{color.bold(query_header)}\n"
                f"{color.yellow('Execution time:')} {parsed_data['execution_time']}s\n"
                f"{color.cyan('Tables:')} {', '.join(parsed_data['table_names'])}\n"
                f"{color.bold('Query:')}\n"
                f"{color.green(parsed_data['formatted_query'])}\n"
            )

            # Arguments removed per user request

            result += f"\n{color.separator}\n"
        except (
            Exception  # noqa: BLE001 - logging errors should not break application flow
        ):
            # If parsing fails, return the original message
            return super().format(record)
        else:
            return result


def create_json_log_entry(
    parsed_data: Dict[str, Any], record: Any, query_number: int, context: Dict[str, Any]
) -> Dict[str, Any]:
    """Create a structured JSON log entry.

    Args:
        parsed_data: Parsed SQL query data
        record: The log record
        query_number: The query number
        context: Request context information

    Returns:
        Dictionary ready for JSON serialization
    """
    # Create timestamp in IST timezone
    ist_time = get_ist_timestamp()

    return {
        "timestamp": ist_time,
        "request_id": context["request_id"],
        "endpoint": context["endpoint"],
        "query_number": query_number,
        "thread_id": str(threading.get_ident()),
        "execution_time_ms": parsed_data["execution_time_ms"],
        "database_type": "postgresql",
        "tables": parsed_data["table_names"],
        "query_type": parsed_data["query_type"],
        "query": parsed_data["formatted_query"],
        "query_simplified": parsed_data["formatted_query"],
        "is_simplified": parsed_data["is_simplified"],
        "query_hash": parsed_data["query_hash"],
        "client_id": context["client_id"],
        "log_level": record.levelname,
        "is_muted": parsed_data["is_muted"],
    }


def _format_request_boundary(record: logging.LogRecord) -> str:
    event_type = getattr(record, "event_type", "unknown")
    request_id = getattr(record, "request_id", "unknown")
    client_id = getattr(record, "client_id", "unknown")
    user = getattr(record, "user", "unknown")
    method = getattr(record, "method", "unknown")
    path = getattr(record, "path", "unknown")

    timestamp = get_ist_timestamp()
    color = ColorProviderFactory.create()

    if event_type == "request_start":
        return (
            f"\n{color.separator}\n"
            f"{color.bold(color.green(f'🚀 REQUEST START [{request_id}]'))}\n"
            f"{color.magenta('Timestamp:')} {timestamp}\n"
            f"{color.cyan('Client:')}{color.yellow(client_id)} | "
            f"{color.cyan('User:')}{color.yellow(user)} | "
            f"{color.cyan('Method:')}{color.yellow(f'{method} {path}')}\n"
            f"{color.separator}\n"
        )
    if event_type == "request_end":
        duration = getattr(record, "duration", "unknown")
        status_code = getattr(record, "status_code", "unknown")

        # Color code status
        status_text = str(status_code)
        if isinstance(status_code, int):
            if status.HTTP_200_OK <= status_code < status.HTTP_300_MULTIPLE_CHOICES:
                status_colored = color.green(status_text)
            elif (
                status.HTTP_400_BAD_REQUEST
                <= status_code
                < status.HTTP_500_INTERNAL_SERVER_ERROR
            ):
                status_colored = color.yellow(status_text)
            else:
                status_colored = color.red(status_text)
        else:
            status_colored = color.yellow(status_text)

        return (
            f"\n{color.separator}\n"
            f"{color.magenta('Timestamp:')} {timestamp}\n"
            f"{color.bold(color.blue(f'✅ REQUEST END [{request_id}]'))}\n"
            f"{color.cyan('Duration:')}{color.yellow(duration)}s | "
            f"{color.cyan('Status:')}{status_colored} | "
            f"{color.cyan('Client:')}{color.yellow(client_id)} | "
            f"{color.cyan('User:')}{color.yellow(user)} | "
            f"{color.cyan('Method:')}{color.yellow(f'{method} {path}')}\n"
            f"{color.separator}\n"
        )
    return "Unknown Request Boundary"
