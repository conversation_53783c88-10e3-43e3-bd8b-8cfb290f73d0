"""SQL Query Formatting Utilities.

This module provides utilities for formatting SQL queries with different formatters.
It supports both custom highlighting and third-party formatters like sqlparse.
"""

from abc import ABC, abstractmethod
from typing import Set, Tuple

import sqlparse

from .sql_highlighter import SQLHighlighter


class SQLFormatterBase(ABC):
    """Base class for SQL formatters."""

    @abstractmethod
    def format_query(self, query: str, **kwargs) -> Tuple[str, Set[str]]:
        """Format a SQL query and extract table names.

        Args:
            query: The SQL query to format

        Returns:
            Tuple containing:
                - The formatted query with syntax highlighting
                - Set of table names found in the query
        """
        pass


class SqlparseFormatter(SQLFormatterBase):
    """SQL formatter using sqlparse library."""

    @staticmethod
    def _simplify_query(query: str) -> str:
        """Simplify a SQL query by removing unnecessary prefixes and quotes.
        This may alter query semantics but the intention here is to make the query more readable.
        If you need to run the query as is from an editor, make sure you add back the necessary prefixes.

        This method:
        1. Removes all table prefixes from column references
        2. Removes unnecessary quotes from identifiers
        3. Preserves quotes only when necessary (e.g., when identifier contains spaces)

        Args:
            query: The SQL query to simplify

        Returns:
            The simplified query
        """
        import re

        def unquote_identifier(match):
            """Remove quotes from an identifier if not needed."""
            identifier = match.group(1)
            # Keep quotes if identifier contains spaces or special chars
            if any(c in identifier for c in " -()[]{}"):
                return f'"{identifier}"'
            return identifier

        def simplify_column_ref(match):
            """Simplify column references by removing table prefixes."""
            # The column part (with or without quotes)
            col = match.group(2)
            if col.startswith('"') and col.endswith('"'):
                col = col[1:-1]
                # Keep quotes if needed
                if any(c in col for c in " -()[]{}"):
                    col = f'"{col}"'
            return col

        # First pass: Remove all table prefixes from column references
        # This matches patterns like:
        # - table.column
        # - "table"."column"
        # - table_name.column
        pattern = r'("[^"]+"|[a-zA-Z][a-zA-Z0-9_]*)\.([^,\s\)]+|"[^"]+")'
        query = re.sub(pattern, simplify_column_ref, query)

        # Second pass: Remove unnecessary quotes from remaining identifiers
        query = re.sub(r'"([^"]+)"', unquote_identifier, query)

        return query

    def format_query(
        self,
        query: str,
        *,
        simplify: bool = False,
        tables_to_mute: Set[str] | None = None,
    ) -> Tuple[str, Set[str]]:
        """Format a SQL query using sqlparse and extract table names.

        Args:
            query: The SQL query to format

        Returns:
            Tuple containing:
                - The formatted query with syntax highlighting
                - Set of table names found in the query
        """
        # First format the query using sqlparse
        formatted = sqlparse.format(
            query,
            reindent=True,
            keyword_case="upper",
            indent_width=4,
            # Don't wrap lines to show full queries
            wrap_after=80,
        )

        # Simplify the formatted query
        simplified = self._simplify_query(formatted) if simplify else formatted

        # Then extract and highlight table names
        highlighted, table_names = SQLHighlighter.extract_and_highlight_tables(
            simplified
        )

        if (
            tables_to_mute is not None
            and len(table_names) == 1
            and table_names.intersection(tables_to_mute)
        ):
            # If interstage_clients is the only table, then use the query as <interstage_clients_select_query>
            # check for set intersection between table_names and tables_to_mute
            highlighted = f"<{list(table_names)[0]}_select_query>"

        return highlighted, table_names


# Default formatter to use
DEFAULT_FORMATTER = "sqlparse"  # or 'sqlparse'


def get_sql_formatter(formatter_type: str = DEFAULT_FORMATTER) -> SQLFormatterBase:
    """Get the appropriate SQL formatter based on type.

    Args:
        formatter_type: Type of formatter to use ('custom' or 'sqlparse')

    Returns:
        An instance of SQLFormatterBase
    """
    formatters = {"sqlparse": SqlparseFormatter()}

    return formatters.get(formatter_type, SqlparseFormatter())
