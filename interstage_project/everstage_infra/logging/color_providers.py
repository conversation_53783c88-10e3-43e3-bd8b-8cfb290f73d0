"""Color Provider System for Configurable ANSI Formatting.

This module provides a configurable color provider pattern that allows enabling/disabling
ANSI color formatting without scattered conditional logic throughout the codebase.
"""

import os
from abc import ABC, abstractmethod


class ColorProvider(ABC):
    """Abstract base class for color providers."""

    @abstractmethod
    def blue(self, text: str) -> str:
        """Apply blue color to text."""
        pass

    @abstractmethod
    def green(self, text: str) -> str:
        """Apply green color to text."""
        pass

    @abstractmethod
    def yellow(self, text: str) -> str:
        """Apply yellow color to text."""
        pass

    @abstractmethod
    def red(self, text: str) -> str:
        """Apply red color to text."""
        pass

    @abstractmethod
    def cyan(self, text: str) -> str:
        """Apply cyan color to text."""
        pass

    @abstractmethod
    def magenta(self, text: str) -> str:
        """Apply magenta color to text."""
        pass

    @abstractmethod
    def bold(self, text: str) -> str:
        """Apply bold formatting to text."""
        pass

    @abstractmethod
    def thread_color(self, text: str, thread_id: int) -> str:
        """Apply thread-specific color to text."""
        pass

    @property
    @abstractmethod
    def separator(self) -> str:
        """Get separator line."""
        pass


class ANSIColorProvider(ColorProvider):
    """Color provider that uses ANSI escape codes."""

    # ANSI Color codes
    BLUE = "\033[94m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    RED = "\033[91m"
    CYAN = "\033[96m"
    MAGENTA = "\033[95m"
    BOLD = "\033[1m"
    RESET = "\033[0m"

    # Thread color options (bright colors for better visibility)
    _THREAD_COLORS = [
        "\033[38;5;199m",  # Bright pink
        "\033[38;5;47m",  # Bright green
        "\033[38;5;51m",  # Bright cyan
        "\033[38;5;214m",  # Bright orange
        "\033[38;5;183m",  # Light purple
        "\033[38;5;220m",  # Gold
        "\033[38;5;208m",  # Orange
        "\033[38;5;147m",  # Light blue
    ]

    def blue(self, text: str) -> str:
        return f"{self.BLUE}{text}{self.RESET}"

    def green(self, text: str) -> str:
        return f"{self.GREEN}{text}{self.RESET}"

    def yellow(self, text: str) -> str:
        return f"{self.YELLOW}{text}{self.RESET}"

    def red(self, text: str) -> str:
        return f"{self.RED}{text}{self.RESET}"

    def cyan(self, text: str) -> str:
        return f"{self.CYAN}{text}{self.RESET}"

    def magenta(self, text: str) -> str:
        return f"{self.MAGENTA}{text}{self.RESET}"

    def bold(self, text: str) -> str:
        return f"{self.BOLD}{text}{self.RESET}"

    def thread_color(self, text: str, thread_id: int) -> str:
        """Apply thread-specific color to text based on thread ID."""
        color_index = hash(thread_id) % len(self._THREAD_COLORS)
        thread_color = self._THREAD_COLORS[color_index]
        return f"{thread_color}{text}{self.RESET}"

    @property
    def separator(self) -> str:
        return self.blue("=" * 100)


class NoColorProvider(ColorProvider):
    """Color provider that returns plain text without any formatting."""

    def blue(self, text: str) -> str:
        return text

    def green(self, text: str) -> str:
        return text

    def yellow(self, text: str) -> str:
        return text

    def red(self, text: str) -> str:
        return text

    def cyan(self, text: str) -> str:
        return text

    def magenta(self, text: str) -> str:
        return text

    def bold(self, text: str) -> str:
        return text

    def thread_color(self, text: str, _thread_id: int) -> str:
        return text

    @property
    def separator(self) -> str:
        return "=" * 100


class ColorProviderFactory:
    """Factory for creating color providers based on configuration."""

    @staticmethod
    def create() -> ColorProvider:
        """Create appropriate color provider based on configuration.

        Returns:
            ColorProvider: ANSIColorProvider if colors are enabled, NoColorProvider otherwise
        """
        # Check environment variable first, then Django setting, default to True
        colors_enabled = (
            os.getenv("EVERSTAGE_SQL_LOGGING_COLORS", "false").lower() == "true"
        )

        return ANSIColorProvider() if colors_enabled else NoColorProvider()
