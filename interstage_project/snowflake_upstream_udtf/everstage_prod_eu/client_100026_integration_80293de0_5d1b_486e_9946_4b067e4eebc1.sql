CREATE OR REPLACE FUNCTION client_100026_integration_80293de0_5d1b_486e_9946_4b067e4eebc1(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        # Filtering for Enterprise object
        # invoice_id should not contain MYD
        invoice_id = source_data.get("invoice_id") or ""
        if "MYD" in invoice_id:
            yield None
        else:
            yield (source_data,)
$$;