"""
Refactored comprehensive test suite for update_expression_columns function.

This version centralizes test data, uses parameterized tests extensively,
and provides better organization while maintaining complete test coverage.
"""

from dataclasses import dataclass
from typing import Dict, List, Tuple
from unittest.mock import patch

import pytest

from superset.services.db_meta_data_services import update_expression_columns

# ============================================================================
# CENTRALIZED TEST DATA STRUCTURES
# ============================================================================


@dataclass
class TestCase:
    """Structure for individual test case data."""

    name: str
    expression: str
    mapping: Dict[str, str]
    expected: str
    mode: str = "expression"
    description: str = ""


class ColumnMappings:
    """Centralized column mapping definitions."""

    SIMPLE = {"sales_amount": "revenue"}

    BASIC_MULTI = {
        "sales_amount": "revenue_amount",
        "commission_rate": "fee_rate",
        "region": "territory",
    }

    OVERLAPPING = {
        "sales": "revenue",
        "total_sales": "total_revenue",
        "sales_amount": "revenue_amount",
        "avg_sales": "avg_revenue",
        "avg_sales_amount": "avg_revenue_amount",
    }

    CASE_SENSITIVE = {
        "Amount": "Value",
        "AMOUNT": "VALUE",
        "amount": "value",
        "AmOuNt": "VaLuE",
    }

    SPECIAL_CHARS = {
        "column-with-dashes": "new-column",
        "column_with_underscores": "new_column_underscores",
        "column.with.dots": "new_column_dots",
        "column(with)parens": "new_column_parens",
        "column[with]brackets": "new_column_brackets",
    }

    UNICODE = {"销售额": "revenue", "收入": "income", "客户": "customer"}

    NUMERIC = {
        "col1": "first_col",
        "column_2": "second_col",
        "3rd_column": "third_col",
        "column4th": "fourth_col",
        "1st_column": "first_column",
        "2nd_column": "second_column",
        "999_column": "last_column",
    }

    # NEW: Circular mapping scenarios
    CIRCULAR_2WAY = {"a": "b", "b": "a"}

    CIRCULAR_3WAY = {"a": "b", "b": "c", "c": "a"}

    CIRCULAR_4WAY = {"a": "b", "b": "c", "c": "d", "d": "a"}

    CIRCULAR_5WAY = {"a": "b", "b": "c", "c": "d", "d": "e", "e": "a"}

    # Chain mappings (non-circular)
    CHAIN_SIMPLE = {"a": "new_a", "b": "new_b", "c": "new_c"}

    CHAIN_OVERLAPPING = {
        "sales": "revenue",
        "total_sales": "total_revenue",
        "sales_rep": "revenue_rep",
    }

    CHAIN_PREFIX = {
        "col": "field",
        "col_name": "field_name",
        "col_name_display": "field_name_display",
    }

    # Self-referential and identity mappings
    IDENTITY_MIXED = {"sales": "sales", "commission": "fee", "region": "region"}

    # Swap scenarios
    SWAP_SIMPLE = {"source": "target", "target": "source"}

    # Case-sensitive circular
    CASE_CIRCULAR = {"Field_A": "Field_B", "Field_B": "Field_C", "Field_C": "Field_A"}

    # NEW: Advanced special characters for enhanced regex testing
    ADVANCED_SPECIAL_CHARS = {
        "/#Amount@/": "amount_edited",
        "$price%": "price_value",
        "column^power": "power_column",
        "data&info": "combined_data",
        "field#hash": "hash_field",
        "~pattern*": "pattern_field",
    }

    # PostgreSQL specific operators
    POSTGRESQL_OPERATORS = {
        "json_field": "new_json_field",
        "text_field": "new_text_field",
        "array_col": "new_array_col",
        "pattern_col": "new_pattern_col",
    }

    # JSON and array access
    JSON_ARRAY_ACCESS = {
        "json_data": "structured_data",
        "array_field": "list_field",
        "nested_json": "flat_json",
    }

    # Advanced whitespace and formatting
    WHITESPACE_FORMATTING = {
        "col_with_newlines": "formatted_col",
        "tab_separated": "clean_separated",
        "carriage_return": "clean_return",
    }


class TestDataSets:
    """Centralized test data organized by functionality."""

    # Basic quote handling test cases
    DOUBLE_QUOTED_CASES = [
        TestCase(
            "simple",
            'SELECT "sales_amount" FROM table',
            ColumnMappings.SIMPLE,
            'SELECT "revenue" FROM table',
        ),
        TestCase(
            "multiple_same",
            '"sales_amount" + "sales_amount" * 0.1',
            ColumnMappings.SIMPLE,
            '"revenue" + "revenue" * 0.1',
        ),
        TestCase(
            "multiple_different",
            'SELECT "sales_amount", "commission_rate", "region"',
            ColumnMappings.BASIC_MULTI,
            'SELECT "revenue_amount", "fee_rate", "territory"',
        ),
        TestCase(
            "in_where",
            'WHERE "sales_amount" > 100 AND "region" = \'US\'',
            ColumnMappings.BASIC_MULTI,
            'WHERE "revenue_amount" > 100 AND "territory" = \'US\'',
        ),
    ]

    SINGLE_QUOTED_CASES = [
        TestCase(
            "simple",
            "SELECT 'sales_amount' FROM table",
            ColumnMappings.SIMPLE,
            "SELECT 'revenue' FROM table",
        ),
        TestCase(
            "multiple",
            "SELECT 'sales_amount', 'commission_rate' WHERE 'sales_amount' > 100",
            ColumnMappings.BASIC_MULTI,
            "SELECT 'revenue_amount', 'fee_rate' WHERE 'revenue_amount' > 100",
        ),
        TestCase(
            "arithmetic",
            "'sales_amount' + 'commission_rate'",
            ColumnMappings.BASIC_MULTI,
            "'revenue_amount' + 'fee_rate'",
        ),
    ]

    BARE_COLUMN_CASES = [
        TestCase(
            "simple",
            "SELECT sales_amount FROM table",
            ColumnMappings.SIMPLE,
            'SELECT "revenue" FROM table',
        ),
        TestCase(
            "at_start", "sales_amount > 1000", ColumnMappings.SIMPLE, '"revenue" > 1000'
        ),
        TestCase(
            "after_operators",
            "amount + sales_amount - other * sales_amount",
            ColumnMappings.SIMPLE,
            'amount + "revenue" - other * "revenue"',
        ),
        TestCase(
            "with_parens",
            "( sales_amount ) + sales_amount",
            ColumnMappings.SIMPLE,
            '( "revenue" ) + "revenue"',
        ),
        TestCase(
            "special_chars",
            "@sales_amount #sales_amount $sales_amount",
            ColumnMappings.SIMPLE,
            '@"revenue" #"revenue" $"revenue"',
        ),
    ]

    # NEW: Advanced Operator Boundary Cases for Enhanced Regex
    ADVANCED_OPERATOR_BOUNDARIES = [
        # PostgreSQL Cast Operator ::
        TestCase(
            "postgresql_cast_operator",
            "json_field::integer + text_field::text",
            ColumnMappings.POSTGRESQL_OPERATORS,
            '"new_json_field"::integer + "new_text_field"::text',
            description="PostgreSQL cast operator boundaries",
        ),
        # JSON Field Access Operators -> and ->>
        TestCase(
            "json_arrow_operators",
            "json_data->field + json_data->>text_field",
            ColumnMappings.JSON_ARRAY_ACCESS,
            '"structured_data"->field + "structured_data"->>text_field',
            description="JSON field access operators",
        ),
        # Pattern Matching Operators ~*, !~, etc.
        TestCase(
            "pattern_matching_operators",
            "pattern_col~*'pattern' AND pattern_col!~'other'",
            ColumnMappings.POSTGRESQL_OPERATORS,
            "\"new_pattern_col\"~*'pattern' AND \"new_pattern_col\"!~'other'",
            description="Pattern matching operators",
        ),
        # String Concatenation Operator ||
        TestCase(
            "string_concat_operator",
            "text_field||'_suffix' + text_field||'_prefix'",
            ColumnMappings.POSTGRESQL_OPERATORS,
            "\"new_text_field\"||'_suffix' + \"new_text_field\"||'_prefix'",
            description="String concatenation operator",
        ),
        # Array Access with Brackets [ ]
        TestCase(
            "array_access_brackets",
            "array_field[1] + array_field[key] + array_field['string_key']",
            ColumnMappings.JSON_ARRAY_ACCESS,
            '"list_field"[1] + "list_field"[key] + "list_field"[\'string_key\']',
            description="Array access with square brackets",
        ),
        # Curly Braces { }
        TestCase(
            "curly_braces_boundaries",
            "{json_data} + function{array_field}",
            ColumnMappings.JSON_ARRAY_ACCESS,
            '{"structured_data"} + function{"list_field"}',
            description="Curly braces as boundaries",
        ),
        # Regex Pattern Operators .*
        TestCase(
            "regex_pattern_operators",
            "pattern_col.*other + pattern_col~*wildcard",
            ColumnMappings.POSTGRESQL_OPERATORS,
            '"new_pattern_col".*other + "new_pattern_col"~*wildcard',
            description="Regex pattern operators",
        ),
        # Backslash Escaping
        TestCase(
            "backslash_boundaries",
            "text_field\\escape + text_field\\path\\to\\file",
            ColumnMappings.POSTGRESQL_OPERATORS,
            '"new_text_field"\\escape + "new_text_field"\\path\\to\\file',
            description="Backslash as boundary character",
        ),
    ]

    # NEW: Whitespace and Formatting Boundary Cases
    WHITESPACE_BOUNDARY_CASES = [
        # Newline Boundaries
        TestCase(
            "newline_boundaries",
            "col_with_newlines\n+ other_col\nWHERE col_with_newlines\n> 0",
            ColumnMappings.WHITESPACE_FORMATTING,
            '"formatted_col"\n+ other_col\nWHERE "formatted_col"\n> 0',
            description="Newline character boundaries",
        ),
        # Tab Boundaries
        TestCase(
            "tab_boundaries",
            "tab_separated\t+ other\tWHERE tab_separated\t= 'value'",
            ColumnMappings.WHITESPACE_FORMATTING,
            '"clean_separated"\t+ other\tWHERE "clean_separated"\t= \'value\'',
            description="Tab character boundaries",
        ),
        # Carriage Return Boundaries
        TestCase(
            "carriage_return_boundaries",
            "carriage_return\r+ other\rAND carriage_return\r<> null",
            ColumnMappings.WHITESPACE_FORMATTING,
            '"clean_return"\r+ other\rAND "clean_return"\r<> null',
            description="Carriage return character boundaries",
        ),
        # Mixed Whitespace
        TestCase(
            "mixed_whitespace_boundaries",
            "col_with_newlines\n\t+ tab_separated\t\r+ carriage_return\r\n",
            ColumnMappings.WHITESPACE_FORMATTING,
            '"formatted_col"\n\t+ "clean_separated"\t\r+ "clean_return"\r\n',
            description="Mixed whitespace character boundaries",
        ),
    ]

    # NEW: Complex Advanced SQL Scenarios
    ADVANCED_SQL_SCENARIOS = [
        # Complex PostgreSQL Query with Multiple Operators
        TestCase(
            "complex_postgresql_query",
            "SELECT json_data->>'name'::text, array_col[1]::integer FROM table WHERE pattern_col~*'test.*' AND json_data->'status'||'_processed'",
            ColumnMappings.POSTGRESQL_OPERATORS,
            "SELECT \"new_json_field\"->'name'::text, \"new_array_col\"[1]::integer FROM table WHERE \"new_pattern_col\"~*'test.*' AND \"new_json_field\"->'status'||'_processed'",
            description="Complex PostgreSQL query with multiple advanced operators",
        ),
        # Advanced CASE Statement with Special Operators
        TestCase(
            "advanced_case_statement",
            "CASE WHEN json_field->>'type' = 'A' THEN array_col[1]::numeric WHEN pattern_col~*'special.*' THEN text_field||'_suffix'::text ELSE 0 END",
            ColumnMappings.POSTGRESQL_OPERATORS,
            "CASE WHEN \"new_json_field\"->'type' = 'A' THEN \"new_array_col\"[1]::numeric WHEN \"new_pattern_col\"~*'special.*' THEN \"new_text_field\"||'_suffix'::text ELSE 0 END",
            description="Advanced CASE statement with special operators",
        ),
        # Window Function with JSON and Array Operations
        TestCase(
            "window_function_advanced",
            "ROW_NUMBER() OVER (PARTITION BY json_data->>'category' ORDER BY array_col[1]::numeric DESC)",
            ColumnMappings.JSON_ARRAY_ACCESS,
            'ROW_NUMBER() OVER (PARTITION BY "structured_data"->\'category\' ORDER BY "list_field"[1]::numeric DESC)',
            description="Window function with JSON and array operations",
        ),
        # Subquery with Advanced Operators
        TestCase(
            "subquery_advanced_operators",
            "SELECT * FROM (SELECT json_field->>'id'::integer as id, pattern_col~*'active.*' as is_active FROM table) WHERE id IN (SELECT array_col[1] FROM other_table)",
            ColumnMappings.POSTGRESQL_OPERATORS,
            'SELECT * FROM (SELECT "new_json_field"->\'id\'::integer as id, "new_pattern_col"~*\'active.*\' as is_active FROM table) WHERE id IN (SELECT "new_array_col"[1] FROM other_table)',
            description="Subquery with advanced operators",
        ),
    ]

    # NEW: Edge Cases for Advanced Regex Features
    ADVANCED_EDGE_CASES = [
        # Multiple Special Characters in Sequence
        TestCase(
            "function_call_prevention_advanced",
            "/#Amount@/() + $price%() + column^power() + data&info()",
            ColumnMappings.ADVANCED_SPECIAL_CHARS,
            "/#Amount@/() + $price%() + column^power() + data&info()",
            description="Should not match function calls even with special characters",
        ),
        # Column at Expression End with Advanced Operators
        TestCase(
            "column_at_end_advanced",
            "SELECT 1, 2, json_field",
            ColumnMappings.POSTGRESQL_OPERATORS,
            'SELECT 1, 2, "new_json_field"',
            description="Column at end of expression with advanced regex",
        ),
    ]

    # NEW: Performance Test Cases for Advanced Regex
    ADVANCED_PERFORMANCE_CASES = [
        # Many Special Characters
        TestCase(
            "many_special_characters",
            " + ".join([f"special_col_{i}@#$%^&*{i}" for i in range(20)]),
            {f"special_col_{i}@#$%^&*{i}": f"clean_col_{i}" for i in range(20)},
            " + ".join([f'"clean_col_{i}"' for i in range(20)]),
            description="Many columns with special characters",
        ),
    ]

    # Word boundary critical tests
    WORD_BOUNDARY_CASES = [
        TestCase(
            "boundaries_critical",
            "sales + total_sales + sales_amount + avg_sales",
            {"sales": "revenue", "sales_amount": "revenue_amount"},
            '"revenue" + total_sales + "revenue_amount" + avg_sales',
        ),
        TestCase(
            "overlapping_names",
            "sales + total_sales + sales_amount + avg_sales_amount",
            ColumnMappings.OVERLAPPING,
            '"revenue" + "total_revenue" + "revenue_amount" + "avg_revenue_amount"',
        ),
        TestCase(
            "function_exclusion",
            "sales_amount() + sales_amount",
            ColumnMappings.SIMPLE,
            'sales_amount() + "revenue"',
        ),
        TestCase(
            "complex_overlap",
            "a + ab + abc + abcd + a_b + a_bc",
            {
                "a": "x",
                "ab": "xy",
                "abc": "xyz",
                "abcd": "xyzw",
                "a_b": "x_y",
                "a_bc": "x_yz",
            },
            '"x" + "xy" + "xyz" + "xyzw" + "x_y" + "x_yz"',
        ),
    ]

    # String literal preservation (CRITICAL)
    STRING_LITERAL_CASES = [
        TestCase(
            "single_quoted_preserved",
            "WHERE status = 'active' AND region = 'sales_amount'",
            ColumnMappings.SIMPLE,
            "WHERE status = 'active' AND region = 'revenue'",
        ),
        TestCase(
            "double_quoted_preserved",
            'WHERE description = "Contains sales_amount data"',
            ColumnMappings.SIMPLE,
            'WHERE description = "Contains sales_amount data"',
        ),
        TestCase(
            "mixed_literal_column",
            "sales_amount > 1000 AND description LIKE 'high sales_amount%'",
            ColumnMappings.SIMPLE,
            "\"revenue\" > 1000 AND description LIKE 'high sales_amount%'",
        ),
        TestCase(
            "nested_quotes",
            "description = \"User said 'sales_amount is high'\" AND sales_amount > 1000",
            ColumnMappings.SIMPLE,
            'description = "User said \'sales_amount is high\'" AND "revenue" > 1000',
        ),
    ]

    # SQL Function tests
    SQL_FUNCTION_CASES = [
        TestCase(
            "aggregates",
            "SUM(sales_amount) + AVG(commission_rate) + COUNT(region)",
            ColumnMappings.BASIC_MULTI,
            'SUM("revenue_amount") + AVG("fee_rate") + COUNT("territory")',
        ),
        TestCase(
            "nested_functions",
            "ROUND(AVG(sales_amount), 2) + FLOOR(MAX(commission_rate))",
            ColumnMappings.BASIC_MULTI,
            'ROUND(AVG("revenue_amount"), 2) + FLOOR(MAX("fee_rate"))',
        ),
        TestCase(
            "window_function",
            "ROW_NUMBER() OVER (PARTITION BY region ORDER BY sales_amount DESC)",
            ColumnMappings.BASIC_MULTI,
            'ROW_NUMBER() OVER (PARTITION BY "territory" ORDER BY "revenue_amount" DESC)',
        ),
        TestCase(
            "analytical_lag",
            "LAG(sales_amount, 1) OVER (PARTITION BY region ORDER BY date)",
            {
                "sales_amount": "revenue",
                "region": "territory",
                "date": "transaction_date",
            },
            'LAG("revenue", 1) OVER (PARTITION BY "territory" ORDER BY "transaction_date")',
        ),
    ]

    # CASE statement tests
    CASE_STATEMENT_CASES = [
        TestCase(
            "simple_case",
            "CASE WHEN sales_amount > 1000 THEN 'high' ELSE 'low' END",
            ColumnMappings.SIMPLE,
            "CASE WHEN \"revenue\" > 1000 THEN 'high' ELSE 'low' END",
        ),
        TestCase(
            "complex_case",
            "CASE WHEN sales_amount > 1000 AND region = 'US' THEN commission_rate * 1.1 ELSE 0 END",
            ColumnMappings.BASIC_MULTI,
            'CASE WHEN "revenue_amount" > 1000 AND "territory" = \'US\' THEN "fee_rate" * 1.1 ELSE 0 END',
        ),
    ]

    # Edge cases
    EDGE_CASE_SCENARIOS = [
        TestCase(
            "special_chars",
            '"column-with-dashes" + "column_with_underscores" + "column.with.dots"',
            ColumnMappings.SPECIAL_CHARS,
            '"new-column" + "new_column_underscores" + "new_column_dots"',
        ),
        TestCase(
            "unicode",
            '"销售额" + "收入" + "客户"',
            ColumnMappings.UNICODE,
            '"revenue" + "income" + "customer"',
        ),
        TestCase(
            "numeric_names",
            '"col1" + "column_2" + "3rd_column"',
            ColumnMappings.NUMERIC,
            '"first_col" + "second_col" + "third_col"',
        ),
        TestCase(
            "case_sensitive",
            "Amount + AMOUNT + amount + AmOuNt",
            ColumnMappings.CASE_SENSITIVE,
            '"Value" + "VALUE" + "value" + "VaLuE"',
        ),
    ]

    # Real-world scenarios
    REAL_WORLD_CASES = [
        TestCase(
            "adhoc_filter",
            "sales_amount > 1000 AND region IN ('US', 'CA') AND status = 'active'",
            {"sales_amount": "revenue", "region": "territory", "status": "deal_status"},
            "\"revenue\" > 1000 AND \"territory\" IN ('US', 'CA') AND \"deal_status\" = 'active'",
        ),
        TestCase(
            "metric_expression",
            "SUM(sales_amount) / COUNT(DISTINCT customer_id)",
            {"sales_amount": "revenue", "customer_id": "client_id"},
            'SUM("revenue") / COUNT(DISTINCT "client_id")',
        ),
        TestCase(
            "calculated_column",
            "CASE WHEN sales_amount > target_amount THEN 'exceeded' ELSE 'not_met' END",
            {"sales_amount": "revenue", "target_amount": "quota"},
            "CASE WHEN \"revenue\" > \"quota\" THEN 'exceeded' ELSE 'not_met' END",
        ),
    ]

    # Mixed quoting scenarios (MISSING)
    MIXED_QUOTING_CASES = [
        TestCase(
            "all_types_together",
            "SELECT \"quoted_col\", 'literal_col', bare_col FROM table",
            {"quoted_col": "q_col", "literal_col": "l_col", "bare_col": "b_col"},
            'SELECT "q_col", \'l_col\', "b_col" FROM table',
        ),
        TestCase(
            "complex_mixed",
            "\"amount\" + SUM('total') + avg_amount WHERE status = 'active'",
            {"amount": "revenue", "total": "sum_total", "avg_amount": "average"},
            "\"revenue\" + SUM('sum_total') + \"average\" WHERE status = 'active'",
        ),
        TestCase(
            "mixed_with_conflicts",
            "\"sales\" + 'sales_amount' + sales + sales_total",
            {
                "sales": "revenue",
                "sales_amount": "revenue_amount",
                "sales_total": "revenue_total",
            },
            '"revenue" + \'revenue_amount\' + "revenue" + "revenue_total"',
        ),
    ]

    # Advanced case sensitivity (MISSING)
    ADVANCED_CASE_SENSITIVITY_CASES = [
        TestCase(
            "case_in_quoted",
            '"Amount" + "AMOUNT" + "amount"',
            {"Amount": "Value", "AMOUNT": "VALUE", "amount": "value"},
            '"Value" + "VALUE" + "value"',
        ),
        TestCase(
            "case_variations_overlap",
            "sales + Sales + SALES + sales_amount + Sales_Amount",
            {
                "sales": "revenue",
                "Sales": "Revenue",
                "SALES": "REVENUE",
                "sales_amount": "revenue_amount",
                "Sales_Amount": "Revenue_Amount",
            },
            '"revenue" + "Revenue" + "REVENUE" + "revenue_amount" + "Revenue_Amount"',
        ),
    ]

    # Placeholder system tests (MISSING)
    PLACEHOLDER_SYSTEM_CASES = [
        TestCase(
            "placeholder_restoration",
            "\"sales_amount\" + sales_amount + 'sales_amount' + sales_amount",
            {"sales_amount": "revenue"},
            '"revenue" + "revenue" + \'revenue\' + "revenue"',
        ),
        TestCase(
            "complex_placeholder_mix",
            'SELECT "quoted_col", bare_col, \'literal_col\' WHERE description = "Contains quoted_col text"',
            {"quoted_col": "q_col", "bare_col": "b_col", "literal_col": "l_col"},
            'SELECT "q_col", "b_col", \'l_col\' WHERE description = "Contains quoted_col text"',
        ),
    ]

    # Advanced subquery tests (MISSING)
    ADVANCED_SUBQUERY_CASES = [
        TestCase(
            "exists_subquery",
            "SELECT sales_amount FROM deals d1 WHERE EXISTS (SELECT 1 FROM customers c WHERE c.region = d1.region AND c.status = 'active')",
            {
                "sales_amount": "revenue",
                "region": "territory",
                "status": "customer_status",
            },
            'SELECT "revenue" FROM deals d1 WHERE EXISTS (SELECT 1 FROM customers c WHERE c."territory" = d1."territory" AND c."customer_status" = \'active\')',
        ),
        TestCase(
            "searched_case",
            "CASE sales_category WHEN 'A' THEN sales_amount * 1.1 WHEN 'B' THEN sales_amount * 1.05 ELSE sales_amount END",
            {"sales_category": "revenue_category", "sales_amount": "revenue"},
            'CASE "revenue_category" WHEN \'A\' THEN "revenue" * 1.1 WHEN \'B\' THEN "revenue" * 1.05 ELSE "revenue" END',
        ),
    ]

    # Regex special characters (MISSING)
    REGEX_SPECIAL_CHAR_CASES = [
        TestCase(
            "regex_special",
            '"column.with.dots" + "column(with)parens" + "column[with]brackets"',
            {
                "column.with.dots": "new_column_dots",
                "column(with)parens": "new_column_parens",
                "column[with]brackets": "new_column_brackets",
            },
            '"new_column_dots" + "new_column_parens" + "new_column_brackets"',
        ),
    ]

    # Spacious column names (columns with spaces) - MISSING CRITICAL CASE
    SPACIOUS_COLUMN_CASES = [
        TestCase(
            "simple_space_double_quoted",
            'SELECT "deal name", "opportunity id" FROM deals',
            {"deal name": "deal_name", "opportunity id": "opportunity_id"},
            'SELECT "deal_name", "opportunity_id" FROM deals',
        ),
        TestCase(
            "simple_space_single_quoted",
            "SELECT 'deal name', 'opportunity id' FROM deals",
            {"deal name": "deal_name", "opportunity id": "opportunity_id"},
            "SELECT 'deal_name', 'opportunity_id' FROM deals",
        ),
        TestCase(
            "multiple_words_spaces",
            '"sales rep name" + "customer contact info"',
            {
                "sales rep name": "sales_representative_name",
                "customer contact info": "customer_contact_information",
            },
            '"sales_representative_name" + "customer_contact_information"',
        ),
        TestCase(
            "mixed_case_spaces",
            '"Deal Name" + "Sales Amount" + "Commission Rate"',
            {
                "Deal Name": "Deal_Name",
                "Sales Amount": "Sales_Amount",
                "Commission Rate": "Commission_Rate",
            },
            '"Deal_Name" + "Sales_Amount" + "Commission_Rate"',
        ),
        TestCase(
            "spaces_in_functions",
            'SUM("total sales") + AVG("commission rate") + COUNT("deal name")',
            {
                "total sales": "total_sales",
                "commission rate": "commission_rate",
                "deal name": "deal_name",
            },
            'SUM("total_sales") + AVG("commission_rate") + COUNT("deal_name")',
        ),
        TestCase(
            "spaces_in_where_clause",
            'WHERE "deal name" = \'Big Deal\' AND "sales amount" > 10000',
            {"deal name": "deal_name", "sales amount": "sales_amount"},
            'WHERE "deal_name" = \'Big Deal\' AND "sales_amount" > 10000',
        ),
        TestCase(
            "spaces_with_string_literals",
            "\"deal name\" = 'Big deal name' AND \"sales rep\" = 'John Doe'",
            {"deal name": "deal_name", "sales rep": "sales_representative"},
            "\"deal_name\" = 'Big deal name' AND \"sales_representative\" = 'John Doe'",
        ),
        TestCase(
            "spaces_in_case_statement",
            'CASE WHEN "deal value" > 10000 THEN "high value" ELSE "low value" END',
            {
                "deal value": "deal_value",
                "high value": "high_value",
                "low value": "low_value",
            },
            'CASE WHEN "deal_value" > 10000 THEN "high_value" ELSE "low_value" END',
        ),
        TestCase(
            "spaces_in_window_functions",
            'ROW_NUMBER() OVER (PARTITION BY "sales rep" ORDER BY "deal value" DESC)',
            {"sales rep": "sales_representative", "deal value": "deal_value"},
            'ROW_NUMBER() OVER (PARTITION BY "sales_representative" ORDER BY "deal_value" DESC)',
        ),
        TestCase(
            "business_scenario_real_world",
            '"Customer Name" + "Deal Stage" + "Expected Close Date" + "Annual Contract Value"',
            {
                "Customer Name": "customer_name",
                "Deal Stage": "deal_stage",
                "Expected Close Date": "expected_close_date",
                "Annual Contract Value": "annual_contract_value",
            },
            '"customer_name" + "deal_stage" + "expected_close_date" + "annual_contract_value"',
        ),
        TestCase(
            "spaces_with_overlapping_names",
            '"deal" + "deal name" + "big deal name" + "deal amount"',
            {
                "deal": "opportunity",
                "deal name": "opportunity_name",
                "big deal name": "big_opportunity_name",
                "deal amount": "opportunity_amount",
            },
            '"opportunity" + "opportunity_name" + "big_opportunity_name" + "opportunity_amount"',
        ),
        TestCase(
            "spaces_mixed_quoting_styles",
            "SELECT \"deal name\", 'opportunity type', \"sales amount\" WHERE description = 'deal name info'",
            {
                "deal name": "deal_name",
                "opportunity type": "opportunity_type",
                "sales amount": "sales_amount",
            },
            "SELECT \"deal_name\", 'opportunity_type', \"sales_amount\" WHERE description = 'deal name info'",
        ),
    ]

    # NEW: Circular Column Mappings (CRITICAL EDGE CASES)
    CIRCULAR_MAPPING_CASES = [
        TestCase(
            "simple_circular_2way", "a + b", ColumnMappings.CIRCULAR_2WAY, '"b" + "a"'
        ),
        TestCase(
            "three_way_circular",
            "a + b + c",
            ColumnMappings.CIRCULAR_3WAY,
            '"b" + "c" + "a"',
        ),
        TestCase(
            "four_way_circular",
            "a + b + c + d",
            ColumnMappings.CIRCULAR_4WAY,
            '"b" + "c" + "d" + "a"',
        ),
        TestCase(
            "five_way_circular",
            "a + b + c + d + e",
            ColumnMappings.CIRCULAR_5WAY,
            '"b" + "c" + "d" + "e" + "a"',
        ),
        TestCase(
            "circular_with_spaces",
            '"col a" + "col b"',
            {"col a": "col b", "col b": "col a"},
            '"col b" + "col a"',
        ),
        TestCase(
            "mixed_circular_quoting",
            "a + \"b\" + 'c'",
            {"a": "b", "b": "c", "c": "a"},
            '"b" + "c" + \'a\'',
        ),
        TestCase(
            "circular_in_functions",
            "SUM(a) + AVG(b) + COUNT(c)",
            ColumnMappings.CIRCULAR_3WAY,
            'SUM("b") + AVG("c") + COUNT("a")',
        ),
        TestCase(
            "circular_case_statement",
            "CASE WHEN a > b THEN c ELSE a END",
            ColumnMappings.CIRCULAR_3WAY,
            'CASE WHEN "b" > "c" THEN "a" ELSE "b" END',
        ),
        TestCase(
            "circular_window_function",
            "ROW_NUMBER() OVER (PARTITION BY a ORDER BY b DESC)",
            ColumnMappings.CIRCULAR_2WAY,
            'ROW_NUMBER() OVER (PARTITION BY "b" ORDER BY "a" DESC)',
        ),
        TestCase(
            "circular_with_literals",
            "a + b + 'literal a' + \"string b\"",
            ColumnMappings.CIRCULAR_2WAY,
            '"b" + "a" + \'literal a\' + "string b"',
        ),
    ]

    # NEW: Chain Mappings (CRITICAL)
    CHAIN_MAPPING_CASES = [
        TestCase(
            "simple_chain",
            "a + b + c",
            ColumnMappings.CHAIN_SIMPLE,
            '"new_a" + "new_b" + "new_c"',
        ),
        TestCase(
            "overlapping_chain",
            "sales + total_sales + sales_rep",
            ColumnMappings.CHAIN_OVERLAPPING,
            '"revenue" + "total_revenue" + "revenue_rep"',
        ),
        TestCase(
            "prefix_chain",
            "col + col_name + col_name_display",
            ColumnMappings.CHAIN_PREFIX,
            '"field" + "field_name" + "field_name_display"',
        ),
        TestCase(
            "chain_in_subquery",
            "SELECT * FROM (SELECT col, col_name FROM table) WHERE col_name_display = 'test'",
            ColumnMappings.CHAIN_PREFIX,
            'SELECT * FROM (SELECT "field", "field_name" FROM table) WHERE "field_name_display" = \'test\'',
        ),
        TestCase(
            "chain_mixed_quotes",
            "col + \"col_name\" + 'col_name_display'",
            ColumnMappings.CHAIN_PREFIX,
            '"field" + "field_name" + \'field_name_display\'',
        ),
    ]

    # NEW: Self-referential and Identity Mappings
    IDENTITY_MAPPING_CASES = [
        TestCase(
            "self_referential",
            "sales + commission",
            {"sales": "sales", "commission": "fee"},
            '"sales" + "fee"',
        ),
        TestCase("circular_self_ref", "a + b", {"a": "b", "b": "b"}, '"b" + "b"'),
        TestCase(
            "all_identity",
            "sales + commission + region",
            {"sales": "sales", "commission": "commission", "region": "region"},
            '"sales" + "commission" + "region"',
        ),
    ]

    # NEW: Complex UI Scenario Mappings
    UI_SCENARIO_MAPPING_CASES = [
        TestCase(
            "rename_deselect_mix",
            "sales + commission + region + status",
            {
                "sales": "revenue",
                "region": "territory",
            },  # commission and status deselected/not mapped
            '"revenue" + commission + "territory" + status',
        ),
        TestCase(
            "different_targets",
            "old_name + old_id",
            {"old_name": "new_name", "old_id": "new_id"},  # Different unique targets
            '"new_name" + "new_id"',
        ),
        TestCase(
            "overlapping_source_target",
            "source + target",
            ColumnMappings.SWAP_SIMPLE,  # Swap scenario
            '"target" + "source"',
        ),
        TestCase(
            "case_sensitive_circular",
            "Field_A + Field_B + Field_C",
            ColumnMappings.CASE_CIRCULAR,
            '"Field_B" + "Field_C" + "Field_A"',
        ),
        TestCase(
            "complex_ui_scenario",
            'SUM(old_revenue) + AVG("old_commission") + COUNT(old_region)',
            {
                "old_revenue": "new_revenue",
                "old_commission": "new_commission",
                "old_region": "new_region",
            },
            'SUM("new_revenue") + AVG("new_commission") + COUNT("new_region")',
        ),
    ]

    # NEW: Performance and Stress Test Cases
    PERFORMANCE_STRESS_CASES = [
        TestCase(
            "large_circular_performance",
            " + ".join([f"col_{i}" for i in range(10)]),
            {f"col_{i}": f"col_{(i+1)%10}" for i in range(10)},  # 10-way circular
            " + ".join([f'"col_{(i+1)%10}"' for i in range(10)]),
        ),
        TestCase(
            "many_overlapping_prefixes",
            "a + aa + aaa + aaaa + aaaaa + aaaaaa",
            {
                "a": "x",
                "aa": "xx",
                "aaa": "xxx",
                "aaaa": "xxxx",
                "aaaaa": "xxxxx",
                "aaaaaa": "xxxxxx",
            },
            '"x" + "xx" + "xxx" + "xxxx" + "xxxxx" + "xxxxxx"',
        ),
    ]

    # NEW: Real-world Complex Scenarios
    COMPLEX_REAL_WORLD_CASES = [
        TestCase(
            "crm_fields_circular",
            '"Account Name" + "Contact Name" + "Opportunity Name"',
            {
                "Account Name": "Contact Name",
                "Contact Name": "Opportunity Name",
                "Opportunity Name": "Account Name",
            },
            '"Contact Name" + "Opportunity Name" + "Account Name"',
        ),
        TestCase(
            "financial_metrics_chain",
            "SUM(revenue) + AVG(profit_margin) + COUNT(deal_count) + MAX(quota_attainment)",
            {
                "revenue": "total_revenue",
                "profit_margin": "margin_percentage",
                "deal_count": "closed_deals",
                "quota_attainment": "quota_achievement",
            },
            'SUM("total_revenue") + AVG("margin_percentage") + COUNT("closed_deals") + MAX("quota_achievement")',
        ),
        TestCase(
            "sales_pipeline_swap",
            'CASE WHEN lead_source = "referral" THEN conversion_rate * 1.2 ELSE conversion_rate END',
            {
                "lead_source": "conversion_rate",
                "conversion_rate": "lead_source",
            },  # Business logic swap
            'CASE WHEN "conversion_rate" = "referral" THEN "lead_source" * 1.2 ELSE "lead_source" END',
        ),
    ]


# Mode comparison test data
MODE_COMPARISON_CASES = [
    (
        "expression_quotes_bare",
        "sales_amount + commission",
        {"sales_amount": "revenue", "commission": "fee"},
        "expression",
        '"revenue" + "fee"',
    ),
    (
        "expression_preserves_quoted",
        '"sales_amount" + commission',
        {"sales_amount": "revenue", "commission": "fee"},
        "expression",
        '"revenue" + "fee"',
    ),
    (
        "label_keeps_bare",
        "sales_amount",
        {"sales_amount": "revenue"},
        "label",
        "revenue",
    ),
    (
        "label_processes_quoted",
        '"sales_amount"',
        {"sales_amount": "revenue"},
        "label",
        '"revenue"',
    ),
    (
        "label_complex",
        "sales_amount + commission",
        {"sales_amount": "revenue", "commission": "fee"},
        "label",
        "revenue + fee",
    ),
]

# Edge case tuples for simple scenarios
EDGE_CASE_TUPLES = [
    ("empty_expression", "", {"old": "new"}, ""),
    ("whitespace_only", "   \n\t   ", {"old": "new"}, "   \n\t   "),
    (
        "empty_mapping",
        "SELECT sales_amount FROM table",
        {},
        "SELECT sales_amount FROM table",
    ),
]


# ============================================================================
# CENTRALIZED FIXTURES
# ============================================================================


@pytest.fixture
def simple_mapping():
    """Simple column mapping for basic tests."""
    return ColumnMappings.SIMPLE


@pytest.fixture
def multi_column_mapping():
    """Multi-column mapping for complex tests."""
    return ColumnMappings.BASIC_MULTI


@pytest.fixture
def complex_mapping():
    """Complex column mapping with overlaps."""
    return ColumnMappings.OVERLAPPING


@pytest.fixture
def special_char_mapping():
    """Column mapping with special characters."""
    return ColumnMappings.SPECIAL_CHARS


# ============================================================================
# PARAMETERIZED TEST CLASSES
# ============================================================================


class TestUpdateExpressionColumnsCore:
    """Core functionality tests using centralized data."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.DOUBLE_QUOTED_CASES, ids=lambda tc: tc.name
    )
    def test_double_quoted_columns(self, test_case):
        """Test double-quoted column scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert result == test_case.expected, f"Failed for case: {test_case.name}"

    @pytest.mark.parametrize(
        "test_case", TestDataSets.SINGLE_QUOTED_CASES, ids=lambda tc: tc.name
    )
    def test_single_quoted_columns(self, test_case):
        """Test single-quoted column scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert result == test_case.expected, f"Failed for case: {test_case.name}"

    @pytest.mark.parametrize(
        "test_case", TestDataSets.BARE_COLUMN_CASES, ids=lambda tc: tc.name
    )
    def test_bare_columns(self, test_case):
        """Test bare column scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert result == test_case.expected, f"Failed for case: {test_case.name}"


class TestUpdateExpressionColumnsWordBoundaries:
    """Critical word boundary tests."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.WORD_BOUNDARY_CASES, ids=lambda tc: tc.name
    )
    def test_word_boundary_scenarios(self, test_case):
        """Test word boundary scenarios - CRITICAL for preventing incorrect replacements."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for critical word boundary case: {test_case.name}"


class TestUpdateExpressionColumnsStringLiterals:
    """String literal preservation tests - CRITICAL."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.STRING_LITERAL_CASES, ids=lambda tc: tc.name
    )
    def test_string_literal_preservation(self, test_case):
        """Test string literal preservation - CRITICAL for data integrity."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for critical string literal case: {test_case.name}"


class TestUpdateExpressionColumnsSQLFunctions:
    """SQL function and complex expression tests."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.SQL_FUNCTION_CASES, ids=lambda tc: tc.name
    )
    def test_sql_functions(self, test_case):
        """Test SQL function scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for SQL function case: {test_case.name}"

    @pytest.mark.parametrize(
        "test_case", TestDataSets.CASE_STATEMENT_CASES, ids=lambda tc: tc.name
    )
    def test_case_statements(self, test_case):
        """Test CASE statement scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for CASE statement case: {test_case.name}"


class TestUpdateExpressionColumnsModes:
    """Test expression vs label mode differences."""

    @pytest.mark.parametrize(
        "name,expression,mapping,mode,expected", MODE_COMPARISON_CASES
    )
    def test_expression_vs_label_modes(self, name, expression, mapping, mode, expected):
        """Test differences between expression and label modes."""
        result = update_expression_columns(expression, mapping, mode)
        assert result == expected, f"Failed for mode comparison case: {name}"


class TestUpdateExpressionColumnsEdgeCases:
    """Edge cases and special scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.EDGE_CASE_SCENARIOS, ids=lambda tc: tc.name
    )
    def test_edge_case_scenarios(self, test_case):
        """Test edge case scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert result == test_case.expected, f"Failed for edge case: {test_case.name}"

    @pytest.mark.parametrize("name,expression,mapping,expected", EDGE_CASE_TUPLES)
    def test_empty_and_special_cases(self, name, expression, mapping, expected):
        """Test empty expressions and special cases."""
        result = update_expression_columns(expression, mapping, "expression")
        assert result == expected, f"Failed for edge case: {name}"

    def test_none_column_mapping(self):
        """Test handling of None column mapping."""
        expression = "SELECT sales_amount FROM table"
        try:
            result = update_expression_columns(expression, None, "expression")
            assert result == expression
        except (TypeError, AttributeError):
            pass  # Acceptable behavior


class TestUpdateExpressionColumnsRealWorld:
    """Real-world scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.REAL_WORLD_CASES, ids=lambda tc: tc.name
    )
    def test_real_world_scenarios(self, test_case):
        """Test real-world chart scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for real-world case: {test_case.name}"


class TestUpdateExpressionColumnsPerformance:
    """Performance and stress tests."""

    def test_large_expression_performance(self):
        """Test performance with larger expressions."""
        import time

        columns = [f"col_{i}" for i in range(100)]
        expression = " + ".join(columns)
        mapping = {col: f"new_{col}" for col in columns}

        start_time = time.time()
        result = update_expression_columns(expression, mapping, "expression")
        end_time = time.time()

        assert (end_time - start_time) < 1.0, "Performance test failed - took too long"

        # Verify correctness
        for i in range(10):
            assert f'"new_col_{i}"' in result

    def test_large_mapping_performance(self):
        """Test performance with large unused mapping."""
        expression = "col_1 + col_2 + col_3"
        mapping = {f"unused_col_{i}": f"new_unused_col_{i}" for i in range(100)}
        mapping.update(
            {"col_1": "new_col_1", "col_2": "new_col_2", "col_3": "new_col_3"}
        )

        import time

        start_time = time.time()
        result = update_expression_columns(expression, mapping, "expression")
        end_time = time.time()

        assert (end_time - start_time) < 0.5, "Large mapping performance test failed"
        assert result == '"new_col_1" + "new_col_2" + "new_col_3"'


class TestUpdateExpressionColumnsIntegration:
    """Integration and complex scenario tests."""

    @patch("uuid.uuid4")
    def test_uuid_generation_called(self, mock_uuid):
        """Test that UUID generation is called for placeholders."""
        mock_uuid.return_value.hex = "test123"

        expression = "sales_amount + commission"
        mapping = {"sales_amount": "revenue", "commission": "fee"}
        result = update_expression_columns(expression, mapping, "expression")

        assert mock_uuid.called, "UUID generation should be called for placeholders"

    def test_complex_subquery_scenario(self):
        """Test complex subquery with multiple column replacements."""
        expression = """SELECT sales_amount FROM deals d1
                       WHERE sales_amount > (SELECT AVG(sales_amount) FROM deals d2 WHERE d2.region = d1.region)"""
        mapping = {"sales_amount": "revenue", "region": "territory"}
        result = update_expression_columns(expression, mapping, "expression")

        assert '"revenue"' in result
        assert '"territory"' in result
        assert (
            result.count('"revenue"') == 3
        ), "Should replace all three occurrences of sales_amount"
        assert (
            result.count('"territory"') == 2
        ), "Should replace both occurrences of region"

    def test_deeply_nested_expression(self):
        """Test deeply nested expression handling."""
        expression = """
            CASE
                WHEN (sales_amount > (SELECT AVG(sales_amount) FROM (
                    SELECT sales_amount FROM deals WHERE region IN (
                        SELECT region FROM regions WHERE active = 1
                    )
                ) sub))
                THEN commission_rate * 1.5
                ELSE commission_rate
            END
        """
        mapping = {
            "sales_amount": "revenue",
            "region": "territory",
            "commission_rate": "fee_rate",
        }
        result = update_expression_columns(expression, mapping, "expression")

        assert '"revenue"' in result
        assert '"territory"' in result
        assert '"fee_rate"' in result
        assert result.count('"revenue"') == 3
        assert result.count('"territory"') == 2
        assert result.count('"fee_rate"') == 2


class TestUpdateExpressionColumnsComplexRealWorld:
    """Test complex real-world scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.COMPLEX_REAL_WORLD_CASES, ids=lambda tc: tc.name
    )
    def test_complex_real_world_scenarios(self, test_case):
        """Test complex real-world scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for complex real-world case: {test_case.name}"

    def test_business_intelligence_dashboard_scenario(self):
        """Test realistic BI dashboard expression with multiple column renames."""
        expression = """
            CASE
                WHEN deal_stage IN ('Qualified', 'Proposal') THEN deal_amount * probability / 100
                WHEN deal_stage = 'Closed Won' THEN deal_amount
                ELSE 0
            END
        """
        mapping = {
            "deal_stage": "opportunity_stage",
            "deal_amount": "opportunity_value",
            "probability": "win_probability",
        }
        result = update_expression_columns(expression, mapping, "expression")

        # Verify all column replacements occurred
        assert '"opportunity_stage"' in result
        assert '"opportunity_value"' in result
        assert '"win_probability"' in result
        # Verify string literals were preserved
        assert "'Qualified'" in result
        assert "'Proposal'" in result
        assert "'Closed Won'" in result

    def test_sales_commission_calculation_circular(self):
        """Test realistic sales commission calculation with circular column dependencies."""
        expression = "base_salary + (total_sales * commission_rate) + (quota_attainment * bonus_multiplier)"
        # Simulate a UI rename where fields get shuffled around
        mapping = {
            "base_salary": "commission_rate",
            "commission_rate": "bonus_multiplier",
            "bonus_multiplier": "base_salary",
        }
        result = update_expression_columns(expression, mapping, "expression")
        expected = '"commission_rate" + (total_sales * "bonus_multiplier") + (quota_attainment * "base_salary")'
        assert (
            result == expected
        ), f"Sales commission circular scenario failed: {result}"


class TestUpdateExpressionColumnsAdvancedOperatorBoundaries:
    """Test advanced operator boundary cases for the enhanced regex pattern."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.ADVANCED_OPERATOR_BOUNDARIES, ids=lambda tc: tc.name
    )
    def test_advanced_operator_boundary_scenarios(self, test_case):
        """Test advanced operator boundary scenarios - CRITICAL for new regex pattern."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for advanced operator boundary case: {test_case.name}"


class TestUpdateExpressionColumnsWhitespaceBoundaries:
    """Test whitespace character boundary cases."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.WHITESPACE_BOUNDARY_CASES, ids=lambda tc: tc.name
    )
    def test_whitespace_boundary_scenarios(self, test_case):
        """Test whitespace boundary scenarios - important for formatting preservation."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for whitespace boundary case: {test_case.name}"

    def test_multiline_sql_formatting(self):
        """Test multiline SQL with proper formatting preservation."""
        expression = """SELECT 
            sales_amount,
            commission_rate
        FROM deals
        WHERE 
            sales_amount > 1000
            AND region = 'US'"""
        mapping = {
            "sales_amount": "revenue",
            "commission_rate": "fee_rate",
            "region": "territory",
        }
        result = update_expression_columns(expression, mapping, "expression")

        # Verify formatting is preserved
        assert "\n" in result
        assert '"revenue"' in result
        assert '"fee_rate"' in result
        assert '"territory"' in result
        # Verify structure is maintained
        assert "SELECT" in result and "FROM" in result and "WHERE" in result


class TestUpdateExpressionColumnsAdvancedEdgeCases:
    """Test advanced edge cases for enhanced regex features."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.ADVANCED_EDGE_CASES, ids=lambda tc: tc.name
    )
    def test_advanced_edge_case_scenarios(self, test_case):
        """Test advanced edge case scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for advanced edge case: {test_case.name}"


class TestUpdateExpressionColumnsAdvancedPerformance:
    """Test performance scenarios with advanced regex features."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.ADVANCED_PERFORMANCE_CASES, ids=lambda tc: tc.name
    )
    def test_advanced_performance_scenarios(self, test_case):
        """Test advanced performance scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for advanced performance case: {test_case.name}"

    def test_large_complex_query_performance(self):
        """Test performance with large complex query containing many advanced operators."""
        import time

        # Create a large complex query
        columns = [f"data_{i}" for i in range(100)]
        expressions = []
        for i in range(0, 100, 4):
            expressions.extend(
                [
                    f"data_{i}->>'field_{i}'::text",
                    f"data_{i+1}[{i+1}]::integer",
                    f"data_{i+2}~*'pattern_{i+2}'",
                    f"data_{i+3}||'_suffix_{i+3}'",
                ]
            )

        expression = " + ".join(expressions[:50])  # Limit to reasonable size
        mapping = {f"data_{i}": f"new_data_{i}" for i in range(50)}

        start_time = time.time()
        result = update_expression_columns(expression, mapping, "expression")
        end_time = time.time()

        # Should complete within reasonable time
        assert (
            end_time - start_time
        ) < 2.0, f"Large complex query took too long: {end_time - start_time}s"

        # Verify some key replacements
        assert '"new_data_0"' in result
        assert '"new_data_10"' in result
        assert '"new_data_20"' in result


class TestDataValidation:
    """Test the test data itself for consistency."""

    def test_all_test_cases_have_required_fields(self):
        """Validate all test cases have required fields."""
        all_test_sets = [
            TestDataSets.DOUBLE_QUOTED_CASES,
            TestDataSets.SINGLE_QUOTED_CASES,
            TestDataSets.BARE_COLUMN_CASES,
            TestDataSets.WORD_BOUNDARY_CASES,
            TestDataSets.STRING_LITERAL_CASES,
            TestDataSets.SQL_FUNCTION_CASES,
            TestDataSets.CASE_STATEMENT_CASES,
            TestDataSets.EDGE_CASE_SCENARIOS,
            TestDataSets.REAL_WORLD_CASES,
            TestDataSets.MIXED_QUOTING_CASES,
            TestDataSets.ADVANCED_CASE_SENSITIVITY_CASES,
            TestDataSets.PLACEHOLDER_SYSTEM_CASES,
            TestDataSets.ADVANCED_SUBQUERY_CASES,
            TestDataSets.REGEX_SPECIAL_CHAR_CASES,
            TestDataSets.SPACIOUS_COLUMN_CASES,
            TestDataSets.CIRCULAR_MAPPING_CASES,
            TestDataSets.CHAIN_MAPPING_CASES,
            TestDataSets.IDENTITY_MAPPING_CASES,
            TestDataSets.UI_SCENARIO_MAPPING_CASES,
            TestDataSets.PERFORMANCE_STRESS_CASES,
            TestDataSets.COMPLEX_REAL_WORLD_CASES,
            # NEW: Advanced test sets for enhanced regex pattern
            TestDataSets.ADVANCED_OPERATOR_BOUNDARIES,
            TestDataSets.WHITESPACE_BOUNDARY_CASES,
            TestDataSets.ADVANCED_SQL_SCENARIOS,
            TestDataSets.ADVANCED_EDGE_CASES,
            TestDataSets.ADVANCED_PERFORMANCE_CASES,
        ]

        for test_set in all_test_sets:
            for case in test_set:
                assert case.name, "Test case must have a name"
                assert case.expression, "Test case must have an expression"
                assert case.mapping, "Test case must have a mapping"
                assert case.expected is not None, "Test case must have expected result"

    def test_no_duplicate_test_names_in_sets(self):
        """Ensure no duplicate test names within each set."""
        test_sets = {
            "DOUBLE_QUOTED": TestDataSets.DOUBLE_QUOTED_CASES,
            "SINGLE_QUOTED": TestDataSets.SINGLE_QUOTED_CASES,
            "BARE_COLUMN": TestDataSets.BARE_COLUMN_CASES,
            "WORD_BOUNDARY": TestDataSets.WORD_BOUNDARY_CASES,
            "STRING_LITERAL": TestDataSets.STRING_LITERAL_CASES,
            "SQL_FUNCTION": TestDataSets.SQL_FUNCTION_CASES,
            "CASE_STATEMENT": TestDataSets.CASE_STATEMENT_CASES,
            "EDGE_CASE": TestDataSets.EDGE_CASE_SCENARIOS,
            "REAL_WORLD": TestDataSets.REAL_WORLD_CASES,
            "MIXED_QUOTING": TestDataSets.MIXED_QUOTING_CASES,
            "ADVANCED_CASE_SENSITIVITY": TestDataSets.ADVANCED_CASE_SENSITIVITY_CASES,
            "PLACEHOLDER_SYSTEM": TestDataSets.PLACEHOLDER_SYSTEM_CASES,
            "ADVANCED_SUBQUERY": TestDataSets.ADVANCED_SUBQUERY_CASES,
            "REGEX_SPECIAL_CHAR": TestDataSets.REGEX_SPECIAL_CHAR_CASES,
            "SPACIOUS_COLUMN": TestDataSets.SPACIOUS_COLUMN_CASES,
            "CIRCULAR_MAPPING": TestDataSets.CIRCULAR_MAPPING_CASES,
            "CHAIN_MAPPING": TestDataSets.CHAIN_MAPPING_CASES,
            "IDENTITY_MAPPING": TestDataSets.IDENTITY_MAPPING_CASES,
            "UI_SCENARIO_MAPPING": TestDataSets.UI_SCENARIO_MAPPING_CASES,
            "PERFORMANCE_STRESS": TestDataSets.PERFORMANCE_STRESS_CASES,
            "COMPLEX_REAL_WORLD": TestDataSets.COMPLEX_REAL_WORLD_CASES,
            # NEW: Advanced test sets for enhanced regex pattern
            "ADVANCED_OPERATOR_BOUNDARIES": TestDataSets.ADVANCED_OPERATOR_BOUNDARIES,
            "WHITESPACE_BOUNDARY_CASES": TestDataSets.WHITESPACE_BOUNDARY_CASES,
            "ADVANCED_SQL_SCENARIOS": TestDataSets.ADVANCED_SQL_SCENARIOS,
            "ADVANCED_EDGE_CASES": TestDataSets.ADVANCED_EDGE_CASES,
            "ADVANCED_PERFORMANCE_CASES": TestDataSets.ADVANCED_PERFORMANCE_CASES,
        }

        for set_name, test_set in test_sets.items():
            names = [case.name for case in test_set]
            assert len(names) == len(
                set(names)
            ), f"Duplicate test case names in {set_name}"


class TestUpdateExpressionColumnsMixedQuoting:
    """Test mixed quoting scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.MIXED_QUOTING_CASES, ids=lambda tc: tc.name
    )
    def test_mixed_quoting_scenarios(self, test_case):
        """Test mixed quoting scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for mixed quoting case: {test_case.name}"


class TestUpdateExpressionColumnsAdvancedCaseSensitivity:
    """Test advanced case sensitivity scenarios."""

    @pytest.mark.parametrize(
        "test_case",
        TestDataSets.ADVANCED_CASE_SENSITIVITY_CASES,
        ids=lambda tc: tc.name,
    )
    def test_advanced_case_sensitivity(self, test_case):
        """Test advanced case sensitivity scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for case sensitivity case: {test_case.name}"


class TestUpdateExpressionColumnsPlaceholderSystem:
    """Test placeholder system behavior."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.PLACEHOLDER_SYSTEM_CASES, ids=lambda tc: tc.name
    )
    def test_placeholder_system(self, test_case):
        """Test placeholder system scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for placeholder system case: {test_case.name}"

    def test_placeholder_uniqueness(self):
        """Test that UUID placeholders don't collide."""
        expression = "sales_amount + commission + region"
        mapping = {
            "sales_amount": "revenue",
            "commission": "fee",
            "region": "territory",
        }

        # Run multiple times to ensure consistent results
        results = []
        for _ in range(10):
            result = update_expression_columns(expression, mapping, "expression")
            results.append(result)

        # All results should be identical
        expected = '"revenue" + "fee" + "territory"'
        assert all(r == expected for r in results)


class TestUpdateExpressionColumnsAdvancedSubqueries:
    """Test advanced subquery scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.ADVANCED_SUBQUERY_CASES, ids=lambda tc: tc.name
    )
    def test_advanced_subquery_scenarios(self, test_case):
        """Test advanced subquery scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for advanced subquery case: {test_case.name}"


class TestUpdateExpressionColumnsRegexSpecialChars:
    """Test regex special character handling."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.REGEX_SPECIAL_CHAR_CASES, ids=lambda tc: tc.name
    )
    def test_regex_special_char_scenarios(self, test_case):
        """Test regex special character scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for regex special char case: {test_case.name}"


class TestUpdateExpressionColumnsSpacious:
    """Test spacious column names (columns with spaces) - CRITICAL for business scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.SPACIOUS_COLUMN_CASES, ids=lambda tc: tc.name
    )
    def test_spacious_column_scenarios(self, test_case):
        """Test spacious column name scenarios - CRITICAL for business databases."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for spacious column case: {test_case.name}"


class TestUpdateExpressionColumnsCircularMappings:
    """Test circular column mappings - CRITICAL edge cases for UI scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.CIRCULAR_MAPPING_CASES, ids=lambda tc: tc.name
    )
    def test_circular_mapping_scenarios(self, test_case):
        """Test circular mapping scenarios - CRITICAL for preventing infinite loops and data corruption."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for circular mapping case: {test_case.name}"

    def test_circular_mapping_consistency(self):
        """Test that circular mappings produce consistent results across multiple runs."""
        expression = "a + b + c"
        mapping = {"a": "b", "b": "c", "c": "a"}
        expected = '"b" + "c" + "a"'

        # Run multiple times to ensure consistency
        results = []
        for _ in range(5):
            result = update_expression_columns(expression, mapping, "expression")
            results.append(result)

        # All results should be identical
        assert all(
            r == expected for r in results
        ), f"Circular mapping results inconsistent: {results}"

    def test_large_circular_chain_stability(self):
        """Test stability with large circular chains."""
        # Create a 20-way circular mapping: a->b, b->c, ..., t->a
        columns = [chr(ord("a") + i) for i in range(20)]
        expression = " + ".join(columns)
        mapping = {col: chr(ord("a") + (i + 1) % 20) for i, col in enumerate(columns)}

        result = update_expression_columns(expression, mapping, "expression")

        # Verify each column was correctly replaced
        for i, col in enumerate(columns):
            next_col = chr(ord("a") + (i + 1) % 20)
            assert (
                f'"{next_col}"' in result
            ), f"Column {col} -> {next_col} replacement failed"


class TestUpdateExpressionColumnsChainMappings:
    """Test chain column mappings - scenarios where columns map in a sequence."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.CHAIN_MAPPING_CASES, ids=lambda tc: tc.name
    )
    def test_chain_mapping_scenarios(self, test_case):
        """Test chain mapping scenarios - important for complex renaming operations."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for chain mapping case: {test_case.name}"

    def test_overlapping_prefix_chain_order(self):
        """Test that overlapping prefix chains are handled in correct order (longest first)."""
        expression = "col + col_name + col_name_display + col_name_display_full"
        mapping = {
            "col": "field",
            "col_name": "field_name",
            "col_name_display": "field_name_display",
            "col_name_display_full": "field_name_display_full",
        }
        result = update_expression_columns(expression, mapping, "expression")
        expected = (
            '"field" + "field_name" + "field_name_display" + "field_name_display_full"'
        )
        assert result == expected, f"Overlapping prefix chain failed: {result}"


class TestUpdateExpressionColumnsIdentityMappings:
    """Test identity and self-referential mappings."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.IDENTITY_MAPPING_CASES, ids=lambda tc: tc.name
    )
    def test_identity_mapping_scenarios(self, test_case):
        """Test identity mapping scenarios - edge cases where columns map to themselves or similar."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for identity mapping case: {test_case.name}"

    def test_mixed_identity_and_change(self):
        """Test mixed scenarios with both identity mappings and actual changes."""
        expression = "keep_same + change_this + keep_same_too"
        mapping = {
            "keep_same": "keep_same",
            "change_this": "changed",
            "keep_same_too": "keep_same_too",
        }
        result = update_expression_columns(expression, mapping, "expression")
        expected = '"keep_same" + "changed" + "keep_same_too"'
        assert result == expected, f"Mixed identity mapping failed: {result}"


class TestUpdateExpressionColumnsUIScenarios:
    """Test UI-driven scenarios - select/deselect/rename operations."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.UI_SCENARIO_MAPPING_CASES, ids=lambda tc: tc.name
    )
    def test_ui_scenario_mappings(self, test_case):
        """Test UI scenario mappings - realistic user interface operations."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for UI scenario case: {test_case.name}"

    def test_partial_column_selection_scenario(self):
        """Test scenario where user selects only some columns from a larger set."""
        # Original expression has 5 columns, user only selects 3
        expression = "col1 + col2 + col3 + col4 + col5"
        mapping = {
            "col1": "selected1",
            "col3": "selected3",
            "col5": "selected5",
        }  # col2, col4 not selected
        result = update_expression_columns(expression, mapping, "expression")
        expected = '"selected1" + col2 + "selected3" + col4 + "selected5"'
        assert result == expected, f"Partial selection scenario failed: {result}"

    def test_column_swap_ui_scenario(self):
        """Test UI scenario where user swaps two column names."""
        expression = "SUM(source_column) + AVG(target_column) + COUNT(*)"
        mapping = {"source_column": "target_column", "target_column": "source_column"}
        result = update_expression_columns(expression, mapping, "expression")
        expected = 'SUM("target_column") + AVG("source_column") + COUNT(*)'
        assert result == expected, f"Column swap UI scenario failed: {result}"


class TestUpdateExpressionColumnsPerformanceStress:
    """Test performance and stress scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.PERFORMANCE_STRESS_CASES, ids=lambda tc: tc.name
    )
    def test_performance_stress_scenarios(self, test_case):
        """Test performance stress scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for performance stress case: {test_case.name}"

    def test_extremely_large_circular_mapping(self):
        """Test performance with very large circular mapping (100 columns)."""
        import time

        # Create 100-way circular mapping
        columns = [f"col_{i}" for i in range(100)]
        expression = " + ".join(columns)
        mapping = {col: f"col_{(i + 1) % 100}" for i, col in enumerate(columns)}

        start_time = time.time()
        result = update_expression_columns(expression, mapping, "expression")
        end_time = time.time()

        # Should complete within reasonable time (5 seconds)
        assert (
            end_time - start_time
        ) < 5.0, f"Large circular mapping took too long: {end_time - start_time}s"

        # Verify correctness by checking a few samples
        assert '"col_1"' in result and '"col_50"' in result and '"col_0"' in result

    def test_deep_nesting_performance(self):
        """Test performance with deeply nested expressions."""
        # Create deeply nested CASE statements with circular mappings
        expression = "CASE WHEN a > 0 THEN (CASE WHEN b > 0 THEN (CASE WHEN c > 0 THEN a ELSE b END) ELSE c END) ELSE a END"
        mapping = {"a": "b", "b": "c", "c": "a"}

        import time

        start_time = time.time()
        result = update_expression_columns(expression, mapping, "expression")
        end_time = time.time()

        assert (
            end_time - start_time
        ) < 1.0, f"Deep nesting took too long: {end_time - start_time}s"
        expected = 'CASE WHEN "b" > 0 THEN (CASE WHEN "c" > 0 THEN (CASE WHEN "a" > 0 THEN "b" ELSE "c" END) ELSE "a" END) ELSE "b" END'
        assert result == expected


class TestUpdateExpressionColumnsComplexRealWorld:
    """Test complex real-world scenarios."""

    @pytest.mark.parametrize(
        "test_case", TestDataSets.COMPLEX_REAL_WORLD_CASES, ids=lambda tc: tc.name
    )
    def test_complex_real_world_scenarios(self, test_case):
        """Test complex real-world scenarios."""
        result = update_expression_columns(
            test_case.expression, test_case.mapping, test_case.mode
        )
        assert (
            result == test_case.expected
        ), f"Failed for complex real-world case: {test_case.name}"

    def test_business_intelligence_dashboard_scenario(self):
        """Test realistic BI dashboard expression with multiple column renames."""
        expression = """
            CASE
                WHEN deal_stage IN ('Qualified', 'Proposal') THEN deal_amount * probability / 100
                WHEN deal_stage = 'Closed Won' THEN deal_amount
                ELSE 0
            END
        """
        mapping = {
            "deal_stage": "opportunity_stage",
            "deal_amount": "opportunity_value",
            "probability": "win_probability",
        }
        result = update_expression_columns(expression, mapping, "expression")

        # Verify all column replacements occurred
        assert '"opportunity_stage"' in result
        assert '"opportunity_value"' in result
        assert '"win_probability"' in result
        # Verify string literals were preserved
        assert "'Qualified'" in result
        assert "'Proposal'" in result
        assert "'Closed Won'" in result

    def test_sales_commission_calculation_circular(self):
        """Test realistic sales commission calculation with circular column dependencies."""
        expression = "base_salary + (total_sales * commission_rate) + (quota_attainment * bonus_multiplier)"
        # Simulate a UI rename where fields get shuffled around
        mapping = {
            "base_salary": "commission_rate",
            "commission_rate": "bonus_multiplier",
            "bonus_multiplier": "base_salary",
        }
        result = update_expression_columns(expression, mapping, "expression")
        expected = '"commission_rate" + (total_sales * "bonus_multiplier") + (quota_attainment * "base_salary")'
        assert (
            result == expected
        ), f"Sales commission circular scenario failed: {result}"


class TestUpdateExpressionColumnsValidationEdgeCases:
    """Test validation and error edge cases."""

    def test_empty_column_mapping_performance(self):
        """Test performance when column mapping is empty."""
        expression = (
            "SELECT " + ", ".join([f"col_{i}" for i in range(100)]) + " FROM table"
        )
        mapping = {}  # Empty mapping

        import time

        start_time = time.time()
        result = update_expression_columns(expression, mapping, "expression")
        end_time = time.time()

        # Should be very fast with empty mapping
        assert (
            end_time - start_time
        ) < 0.1, f"Empty mapping took too long: {end_time - start_time}s"
        assert result == expression, "Expression should be unchanged with empty mapping"

    def test_malformed_expression_robustness(self):
        """Test robustness with malformed SQL expressions."""
        malformed_expressions = [
            'SELECT "unclosed_quote FROM table',  # Unclosed quote
            "SELECT column FROM WHERE",  # Invalid SQL
            "SELECT ' + unclosed_single_quote",  # Unclosed single quote
            "",  # Empty expression
            "   \n\t   ",  # Whitespace only
        ]

        mapping = {"column": "new_column"}

        for expr in malformed_expressions:
            # Should not crash, should return something reasonable
            try:
                result = update_expression_columns(expr, mapping, "expression")
                # Basic sanity check - result should be a string
                assert isinstance(
                    result, str
                ), f"Result should be string for expression: {expr}"
            except Exception as e:
                # If it raises an exception, it should be a reasonable one
                assert isinstance(
                    e, (ValueError, TypeError)
                ), f"Unexpected exception type for {expr}: {type(e)}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
