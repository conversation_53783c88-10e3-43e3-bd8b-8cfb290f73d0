"""
File consists of service methods to aid in providing any source metadata related requests
"""

import re
import uuid
from enum import Enum
from typing import Dict, Match

from commission_engine.accessors.databook_accessor import (
    DatabookAccessor,
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from spm.accessors.email_template_accessor import EmailTemplateDetailsAccessor
from superset.accessors.analytics_model_accessor import DatasheetDatasetMapAccessor


class AnalyticsEmailTemplateCode(Enum):
    SEND_ANALYTICS_REPORTS = "SEND_ANALYTICS_REPORTS"


def get_ds_names_for_client(client_id):
    """
    This method is used to get all the active datasheets for a client
    """
    return DatasheetAccessor(client_id).client_latest_kd_aware_with_hidden()


def get_required_databooks_for_client(client_id, db_ids):
    """
    This method is used to get all the active databooks for a client
    """

    return DatabookAccessor(client_id).get_databook_by_db_ids(db_ids)


def get_db_data_by_name(client_id, databook_name):
    """
    This method is used to get the databook id from the databook name retrieved from the table name of superset
    """

    return DatabookAccessor(client_id).get_databook_id_by_name(databook_name)


def get_ds_data_by_name(client_id, databook_id, datasheet_name):
    """
    This method is used to get the datasheet id from the datasheet name retrieved from the table name of superset
    """

    return DatasheetAccessor(client_id).get_datasheet_id_by_name(
        databook_id, datasheet_name
    )


def get_req_datasheet_variables(client_id, datasheet_id):
    """
    This method is used to get the required variables for a datasheet

    Here we will be getting the system_name, display_name and data_type of the variables as a list of dictionary for the target datasheet_id
    """

    return DatasheetVariableAccessor(client_id).get_variables_for_ds_ids(
        datasheet_ids=datasheet_id,
        as_dicts=True,
        columns=["system_name", "display_name", "data_type"],
    )


def get_superset_datasets_for_databook(client_id, databook_id):
    """
    This method is used to get the superset datasets for a databook

    It helps in finding out whether the databook is used in superset or not, if it is used then we get the dataset object
    else we get empty list
    """

    return DatasheetDatasetMapAccessor(client_id).get_datasets_by_databook_id(
        databook_id
    )


def get_superset_dataset_for_datasheet(client_id, databook_id, datasheet_id):
    """
    This method is used to get the superset dataset for a datasheet.

    It helps in finding out whether the datasheet is used in superset or not, if it is used then we get the dataset object
    else we get empty list
    """

    return DatasheetDatasetMapAccessor(client_id).get_dataset_by_datasheet_id(
        databook_id, datasheet_id
    )


def get_superset_dataset_by_tablename(client_id, tablename):
    """
    This method is used to get the superset dataset for a table name

    If the table name is not found in the superset dataset map, then it returns empty list
    else we get the Dataset object entry as a list of dictionary. The length of the list is always 1 since the table_name is unique
    """

    return DatasheetDatasetMapAccessor(client_id).get_dataset_by_table_name(tablename)


def get_email_template_id_for_analytics_reports():
    """
    Email template for analytics alerts and reports will be set under configure - > Email templates with event code as 'SEND_ANALYTICS_REPORTS'
    This template is not client specific
    """
    return EmailTemplateDetailsAccessor().get_template_id_for_event_code(
        AnalyticsEmailTemplateCode.SEND_ANALYTICS_REPORTS.value
    )


def get_dataset_id_for_datasheet(*, client_id, datasheet_id) -> int | None:
    """
    This method is used to get the superset dataset for a datasheet
    """
    datasheet_dataset_map = (
        DatasheetDatasetMapAccessor(client_id)
        .client_latest_kd_aware()
        .filter(datasheet_id=datasheet_id)
        .first()
    )
    return datasheet_dataset_map.superset_dataset_id if datasheet_dataset_map else None


def update_expression_columns(
    expression: str, column_name_mapping: Dict[str, str], key: str = "expression"
) -> str:
    """
    Replaces column names in a SQL expression or label using placeholders to prevent unintended modifications.

    Steps:

    1. Replace double-quoted column names with placeholders.

    2. Replace unused double-quoted content to protect it.

    3. Replace single-quoted column names with placeholders.

    4. Replace unused string literals to preserve integrity.

    5. Replace bare column names with placeholders, ensuring proper formatting.

    6. Restore all placeholders with their updated column names.

    Args:
        expression: The SQL expression or label to update
        column_name_mapping: Dictionary mapping old column names to new column names
        key: Type of expression being processed ('expression' or 'label')

    Returns:
        The updated expression with column names replaced
    """
    if not expression.strip():
        return expression

    expr = expression

    sorted_column_mapping = dict(
        sorted(column_name_mapping.items(), key=lambda x: x[0], reverse=True)
    )

    quoted_column_placeholders_map: Dict[str, str] = {}
    unused_quoted_content_placeholders_map: Dict[str, str] = {}
    literal_column_placeholder_map: Dict[str, str] = {}
    unused_literal_placeholders_map: Dict[str, str] = {}
    bare_column_placeholders_map: Dict[str, str] = {}

    # Step 1: Replace double-quoted column names with placeholders
    for old_col, new_col in sorted_column_mapping.items():
        placeholder = f"__QUOTED_COL_{uuid.uuid4().hex}__"
        quoted_column_placeholders_map[placeholder] = new_col
        pattern = rf'"{re.escape(old_col)}"'
        expr = re.sub(pattern, f'"{placeholder}"', expr)

    # Step 2: Replace unused double-quoted content with placeholders
    def unused_quoted_content_replacer(match: Match) -> str:
        content = match.group(0)
        # Skip if this already contains one of our placeholders
        if any(
            placeholder in content for placeholder in quoted_column_placeholders_map
        ):
            return content
        placeholder = f"__UNUSED_QUOTED_CONTENT_{uuid.uuid4().hex}__"
        unused_quoted_content_placeholders_map[placeholder] = content
        return placeholder

    # Make sure we don't replace already processed content
    expr = re.sub(r'"[^"]*"', unused_quoted_content_replacer, expr)

    # Step 3: Replace single-quoted column names with placeholders
    for old_col, new_col in sorted_column_mapping.items():
        placeholder = f"__LITERAL_COL_{uuid.uuid4().hex}__"
        literal_column_placeholder_map[placeholder] = new_col
        pattern = rf"'{re.escape(old_col)}'"
        expr = re.sub(pattern, f"'{placeholder}'", expr)

    # Step 4: Replace unused string literals with placeholders
    def literal_replacer(match: Match) -> str:
        literal = match.group(0)
        # Skip if this already contains one of our placeholders
        if any(
            placeholder in literal for placeholder in literal_column_placeholder_map
        ):
            return literal
        placeholder = f"__UNUSED_LITERAL_CONTENT_{uuid.uuid4().hex}__"
        unused_literal_placeholders_map[placeholder] = literal
        return placeholder

    expr = re.sub(r"'[^']*'", literal_replacer, expr)

    # Step 5: Replace bare column names (not at the start of the expression)
    for old_col, new_col in sorted_column_mapping.items():
        placeholder = f"__BARE_COL_{uuid.uuid4().hex}__"
        bare_column_placeholders_map[placeholder] = new_col

        escaped_col = re.escape(old_col)

        # Match bare column names that are not function calls
        # Uses word boundaries to avoid partial matches
        pattern = rf"\b{escaped_col}\b(?!\s*\()"
        replacement = f'"{placeholder}"' if key == "expression" else placeholder
        expr = re.sub(pattern, replacement, expr)

    # Replace bare column names only at the beginning of the expression
    for old_col, new_col in sorted_column_mapping.items():
        placeholder = f"__BARE_COL_START_{uuid.uuid4().hex}__"
        bare_column_placeholders_map[placeholder] = new_col

        escaped_col = re.escape(old_col)

        # Match column names only at the start of the expression (not function calls)
        pattern = rf"^\b{escaped_col}\b(?!\s*\()"
        replacement = f'"{placeholder}"' if key == "expression" else placeholder
        expr = re.sub(pattern, replacement, expr)

    # Step 7-11: Restore all placeholders in the correct order

    for placeholder, new_col in bare_column_placeholders_map.items():
        expr = expr.replace(placeholder, new_col)

    for placeholder, content in unused_literal_placeholders_map.items():
        expr = expr.replace(placeholder, content)

    for placeholder, new_col in literal_column_placeholder_map.items():
        expr = expr.replace(placeholder, new_col)

    for placeholder, content in unused_quoted_content_placeholders_map.items():
        expr = expr.replace(placeholder, content)

    for placeholder, new_col in quoted_column_placeholders_map.items():
        expr = expr.replace(placeholder, new_col)

    return expr
