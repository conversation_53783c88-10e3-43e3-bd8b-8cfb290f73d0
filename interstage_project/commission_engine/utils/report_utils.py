from datetime import datetime
from enum import Enum

from commission_engine.accessors.client_accessor import (
    can_run_auto_enrich_report,
    get_client_features,
)
from commission_engine.accessors.ever_object_accessor import (
    EverObjectAccessor,
    EverObjectVariableAccessor,
)
from commission_engine.accessors.report_enrichment_accessor import (
    CommissionReportEnrichmentAccessor,
    get_auto_enrich_variables,
)
from commission_engine.utils.general_data import ModuleType, ReportObject
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.accessors.custom_field_accessor import CustomFieldsAccessor
from spm.accessors.manual_adjustment_accessor import DrawAdjustmentAccessor


class RECORD_TYPE(Enum):
    COMMISSION = "Commission"
    COMMISSION_ADJUSTMENT = "Commission Adjustment"
    DRAW_ADJUSTMENT = "Draw Adjustment"
    SETTLEMENT = "Settlement"


class DataOrigin(Enum):
    COMMISSION_OBJECT = "commission_object"
    SYSTEM_OBJECT = "system_object"
    CUSTOM_OBJECT = "custom_object"
    INTER_OBJECT = "inter_object"
    FORECAST_OBJECT = "forecast_object"
    INTER_FORECAST_OBJECT = "inter_forecast_object"
    QUOTE_OBJECT = "quote_object"


def get_report_object_ids(data_origin=None):
    report_object_ids = EverObjectAccessor().get_ever_object_ids(data_origin)
    return list(report_object_ids)


def get_other_module_reports(client_id: int) -> list[str]:
    client_features = get_client_features(client_id) or {}
    client_modules = client_features.get("modules", [])
    excluded_reports = []
    for module in ModuleType.values():
        if module not in client_modules:
            excluded_reports.extend(ReportObject.values(module))
    return excluded_reports


def get_report_variables(report_object_id, client_id=None):

    predefined_variables = EverObjectVariableAccessor().get_ever_object_variable(
        object_id=report_object_id
    )
    # appends active custom fields if it is a user report

    if report_object_id == "user" and client_id is not None:
        custom_fields = CustomFieldsAccessor(
            client_id=client_id
        ).fetch_all_custom_fields(
            projection=[
                "system_name",
                "display_name",
                "data_type_id",
                "field_type",
                "options",
            ]
        )
        for index, cf in enumerate(custom_fields):
            if cf.get("field_type") == "Dropdown":
                custom_fields[index]["data_type_id"] = 4
        return predefined_variables + (custom_fields or [])

    if report_object_id in ["commission", "inter_commission"] and client_id is not None:
        if can_run_auto_enrich_report(client_id):
            cre_variables = get_auto_enrich_variables(client_id)
        else:
            cre_variables = list(
                CommissionReportEnrichmentAccessor(client_id).get_all_records_values(
                    columns=[
                        "system_name",
                        "report_system_name",
                        "display_name",
                        "data_type_id",
                        "criteria_id",
                    ],
                    report_type=report_object_id,
                )
            )

            criteria_ids = set([variable["criteria_id"] for variable in cre_variables])

            from spm.accessors.commission_plan_accessor import PlanCriteriaAccessor

            criteria_id_name_map = dict(
                PlanCriteriaAccessor(client_id).get_names_by_ids(criteria_ids)
            )

            for variable in cre_variables:
                criteria_id = variable.pop("criteria_id")
                display_name = variable["display_name"]
                variable["display_name"] = (
                    criteria_id_name_map[criteria_id] + "::" + display_name
                )

        for variable in cre_variables:
            variable["system_name"] = variable["report_system_name"]
            variable["is_enriched_variable"] = True
            del variable["report_system_name"]

        return predefined_variables + cre_variables
    return predefined_variables


def build_row_keys(report_object_id, data):
    row_keys = []
    pk = EverObjectAccessor().get_primary_key(object_id=report_object_id)
    for rec in data:
        composite_key = []
        for primary_key in pk:
            key = rec.get(primary_key)
            if (
                report_object_id
                in [
                    "quota_attainment",
                    "inter_quota_attainment",
                    "forecast_quota_attainment",
                    "inter_forecast_quota_attainment",
                ]
                and primary_key == "individual_or_team_quota"
            ):
                key = "team" if rec.get("is_team") else "individual"
            composite_key.append(str(key))
        row_key = "##::##".join(composite_key).lower()
        row_keys.append(row_key)

    return row_keys


def build_record_key(report_object_id, record, pk):
    composite_key = []
    if pk is None:
        pk = EverObjectAccessor().get_primary_key(object_id=report_object_id)
    for primary_key in pk:
        key = record.get(primary_key)
        if (
            report_object_id in ["quota_attainment", "inter_quota_attainment"]
            and primary_key == "individual_or_team_quota"
        ):
            key = "team" if record.get("is_team") else "individual"
        composite_key.append(str(key))
    row_key = "##::##".join(composite_key).lower()
    return row_key


def get_primary_keys(report_object_id):
    pk = EverObjectAccessor().get_primary_key(object_id=report_object_id)
    return pk


def get_table_for_object_id(report_object_id):
    table_name = EverObjectAccessor().get_table_name(object_id=report_object_id)[0]
    return table_name


def get_draws_for_period_and_payee_list(
    client_id: int,
    fiscal_start_month: int,
    ped_email_filter_map: dict,
    period: datetime = None,
    payee_list: list[str] = None,
):
    # Dynamically importing due to a circular dependency
    from commission_engine.utils.date_utils import (
        get_fiscal_year,
        get_period_draws,
        get_period_start_and_end_date,
    )

    if ped_email_filter_map is None:
        emp_payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            period, payee_list, as_dicts=False
        )
        # Create a mapping of period start and end dates to employee email IDs
        ped_email_filter_map = {}
        for payroll in emp_payroll:
            email = payroll.employee_email_id
            # Get the start and end dates of the period based on payroll details
            psd_ped = get_period_start_and_end_date(
                period,
                fiscal_start_month,
                payroll.payout_frequency.lower(),
                client_id=client_id,
            )

            # Create a key based on start and end dates to group employees in the same period
            psd_ped_key = (psd_ped["start_date"], psd_ped["end_date"])
            if psd_ped_key not in ped_email_filter_map:
                ped_email_filter_map[psd_ped_key] = [email]
            else:
                ped_email_filter_map[psd_ped_key].append(email)

    # Create a mapping of period label and fiscal year to payee list
    period_fy_payee_map = {}
    psd_ped_list = list(ped_email_filter_map.keys())
    for psd_ped in psd_ped_list:
        period_label = get_period_draws(psd_ped[0], psd_ped[1], fiscal_start_month)
        fiscal_year = get_fiscal_year(fiscal_start_month, psd_ped[0])
        period_fy_payee_map[(period_label, fiscal_year)] = ped_email_filter_map[psd_ped]

    # Fetch draws adjustments for each period and fiscal year
    # combination and the corresponding payee list
    draws_query_set = []
    for period_fy, payee_list in period_fy_payee_map.items():
        draws_query_set.extend(
            DrawAdjustmentAccessor(
                client_id=client_id
            ).get_draw_adjustment_for_payee_list(
                payee_list, int(period_fy[1]), period_fy[0]
            )
        )

    return draws_query_set


def get_report_object_data_table_name(client_id: int | str, object_id: str) -> str:
    """
    Returns the report object data table name based on client_id and object_id.
    """
    table_name = f"report_object_data_{client_id}_{object_id}"
    return table_name


def resolve_report_object_table_name(
    client_id: int | str, object_id: str, table_name: str
) -> str:
    """
    Returns the object table name based on client_id, object_id and table_name.
    """
    if table_name == "report_object_data":
        return get_report_object_data_table_name(client_id, object_id)
    elif table_name == "system_object_data":
        return get_system_object_data_table_name(client_id, object_id)
    elif table_name == "forecast_object_data":
        return get_forecast_object_data_table_name(client_id, object_id)
    elif table_name == "inter_object_data":
        return get_inter_object_data_table_name(client_id, object_id)
    elif table_name == "inter_forecast_object_data":
        return get_inter_forecast_object_data_table_name(client_id, object_id)
    else:
        raise ValueError(f"Invalid table name: {table_name}")


def get_system_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the system object table name based on client_id and object_id.
    """
    table_name = f"system_object_data_{client_id}_{object_id}"
    return table_name


def get_forecast_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the forecast object table name based on client_id and object_id.
    """
    table_name = f"forecast_object_data_{client_id}_{object_id}"
    return table_name


def get_inter_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the inter object table name based on client_id and object_id.
    """
    table_name = f"inter_object_data_{client_id}_{object_id}"
    return table_name


def get_inter_forecast_object_data_table_name(client_id, object_id) -> str:
    """
    Returns the inter forecast object table name based on client_id and object_id.
    """
    table_name = f"inter_forecast_object_data_{client_id}_{object_id}"
    return table_name
