# General Data Module
#
# This module serves as a central repository of constants, enumerations, and configuration data
# used throughout the sales commission automation platform. It defines the core data structures
# that support the application's functionality across multiple components.
#
# The file is organized into several key sections:
# 1. Authentication and Integration Configuration - Settings for external service connections
# 2. Status and State Enumerations - Standard status codes for various processes
# 3. Notification Configuration - Templates and settings for user notifications
# 4. Commission and Payout Structures - Definitions for commission calculations and payouts
# 5. Data Synchronization - Configuration for data syncing between systems
# 6. Analytical and Reporting Constants - Definitions for reporting and analytics
#
# This file is central to maintaining consistency across the application, ensuring
# that all components use the same standardized values for status codes, message formats,
# and system configuration.

import os
from enum import Enum, auto
from typing import List

from slack_everstage.utils.slack_utils import get_site_url

# OAuth scope list required for Microsoft Teams integration authentication
SCOPE_LIST = "offline_access%20User.ReadWrite.All%20TeamsAppInstallation.ReadWriteForUser%20Chat.ReadWrite%20AppCatalog.ReadWrite.All"

# Maps for service authentication configuration
# Maps service names to their respective OAuth token endpoints
service_access_token_url_map = {
    "salesforce": "https://login.salesforce.com/services/oauth2/token",
    "hubspot": "https://api.hubapi.com/integrations/v1/me",
}

# Maps service names to their authentication method types
service_access_type_map = {
    "salesforce": "UN-PWD",
    "hubspot": "API Key",
    "zoho": "UN-PWD",
}

# Maps service names to payload format types for API requests
service_payload_type_map = {
    "salesforce": "params",
    "hubspot": "params",
    "zoho": "params",
}

# Response message when in case of insufficient ddta permission
GRAPHQL__PERMISSION_DENIED = "Permission denied"
RESPONSE__PERMISSION_DENIED = "You don't have permission to access this resource"
static_frequencies = ["monthly", "quarterly", "annual", "halfyearly"]
ZOHO_DEFAULT_VERSION = "v2"

# Response message when plan is not accessible to a user
GRAPHQL_PLAN_PERMISSION_DENIED = "Permission to access this plan is denied"
RESPONSE_PLAN_PERMISSION_DENIED = "You don't have permission to access this plan"


# External Service Integration Enums
# These enums define the supported external services and their configuration options
# Used throughout the application for integration and data synchronization


# Enum defining supported external service integrations
# These are the primary external systems that can connect to the platform
class Services(Enum):
    SALESFORCE = "salesforce"
    HUBSPOT = "hubspot"


# Enum defining Salesforce OAuth grant types
# Used in the authentication process for Salesforce API
class SalesforceGrantTypes(Enum):
    PASSWORD = "password"  # nosec


# Enum defining Stripe API operation modes
# Determines how data is retrieved from Stripe (events vs. list API)
class StripeAPIModes(Enum):
    EVENTS_API = "events_api"
    LIST_API = "list_api"


# Enum defining Quickbooks report type options
# Used when retrieving different types of data from Quickbooks
class QuickbooksReportTypes(Enum):
    TRANSACTION = "transaction"
    LIST = "list"


# Enum defining HubSpot OAuth grant types
# Used in the authentication process for HubSpot API
class HubspotGrantTypes(Enum):
    ACCESSTOKEN = "accesstoken"


# Enum defining data source options for custom objects
# Determines where custom object data originates from
class CustomObjectDataSource(Enum):
    UPSTREAM = "upstream"
    MANUAL_UPLOAD = "manual_upload"


# Enum defining dashboard types in the platform
# Used to categorize different dashboard views and capabilities
class DashboardType(Enum):
    SUPERSET_DASHBOARD = "superset_dashboard"
    ALL_DASHBOARDS = "all_dashboards"
    DEFAULT_DASHBOARD = "default_dashboard"


# Enum defining HTTP request methods used in API integrations
# Standard HTTP methods for external API communication
class HttpRequestTypes(Enum):
    GET = "get"
    POST = "post"
    PUT = "put"
    DELETE = "delete"


# Enum defining task status options for scheduled operations
# Used to track the enabled/disabled state of background tasks
class TaskStatus(Enum):
    ENABLED = True
    DISABLED = False


# Mapping of task categories to their localized display names
# Uses $COMMISSION placeholder for client-specific terminology
TASK_CATEGORY_LOCALIZED = {
    "COMMISSION_STATUS": "$COMMISSIONS Status",
    "MILESTONES": "Milestone",
    "QUERIES": "Queries",
    "OTHERS": "Others",
    "COMMISSIONS": "$COMMISSIONS",
    "OPERATIONS": "Operations",
    "APPROVALS": "Approvals",
    "STATEMENT_REMINDER": "Statement review reminder",
}


# Enum defining task categories for notification system
# Categorizes notifications for filtering, display, and management
class TaskCategory(Enum):
    COMMISSION_STATUS = "Commission Status"
    MILESTONES = "Milestone"
    QUERIES = "Queries"
    OTHERS = "Others"
    COMMISSIONS = "Commissions"
    OPERATIONS = "Operations"
    APPROVALS = "Approvals"
    STATEMENT_REMINDER = "Statement review reminder"
    QUOTES = "Quote events"
    CPQ_APPROVALS = "Approval events"


# Mapping of Everstage-specific column names to user-friendly display names
# Used to provide consistent, readable column names in the UI
EVERSTAGE_COLUMNS = {
    "tierName": "Tier / Overridden Tier",
    "originalTierName": "Actual Tier",
    "quotaErosion": "Quota Retirement",
    "commission": "Commissions",
}


# List of Salesforce objects that support hard delete operations
# Used when determining whether to use hard or soft delete with Salesforce
SALESFORCE_HARD_DELETE_OBJECTS = [
    "accountpartner",
    "accountteammember",
    "contractlineitem",
    "opportunitylineitem",
    "opportunitypartner",
    "opportunitysplit",
    "opportunityteammember",
    "quotelineitem",
]

# List of Salesforce functions supported by the platform
# Used when validating function usage in calculated fields
SUPPORTED_SALESFORCE_FUNCTIONS = ["convertcurrency"]


# Enum defining notification channels and their configuration
# Each channel has connection URLs, user configuration keys, and disconnect options
class NotificationMode(Enum):
    SLACK = {
        "name": "slack",
        "connect_url": f"{get_site_url()}/slack/install",
        "employee_config_key": ["slack_user_id", "slack_team_id"],
        "disconnect_url": "/spm/payee_notification/slack/disconnect",
        "cpq_connect_url": f"{get_site_url()}/slack/cpq_install",
        "cpq_disconnect_url": "/ninja/cpq/notifications/slack/disconnect",
    }
    EMAIL = {
        "name": "email",
        "connect_url": None,
        "employee_config_key": [],
        "disconnect_url": None,
        "cpq_connect_url": None,
        "cpq_disconnect_url": None,
    }
    MS_TEAMS = {
        "name": "ms_teams",
        "connect_url": f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id={os.getenv('MSTEAMS_CLIENT_ID')}&response_type=code&redirect_uri={os.getenv('URL')}/settings/notifications&response_mode=query&scope={SCOPE_LIST}&state=msteams",
        "admin_consent_url": f"https://login.microsoftonline.com/common/adminconsent?client_id={os.getenv('MSTEAMS_CLIENT_ID')}",
        "employee_config_key": ["msteams_conversation_id", "msteams_user_id"],
        "disconnect_url": "/msteams/disconnect",
        "cpq_connect_url": f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id={os.getenv('MSTEAMS_CLIENT_ID')}&response_type=code&redirect_uri={os.getenv('URL')}/cpq/settings/notifications&response_mode=query&scope={SCOPE_LIST}&state=msteams",
        "cpq_disconnect_url": "/msteams/disconnect",
    }


# Enum defining commission freeze date options for period closing
# Used for setting up automatic commission locking dates
class CommissionFreezeDate(Enum):
    D_5 = {"value": 5, "label": "5th of every month"}
    D_10 = {"value": 10, "label": "10th of every month"}
    D_15 = {"value": 15, "label": "15th of every month"}
    D_20 = {"value": 20, "label": "20th of every month"}
    D_25 = {"value": 25, "label": "25th of every month"}


# Enum defining supported integration types
# Used to identify different notification integration systems
class IntegrationType(Enum):
    SLACK = "slack"
    MS_TEAMS = "ms_teams"
    EMAIL = "email"


class ModuleType(Enum):
    ICM = "ICM"
    CPQ = "CPQ"

    @classmethod
    def values(cls) -> List[str]:
        return [item.value for item in cls]


# Enum defining notification channel connection statuses
# Tracks whether a user has connected to a notification channel
class NotificationChannelConnectionStatus(Enum):
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"


class LocalizedEnum(Enum):
    """
    Base class for enums that need localization support.
    Provides a get_localized_value method that can be used by any enum inheriting from this class.
    """

    def get_localized_value(
        self, client_id: str, email_id: str | None = None, values: dict | None = None
    ) -> str:
        """
        Get the localized value for the enum member.
        Uses the client's localization settings.
        """
        try:
            from interstage_project.global_utils.localization_utils import (
                get_localized_message_utils,
            )

            return get_localized_message_utils(
                key=self.name, client_id=client_id, email_id=email_id, values=values
            )
        except Exception:
            return self.value


# Enum defining frequency options for commission periods and reporting
class Freq(LocalizedEnum):
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ANNUAL = "annual"
    HALFYEARLY = "halfyearly"
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    DAILY = "daily"
    CUSTOM = "custom"
    TIME = "time"


class PaymentStatus(LocalizedEnum):
    ZERO_PAYOUT = "Zero Payout"
    PARTIALLY_PAID = "Partially Paid"
    PAID = "Paid"
    OVER_PAID = "Over Paid"
    UNPAID = "Unpaid"


# Enum defining display labels for different frequency periods
class FreqLabel(Enum):
    QUARTERLY = ["Q1", "Q2", "Q3", "Q4"]
    HALFYEARLY = ["H1", "H2"]
    MONTHLY = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ]


# Enum defining task types with their implementation details
class Task(Enum):
    SLACK_COMMISSION_NOTIFICATION = {
        "name": "SLACK_COMMISSION_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification",
    }

    EMAIL_COMMISSION_NOTIFICATION = {
        "name": "EMAIL_COMISSION_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_email",
    }
    MS_TEAMS_COMMISSION_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_msteams",
    }
    SLACK_MONTHLY_COMMISSION_NOTIFICATION = {
        "name": "SLACK_MONTHLY_COMMISSION_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification",
    }
    EMAIL_MONTHLY_COMMISSION_NOTIFICATION = {
        "name": "EMAILMONTHLY_COMMISSION_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_email",
    }

    SLACK_STATEMENT_LOCKED_NOTIFICATION = {
        "name": "SLACK_STATEMENT_LOCKED_NOTIFICATION",
        "function": None,
    }
    EMAIL_STATEMENT_LOCKED_NOTIFICATION = {
        "name": "EMAIL_STATEMENT_LOCKED_NOTIFICATION",
        "function": None,
    }
    MS_TEAMS_STATEMENT_LOCKED_NOTIFICATION = {
        "name": "MS_TEAMS_STATEMENT_LOCKED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_MONTHLY_COMMISSION_NOTIFICATION = {
        "name": "MS_TEAMS_MONTHLY_COMMISSION_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_msteams",
    }

    SLACK_COMMISSION_REMINDER_NOTIFICATION = {
        "name": "SLACK_COMMISSION_REMINDER_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification",
    }

    EMAIL_COMMISSION_REMINDER_NOTIFICATION = {
        "name": "EMAIL_COMMISSION_REMINDER_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_email",
    }

    MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_msteams",
    }

    SLACK_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD = {
        "name": "SLACK_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD",
        "function": "commission_engine.tasks.payee_notifications.commission_notification",
    }

    MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD = {
        "name": "MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_msteams",
    }

    EMAIL_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD = {
        "name": "MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD",
        "function": "commission_engine.tasks.payee_notifications.commission_notification_on_email",
    }

    SLACK_PAYOUT_INITIATED_NOTIFICATION = {
        "name": "SLACK_PAYOUT_INITIATED_NOTIFICATION",
        "function": None,
    }

    EMAIL_PAYOUT_INITIATED_NOTIFICATION = {
        "name": "EMAIL_PAYOUT_INITIATED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_PAYOUT_INITIATED_NOTIFICATION = {
        "name": "MS_TEAMS_PAYOUT_INITIATED_NOTIFICATION",
        "function": None,
    }

    SLACK_COMMISSION_ATTAINED_NOTIFICATION = {
        "name": "SLACK_COMMISSION_ATTAINED_NOTIFICATION",
        "function": None,
    }

    EMAIL_COMMISSION_ATTAINED_NOTIFICATION = {
        "name": "EMAIL_COMMISSION_ATTAINED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_COMMISSION_ATTAINED_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_ATTAINED_NOTIFICATION",
        "function": None,
    }

    SLACK_QUOTA_ATTAINED_NOTIFICATION = {
        "name": "SLACK_QUOTA_ATTAINED_NOTIFICATION",
        "function": None,
    }

    EMAIL_QUOTA_ATTAINED_NOTIFICATION = {
        "name": "EMAIL_QUOTA_ATTAINED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_QUOTA_ATTAINED_NOTIFICATION = {
        "name": "MS_TEAMS_QUOTA_ATTAINED_NOTIFICATION",
        "function": None,
    }

    SLACK_QUERY_ASSIGNED_NOTIFICATION = {
        "name": "SLACK_QUERY_ASSIGNED_NOTIFICATION",
        "function": None,
    }

    EMAIL_QUERY_ASSIGNED_NOTIFICATION = {
        "name": "EMAIL_QUERY_ASSIGNED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_QUERY_ASSIGNED_NOTIFICATION = {
        "name": "MS_TEAMS_QUERY_ASSIGNED_NOTIFICATION",
        "function": None,
    }
    SLACK_CATEGORY_CHANGE_NOTIFICATION = {
        "name": "SLACK_CATEGORY_CHANGE_NOTIFICATION",
        "function": None,
    }
    EMAIL_CATEGORY_CHANGE_NOTIFICATION = {
        "name": "EMAIL_CATEGORY_CHANGE_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_CATEGORY_CHANGE_NOTIFICATION = {
        "name": "MS_TEAMS_CATEGORY_CHANGE_NOTIFICATION",
        "function": None,
    }

    SLACK_QUERY_COMMENT_IS_ADDED_NOTIFICATION = {
        "name": "SLACK_QUERY_COMMENT_IS_ADDED_NOTIFICATION",
        "function": None,
    }

    EMAIL_QUERY_COMMENT_IS_ADDED_NOTIFICATION = {
        "name": "EMAIL_QUERY_COMMENT_IS_ADDED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_QUERY_COMMENT_IS_ADDED_NOTIFICATION = {
        "name": "MS_TEAMS_QUERY_COMMENT_IS_ADDED_NOTIFICATION",
        "function": None,
    }

    SLACK_CC_IN_QUERY_IS_ADDED_NOTIFICATION = {
        "name": "SLACK_CC_IN_QUERY_IS_ADDED_NOTIFICATION",
        "function": None,
    }

    EMAIL_CC_IN_QUERY_IS_ADDED_NOTIFICATION = {
        "name": "EMAIL_CC_IN_QUERY_IS_ADDED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_CC_IN_QUERY_IS_ADDED_NOTIFICATION = {
        "name": "MS_TEAMS_CC_IN_QUERY_IS_ADDED_NOTIFICATION",
        "function": None,
    }

    SLACK_QUERY_CLOSED_NOTIFICATION = {
        "name": "SLACK_QUERY_CLOSED_NOTIFICATION",
        "function": None,
    }

    EMAIL_QUERY_CLOSED_NOTIFICATION = {
        "name": "EMAIL_QUERY_CLOSED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_QUERY_CLOSED_NOTIFICATION = {
        "name": "MS_TEAMS_QUERY_CLOSED_NOTIFICATION",
        "function": None,
    }

    SLACK_PLAN_UPDATED_NOTIFICATION = {
        "name": "SLACK_PLAN_UPDATED_NOTIFICATION",
        "function": None,
    }

    EMAIL_PLAN_UPDATED_NOTIFICATION = {
        "name": "EMAIL_PLAN_UPDATED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_PLAN_UPDATED_NOTIFICATION = {
        "name": "MS_TEAMS_PLAN_UPDATED_NOTIFICATION",
        "function": None,
    }

    SLACK_REPORTING_MANAGER_UPDATED_NOTIFICATION = {
        "name": "SLACK_REPORTING_MANAGER_UPDATED_NOTIFICATION",
        "function": None,
    }

    EMAIL_REPORTING_MANAGER_UPDATED_NOTIFICATION = {
        "name": "EMAIL_REPORTING_MANAGER_UPDATED_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_REPORTING_MANAGER_UPDATED_NOTIFICATION = {
        "name": "MS_TEAMS_REPORTING_MANAGER_UPDATED_NOTIFICATION",
        "function": None,
    }

    SLACK_TEAM_UPDATED_NOTIFICATION = {
        "name": "SLACK_TEAM_UPDATED_NOTIFICATION",
        "function": None,
    }

    EMAIL_TEAM_UPDATED_NOTIFICATION = {
        "name": "EMAIL_TEAM_UPDATED_NOTIFICATION",
        "function": None,
    }

    EMAIL_NEW_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "EMAIL_NEW_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }
    SLACK_NEW_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "SLACK_NEW_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }
    SLACK_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "SLACK_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }
    EMAIL_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "EMAIL_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }
    MSTEAMS_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "MSTEAMS_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }
    MSTEAMS_NEW_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "MSTEAMS_NEW_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }
    MS_TEAMS_TEAM_UPDATED_NOTIFICATION = {
        "name": "MS_TEAMS_TEAM_UPDATED_NOTIFICATION",
        "function": None,
    }

    SLACK_COMMISSION_FROZEN_NOTIFICATION = {
        "name": "SLACK_COMMISSION_FROZEN_NOTIFICATION",
        "function": None,
    }

    EMAIL_COMMISSION_FROZEN_NOTIFICATION = {
        "name": "EMAIL_COMMISSION_FROZEN_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_COMMISSION_FROZEN_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_FROZEN_NOTIFICATION",
        "function": None,
    }

    SLACK_COMMISSION_UNFROZEN_NOTIFICATION = {
        "name": "SLACK_COMMISSION_UNFROZEN_NOTIFICATION",
        "function": None,
    }

    EMAIL_COMMISSION_UNFROZEN_NOTIFICATION = {
        "name": "EMAIL_COMMISSION_UNFROZEN_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_COMMISSION_UNFROZEN_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_UNFROZEN_NOTIFICATION",
        "function": None,
    }

    SLACK_COMMISSION_MARKED_AS_PAID_NOTIFICATION = {
        "name": "SLACK_COMMISSION_MARKED_AS_PAID_NOTIFICATION",
        "function": None,
    }

    EMAIL_COMMISSION_MARKED_AS_PAID_NOTIFICATION = {
        "name": "EMAIL_COMMISSION_MARKED_AS_PAID_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_COMMISSION_MARKED_AS_PAID_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_MARKED_AS_PAID_NOTIFICATION",
        "function": None,
    }

    SLACK_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION = {
        "name": "SLACK_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION",
        "function": None,
    }

    EMAIL_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION = {
        "name": "EMAIL_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION",
        "function": None,
    }

    MS_TEAMS_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION = {
        "name": "MS_TEAMS_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION",
        "function": None,
    }

    SLACK_QUERY_STATUS_UPDATE_NOTIFICATION = {
        "name": "SLACK_QUERY_STATUS_UPDATE_NOTIFICATION",
        "function": "commission_engine.tasks.admin_notifications.drs_status_notification",
    }

    EMAIL_QUERY_STATUS_UPDATE_NOTIFICATION = {
        "name": "EMAIL_QUERY_STATUS_UPDATE_NOTIFICATION",
        "function": "commission_engine.tasks.admin_notifications.drs_status_notification_on_email",
    }

    MS_TEAMS_QUERY_STATUS_UPDATE_NOTIFICATION = {
        "name": "MS_TEAMS_QUERY_STATUS_UPDATE_NOTIFICATION",
        "function": "commission_engine.tasks.admin_notifications.drs_status_notification_on_msteams",
    }

    APPROVALS_AUTO_APPROVE_TASK = {
        "name": "APPROVAL_AUTO_APPROVE_TASK",
        "function": "commission_engine.tasks.approval_workflows.auto_approve_daily_task",
    }

    APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK = {
        "name": "APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK",
        "function": "commission_engine.tasks.approval_workflows.pending_requests_email_notify_task",
    }

    CPQ_APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK = {
        "name": "CPQ_APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK",
        "function": "everstage_ddd.cpq.approvals.service.approval_workflows.pending_quote_requests_email_notify_task",
    }

    USERGROUPMEMBERS = {
        "name": "USERGROUPMEMBERS",
        "function": "spm.tasks.user_group_members_base_task",
    }

    DOCUISGN_DOCUSMENTS = {
        "name": "DOCUISGNDOCUSMENTS",
        "function": "spm.tasks.docusign_documents_sync_base_task",
    }

    CPQ_EMAIL_QUOTE_UPDATED_NOTIFICATION = {
        "name": "CPQ_EMAIL_QUOTE_UPDATED_NOTIFICATION",
        "function": None,
    }

    CPQ_SLACK_QUOTE_UPDATED_NOTIFICATION = {
        "name": "CPQ_SLACK_QUOTE_UPDATED_NOTIFICATION",
        "function": None,
    }

    CPQ_MS_TEAMS_QUOTE_UPDATED_NOTIFICATION = {
        "name": "CPQ_MS_TEAMS_QUOTE_UPDATED_NOTIFICATION",
        "function": None,
    }

    CPQ_EMAIL_QUOTE_SIGNED_NOTIFICATION = {
        "name": "CPQ_EMAIL_QUOTE_SIGNED_NOTIFICATION",
        "function": None,
    }

    CPQ_SLACK_QUOTE_SIGNED_NOTIFICATION = {
        "name": "CPQ_SLACK_QUOTE_SIGNED_NOTIFICATION",
        "function": None,
    }

    CPQ_MS_TEAMS_QUOTE_SIGNED_NOTIFICATION = {
        "name": "CPQ_MS_TEAMS_QUOTE_SIGNED_NOTIFICATION",
        "function": None,
    }

    CPQ_EMAIL_QUOTE_WON_NOTIFICATION = {
        "name": "CPQ_EMAIL_QUOTE_WON_NOTIFICATION",
        "function": None,
    }

    CPQ_SLACK_QUOTE_WON_NOTIFICATION = {
        "name": "CPQ_SLACK_QUOTE_WON_NOTIFICATION",
        "function": None,
    }

    CPQ_MS_TEAMS_QUOTE_WON_NOTIFICATION = {
        "name": "CPQ_MS_TEAMS_QUOTE_WON_NOTIFICATION",
        "function": None,
    }

    CPQ_EMAIL_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "CPQ_EMAIL_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }

    CPQ_SLACK_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "CPQ_SLACK_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }

    CPQ_MS_TEAMS_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "CPQ_MS_TEAMS_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "function": None,
    }

    CPQ_SLACK_NEW_QUOTE_APPROVALS_NOTIFICATION = {
        "name": "CPQ_SLACK_NEW_QUOTE_APPROVALS_NOTIFICATION",
        "function": None,
    }

    CPQ_EMAIL_NEW_QUOTE_APPROVALS_NOTIFICATION = {
        "name": "CPQ_EMAIL_NEW_QUOTE_APPROVALS_NOTIFICATION",
        "function": None,
    }

    CPQ_MS_TEAMS_NEW_QUOTE_APPROVALS_NOTIFICATION = {
        "name": "CPQ_MS_TEAMS_NEW_QUOTE_APPROVALS_NOTIFICATION",
        "function": None,
    }


class STATUS_CODE(Enum):
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    IN_PROGRESS = "IN_PROGRESS"


class ModelPkEnum(Enum):
    DEAL = "deal_id"


class SecondaryModelEnum(Enum):
    DEAL = "extendedDeal"


# Status codes for ETL (Extract, Transform, Load) processes
class ETL_STATUS(Enum):
    STARTED = "started"
    COMPLETE = "complete"
    EXTRACTION = "extraction"
    TRANSFORMATION = "transformation"
    LOADING = "loading"
    SUCCESS = "success"
    FAILED = "failed"
    LOCKED = "locked"
    LOCK_FAILED = "lock_failed"
    PARTIALLY_FAILED = "partially_failed"
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    SKIPPED = "skipped"


# UI display status for ETL processes
class ETL_UI_STATUS(Enum):
    WAIT = "wait"
    PROCESS = "process"
    FINISH = "finish"
    ERROR = "error"
    SKIPPED = "skipped"


# Defines synchronization object types for data sync operations
# These represent different data entities and hierarchies in the commission system
class SYNC_OBJECT(Enum):
    UPSTREAM_WRAPPER = "UPSTREAM_WRAPPER_SYNC"
    COMMISSION_WRAPPER_SYNC = "COMMISSION_WRAPPER_SYNC"
    COMMISSION_WRAPPER_SYNC_L1 = "COMMISSION_WRAPPER_SYNC_L1"
    FORECAST_WRAPPER_SYNC = "FORECAST_WRAPPER_SYNC"
    FORECAST_WRAPPER_SYNC_L1 = "FORECAST_WRAPPER_SYNC_L1"
    COMMISSION_PAYEE_SYNC = "COMMISSION_PAYEE_SYNC"
    FORECAST_PAYEE_SYNC = "FORECAST_PAYEE_SYNC"
    COMMISSION_PAYEE_SYNC_L1 = "COMMISSION_PAYEE_SYNC_L1"
    FORECAST_PAYEE_SYNC_L1 = "FORECAST_PAYEE_SYNC_L1"
    TEAM_CRITERIA_WRAPPER = "TEAM_CRITERIA_WRAPPER"
    TEAM_CRITERIA_WRAPPER_L1 = "TEAM_CRITERIA_WRAPPER_L1"
    FORECAST_TEAM_CRITERIA_WRAPPER = "FORECAST_TEAM_CRITERIA_WRAPPER"
    FORECAST_TEAM_CRITERIA_WRAPPER_L1 = "FORECAST_TEAM_CRITERIA_WRAPPER_L1"
    TEAM_PAYEE_SYNC = "TEAM_PAYEE_SYNC"
    TEAM_PAYEE_SYNC_L1 = "TEAM_PAYEE_SYNC_L1"
    FORECAST_TEAM_PAYEE_SYNC = "FORECAST_TEAM_PAYEE_SYNC"
    FORECAST_TEAM_PAYEE_SYNC_L1 = "FORECAST_TEAM_PAYEE_SYNC_L1"
    DATABOOK_WRAPPER_SYNC = "DATABOOK_WRAPPER_SYNC"
    DATABOOK_SYNC = "DATABOOK_SYNC"
    SETTLEMENT_WRAPPER_SYNC = "SETTLEMENT_WRAPPER_SYNC"
    SETTLEMENT_WRAPPER_SYNC_L1 = "SETTLEMENT_WRAPPER_SYNC_L1"
    SETTLEMENT_SYNC = "SETTLEMENT_SYNC"
    SETTLEMENT_SYNC_L1 = "SETTLEMENT_SYNC_L1"
    DATASHEET_SYNC = "DATASHEET_SYNC"
    PAYOUT_SNAPSHOT_WRAPPER_SYNC = "PAYOUT_SNAPSHOT_WRAPPER_SYNC"
    DELETE_PAYOUT_SNAPSHOT_SYNC = "DELETE_PAYOUT_SNAPSHOT_SYNC"
    PAYOUT_SNAPSHOT_SYNC = "PAYOUT_SNAPSHOT_SYNC"
    SETTLEMENT_SNAPSHOT_WRAPPER_SYNC = "SETTLEMENT_SNAPSHOT_WRAPPER_SYNC"
    SETTLEMENT_SNAPSHOT_SYNC = "SETTLEMENT_SNAPSHOT_SYNC"
    REPORT_WRAPPER_SYNC = "REPORT_WRAPPER_SYNC"
    SYSTEM_REPORT_WRAPPER_SYNC = "SYSTEM_REPORT_WRAPPER_SYNC"
    COMMISSION_REPORT_WRAPPER_SYNC = "COMMISSION_REPORT_WRAPPER_SYNC"
    INTER_OBJECT_WRAPPER_SYNC = "INTER_REPORT_WRAPPER_SYNC"
    FORECAST_REPORT_WRAPPER_SYNC = "FORECAST_REPORT_WRAPPER_SYNC"
    INTER_FORECAST_REPORT_WRAPPER_SYNC = "INTER_FORECAST_REPORT_WRAPPER_SYNC"
    SETTLEMENT_REPORT_WRAPPER_SYNC = "SETTLEMENT_REPORT_WRAPPER_SYNC"
    REPORT_OBJECT_SYNC = {
        "commission": "COMMISSION_REPORT_SYNC",
        "quota": "QUOTA_REPORT_SYNC",
        "user": "USER_REPORT_SYNC",
        "quota_attainment": "QUOTA_ATTAINMENT_REPORT_SYNC",
        "commission_summary": "COMMISSION_SUMMARY_REPORT_SYNC",
        "inter_commission": "INTER_COMMISSION_REPORT_SYNC",
        "inter_quota_attainment": "INTER_QUOTA_ATTAINMENT_REPORT_SYNC",
        "settlement": "SETTLEMENT_REPORT_SYNC",
        "forecast_commission": "FORECAST_REPORT_SYNC",
        "inter_forecast_commission": "INTER_FORECAST_REPORT_SYNC",
        "forecast_quota_attainment": "FORECAST_QUOTA_ATTAINMENT_REPORT_SYNC",
        "inter_forecast_quota_attainment": "INTER_FORECAST_QUOTA_ATTAINMENT_REPORT_SYNC",
        "payout": "PAYOUT_REPORT_SYNC",
        "fx_rate": "FX_RATE_REPORT_SYNC",
        "statement_approvals": "APPROVALS_REPORT_SYNC",
        "quote": "QUOTE_REPORT_SYNC",
        "quote_line_item": "QUOTE_LINE_ITEM_REPORT_SYNC",
        "quote_line_item_tier": "QUOTE_LINE_ITEM_TIER_REPORT_SYNC",
    }
    DATABOOK_CUSTOM_OBJECT_SYNC = "CUSTOM_OBJECT_DB_SYNC"
    DATABOOK_REPORT_OBJECT_SYNC = "REPORT_OBJECT_DB_SYNC"
    L1_DATABOOK_REPORT_OBJECT_SYNC = "INTER_REPORT_OBJECT_DB_SYNC"
    DATABOOK_SYSTEM_CUSTOM_SYNC = "SYSTEM_CUSTOM_DB_SYNC"
    DATABOOK_COMMISSION_OBJECT = "COMMISSION_DB_SYNC"
    DATABOOK_FORECAST_OBJECT = "FORECAST_DB_SYNC"
    DATABOOK_SETTLEMENT_OBJECT = "SETTLEMENT_DB_SYNC"
    DATABOOK_INTER_OBJECT_SYNC = "INTER_OBJECT_DB_SYNC"
    DATABOOK_INTER_FORECAST_OBJECT_SYNC = "INTER_FORECAST_OBJECT_DB_SYNC"
    MIGRATE_INTER_OBJECT_SYNC = "MIGRATE_INTER_OBJECT_SYNC"
    DATASHEET_WRAPPER_SYNC = "DATASHEET_WRAPPER_SYNC"
    PLAN_MODIFICATION_SYNC = "PLAN_MODIFICATION_SYNC"
    FIVETRAN_CONNECTOR_SYNC = "FIVETRAN_CONNECTOR_SYNC"
    PAYOUT_SNAPSHOT_COMM_SYNC = "PAYOUT_SNAPSHOT_COMM_SYNC"
    PAYOUT_SNAPSHOT_INTER_COMM_SYNC = "PAYOUT_SNAPSHOT_INTER_COMM_SYNC"
    MIRROR_COMMISSION_DATA_TO_SETTLEMENT = "MIRROR_COMMISSION_DATA_TO_SETTLEMENT"


# Data source types for datasheets
class DATASHEET_SOURCE(Enum):
    CUSTOM_OBJECT = "custom_object"
    OBJECT = "object"
    DATASHEET = "datasheet"
    REPORT = "report"


# Filter types for datasheet operations
class DATASHEET_FILTER_TYPE(Enum):
    FILTER = "FILTER"
    PIVOT = "PIVOT"


object_strategy = {
    "object": "CustomObjectStrategy",
    "datasheet": "DatasheetDataStrategy",
    "commission": "CommissionReportStrategy",
    "user": "UserReportStrategy",
    "commission_summary": "CommissionSummaryReportStrategy",
    "quota": "QuotaReportStrategy",
    "quota_attainment": "QuotaAttainmentReportStrategy",
    "inter_commission": "InterCommissionReportStrategy",
    "inter_quota_attainment": "InterQuotaAttainmentReportStrategy",
    "settlement": "SettlementReportStrategy",
    "forecast_commission": "ForecastReportStrategy",
    "inter_forecast_commission": "InterForecastReportStrategy",
    "forecast_quota_attainment": "ForecastQuotaAttainmentReportStrategy",
    "inter_forecast_quota_attainment": "InterForecastQuotaAttainmentReportStrategy",
    "payout": "PayoutReportStrategy",
    "fx_rate": "FxReportStrategy",
    "statement_approvals": "ApprovalsStatementReportStrategy",
}


syncObjectMap = {
    "upstream_wrapper_sync": SYNC_OBJECT.UPSTREAM_WRAPPER.value,
    "commission_wrapper_sync": SYNC_OBJECT.COMMISSION_WRAPPER_SYNC.value,
    "commission_payee_sync": SYNC_OBJECT.COMMISSION_PAYEE_SYNC.value,
    "team_criteria_wrapper": SYNC_OBJECT.TEAM_CRITERIA_WRAPPER.value,
    "team_payee_sync": SYNC_OBJECT.TEAM_PAYEE_SYNC.value,
}


TimePeriodWt = {
    Freq.QUARTERLY.value: 3,
    Freq.MONTHLY.value: 1,
    Freq.HALFYEARLY.value: 6,
    Freq.ANNUAL.value: 12,
}

# Maps payout frequencies to the number of payouts per year
# Used for calendaring and period calculations
payoutFreqMap = {
    Freq.MONTHLY.value: 12,
    Freq.QUARTERLY.value: 4,
    Freq.HALFYEARLY.value: 2,
    Freq.ANNUAL.value: 1,
}

# Maps variable data types to Python data types
# Used for type conversion and validation in data processing
varDatatypePyDatatypeMap = {
    "INTEGER": "float64",
    "DATE": "datetime64[ns, UTC]",
    # 'DATE': 'datetime64',
    "BOOLEAN": "bool",
    "STRING": "str",
    "PERCENTAGE": "float64",
    "EMAIL": "str",
    "OBJECT": "object",
    "STRINGARRAY": "object",
    "HIERARCHY": "str",
}

# Maps variable data types to Snowflake data types
# Used when creating tables and executing queries in Snowflake
varDatatypeSnowflakeDatatypeMap = {
    "INTEGER": "DOUBLE",
    "DATE": "DATETIME",
    "BOOLEAN": "BOOLEAN",
    "STRING": "STRING",
    "PERCENTAGE": "DOUBLE",
    "EMAIL": "STRING",
    "OBJECT": "OBJECT",
    "STRINGARRAY": "ARRAY",
    "HIERARCHY": "STRING",
}


# Special representation string for hierarchies in Stormbreaker
# Used to identify and process hierarchy data in the analytics engine
STORMBREAKER_HIERARCHY_REPR = "object:repr::string"

# Maps service names to their token type prefixes
# Used in API authentication headers
service_access_token_type_map = {"salesforce": "Bearer ", "hubspot": "Bearer "}


# Enum defining time period units for calendaring
# Used in defining commission periods and reporting timeframes
class Period(Enum):
    MONTH = "Month"
    QUARTER = "Quarter"
    YEAR = "Year"
    HALFYEAR = "Halfyear"
    DAY = "Day"
    TIME = "HH:mm"


# Maps period types to frequency values
# Used to convert between period and frequency representations
period_to_freq_map = {
    Period.DAY.value: Freq.DAILY.value,
    Period.MONTH.value: Freq.MONTHLY.value,
    Period.QUARTER.value: Freq.QUARTERLY.value,
    Period.HALFYEAR.value: Freq.HALFYEARLY.value,
    Period.YEAR.value: Freq.ANNUAL.value,
    Period.TIME.value: Freq.TIME.value,
}


# Enum defining calendar types (fiscal vs. calendar year)
# Used to determine date calculations and period boundaries
class CalType(Enum):
    FISCAL = "Fiscal"
    CALENDAR = "Calendar"


# Enum defining period unit references (current vs. previous)
# Used in relative time references for reporting and calculations
class PeriodUnit(Enum):
    CURRENT = "Current"
    PREVIOUS = "Previous"


# Datasheet action types that trigger updates
class DATASHEET_ACTION(Enum):
    SPEC_CHANGE = "SPEC_CHANGE"
    SOURCE_DATA_CHANGE = "SOURCE_DATA_CHANGE"
    SOURCE_GLOBAL_ADJUSTMENTS_CHANGE = "SOURCE_GLOBAL_ADJUSTMENTS_CHANGE"


# Reasons for datasheet regeneration
class DatasheetDataRegenerateReason(Enum):
    ANCESTOR_MODIFIED = "ANCESTOR MODIFIED"
    LOCAL_ADJUSTMENT_ADDED = "LOCAL ADJUSTMENT ADDED"
    LOCAL_ADJUSTMENT_REVERTED = "LOCAL ADJUSTMENT REVERTED"
    ANCESTOR_CUSTOM_OBJECT_MODIFIED = "ANCESTOR CUSTOM OBJECT MODIFIED"
    ANCESTOR_REPORT_OBJECT_MODIFIED = "ANCESTOR REPORT OBJECT MODIFIED"
    SOURCE_REPORT_OBJECT_DATA_MODIFIED = "SOURCE REPORT OBJECT DATA MODIFIED"
    ANCESTOR_SHEET_CONFIG_MODIFIED = "ANCESTOR SHEET CONFIG MODIFIED"
    CURRENT_SHEET_CONFIG_MODIFIED = "CURRENT CONFIG CHANGED"


# Authentication methods supported by the platform
class LoginMethods(Enum):
    GOOGLE = "google-apps"
    AUTH0 = "auth0"
    SALESFORCE = "salesforce"
    HUBSPOT = "hubspot"


# Analytics and User Tracking Configuration
# These components define the analytics tracking system for monitoring user behavior
# Used to capture events, properties, and metrics for product analytics


# Enum defining analytics tracking properties for Segment integration
# These properties capture user attributes and events for analytics tracking,
# monitoring user activity, and measuring platform engagement
class SegmentProperties(Enum):
    # User identification and basic profile properties
    IP_ADDRESS = "ip"
    BROWSER = "Browser"
    DEVICE = "Device"
    NAME = "Name"
    FIRST_NAME = "First Name"
    LAST_NAME = "Last Name"
    EMAIL = "Email"
    CLIENT_ID = "Client ID"
    CLIENT = "Client"
    IS_MANAGER = "Is Manager"
    ROLE = "Role"
    PREFERRED_LANGUAGE = "Preferred Language"
    EMPLOYEE_ID = "Employee Id"
    CRYSTAL_ACCESS = "Crystal Access"
    STATUS = "Status"
    EMPLOYMENT_COUNTRY = "Employment Country"
    JOINING_DATE = "Joining Date"
    EFFECTIVE_START_DATE = "Effective Start Date"
    EXIT_DATE = "Exit Date"
    EFFECTIVE_END_DATE = "Effective End Date"
    DESIGNATION = "Designation"
    LEVEL = "Level"
    PAY_CURRENCY = "Pay Currency"
    PAYOUT_FREQUENCY = "Payout Frequency"

    # Slack integration tracking properties
    SLACK_CONNECTED = "Slack Connected"
    SLACK_NOTIFICATION_FREQ = "Slack Notification Cadence"
    CONNECT_SLACK_DATE = "Connect Slack Date"
    DISCONNECT_SLACK_DATE = "Disconnect Slack Date"

    # General platform usage tracking properties
    LOGIN_METHOD = "Login Method"
    PLATFORM_USED = "Platform Used"
    TIME_PERIOD = "Time Period"
    BULK_ACTION = "Bulk Action"
    PAYEE = "Payee"
    PLAN_NAME = "Plan Name"
    PLAN_PERIOD = "Plan Period"
    PLAN_STATUS = "Plan Status"
    CLONED_PLAN_NAME = "Cloned Plan Name"
    IS_SPIFF = "isSPIFF"
    NUM_OF_PAYEES = "Num of Payees"
    NUM_OF_CRITERIA = "Num of Criteria"
    DOCUMENT_ATTACHED = "Document Attached"
    CRITERIA_NAME = "Criteria Name"
    CRITERIA_TYPE = "Criteria Type"
    QUERY_ID = "Query ID"
    QUERY_TITLE = "Query Title"
    FROM_PLAN_NAME = "From Plan Name"
    TO_PLAN_NAME = "To Plan Name"
    SLACK_EMAIL = "Slack Email"
    CONNECTION_TYPE = "Connection Type"
    PAYEE_NAME = "Payee Name"
    PAYEE_EMP_ID = "Payee Employee Id"
    CMD_TYPE = "Command Type"
    QUERY_ACTION = "Query Entry Point"
    NOTIFICATION_TYPE = "Notification Type"
    DOCUSIGN_EMAIL = "DocuSign Email"
    TEMPLATE_NAME = "Template Name"
    LINKED_PLAN = "Linked Plan"
    ENV_SENT = "Num of envelopes sent"
    ENTRY_POINT = "Entry Point"
    FISCAL_YEAR = "Fiscal Year"

    # Custom Team Event Properties
    CUSTOM_TEAM_NAME = "Custom Team Name"

    # Reporting Hierarchy Event Properties
    HIERARCHY_FROM_MANAGER = "From Manager"
    HIERARCHY_TO_MANAGER = "To Manager"
    HIERARCHY_TO_NUM_PAYEES = "Number of Payees"

    CHANGED_OPPORTUNITIES = "Num of Changed Opportunities"
    CHANGED_FIELDS = "Changed Fields"

    REVENUE_LEADER = "Revenue Leader"
    EFFECTIVE_TIME_PERIOD = "Effective Time Period"

    CURRENCY = "Currency"

    NUM_OF_ROWS_IMPORTED = "Num of rows imported"
    OBJECT_NAME = "Object Name"

    ADJUSTMENT_TYPE = "Adjustment Type"

    # MS Teams Event properties
    MS_TEAMS_EMAIL = "MS Teams email"
    MS_TEAMS_CONNECTION_TYPE = "Connection Type"
    MS_TEAMS_PAYEE_EMPLOYEE_ID = "Payee Employee ID"
    CONNECT_MS_TEAMS_DATE = "Connect MS Teams Date"
    DISCONNECT_MS_TEAMS_DATE = "Disconnect MS Teams Date"
    MS_TEAMS_CONNECTED = "MS Teams Connected"

    # Quota Event Properties
    QUOTA_CATEGORY = "Quota Category"

    # Databook Event Properties
    DATABOOK_ID = "Databook ID"
    DATABOOK_NAME = "Databook Name"
    NEW_DATABOOK_NAME = "New Databook Name"
    CLONED_DATABOOK_ID = "Cloned Databook ID"
    CLONED_DATABOOK_NAME = "Cloned Databook Name"

    # Datasheet Event Properties
    DATASHEET_ID = "Datasheet ID"
    DATASHEET_NAME = "Datasheet Name"
    NEW_DATASHEET_NAME = "New Datasheet Name"
    DATASHEET_DATA_SOURCE = "Datasheet Data Source"
    DATASHEET_SOURCE_NAME = "Datasheet Source Name"
    DATA_OUTPUT = "Data output"

    NUM_OF_USERS_IMPORTED = "Num of users imported"
    NUM_OF_USERS_INVITED = "Num of users invited"
    CHANGED_PROFILE_FIELDS = "Changed Profile Fields"
    CHANGED_QUERY_SETTING_CONFIG = "Changed Query Setting Config"

    # Approval workflow tracking properties
    APPROVAL_TEMPLATE_NAME = "Approval template name"
    APPROVAL_TEMPLATE_ID = "Approval template id"
    NUM_OF_INSTANCES = "Num of instances"
    NUM_OF_APPROVAL_REQUEST = "Num of approval request"
    APPROVAL_INSTANCE_TYPE = "Approval instance type"
    APPROVAL_REQUEST_ID = "Approval request id"
    APPROVAL_STAGE_NAME = "Approval stage name"
    APPROVAL_STAGE_ID = "Approval stage id"
    APPROVERS = "Approvers"
    STAGE_DUE_DATE = "Stage due date"
    COMMENTS = "Comments"
    COUNT_OF_STAGES = "Count of stages"
    IS_LINE_ITEM_LEVEL = "Is line item level"
    APPROVAL_CONFIG = "Approval config"

    # Group management properties
    PERIOD = "Period"
    GROUP_NAME = "Group Name"
    ADD_MEMBERS_BASED_ON_RULES = "Add Members Based on Rules"
    EXCLUDE_USERS = "Exclude Users"

    # Crystal view properties (forecasting/simulation)
    CRYSTAL_SIMULATOR_NAME = "Crystal Simulator Name"
    CRYSTAL_CREATED_DATE = "Crystal Created Date"
    ADDL_PAYEES_TO_CRYSTAL_VIEW = "Addl. Payees to Crystal View"

    # Notification event properties
    NEW_APPROVAL_REQUEST_NOTIFICATION = "New Approval Request Notification"
    EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = (
        "Everyday Pending Approval Request Notification"
    )
    NEW_ROLE = "New Role"
    EDIT_ROLE = "Edit Role"
    CLONE_ROLE = "Clone Role"

    # Custom field properties
    CUSTOME_FIELD_NAME = "Custom Field Name"
    CUSTOME_FIELD_TYPE = "Custom Field Type"
    IS_MANDATORY = "Is Mandatory"

    IS_SPIFF_PLAN_REORDERED = "Is SPIFF Plan Reordered"

    COMMISSION_LOCK_DATE = "Commission Lock Date"

    # Datasheet and view configuration properties
    IS_GLOBAL_ADJUSTMENT = "Is Global Adjustment"
    COUNT_OF_ROW_PERMISSIONS = "Count of Row Permissions"
    HIDE_COLUMNS = "Hide Columns"

    VIEW_NAME = "View Name"
    DISPLAY_CONDITION_FIELD = "Display Condition Field"
    COUNT_OF_DISPLAY_CONDITIONS = "Count of Display Conditions"
    SUCCESS_ACTION_FIELD = "Success Action Field"
    COUNT_OF_SUCCESS_ACTION = "Count of Success Actions"
    DATE_FIELD_NAME = "Date Field Name"
    ROW_NAME_FIELD = "Row Name Field"
    COUNT_OF_COLUMNS = "Count of Columns"
    COUNT_OF_EDITABLE_COLUMN_NAMES = "Count of Editable Column Names"
    DATE_OF_CREATION = "Date of Creation"
    COUNT_OF_USERS_IMPORTED = "Count of Users Imported"
    OVERWRITE_VALUE = "Overwrite Value"
    COUNT_OF_USERS_UPDATED = "Count of Users Updated"

    # Datasheet transformation properties
    JOIN_TYPE = "Join Type"
    AGGREGATION_TYPE = "Aggregation Type"
    COUNT_OF_TRANSFORMATIONS = "Count of Transformations"
    COUNT_OF_FIELDS = "Count of Fields"

    PERIOD_START = "Period Start Date"
    PERIOD_END = "Period End Date"

    MANAGE_DATA = "Manage Data"

    # Request tracking properties
    REQUEST_ID = "Request ID"
    SUMMARY = "Summary"
    REJECTION_REASON = "Rejection Reason"

    INSTANCE_ID = "Instance ID"
    ENTITY_KEYS = "Entity Keys"

    # Commission Plan RBAC
    ADD_SHARED_PLAN_USERS = "Add Shared Plan Users and Groups"
    REMOVE_SHARED_PLAN_USERS = "Remove Shared Plan Users and Groups"
    EDIT_SHARED_PLAN_USERS = "Edit Shared Plan Users and Groups"

    FIELD_TYPE = "Field Type"


# Mapping between MixPanel property keys and their display names
# Used to standardize property naming between systems
mixPanelPropertiesMap = {
    "entry_point": "Entry Point",
    "payee_name": "Payee Name",
    "currency_type": "Currency Type",
    "currency_name": "Currency Name",
    "period": "Period",
    "page_name": "Page Name",
    "criteria_name": "Criteria Name",
    "quota_category": "Quota Categoty",
    "fiscal_year": "Fiscal Year",
    "widget_type_name": "Widget Type Name",
    "dashboard_name": "Dashboard Name",
    "filter_columns": "Filter Columns",
    "quick_filter_type": "Quick Filter Type",
    "export_option": "Export Option",
    "count_of_users_imported": "Count of Users Imported",
    "list_of_filters": "List of Filters",
    "user_name": "User Name",
    "user_role": "User Role",
    "text_searched": "Text Searched",
    "template_download": "Template Download",
    "time_to_export": "Time to Export",
    "is_arrears_view": "Is Arrears View",
    "crystal_simulator_name": "Crystal Simulator Name",
    "addl_payees_to_crystal_view": "Addl. Payees to Crystal View",
    "remove_payee": "Remove Payee",
    "overwrite_values": "Overwrite Values",
    "time_in_filter_page": "Time in Filter Page",
    "view_payouts_filter": "View Payouts Filter",
    "admin_email": "Admin Email",
    "connect_ms_teams_date": "Connect MS Teams Date",
    "disconnect_ms_teams": "Disconnect MS Teams",
    "disconnect_ms_teams_date": "Disconnect MS Teams Date",
    "commission_plan_name": "Commission Plan Name",
    "criteria": "Criteria",
    "databook": "Databook",
    "datasheet": "Datasheet",
    "report_type": "Report Type",
    "count_of_variables_added": "Count of Variables Added",
    "include_all_plans_using_this_ds": "Include all plans using this Datasheet",
    "query_assignment_option": "Query Assignment Option",
    "allow_cc": "Allow CC",
    "datasheet_name": "Datasheet Name",
    "data_option": "Data Option",
    "transformation_option": "Transformation Option",
    "data_type": "Data Type",
    "formula_field_name": "Formula Field Name",
    "export_type": "Export Type",
    "count_of_row_groups": "Count of Row Groups",
    "count_of_column_labels": "Count of Column Labels",
    "count_of_values": "Count of Values",
    "adjustment_type": "Adjustment Type",
    "is_global_adjustment": "Is Global Adjustment",
    "count_of_saved_views": "Count of Saved Views",
    "join_type": "Join Type",
    "aggregation_type": "Aggregation Type",
    "count_of_transformations": "Count of Transformations",
    "count_of_fields": "Count of Fields",
    "apply_filter": "Apply Filter",
    "search_text": "Search Text",
    "quick_filter": "Quick Filter",
    "paginated_value": "Paginated Value",
    "platform_used": "Platform Used",
    "display_condition_field": "Display Condition Field",
    "count_of_display_conditions": "Count of Display Conditions",
    "success_action_field": "Success Action Field",
    "count_of_success_action": "Count of Success Action",
    "date_field_name": "Date Field Name",
    "row_name_field": "Row Name Field",
    "count_of_columns": "Count of Columns",
    "count_of_editable_column_names": "Count of Editable Column Names",
    "date_of_creation": "Date of Creation",
    "view_name": "View Name",
    "count_of_users_updated": "Count of Users Updated",
    "enrichment_records": "ENRICHMENT RECORDS",
    "location": "Location",
}


# Enum defining tracked events for analytics
# Used to capture user actions and system events for analytics
class SegmentEvents(Enum):
    LOGIN = "Login"
    LOGOUT = "Logout"

    VIEW_COMMISSIONS = "View Commissions"
    FREEZE_COMMISSIONS = "Lock Commissions"
    UNFREEZE_COMMISSIONS = "Unlock Commissions"
    MARKED_AS_PAID = "Marked as Paid"
    MARKED_AS_UNPAID = "Marked as Unpaid"
    VIEW_COMMISSION_PLAN = "View Commission Plan"
    CLONE_COMMISSION_PLAN = "Clone Commission Plan"
    DELETE_COMMISSION_PLAN = "Delete Commission Plan"
    CREATE_COMMISSION_PLAN = "Create Commission Plan"
    CREATE_CRITERIA = "Create Criteria"
    EDIT_CRITERIA = "Edit Criteria"
    EDIT_COMMISSION_PLAN = "Edit Commission Plan"
    PUBLISH_COMMISSION_PLAN = "Publish Commission Plan"
    CREATE_QUERY = "Create Query"
    UPDATE_QUERY = "Update Query"
    CLOSE_QUERY = "Close Query"
    BULK_UPDATE_COMMISSION_PLAN_MEMBERS = "Bulk update Commission Plan Members"

    # Custom Team Events
    ADD_CUSTOM_TEAM = "Add Custom Team"
    RENAME_CUSTOM_TEAM = "Rename Custom Team"
    REMOVE_CUSTOM_TEAM = "Remove Custom Team"

    # Reporting Hierarchy Events
    HIERARCHY_BULK_UPDATE = "Reporting Hierarchy Bulk Update"

    # Pod Relationship Events
    ADD_POD_RELATIONSHIP = "Add Pod Relationship"

    # Map Payee Events
    MAP_PAYEE = "Map Payee"

    # Quota Events
    ADD_QUOTA = "Add Quota"
    EDIT_QUOTA = "Edit Quota"

    # Draw Events
    ADD_DRAW = "Add Draw"
    EDIT_DRAW = "Edit Draw"

    # Databook Events
    VIEW_DATABOOK = "View Databook"
    CLONE_DATABOOK = "Clone Databook"
    DELETE_DATABOOK = "Delete Databook"
    ARCHIVE_DATABOOK = "Archive Databook"
    UNARCHIVE_DATABOOK = "Unarchive Databook"
    RENAME_DATABOOK = "Rename Databook"
    UPDATE_DATABOOK = "Update Databook"
    CREATE_DATABOOK = "Create Databook"

    # Datasheet Events
    CREATE_DATASHEET = "Create Datasheet"
    VISIT_DATASHEET = "Visit Datasheet"
    EDIT_DATASHEET = "Edit Datasheet"
    CLONE_DATASHEET = "Clone Datasheet"
    DELETE_DATASHEET = "Delete Datasheet"
    FILTER_DATASHEET = "Filter Datasheet"
    ADJUST_RECORD = "Adjust Record"
    EDIT_ADJUSTED_RECORD = "Edit Adjusted Record"
    REVERT_ADJUSTMENT = "Revert Adjustment"
    EXPORT_DATASHEET = "Export Datasheet"

    # Integration Events
    SLACK_CONNECTED = "Connect Slack"
    SLACK_DISCONNECTED = "Disconnect Slack"
    SLACK_COMMANDS = "Execute Slack Commands"
    SLACK_NOTIFICATION = "Sent Slack Notification"
    CONNECT_DOCUSIGN = "Connect Docusign"
    DISCONNECT_DOCUSIGN = "Disconnect Docusign"
    CREATE_CONTRACT = "Create Contract"
    EDIT_CONTRACT = "Edit Contract"
    VIEW_CONTRACT = "View Contract"
    SEND_ENVELOPE = "Send Envelope"

    # MS Teams
    MS_TEAMS_CONNECTED = "Connect MS Teams"
    MS_TEAMS_DISCONNECTED = "Disconnect MS Teams"
    MS_TEAMS_NOTIFICATION = "Sent MS Teams Notification"
    MS_TEAMS_SLASH_COMMANDS = "Execute Slash command in MS Teams"

    # Crystal and Configuration Events
    APPLY_CRYSTAL_PROJECTION = "Apply Crystal Projection"
    UPDATE_REVENUE_LEADER = "Update Revenue Leader"
    UPDATE_FX_RATES = "Update FX Rates"
    ADD_OBJECT = "Add Object"
    EDIT_OBJECT = "Edit Object"
    UPLOAD_DATA = "Upload Data"
    MANUAL_ADJUSTMENT = "Manual Adjustment"
    DELETE_MANUAL_ADJUSTMENT = "Delete Manual Adjustment"
    SENT_EMAIL_NOTIFICATION = "Sent Email Notification"

    ADD_USER = "Add User"
    USER_UPLOAD = "User Upload"
    EDIT_USER = "Edit User"
    SEND_INVITE = "Send Invite"
    BULK_INVITE = "Bulk Invite"
    RESEND_INVITE = "Resend Invite"
    UPDATE_PROFILE_FIELDS = "Update Profile Fields"
    UPDATE_QUERY_SETTING_CONFIG = "Update Query Setting Config"

    CREATE_APPROVAL_TEMPLATE = "Create approval template"
    UPDATE_APPROVAL_TEMPLATE = "Update approval template"
    DELETE_APPROVAL_TEMPLATE = "Delete approval template"
    CLONE_APPROVAL_TEMPLATE = "Clone approval template"
    CREATE_APPROVAL_INSTANCE = "Create approval instance"
    BULK_CREATE_APPROVAL_INSTANCE = "Bulk create approval instance"
    APPROVE_REQUEST = "Approve request"
    BULK_APPROVE_REQUEST = "Bulk approve request"
    REJECT_APPROVAL_REQUEST = "Reject approval request"
    BULK_REJECT_REQUEST = "Bulk reject request"
    WITHDRAW_STAGE_APPROVAL = "Withdraw stage approval"
    ADD_APPROVER_TO_STAGE = "Add approver to stage"
    CHANGE_STAGE_DUE_DATE = "Change stage due date"
    UPDATE_APPROVAL_CONFIG = "Update approval config"

    # Bulk upload custom object data
    BULK_MANAGE_DATA = "Custom object data upload"

    EXPORT_STATEMENTS_CSV = "Export Statements as Csv"
    EXPORT_STATEMENTS_PDF = "Export Statements as Pdf"

    VISIT_HELP_CENTER = "Visit Help Center"
    VISIT_RAISE_QUERY = "Visit Raise Query"
    VISIT_CONTACT_EVERSTAGE = "Visit Contact Everstage"
    SWITCH_PERIOD = "Switch Period"
    SWITCH_DISPLAY_CURRENCY = "Switch Display Currency"
    SWITCH_PAYEES = "Switch Payees"
    RAISE_QUERY_FROM_STATEMENTS = "Raise Query From Statements"
    VISIT_PAGE = "Visit Page"
    VIEW_PROFILE_CARD = "View Profile Card"
    VIEW_PLAN_DETAILS = "View Plan Details"
    VIEW_CRITERIA_TABLE_VIEW = "View Criteria Table View"
    SWITCH_QUOTA_CATEGORIES = "Switch Quota Categories"
    SWITCH_FISCAL_YEAR = "Switch Fiscal Year"
    VIEW_QUOTA = "View Quota"
    VIEW_LEADERBOARD = "View Leaderboard"
    QUICK_FILTER = "Quick Filter"
    USER_EXPORT = "User Export"
    IMPORT_NEW_USERS = "Import New Users"
    EDIT_EXISTING_USERS = "Edit Existing Users"
    FILTER_PARAM = "Filter Param"
    LOGIN_AS_USER = "Login As User"
    SEARCH_USAGE = "Search Usage"
    MANAGE_GROUP = "Manage Group"
    CLONE_GROUP = "Clone Group"
    DELETE_GROUP = "Delete Group"
    DOWNLOAD_TEMPLATE = "Download Template"
    EXPORTS_CSV = "Exports CSV"
    ARREARS_VIEW = "Arrears View"
    MANAGE_PAYEE = "Manage Payee"
    NEW_SIMULATOR = "New Simulator"
    NEW_SIMULATOR_ADD_PAYEE = "New Simulator - Add Payee"
    REMOVE_PAYEE = "Remove Payee"
    SHOWING_FOR = "Showing For"
    VIEW_FILTER = "View Filter"
    PAYEE_STATEMENT = "Payee Statement"

    # Settings Events
    CONNECT_MICROSOFT_TEAMS = "Connect Microsoft Teams"
    DISCONNECT_MICROSOFT_TEAMS = "Disconnect Microsoft Teams"
    APPROVALS = "Approvals"

    NEW_ROLES = "New Roles"
    EDIT_ROLES = "Edit Roles"
    CLONE_ROLES = "Clone Roles"

    CUSTOM_FIELDS = "Custom Fields"

    CUSTOMIZE_STATEMENT = "Customize Statement"

    REPORT_ENRICHMENT = "Report Enrichment"
    DELETE_REPORT_ENRICHMENT = "Delete Report Enrichment"
    BULK_DELETE_REPORT_ENRICHMENT = "Bulk Delete Report Enrichment"
    QUERY_ASSIGNMENT = "Query Assignment"
    COMMISSION_LOCK_DATE = "Commission Lock Date"

    GET_DATA_FROM = "Get Data From"
    ADD_TRANSFORMATION = "Add Transformation"
    ADD_FORMULA_FIELD = "Add Formula Field"
    EXPORT_CSV = "Export CSV"
    CUSTOMIZE_COLUMNS = "Customize Columns"
    PIVOT_MODE = "Pivot Mode"
    ADJUSTMENT_TYPE = "Adjustment Type"
    COUNT_OF_SAVED_VIEWS_PER_SHEET = "Count of Saved Views per Sheet"
    GET_JOIN_TYPE = "Get Join Type from DataSheet transformation"
    GET_AGGREGATION_TYPE = "Get Aggregation Type from the Data Sheet transformation"
    COUNT_OF_TRANSFORMATIONS = "Count of Transformations per Data Sheet"
    COUNT_OF_FIELDS = "Count of Fields per Data Sheet"
    COUNT_OF_FORMULA_FIELDS = (
        "Count of Formula Fields per Data Sheet and their Data Type"
    )
    APPLY_FILTER = "Apply Filter"
    SEARCH_PAYOUTS = "Search Payouts"
    PAGINATION = "Pagination"
    CREATE_NEW_PERMISSSION_SET = "Create New Permission Set"
    CREATE_VIEW = "Create View"
    REJECT_REQUEST_OF_SUB_REQUEST = "Reject request of sub request"
    APPROVE_REQUEST_OF_SUB_REQUEST = "Approve request of sub request"
    DECLINE_SUB_REQUEST = "Decline sub request"
    ACCEPT_SUB_REQUEST = "Accept sub request"
    DELETE_INSTANCE = "Delete Instance"
    REEVALUATE_APPROVALS = "Reevaluate Approvals"

    # Commission Plan RBAC
    ADD_SHARED_PLAN_USERS = "Add Shared Plan Users and Groups"
    SHARED_PLAN_MEMBER_ACTION = "Shared Plan Member Action"

    # Canvas
    RENAME_DATASHEET_COLUMN_HYPERLINK_CLICKED = "Rename column hyperlink clicked"

    # Historical payroll and custom field management events
    HISTORICAL_PAYROLL_CUSTOM_FIELD_EDIT = (
        "Edit historical payroll and custom field entries"
    )
    HISTORICAL_PAYROLL_CUSTOM_FIELD_SPLIT = (
        "Split historical payroll and custom field entries"
    )
    HISTORICAL_PAYROLL_CUSTOM_FIELD_DELETE = (
        "Delete historical payroll and custom field entries"
    )


class CommissionViewType(Enum):
    EARNED_COMMISSIONS = "earned_commissions"
    DEFERRED_COMMISSIONS = "deferred_commissions"
    PREV_DEFERRED_COMMISSIONS = "prev_deferred_commissions"
    CURRENT_PERIOD_PAYOUT = "current_period_payout"


class ETL_ACTIVITY(Enum):
    CONNECTOR_E2E_SYNC = "End to End Sync"
    CONNECTOR_UPSTREAM_SYNC = "Upstream Sync"
    REFRESH_DATABOOK = "Refresh Databooks"
    COMMISSION_CALCULATION = "Commission calculation"
    FORECAST_CALCULATION = "Forecast calculation"
    REPORT_ETL = "Report ETL"
    MIGRATE_INTER_DATA = "Migrate Inter Object Data"
    SETTLEMENT_CALCULATION = "Settlement calculation"
    GENERATE_DATASHEET = "GenerateDatasheetData"
    QUOTE_REPORT_SYNC = "Quote Report Sync"


class ETLTriggers(Enum):
    SCHEDULED_RUN = "scheduled_run"  # Scheduled execution via cron jobs
    MANUAL_REQUEST = "manual_request"  # User-initiated sync via UI requests
    REALTIME_EXTRACTION = "realtime_extraction"  # Polling, Webhooks, Streaming etc.

    @classmethod
    def choices(cls) -> list[tuple[str, str]]:
        return [(item.name, item.value) for item in cls]


class PAYEE_FILTER(Enum):
    ALL = "all"
    SELF = "self"
    SELF_AND_REPORTEES = "self_and_reportees"


class DATABOOK_SYNC_STRATEGY(Enum):
    """
    Available ways to run a databook ETL - use the value of the enum to store in the database
    """

    MEMORY = "memory"
    SNOWFLAKE = "snowflake"


class CURRENCY_TYPE(Enum):
    BASE = "base"
    PAYEE = "payee"
    ORIGINAL = "original"


class PERMISSION_TARGET_TYPE(Enum):
    """
    Datasheet and custom object permission target types
    """

    USER = "user"
    USER_GROUP = "user_group"


PROFILE_CARD_DEFAULT_FIELDS_MAP = {
    "reporting_manager": "REPORTING_MANAGER",
    "employment_country": "COUNTRY",
    "pay_currency": "PAYOUT_CURRENCY",
    "payout_frequency": "PAYOUT_FREQUENCY",
    "fixed_pay": "FIXED_PAY",
    "variable_pay": "VARIABLE_PAY",
    "employee_id": "EMPLOYEE_ID",
    "bcr": "BCR",
}

APPROVALS_AUTO_APPROVE_TASKS_TIME = {"hour": 0, "minute": 0, "second": 0}

APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TIME = {"hour": 10, "minute": 0, "second": 0}

COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD_TIME = {"hour": 9, "minute": 0, "second": 0}

VALID_NOTIFICATION_FREEZE_DAYS = [5, 10, 15, 20, 25]

SNAPSHOT_TABLE_ADDITIONAL_COLUMNS = {
    "quotaErosion": "quota_erosion",
    "tierName": "tier_name",
}

SUB_APPROVAL_REQUEST_STATUS_MAP = {
    "requested": "requested",
    "accepted": "approved",
    "declined": "rejected",
    "withdrawn": "withdrawn",
    "aborted": "cancelled",
    "revoked": "revoked",
}

USERGROUPMEMBERS_SYNC_TIME = {"hour": 3, "minute": 30, "second": 0}

DOCUSIGN_CONTRACTS_SYNC_TIME = {"hour": 3, "minute": 0, "second": 0}


class RBACPlan(Enum):
    ALL_PLANS = "ALL_PLANS"
    SHARED_PLANS = "SHARED_PLANS"
    VIEW = "can_view"
    EDIT = "can_edit"
    DELETE = "can_delete"


class RBAC(Enum):
    UI_PERMISSION = "Choose what action users can perform in "
    DATA_PERMISSION = "Choose what data users can see in "
    ALL_DATA = "ALL_DATA"
    INDIVIDUAL_AND_TEAM_DATA = "INDIVIDUAL_AND_TEAM_DATA"
    INDIVIDUAL_DATA = "INDIVIDUAL_DATA"
    ALLOW_ALL_IMPERSONATION = "ALL"


class RBACComponent(Enum):
    DASHBOARD = "dashboard"
    DATABOOKS = "databooks"
    COMMISSION_PLANS = "commission_plans"
    COMMISSION_FEED = "commission_feed"
    PAYOUTS_STATEMENTS = "payouts_statements"
    CRYSTAL = "crystal"
    QUOTAS_DRAWS = "quotas_draws"
    MANAGE_USERS = "manage_users"
    QUERIES = "queries"
    SETTINGS = "settings"
    ADVANCED_PERMISSION = "advanced_permissions"
    GLOBAL_SEARCH = "global_search"
    QUOTES = "quotes"


class RbacPermissions(Enum):
    VIEW_DASHBOARD = "view:dashboard"
    MANAGE_DASHBOARD = "manage:dashboard"
    DELETE_DASHBOARD = "delete:dashboard"
    VIEW_ADMINDASHBOARD = "view:admindashboard"
    VIEW_PAYEEDASHBOARD = "view:payeedashboard"
    VIEW_DATABOOK = "view:databook"
    MANAGE_DATABOOK = "manage:databook"
    MANAGE_DATASHEETPERMISSIONS = "manage:datasheetpermissions"
    MANAGE_DATASHEETADJUSTMENTS = "manage:datasheetadjustments"
    EXPORT_DATASHEET = "export:datasheet"
    DELETE_DATASHEET = "delete:datasheet"
    VIEW_COMMISSIONPLAN = "view:commissionplan"
    EDIT_COMMISSIONPLAN = "edit:commissionplan"
    CREATE_COMMISSIONPLAN = "create:commissionplan"
    DELETE_COMMISSIONPLAN = "delete:commissionplan"
    VIEW_COMMISSIONFEED = "view:commissionfeed"
    MANAGE_COMMISSIONFEED = "manage:commissionfeed"
    VIEW_PAYOUTS = "view:payouts"
    MANAGE_PAYOUTS = "manage:payouts"
    REGISTER_PAYOUTS = "register:payouts"
    INVALIDATE_PAYOUTS = "invalidate:payouts"
    VIEW_REQUESTAPPROVALS = "view:requestapprovals"
    VIEW_APPROVALS = "view:approvals"
    EXPORT_PAYOUTS = "export:payouts"
    VIEW_STATEMENTS = "view:statements"
    EXPORT_STATEMENT = "export:statement"
    VIEW_HIDDENCRITERIA = "view:hiddencriteria"
    MANAGE_COMMISSIONADJUSTMENT = "manage:commissionadjustment"
    VIEW_PAYOUTVALUEOTHERS = "view:payoutvalueothers"
    MANAGE_CRYSTAL = "manage:crystal"
    VIEW_QUOTAS = "view:quotas"
    MANAGE_QUOTAS = "manage:quotas"
    MANAGE_QUOTA_SETTINGS = "manage:quotasettings"
    VIEW_HIDDENQUOTAS = "view:hiddenquotas"
    VIEW_DRAWS = "view:draws"
    MANAGE_DRAWS = "manage:draws"
    VIEW_USERS = "view:users"
    DELETE_USERS = "delete:users"
    MANAGE_USERS = "manage:users"
    VIEW_PAYROLL = "view:payroll"
    EDIT_PAYROLL = "edit:payroll"
    EXPORT_USERS = "export:users"
    ALLOW_IMPERSONATION = "allow:impersonation"
    VIEW_TEAMS = "view:teams"
    MANAGE_USERGROUPS = "manage:usergroups"
    MANAGE_USERCUSTOMFIELD = "manage:usercustomfield"
    MANAGE_PAYROLL_ENTRIES_USER = "manage:payrollentriesusers"
    VIEW_QUERIES = "view:queries"
    CREATE_QUERIES = "create:queries"
    EDIT_QUERIES = "edit:queries"
    DELETE_QUERIES = "delete:queries"
    MANAGE_CONFIG = "manage:config"
    MANAGE_CONTRACTS = "manage:contracts"
    MANAGE_DATASETTINGS = "manage:datasettings"
    MANAGE_ROLES = "manage:roles"
    MANAGE_OWNDATA = "manage:owndata"
    MANAGE_REPORTENRICH = "manage:reportenrich"
    VIEW_EVERSTAGE = "view:everstage"
    MANAGE_ADMINUI = "manage:adminui"
    MANAGE_ALLADMINS = "manage:alladmins"
    MANAGE_ETLSTATUS = "manage:etl-status"
    MANAGE_ADMINUI_AUTH0_USERS = "manage:auth0-users"
    MANAGE_INTEGRATIONS = "manage:integrations"
    MANAGE_CUSTOMERS = "manage:customers"
    MANAGE_ANALYTICS = "manage:analytics"
    MANAGE_ACCOUNTNOTIFICATIONS = "manage:accountnotifications"
    USE_GENIE = "use:genie"
    MANAGE_AGENT_STUDIO = "manage:agentstudio"
    MANAGE_AGENT_WORKBENCH = "manage:agentworkbench"
    MANAGE_QA_REPORTS = "manage:qareports"
    MANAGE_AUTOGEN_DESCRIPTION = "manage:autogendescription"
    MANAGE_COMMISSION_FORMULA_GENERATION = "manage:commissionformulageneration"
    MANAGE_DATASHEET_AI_GENERATION = "manage:datasheetaigeneration"
    MANAGE_APPROVAL_WORKFLOWS = "manage:approvalworkflows"
    PUBLISH_COMMISSION_PLAN = "publish:commissionplan"
    VIEW_GLOBAL_SEARCH = "view:globalsearch"
    # LearnUpon Permissions
    VIEW_ADMIN_CONTENT = "view:admincontent"
    VIEW_PAYEE_CONTENT = "view:payeecontent"
    VIEW_SYNC_DETAILS = "view:syncdetails"
    # TQM Permissions
    VIEW_TERRITORY_PLANS = "view:territoryplans"
    EXPLORE_TERRITORY_PLANS = "explore:territoryplans"
    EDIT_TERRITORY_PLANS = "edit:territoryplans"
    # TSAR DesktopWeb Permissions
    TSAR_DESKTOPWEB_ADMINUI_READ = "tsar-webapp:admin-ui:read"
    TSAR_DESKTOPWEB_ADMINUI_MODIFY = "tsar-webapp:admin-ui:modify"
    TSAR_DESKTOPWEB_ADMINUI_REVOKE = "tsar-webapp:admin-ui:revoke"
    TSAR_DESKTOPWEB_FRESHSERVICE_APPROVE = "tsar-webapp:freshservice:approve"
    TSAR_DESKTOPWEB_ADDON_ADMINUI_FLEXIBLE_DURATION = (
        "tsar-webapp-addon:admin-ui:flexible-duration"
    )
    # TSAR Postgresql Permissions
    TSAR_POSTGRESQL_ADMINUI_READ = "tsar-postgresql:admin-ui:read"
    TSAR_POSTGRESQL_ADMINUI_REVOKE = "tsar-postgresql:admin-ui:revoke"
    TSAR_POSTGRESQL_ADDON_ADMINUI_FLEXIBLE_DURATION = (
        "tsar-postgresql-addon:admin-ui:flexible-duration"
    )
    TSAR_POSTGRESQL_ALL_ROLES_FRESHSERVICE_APPROVE = (
        "tsar-postgresql-all-roles:freshservice:approve"
    )
    TSAR_POSTGRESQL_SENSITIVE_READER_ROLE_FRESHSERVICE_APPROVE_SELF = (
        "tsar-postgresql-sensitive-reader-role:freshservice:approve-self"
    )
    TSAR_POSTGRESQL_ALL_ROLES_ADMINUI_MODIFY = (
        "tsar-postgresql-all-roles:admin-ui:modify"
    )
    TSAR_POSTGRESQL_SENSITIVE_READER_ROLE_ADMINUI_MODIFY = (
        "tsar-postgresql-sensitive-reader-role:admin-ui:modify"
    )
    TSAR_POSTGRESQL_SENSITIVE_READER_ROLE_ADMINUI_MODIFY_SELF = (
        "tsar-postgresql-sensitive-reader-role:admin-ui:modify-self"
    )
    TSAR_POSTGRESQL_USERS_MANAGE = "tsar-postgresql-users:*:manage"

    # TSAR Snowflake Permissions
    TSAR_SNOWFLAKE_ADMINUI_READ = "tsar-snowflake:admin-ui:read"
    TSAR_SNOWFLAKE_ADMINUI_REVOKE = "tsar-snowflake:admin-ui:revoke"
    TSAR_SNOWFLAKE_ADDON_ADMINUI_FLEXIBLE_DURATION = (
        "tsar-snowflake-addon:admin-ui:flexible-duration"
    )
    TSAR_SNOWFLAKE_ALL_ROLES_FRESHSERVICE_APPROVE = (
        "tsar-snowflake-all-roles:freshservice:approve"
    )
    TSAR_SNOWFLAKE_SENSITIVE_READER_ROLE_FRESHSERVICE_APPROVE_SELF = (
        "tsar-snowflake-sensitive-reader-role:freshservice:approve-self"
    )
    TSAR_SNOWFLAKE_ALL_ROLES_ADMINUI_MODIFY = "tsar-snowflake-all-roles:admin-ui:modify"
    TSAR_SNOWFLAKE_SENSITIVE_READER_ROLE_ADMINUI_MODIFY = (
        "tsar-snowflake-sensitive-reader-role:admin-ui:modify"
    )
    TSAR_SNOWFLAKE_SENSITIVE_READER_ROLE_ADMINUI_MODIFY_SELF = (
        "tsar-snowflake-sensitive-reader-role:admin-ui:modify-self"
    )
    TSAR_SNOWFLAKE_USERS_MANAGE = "tsar-snowflake-users:*:manage"
    VIEW_CPQ_DASHBOARD = "view:cpqdashboard"
    MANAGE_CPQ_DASHBOARD = "manage:cpqdashboard"
    VIEW_QUOTES = "view:quotes"
    EDIT_QUOTES = "edit:quotes"
    CREATE_QUOTES = "create:quotes"
    DELETE_QUOTES = "delete:quotes"
    MANAGE_CPQ_SETTINGS = "manage:cpqsettings"
    MANAGE_PRODUCTS_AND_PRICES = "manage:productsandprices"
    MANAGE_CPQ_USERS_AND_GROUPS = "manage:cpqusersandgroups"


class REPORT_ETL_COMMISSION(Enum):
    BATCH_SIZE = 500


class SETTLEMENT_SNAPSHOT_ETL(Enum):
    BATCH_SIZE = 500


APPROVALS_PENDING_REQUESTS_NOTIFY_TIME = {"hour": 10, "minute": 0, "second": 0}

default_localization_map = {
    "en": {
        "QUOTA": "Quota",
        "QUOTAS": "Quotas",
        "QUOTA_EROSION": "Quota Erosion",
        "COMMISSION": "Commission",
        "COMMISSIONS": "Commissions",
        "PAYOUT": "Payout",
        "PAYOUTS": "Payouts",
        "ADJUSTMENT": "Adjustment",
        "ADJUSTMENTS": "Adjustments",
        "ARREARS": "Arrears",
        "ARREAR": "Arrear",
        "DEFERRED": "Deferred",
        "EARNED": "Earned",
        "ON_TARGET_VARIABLE_PAY": "On-Target Variable Pay",
    },
    "es": {
        "QUOTA": "Cuota",
        "QUOTAS": "Cuotas",
        "QUOTA_EROSION": "Erosión de Cuota",
        "COMMISSION": "Comisión",
        "COMMISSIONS": "Comisiones",
        "PAYOUT": "Pago",
        "PAYOUTS": "Pagos",
        "ADJUSTMENT": "Ajuste",
        "ADJUSTMENTS": "Ajustes",
        "ARREARS": "Atrasos",
        "ARREAR": "Atraso",
        "DEFERRED": "Diferido",
        "EARNED": "Ganado",
        "ON_TARGET_VARIABLE_PAY": "Pago Variable Objetivo",
    },
    "pt": {
        "QUOTA": "Meta",
        "QUOTAS": "Metas",
        "QUOTA_EROSION": "Erosão de Meta",
        "COMMISSION": "Comissão",
        "COMMISSIONS": "Comissões",
        "PAYOUT": "Pagamento",
        "PAYOUTS": "Pagamentos",
        "ADJUSTMENT": "Ajuste",
        "ADJUSTMENTS": "Ajustes",
        "ARREARS": "Atrasos",
        "ARREAR": "Atraso",
        "DEFERRED": "Diferido",
        "EARNED": "Ganho",
        "ON_TARGET_VARIABLE_PAY": "Remuneração Variável-Alvo",
    },
}


class CommCalcLevels(Enum):
    L1 = "L1"
    L2 = "L2"


SNOWFLAKE_IN_CLAUSE_MAX_LIMIT = 15000


user_properties_list = [
    {
        "system_name": "first_name",
        "display_name": "First Name",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "last_name",
        "display_name": "Last Name",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "employee_email_id",
        "display_name": "Email",
        "data_type_id": 12,
        "field_type": "Email",
        "show_for_single_valued": True,
    },
    {
        "system_name": "joining_date",
        "display_name": "Joining Date",
        "data_type_id": 2,
        "field_type": "Date",
        "show_for_single_valued": True,
    },
    {
        "system_name": "employee_id",
        "display_name": "Employee ID",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "designation",
        "display_name": "Designation",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "country_name",
        "display_name": "Employment Country",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "reporting_manager_email_id",
        "display_name": "Reporting Manager Email",
        "data_type_id": 12,
        "field_type": "Email",
        "show_for_single_valued": True,
    },
    {
        "system_name": "pay_currency",
        "display_name": "Payout Currency",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "payout_frequency",
        "display_name": "Payout Frequency",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": True,
    },
    {
        "system_name": "fixed_pay",
        "display_name": "Base Pay",
        "data_type_id": 1,
        "field_type": "Integer",
        "show_for_single_valued": True,
    },
    {
        "system_name": "variable_pay",
        "display_name": "Variable Pay",
        "data_type_id": 1,
        "field_type": "Integer",
        "show_for_single_valued": True,
    },
    {
        "system_name": "exit_date",
        "display_name": "Exit Date",
        "data_type_id": 2,
        "field_type": "Date",
        "show_for_single_valued": True,
    },
    {
        "system_name": "associated_plan_name",
        "display_name": "Associated Plans (by plan name)",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": False,
    },
    {
        "system_name": "associated_plan_id",
        "display_name": "Associated Plans (by plan id)",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": False,
    },
    {
        "system_name": "active_plan_name",
        "display_name": "Associated Active Plans (by plan name)",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": False,
    },
    {
        "system_name": "active_plan_id",
        "display_name": "Associated Active Plans (by plan id)",
        "data_type_id": 4,
        "field_type": "String",
        "show_for_single_valued": False,
    },
]


class Notification(Enum):

    COMMISSION_NOTIFICATION = {
        "name": "COMMISSION_NOTIFICATION",
        "label": "$COMMISSION and $QUOTA attainment status",
        "show_to_user": True,
        "managed_by_payee": True,
        "is_event_based": False,
        "permissions": [
            RbacPermissions.VIEW_STATEMENTS.value,
        ],
        "frequency": Freq.DAILY.value,
        "allowed_frequencies": [
            Freq.BIWEEKLY.value,
            Freq.DAILY.value,
            Freq.WEEKLY.value,
        ],
        "slack": {
            "task": Task.SLACK_COMMISSION_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_NOTIFICATION.name,
        },
        "category": TaskCategory.COMMISSIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.COMMISSIONS.name],
    }

    MONTHLY_COMMISSION_NOTIFICATION = {
        "name": "MONTHLY_COMMISSION_NOTIFICATION",
        "label": "Monthly update on $COMMISSIONS and $QUOTA attainment status along with YTD $PAYOUT_lc details",
        "show_to_user": False,
        "managed_by_payee": True,
        "is_event_based": False,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": Freq.MONTHLY.value,
        "slack": {
            "task": Task.SLACK_MONTHLY_COMMISSION_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_MONTHLY_COMMISSION_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_MONTHLY_COMMISSION_NOTIFICATION.name,
        },
        "category": TaskCategory.COMMISSIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.COMMISSIONS.name],
    }

    COMMISSION_REMINDER_NOTIFICATION = {
        "name": "COMMISSION_REMINDER_NOTIFICATION",
        "label": "Remind me on the first day of each month",
        "admin_label": "Remind payees on the first day of each month",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": False,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": Freq.MONTHLY.value,
        "slack": {
            "task": Task.SLACK_COMMISSION_REMINDER_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_REMINDER_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION.name,
        },
        "category": TaskCategory.STATEMENT_REMINDER.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[
            TaskCategory.STATEMENT_REMINDER.name
        ],
    }

    COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD = {
        "name": "COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD",
        "label": "Remind me 2 days before the lock date",
        "admin_label": "Remind payees 2 days before the lock date",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": False,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": Freq.CUSTOM.value,
        "slack": {
            "task": Task.SLACK_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD.name,
        },
        "category": TaskCategory.STATEMENT_REMINDER.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[
            TaskCategory.STATEMENT_REMINDER.name
        ],
    }

    COMMISSION_MILESTONE_NOTIFICATION = {
        "name": "COMMISSION_MILESTONE_NOTIFICATION",
        "label": "When I hit milestones in $COMMISSIONS_lc (25%,50%, 75%, 100%)",
        "admin_label": "When a payee hit milestones in $COMMISSIONS_lc (25%,50%, 75%, 100%)",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [
            RbacPermissions.VIEW_STATEMENTS.value,
        ],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_COMMISSION_ATTAINED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_ATTAINED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_ATTAINED_NOTIFICATION.name,
        },
        "category": TaskCategory.MILESTONES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.MILESTONES.name],
    }

    QUOTA_MILESTONE_NOTIFICATION = {
        "name": "QUOTA_MILESTONE_NOTIFICATION",
        "label": "When I hit milestones in $QUOTA_lc attainment (25%,50%, 75%, 100%)",
        "admin_label": "When a payee hit milestones in $QUOTA_lc attainment (25%,50%, 75%, 100%)",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [RbacPermissions.VIEW_STATEMENTS.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_QUOTA_ATTAINED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_QUOTA_ATTAINED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_QUOTA_ATTAINED_NOTIFICATION.name,
        },
        "category": TaskCategory.MILESTONES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.MILESTONES.name],
    }

    STATEMENTS_LOCKED_NOTIFICATION = {
        "name": "STATEMENTS_LOCKED_NOTIFICATION",
        "label": "When the Statement is locked",
        "managed_by_payee": True,
        "show_to_user": False,
        "allowed_frequencies": None,
        "enabled_on_connect": True,
        "is_event_based": True,
        "permissions": [],
        "frequency": Freq.MONTHLY.value,
        "slack": {
            "task": Task.SLACK_STATEMENT_LOCKED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_STATEMENT_LOCKED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_STATEMENT_LOCKED_NOTIFICATION.name,
        },
        "category": TaskCategory.COMMISSIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.COMMISSIONS.name],
    }

    PAYOUT_INITIATED_NOTIFICATION = {
        "name": "PAYOUT_INITIATED_NOTIFICATION",
        "label": "When payment is registered",
        "show_to_user": False,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_PAYOUT_INITIATED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_PAYOUT_INITIATED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_PAYOUT_INITIATED_NOTIFICATION.name,
        },
        "category": TaskCategory.COMMISSIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.COMMISSIONS.name],
    }

    COMMISSION_FROZEN_NOTIFICATION = {
        "name": "COMMISSION_FROZEN_NOTIFICATION",
        "label": "When Statements are locked",
        "show_to_user": True,
        "managed_by_payee": False,
        "allowed_frequencies": None,
        "is_event_based": True,
        "data_permission": RBACComponent.PAYOUTS_STATEMENTS.value,
        "permissions": [RbacPermissions.VIEW_PAYOUTS.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_COMMISSION_FROZEN_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_FROZEN_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_FROZEN_NOTIFICATION.name,
        },
        "category": TaskCategory.OPERATIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OPERATIONS.name],
    }

    COMMISSION_UNFROZEN_NOTIFICATION = {
        "name": "COMMISSION_UNFROZEN_NOTIFICATION",
        "label": "When Statements are unlocked",
        "show_to_user": True,
        "managed_by_payee": False,
        "allowed_frequencies": None,
        "is_event_based": True,
        "data_permission": RBACComponent.PAYOUTS_STATEMENTS.value,
        "permissions": [RbacPermissions.VIEW_PAYOUTS.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_COMMISSION_UNFROZEN_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_UNFROZEN_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_UNFROZEN_NOTIFICATION.name,
        },
        "category": TaskCategory.OPERATIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OPERATIONS.name],
    }

    COMMISSION_MARKED_AS_PAID_NOTIFICATION = {
        "name": "COMMISSION_MARKED_AS_PAID_NOTIFICATION",
        "label": "When payment is registered",
        "show_to_user": True,
        "managed_by_payee": False,
        "allowed_frequencies": None,
        "is_event_based": True,
        "data_permission": RBACComponent.PAYOUTS_STATEMENTS.value,
        "permissions": [RbacPermissions.VIEW_PAYOUTS.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_COMMISSION_MARKED_AS_PAID_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_MARKED_AS_PAID_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_MARKED_AS_PAID_NOTIFICATION.name,
        },
        "category": TaskCategory.OPERATIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OPERATIONS.name],
    }

    COMMISSION_MARKED_AS_UNPAID_NOTIFICATION = {
        "name": "COMMISSION_MARKED_AS_UNPAID_NOTIFICATION",
        "label": "When payment is invalidated",
        "show_to_user": True,
        "managed_by_payee": False,
        "allowed_frequencies": None,
        "is_event_based": True,
        "data_permission": RBACComponent.PAYOUTS_STATEMENTS.value,
        "permissions": [RbacPermissions.VIEW_PAYOUTS.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_COMMISSION_MARKED_AS_UNPAID_NOTIFICATION.name,
        },
        "category": TaskCategory.OPERATIONS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OPERATIONS.name],
    }

    QUERY_STATUS_UPDATE_NOTIFICATION = {
        "name": "QUERY_STATUS_UPDATE_NOTIFICATION",
        "label": "Daily update of queries status (open, unassigned)",
        "show_to_user": True,
        "managed_by_payee": False,
        "allowed_frequencies": None,
        "is_event_based": False,
        "data_permission": RBACComponent.QUERIES.value,
        "permissions": [RbacPermissions.VIEW_QUERIES.value],
        "frequency": Freq.DAILY.value,
        "slack": {
            "task": Task.SLACK_QUERY_STATUS_UPDATE_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_QUERY_STATUS_UPDATE_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_QUERY_STATUS_UPDATE_NOTIFICATION.name,
        },
        "category": TaskCategory.QUERIES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.QUERIES.name],
    }

    QUERY_CATEGORY_CHANGE_NOTIFICATION = {
        "name": "QUERY_CATEGORY_CHANGE_NOTIFICATION",
        "label": "When query category is updated",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [RbacPermissions.VIEW_QUERIES.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_CATEGORY_CHANGE_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_CATEGORY_CHANGE_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_CATEGORY_CHANGE_NOTIFICATION.name,
        },
        "category": TaskCategory.QUERIES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.QUERIES.name],
    }

    QUERY_ASSIGNED_NOTIFICATION = {
        "name": "QUERY_ASSIGNED_NOTIFICATION",
        "label": "When a query is assigned to me",
        "admin_label": "When a query is assigned to a user",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [RbacPermissions.VIEW_QUERIES.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_QUERY_ASSIGNED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_QUERY_ASSIGNED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_QUERY_ASSIGNED_NOTIFICATION.name,
        },
        "category": TaskCategory.QUERIES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.QUERIES.name],
    }

    QUERY_COMMENT_ADDED_NOTIFICATION = {
        "name": "QUERY_COMMENT_ADDED_NOTIFICATION",
        "label": "When a comment is added to a query that I am part of",
        "admin_label": "When a comment is added to a query involving a user",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [RbacPermissions.VIEW_QUERIES.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_QUERY_COMMENT_IS_ADDED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_QUERY_COMMENT_IS_ADDED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_QUERY_COMMENT_IS_ADDED_NOTIFICATION.name,
        },
        "category": TaskCategory.QUERIES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.QUERIES.name],
    }

    CC_IN_QUERY_NOTIFICATION = {
        "name": "CC_IN_QUERY_NOTIFICATION",
        "label": "When I am added to the thread in a Query",
        "admin_label": "When a user is added to a query thread",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [RbacPermissions.VIEW_QUERIES.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_CC_IN_QUERY_IS_ADDED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_CC_IN_QUERY_IS_ADDED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_CC_IN_QUERY_IS_ADDED_NOTIFICATION.name,
        },
        "category": TaskCategory.QUERIES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.QUERIES.name],
    }

    QUERY_CLOSED_NOTIFICATION = {
        "name": "QUERY_CLOSED_NOTIFICATION",
        "label": "When a query I am part of is resolved",
        "admin_label": "When a query involving a user is resolved",
        "show_to_user": True,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [RbacPermissions.VIEW_QUERIES.value],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_QUERY_CLOSED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_QUERY_CLOSED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_QUERY_CLOSED_NOTIFICATION.name,
        },
        "category": TaskCategory.QUERIES.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.QUERIES.name],
    }

    PLAN_UPDATED_NOTIFICATION = {
        "name": "PLAN_UPDATED_NOTIFICATION",
        "label": "When I am added to a $COMMISSION_lc plan",
        "admin_label": "When payees are added to a $COMMISSION_lc plan",
        "show_to_user": False,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_PLAN_UPDATED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_PLAN_UPDATED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_PLAN_UPDATED_NOTIFICATION.name,
        },
        "category": TaskCategory.OTHERS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OTHERS.name],
    }

    REPORTING_MANAGER_UPDATED_NOTIFICATION = {
        "name": "REPORTING_MANAGER_UPDATED_NOTIFICATION",
        "label": "When my reporting manager changes",
        "admin_label": "When a payee's reporting manager changes",
        "show_to_user": False,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_REPORTING_MANAGER_UPDATED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_REPORTING_MANAGER_UPDATED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_REPORTING_MANAGER_UPDATED_NOTIFICATION.name,
        },
        "category": TaskCategory.OTHERS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OTHERS.name],
    }

    TEAM_UPDATED_NOTIFICATION = {
        "name": "TEAM_UPDATED_NOTIFICATION",
        "label": "When my team is updated",
        "admin_label": "When the payee's team is updated",
        "show_to_user": False,
        "managed_by_payee": True,
        "allowed_frequencies": None,
        "is_event_based": True,
        "permissions": [],
        "frequency": None,
        "slack": {
            "task": Task.SLACK_TEAM_UPDATED_NOTIFICATION.name,
        },
        "email": {
            "task": Task.EMAIL_TEAM_UPDATED_NOTIFICATION.name,
        },
        "ms_teams": {
            "task": Task.MS_TEAMS_TEAM_UPDATED_NOTIFICATION.name,
        },
        "category": TaskCategory.OTHERS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.OTHERS.name],
    }

    NEW_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "NEW_APPROVAL_REQUEST_NOTIFICATION",
        "label": "Notify when there’s a new approval request",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": True,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": None,
        "slack": {"task": Task.SLACK_NEW_APPROVAL_REQUEST_NOTIFICATION.name},
        "email": {"task": Task.EMAIL_NEW_APPROVAL_REQUEST_NOTIFICATION.name},
        "ms_teams": {"task": Task.MSTEAMS_NEW_APPROVAL_REQUEST_NOTIFICATION.name},
        "category": TaskCategory.APPROVALS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.APPROVALS.name],
    }

    EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "label": "Send a daily reminder for pending approval requests",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": False,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": Freq.DAILY.value,
        "slack": {
            "task": Task.SLACK_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        },
        "email": {
            "task": Task.EMAIL_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        },
        "ms_teams": {
            "task": Task.MSTEAMS_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        },
        "category": TaskCategory.APPROVALS.value,
        "category_localized": TASK_CATEGORY_LOCALIZED[TaskCategory.APPROVALS.name],
    }


class CpqNotification(Enum):

    QUOTE_UPDATED_NOTIFICATION = {
        "name": "QUOTE_UPDATED_NOTIFICATION",
        "label": "Notify rep when their quote is updated",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": True,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": None,
        "slack": {"task": Task.CPQ_SLACK_QUOTE_UPDATED_NOTIFICATION.value},
        "email": {"task": Task.CPQ_EMAIL_QUOTE_UPDATED_NOTIFICATION.value},
        "ms_teams": {"task": Task.CPQ_MS_TEAMS_QUOTE_UPDATED_NOTIFICATION.value},
        "category": TaskCategory.QUOTES.value,
        "is_slack_channel_notification": False,
    }

    QUOTE_SIGNED_NOTIFICATION = {
        "name": "QUOTE_SIGNED_NOTIFICATION",
        "label": "Notify rep when recipients sign a quote",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": True,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": None,
        "slack": {"task": Task.CPQ_SLACK_QUOTE_SIGNED_NOTIFICATION.value},
        "email": {"task": Task.CPQ_EMAIL_QUOTE_SIGNED_NOTIFICATION.value},
        "ms_teams": {"task": Task.CPQ_MS_TEAMS_QUOTE_SIGNED_NOTIFICATION.value},
        "category": TaskCategory.QUOTES.value,
        "is_slack_channel_notification": True,
    }

    QUOTE_WON_NOTIFICATION = {
        "name": "QUOTE_WON_NOTIFICATION",
        "label": "Notify rep when quote is won",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": True,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": None,
        "slack": {"task": Task.CPQ_SLACK_QUOTE_WON_NOTIFICATION.value},
        "email": {"task": Task.CPQ_EMAIL_QUOTE_WON_NOTIFICATION.value},
        "ms_teams": {"task": Task.CPQ_MS_TEAMS_QUOTE_WON_NOTIFICATION.value},
        "category": TaskCategory.QUOTES.value,
        "is_slack_channel_notification": True,
    }

    CPQ_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = {
        "name": "CPQ_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
        "label": "Send daily reminder to users about approvals pending on them",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": False,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": Freq.DAILY.value,
        "slack": {
            "task": Task.CPQ_SLACK_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        },
        "email": {
            "task": Task.CPQ_EMAIL_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        },
        "ms_teams": {
            "task": Task.CPQ_MS_TEAMS_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        },
        "category": TaskCategory.CPQ_APPROVALS.value,
        "is_slack_channel_notification": False,
    }

    CPQ_NEW_QUOTE_APPROVALS_NOTIFICATION = {
        "name": "CPQ_NEW_QUOTE_APPROVALS_NOTIFICATION",
        "label": "Notify user when their approval is requested for a quote",
        "show_to_user": False,
        "managed_by_payee": False,
        "is_event_based": True,
        "allowed_frequencies": None,
        "permissions": [],
        "frequency": None,
        "slack": {"task": Task.CPQ_SLACK_NEW_QUOTE_APPROVALS_NOTIFICATION.value},
        "email": {"task": Task.CPQ_EMAIL_NEW_QUOTE_APPROVALS_NOTIFICATION.value},
        "ms_teams": {"task": Task.CPQ_MS_TEAMS_NEW_QUOTE_APPROVALS_NOTIFICATION.value},
        "category": TaskCategory.CPQ_APPROVALS.value,
        "is_slack_channel_notification": False,
    }


class Command(Enum):
    SLASH_COMMISSION_COMMAND = {
        "name": "SLASH_COMMISSION_COMMAND",
        "command": "/Commissions",
        "permissions": [
            RbacPermissions.VIEW_PAYOUTS.value,
            RbacPermissions.VIEW_STATEMENTS.value,
            RbacPermissions.VIEW_COMMISSIONPLAN.value,
        ],
    }
    SLASH_QUOTA_ATTAINMENT_COMMAND = {
        "name": "SLASH_QUOTA_ATTAINMENT_COMMAND",
        "command": "/QuotaAttainment",
        "permissions": [
            RbacPermissions.VIEW_QUOTAS.value,
        ],
    }
    SLASH_QUOTA_COMMAND = {
        "name": "SLASH_QUOTA_COMMAND",
        "command": "/Quota",
        "permissions": [
            RbacPermissions.VIEW_QUOTAS.value,
        ],
    }
    SLASH_CRYSTAL_COMMAND = {
        "name": "SLASH_CRYSTAL_COMMAND",
        "command": "/Crystal",
        "permissions": [
            RbacPermissions.MANAGE_CRYSTAL.value,
        ],
    }


class SLACK_MESSAGES(Enum):
    NOT_ADDED_TO_COMMISSION_PLAN = "Hey {emp_name}! {emoji}\n\nYou've not been added to a $COMMISSION_lc plan yet. Please contact your admin."
    CHECK_OUT_COMMISSIONS = (
        "Hey {emp_name}! {emoji}\n\nCheck out your $COMMISSIONS_lc *{period_label}*"
    )
    SELECT_QUOTA_CATEGORY = (
        "Hi {emp_name}! {emoji}\n\n Please select a $QUOTA_lc category"
    )
    SELECT_QUOTA = "Select $QUOTA"
    UNABLE_TO_POST_QUOTA = "Unable to post $QUOTA. Please contact the admin."
    CHANGE_QUOTA_CATEGORY = "Change $QUOTA_lc category"
    CHECK_OUT_QUOTA = "Hey {emp_name}! {emoji}\n\nCheck out your $QUOTAS_lc for *{quota_category_name}*:"
    MY_QUOTAS = "My $QUOTAS_lc"
    DONT_HAVE_QUOTAS = "Hey {emp_name}! {emoji}\n\n You don't have $QUOTAS_lc set!"
    CHECK_OUT_QUOTA_ATTAINMENT = "Hey {emp_name}! {emoji}\n\nCheck out your $QUOTA_lc attainment for *{quota_time_period}*:"
    ATTAINED_QUOTAS = "*$QUOTA:* {category}\n\n *$QUOTA Attainment :dart:%:* {attainment}%\n\n *$QUOTA value:* {quota_value}\n\n *Actuals:* {quota_erosion}\n\n"
    NO_QUOTA_ATTAINMENT = (
        "Hi {emp_name}! :wave:\n\nYou don't have $QUOTA_lc attainment!"
    )
    SELECT_TIME_PERIOD = "Hey {emp_name}! {emoji}\n\nPlease select a time period for which you want the $QUOTA_lc attainment"
    NO_DATA = "No $COMMISSION/$QUOTA Erosion data available."
    UNABLE_TO_GET_COMMISSION = "Unable to get $COMMISSIONS. Please contact the admin."
    SELECT_COMM_TIME_PERIOD = (
        "Hi! Please select a time period for which you want the $COMMISSIONS_lc "
    )
    NO_COMM_DATA = "No $COMMISSION Data to Display"
    COMM_SPLIT_BY_COMPONENT = (
        "Here's your $COMMISSION_lc split by component for *{period_label}* "
    )
    COMM_ADJUSTMENTS = "*$COMMISSION Adjustments:* {currency}{adjustments}"
    SELECT_PLAN_VIEW_SPLIT = (
        "Hi! Please select a Plan to view $COMMISSION_lc split by component "
    )
    COMMISSION_AMOUNT = "*$COMMISSION :moneybag::* {currency_symbol}{comm_amount}"
    COMMISSION_PERCENT = "*$COMMISSION %:* {commission_percentage} "
    DAYS_FOR_PERIOD_CLOSE = "*$PAYOUT {emoji}*: {currency_symbol}{commission_amount} ({days_left} day(s) for period close)"
    LAST_MONTH_COMM_CALC = "Your $COMMISSION_lc calculation for the last month will be closed by {freeze_date}.\n"
    PAYOUT_AMOUNT = "*$PAYOUT {emoji}*: {currency_symbol}{comm_amount}"
    CURRENT_MONTH_PAYOUT_INITIATED = "Hey {emp_name}! {emoji}\n\nYour $PAYOUT_lc for the current period has been initiated"
    ACHIEVED_QUOTA = "Congratulations, {emp_name}! You've achieved your $QUOTA. Keep up this good work! {emoji}"
    QUOTA_CATEGORY = "{quota_type}*$QUOTA Category:* {category}"
    QUOTA_PERIOD = "*$QUOTA Period:* {quota_time_period}"
    QUOTA_ATTAINMENT = "*$QUOTA Attainment {emoji}%:* {quota_attainment}%"
    PAYOUT_PERCENT = "*$PAYOUT %*: {payout_percent} "
    PAYOUT_YTD_PERIOD_CLOSE = "*$PAYOUT YTD *: {currency_symbol}{ytd_payout} ({days_left_for_payout} day(s) for period close) "
    PAYOUT_YTD = "*$PAYOUT YTD*: {currency_symbol}{ytd_payout}"
    PAYOUT_PERCENT_YTD = "*$PAYOUT % YTD*: {ytd_percent} "
    QUOTA_VALUE = "*$QUOTA:* {quota_value}"
    VIEW_COMMISSIONS = "View $COMMISSIONS"
    VIEW_COMMISSION = "View $COMMISSION"
    ADMIN_REVERTED_PAYOUT = "Hey {admin_to_notify_name}! {emoji}\n\n{unpaid_by_name} has *reverted $PAYOUT_lc* for:"
    NOTIFY_REVERTED_PAYOUT = "{unpaid_by_name} has *reverted $PAYOUT_lc* for:"
    COMMISSION = "$COMMISSION"
    ADJUSTMENTS = "$ADJUSTMENTS"
    COMMISSIONS = "$COMMISSIONS"
    ADJUSTMENT = "$ADJUSTMENT"
    PAYOUT = "$PAYOUT"
    PAYOUT_LC = "$PAYOUT_lc"
    COMMISSION_LC = "$COMMISSION_lc"
    PAYOUTS = "$PAYOUTS"
    PAYOUTS_LC = "$PAYOUTS_lc"
    QUOTA = "$QUOTA"
    QUOTAS = "$QUOTAS"
    COMMISSIONS_PAID = "$COMMISSIONS Paid"
    COMMISSIONS_UNPAID = "$COMMISSIONS Unpaid"
    COMMS_BULK_PAY = "$COMMISSIONS Paid Bulk"
    COMMS_BULK_UNPAY = "$COMMISSIONS UpPaid Bulk"
    QUOTA_ATTAINMENT_HEADER = "$QUOTA attainment"
    UPDATED_COMM_PLAN = (
        "Hey {emp_name}! {emoji}\n\nYour $COMMISSION_lc plan has been updated"
    )
    ADDED_COMM_PLAN = "Hey {emp_name}! {emoji}\n\nYou've been added to the following $COMMISSION_lc plan"


class MS_TEAMS_MESSAGES(Enum):
    QUOTA_CATEGORY = "<b>$QUOTA:</b> {quota_category}<br><br>"
    QUOTA_ATTAINMENT = "<b>$QUOTA Attainment {emoji} %:</b> {quota_attainment}<br><br>"
    CHECK_OUT_COMMISSIONS = "Hi {emp_name}! {emoji}<br><br> Check out your $COMMISSIONS_lc for <b>{period_label}</b><br><br>"
    VIEW_COMMISSIONS = "View $COMMISSIONS"
    COMMISSION_AMOUNT = (
        "<b>$COMMISSION </b> {emoji}:  {currency_symbol}{comm_amount}<br><br>"
    )
    COMMISSION_PERCENT = "<b>$COMMISSION %:</b> {comm_percent}<br><br>"
    COMM_ADJUSTMENTS = (
        "<b>$COMMISSION Adjustments:</b>  {currency_symbol}{adjustments}<br><br>"
    )
    DRAW_ADJUSTMENTS = "<b>Draw Adjustments:</b>  {currency_symbol}{draws}<br><br>"
    NOT_ADDED_TO_PLAN = "Hi {emp_name}! {emoji}<br><br>You've not been added to a $COMMISSION_lc plan yet. Please contact your admin."
    UNABLE_TO_POST_COMM = "Unable to post $COMMISSIONS. Please contact the admin."
    PICK_COMM_PLAN = "Pick a $COMMISSION_lc plan"
    SPLIT_BY_COMPONENT = "Here's your $COMMISSION_lc split by component for <b>{period_label}</b><br><br>"
    CHECK_OUT_QUOTAS = "Hi {emp_name}! {emoji}<br><br> Check out your $QUOTAS_lc for <b>{quota_category_name}</b>:<br><br>"
    MY_QUOTAS = "My $QUOTAS_lc"
    CHANGE_QUOTA_CATEGORY = "Change $QUOTA_lc category"
    NOT_ASSIGNED_QUOTAS = "Hi {emp_name}! {emoji}<br><br>You've not been assigned $QUOTAS_lc yet. Please contact your admin."
    UNABLE_TO_POST_QUOTA = "Unable to post $QUOTA. Please contact the admin."
    CHECK_OUT_QUOTA_ATTAINMENT = "Hi {emp_name}! {emoji}<br><br> Check out your $QUOTA_lc attainment for <b>{quota_time_period}</b>:<br><br>"
    QUOTA_VALUES = "<b>$QUOTA</b>: {category}<br><br><b>$QUOTA Attainment {emoji}</b>%: {quota_attainment}%<br><br><b>$QUOTA value</b>: {quota_value}<br><br><b>Actuals</b>: {quota_erosion}<br><br>"
    UNABLE_TO_POST_QUOTA_ATTAINMENT = (
        "Unable to post $QUOTAS Attainment. Please contact the admin."
    )
    PAYEE_QUOTA_VALUE = "<b>$QUOTA value:</b> {currency_symbol}{quota_value}<br><br>"
    PAYEE_QUOTA_EROSION = "<b>Actuals:</b> {currency_symbol}{quota_erosion}<br><br>"
    PAYOUT_PERIOD_CLOSE_YTD = "<b>$PAYOUT YTD</b>: {currency_symbol}{ytd_payout} ({days_left_for_payout} day(s) for period close)<br><br>"
    PAYOUT_AMOUNT_YTD = "<b>$PAYOUT YTD</b>: {currency_symbol}{ytd_payout}<br><br>"
    PAYOUT_PERCENT_YTD = "<b>$PAYOUT % YTD</b>: {ytd_percent}<br><br>"
    PAYOUT_AMOUNT = "<b>$PAYOUT {emoji}</b>: {currency_symbol}{comm_amount}<br><br>"
    PAYOUT_PERCENT = "<b>$PAYOUT %:</b> {payout_per}<br><br>"
    PAYOUT_PERIOD_CLOSE = "<b>$PAYOUT {emoji}</b>: {currency_symbol}{comm_amount} ({days_left_for_payout} day(s) for period close)<br><br>"
    COMM_CALC_CLOSED = "Your $COMMISSION_lc calculation for the last month will be closed by {freeze_date}<br><br>"
    PAYOUT_INITIATED = "Hey {emp_name}! {emoji}<br><br> Your $PAYOUT_lc for the current period has been initiated.<br><br>"
    QUOTA_ACHIEVED = "Congratulations, {emp_name}! You've achieved your $QUOTA_lc. Keep up this good work! {emoji}"
    PAYOUT_REVERTED = "Hey {admin_to_notify_name}! {emoji}<br><br>{unpaid_by_name} has <b>reverted $PAYOUT_lc</b> for:<br><br>{unpaid_for_name}<br><br>"
    PAYOUT_REVERTED_COUNT = "Hey {admin_to_notify_name}! {emoji}<br><br>{unpaid_by_name} has <b>reverted $PAYOUT_lc</b> for:<br><br>{unpaid_for_count} Payees<br><br>"
    COMMISSIONS = "$COMMISSIONS"
    ADJUSTMENTS = "$ADJUSTMENTS"
    PAYOUTS = "$PAYOUTS"
    QUOTAS = "$QUOTAS"
    COMMISSION = "$COMMISSION"
    ADJUSTMENT = "$ADJUSTMENT"
    PAYOUT = "$PAYOUT"
    QUOTA = "$QUOTA"
    COMMISSION_LC = "$COMMISSION_lc"
    PAYOUT_LC = "$PAYOUT_lc"
    UPDATED_COMM_PLAN = "Hey {emp_name}! {emoji}<br><br>Your $COMMISSION_lc plan has been updated<br><br>"
    ADDED_TO_COMM_PLAN = "Hey {emp_name}! {emoji}<br><br>You've been added to the following $COMMISSION_lc plan:<br><br>"


class VariablePayType(Enum):
    ANNUAL = "ANNUAL"
    PERIODIC = "PERIODIC"


class CustomCalendarType(Enum):
    CUSTOM = "CUSTOM"
    WEEKLY = "WEEKLY"
    BI_WEEKLY = "BI-WEEKLY"


class StatementsPdf(Enum):
    TEMP_FILE_PATH = "temp_statements"
    LOCKED = "Locked"
    UNLOCKED = "Unlocked"
    HEADER_FILE_PATH = "spm/templates/header.html"
    FOOTER_FILE_PATH = "spm/templates/footer.html"
    LOCAL_TESTING = "Statements Pdf file has been saved in your Downloads folder"

    EVERSTAGE_LOGO_LIGHT = "/everstage-logo-light.svg"
    PERSPECTIVE_ICON = "/perspective.svg"
    EVERSTAGE_LOGO_DARK = "/everstage-logo-dark.svg"
    DARK_THEME_LOCK = "/lock_light.svg"
    DARK_THEME_UNLOCK = "/unlock_dark.svg"
    LIGHT_THEME_LOCK = "/lock_dark.svg"
    LIGHT_THEME_UNLOCK = "/unlock%20copy.svg"
    STATEMENT_FOR = "Statement for"


class ReportObject(Enum):
    COMMISSION = (
        "commission",
        "report_object_data",
        ModuleType.ICM.value,
    )
    FORECAST_COMMISSION = (
        "forecast_commission",
        "forecast_object_data",
        ModuleType.ICM.value,
    )
    COMMISSION_SUMMARY = (
        "commission_summary",
        "report_object_data",
        ModuleType.ICM.value,
    )
    SETTLEMENT = (
        "settlement",
        "report_object_data",
        ModuleType.ICM.value,
    )
    QUOTA_ATTAINMENT = (
        "quota_attainment",
        "report_object_data",
        ModuleType.ICM.value,
    )
    FORECAST_QUOTA_ATTAINMENT = (
        "forecast_quota_attainment",
        "forecast_object_data",
        ModuleType.ICM.value,
    )
    QUOTA = (
        "quota",
        "system_object_data",
        ModuleType.ICM.value,
    )
    USER = (
        "user",
        "system_object_data",
        ModuleType.ICM.value,
    )
    INTER_QUOTA_ATTAINMENT = (
        "inter_quota_attainment",
        "inter_object_data",
        ModuleType.ICM.value,
    )
    INTER_COMMISSION = (
        "inter_commission",
        "inter_object_data",
        ModuleType.ICM.value,
    )
    INTER_FORECAST_COMMISSION = (
        "inter_forecast_commission",
        "inter_forecast_object_data",
        ModuleType.ICM.value,
    )
    INTER_FORECAST_QUOTA_ATTAINMENT = (
        "inter_forecast_quota_attainment",
        "inter_forecast_object_data",
        ModuleType.ICM.value,
    )
    PAYOUT = (
        "payout",
        "report_object_data",
        ModuleType.ICM.value,
    )
    FX_RATE = (
        "fx_rate",
        "system_object_data",
        ModuleType.ICM.value,
    )
    STATEMENT_APPROVALS = (
        "statement_approvals",
        "system_object_data",
        ModuleType.ICM.value,
    )
    QUOTE = (
        "quote",
        "report_object_data",
        ModuleType.CPQ.value,
    )
    QUOTE_LINE_ITEM = (
        "quote_line_item",
        "report_object_data",
        ModuleType.CPQ.value,
    )
    QUOTE_LINE_ITEM_TIER = (
        "quote_line_item_tier",
        "report_object_data",
        ModuleType.CPQ.value,
    )

    def __new__(
        cls, object_id: str, table_name: str, product_module: str
    ) -> "ReportObject":
        _object = object.__new__(cls)
        _object._value_ = object_id
        _object._table_name = table_name
        _object._product_module = product_module
        return _object

    @property
    def table_name(self) -> str:
        return self._table_name

    @property
    def product_module(self) -> str:
        return self._product_module

    @classmethod
    def get_table_name_by_object_id(cls, object_id: str) -> str:
        for member in cls:
            if member.value == object_id:
                return member.table_name
        raise NotImplementedError

    @classmethod
    def get_enum_by_object_id(cls, object_id: str) -> "ReportObject":
        for member in cls:
            if member.value == object_id:
                return member
        raise NotImplementedError

    @classmethod
    def get_values_by_table_name(
        cls, table_name: str, product_module: str = ModuleType.ICM.value
    ) -> List[str]:
        return [
            item.value
            for item in cls
            if (item.table_name == table_name and item.product_module == product_module)
        ]

    @classmethod
    def values(cls, product_module: str) -> List[str]:
        return [item.value for item in cls if item.product_module == product_module]


class DATASHEET_TRANSFORMATIONS(Enum):
    """
    Enum for datasheet transformations
    """

    JOIN = auto()
    UNION = auto()
    FILTER = auto()
    GROUP_BY = auto()
    SORT = auto()
    ADVANCED_FILTER = auto()
    ADVANCED_FILTER_V2 = auto()
    TEMPORAL_SPLICE = auto()
    FLATTEN = auto()
    GET_USER_PROPERTIES = auto()


class DATASHEET_ADJUSTMENT_TYPE(Enum):
    """
    Enum for the type of datasheet adjustment
    """

    GLOBAL = "GLOBAL"
    LOCAL = "LOCAL"


class DATASHEET_ADJUSTMENT_OPERATION(Enum):
    """
    Enum for the operation to be performed on the datasheet adjustment
    """

    UPDATE = "UPDATE"
    IGNORE = "IGNORE"
    INSERT = "INSERT"


class DatabookDeleteError(Enum):
    """
    Enum for Databook delete errors
    """

    USED_IN_CRYSTAL_VIEW = "Databook is used in Draft/Published Crystal view"
    USED_IN_DASHBOARD = "Databook is used in Dashboard"
    USED_IN_WORKFLOW = "Cannot delete databook as it is part of workflow."


class DatasheetDeleteError(Enum):
    """
    Enum for Datasheet delete errors
    """

    GENERATION_IN_PROGRESS = "Currently, {total_count} datasheets are being generated and some of them are {datasheet_names}. Please try again later."
    USED_IN_WORKFLOW = "Cannot delete datasheet as it is part of workflow."
    NONE = "None"


class DATASHEET_INVALID_REASON(Enum):
    """
    Enum for the reason for invalid datasheet
    """

    CYCLE_FORMATION_IN_DATASHEET = "The datasheets {datasheet_names} being utilized in these calculated fields/transformations have already been derived from this datasheet. As a result, they cannot be used as a source. Please make the necessary changes before saving."
    DATASHEET_SOURCE_ARCHIVED = "To create this datasheet, please activate the following databooks: {databook_names}, as this datasheet utilizes all these archived databooks as sources."


class UpstreamETLVersions(Enum):
    V1 = "v1"
    V2 = "v2"


class COMMISSION_TYPE(Enum):
    """
    Enum for the type of commission. It can be either COMMISSION or FORECAST
    """

    COMMISSION = "COMMISSION"
    FORECAST = "FORECAST"


class CLIENT_FEATURES(Enum):
    APPROVAL_CONFIG = "approval_config"
    SHOW_FORECAST = "show_forecast"


class SnapshostTable(Enum):
    """
    Enum to prefix the columns in the snapshot table
    """

    COLUMN_IDENTIFIER_PREFIX = "snapshot_sys_"


class SQLConnectionTypes(Enum):
    MSSQL = "mssql"
    SNOWFLAKE = "snowflake"
    POSTGRESQL = "postgres"
    SUITEANALYTICS = "suiteanalytics"


commission_plan_scope_map = {
    "view:commissionplan": "can_view",
    "edit:commissionplan": "can_edit",
    "delete:commissionplan": "can_delete",
}


class PlanModificationTypes(Enum):
    PLAN_DELETE = "plan_delete"
    REMOVE_PAYEE = "remove_payee"
    EDIT_PERIOD = "edit_period"
    SETTLEMENT_END_DATE = "settlement_end_date"


class CommissionChangesTypes(Enum):
    SYNC = "sync"
    UNFREEZE = "unfreeze"
    DELETE = "delete"
    FREEZE = "freeze"


class PayoutStatusChangesTypes(Enum):
    SYNC = "sync"
    UNFREEZE = "unfreeze"
    FREEZE = "freeze"
    PAYOUT_STATUS_CHANGE = "payout_status_change"
    PAYMENT_REGISTER = "payment_register"
    PAYMENT_INVALIDATE = "invalidate_payment"
    ADD_COMM_ADJ = "add_comm_adj"
    UPDATE_COMM_ADJ = "update_comm_adj"
    DELETE_COMM_ADJ = "delete_comm_adj"
    ADD_DRAW_ADJ = "add_draw_adj"
    DELETE_DRAW_ADJ = "delete_draw_adj"
    PROCESS_ARREAR = "process_arrear"
    IGNORE_ARREAR = "ignore_arrear"
    ARREAR_CHANGE = "arrear_change"
    APPROVAL_UPDATE = "approval_update"
    APPROVAL_CREATE = "approval_create"
    APPROVAL_APPROVE = "approval_approve"
    APPROVAL_REJECT = "approval_reject"
    APPROVAL_WITHDRAW = "approval_withdraw"
    APPROVAL_REVOKE = "approval_revoke"
    EXIT_PAYEE = "exit_payee"
    INVITE_PAYEE = "invite_payee"
    ACTIVE_PAYEE = "active_payee"
    REVERT_EXIT_PAYEE = "revert_exit_payee"


class ApprovalTypes(Enum):
    STATEMENT_LEVEL = "statement_level"
    LINE_ITEM_LEVEL = "line_item_level"
    DISABLED = "disabled"


class UpstreamSyncModes(Enum):
    ALL = "all"
    CHANGES = "changes"
    SNAPSHOT = "snapshot"


class MaestroStageStatus(Enum):
    STARTED = "started"
    COMPLETED = "completed"
    FAILED = "failed"
