import datetime
import logging
import random
from http.client import RemoteDisconnected
from importlib import import_module
from time import sleep
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

import pydash
import pytz
from dateutil.parser import parse
from django.core.cache import cache
from django.utils.timezone import make_aware

from commission_engine.accessors.etl_config_accessor import (
    ApiAccessConfigAccessor,
    EnrichmentConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusAccessor,
)
from commission_engine.custom_exceptions.request_exceptions import (
    ThirdPartyAPIException,
)
from commission_engine.models.etl_config_models import TransformationConfig
from commission_engine.self_service_integrations.hubspot_self_service_integration import (
    HubspotServiceIntegration,
)
from commission_engine.third_party_connections.enrichment import apply_enrichments
from commission_engine.third_party_connections.exceptions.missing_field_exception import (
    MissingFieldException,
)
from commission_engine.third_party_connections.third_party_api import ThirdPartyApi
from commission_engine.third_party_connections.utils import (
    check_primary_or_snapshot_key_deleted,
)
from commission_engine.utils import date_utils
from commission_engine.utils.general_data import UpstreamETLVersions
from commission_engine.utils.log_utils import merge_log_context
from everstage_ddd.upstream import UpstreamChangesWriter
from interstage_project.threadlocal_log_context import (
    get_threadlocal_context,
    set_threadlocal_context,
)

logger = logging.getLogger(__name__)

TIMEOUT_ERROR_CODES = (429, 502, 503, 504, 521, 522, 523, 524)
# Reference: https://developers.hubspot.com/docs/api/error-handling

# TODO: Remove these constants
ERR_CXN_ABORTED = "Connection aborted"
ERR_END_CLOSED = "Remote end closed connection without response"


class Hubspot(ThirdPartyApi):
    def __init__(self):
        self.source_deleted_fields = []

    def _log_exception_and_sleep(
        self, exception: Exception | None = None, sleep_time: float | None = None
    ):
        if sleep_time is None:
            sleep_time = random.uniform(0, 3)
        if exception is not None:
            logger.exception("Exception: %s", exception)
        logger.info("Sleeping for %s seconds", sleep_time)
        sleep(sleep_time)

    def _update_search_pagination_filter(
        self, body: dict, last_page_record: dict
    ) -> dict:
        """
        Updates pagination filters when records exceed 10,000 limit in search api.

        Args:
            body: Request body containing filters and sort field (pagination field)
            last_page_record: Last record in the current page

        Returns:
            Updated body with new pagination filters to fetch next 10,000 records
        """

        pagination_field = body["sorts"][0]["propertyName"]
        last_pagination_field_value = last_page_record.get("properties", {}).get(
            pagination_field
        )

        pagination_filter = {
            "value": last_pagination_field_value,
            "operator": "GT",
            "propertyName": pagination_field,
        }

        # Handle case where filterGroups might not exist (in all mode)
        if not body.get("filterGroups"):
            body["filterGroups"] = [{"filters": [pagination_filter]}]
        else:
            # For each filter group
            for filter_group in body["filterGroups"]:
                # Handle if filters is empty
                if not filter_group.get("filters"):
                    filter_group["filters"] = [pagination_filter]

                # Check if pagination filter already exists in this group
                pagination_filter_exists = False
                for filter_item in filter_group["filters"]:
                    if (
                        filter_item.get("propertyName") == pagination_field
                        and filter_item.get("operator") == "GT"
                    ):
                        # Update existing filter with new value
                        filter_item.update(pagination_filter)
                        pagination_filter_exists = True
                        break

                # If pagination filter doesn't exist in this group, add it
                if not pagination_filter_exists:
                    filter_group["filters"].append(pagination_filter)

        return body

    def get_stages_and_enriched_records(
        self,
        client_id: int,
        object_id: str,
        records: List[Dict[str, Any]],
        visited: Set[str],
        current_page_records: Set[str],
        is_validation: bool,
        integration_id: UUID,
    ) -> List[Dict[str, Any]]:
        if object_id.startswith("pipeline_stages"):
            result = self.get_stages_records({"results": records})
        else:
            api_enrichments = EnrichmentConfigAccessor(
                client_id
            ).get_api_enrichments_by_integration_id(integration_id)
            result = self.apply_api_enrichment(
                client_id,
                records,
                api_enrichments,
                is_validation=is_validation,
                integration_id=integration_id,
                current_page_records=current_page_records,
                visited=visited,
            )

        return result

    def verify_deleted_fields(
        self,
        client_id: int,
        integration_id: UUID,
        sobject: str,
        trans_configs: Optional[list[TransformationConfig]] = None,
    ) -> list[str]:
        """
        Checks if the fields are present in the source object by comparing with the name field.
        Stores the mapping of deleted fields in source_deleted_fields and returns updated field list.

        Args:
            client_id: Client ID
            integration_id: Integration ID
            sobject: Source object name
            trans_configs: List of transformation configs

        Returns:
            List of fields that are present in the source object and not deleted
        """
        try:
            atc_id = (
                ExtractionConfigAccessor(client_id)
                .get_record_by_integration_id(integration_id)[0]
                .access_token_config_id
            )

            if not trans_configs:
                trans_configs = list(
                    TransformationConfigAccessor(
                        client_id
                    ).get_records_by_integration_id(integration_id)
                )

            # Get object properties from Hubspot API
            hs_self_service = HubspotServiceIntegration(client_id, atc_id)
            hs_self_service.clear_cached_fields_for_object(sobject)
            sobject_details = hs_self_service.get_all_fields_in_object(sobject)

            # Create set of available fields from properties
            available_fields = {prop["name"] for prop in sobject_details}

            verified_fields = []
            deleted_fields = []

            # Find deleted fields (fields in transformation but not in source)
            # Skip association fields to be marked as deleted
            for tc in trans_configs:
                associated_object_id = (tc.additional_config or {}).get(
                    "associated_object_id"
                )
                if associated_object_id:
                    verified_fields.append(tc.source_field)
                    continue

                if tc.source_field in available_fields:
                    verified_fields.append(tc.source_field)
                else:
                    deleted_fields.append(tc.source_field)

            # Store deleted fields mapping
            if deleted_fields:
                self.source_deleted_fields.extend(deleted_fields)
                logger.info(
                    "Found deleted fields for object %s: %s", sobject, deleted_fields
                )

            # Check if any primary or snapshot key is deleted
            if check_primary_or_snapshot_key_deleted(
                client_id, integration_id, deleted_fields
            ):
                deleted_fields_str = ",".join(deleted_fields)
                raise MissingFieldException(  # noqa: TRY301
                    field=deleted_fields_str,
                    message=f"The primary or snapshot key is deleted at the source. Deleted fields: {deleted_fields_str}",
                    deleted_fields=deleted_fields,
                )

            # Return available fields, associated fields, or lookup fields
            return verified_fields  # noqa: TRY300

        except MissingFieldException:
            logger.exception(
                "Error verifying deleted fields, Primary or snapshot key is deleted at the source."
            )
            raise
        except Exception:
            logger.exception("Error verifying deleted fields")
            return []

    def get_extractor_params(
        self,
        client_id,
        request_body,
        changes_start_time,
        sync_mode,
        integration_id,
    ):
        """
        Args:
                client_id: customer's unique id
                request_body: body params specified in the config
                changes_start_time: to fetch records modified after this time
                sync_mode: contains mode of string e.g. "all", "changes" ...
                integration_id: Id of the integrated object.
        Returns:
                url body parameters
        """
        source_fields = []

        # Getting properties to fetch for the object
        trans_configs = list(
            TransformationConfigAccessor(client_id).get_records_by_integration_id(
                integration_id
            )
        )
        for con in trans_configs:
            source_fields.append(con.source_field)
        properties = set(source_fields)

        # filters to apply to the records
        if not changes_start_time:
            changes_start_time = make_aware(
                datetime.datetime.now()
            ) - datetime.timedelta(days=7)
        last_modified_date = date_utils.convert_datetime_to_timestamp(
            changes_start_time
        )
        # maximum limit is 100 per page in hubspot
        LIMIT = 100
        last_modified_date_column = "hs_lastmodifieddate"
        sort_field = "hs_object_id"  # Only can be sorted by one field in hubspot
        if request_body and isinstance(request_body, dict):
            if "limit" in request_body:
                LIMIT = request_body["limit"]
            if "last_modified_date_column" in request_body:
                last_modified_date_column = request_body["last_modified_date_column"]
            if "sort_by" in request_body:
                # Use a unique field here as pagination logic depends on it to fetch records more than 10,000
                sort_field = request_body["sort_by"].strip()
        properties.add(sort_field)
        sobject = trans_configs[0].source_object_id
        updated_properties = self.verify_deleted_fields(
            client_id, integration_id, sobject, trans_configs
        )
        request_body_params = {
            "limit": LIMIT,
            "properties": updated_properties if updated_properties else properties,
            "filterGroups": [
                {
                    "filters": [
                        {
                            "value": str(last_modified_date),
                            "operator": "GTE",
                            "propertyName": last_modified_date_column,
                        },
                    ]
                }
            ],
            "sorts": [
                {
                    "propertyName": sort_field,
                    "direction": "ASCENDING",
                }
            ],
        }
        if sync_mode == "all":
            del request_body_params["filterGroups"]
        return request_body_params, last_modified_date_column

    def get_changed_records(
        self,
        client_id,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        object_id,
        _unused_primary_kd,
        changes_start_time,
        sync_mode,
        config_object,
        _unused_snapshot_key,
        _unused_destination_object_type,
        integration_id: UUID,
        is_validation,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
        snowflake_uploader: Optional[UpstreamChangesWriter] = None,
    ):
        """
        Fetch records using Hubspot search api
            - POST /crm/v3/objects/deals/search
        Args:
            client_id: customer's unique id
            object_id: the source object to fetch from the CRM
            changes_start_time: modified/created time from which we want to get the records
            config_object: contains the URL to access the resource
            sync_mode: contains mode of string e.g. "all", "changes" ...
            _unused_destination_object_type: parameter unused
            _unused_snapshot_key: parameter unused
            _unused_primary_kd: parameter unused
            integration_id: Id of the integrated object.
        Returns: list of data fetched for an object
        """
        module_name = "everstage_etl.tasks.extraction"
        module = import_module(module_name)
        insert_extract_sync_log = getattr(module, "insert_extract_sync_log")

        integration_record = IntegrationAccessor(
            client_id=client_id
        ).get_object_by_integration_id(integration_id=integration_id)
        additional_data = integration_record.additional_data  # type: ignore
        run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)

        # Create new UpstreamChangesWriter instance only
        # if optional parameter (snowflake_uploader) is not present and run_snowflake_sync is true in integration record, additional_data
        if snowflake_uploader is None and run_snowflake_sync:
            snowflake_uploader = UpstreamChangesWriter(
                client_id=client_id,
                integration_id=integration_id,
                is_validation=is_validation,
            )
            snowflake_uploader.set_meta(is_deleted=False)

        if source_primary_keys is None:
            source_primary_keys = []
        old_log_context = get_threadlocal_context()
        new_log_context = {
            "client_id": client_id,
            "object_id": object_id,
            "changes_start_time": changes_start_time,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": sync_run_id,
        }

        merged_log_context = merge_log_context(old_log_context, new_log_context)

        set_threadlocal_context(merged_log_context)

        logger.info("RUNNING CHANGES SYNC..")
        curr_page = 0
        after = None
        total_so_far = 0
        final_result = []
        visited = set()
        curr_page_records = set()
        attempts = 0

        api_type = (
            config_object.additional_data.get("api_type", "search")
            if config_object.additional_data
            else "search"
        )
        if api_type == "search":
            body, _last_modified_date_column = self.get_extractor_params(
                client_id,
                config_object.request_body,
                changes_start_time,
                sync_mode,
                integration_id,
            )
            logger.info("BEGIN: IMPORT DATA FROM HUBSPOT")
            while curr_page == 0 or after is not None:
                page_records = []
                curr_page_records = set()
                curr_page += 1
                try:
                    attempts += 1
                    if attempts > 3:
                        raise Exception(
                            f"Failed to retrieve page {curr_page} after 3 attempts"
                        )
                    logger.info(f"BODY PARAMS {body=}")
                    part_result = self.api_call(
                        client_id=client_id,
                        url=config_object.request_url,
                        body=body,
                        api_config_object=config_object,
                        is_validation=is_validation,
                    )
                except (ConnectionError, RemoteDisconnected) as e:
                    curr_page -= 1
                    self._log_exception_and_sleep(e)
                    continue
                except ThirdPartyAPIException as e:
                    curr_page -= 1
                    if e.response.status_code in TIMEOUT_ERROR_CODES:
                        self._log_exception_and_sleep(e)
                        continue
                    raise e
                # TODO: Remove this block
                except Exception as e:
                    curr_page -= 1
                    if ERR_CXN_ABORTED in str(e) or ERR_END_CLOSED in str(e):
                        self._log_exception_and_sleep(e)
                        continue
                    raise e
                logger.info(
                    f"Page - {curr_page}: Received successful response after {attempts} attempts"
                )
                attempts = 0

                data = part_result.get("results", [])
                for _data in data:
                    if _data.get("id") not in visited:
                        page_records.append(_data.get("properties", {}))
                        curr_page_records.add(_data.get("id"))
                        visited.add(_data.get("id"))
                        total_so_far = total_so_far + 1
                after = part_result.get("paging", {}).get("next", {}).get("after")

                # Hubspot API limits to maximum 10,000 records in search API
                # After 10,000 records, we need to update the pagination filter to fetch next set of records
                # Since results are sorted by ID in ascending order, we can use the last record's ID
                # to get the next chunk of data.
                # For example: If there are 25,000 records total:
                # First set of API calls: Returns records 1-10,000 (sorted by ascending ID)
                # Second set of API calls: We take last record's ID from previous response (10,000th record)
                #                          Update filter with that ID to get records 10,001-20,000
                # Third set of API calls: Use ID of 20,000th record to fetch remaining 20,001-25,000
                # This ensures we get all records beyond the 10,000 record API limit
                if after and int(after) >= 10000:
                    # Getting the records modified after the last record in previous chunk
                    after = "0"
                    n = len(data)
                    last_record_in_curr_page = data[n - 1] if n > 0 else {}
                    body = self._update_search_pagination_filter(
                        body, last_record_in_curr_page
                    )

                body["after"] = after

                # is_validation is True then exit the loop to avoid unnecessary fetching of records.
                if is_validation:
                    logger.info("Skipping further record fetching for validation")
                    if snowflake_uploader:
                        page_records = self.get_stages_and_enriched_records(
                            client_id=client_id,
                            object_id=object_id,
                            records=page_records,
                            visited=visited,
                            current_page_records=curr_page_records,
                            is_validation=is_validation,
                            integration_id=integration_id,
                        )
                        snowflake_uploader.save_in_temp_table(page_records)
                        break
                    else:
                        page_records = self.get_stages_and_enriched_records(
                            client_id=client_id,
                            object_id=object_id,
                            records=page_records,
                            visited=visited,
                            current_page_records=curr_page_records,
                            is_validation=is_validation,
                            integration_id=integration_id,
                        )
                        UpstreamETLStatusAccessor(
                            client_id=client_id,
                            e2e_sync_run_id=e2e_sync_run_id,
                            sync_run_id=sync_run_id,
                        ).update_object({"extracted_records_count": total_so_far})

                        # returning the first page's records (page_records) for validation as
                        # breaking the loop would not update the final_result which in turn would
                        # result in empty list.
                        return page_records
                if snowflake_uploader:
                    page_records = self.get_stages_and_enriched_records(
                        client_id=client_id,
                        object_id=object_id,
                        records=page_records,
                        visited=visited,
                        current_page_records=curr_page_records,
                        is_validation=is_validation,
                        integration_id=integration_id,
                    )
                    snowflake_uploader.save_in_temp_table(page_records)
                elif upstream_etl_version == UpstreamETLVersions.V1.value:
                    final_result.extend(page_records)
                else:
                    page_records = self.get_stages_and_enriched_records(
                        client_id=client_id,
                        object_id=object_id,
                        records=page_records,
                        visited=visited,
                        current_page_records=curr_page_records,
                        is_validation=is_validation,
                        integration_id=integration_id,
                    )
                    logger.info(
                        f"BEGIN: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                    )
                    insert_extract_sync_log(
                        client_id,
                        e2e_sync_run_id,
                        sync_run_id,
                        object_id,
                        page_records,
                        "change",
                        source_primary_keys=source_primary_keys,
                    )
                    logger.info(
                        f"END: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                    )

                logger.info(
                    f"No of records fetched so far - {total_so_far}, page - {curr_page}"
                )

            logger.info(
                f"Done making Api Calls for {object_id} with total pages {curr_page}"
            )

        elif api_type == "list":
            _last_modified_date_column = "updatedAt"
            body = {}
            logger.info("BEGIN: IMPORT DATA FROM HUBSPOT")
            while curr_page == 0 or after is not None:
                page_records = []
                curr_page += 1
                try:
                    attempts += 1
                    if attempts > 3:
                        raise Exception(
                            f"Failed to retrieve page {curr_page} after 3 attempts"
                        )
                    logger.info(f"BODY PARAMS {body=}")
                    result = self.api_call(
                        client_id=client_id,
                        url=config_object.request_url,
                        body=body,
                        api_config_object=config_object,
                        is_validation=is_validation,
                    )
                except (ConnectionError, RemoteDisconnected) as e:
                    curr_page -= 1
                    self._log_exception_and_sleep(e)
                    continue
                except ThirdPartyAPIException as e:
                    curr_page -= 1
                    if e.response.status_code in TIMEOUT_ERROR_CODES:
                        self._log_exception_and_sleep(e)
                        continue
                    raise e
                # TODO: Remove this block
                except Exception as e:
                    curr_page -= 1
                    if ERR_CXN_ABORTED in str(e) or ERR_END_CLOSED in str(e):
                        self._log_exception_and_sleep(e)
                        continue
                    raise e
                logger.info(
                    f"Page - {curr_page}: Received successful response after {attempts} attempts"
                )
                attempts = 0

                data = result.get("results", [])
                for _data in data:
                    if _data.get("id") not in visited:
                        page_records.append(_data)
                        visited.add(_data.get("id"))
                        total_so_far = total_so_far + 1
                after = result.get("paging", {}).get("next", {}).get("after")

                if after is not None:
                    body["after"] = after

                # is_validation is True then exit the loop to avoid unnecessary fetching of records.
                if is_validation:
                    logger.info("Skipping further record fetching for validation")
                    if snowflake_uploader:
                        page_records = self.get_stages_and_enriched_records(
                            client_id=client_id,
                            object_id=object_id,
                            records=page_records,
                            visited=visited,
                            current_page_records=curr_page_records,
                            is_validation=is_validation,
                            integration_id=integration_id,
                        )
                        snowflake_uploader.save_in_temp_table(page_records)
                        break
                    else:
                        page_records = self.get_stages_and_enriched_records(
                            client_id=client_id,
                            object_id=object_id,
                            records=page_records,
                            visited=visited,
                            current_page_records=curr_page_records,
                            is_validation=is_validation,
                            integration_id=integration_id,
                        )
                        UpstreamETLStatusAccessor(
                            client_id=client_id,
                            e2e_sync_run_id=e2e_sync_run_id,
                            sync_run_id=sync_run_id,
                        ).update_object({"extracted_records_count": total_so_far})

                        # returning the first page's records (page_records) for validation as
                        # breaking the loop would not update the final_result which in turn would
                        # result in empty list.
                        return page_records

                if snowflake_uploader:
                    page_records = self.get_stages_and_enriched_records(
                        client_id=client_id,
                        object_id=object_id,
                        records=page_records,
                        visited=visited,
                        current_page_records=curr_page_records,
                        is_validation=is_validation,
                        integration_id=integration_id,
                    )
                    snowflake_uploader.save_in_temp_table(page_records)
                elif upstream_etl_version == UpstreamETLVersions.V1.value:
                    final_result.extend(page_records)
                else:
                    page_records = self.get_stages_and_enriched_records(
                        client_id=client_id,
                        object_id=object_id,
                        records=page_records,
                        visited=visited,
                        current_page_records=curr_page_records,
                        is_validation=is_validation,
                        integration_id=integration_id,
                    )
                    logger.info(
                        f"BEGIN: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                    )
                    insert_extract_sync_log(
                        client_id,
                        e2e_sync_run_id,
                        sync_run_id,
                        object_id,
                        page_records,
                        "change",
                        source_primary_keys=source_primary_keys,
                    )
                    logger.info(
                        f"END: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                    )

                logger.info(
                    f"No of records fetched so far - {total_so_far}, page - {curr_page}"
                )

            logger.info(
                f"Done making Api Calls for {object_id} with total pages {curr_page}"
            )

        logger.info(f"Total records fetched for {object_id} => {total_so_far}")

        if snowflake_uploader and run_snowflake_sync:
            snowflake_uploader.save()
        elif upstream_etl_version == UpstreamETLVersions.V1.value:
            final_result = pydash.flatten(final_result)
            final_result = self.get_stages_and_enriched_records(
                client_id=client_id,
                object_id=object_id,
                records=final_result,
                visited=visited,
                current_page_records=curr_page_records,
                is_validation=is_validation,
                integration_id=integration_id,
            )
            total_so_far = len(final_result)
        logger.info("END: IMPORT DATA FROM HUBSPOT")

        UpstreamETLStatusAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        ).update_object({"extracted_records_count": total_so_far})

        return final_result, self.source_deleted_fields

    def get_deleted_records(
        self,
        client_id,
        object_id,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        _unused_primary_kd,
        changes_start_time,
        _unused_sync_mode,
        del_config_obj,
        _unused_destination_object_type,
        integration_id,
        is_validation,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
        snowflake_uploader: Optional[UpstreamChangesWriter] = None,
    ):
        """
        API : GET /crm/v3/objects/deals
            @params {archived=true} to get all the deleted records
        Args:
            client_id: customer's unique id
            object_id: the source object to fetch from the CRM
            del_config_obj: Access config details for deleting the object
            _unused_destination_object_type: parameter unused
            _unused_sync_mode: parameter unused
            _unused_changes_start_time: parameter unused
            _unused_primary_kd: parameter unused
            _unused_integration_id: parameter unused

        Returns:
            list of deleted records id
        """
        module_name = "everstage_etl.tasks.extraction"
        module = import_module(module_name)
        insert_extract_sync_log = getattr(module, "insert_extract_sync_log")

        integration_record = IntegrationAccessor(
            client_id=client_id
        ).get_object_by_integration_id(integration_id=integration_id)
        additional_data = integration_record.additional_data  # type: ignore
        run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)

        # Create new UpstreamChangesWriter instance only
        # if optional parameter (snowflake_uploader) is not present and run_snowflake_sync is true in integration record, additional_data
        if snowflake_uploader is None and run_snowflake_sync:
            snowflake_uploader = UpstreamChangesWriter(
                client_id=client_id,
                integration_id=integration_id,
                is_validation=is_validation,
            )
            snowflake_uploader.set_meta(is_deleted=True)

        if source_primary_keys is None:
            source_primary_keys = []
        old_log_context = get_threadlocal_context()
        new_log_context = {
            "client_id": client_id,
            "object_id": object_id,
            "changes_start_time": changes_start_time,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": sync_run_id,
        }

        merged_log_context = merge_log_context(old_log_context, new_log_context)

        set_threadlocal_context(merged_log_context)

        logger.info("RUNNING DELETE SYNC")
        after = None
        deleted_records = []
        curr_page = 0
        total_so_far = 0
        attempts = 0

        trans_configs = TransformationConfigAccessor(
            client_id
        ).get_records_by_integration_id(integration_id)
        source_fields = [con.source_field for con in list(trans_configs)]
        params = {"limit": 100, "archived": True, "properties": set(source_fields)}
        additional_params = del_config_obj.request_body

        if additional_params:
            params.update(additional_params)

        logger.info("BEGIN: IMPORT DATA FROM HUBSPOT")
        while curr_page == 0 or after is not None:
            page_records = []
            curr_page += 1
            try:
                attempts += 1
                if attempts > 3:
                    raise Exception(
                        f"Failed to retrieve page {curr_page} after 3 attempts"
                    )
                logger.info(f"BODY PARAMS {params=}")
                part_result = self.api_call(
                    client_id=client_id,
                    url=del_config_obj.request_url,
                    api_config_object=del_config_obj,
                    body=params,
                    is_validation=is_validation,
                )
            except (ConnectionError, RemoteDisconnected) as e:
                curr_page -= 1
                self._log_exception_and_sleep(e)
                continue
            except ThirdPartyAPIException as e:
                curr_page -= 1
                if e.response.status_code in TIMEOUT_ERROR_CODES:
                    self._log_exception_and_sleep(e)
                    continue
                raise e
            # TODO: Remove this block
            except Exception as e:
                curr_page -= 1
                if ERR_CXN_ABORTED in str(e) or ERR_END_CLOSED in str(e):
                    self._log_exception_and_sleep(e)
                    continue
                raise e
            logger.info(
                f"Page - {curr_page}: Received successful response after {attempts} attempts"
            )
            attempts = 0

            data = part_result.get("results", [])
            for _data in data:
                deleted_date = _data.get("archivedAt")
                if deleted_date:
                    deleted_date = parse(deleted_date)
                    deleted_date = deleted_date.replace(tzinfo=pytz.UTC)
                    if deleted_date >= changes_start_time:
                        deleted_data = _data.get("properties", {})
                        deleted_data["Id"] = _data.get("id").lower()
                        page_records.append(deleted_data)
                        total_so_far = total_so_far + 1
            after = part_result.get("paging", {}).get("next", {}).get("after")
            params["after"] = after

            # is_validation is True then exit the loop to avoid unnecessary fetching of records.
            if is_validation:
                logger.info("Skipping further record fetching for validation")
                if snowflake_uploader:
                    snowflake_uploader.save_in_temp_table(page_records)
                    break
                else:
                    UpstreamETLStatusAccessor(
                        client_id=client_id,
                        e2e_sync_run_id=e2e_sync_run_id,
                        sync_run_id=sync_run_id,
                    ).update_object({"deleted_records_count": total_so_far})
                    # returning the first page's records (page_records) for validation as
                    # breaking the loop would not update the deleted_records which in turn would
                    # result in empty list.
                    return page_records
            if snowflake_uploader:
                snowflake_uploader.save_in_temp_table(page_records)
            elif upstream_etl_version == UpstreamETLVersions.V1.value:
                deleted_records.extend(page_records)
            else:
                logger.info(
                    f"BEGIN: Batch insert current page ({curr_page}) deleted records of length {len(page_records)} to extraction_sync_log"
                )
                insert_extract_sync_log(
                    client_id,
                    e2e_sync_run_id,
                    sync_run_id,
                    object_id,
                    page_records,
                    "delete",
                    source_primary_keys=source_primary_keys,
                )
                logger.info(
                    f"END: Batch insert current page ({curr_page}) deleted records of length {len(page_records)} to extraction_sync_log"
                )

            logger.info(
                f"No of records fetched so far - {total_so_far}, page - {curr_page}"
            )

        logger.info(f"Total deleted records for {object_id} => {total_so_far}")

        if snowflake_uploader and run_snowflake_sync:
            snowflake_uploader.save()

        logger.info("END: IMPORT DATA FROM HUBSPOT")

        UpstreamETLStatusAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        ).update_object({"deleted_records_count": total_so_far})

        return deleted_records

    def get_stages_records(self, pipeline_data):
        logger.info("Processing Stages data from Pipelines")
        all_stages = []
        pipelines = pipeline_data.get("results", [])
        for pipeline in pipelines:
            stages = pipeline.get("stages")
            for stage in stages:
                stage["pipeline_id"] = pipeline.get("id")
            all_stages = all_stages + stages
        return all_stages

    def parse_bulk_api_response(self, data):
        """
        parse the bulk api response and return the results
        In Enrichiment we expect the id to be present at flattened level
        so we need to parse the response and add the id to the flattened level
        Args:
            data: bulk api response
        Returns:
            parsed bulk api response
        """

        logger.info("BEGIN: Parsing bulk api response ")
        for rec in data["results"]:
            id_value = rec.get("from", {}).get("id")
            if id_value:
                rec["id"] = id_value
        logger.info("END: Parsing bulk api response ")
        return data

    def apply_api_enrichment(
        self,
        client_id,
        filtered_result,
        list_of_enrichment,
        is_validation,
        integration_id,
        current_page_records,
        visited,
    ):
        logger.info("BEGIN: Enrichment")
        api_config_source_ids = set()
        source_id_enrichment_map = {}
        is_association_params_req = dict()
        use_bulk_api_map = dict()

        for enr in list_of_enrichment:
            source_id = enr.ref_api_config_obj
            is_association_params_req[source_id] = (enr.additional_data or {}).get(
                "is_association"
            )
            api_config_source_ids.add(source_id)
            use_bulk_api_map[source_id] = (enr.additional_data or {}).get(
                "use_bulk_api", False
            )

            if source_id in source_id_enrichment_map:
                source_id_enrichment_map[source_id].append(enr)
            else:
                source_id_enrichment_map[source_id] = [enr]

        api_config_source_ids = list(api_config_source_ids)

        for source_id in api_config_source_ids:
            use_bulk_api = use_bulk_api_map[source_id]
            requests_body_params = {"inputs": []}
            if use_bulk_api:
                for (
                    item
                ) in current_page_records:  # id's to pass to get the associated data
                    requests_body_params["inputs"].append({"id": item})
            else:
                for item in visited:
                    requests_body_params["inputs"].append({"id": item})
            params = (
                requests_body_params if is_association_params_req[source_id] else None
            )
            config_obj = ApiAccessConfigAccessor(
                client_id
            ).get_obj_by_integration_id_and_source_id(integration_id, source_id)
            if not config_obj:
                logger.info(f"Config object not found for {source_id}")
                continue
            third_api_obj = ThirdPartyApi()
            data = None
            if not use_bulk_api:
                cache_key = (
                    f"{client_id}_{integration_id}_{config_obj.source_object_id}"
                )
                data = cache.get(cache_key)
            request_body = config_obj.request_body
            if params:
                request_body = params

            if not data:
                all_data = {"results": []}
                data_count = 0
                request_url = config_obj.request_url
                logger.info("BEGIN ENRICHMENT EXTRACTION")
                while True:
                    try:
                        logger.info(f"BODY PARAMS {request_body=}")
                        data = third_api_obj.api_call(
                            client_id,
                            request_url,
                            request_body,
                            config_obj,
                            is_validation=is_validation,
                        )
                    except (ConnectionError, RemoteDisconnected) as e:
                        self._log_exception_and_sleep(e)
                        continue
                    except ThirdPartyAPIException as e:
                        if e.response.status_code in TIMEOUT_ERROR_CODES:
                            self._log_exception_and_sleep(e)
                            continue
                        raise e
                    # TODO: Remove this block
                    except Exception as e:
                        if ERR_CXN_ABORTED in str(e) or ERR_END_CLOSED in str(e):
                            self._log_exception_and_sleep(e)
                            continue
                        raise e
                    if config_obj.response_key in data:
                        all_data["results"].extend(data["results"])
                        data_count += len(data["results"])
                    if "paging" in list(data.keys()):
                        request_url = data.get("paging", {}).get("next", {}).get("link")
                        logger.info(f"FETCHING ENRICHMENT DATA FROM {request_url}")
                    else:
                        request_url = None
                        logger.info("ENRICHMENT EXTRACTION COMPLETE!")

                    if request_url is None:
                        break

                    # is_validation is True then exit the loop to avoid unnecessary fetching of records.
                    if is_validation:
                        logger.info(
                            "Skipping further associations record fetching for validation"
                        )
                        break

                logger.info(f"TOTAL RECORDS FETCHED: {data_count}")

                if not is_validation and not use_bulk_api:
                    logger.info(f"Caching associations data for {cache_key}")
                    cache.set(cache_key, all_data, 3600)

                data = all_data

            if source_id == "pipeline_stages":
                # as there is no direct api to fetch stages record, we fetch pipeline data
                # and grp the stages records
                data = {"results": self.get_stages_records(pipeline_data=data)}
            if use_bulk_api:
                data = self.parse_bulk_api_response(data)
            filtered_result = apply_enrichments(
                data, filtered_result, source_id_enrichment_map[source_id]
            )
        logger.info("END: Applied Api enrichment!!")
        return filtered_result
