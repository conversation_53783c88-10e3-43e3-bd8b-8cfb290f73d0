import json
import logging
from datetime import datetime, timedelta
from importlib import import_module
from typing import List, Optional
from uuid import UUID

from rest_framework import status

from commission_engine.accessors.etl_config_accessor import ApiAccessConfigAccessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusAccessor,
)
from commission_engine.custom_exceptions.request_exceptions import (
    ThirdPartyAPIException,
)
from commission_engine.models.etl_config_models import ApiAccessConfig
from commission_engine.third_party_connections.third_party_api import ThirdPartyApi
from commission_engine.utils.general_data import UpstreamETLVersions
from commission_engine.utils.log_utils import merge_log_context
from interstage_project.threadlocal_log_context import (
    get_threadlocal_context,
    set_threadlocal_context,
)

logger = logging.getLogger(__name__)


class Gong(ThirdPartyApi):
    def _get_extractor_params(
        self, request_body: dict, primary_kd: datetime, changes_start_time: datetime
    ) -> dict:
        """
        Generate the parameters for the API request to extract data based on the provided request body,
        primary key date, and start time for changes since last sync.

        :param request_body: The request body for the API request.
        :param primary_kd: The primary key date for the sync.
        :param changes_start_time: The start time for the changes since last sync.
        :return: The modified request body with created[gte] and created[lte] filters.
        """

        start_time = (
            changes_start_time
            if changes_start_time
            else (primary_kd - timedelta(days=7))
        )
        request_body["filter"] = {
            "fromDateTime": start_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "toDateTime": primary_kd.strftime("%Y-%m-%dT%H:%M:%SZ"),
        }
        return request_body

    def _get_call_records(
        self, client_id: int, call_ids: List[str], config_object: ApiAccessConfig
    ):
        """
        Get call records from Gong API for the given call IDs.

        :param client_id: The client ID.
        :param call_ids: The list of call IDs.
        :param config_object: The API access config object.
        :return: The call records.
        """
        done = False
        curr_page = 0
        url_body_params = config_object.request_body
        url_body_params["filter"] = {"callIds": call_ids}
        call_records = []
        extraction_key = (
            config_object.response_key if config_object.response_key else "data"
        )
        logger.info(f"BEGIN: Fetching call records for {call_ids}")
        while not done:
            curr_page += 1
            logger.info(f"Fetching call records for page {curr_page}")
            try:
                res_obj = self.api_call(
                    client_id,
                    config_object.request_url,
                    url_body_params,
                    config_object,
                    is_validation=False,
                )
            except ThirdPartyAPIException as err:
                status_code = err.response.status_code
                api_response = err.response.json()
                logger.info(f"API Response: {api_response}")
                if status_code == status.HTTP_404_NOT_FOUND:
                    logger.info("No records found for the provided filter")
                    done = True
                    continue
                raise ThirdPartyAPIException(err.response) from err
            if res_obj["records"].get("cursor") is None:
                done = True
            else:
                url_body_params["cursor"] = res_obj["records"]["cursor"]

            call_records.extend(res_obj[extraction_key])
        logger.info(f"END: Fetching call records for {call_ids}")
        call_id_map = {}
        for call_record in call_records:
            call_id_map[call_record["metaData"]["id"]] = call_record
        return call_id_map

    def get_changed_records(
        self,
        client_id: int,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        object_id: str,
        primary_kd: datetime,
        changes_start_time: datetime,
        _unused_sync_mode: str,
        config_object: ApiAccessConfig,
        _unused_snapshot_key: str,
        _unused_destination_object_type: str,
        integration_id: UUID,
        is_validation: bool,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
    ) -> list[dict]:
        module_name = "everstage_etl.tasks.extraction"
        module = import_module(module_name)
        insert_extract_sync_log = getattr(module, "insert_extract_sync_log")

        if source_primary_keys is None:
            source_primary_keys = []
        old_log_context = get_threadlocal_context()
        new_log_context = {
            "client_id": client_id,
            "object_id": object_id,
            "changes_start_time": changes_start_time,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": sync_run_id,
        }

        merged_log_context = merge_log_context(old_log_context, new_log_context)

        set_threadlocal_context(merged_log_context)

        # If the API type is the events API, get the config object for the events API.

        # Get the extractor params for the API request.
        url_body_params = self._get_extractor_params(
            config_object.request_body,
            primary_kd,
            changes_start_time,
        )
        logger.info(
            f"Generated extractor params: {url_body_params} for the object - {object_id}"
        )

        # Loop through the API response pages until there are no more pages.
        done = False
        final_result = []
        curr_page = 0
        total_so_far = 0
        extraction_key = (
            config_object.response_key if config_object.response_key else "data"
        )
        is_transcript_object = config_object.additional_data.get(
            "is_transcript_object", False
        )
        while not done:
            page_records = []
            curr_page += 1
            res_obj = None
            logger.info("BEGIN: Fetching data from page %s", curr_page)
            try:
                res_obj = self.api_call(
                    client_id,
                    config_object.request_url,
                    url_body_params,
                    config_object,
                    is_validation=is_validation,
                )
            except ThirdPartyAPIException as err:
                status_code = err.response.status_code
                api_response = err.response.json()
                logger.info(f"API Response: {api_response}")
                if status_code == status.HTTP_404_NOT_FOUND:
                    logger.info("No records found for the provided filter")
                    done = True
                    continue
                raise ThirdPartyAPIException(err.response) from err

            logger.info("END: Fetching data from page %s", curr_page)
            # If the API response has data, extract the records from the response data.
            if len(res_obj[extraction_key]) > 0:
                if is_transcript_object:
                    logger.info(
                        "BEGIN: Chunking transcript records for page %s", curr_page
                    )
                    list_records = self._extract_transcript_records(
                        client_id,
                        object_id,
                        str(integration_id),
                        res_obj[extraction_key],
                    )
                    logger.info(
                        "END: Chunking transcript records for page %s", curr_page
                    )
                    total_so_far += len(list_records)
                    page_records.extend(list_records)
                else:
                    records = res_obj[extraction_key]
                    total_so_far += len(records)
                    page_records.extend(records)

            # is_validation is True then exit the loop to avoid unnecessary fetching of records.
            if is_validation:
                logger.info("Skipping further record fetching for validation")
                done = True

            # If the API response has no more pages, exit the loop.
            if res_obj["records"].get("cursor") is None:
                done = True
            else:
                url_body_params["cursor"] = res_obj["records"]["cursor"]

            if upstream_etl_version == UpstreamETLVersions.V1.value:
                final_result.extend(page_records)
            else:
                logger.info(
                    f"BEGIN: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                )
                insert_extract_sync_log(
                    client_id,
                    e2e_sync_run_id,
                    sync_run_id,
                    object_id,
                    page_records,
                    "change",
                    source_primary_keys=source_primary_keys,
                )
                logger.info(
                    f"END: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                )

        logger.info(f"Total Records for {object_id} - {total_so_far}")

        UpstreamETLStatusAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        ).update_object({"extracted_records_count": total_so_far})

        return final_result

    def _extract_crm_context_data(self, data):
        """
        Extracts system object types and their IDs from the given context.

        Args:
            context (list): A list of dictionaries containing system and object details.

        Returns:
            dict: A dictionary with keys as 'system_objecttype' in lowercase
                and values as the corresponding object IDs.
        """
        result = {}
        for entry in data:
            system = entry.get("system", "").lower()
            objects = entry.get("objects", [])

            for obj in objects:
                object_type = obj.get("objectType", "").lower()
                object_id = obj.get("objectId", "").lower()
                key = f"{system}_{object_type}"
                result[key] = object_id

        return result

    def _extract_user_details(self, data):
        """
        Extracts user details and maps them to their speakerId.

        Args:
            data (list): List of dictionaries containing user details.

        Returns:
            dict: A dictionary mapping speakerId to user details (name and email).
        """
        result = {}
        for entry in data:
            speaker_id = entry.get("speakerId")
            if not speaker_id:  # Ignore if speakerId is missing
                continue

            name = entry.get("name")
            email = entry.get(
                "emailAddress", None
            )  # Default to None if emailAddress is missing
            affiliation = entry.get("affiliation", "")
            if not affiliation or affiliation.lower() == "unknown":
                affiliation = "External"

            result[speaker_id] = {
                "speakerName": name,
                "speakerEmail": email,
                "speakerAffiliation": affiliation,
            }

        return result

    def _extract_transcript_records(
        self, client_id: int, object_id: str, integration_id: str, data_list
    ):
        """
        Extracts list type API records for an object ID from a list of API response data.
        If there is a api config record with the object_id + "_include_call", then this
        function will fetch the call records for the given call ids and add the call data
        to the transcript records.

        :param client_id: The client ID.
        :param object_id: The object ID to extract API records for.
        :param integration_id: The integration ID.
        :param data_list: The list of response data from the API.
        :return: A list of extracted API records.
        """
        all_call_ids = []
        additional_call_data_config = ApiAccessConfigAccessor(
            client_id
        ).get_obj_by_integration_id_and_source_id(
            integration_id, object_id.lower() + "_include_call"
        )
        if additional_call_data_config:
            logger.info(
                f"Found additional call data config for {object_id} with integration_id {integration_id}"
            )
            for obj in data_list:
                all_call_ids.append(obj["callId"])
            call_id_map = self._get_call_records(
                client_id, all_call_ids, additional_call_data_config
            )

        result = []
        for obj in data_list:
            ## we get call transcript for a call
            call_id = obj["callId"]
            chunk_id = 0
            for transcript_chunk in obj["transcript"]:
                if additional_call_data_config:
                    call_data = call_id_map.get(call_id, {})
                    parties_data = self._extract_user_details(
                        call_data.get("parties", [])
                    )
                    # add metaData to transcript_chunk
                    if call_data.get("metaData"):
                        for key, value in call_data.get("metaData").items():
                            transcript_chunk[key] = value

                    # add crm context data to transcript_chunk
                    if call_data.get("context"):
                        context_data = self._extract_crm_context_data(
                            call_data.get("context")
                        )
                        for key, value in context_data.items():
                            transcript_chunk[key] = value

                    # add user details to transcript_chunk
                    if parties_data.get(transcript_chunk.get("speakerId")):
                        for key, value in parties_data.get(
                            transcript_chunk.get("speakerId"), {}
                        ).items():
                            transcript_chunk[key] = value
                chunk_id += 1
                transcript_chunk["callId"] = call_id
                transcript_chunk["sentences"] = json.dumps(
                    transcript_chunk["sentences"]
                )
                transcript_chunk["chunkId"] = chunk_id
                transcript_chunk["is_last_chunk"] = (
                    True if chunk_id == len(obj["transcript"]) else False
                )
                result.append(transcript_chunk)
        return result
