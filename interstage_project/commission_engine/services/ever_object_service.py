from types import SimpleNamespace

from commission_engine.accessors.client_accessor import (
    can_run_auto_enrich_report,
    get_payout_approvals_flag,
    get_statement_approval_flag,
)
from commission_engine.accessors.ever_object_accessor import (
    EverObjectAccessor,
    EverObjectVariableAccessor,
)
from commission_engine.accessors.report_enrichment_accessor import (
    CommissionReportEnrichmentAccessor,
    get_auto_enrich_variables,
)
from commission_engine.pydantic_serializers.ever_object import (
    EverObjectVariablesListResponseWithSource,
)
from commission_engine.services.client_feature_service import (
    check_client_features_exist,
)
from commission_engine.utils.report_utils import (
    get_other_module_reports,
    get_report_variables,
)
from spm.accessors.custom_field_accessor import CustomFieldsAccessor


def get_all_objects(client_id):
    feature_dict = check_client_features_exist(
        client_id,
        [
            "expose_comm_reports_in_plan",
            "show_forecast",
            "show_payout_report_object",
            "show_fx_rate_report_object",
            "show_approvals_report_object",
        ],
    )
    expose_comm_reports_in_plan = feature_dict.get("expose_comm_reports_in_plan", False)
    show_forecast = feature_dict.get("show_forecast", False)
    show_payout_report_object = feature_dict.get("show_payout_report_object", False)
    show_fx_rate_report_object = feature_dict.get("show_fx_rate_report_object", False)
    show_approvals_report_object = feature_dict.get(
        "show_approvals_report_object", False
    )
    data_origin_to_exclude = set()
    object_id_to_exclude = set()

    object_id_to_exclude.update(get_other_module_reports(client_id))
    if not expose_comm_reports_in_plan:
        data_origin_to_exclude.add("inter_object")
        data_origin_to_exclude.add("inter_forecast_object")
    if not show_forecast:
        data_origin_to_exclude.add("forecast_object")
        data_origin_to_exclude.add("inter_forecast_object")
    if not show_payout_report_object:
        object_id_to_exclude.add("payout")
    if not show_fx_rate_report_object:
        object_id_to_exclude.add("fx_rate")
    if not (show_approvals_report_object and get_payout_approvals_flag(client_id)):
        object_id_to_exclude.add("statement_approvals")
    else:
        if not get_statement_approval_flag(client_id):
            object_id_to_exclude.add("statement_approvals")

    query = EverObjectAccessor().ever_obj_kd_aware().order_by("name")

    if data_origin_to_exclude:
        query = query.exclude(data_origin__in=data_origin_to_exclude)

    if object_id_to_exclude:
        query = query.exclude(ever_object_id__in=object_id_to_exclude)

    return list(query.values())


def get_all_variables_for_object(
    *, ever_object_id, client_id
) -> EverObjectVariablesListResponseWithSource:
    ever_object_record = EverObjectAccessor().get_ever_objs(object_id=ever_object_id)

    variables = get_report_variables(ever_object_id, client_id=client_id)

    filtered_variables = []
    used_system_names = set()
    for variable in variables:
        if variable["system_name"] in used_system_names:
            continue
        used_system_names.add(variable["system_name"])
        variable["source_id"] = ever_object_id
        variable["source_name"] = ever_object_record.name
        variable["is_primary"] = (
            variable["system_name"] in ever_object_record.primary_key
        )
        variable["variable_id"] = _construct_variable_id(
            system_name=variable["system_name"], ever_object_id=ever_object_id
        )

        variable["source_name_history"] = (
            f"{variable['display_name']} << {ever_object_record.name}"
        )
        filtered_variables.append(variable)
    return EverObjectVariablesListResponseWithSource(filtered_variables)


def get_primary_key(*, ever_object_id):
    """
    Returns the primary key for the ever object id
    """
    return EverObjectAccessor().get_primary_key(ever_object_id)


def get_ever_object_by_id(*, ever_object_id):
    """
    Returns the ever object by id
    """
    return EverObjectAccessor().get_ever_objs(object_id=ever_object_id)


def get_report_object_variable(*, client_id, ever_object_id, variable_id):
    """
    Returns the report object variable by variable id
    """

    system_name = _get_system_name_from_variable_id(
        variable_id=variable_id, ever_object_id=ever_object_id
    )
    variable = EverObjectVariableAccessor().get_variable_by_system_name(
        ever_object_id, system_name
    )
    if variable:
        return variable
    if ever_object_id == "user":
        return CustomFieldsAccessor(
            client_id=client_id
        ).get_latest_custom_field_by_system_name(system_name)

    if ever_object_id in ["commission", "inter_commission"]:
        if can_run_auto_enrich_report(client_id):
            cre_variables = get_auto_enrich_variables(client_id)
            for variable in cre_variables:
                if str(variable["report_system_name"]) == system_name:
                    return SimpleNamespace(**variable)
            return SimpleNamespace(display_name="Deleted field")

        return (
            CommissionReportEnrichmentAccessor(client_id)
            .get_record_by_report_system_name(system_name)
            .filter(report_type=ever_object_id)
            .first()
        )

    raise ValueError(f"{system_name} not present in {ever_object_id}")


def get_report_object_variable_display_name(*, client_id, ever_object_id, variable_id):
    """
    Returns the display name of the variable for the given ever object id
    """
    return get_report_object_variable(
        client_id=client_id, ever_object_id=ever_object_id, variable_id=variable_id
    ).display_name


def _construct_variable_id(*, system_name, ever_object_id):
    """
    Constructs the variable name
    """
    return f"{system_name}_{ever_object_id}"


def _get_system_name_from_variable_id(*, variable_id, ever_object_id):
    """
    Returns the system name from the by removing last part of ever object id from variable id

    variable_id is constructed for ever object by combination of system name and ever object id

    Example:

    system name : employee_email_id

    ever object id : quota

    variable_id : employee_email_id_quota

    """
    return variable_id.rsplit(f"_{ever_object_id}", 1)[0]
