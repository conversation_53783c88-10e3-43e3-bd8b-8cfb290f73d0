import logging
from collections import OrderedDict, defaultdict
from typing import Optional
from uuid import UUID

from django.core.cache import cache

from commission_engine.accessors.client_accessor import get_payout_report_object_enabled
from commission_engine.accessors.databook_accessor import DatabookAccessor
from commission_engine.accessors.etl_config_accessor import ExtractionConfigAccessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    CommissionETLStatusReaderAccessor,
    DatabookETLStatusReaderAccessor,
    ETLSyncStatusReaderAccessor,
    ForecastETLStatusReaderAccessor,
    PayoutSnapshotETLStatusReaderAccessor,
    PlanModificationSyncStatusAccessor,
    ReportETLStatusReaderAccessor,
    SettlementETLStatusReaderAccessor,
    SettlementSnapshotETLStatusReaderAccessor,
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.databook_etl_sync_status_service import (
    get_records_for_e2e_task_sync_type,
)
from commission_engine.services.etl_sync_status_service import (
    get_recent_data_sync_status,
    is_sync_status_exist,
)
from commission_engine.services.report_etl_service import should_run_settlement_report
from commission_engine.utils.databook_utils import get_associated_report_objects
from commission_engine.utils.general_data import (
    ETL_ACTIVITY,
    ETL_STATUS,
    ETL_UI_STATUS,
    SYNC_OBJECT,
)
from commission_engine.utils.report_utils import get_report_object_ids
from everstage_ddd.upstream.fivetran_webhook.fivetran_sync_log_accessor import (
    FivetranSyncLogAccessor,
)
from interstage_project.utils import get_cache_key_for_datasheet_sync_details

logger = logging.getLogger(__name__)


def get_task_status(status: str) -> str:
    # use etl_sync_ui_task_status_map function instead of this
    if status in [
        ETL_STATUS.STARTED.value,
        ETL_STATUS.LOCKED.value,
        ETL_STATUS.TRANSFORMATION.value,
        ETL_STATUS.EXTRACTION.value,
        ETL_STATUS.LOADING.value,
    ]:
        task_status = "process"
    elif status in [
        ETL_STATUS.FAILED.value,
        ETL_STATUS.LOCK_FAILED.value,
        ETL_STATUS.PARTIALLY_FAILED.value,
    ]:
        task_status = "error"
    elif status in [
        ETL_STATUS.SUCCESS.value,
        ETL_STATUS.COMPLETE.value,
    ]:
        task_status = "finish"
    elif status in [ETL_STATUS.SKIPPED.value]:
        task_status = "skipped"
    else:
        task_status = "wait"

    return task_status


def etl_sync_ui_task_status_map():
    """
    Map of UI status with actual sync status
    """
    ui_status_map = defaultdict(lambda: ETL_UI_STATUS.WAIT.value)
    ui_status_map.update(
        {
            ETL_STATUS.STARTED.value: ETL_UI_STATUS.PROCESS.value,
            ETL_STATUS.LOCKED.value: ETL_UI_STATUS.PROCESS.value,
            ETL_STATUS.TRANSFORMATION.value: ETL_UI_STATUS.PROCESS.value,
            ETL_STATUS.EXTRACTION.value: ETL_UI_STATUS.PROCESS.value,
            ETL_STATUS.LOADING.value: ETL_UI_STATUS.PROCESS.value,
            ETL_STATUS.FAILED.value: ETL_UI_STATUS.ERROR.value,
            ETL_STATUS.LOCK_FAILED.value: ETL_UI_STATUS.ERROR.value,
            ETL_STATUS.PARTIALLY_FAILED.value: ETL_UI_STATUS.ERROR.value,
            ETL_STATUS.SUCCESS.value: ETL_UI_STATUS.FINISH.value,
            ETL_STATUS.COMPLETE.value: ETL_UI_STATUS.FINISH.value,
            ETL_STATUS.SKIPPED.value: ETL_UI_STATUS.SKIPPED.value,
        }
    )
    return ui_status_map


def get_status_from_set(status_set: set) -> str:
    if "error" in status_set:
        return "error"
    elif len(status_set) == 1 and "finish" in status_set:
        return "finish"
    elif "process" in status_set:
        return "process"
    elif "wait" in status_set:
        if "finish" in status_set:
            return "process"
    elif "skipped" in status_set:
        return "skipped"
    else:
        return "wait"


def etl_record_template(total_count: int = 0, records: bool = True) -> dict:
    etl_record = {
        "total": {"count": total_count},
        "wait": {"count": 0},
        "process": {"count": 0},
        "finish": {"count": 0},
        "error": {"count": 0},
        "skipped": {"count": 0},
    }
    if records:
        etl_record["wait"]["records"] = []
        etl_record["process"]["records"] = []
        etl_record["finish"]["records"] = []
        etl_record["error"]["records"] = []
        etl_record["skipped"]["records"] = []

    return etl_record


def upstream_task_details(
    client_id: int, e2e_sync_run_id: str, extraction_config_tasks: dict[str, str]
) -> dict:
    """Utility method to return e2e upstream task details

    Arguments:
    client_id: int
    e2e_sync_run_id: UUID string
    extraction_config_tasks: dict of integration_id and task name

    Return: Dict of total, waiting, processing, finished, failed process

    Example:
    {
        "total": {"count": 4},
        "wait": {"count": 1, "records": ["OPPORTUNITY_SYNC"]},
        "process": {"count": 1, "records": ["LEAD_SYNC"]},
        "finish": {"count": 1, "records": ["CONTACT_SYNC"]},
        "error": {"count": 1, "records": ["ACTIVITY_SYNC"]},
        "skipped": {"count": 1, "records": ["COMPANIES_SYNC"]},
    }
    """
    if not e2e_sync_run_id:
        return etl_record_template(total_count=0)
    usra = UpstreamETLStatusReaderAccessor(client_id)
    e2e_upstream_record = etl_record_template(total_count=len(extraction_config_tasks))
    all_skipped_tasks = (
        FivetranSyncLogAccessor(client_id)
        .get_records_by_e2e_sync_id_and_status(
            e2e_sync_run_id, ETL_STATUS.SKIPPED.value
        )
        .values_list("sync_status", flat=True)
    )
    e2e_upstream_record["skipped"].update({"count": len(all_skipped_tasks)})

    upstream_tasks = {
        str(task["integration_id"]): str(task["sync_status"])
        for task in usra.get_records_for_e2e_sync_run_id(
            e2e_sync_run_id, values=["object_id", "sync_status", "integration_id"]
        )
        if str(task["object_id"]) != SYNC_OBJECT.UPSTREAM_WRAPPER.value
    }

    # check status of all the extraction config tasks for the e2e_sync_run_id
    for integration_id, task_name in extraction_config_tasks.items():
        task_status = get_task_status(upstream_tasks.get(integration_id))
        e2e_upstream_record[task_status]["records"].append(task_name)
        e2e_upstream_record[task_status]["count"] += 1

    return e2e_upstream_record


def datasheet_task_details(
    client_id: int,
    e2e_sync_run_id: str,
    sync_type: str = None,  # type: ignore
) -> dict:
    """Utility method to return e2e databook task details

    Arguments:
    client_id: int
    e2e_sync_run_id: UUID string
    databook_ids_list: list of databook ids (str) for the client_id

    Return: Dict of total, waiting, processing, finished, failed process

    Example:
    {
        "total": {"count": 4},
        "wait": {"count": 1, "records": ["95b31a1a-16f5-4318-a43f-3c45173386b3"]},
        "process": {"count": 1, "records": ["95b31a1a-16f5-4318-a43f-3c45173386b4"]},
        "finish": {"count": 1, "records": ["95b31a1a-16f5-4318-a43f-3c45173386b5"]},
        "error": {"count": 1, "records": ["95b31a1a-16f5-4318-a43f-3c45173386b6"]},
    }
    """

    e2e_databook_record: dict = etl_record_template(total_count=0)
    e2e_databook_record["show_spinner"] = False

    if not e2e_sync_run_id:
        return e2e_databook_record

    sync_details = get_records_for_e2e_task_sync_type(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        task=SYNC_OBJECT.DATASHEET_WRAPPER_SYNC.value,
        sync_type=sync_type,
        values=["sync_run_id"],
    )
    if len(sync_details) == 0:
        # spinner will be shown in UI till the records are populated in queue
        e2e_databook_record["show_spinner"] = True
        return e2e_databook_record

    # Cache contains the details of stale and total datasheet counts to be processed
    # {'datasheet_count': 16, 'stale_datasheet_count': 12}
    sync_run_id = str(sync_details[0]["sync_run_id"])
    datasheet_sync_details = _get_datasheet_sync_details_from_cache(sync_run_id)

    dbsra = DatabookETLStatusReaderAccessor(client_id)
    datasheet_sync_status: dict = dbsra.get_datasheet_sync_status(
        e2e_sync_run_id=e2e_sync_run_id, sync_type=sync_type
    )

    if datasheet_sync_details:
        datasheet_count = datasheet_sync_details["datasheet_count"]
        stale_datasheet_count = datasheet_sync_details["stale_datasheet_count"]
    else:
        # else code should not be reached since cache is always present when the queue is populated
        logger.error(
            "Datasheet Task Details: Cache not found for e2e sync run id %s--%s",
            e2e_sync_run_id,
            sync_run_id,
        )
        datasheet_count = stale_datasheet_count = len(datasheet_sync_status)

    e2e_databook_record["message"] = (
        f"Out of {datasheet_count} datasheets none of them are stale"
        if stale_datasheet_count == 0
        else f"{stale_datasheet_count} out of {datasheet_count} datasheets have stale data and need to be re-generated."
    )

    e2e_databook_record["total"]["count"] = stale_datasheet_count

    ui_status_map: defaultdict = etl_sync_ui_task_status_map()

    for datasheet_id, status in datasheet_sync_status.items():
        ui_status = ui_status_map[status]
        e2e_databook_record[ui_status]["records"].append(str(datasheet_id))
        e2e_databook_record[ui_status]["count"] += 1

    from everstage_etl.services.datasheet_execution_context_service import (
        retrieve_datasheet_execution_context,
    )

    datasheet_execution_context = retrieve_datasheet_execution_context(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
    )
    skipped_datasheet_ids = datasheet_execution_context.skipped_stale_datasheets
    skipped_datasheet_count = len(skipped_datasheet_ids)
    e2e_databook_record["skipped"]["count"] = skipped_datasheet_count
    e2e_databook_record["skipped"]["records"] = skipped_datasheet_ids
    return e2e_databook_record


def _get_datasheet_sync_details_from_cache(sync_run_id) -> dict:
    # To Do: Cache is not working as expected in unit test
    # Seperate function is written required to mock cache in unit test
    cache_key = get_cache_key_for_datasheet_sync_details(sync_run_id)
    return cache.get(cache_key)


def commission_task_details(client_id: int, e2e_sync_run_id: str, level=None) -> dict:
    """Utility method to return e2e commission task details

    Arguments:
    client_id: int
    e2e_sync_run_id: UUID string

    Return: Dict of total, waiting, processing, finished, failed process

    Example:
    {
        "total": {"count": 4},
        "wait": {"count": 1},
        "process": {"count": 1},
        "finish": {"count": 1},
        "error": {"count": 1},
    }
    """
    csra = CommissionETLStatusReaderAccessor(client_id)
    e2e_commission_record = etl_record_template(records=False)

    if not e2e_sync_run_id:
        return e2e_commission_record

    if level:
        tasks = [
            SYNC_OBJECT.COMMISSION_PAYEE_SYNC_L1.value,
            SYNC_OBJECT.TEAM_PAYEE_SYNC_L1.value,
        ]
        wrapper_tasks = [
            SYNC_OBJECT.COMMISSION_WRAPPER_SYNC_L1.value,
            SYNC_OBJECT.TEAM_CRITERIA_WRAPPER_L1.value,
        ]
    else:
        tasks = [
            SYNC_OBJECT.COMMISSION_PAYEE_SYNC.value,
            SYNC_OBJECT.TEAM_PAYEE_SYNC.value,
        ]
        wrapper_tasks = [
            SYNC_OBJECT.COMMISSION_WRAPPER_SYNC.value,
            SYNC_OBJECT.TEAM_CRITERIA_WRAPPER.value,
        ]

    try:
        total_payee_sync_job_count = int(
            csra.job_count_sum_for_e2e(e2e_sync_run_id, wrapper_tasks)
        )
        e2e_commission_record["total"]["count"] = total_payee_sync_job_count
    except TypeError:
        return e2e_commission_record

    payee_sync_objects = list(
        csra.get_task_and_status_for_task_list_no_plan_id(
            e2e_sync_run_id,
            tasks,
        )
    )

    payee_sync_job_count = 0
    for payee_sync_object in payee_sync_objects:
        # payee_sync_object example value -> ('COMMISSION_PAYEE_SYNC', 'complete')
        task_status = get_task_status(str(payee_sync_object[1]))
        e2e_commission_record[task_status]["count"] += 1
        payee_sync_job_count += 1
    e2e_commission_record["wait"]["count"] = (
        total_payee_sync_job_count - payee_sync_job_count
    )

    return e2e_commission_record


def forecast_task_details(
    client_id: int, e2e_sync_run_id: str | UUID, level=None
) -> dict:
    """Utility method to return e2e forecast sync task details

    Arguments:
    client_id: int
    e2e_sync_run_id: UUID string

    Return: Dict of total, waiting, processing, finished, failed process

    Example:
    {
        "total": {"count": 4},
        "wait": {"count": 1},
        "process": {"count": 1},
        "finish": {"count": 1},
        "error": {"count": 1},
    }
    """
    fsra = ForecastETLStatusReaderAccessor(client_id)
    e2e_forecast_record = etl_record_template(records=False)

    if not e2e_sync_run_id:
        return e2e_forecast_record

    if level:
        tasks = [
            SYNC_OBJECT.FORECAST_PAYEE_SYNC_L1.value,
            SYNC_OBJECT.FORECAST_TEAM_PAYEE_SYNC_L1.value,
        ]
        wrapper_tasks = [
            SYNC_OBJECT.FORECAST_WRAPPER_SYNC_L1.value,
            SYNC_OBJECT.FORECAST_TEAM_CRITERIA_WRAPPER_L1.value,
        ]
    else:
        tasks = [
            SYNC_OBJECT.FORECAST_PAYEE_SYNC.value,
            SYNC_OBJECT.FORECAST_TEAM_PAYEE_SYNC.value,
        ]
        wrapper_tasks = [
            SYNC_OBJECT.FORECAST_WRAPPER_SYNC.value,
            SYNC_OBJECT.FORECAST_TEAM_CRITERIA_WRAPPER.value,
        ]

    try:
        total_payee_sync_job_count = int(
            fsra.job_count_sum_for_e2e(e2e_sync_run_id, wrapper_tasks)
        )
        e2e_forecast_record["total"]["count"] = total_payee_sync_job_count
    except TypeError:
        return e2e_forecast_record

    payee_sync_objects = list(
        fsra.get_task_and_status_for_task_list_no_plan_id(
            e2e_sync_run_id,
            tasks,
        )
    )

    payee_sync_job_count = 0
    for payee_sync_object in payee_sync_objects:
        # payee_sync_object example value -> ('FORECAST_PAYEE_SYNC', 'complete')
        task_status = get_task_status(str(payee_sync_object[1]))
        e2e_forecast_record[task_status]["count"] += 1
        payee_sync_job_count += 1
    e2e_forecast_record["wait"]["count"] = (
        total_payee_sync_job_count - payee_sync_job_count
    )

    return e2e_forecast_record


def settlement_task_details(client_id: int, e2e_sync_run_id: str) -> dict:
    """Utility method to return e2e commission task details

    Arguments:
    client_id: int
    e2e_sync_run_id: UUID string

    Return: Dict of total, waiting, processing, finished, failed process

    Example:
    {
        "total": {"count": 4},
        "wait": {"count": 1},
        "process": {"count": 1},
        "finish": {"count": 1},
        "error": {"count": 1},
    }
    """
    csra = SettlementETLStatusReaderAccessor(client_id)
    e2e_commission_record = etl_record_template(records=False)

    if not e2e_sync_run_id:
        return e2e_commission_record

    tasks = [
        SYNC_OBJECT.SETTLEMENT_SYNC.value,
        SYNC_OBJECT.MIRROR_COMMISSION_DATA_TO_SETTLEMENT.value,
    ]
    wrapper_tasks = [
        SYNC_OBJECT.SETTLEMENT_WRAPPER_SYNC.value,
    ]

    try:
        total_payee_sync_job_count = int(
            csra.job_count_sum_for_e2e(e2e_sync_run_id, wrapper_tasks)
        )
        e2e_commission_record["total"]["count"] = total_payee_sync_job_count
    except TypeError:
        return e2e_commission_record

    payee_sync_objects = list(
        csra.get_task_and_status_for_task_list(
            e2e_sync_run_id,
            tasks,
        )
    )

    payee_sync_job_count = 0
    for payee_sync_object in payee_sync_objects:
        # payee_sync_object example value -> ('COMMISSION_PAYEE_SYNC', 'complete')
        task_status = get_task_status(str(payee_sync_object[1]))
        e2e_commission_record[task_status]["count"] += 1
        payee_sync_job_count += 1
    e2e_commission_record["wait"]["count"] = (
        total_payee_sync_job_count - payee_sync_job_count
    )

    return e2e_commission_record


def settlement_snapshot_task_details(
    client_id: int, e2e_sync_run_id: str
) -> dict | None:
    ssra = SettlementSnapshotETLStatusReaderAccessor(client_id)
    e2e_snapshot_record = etl_record_template(records=False)

    if not e2e_sync_run_id:
        return e2e_snapshot_record

    wrapper_tasks = [SYNC_OBJECT.SETTLEMENT_SNAPSHOT_WRAPPER_SYNC.value]
    tasks = [SYNC_OBJECT.SETTLEMENT_SNAPSHOT_SYNC.value]

    try:
        total_snapshot_job_count = int(
            ssra.job_count_sum_for_e2e(e2e_sync_run_id, wrapper_tasks)
        )
        e2e_snapshot_record["total"]["count"] = total_snapshot_job_count
    except TypeError:
        # TypeError is raised when there is no snapshot tasks for the given e2e_sync_run_id
        logger.error("Error while fetching settlement snapshot task details")
        return None

    snapshot_tasks = list(
        ssra.get_records_by_e2e_sync_run_id_and_task(e2e_sync_run_id, task_list=tasks)
    )
    snapshot_tasks_count = 0
    for snapshot_task in snapshot_tasks:
        task_status = get_task_status(str(snapshot_task[1]))
        e2e_snapshot_record[task_status]["count"] += 1
        snapshot_tasks_count += 1
    e2e_snapshot_record["wait"]["count"] = (
        total_snapshot_job_count - snapshot_tasks_count
    )

    return e2e_snapshot_record


def payout_snapshot_task_details(client_id: int, e2e_sync_run_id: str) -> dict | None:
    ssra = PayoutSnapshotETLStatusReaderAccessor(client_id)
    e2e_snapshot_record = etl_record_template(records=False)

    if not e2e_sync_run_id:
        return e2e_snapshot_record

    wrapper_tasks = [SYNC_OBJECT.PAYOUT_SNAPSHOT_WRAPPER_SYNC.value]
    tasks = [
        SYNC_OBJECT.PAYOUT_SNAPSHOT_SYNC.value,
        SYNC_OBJECT.DELETE_PAYOUT_SNAPSHOT_SYNC.value,
        SYNC_OBJECT.PAYOUT_SNAPSHOT_COMM_SYNC.value,
    ]

    try:
        total_snapshot_job_count = int(
            ssra.job_count_sum_for_e2e(e2e_sync_run_id, wrapper_tasks)
        )
        e2e_snapshot_record["total"]["count"] = total_snapshot_job_count
    except TypeError:
        # TypeError is raised when there is no snapshot tasks for the given e2e_sync_run_id
        return None

    snapshot_tasks = list(
        ssra.get_records_by_e2e_sync_run_id_and_task(e2e_sync_run_id, task_list=tasks)
    )
    snapshot_tasks_count = 0
    for snapshot_task in snapshot_tasks:
        task_status = get_task_status(str(snapshot_task[1]))
        e2e_snapshot_record[task_status]["count"] += 1
        snapshot_tasks_count += 1
    e2e_snapshot_record["wait"]["count"] = (
        total_snapshot_job_count - snapshot_tasks_count
    )

    return e2e_snapshot_record


def report_task_details(
    client_id: int,
    e2e_sync_run_id: str,
    report_objects: list = None,  # type: ignore
    params: dict = None,  # type: ignore
) -> dict:
    """Utility method to return e2e report task details

    Arguments:
    client_id: int
    e2e_sync_run_id: UUID string

    Return: Dict of total, waiting, processing, finished, failed process

    Example:
    {
        "total": {"count": 2},
        "wait": {"count": 1, "records": ["COMMISSION_REPORT_SYNC"]},
        "process": {"count": 0, "records": []},
        "finish": {"count": 1, "records": ["QUOTA_REPORT_SYNC"]},
        "error": {"count": 0, "records": []},
    }
    """
    if not params:
        params = {}
    if not e2e_sync_run_id:
        return etl_record_template(total_count=0)
    rsra = ReportETLStatusReaderAccessor(client_id)

    if not report_objects:
        report_objects = []

    report_etl_objects = rsra.get_records_for_e2e_sync_run_id_and_task_list(
        e2e_sync_run_id,
        report_objects,
        values=["task", "sync_status", "object_id"],
    )
    report_etl_dict = {}
    for obj in report_etl_objects:
        report_etl_dict[obj["object_id"]] = obj
    e2e_report_record = etl_record_template(
        total_count=params.get("run_report_sync", 0)
    )
    for report in report_objects:
        sync_status = (
            report_etl_dict[report]["sync_status"]
            if report in report_etl_dict
            else "wait"
        )
        task_status = get_task_status(sync_status)
        e2e_report_record[task_status]["count"] += 1
        e2e_report_record[task_status]["records"].append(report)

    return e2e_report_record


def get_e2e_detailed_status(client_id: int) -> list[dict]:
    """
    Returns list of dicts containing status of Upstream, Databook and Commission ETL status
    """
    object_id = SYNC_OBJECT.UPSTREAM_WRAPPER.value
    usra = UpstreamETLStatusReaderAccessor(client_id)
    upstream_etl_objects = usra.get_last_n_e2e_syncs(1)
    last_n_e2e_sync_ids = [
        str(upstream_etl_object["e2e_sync_run_id"])
        for upstream_etl_object in upstream_etl_objects
    ]
    extraction_config_tasks = {
        str(task.integration_id): str(task.task)
        for task in ExtractionConfigAccessor(client_id).get_tasks_for_task_group(
            object_id
        )
        if str(task.task) != object_id
    }

    databook_ids_list = DatabookAccessor(client_id).get_all_databook_ids()

    has_db_comm_etl = bool(has_feature(client_id, "is_db_comm_etl"))

    final_list = []
    for e2e_sync_run_id in last_n_e2e_sync_ids:
        e2e_record = {"e2e_sync_run_id": e2e_sync_run_id}

        # Get upstream task details
        e2e_record["upstream_sync"] = upstream_task_details(
            client_id, e2e_sync_run_id, extraction_config_tasks
        )

        # Get databook task details - Custom object sync type
        e2e_record["datasheet_custom_object_sync"] = (
            datasheet_task_details(
                client_id,
                e2e_sync_run_id,
                SYNC_OBJECT.DATABOOK_CUSTOM_OBJECT_SYNC.value,
            )
            if has_db_comm_etl
            else etl_record_template()
        )

        # Get details of Plan Modification Sync
        # plan_modification_sync_details = get_plan_modification_sync_details(
        #     client_id, e2e_sync_run_id
        # )
        # if plan_modification_sync_details and can_run_sf_snapshot(client_id):
        #     e2e_record["plan_modification_sync"] = plan_modification_sync_details

        # Get commission task details
        e2e_record["commission_sync"] = (
            commission_task_details(client_id, e2e_sync_run_id)
            if has_db_comm_etl
            else etl_record_template(records=False)
        )

        # inter_comm_snapshot_sync_details = inter_comm_snapshot_sync_task_details(
        #     client_id, e2e_sync_run_id
        # )
        # if inter_comm_snapshot_sync_details and can_run_sf_snapshot(client_id):
        #     e2e_record["inter_comm_snapshot_sync"] = inter_comm_snapshot_sync_details

        # Get snapshot task details
        snapshot_task_details_record = payout_snapshot_task_details(
            client_id, e2e_sync_run_id
        )
        if snapshot_task_details_record is not None:
            e2e_record["payout_snapshot_sync"] = snapshot_task_details_record

        # Get settlement snapshot task details
        settlement_snapshot_task_details_record = settlement_snapshot_task_details(
            client_id, e2e_sync_run_id
        )
        if settlement_snapshot_task_details_record is not None:
            e2e_record["settlement_snapshot_sync"] = (
                settlement_snapshot_task_details_record
            )

        report_objs = []
        if databook_ids_list:
            report_objs = get_associated_report_objects(client_id, databook_ids_list)

        # Get report task details
        e2e_record["report_sync"] = report_task_details(
            client_id,
            e2e_sync_run_id,
            report_objs,
            {"run_report_sync": len(report_objs)},
        )

        # Get databook task details - Report object sync type
        e2e_record["datasheet_report_object_sync"] = datasheet_task_details(
            client_id,
            e2e_sync_run_id,
            SYNC_OBJECT.DATABOOK_REPORT_OBJECT_SYNC.value,
        )

        final_list.append(e2e_record)

    return final_list


def get_conclusive_status_for_wrapper_type(client_id: int, e2e_sync_run_id: str) -> str:
    if is_sync_status_exist(client_id, e2e_sync_run_id):
        etl_sync_status = ETLSyncStatusReaderAccessor(
            client_id
        ).get_etl_sync_status_record_by_id(e2e_sync_run_id)
        return get_task_status(etl_sync_status["sync_status"])
    return "finish"


def get_connector_e2e_sync_detailed_status(
    client_id: int,
    e2e_sync_run_id: UUID,
    all_objects_selected: bool = True,
    integration_ids: list[str] = None,
) -> dict:
    upstream_sync_required = has_feature(client_id, "is_upstream_etl")
    post_upstream_required = has_feature(client_id, "is_db_comm_etl")
    if upstream_sync_required is None:
        upstream_sync_required = True
    if post_upstream_required is None:
        post_upstream_required = True
    e2e_record = {"e2e_sync_run_id": str(e2e_sync_run_id)}

    if upstream_sync_required:
        object_id = SYNC_OBJECT.UPSTREAM_WRAPPER.value
        extraction_config_tasks = []

        if all_objects_selected:
            extraction_config_tasks = {
                str(task.integration_id): str(task.task)
                for task in ExtractionConfigAccessor(
                    client_id
                ).get_tasks_for_task_group(object_id)
                if str(task.task) != object_id
            }
        else:
            extraction_config_tasks = {
                str(task.integration_id): str(task.task)
                for task in ExtractionConfigAccessor(
                    client_id
                ).get_selected_tasks_for_task_group(object_id, integration_ids)
                if str(task.task) != object_id
            }

        e2e_record["upstream_sync"] = upstream_task_details(
            client_id, e2e_sync_run_id, extraction_config_tasks
        )

    if post_upstream_required:
        databook_ids = DatabookAccessor(client_id).get_all_databook_ids()
        e2e_record.update(
            get_commission_calculation_detailed_status(
                client_id, e2e_sync_run_id, databook_ids, connector_sync=True
            )
        )

    return e2e_record


def get_connector_upstream_sync_detailed_status(
    client_id: int,
    e2e_sync_run_id: UUID,
    all_objects_selected: bool = True,
    integration_ids: list[str] = None,
) -> dict:
    upstream_sync_required = has_feature(client_id, "is_upstream_etl")
    if upstream_sync_required is None:
        upstream_sync_required = True
    e2e_record = {"e2e_sync_run_id": str(e2e_sync_run_id)}

    if upstream_sync_required:
        object_id = SYNC_OBJECT.UPSTREAM_WRAPPER.value
        extraction_config_tasks = []

        if all_objects_selected:
            extraction_config_tasks = {
                str(task.integration_id): str(task.task)
                for task in ExtractionConfigAccessor(
                    client_id
                ).get_tasks_for_task_group(object_id)
                if str(task.task) != object_id
            }
        else:
            extraction_config_tasks = {
                str(task.integration_id): str(task.task)
                for task in ExtractionConfigAccessor(
                    client_id
                ).get_selected_tasks_for_task_group(object_id, integration_ids)
                if str(task.task) != object_id
            }

        e2e_record["upstream_sync"] = upstream_task_details(
            client_id, e2e_sync_run_id, extraction_config_tasks
        )

    return e2e_record


def get_databook_refresh_detailed_status(
    client_id: int, e2e_sync_run_id: UUID, sync_type=None
) -> dict:
    if sync_type == SYNC_OBJECT.DATABOOK_CUSTOM_OBJECT_SYNC.value:
        return {
            "datasheet_custom_object_sync": (
                datasheet_task_details(
                    client_id,
                    e2e_sync_run_id,
                    sync_type,
                )
            )
        }
    elif sync_type == SYNC_OBJECT.DATABOOK_REPORT_OBJECT_SYNC.value:
        return {
            "datasheet_report_object_sync": (
                datasheet_task_details(
                    client_id,
                    e2e_sync_run_id,
                    sync_type,
                )
            )
        }


def get_commission_calculation_detailed_status(
    client_id: int,
    e2e_sync_run_id: UUID,
    databook_ids: list[UUID],
    skip_databook_sync=False,
    connector_sync=False,
) -> dict:
    e2e_record = {}
    if not skip_databook_sync:
        report_objs = []
        if connector_sync:
            report_objs = get_report_object_ids(data_origin=["system_object"])
        elif databook_ids:
            report_objs_all = get_associated_report_objects(client_id, databook_ids)
            for report in report_objs_all:
                if report in get_report_object_ids(data_origin=["system_object"]):
                    report_objs.append(report)

        e2e_record["report_sync_system_object"] = report_task_details(
            client_id,
            str(e2e_sync_run_id),
            report_objects=report_objs,
            params={"run_report_sync": len(report_objs)},
        )
        e2e_record["datasheet_system_custom_object_sync"] = datasheet_task_details(
            client_id,
            str(e2e_sync_run_id),
            SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value,
        )

    commission_sync_name = "commission_sync"
    settlement_sync_name = "settlement_sync"
    snapshot_sync_name = "payout_snapshot_sync"
    settlement_snapshot_sync_name = "settlement_snapshot_sync"

    # plan_modification_sync_details = get_plan_modification_sync_details(
    #     client_id, str(e2e_sync_run_id)
    # )
    # if plan_modification_sync_details and can_run_sf_snapshot(client_id):
    #     e2e_record["plan_modification_sync"] = plan_modification_sync_details

    if has_feature(client_id, "expose_comm_reports_in_plan"):
        commission_sync_name = "l2_commission_sync"

        e2e_record["l1_commission_sync"] = commission_task_details(
            client_id, e2e_sync_run_id, level=1
        )

        report_objects = get_report_object_ids(data_origin=["inter_object"])

        # inter_comm_snapshot_sync_details = inter_comm_snapshot_sync_task_details(
        #     client_id, str(e2e_sync_run_id)
        # )
        # if inter_comm_snapshot_sync_details and can_run_sf_snapshot(client_id):
        #     e2e_record["inter_comm_snapshot_sync"] = inter_comm_snapshot_sync_details

        e2e_record["report_sync_inter_object"] = report_task_details(
            client_id,
            str(e2e_sync_run_id),
            report_objects=report_objects,
            params={"run_report_sync": len(report_objects)},
        )
        e2e_record["datasheet_inter_object_sync"] = datasheet_task_details(
            client_id,
            e2e_sync_run_id,
            SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value,
        )

    e2e_record[commission_sync_name] = commission_task_details(
        client_id, str(e2e_sync_run_id)
    )
    snapshot_task_details_record = payout_snapshot_task_details(
        client_id, str(e2e_sync_run_id)
    )
    if snapshot_task_details_record is not None:
        e2e_record[snapshot_sync_name] = snapshot_task_details_record
    e2e_record[settlement_sync_name] = settlement_task_details(
        client_id, str(e2e_sync_run_id)
    )

    settlement_snapshot_task_details_record = settlement_snapshot_task_details(
        client_id, str(e2e_sync_run_id)
    )
    if settlement_snapshot_task_details_record is not None:
        e2e_record[settlement_snapshot_sync_name] = (
            settlement_snapshot_task_details_record
        )

    report_objects_values = get_report_object_ids(data_origin=["commission_object"])
    if not should_run_settlement_report(client_id):
        report_objects_values = [
            report_object
            for report_object in report_objects_values
            if report_object != "settlement"
        ]
    if not get_payout_report_object_enabled(client_id):
        report_objects_values = [
            report_object
            for report_object in report_objects_values
            if report_object != "payout"
        ]
    e2e_record["report_sync_commission_object"] = report_task_details(
        client_id,
        str(e2e_sync_run_id),
        report_objects=report_objects_values,
        params={"run_report_sync": len(report_objects_values)},
    )
    e2e_record["datasheet_report_object_sync"] = datasheet_task_details(
        client_id,
        str(e2e_sync_run_id),
        SYNC_OBJECT.DATABOOK_COMMISSION_OBJECT.value,
    )

    return e2e_record


def get_forecast_calculation_detailed_status(
    client_id: int,
    e2e_sync_run_id: UUID,
    databook_ids: Optional[list[UUID]],
    skip_databook_sync=False,
) -> dict:
    e2e_record = {}
    if not skip_databook_sync:
        report_objs = []
        if databook_ids:
            report_objs_all = get_associated_report_objects(client_id, databook_ids)
            for report in report_objs_all:
                if report in get_report_object_ids(data_origin=["system_object"]):
                    report_objs.append(report)

        e2e_record["report_sync_system_object"] = report_task_details(
            client_id,
            str(e2e_sync_run_id),
            report_objects=report_objs,
            params={"run_report_sync": len(report_objs)},
        )
        e2e_record["datasheet_system_custom_object_sync"] = datasheet_task_details(
            client_id,
            str(e2e_sync_run_id),
            SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value,
        )

    forecast_sync_name = "forecast_sync"

    if has_feature(client_id, "expose_comm_reports_in_plan"):
        forecast_sync_name = "l2_forecast_sync"

        e2e_record["l1_forecast_sync"] = forecast_task_details(
            client_id, e2e_sync_run_id, level=1
        )

        report_objects = get_report_object_ids(data_origin=["inter_forecast_object"])

        e2e_record["report_sync_inter_object"] = report_task_details(
            client_id,
            str(e2e_sync_run_id),
            report_objects=report_objects,
            params={"run_report_sync": len(report_objects)},
        )
        e2e_record["datasheet_inter_object_sync"] = datasheet_task_details(
            client_id,
            e2e_sync_run_id,
            SYNC_OBJECT.DATABOOK_INTER_FORECAST_OBJECT_SYNC.value,
        )

    e2e_record[forecast_sync_name] = forecast_task_details(
        client_id, str(e2e_sync_run_id)
    )

    report_objects_values = get_report_object_ids(data_origin=["forecast_object"])
    e2e_record["report_sync_forecast_object"] = report_task_details(
        client_id,
        str(e2e_sync_run_id),
        report_objects=report_objects_values,
        params={"run_report_sync": len(report_objects_values)},
    )
    e2e_record["datasheet_report_object_sync"] = datasheet_task_details(
        client_id,
        str(e2e_sync_run_id),
        SYNC_OBJECT.DATABOOK_FORECAST_OBJECT.value,
    )

    return e2e_record


def get_failed_payee_period(client_id, e2e_sync_run_id, fetch_data):

    if not fetch_data:
        return []
    records = list(
        CommissionETLStatusReaderAccessor(client_id).get_failed_payee_period_for_e2e(
            e2e_sync_run_id
        )
    )
    result = {}
    for item in records:
        start_date = item["period_start_date"].date()
        end_date = item["period_end_date"].date()

        date_key = (str(start_date), str(end_date))

        if date_key not in result:
            result[date_key] = []
        result[date_key].append(item["payee_email_id"])

    formatted_results = [
        {"start_date": key[0], "end_date": key[1], "payees": value}
        for key, value in result.items()
    ]

    formatted_results.sort(key=lambda x: (x["start_date"], x["end_date"]))

    return formatted_results


def get_detailed_sync_status(client_id, e2e_sync_run_id=None):
    if e2e_sync_run_id:
        etl_record = ETLSyncStatusReaderAccessor(client_id).get_etl_record_by_id(
            e2e_sync_run_id
        )
    else:
        etl_record = get_recent_data_sync_status(client_id)
    if not etl_record:
        return {}
    e2e_sync_run_id = etl_record.e2e_sync_run_id
    detailed_status = OrderedDict()

    if not etl_record:
        return detailed_status

    params = etl_record.params
    etl_task = etl_record.task
    detailed_status["sync_status"] = etl_record.sync_status
    detailed_status["sync_end_time"] = str(etl_record.sync_end_time)
    detailed_status["e2e_sync_run_id"] = str(e2e_sync_run_id)

    databook_ids = None
    if params and "databook_ids" in params:
        databook_ids = params["databook_ids"]

    integration_ids = []
    if params and "integration_ids" in params and params["integration_ids"]:
        integration_ids = params["integration_ids"]
    all_objects_selected = True if len(integration_ids) == 0 else False

    if etl_task == ETL_ACTIVITY.REFRESH_DATABOOK.value:
        if params and "run_report_sync" in params and params["run_report_sync"]:
            report_objs_values = get_associated_report_objects(client_id, databook_ids)
            detailed_status.update(
                {
                    "report_sync": report_task_details(
                        client_id,
                        str(e2e_sync_run_id),
                        report_objects=report_objs_values,
                        params=params,
                    )
                }
            )
        detailed_status.update(
            {
                "datasheet_report_object_sync": datasheet_task_details(
                    client_id, str(e2e_sync_run_id), None
                )
            }
        )
    elif etl_task == ETL_ACTIVITY.COMMISSION_CALCULATION.value:
        if params and "refresh_databooks" in params and not params["refresh_databooks"]:
            detailed_status.update(
                get_commission_calculation_detailed_status(
                    client_id, e2e_sync_run_id, databook_ids, True
                )
            )
        else:
            detailed_status.update(
                get_commission_calculation_detailed_status(
                    client_id, e2e_sync_run_id, databook_ids, False
                )
            )
    elif etl_task == ETL_ACTIVITY.REPORT_ETL.value:
        report_object = None
        if "object" in params:
            report_object = [params["object"]]
        detailed_status.update(
            {
                "report_etl_object": report_task_details(
                    client_id,
                    e2e_sync_run_id,
                    report_objects=report_object,
                    params=params,
                )
            }
        )
    elif etl_task == ETL_ACTIVITY.CONNECTOR_UPSTREAM_SYNC.value:
        detailed_status.update(
            get_connector_upstream_sync_detailed_status(
                client_id, e2e_sync_run_id, all_objects_selected, integration_ids
            )
        )
    elif etl_task == ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value:
        detailed_status.update(
            get_connector_e2e_sync_detailed_status(
                client_id, e2e_sync_run_id, all_objects_selected, integration_ids
            )
        )
    elif etl_task == ETL_ACTIVITY.SETTLEMENT_CALCULATION.value:
        if params and "refresh_databooks" in params and not params["refresh_databooks"]:
            detailed_status.update(
                get_settlement_calculation_detailed_status(
                    client_id, e2e_sync_run_id, databook_ids, True
                )
            )
        else:
            detailed_status.update(
                get_settlement_calculation_detailed_status(
                    client_id, e2e_sync_run_id, databook_ids, False
                )
            )
    elif etl_task == ETL_ACTIVITY.FORECAST_CALCULATION.value:
        skip_databook_sync = not (
            params.get("refresh_databooks", False) if params else False
        )
        forecast_detailed_status = get_forecast_calculation_detailed_status(
            client_id, e2e_sync_run_id, databook_ids, skip_databook_sync
        )
        detailed_status.update(forecast_detailed_status)
    if params.get("sync_period_end", False):
        detailed_status["period"] = {
            "start_date": params["sync_period"],
            "end_date": params["sync_period_end"],
        }
    return detailed_status


def get_settlement_calculation_detailed_status(
    client_id: int,
    e2e_sync_run_id: UUID,
    databook_ids: list[UUID],
    skip_databook_sync=False,
    connector_sync=False,
) -> dict:
    e2e_record = {}
    if not skip_databook_sync:
        report_objs = []
        if connector_sync:
            report_objs = get_report_object_ids(data_origin=["system_object"])
        elif databook_ids:
            report_objs_all = get_associated_report_objects(client_id, databook_ids)
            for report in report_objs_all:
                if report in get_report_object_ids(data_origin=["system_object"]):
                    report_objs.append(report)

        e2e_record["report_sync_system_object"] = report_task_details(
            client_id,
            str(e2e_sync_run_id),
            report_objects=report_objs,
            params={"run_report_sync": len(report_objs)},
        )
        e2e_record["datasheet_system_custom_object_sync"] = datasheet_task_details(
            client_id,
            str(e2e_sync_run_id),
            SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value,
        )

    commission_sync_name = "settlement_sync"
    settlement_snapshot_sync_name = "settlement_snapshot_sync"

    e2e_record[commission_sync_name] = settlement_task_details(
        client_id, str(e2e_sync_run_id)
    )

    report_objects_values = ["settlement"]
    e2e_record["report_sync_settlement_object"] = report_task_details(
        client_id,
        str(e2e_sync_run_id),
        report_objects=report_objects_values,
        params={"run_report_sync": len(report_objects_values)},
    )
    e2e_record["datasheet_report_object_sync"] = datasheet_task_details(
        client_id,
        str(e2e_sync_run_id),
        SYNC_OBJECT.DATABOOK_SETTLEMENT_OBJECT.value,
    )
    settlement_snapshot_task_details_record = settlement_snapshot_task_details(
        client_id, str(e2e_sync_run_id)
    )
    if settlement_snapshot_task_details_record is not None:
        e2e_record[settlement_snapshot_sync_name] = (
            settlement_snapshot_task_details_record
        )
    return e2e_record


def get_plan_modification_sync_details(
    client_id: int, e2e_sync_run_id: str
) -> dict | None:
    pedss = PlanModificationSyncStatusAccessor(client_id, e2e_sync_run_id, None)
    e2e_plan_modification_record = etl_record_template(records=False)

    tasks = [SYNC_OBJECT.PLAN_MODIFICATION_SYNC.value]

    try:
        total_plan_modification_job_count = int(pedss.get_total_record_count())
        e2e_plan_modification_record["total"][
            "count"
        ] = total_plan_modification_job_count
    except TypeError:
        # TypeError is raised when there is no plan_modification tasks for the given e2e_sync_run_id
        return None

    plan_modifications = list(
        pedss.get_records_by_e2e_sync_run_id_and_task(task_list=tasks)
    )
    plan_modifications_count = 0
    for plan_modification in plan_modifications:
        task_status = get_task_status(str(plan_modification[1]))
        e2e_plan_modification_record[task_status]["count"] += 1
        plan_modifications_count += 1
    e2e_plan_modification_record["wait"]["count"] = (
        total_plan_modification_job_count - plan_modifications_count
    )

    return e2e_plan_modification_record


def inter_comm_snapshot_sync_task_details(
    client_id: int, e2e_sync_run_id: str
) -> dict | None:
    icsss = PayoutSnapshotETLStatusReaderAccessor(client_id)
    e2e_inter_com_snapshot_record = etl_record_template(records=False)

    tasks = [SYNC_OBJECT.PAYOUT_SNAPSHOT_INTER_COMM_SYNC.value]

    try:
        total_inter_com_snapshot_job_count = int(
            icsss.job_count_sum_for_inter_comm_snapshot(
                e2e_sync_run_id, SYNC_OBJECT.PAYOUT_SNAPSHOT_INTER_COMM_SYNC.value
            )
        )
        e2e_inter_com_snapshot_record["total"][
            "count"
        ] = total_inter_com_snapshot_job_count
    except TypeError:
        # TypeError is raised when there is no inter_com_snapshot tasks for the given e2e_sync_run_id
        return None

    inter_com_snapshots = list(
        icsss.get_records_by_e2e_sync_run_id_and_task(e2e_sync_run_id, task_list=tasks)
    )
    inter_com_snapshots_count = 0
    for inter_com_snapshot in inter_com_snapshots:
        task_status = get_task_status(str(inter_com_snapshot[1]))
        e2e_inter_com_snapshot_record[task_status]["count"] += 1
        inter_com_snapshots_count += 1
    e2e_inter_com_snapshot_record["wait"]["count"] = (
        total_inter_com_snapshot_job_count - inter_com_snapshots_count
    )

    return e2e_inter_com_snapshot_record
