from inspect import getmembers, isabstract, isclass

import commission_engine.services.data_sources as data_sources
import commission_engine.services.data_sources.context as ctx
from commission_engine.accessors.client_accessor import (
    get_fx_rate_report_object_enabled,
    get_payout_report_object_enabled,
)
from commission_engine.utils.general_data import (
    DATASHEET_SOURCE,
    ReportObject,
    object_strategy,
)
from everstage_ddd.cpq.reports import get_cpq_report_strategies


class ObjectFactory(object):
    """
     A Factory, which creates the object based on the source type.
    if source_type is
        'object', then creates the CustomObjectStrategy instance
        'datasheet', then creates the DatasheetDataStrategy instance
        'report' and source_id is 'commission', then creates the CommissionReportStrategy instance
        'report' and source_id is 'quota', then creates the QuotaReportStrategy instance
        and so on..
    """

    objects = {}

    def __init__(self):
        self.load_objects()

    def load_objects(self):
        """
        getmembers return the members/class used in a module as (name, type)
        here data_sources is a module which holds all the ConcreteClasses which we imported in the init.py file
        and we are fetching only the concrete classes using the lambda predicate and creating a map
        as {'class_name': class}
        for ex: {'CustomObjectStrategy': <class> CustomObjectStrategy}
        """

        classes = getmembers(data_sources, lambda m: isclass(m) and not isabstract(m))
        for name, _type in classes:
            if isclass(_type) and issubclass(_type, data_sources.AbsDataStrategy):
                self.objects.update({name: _type})

    def create_instance(
        self, client_id, data_source: str, log_context: dict | None = None
    ):
        """returns the instance of the Concrete Strategy class based on the source type"""
        # If the source type is 'payout' and the payout report object is enabled, then return the payout report object if it is the data source
        # Else remove the payout object from the object_strategy and process the data source
        payout_report_object_enabled = get_payout_report_object_enabled(client_id)
        if (
            data_source == ReportObject.PAYOUT.value
            and not payout_report_object_enabled
        ):
            raise Exception(
                f"Payout report object is not enabled for client {client_id}"
            )

        fx_rate_report_object_enabled = get_fx_rate_report_object_enabled(client_id)
        if (
            data_source == ReportObject.FX_RATE.value
            and not fx_rate_report_object_enabled
        ):
            raise Exception(f"Fx report object is not enabled for client {client_id}")

        cpq_reports = get_cpq_report_strategies()
        if data_source in cpq_reports:
            return cpq_reports[data_source](client_id)

        strategy = object_strategy.get(data_source)
        if strategy and strategy in self.objects:
            return self.objects[strategy](client_id=client_id, log_context=log_context)
        else:
            raise Exception(f"Incorrect Source type {data_source}")


def create_data_context(
    client_id: int, object_id: str, object_type: str, log_context: dict
) -> ctx.DataContext:
    """
    Utility method for creating the Data Context Class instance, the strategy reference
    for the Context is set by using the object_id and object_type.

    @object_id -  custom_object_id/datasheet_id/report_object_id
    @object_type - object/datasheet/report
    """
    data_source = (
        object_id if object_type == DATASHEET_SOURCE.REPORT.value else object_type
    )
    source_strategy = ObjectFactory().create_instance(
        data_source=data_source, client_id=client_id, log_context=log_context
    )
    data_context = ctx.DataContext(source_strategy)
    return data_context


def create_report_data_context(client_id: int, object_id: str) -> ctx.DataContext:
    """
    Utility method for creating the Data Context Class instance, the strategy reference
    for the Context is set by using the object_id and object_type.

    @object_id -  quota_attainment/quota/commission/commission_summary/user
    @object_type - commission_object/system_object
    """
    data_source = object_id
    source_strategy = ObjectFactory().create_instance(
        data_source=data_source, client_id=client_id
    )
    data_context = ctx.DataContext(source_strategy)
    return data_context
