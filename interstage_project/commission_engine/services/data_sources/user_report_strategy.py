import traceback
from datetime import datetime, timedelta
from operator import itemgetter

import pandas as pd
from django.utils import timezone

import commission_engine.accessors.client_accessor as client_acc
import commission_engine.utils.date_utils as d_utils
import commission_engine.utils.general_utils as g_utils
import spm.accessors.config_accessors.employee_accessor as employee_acc
import spm.services.config_services.employee_services as employee_services
from commission_engine.accessors.etl_housekeeping_accessor import (
    ReportETLStatusAccessor,
)
from commission_engine.accessors.ever_object_accessor import EverObjectAccessor
from commission_engine.accessors.report_object_accessor import (
    ReportObjectDataAccessor,
    latest_knowledge_date_for_report_objects,
)
from commission_engine.services.data_sources.abstract_strategy import AbsDataStrategy
from commission_engine.services.data_sources.report_object_writer import (
    write_to_snowflake_report_object,
)
from commission_engine.utils.general_utils import set_accurate_column_types
from commission_engine.utils.report_utils import (
    get_report_variables,
    get_system_object_data_table_name,
)
from interstage_project import utils
from interstage_project.global_utils.language_enum import LocalizationLanguages
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanCriteriaAccessor,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.custom_field_accessor import (
    CustomFieldDataAccessor,
    CustomFieldsAccessor,
)
from spm.accessors.quota_acessors import QuotaAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.services.custom_calendar_services import get_custom_calendar_map


class UserReportStrategy(AbsDataStrategy):
    """User Report Concrete Class, works on the Employee, Employee Payroll and Hierarchy Table Records"""

    def __init__(self, client_id, log_context=None):
        self.client_id = client_id
        self.client = client_acc.get_client(client_id)
        self.hierarchy_map = {}
        self.employee_plan_map = {}
        self.employee_spiff_map = {}
        self.employee_quota_categories = {}
        self.active_countries = {}
        self.employee_map = {}
        self.role_permission_id_name_map = {}
        self.curr_time = timezone.now()
        self.max_time = timezone.make_aware(datetime.max)
        log_context = log_context or {"client_id": client_id}
        self.logger = utils.LogWithContext(log_context)
        self.object_id = "user"
        self.is_custom_mode = False
        self.custom_calendar_map = {}
        self.quota_category_display_name_map = {}

    def get_data(self, **kwargs: dict) -> pd.DataFrame:
        self.logger.info("GET DATA - User Report")
        filters = kwargs.get("filters")
        report_data = ReportObjectDataAccessor(
            client_id=self.client_id
        ).get_data_by_object_id(object_id=self.object_id, filters=filters)
        report_data_as_values = []
        for _ in report_data:
            data = _.data
            data.update({"row_key": _.row_key})
            report_data_as_values.append(data)
        df = pd.DataFrame(report_data_as_values)

        if not df.empty:
            object_vars = get_report_variables(
                report_object_id=self.object_id, client_id=self.client_id
            )
            df = set_accurate_column_types(object_vars, df)
        return df

    def get_latest_record(self, **kwargs):
        latest_knowledge_date_dict = latest_knowledge_date_for_report_objects(
            client_id=self.client_id,
            report_object_ids=self.object_id,
        )
        last_updated_time = latest_knowledge_date_dict.get(self.object_id, None)
        return last_updated_time

    def is_data_stale(self, **kwargs) -> bool:
        """
        returns staleness status of report_object_data
        if true, then Report_ETL should be run
        """
        last_sync_time = None
        last_success_obj = ReportETLStatusAccessor(
            client_id=self.client_id
        ).get_last_success_obj(object_id=self.object_id)

        if last_success_obj:
            last_sync_time = last_success_obj.changes_end_time

        if last_success_obj is None:
            return True

        emp_query_set = employee_acc.EmployeeAccessor(
            self.client_id
        ).get_modified_records(updated_time=last_sync_time)

        payroll_query_set = employee_acc.EmployeePayrollAccessor(
            self.client_id
        ).get_modified_records(updated_time=last_sync_time)
        hierarchy_query_set = employee_acc.HierarchyAccessor(
            self.client_id
        ).get_modified_records(updated_time=last_sync_time)

        custom_field_data_query_set = CustomFieldDataAccessor(
            self.client_id
        ).get_modified_records(updated_time=last_sync_time)

        plan_details_query_set = employee_acc.PlanDetailsAllAccessor(
            self.client_id
        ).get_modified_records(updated_time=last_sync_time)

        if (
            emp_query_set.exists()
            or payroll_query_set.exists()
            or hierarchy_query_set.exists()
            or custom_field_data_query_set.exists()
            or plan_details_query_set.exists()
        ):
            return True

        # check for stale if any employee is deleted
        deleted_user_records = employee_acc.EmployeeAccessor(
            self.client_id
        ).get_last_deleted_employees(last_sync_time)
        if deleted_user_records.exists():
            return True

        return False

    def data_mapper(self, employee_email_ids: list[str]) -> None:
        hierarchy_records = (
            employee_acc.HierarchyAccessor(client_id=self.client_id)
            .client_kd_aware()
            .values()
        )
        for rec in hierarchy_records:
            start = rec.get("effective_start_date")
            end = rec.get("effective_end_date")
            email_id = rec.get("reporting_manager_email_id")
            if email_id not in self.hierarchy_map:
                self.hierarchy_map[email_id] = []
            self.hierarchy_map[email_id].append((start, end))

        plan_details = employee_acc.PlanDetailsAllAccessor(
            client_id=self.client_id
        ).get_all_valid_employee_plan(
            employee_email_ids=employee_email_ids, date=self.curr_time
        )

        plan_ids = set()
        email_plan_map = {}
        for rec in plan_details:
            plan_id = str(rec.get("plan_id"))
            plan_ids.add(plan_id)
            if rec.get("employee_email_id") not in email_plan_map:
                email_plan_map[rec.get("employee_email_id")] = []
            email_plan_map[rec.get("employee_email_id")].append(plan_id)

        plans = CommissionPlanAccessor(
            client_id=self.client_id
        ).get_commission_plan_by_plan_id(plan_id=plan_ids, as_dicts=True)
        plan_grouped_by_id = {str(plan.get("plan_id")): plan for plan in plans}

        plan_criteria = PlanCriteriaAccessor(
            client_id=self.client_id
        ).get_quota_criteria_for_plans(plan_ids=plan_ids)
        group_criteria_by_plan = {}
        for rec in plan_criteria:
            plan_id = str(rec.get("plan_id"))
            if plan_id not in group_criteria_by_plan:
                group_criteria_by_plan[plan_id] = []
            group_criteria_by_plan[plan_id].append(rec)

        hidden_quotas = client_acc.get_client_hidden_quota_categories(
            client_id=self.client_id
        )

        for email_id, plans in email_plan_map.items():
            for plan_id in plans:
                plan_obj = plan_grouped_by_id.get(plan_id)
                if plan_obj.get("plan_type") == "MAIN":
                    self.employee_plan_map[email_id] = plan_obj.get("plan_name")
                else:
                    if email_id not in self.employee_spiff_map:
                        self.employee_spiff_map[email_id] = []
                    self.employee_spiff_map[email_id].append(plan_obj.get("plan_name"))

                criteria_records_in_plan = group_criteria_by_plan.get(plan_id, [])
                for record in criteria_records_in_plan:
                    quota_category = record.get("criteria_data", {}).get("quota_name")
                    if quota_category not in hidden_quotas:
                        if email_id not in self.employee_quota_categories:
                            self.employee_quota_categories[email_id] = set()
                        self.employee_quota_categories[email_id].add(quota_category)

        # Get hierarchy records for the received employee_email_ids
        employee_hierarchy_records = employee_acc.HierarchyAccessor(
            client_id=self.client_id
        ).get_all_employee_hierarchy_by_email(employee_email_ids=employee_email_ids)

        # Extract reporting manager email ids from the hierarchy records
        reporting_manager_emails_list = [
            x.get("reporting_manager_email_id") for x in employee_hierarchy_records
        ]

        # Get unique employee email ids that includes employee_email_ids and reporting_manager_emails_list
        unique_employee_emails = set(employee_email_ids + reporting_manager_emails_list)

        employee_records = employee_acc.EmployeeAccessor(self.client_id).get_employees(
            employee_email_ids=unique_employee_emails, as_dicts=True
        )
        self.employee_map = {emp["employee_email_id"]: emp for emp in employee_records}

        self.active_countries = {
            country.country_code: country.country_name
            for country in list(
                CountriesAccessor(self.client_id).get_all_active_countries()
            )
        }

        all_role_permission = RolePermissionsAccessor(self.client_id).get_all_roles(
            projection=["role_permission_id", "display_name"]
        )
        self.role_permission_id_name_map = {
            str(rl["role_permission_id"]): rl["display_name"]
            for rl in all_role_permission
        }
        self.get_custom_calendar_details()

        # create quota_category_display_name map
        qs = QuotaAccessor(self.client_id).get_all_quota_category_display_name_map()
        self.quota_category_display_name_map = {
            record["quota_category_name"]: record["display_name"] for record in qs
        }

    def split_data_by_date_intervals(
        self, list_a: list[dict], list_b: list[dict]
    ) -> list[dict]:
        # pylint: disable=pointless-string-statement
        """
        splits the date interval for the overlapping date as overlapping interval and non overlapping interval
        for overlapping interval, the data points are combined from both the records
        for non overlapping interval, only the data of the corresponding records are considered

        The final result contains merged list of records, where the field values from data point A and data point B
        are considered based on the date interval
        """
        if list_a is None:
            return list_b or []

        if list_b is None:
            return list_a or []

        list_a = (
            sorted(list_a, key=itemgetter("effective_start_date")) if list_a else []
        )
        list_b = (
            sorted(list_b, key=itemgetter("effective_start_date")) if list_b else []
        )
        ind_a, ind_b, len_a, len_b = 0, 0, len(list_a), len(list_b)

        record_a = list_a[ind_a] if ind_a < len_a else None
        record_b = list_b[ind_b] if ind_b < len_b else None

        eff_date_a, eff_date_b = tuple(), tuple()

        def swap_a_and_b():
            nonlocal record_a, record_b, ind_a, ind_b, list_a, list_b, len_a, len_b, eff_date_a, eff_date_b
            record_a, record_b = record_b, record_a
            ind_a, ind_b = ind_b, ind_a
            list_a, list_b = list_b, list_a
            len_a, len_b = len_b, len_a
            eff_date_a, eff_date_b = eff_date_b, eff_date_a

        result = []

        while ind_a < len_a and ind_b < len_b:
            eff_date_a = (
                d_utils.start_of_day(record_a.get("effective_start_date")),
                d_utils.end_of_day(
                    (record_a.get("effective_end_date") or self.max_time)
                ),
            )
            eff_date_b = (
                d_utils.start_of_day(record_b.get("effective_start_date")),
                d_utils.end_of_day(
                    (record_b.get("effective_end_date") or self.max_time)
                ),
            )

            if eff_date_a[0] == eff_date_b[0]:
                """
                consider a rec of type = (start_time, end_time, data)

                if both the records having same start date and
                   diff end date,
                    say rec_a = (2t, 4t, data_a), rec_b = (2t, 7t, data_b)
                    then split the records by end date and merge common interval,
                    result = [(2t, 4t, data_a, data_b)], rec_a fully covered and rec_b becomes (4t, 7t, data_b)

                   same end date
                    say rec_a = (2t, 4t, data_a), rec_b = (2t, 4t, data_b)
                    then merge both the records
                    result = [(2t, 4t, data_a, data_b)]
                """
                if eff_date_a[1] > eff_date_b[1]:
                    # swap to make "object a" as min wrt eff.end_date
                    swap_a_and_b()

                merged_record = {
                    **record_a,
                    **record_b,
                    "effective_start_date": record_a.get("effective_start_date"),
                    "effective_end_date": record_a.get("effective_end_date"),
                }
                result.append(merged_record)
                if eff_date_a[1] != eff_date_b[1]:
                    record_b["effective_start_date"] = d_utils.start_of_day(
                        record_a.get("effective_end_date") + timedelta(days=1)
                    )
                    ind_a += 1
                    record_a = list_a[ind_a] if ind_a < len_a else None
                else:
                    ind_a += 1
                    ind_b += 1
                    record_a = list_a[ind_a] if ind_a < len_a else None
                    record_b = list_b[ind_b] if ind_b < len_b else None
            else:
                """
                if both the records having diff start date and
                 diff end date,
                    if non overlapping range, say rec_a = (2t, 4t, data_a), rec_b = (5t, 6t, data_b)
                    then append rec_a to result and get next record from list_a

                    if overlapping range, say rec_a = (2t, 7t, data_a), rec_b = (5t, 6t, data_b)
                    then append (2t, 4t, data_a, none_data_b) to result
                    rec_a becomes (5t, 7t, data_a)
                """
                if eff_date_a[0] > eff_date_b[0]:
                    # swap to make "object a" as min wrt eff start date
                    swap_a_and_b()

                # non overlapping ranges
                if eff_date_a[1] < eff_date_b[0]:
                    merged_record = {
                        **record_a,
                        "effective_start_date": record_a.get("effective_start_date"),
                        "effective_end_date": record_a.get("effective_end_date"),
                    }
                    ind_a += 1
                    record_a = list_a[ind_a] if ind_a < len_a else None
                    result.append(merged_record)

                # overlapping ranges
                else:
                    end_date = d_utils.end_of_day(
                        record_b.get("effective_start_date") - timedelta(days=1)
                    )
                    merged_record = {
                        **record_a,
                        "effective_start_date": record_a.get("effective_start_date"),
                        "effective_end_date": end_date,
                    }
                    record_a["effective_start_date"] = record_b.get(
                        "effective_start_date"
                    )
                    result.append(merged_record)

        while ind_a < len_a:
            merged_record = {
                **record_a,
            }
            result.append(merged_record)
            ind_a += 1
            record_a = list_a[ind_a] if ind_a < len_a else None

        while ind_b < len_b:
            merged_record = {
                **record_b,
            }
            result.append(merged_record)
            ind_b += 1
            record_b = list_b[ind_b] if ind_b < len_b else None

        return result

    def add_employment_country(self, record: pd.Series) -> str:
        return self.active_countries.get(record.get("employment_country"))

    def add_status(self, record: pd.Series) -> str:
        exit_date = record.get("exit_date")
        deactivation_date = record.get("deactivation_date")
        record_status = record.get("status")
        now = timezone.make_aware(datetime.now())
        status = employee_services.get_user_status(
            emp_status=record_status,
            exit_date=exit_date,
            deactivation_date=deactivation_date,
            effective_date=now,
        )
        return status

    def add_payee_variable_pay(self, record: pd.Series) -> int:
        return record.get("variable_pay")

    def add_reporting_manager_name(self, record: pd.Series) -> str:
        manager = self.employee_map.get(record.get("reporting_manager_email_id"), {})
        return (
            f"{manager.get('first_name')} {manager.get('last_name')}" if manager else ""
        )

    def add_current_timezone(self, record: pd.Series) -> str:
        return record.get("time_zone") or self.client.time_zone or "UTC"

    def get_custom_calendar_details(self):
        self.custom_calendar_map = get_custom_calendar_map(self.client_id)

    def add_payout_frequency(self, record: pd.Series) -> str:
        payout_frequency = record.get("payout_frequency")
        if payout_frequency in self.custom_calendar_map.keys():
            return self.custom_calendar_map.get(payout_frequency)
        return payout_frequency

    def add_payee_or_manager(self, record: pd.Series) -> str:
        """
        return if the employee is payee or manager between the eff.start and eff.end date
            Note: if the eff.start data is Null (means the employee has no payroll or hierarchy) but has reports
            then the employee considered as Manager
        """
        start = record.get("effective_start_date")
        end = record.get("effective_end_date") or self.curr_time
        email_id = record.get("employee_email_id")
        if email_id not in self.hierarchy_map:
            return "Payee"
        else:
            if start is None:
                return "Manager"

            for _date in self.hierarchy_map.get(email_id):
                if start >= _date[0] and (_date[1] is None or end <= _date[1]):
                    return "Manager"
        return "Payee"

    def add_active_plan(self, record: pd.Series) -> str:
        return self.employee_plan_map.get(record.get("employee_email_id"))

    def add_active_spiff(self, record: pd.Series) -> str:
        spiffs = self.employee_spiff_map.get(record.get("employee_email_id")) or []
        return ", ".join(spiffs)

    def add_active_quota_categories(self, record: pd.Series) -> str:
        quota_categories = self.employee_quota_categories.get(record.get("employee_email_id")) or []  # type: ignore
        display_names = []
        for category in quota_categories:
            if category == "Primary":
                display_names.append("Primary")
            else:
                display_names.append(self.quota_category_display_name_map.get(category))

        return ", ".join(display_names)

    def add_created_by(self, record: pd.Series) -> str:
        return (
            record.get("additional_details", {}).get("updated_by")
            if record.get("additional_details")
            else None
        )

    def add_rbac_user_role(self, record: pd.Series) -> str:
        all_user_roles = record.get("user_role", [])
        return ", ".join(
            [
                self.role_permission_id_name_map.get(role_id, "-")
                for role_id in all_user_roles
            ]
        )

    def add_preferred_language(self, record: pd.Series) -> str:
        """Add preferred language from employee record"""
        key = record.get("preferred_language", "en")
        language = LocalizationLanguages.get_language_by_key(key)
        return (
            language.value.display_name
            if language
            else LocalizationLanguages.ENGLISH.value.display_name
        )

    def extract_data(self, **kwargs) -> pd.DataFrame:
        # pylint: disable=pointless-string-statement
        """

        If there is any updates (after the last_sync_end_time) to the users on the tables below,
            1. Employee
            2. Employee Payroll Details
            3. Hierarchy
            4. Custom Field Data
        then
            extract active data for the users from all the tables and merge the records by email_id.

        """
        self.is_custom_mode = kwargs.get("is_custom_mode")

        employee_accessor = employee_acc.EmployeeAccessor(self.client_id)
        emp_payroll_accessor = employee_acc.EmployeePayrollAccessor(self.client_id)
        hierarchy_accessor = employee_acc.HierarchyAccessor(self.client_id)

        self.logger.info(f"{self.object_id} Report Sync for client: {self.client_id}")
        if not self.is_custom_mode:
            changes_start_time = kwargs.get("changes_start_time")
            self.logger.info(
                "Changes mode - " f"changes_start_time: {changes_start_time}, "
            )

            employee_email_ids = employee_accessor.get_email_id_of_records_modified(
                updated_time=changes_start_time
            )

            hierarchy_data = hierarchy_accessor.get_email_id_of_records_modified(
                updated_time=changes_start_time
            )

            hierarchy_email_ids = set()
            for rec in hierarchy_data:
                hierarchy_email_ids.add(rec[0])
                hierarchy_email_ids.add(rec[1])

            hierarchy_email_ids = list(hierarchy_email_ids)
            # get the history of managers for the payees (hierarchy_email_ids) fetched
            manager_email_ids = []
            if hierarchy_email_ids and len(hierarchy_email_ids) > 0:
                manager_email_ids = employee_acc.HierarchyAccessor(
                    self.client_id
                ).get_all_manager_email_ids(reportee_emails=hierarchy_email_ids)

            employee_payroll_email_ids = (
                emp_payroll_accessor.get_email_id_of_records_modified(
                    updated_time=changes_start_time
                )
            )

            payees_with_cf_data_changes = CustomFieldDataAccessor(
                self.client_id
            ).get_all_modified_user_by_updated_time(updated_at=changes_start_time)

            plan_details_email_ids = employee_acc.PlanDetailsAllAccessor(
                self.client_id
            ).get_email_id_of_records_modified(updated_time=changes_start_time)

            """
                for all the modified records in employee table, get the reportee (if any) and
                them to the list of email_ids to be updated, this is required to update the
                manager employee data like name, role.., for the corresponding reportees
            """
            reportees_email = hierarchy_accessor.get_hierarchy_history_for_emails(
                email_ids=employee_email_ids
            )
            users_modified_after_last_sync = list(
                set(
                    employee_email_ids
                    + employee_payroll_email_ids
                    + hierarchy_email_ids
                    + reportees_email
                    + payees_with_cf_data_changes
                    + plan_details_email_ids
                    + manager_email_ids
                )
            )
        else:
            users_modified_after_last_sync = kwargs.get("payee_email_id")
            self.logger.info(
                f"Period mode - payee_email_ids: {users_modified_after_last_sync}"
            )

        self.data_mapper(employee_email_ids=users_modified_after_last_sync)

        # getting the active data for the users modified
        employee_records = employee_accessor.get_employees(
            employee_email_ids=users_modified_after_last_sync
        )
        employee_payroll_records = (
            emp_payroll_accessor.get_all_employee_payroll_by_email(
                employee_email_ids=users_modified_after_last_sync
            )
        )
        hierarchy_records = hierarchy_accessor.get_all_employee_hierarchy_by_email(
            employee_email_ids=users_modified_after_last_sync
        )

        payee_cf_data = {}
        custom_fields_data_records = CustomFieldDataAccessor(
            self.client_id
        ).get_payee_custom_field_data_with_email_list(
            email_list=users_modified_after_last_sync, as_dicts=True
        )
        active_drop_down_cf = CustomFieldsAccessor(
            self.client_id
        ).get_active_fields_by_field_type("Dropdown")
        drop_down_cf_map = {}
        for drop_down_rec in active_drop_down_cf:
            drop_down_cf_map[drop_down_rec["system_name"]] = {}
            for opt in drop_down_rec["options"]:
                drop_down_cf_map[drop_down_rec["system_name"]][opt["system_name"]] = (
                    opt["display_name"]
                )
        for record in custom_fields_data_records:
            record_data = record.get("data", {})
            for cf_system_name, cf_val in record_data.items():
                if (
                    cf_system_name in drop_down_cf_map
                    and cf_val in drop_down_cf_map[cf_system_name]
                ):
                    record_data[cf_system_name] = drop_down_cf_map[cf_system_name][
                        cf_val
                    ]
            if record.get("email") not in payee_cf_data:
                payee_cf_data[record.get("email")] = []
            payee_cf_data[record.get("email")].append(
                {
                    "effective_start_date": record.get("effective_start_date"),
                    "effective_end_date": record.get("effective_end_date"),
                    "employee_email_id": record.get("email"),
                    **record_data,
                }
            )

        # grouping the payroll and hierarchy by email_id
        payroll_records_by_email = {}
        hierarchy_records_by_email = {}
        for record in employee_payroll_records:
            if record.get("employee_email_id") not in payroll_records_by_email:
                payroll_records_by_email[record.get("employee_email_id")] = []
            payroll_records_by_email[record.get("employee_email_id")].append(record)

        for record in hierarchy_records:
            if record.get("employee_email_id") not in hierarchy_records_by_email:
                hierarchy_records_by_email[record.get("employee_email_id")] = []
            hierarchy_records_by_email[record.get("employee_email_id")].append(record)

        # merging the payroll and hierarchy records for the user based on eff.dates
        merged_records = []
        for email_id in users_modified_after_last_sync:
            payroll_record_for_user = payroll_records_by_email.get(email_id)
            hierarchy_records_for_user = hierarchy_records_by_email.get(email_id)
            custom_field_record_for_user = payee_cf_data.get(email_id)
            inter_record = self.split_data_by_date_intervals(
                payroll_record_for_user, hierarchy_records_for_user
            )
            merged_records.extend(
                self.split_data_by_date_intervals(
                    inter_record, custom_field_record_for_user
                )
            )

        # merge all the data by email_id
        final_df = pd.DataFrame(employee_records)
        if merged_records:
            employee_eff_df = pd.DataFrame(merged_records)
            final_df = pd.merge(
                final_df,
                employee_eff_df,
                how="left",
                on="employee_email_id",
            )
        else:
            if "effective_start_date" not in final_df.columns:
                final_df["effective_start_date"] = None
            if "effective_end_date" not in final_df.columns:
                final_df["effective_end_date"] = None

        return final_df

    def transform_data(self, **kwargs) -> pd.DataFrame:
        df = kwargs.get("data")
        df = g_utils.remove_nan_nat(df)
        if not df.empty:
            df["employment_country"] = df.swifter.apply(
                self.add_employment_country, axis=1
            )
            df["status"] = df.swifter.apply(self.add_status, axis=1)
            df["payee_variable_pay"] = df.swifter.apply(
                self.add_payee_variable_pay, axis=1
            )
            df["reporting_manager_name"] = df.swifter.apply(
                self.add_reporting_manager_name, axis=1
            )
            df["payee_or_manager"] = df.swifter.apply(self.add_payee_or_manager, axis=1)
            df["active_plan"] = df.swifter.apply(self.add_active_plan, axis=1)
            df["active_spiffs"] = df.swifter.apply(self.add_active_spiff, axis=1)
            df["active_quota_categories"] = df.swifter.apply(
                self.add_active_quota_categories, axis=1
            )
            df["created_by"] = df.swifter.apply(self.add_created_by, axis=1)
            df["user_role"] = df.swifter.apply(self.add_rbac_user_role, axis=1)
            df["payout_frequency"] = df.swifter.apply(self.add_payout_frequency, axis=1)
            df["time_zone"] = df.swifter.apply(self.add_current_timezone, axis=1)
            df["preferred_language"] = df.swifter.apply(
                self.add_preferred_language, axis=1
            )
            df = g_utils.remove_nan_nat(df)

            return df
        return pd.DataFrame()

    def load_data(self, **kwargs) -> None:
        """
        loader for  user report, invalidates the existing data
        based on the snapshot keys
        """
        transformed_data = kwargs.get("transformed_data")
        report_object_id = kwargs.get("report_object_id")
        knowledge_date = kwargs.get("knowledge_date")
        data = transformed_data.to_dict("records")
        changes_start_time = kwargs.get("changes_start_time")
        is_historical_sync_run = kwargs.get("is_historical_sync_run")
        e2e_sync_run_id = kwargs.get("e2e_sync_run_id", None)

        log_context = {
            "client_id": self.client_id,
            "object_id": report_object_id,
            "e2e_sync_run_id": e2e_sync_run_id,
        }
        logger = utils.LogWithContext(log_context)
        report_records = []
        row_keys = []
        snapshot_ids = [
            "employee_email_id",
        ]
        snapshot_keys = []
        cnt = 0
        invalidate_snapshot_keys = []
        if is_historical_sync_run:
            deleted_records = employee_acc.EmployeeAccessor(
                self.client_id
            ).get_deleted_records_by_changes_start_time(
                changes_start_time=changes_start_time
            )
            for rec in deleted_records:
                invalidate_composite_key = []
                for snap_id in snapshot_ids:
                    key = rec.get(snap_id)
                    invalidate_composite_key.append(str(key))
                invalidate_snapshot_key = "##::##".join(
                    invalidate_composite_key
                ).lower()
                invalidate_snapshot_keys.append(invalidate_snapshot_key)
                self.logger.debug(
                    f"Snap keys to invalidate len : {len(invalidate_snapshot_keys)}, "
                    f"keys: {invalidate_snapshot_keys}"
                )
        try:
            for rec in data:
                cnt += 1
                ever_object = EverObjectAccessor().get_ever_objs(
                    object_id=report_object_id
                )
                report_record = {
                    "client_id": self.client_id,
                    "knowledge_begin_date": knowledge_date,
                    "object_id": report_object_id,
                    "object_type": ever_object.data_origin,
                    "data": rec,
                }

                composite_key = list()
                composite_snap_key = list()

                pk = ever_object.primary_key
                for primary_key in pk:
                    composite_key.append(str(rec.get(primary_key)))

                for snap_id in snapshot_ids:
                    composite_snap_key.append(str(rec.get(snap_id)))

                row_key = "##::##".join(composite_key).lower()
                snap_key = "##::##".join(composite_snap_key).lower()
                report_record["row_key"] = row_key
                report_record["snapshot_key"] = snap_key

                snapshot_keys.append(snap_key)
                row_keys.append(row_key)
                report_records.append(report_record)
            table_name = get_system_object_data_table_name(
                client_id=self.client_id, object_id=report_object_id
            )
            write_to_snowflake_report_object(
                client_id=self.client_id,
                row_keys_to_invalidate=invalidate_snapshot_keys,
                snapshot_keys=snapshot_keys,
                object_id=report_object_id,
                knowledge_date=knowledge_date,
                report_records=report_records,
                is_historical_sync_run=is_historical_sync_run,
                logger=logger,
                table_name=table_name,
                e2e_sync_run_id=e2e_sync_run_id,
            )

        except Exception as e:
            logger.error(
                "LOADER %% EXCEPTION: {}".format(e),
                extra_context=traceback.print_exc(),
            )
            raise e
