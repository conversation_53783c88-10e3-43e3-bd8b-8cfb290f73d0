from typing import List

from django.db.models import QuerySet

from commission_engine.models.ever_object_model import EverObject, EverObjectVariable


class EverObjectAccessor:
    def __init__(self) -> None:
        pass

    def ever_obj_kd_aware(self):
        return EverObject.objects.filter(is_deleted=False).filter(
            knowledge_end_date__isnull=True
        )

    def get_ever_objs(self, object_id):
        qs = self.ever_obj_kd_aware()
        if isinstance(object_id, list):
            return list(qs.filter(ever_object_id__in=object_id))
        else:
            return qs.filter(ever_object_id=object_id).first()

    def get_ever_object_ids(self, data_origin=None):
        qs = self.ever_obj_kd_aware()
        if data_origin:
            qs = qs.filter(data_origin__in=list(data_origin))
        return qs.values_list("ever_object_id", flat=True)

    def get_ever_obj_by_data_origin(self, data_origin):
        qs = self.ever_obj_kd_aware()
        if isinstance(data_origin, list):
            return list(qs.filter(data_origin__in=data_origin))

        return qs.filter(data_origin=data_origin)

    def get_ever_objects_excluding_ids(
        self, ever_object_ids=None
    ) -> QuerySet[EverObject]:
        """
        Returns a list of report objects for this client.  If report_object_ids is None, then return all objects
        Else return all objects excluding the ids in the list
        """
        data_origins = ["commission_object", "system_object", "quote_object"]
        query_set = None
        if not ever_object_ids:
            query_set = self.ever_obj_kd_aware().filter(data_origin__in=data_origins)
        else:
            query_set = (
                self.ever_obj_kd_aware()
                .filter(data_origin__in=data_origins)
                .exclude(ever_object_id__in=ever_object_ids)
            )
        return query_set

    def get_primary_key(self, object_id):
        pk = list(
            self.ever_obj_kd_aware()
            .filter(ever_object_id=object_id)
            .values_list("primary_key", flat=True)
        )
        return pk[0] if pk else []

    def does_ever_object_exist(self, ever_object_id):
        return self.ever_obj_kd_aware().filter(ever_object_id=ever_object_id).exists()

    def get_table_name(self, object_id):
        return (
            self.ever_obj_kd_aware()
            .filter(ever_object_id=object_id)
            .values_list("table_name", flat=True)
        )


class EverObjectVariableAccessor:
    def __init__(self) -> None:
        pass

    def ever_obj_kd_aware(self):
        return EverObjectVariable.objects.filter(is_deleted=False).filter(
            knowledge_end_date__isnull=True
        )

    def get_ever_object_variable(self, object_id):
        return list(
            (
                self.ever_obj_kd_aware().filter(ever_object_id=object_id)
                # .values_list("data_type_id", "system_name", "name")
                .values("data_type_id", "system_name", "display_name")
            )
        )

    def get_ever_object_variable_by_id(self, object_id):
        return self.ever_obj_kd_aware().filter(ever_object_id=object_id)

    def get_ds_var_system_name_and_dtype(self, object_id):
        return list(
            self.ever_obj_kd_aware()
            .filter(ever_object_id=object_id)
            .values_list("system_name", "data_type_id")
        )

    def get_variables_query_set_for_objects(
        self, ids: List[str]
    ) -> QuerySet[EverObjectVariable]:
        return (
            self.ever_obj_kd_aware()
            .filter(ever_object_id__in=ids)
            .order_by("ever_object_id")
        )

    def get_variable_by_system_name(self, object_id, system_name):
        return (
            self.ever_obj_kd_aware()
            .filter(ever_object_id=object_id, system_name=system_name)
            .first()
        )
