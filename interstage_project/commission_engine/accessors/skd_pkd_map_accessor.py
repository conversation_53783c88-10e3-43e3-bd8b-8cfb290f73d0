from datetime import datetime
from uuid import UUID

from django.db.models import Q

from commission_engine.accessors.databook_accessor import DatasheetAccessor
from commission_engine.models.skd_pkd_map_models import (
    CustomObjectPkdMap,
    DbkdPkdMap,
    ReportObjectPkdMap,
    SkdPkdMap,
)
from commission_engine.utils.general_data import ReportObject


class SkdPkdMapAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return SkdPkdMap.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_latest_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def get_rec_for_psd_ped(self, period_start_date, period_end_date, knowledge_date):
        qs = self.client_kd_deleted_aware(knowledge_date).filter(
            period_start_date=period_start_date,
            period_end_date=period_end_date,
        )
        if qs.exists():
            qs = qs.last()
            return qs
        else:
            return None

    def invalidate(self, psd, ped, kd):
        self.client_kd_latest_aware().filter(period_start_date=psd).filter(
            period_end_date=ped
        ).update(knowledge_end_date=kd)

    def get_latest(self, psd, ped):
        return self.client_kd_latest_aware().filter(
            period_start_date=psd, period_end_date=ped
        )

    def upsert_object(self, psd, ped, kd, snapshot_id):
        self.invalidate(psd, ped, kd)
        SkdPkdMap.objects.create(
            client_id=self.client_id,
            period_start_date=psd,
            period_end_date=ped,
            knowledge_begin_date=kd,
            secondary_snapshot_id=snapshot_id,
            primary_kd=kd,
        )


class DbkdPkdMapAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DbkdPkdMap.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_latest_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def get_rec_for_databook_id(self, databook_id, knowledge_date):
        qs = self.client_kd_deleted_aware(knowledge_date).filter(
            databook_id=databook_id
        )
        if qs.exists():
            qs = qs.last()
            return qs
        else:
            return None

    def get_rec_for_databook_id_datasheet_id(
        self, databook_id, datasheet_id, knowledge_date
    ):
        qs = self.client_kd_deleted_aware(knowledge_date).filter(
            databook_id=databook_id, datasheet_id=datasheet_id
        )
        if qs.exists():
            qs = qs.last()
            return qs
        else:
            return None

    def invalidate(self, databook_id, kd):
        self.client_kd_latest_aware().filter(databook_id=databook_id).update(
            knowledge_end_date=kd
        )

    def invalidate_with_datasheet(self, databook_id, datasheet_id, kd):
        self.client_kd_latest_aware().filter(
            databook_id=databook_id, datasheet_id=datasheet_id
        ).update(knowledge_end_date=kd)

    def get_latest(self, databook_ids):
        if not isinstance(databook_ids, list):
            databook_ids = [databook_ids]
        return self.client_kd_latest_aware().filter(databook_id__in=databook_ids)

    def upsert_object(self, databook_id, kd, audit=None):
        self.invalidate(databook_id, kd)
        DbkdPkdMap.objects.create(
            client_id=self.client_id,
            databook_id=databook_id,
            knowledge_begin_date=kd,
            primary_kd=kd,
            additional_details=audit,
        )

    def bulk_invalidate_and_insert_datasheet_records(
        self,
        client_id: int,
        datasheet_ids: list[UUID],
        knowledge_date: datetime,
        audit=None,
    ) -> None:
        """
        This method is used to invalidate all the records for the given datasheet_ids and insert new records with the
        """

        qs = self.client_kd_deleted_aware(knowledge_date=knowledge_date)
        qs = qs.filter(datasheet_id__in=datasheet_ids)

        qs.update(knowledge_end_date=knowledge_date)
        ds_accessor = DatasheetAccessor(client_id=client_id)
        get_datasheet_id_to_databook_id_dict = (
            ds_accessor.get_datasheet_id_to_databook_id_dict(
                datasheet_ids=datasheet_ids
            )
        )
        dbpkd_objects_records = [
            DbkdPkdMap(
                knowledge_begin_date=knowledge_date,
                primary_kd=knowledge_date,
                additional_details=audit,
                client_id=client_id,
                databook_id=get_datasheet_id_to_databook_id_dict[datasheet_id],
                datasheet_id=datasheet_id,
            )
            for datasheet_id in datasheet_ids
        ]
        DbkdPkdMap.objects.bulk_create(dbpkd_objects_records)

    def create_object(self, databook_id, kd, audit=None):
        DbkdPkdMap.objects.create(
            client_id=self.client_id,
            databook_id=databook_id,
            knowledge_begin_date=kd,
            primary_kd=kd,
            additional_details=audit,
        )

    def get_first_record(self, db_id, ds_id):
        qs = self.client_aware().filter(
            is_deleted=False,
            databook_id=db_id,
            datasheet_id=ds_id,
            knowledge_end_date__isnull=False,
        )
        if qs.exists():
            qs = qs.earliest("knowledge_begin_date")
            return qs
        else:
            return None

    def get_datasheet_latest_primary_kd(
        self, databook_id: str, datasheet_id: str, knowledge_date: datetime = None
    ):
        """
        gets latest primary_kd for the datasheet
        """
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date=knowledge_date)
        else:
            qs = self.client_kd_latest_aware()

        qs = qs.filter(
            databook_id=databook_id,
            datasheet_id=datasheet_id,
        ).values("primary_kd")

        return list(qs)

    def fetch_records_for_specified_columns(
        self, datasheet_ids=None, columns=None
    ) -> list:
        """
        This function is used to get record based on datasheet_ids for the specified columns.
        """
        query = self.client_kd_latest_aware()

        if datasheet_ids is not None:
            query = query.filter(datasheet_id__in=datasheet_ids)

        if columns is not None:
            query_result = query.values(*columns)
        else:
            query_result = query.values()

        return list(query_result)


class CustomObjectPkdMapAccessor:
    """Accessor class for CustomObjectPkdMap"""

    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        """Returns entries for the given client"""
        return CustomObjectPkdMap.objects.filter(client_id=self.client_id)

    def upsert_custom_object_primary_kd(
        self,
        custom_object_id: int,
        primary_kd: datetime,
        e2e_sync_run_id: UUID | None = None,
    ):
        """
        Upsert an entry for the given custom_object_id and primary_kd
        If the given custom_object_id already exists, update the primary_kd
        Else, create a new entry for the custom_object_id
        """
        defaults = {
            "primary_kd": primary_kd,
            "e2e_sync_run_id": e2e_sync_run_id,
        }

        self.client_aware().update_or_create(
            client_id=self.client_id,
            custom_object_id=custom_object_id,
            defaults=defaults,
        )

    def get_custom_objects_primary_kd(
        self, custom_object_ids: set[str]
    ) -> dict[str, datetime]:
        """
        Return the corresponding primary KDs for the given custom object IDs
        """
        results = self.client_aware().filter(custom_object_id__in=custom_object_ids)
        return {str(co_pkd.custom_object_id): co_pkd.primary_kd for co_pkd in results}


REPORT_TABLE_MAP = {report.value: report.table_name for report in ReportObject}


class ReportObjectPkdMapAccessor:
    def __init__(self, client_id):
        """Accessor class for ReportObjectPkdMap"""
        self.client_id = client_id

    def client_aware(self):
        """Returns entries for the given client"""
        return ReportObjectPkdMap.objects.filter(client_id=self.client_id)

    def upsert_report_primary_kd(
        self,
        report_object_id: str,
        primary_kd: datetime | None,
        e2e_sync_run_id: UUID | None = None,
    ):
        """
        Upsert an entry for the given report_object_id and primary_kd
        If the given report_object_id already exists, update the primary_kd
        Else, create a new entry for the report_object_id
        """
        report_type = REPORT_TABLE_MAP[report_object_id]

        defaults = {
            "primary_kd": primary_kd,
            "report_type": report_type,
            "e2e_sync_run_id": e2e_sync_run_id,
        }

        self.client_aware().update_or_create(
            client_id=self.client_id,
            report_object_id=report_object_id,
            defaults=defaults,
        )

    def get_reports_primary_kd(self, report_objects: list[str]) -> dict[str, datetime]:
        """
        Return the corresponding primary KDs for the given report objects
        """
        results = self.client_aware().filter(report_object_id__in=report_objects)

        return {ro_pkd.report_object_id: ro_pkd.primary_kd for ro_pkd in results}
