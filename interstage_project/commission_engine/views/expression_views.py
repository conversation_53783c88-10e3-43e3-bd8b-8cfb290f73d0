import json
import logging
import traceback
from datetime import datetime
from typing import Any
from uuid import uuid4

from django.http import FileResponse
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.accessors.accessor_factories.simulated_commission_accessor_factory import (
    SimulatedCommissionAccessorFactory,
)
from commission_engine.decorators.client_feature_decorator import requires_features
from commission_engine.services.commission_calculation_service.commission_simulation import (
    all_payees_simulation,
    delete_simulated_commissions,
    export_simulated_records_as_csv,
    get_simulated_records,
    get_simulated_records_count,
    simulate,
)
from commission_engine.services.commission_calculation_service.commission_simulation.simulation_manager import (
    get_payee_simulation_data,
    get_simulated_records_for_export_csv,
)
from commission_engine.services.commission_calculation_service.criteria_calculation_service import (
    ExpressionValidationServiceForV2,
    validate_infix,
    validate_infix_wrapper,
)
from commission_engine.services.expression_designer import (
    ContextMetaDataModel,
    ValidationContextModel,
    autocomplete_context,
)
from commission_engine.utils.general_data import (
    CLIENT_FEATURES,
    COMMISSION_TYPE,
    RESPONSE__PERMISSION_DENIED,
    RESPONSE_PLAN_PERMISSION_DENIED,
    RBACComponent,
    RbacPermissions,
)
from commission_engine.utils.rbac_decorators import rbac_with_simulation_id
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    authorize_for_profile_lookup_v2,
    requires_scope,
)
from interstage_project.camel_case_parser_utils import (
    UnderscoreBeforeNumberCamelCaseJSONParserPlans,
)
from interstage_project.utils import add_log_context_view, handle_ever_exception, log_me
from spm.services.rbac_services import is_payout_value_permission
from spm.services.rbac_commission_plan_services import PlanPermissions

logger_new = logging.getLogger(__name__)


class ValidateExp(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_COMMISSIONPLAN.value,
                RbacPermissions.MANAGE_DATABOOK.value,
                RbacPermissions.VIEW_DATABOOK.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        req_data = request.data
        version, expression, context = "v1", req_data, None

        result = {}

        if isinstance(req_data, dict):
            # For v2 expression validation
            (
                version,
                expression,
                additional_vars,
                is_ds_variables_required,
                has_intermediate_variables,
                transformation_order,
                output_columns,
                additional_source_variables,
            ) = (
                req_data.get("expression_version"),
                req_data.get("expression"),
                req_data.get("additional_variables", []),
                req_data.get("is_ds_variables_required", False),
                req_data.get("has_intermediate_variables", False),
                req_data.get("transformation_order", 0),
                req_data.get("output_columns", []),
                req_data.get("additional_source_variables", []),
            )

            context = ValidationContextModel(**req_data["meta"])

            if is_ds_variables_required:
                additional_vars = autocomplete_context.get_source_variables_wrapper(
                    client_id=client_id,
                    databook_id=context.databook_id,
                    datasheet_id=context.datasheet_id,  # type: ignore
                    context_meta_data=context.context_meta,
                    has_intermediate_variables=has_intermediate_variables,
                    transformation_order=transformation_order,
                    output_columns=output_columns,
                    additional_source_variables=additional_source_variables,
                )

            additional_vars_modified = []
            for item in additional_vars:
                new_item = {
                    "system_name": item["value"],
                    "data_type": item["meta"]["data_type"],
                    "data_type_id": item["meta"]["data_type_id"],
                    "display_name": item["label"],
                }
                additional_vars_modified.append(new_item)

            context.additional_variables = additional_vars_modified

            result = validate_infix_wrapper(
                client_id,
                expression,
                context,
                version,
                is_datasheet_v2=is_ds_variables_required,
            )
        elif isinstance(req_data, list):
            result = validate_infix(expression)

        return Response(result, status=status.HTTP_201_CREATED)


class EvaluateExp(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("EvaluateExp")
    def post(self, request):
        try:
            client_id = request.client_id
            login_user = str(request.user)
            params = request.data
            logger = request.logger
            timestamp = datetime.now()
            plan_id = params.get("plan_id")
            payee_email_ids = params.get("payee_email_ids")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_email_lookup(
                client_id,
                login_user,
                payee_email_ids,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            exp_result = all_payees_simulation(
                client_id,
                login_user,
                timestamp,
                params,
                logger,
                COMMISSION_TYPE.COMMISSION,
            )

            if "error" in exp_result:
                return Response(exp_result, status=status.HTTP_400_BAD_REQUEST)

            res = json.dumps(exp_result, default=str)
            res = json.loads(res)
            return Response(res, status=status.HTTP_200_OK)
        except Exception as e:
            log_me(traceback.print_exc())
            log_me("EVALUATE EXCEPTION : {}".format(e))
            return Response("EVALUATE EXCEPTION", status=status.HTTP_400_BAD_REQUEST)


class EvaluateExpPlanV2(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("EvaluateExp")
    def post(self, request):
        try:
            client_id = request.client_id
            login_user = str(request.user)
            params = request.data

            plan_id = params.get("plan_id")
            payee_email_id = params.get("payee_email_id")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            logger = request.logger
            page_size = params.get("page_size", 20)
            current_page = params.get("current_page", 0)
            orderby_fields = params.get("orderby_fields", [])
            exp_result: dict[str, Any] = get_payee_simulation_data(
                client_id=client_id,
                login_user=login_user,
                params=params,
                logger=logger,
                page_size=page_size,
                current_page=current_page,
                orderby_fields=orderby_fields,
            )

            if "error" in exp_result:
                return Response(exp_result, status=status.HTTP_400_BAD_REQUEST)

            exp_result["simulation_data"]["data"] = json.dumps(
                exp_result["simulation_data"]["data"], default=str
            )

            return Response(exp_result, status=status.HTTP_200_OK)
        except Exception as e:
            logger_new.exception("Error in evaluate criteria")
            return Response(
                {"error": "EVALUATE EXCEPTION", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ForecastEvaluateExpPlanV2(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    @add_log_context_view("EvaluateExp")
    def post(self, request):
        try:
            client_id = request.client_id
            login_user = str(request.user)
            params = request.data

            plan_id = params.get("plan_id")
            payee_email_id = params.get("payee_email_id")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user, COMMISSION_TYPE.FORECAST
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            logger = request.logger
            page_size = params.get("page_size", 20)
            current_page = params.get("current_page", 0)
            orderby_fields = params.get("orderby_fields", [])
            exp_result: dict[str, Any] = get_payee_simulation_data(
                client_id=client_id,
                login_user=login_user,
                params=params,
                logger=logger,
                page_size=page_size,
                current_page=current_page,
                orderby_fields=orderby_fields,
                commission_type=COMMISSION_TYPE.FORECAST,
            )

            if "error" in exp_result:
                return Response(exp_result, status=status.HTTP_400_BAD_REQUEST)

            exp_result["simulation_data"]["data"] = json.dumps(
                exp_result["simulation_data"]["data"], default=str
            )

            return Response(exp_result, status=status.HTTP_200_OK)
        except Exception as e:
            logger_new.exception("Error in evaluate criteria - Forecast")
            return Response(
                {"error": "EVALUATE EXCEPTION", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ExportSimulatedRecordsAsCsvPlanV2(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("ExportSimulatedRecordsAsCsvPlanV2")
    def post(self, request):
        try:
            client_id = request.client_id
            login_user = str(request.user)
            params = request.data

            plan_id = params.get("plan_id")
            payee_email_id = params.get("payee_email_id")

            if plan_id is not None:
                current_user = request.user.username
                if not PlanPermissions(client_id, current_user).can_user_view_plan(
                    plan_id
                ):
                    return Response(
                        RESPONSE_PLAN_PERMISSION_DENIED,
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Check authorization for the payee
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )
            if not has_payout_value_permission and payee_email_id != login_user:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            timestamp = datetime.now()
            logger = request.logger
            criteria_columns = params.get("criteria_columns", [])
            columns_list = params.get("columns_list", [])
            df, is_success = get_simulated_records_for_export_csv(
                client_id=client_id,
                login_user=login_user,
                timestamp=timestamp,
                params=params,
                logger=logger,
                criteria_columns=criteria_columns,
                columns_list=columns_list,
                commission_type=COMMISSION_TYPE.COMMISSION,
            )

            if is_success is False:
                return Response("ERROR", status=status.HTTP_400_BAD_REQUEST)

            return FileResponse(
                df.to_csv(index=False),
                as_attachment=True,
                filename=f"Simulation_{params['payee_email_id']}.csv",
            )
        except Exception:
            logger_new.exception("Error in export evaluation criteria")
            return Response("EVALUATE EXCEPTION", status=status.HTTP_400_BAD_REQUEST)


class ExportSimulatedForecastRecordsAsCsvPlanV2(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("ExportSimulatedForecastRecordsAsCsvPlanV2")
    def post(self, request):
        try:
            client_id = request.client_id
            login_user = str(request.user)
            params = request.data

            plan_id = params.get("plan_id")
            payee_email_id = params.get("payee_email_id")

            if plan_id is not None:
                current_user = request.user.username
                if not PlanPermissions(
                    client_id, current_user, COMMISSION_TYPE.FORECAST
                ).can_user_view_plan(plan_id):
                    return Response(
                        RESPONSE_PLAN_PERMISSION_DENIED,
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Check authorization for the payee
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )
            if not has_payout_value_permission and payee_email_id != login_user:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            timestamp = datetime.now()
            logger = request.logger
            criteria_columns = params.get("criteria_columns", [])
            columns_list = params.get("columns_list", [])
            df, is_success = get_simulated_records_for_export_csv(
                client_id=client_id,
                login_user=login_user,
                timestamp=timestamp,
                params=params,
                logger=logger,
                criteria_columns=criteria_columns,
                columns_list=columns_list,
                commission_type=COMMISSION_TYPE.FORECAST,
            )

            if is_success is False:
                return Response("ERORR", status=status.HTTP_400_BAD_REQUEST)

            return FileResponse(
                df.to_csv(index=False),
                as_attachment=True,
                filename=f"Simulation_{params['payee_email_id']}.csv",
            )
        except Exception:
            logger_new.exception("Error in export evaluation criteria - Forecast")
            return Response("EVALUATE EXCEPTION", status=status.HTTP_400_BAD_REQUEST)


class EvaluateExpV2(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("EvaluateExpV2")
    def post(self, request):
        """
        following params are expected in request.data
            - same params as required in EvaluateExp, plus
            - criteria_columns
            - session_id
        """
        try:
            client_id = request.client_id
            login_user = request.user.username
            params = request.data

            plan_id = params.get("plan_id")
            payee_email_ids = params.get("payee_email_ids")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_email_lookup(
                client_id,
                login_user,
                payee_email_ids,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )

            logger = request.logger
            session_id = params["session_id"]
            timestamp = datetime.now()
            simulation_id = uuid4()

            resp = simulate(
                client_id,
                login_user,
                session_id,
                simulation_id,
                timestamp,
                params,
                logger,
                commission_type=COMMISSION_TYPE.COMMISSION,
            )

            if "error" in resp:
                return Response(resp, status=status.HTTP_400_BAD_REQUEST)

            # Apply view:payoutvalueothers permission - mask sensitive values
            if (
                not has_payout_value_permission
                and "payees" in resp
                and isinstance(resp.get("payees"), list)
            ):
                for payee in resp["payees"]:
                    # Only show values for the logged-in user's own data
                    if (
                        isinstance(payee, dict)
                        and payee.get("employee_email_id") != login_user
                    ):
                        # Mask sensitive commission and payout values
                        if "commission" in payee:
                            payee["commission"] = "-"
                        if "tier_value" in payee:
                            payee["tier_value"] = "-"
                        if "quota_erosion" in payee:
                            payee["quota_erosion"] = "-"

                        # Mask summary data
                        if "summary" in payee:
                            payee["summary"] = "-"

                        # Mask detailed result data
                        if "result" in payee and isinstance(payee["result"], list):
                            for result_item in payee["result"]:
                                if isinstance(result_item, dict):
                                    if "commission" in result_item:
                                        result_item["commission"] = "-"
                                    if "tier_value" in result_item:
                                        result_item["tier_value"] = "-"
                                    if "quota_erosion" in result_item:
                                        result_item["quota_erosion"] = "-"

                # Mask total_commissions at the top level
                if "total_commissions" in resp:
                    # Only show total if user has permission or if it's only their own data
                    user_payees = [
                        p
                        for p in resp["payees"]
                        if isinstance(p, dict)
                        and p.get("employee_email_id") == login_user
                    ]
                    if len(user_payees) != len(resp["payees"]):
                        resp["total_commissions"] = "-"

            return Response(resp, status=status.HTTP_200_OK)
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EVALUATE-V2 EXCEPTION : {}".format(exc))
            return Response(
                {"error": "EVALUATE-V2 EXCEPTION", "details": str(exc)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ForecastEvaluateExpV2(APIView):
    parser_classes = (UnderscoreBeforeNumberCamelCaseJSONParserPlans,)

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("EvaluateExpV2")
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    def post(self, request):
        """
        following params are expected in request.data
            - same params as required in EvaluateExp, plus
            - criteria_columns
            - session_id
        """
        try:
            client_id = request.client_id
            login_user = request.user.username
            params = request.data

            plan_id = params.get("plan_id")
            payee_email_ids = params.get("payee_email_ids")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user, COMMISSION_TYPE.FORECAST
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_email_lookup(
                client_id,
                login_user,
                payee_email_ids,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )

            logger = request.logger
            session_id = params["session_id"]
            timestamp = datetime.now()
            simulation_id = uuid4()

            resp = simulate(
                client_id,
                login_user,
                session_id,
                simulation_id,
                timestamp,
                params,
                logger,
                commission_type=COMMISSION_TYPE.FORECAST,
            )

            if "error" in resp:
                return Response(resp, status=status.HTTP_400_BAD_REQUEST)

            # Apply view:payoutvalueothers permission - mask sensitive values
            if (
                not has_payout_value_permission
                and "payees" in resp
                and isinstance(resp.get("payees"), list)
            ):
                for payee in resp["payees"]:
                    # Only show values for the logged-in user's own data
                    if (
                        isinstance(payee, dict)
                        and payee.get("employee_email_id") != login_user
                    ):
                        # Mask sensitive commission and payout values
                        if "commission" in payee:
                            payee["commission"] = "-"
                        if "tier_value" in payee:
                            payee["tier_value"] = "-"
                        if "quota_erosion" in payee:
                            payee["quota_erosion"] = "-"

                        # Mask summary data
                        if "summary" in payee:
                            payee["summary"] = "-"

                        # Mask detailed result data
                        if "result" in payee and isinstance(payee["result"], list):
                            for result_item in payee["result"]:
                                if isinstance(result_item, dict):
                                    if "commission" in result_item:
                                        result_item["commission"] = "-"
                                    if "tier_value" in result_item:
                                        result_item["tier_value"] = "-"
                                    if "quota_erosion" in result_item:
                                        result_item["quota_erosion"] = "-"

                # Mask total_commissions at the top level
                if "total_commissions" in resp:
                    # Only show total if user has permission or if it's only their own data
                    user_payees = [
                        p
                        for p in resp["payees"]
                        if isinstance(p, dict)
                        and p.get("employee_email_id") == login_user
                    ]
                    if len(user_payees) != len(resp["payees"]):
                        resp["total_commissions"] = "-"

            return Response(resp, status=status.HTTP_200_OK)
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EVALUATE-V2 EXCEPTION : {}".format(exc))
            return Response(
                {"error": "EVALUATE-V2 EXCEPTION", "details": str(exc)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SimulatedRecords(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(rbac_with_simulation_id(COMMISSION_TYPE.COMMISSION))
    @add_log_context_view("SimulatedRecords")
    def post(self, request):
        """
        following params are expected in request.data
            - simulation_id
            - simulation_timestamp
            - payee_email_id
            - is_line_item_level
            - databook_id
            - datasheet_id
            - date_field
            - payee_field
            - period_start_date
            - period_end_date
            - criteria_type
            - sort_by
            - sort_order
            - page
            - page_size
            - criteria_columns
            - columns_list [{system_name: <>, display_name: <>}, ...]
            - refs : {
                page: 1,
                page_size: 10,
                sort_by: "co_3_amount",
                sort_order: "asc",
                starting_value: <>,
                starting_rowkey: <>,
                starting_rowkey_simcom_index: <>,
                ending_value: <>,
                ending_rowkey: <>,
                ending_rowkey_simcom_index: <>,
            }
        """
        try:
            client_id = request.client_id
            login_user = request.user.username
            params = request.data
            logger = request.logger
            timestamp = datetime.now()
            plan_id = str(
                SimulatedCommissionAccessorFactory(
                    client_id, COMMISSION_TYPE.COMMISSION
                )
                .get_accessor()
                .get_plan_id_for_simulation_id(params["simulation_id"])
            )
            payee_email_id = params.get("payee_email_id")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )

            logger.info("BEGIN: Get Simulated Records")
            records = get_simulated_records(
                client_id,
                login_user,
                timestamp,
                params,
                logger,
                commission_type=COMMISSION_TYPE.COMMISSION,
            )
            logger.info("END: Get Simulated Records")

            # Apply view:payoutvalueothers permission - mask sensitive values if not authorized
            if not has_payout_value_permission and payee_email_id != login_user:
                if "data" in records and "columns" in records:
                    # Find indices of sensitive columns to mask
                    sensitive_columns = [
                        "commission",
                        "tier_value",
                        "quota_erosion",
                        "tierValue",
                        "quotaErosion",
                    ]
                    columns_to_mask = []

                    for i, col_name in enumerate(records["columns"]):
                        if col_name in sensitive_columns:
                            columns_to_mask.append(i)

                    # Mask sensitive values in data rows
                    if columns_to_mask and isinstance(records["data"], list):
                        for row in records["data"]:
                            if isinstance(row, list):
                                for col_index in columns_to_mask:
                                    if col_index < len(row):
                                        row[col_index] = "-"

            return Response(records, status=status.HTTP_200_OK)
        except PermissionError as exc:
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS",
                    "details": str(exc),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS",
                    "details": str(exc),
                },
                status=(
                    status.HTTP_204_NO_CONTENT
                    if str(exc) == "NO_RECORDS_FOUND"
                    else status.HTTP_400_BAD_REQUEST
                ),
            )


class SimulatedForecastRecords(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(rbac_with_simulation_id(COMMISSION_TYPE.FORECAST))
    @add_log_context_view("SimulatedForecastRecords")
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    def post(self, request):
        """
        following params are expected in request.data
            - simulation_id
            - simulation_timestamp
            - payee_email_id
            - is_line_item_level
            - databook_id
            - datasheet_id
            - date_field
            - payee_field
            - period_start_date
            - period_end_date
            - criteria_type
            - sort_by
            - sort_order
            - page
            - page_size
            - criteria_columns
            - columns_list [{system_name: <>, display_name: <>}, ...]
            - refs : {
                page: 1,
                page_size: 10,
                sort_by: "co_3_amount",
                sort_order: "asc",
                starting_value: <>,
                starting_rowkey: <>,
                starting_rowkey_simcom_index: <>,
                ending_value: <>,
                ending_rowkey: <>,
                ending_rowkey_simcom_index: <>,
            }
        """
        try:
            client_id = request.client_id
            login_user = request.user.username
            params = request.data
            logger = request.logger
            timestamp = datetime.now()
            plan_id = str(
                SimulatedCommissionAccessorFactory(client_id, COMMISSION_TYPE.FORECAST)
                .get_accessor()
                .get_plan_id_for_simulation_id(params["simulation_id"])
            )
            payee_email_id = params.get("payee_email_id")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )

            logger.info("BEGIN: Get Simulated Records")
            records = get_simulated_records(
                client_id,
                login_user,
                timestamp,
                params,
                logger,
                commission_type=COMMISSION_TYPE.FORECAST,
            )
            logger.info("END: Get Simulated Records")

            # Apply view:payoutvalueothers permission - mask sensitive values if not authorized
            if not has_payout_value_permission and payee_email_id != login_user:
                if "data" in records and "columns" in records:
                    # Find indices of sensitive columns to mask
                    sensitive_columns = [
                        "commission",
                        "tier_value",
                        "quota_erosion",
                        "tierValue",
                        "quotaErosion",
                    ]
                    columns_to_mask = []

                    for i, col_name in enumerate(records["columns"]):
                        if col_name in sensitive_columns:
                            columns_to_mask.append(i)

                    # Mask sensitive values in data rows
                    if columns_to_mask and isinstance(records["data"], list):
                        for row in records["data"]:
                            if isinstance(row, list):
                                for col_index in columns_to_mask:
                                    if col_index < len(row):
                                        row[col_index] = "-"

            return Response(records, status=status.HTTP_200_OK)
        except PermissionError as exc:
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS",
                    "details": str(exc),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS",
                    "details": str(exc),
                },
                status=(
                    status.HTTP_204_NO_CONTENT
                    if str(exc) == "NO_RECORDS_FOUND"
                    else status.HTTP_400_BAD_REQUEST
                ),
            )


class SimulatedRecordsCount(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(rbac_with_simulation_id(COMMISSION_TYPE.COMMISSION))
    @add_log_context_view("SimulatedRecordsCount")
    def post(self, request):
        """
        following params are expected in request.data
            - simulation_id
            - simulation_timestamp
            - payee_email_id
            - is_line_item_level
            - databook_id
            - datasheet_id
            - date_field
            - payee_field
            - period_start_date
            - period_end_date
        """
        try:
            client_id = request.client_id
            params = request.data
            logger = request.logger

            logger.info("BEGIN: Get Simulated Records Count")
            count = get_simulated_records_count(
                client_id, params, logger, commission_type=COMMISSION_TYPE.COMMISSION
            )
            logger.info("END: Get Simulated Records Count")

            return Response({"count": count}, status=status.HTTP_200_OK)
        except PermissionError as exc:
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS COUNT: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS COUNT",
                    "details": str(exc),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS COUNT: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS COUNT",
                    "details": str(exc),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class SimulatedForecastRecordsCount(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(rbac_with_simulation_id(COMMISSION_TYPE.FORECAST))
    @add_log_context_view("SimulatedForecastRecordsCount")
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    def post(self, request):
        """
        following params are expected in request.data
            - simulation_id
            - simulation_timestamp
            - payee_email_id
            - is_line_item_level
            - databook_id
            - datasheet_id
            - date_field
            - payee_field
            - period_start_date
            - period_end_date
        """
        try:
            client_id = request.client_id
            params = request.data
            logger = request.logger

            logger.info("BEGIN: Get Simulated Records Count")
            count = get_simulated_records_count(
                client_id, params, logger, commission_type=COMMISSION_TYPE.FORECAST
            )
            logger.info("END: Get Simulated Records Count")

            return Response({"count": count}, status=status.HTTP_200_OK)
        except PermissionError as exc:
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS COUNT: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS COUNT",
                    "details": str(exc),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN GETTING SIMULATED RECORDS COUNT: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN GETTING SIMULATED RECORDS COUNT",
                    "details": str(exc),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class DeleteSimulatedCommissions(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("DeleteSimulatedCommissions")
    def post(self, request):
        """
        following params are expected in request.data
            - delete_type : "SESSION" or "SIMULATION" or "OLDER" or "SESSION_AND_OLDER"
            - simulation_id : if delete_type is "SIMULATION"
            - session_id : if delete_type is "SESSION" or "SESSION_AND_OLDER"
        """
        try:
            client_id = request.client_id
            params = request.data
            logger = request.logger

            logger.info("Begin: Delete simulated commission records")
            resp = delete_simulated_commissions(
                client_id, params, commission_type=COMMISSION_TYPE.COMMISSION
            )
            logger.info(
                f"End: Delete simulated commission records - deleted {resp[0]} records"
            )
            return Response(resp, status=status.HTTP_200_OK)
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN DELETING SIMULATED COMMISSIONS: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN DELETING SIMULATED COMMISSIONS:",
                    "details": str(exc),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class DeleteSimulatedForecastCommissions(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("DeleteSimulatedCommissions")
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    def post(self, request):
        """
        following params are expected in request.data
            - delete_type : "SESSION" or "SIMULATION" or "OLDER" or "SESSION_AND_OLDER"
            - simulation_id : if delete_type is "SIMULATION"
            - session_id : if delete_type is "SESSION" or "SESSION_AND_OLDER"
        """
        try:
            client_id = request.client_id
            params = request.data
            logger = request.logger

            logger.info("Begin: Delete simulated commission records")
            resp = delete_simulated_commissions(
                client_id, params, commission_type=COMMISSION_TYPE.FORECAST
            )
            logger.info(
                f"End: Delete simulated commission records - deleted {resp[0]} records"
            )
            return Response(resp, status=status.HTTP_200_OK)
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN DELETING SIMULATED COMMISSIONS: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN DELETING SIMULATED COMMISSIONS:",
                    "details": str(exc),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class ExportSimulatedRecordsAsCsv(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(rbac_with_simulation_id(COMMISSION_TYPE.COMMISSION))
    @add_log_context_view("ExportSimulatedRecordsAsCsv")
    def post(self, request):
        """
        following params are expected in request.data
            - simulation_id
            - simulation_timestamp
            - payee_email_id
            - is_line_item_level
            - databook_id
            - datasheet_id
            - date_field
            - payee_field
            - period_start_date
            - period_end_date
            - criteria_type
            - criteria_columns
            - columns_list [{system_name: <>, display_name: <>}, ...]
        """
        try:
            client_id = request.client_id
            login_user = request.user.username
            params = request.data
            logger = request.logger
            timestamp = datetime.now()
            plan_id = str(
                SimulatedCommissionAccessorFactory(
                    client_id, COMMISSION_TYPE.COMMISSION
                )
                .get_accessor()
                .get_plan_id_for_simulation_id(params["simulation_id"])
            )
            payee_email_id = params.get("payee_email_id")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )
            if not has_payout_value_permission and payee_email_id != login_user:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            logger.info("Begin: Export simulated records as csv")
            csv = export_simulated_records_as_csv(
                client_id,
                login_user,
                timestamp,
                params,
                logger,
                commission_type=COMMISSION_TYPE.COMMISSION,
            )
            logger.info("End: Export simulated records as csv")
            return FileResponse(
                csv,
                as_attachment=True,
                filename=f"Simulation_{params['payee_email_id']}.csv",
            )
        except PermissionError as exc:
            log_me("EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV",
                    "details": str(exc),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV",
                    "details": str(exc),
                },
                status=(
                    status.HTTP_204_NO_CONTENT
                    if str(exc) == "NO_RECORDS_FOUND"
                    else status.HTTP_400_BAD_REQUEST
                ),
            )


class ExportSimulatedForecastRecordsAsCsv(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @method_decorator(rbac_with_simulation_id(COMMISSION_TYPE.FORECAST))
    @add_log_context_view("ExportSimulatedRecordsAsCsv")
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    def post(self, request):
        """
        following params are expected in request.data
            - simulation_id
            - simulation_timestamp
            - payee_email_id
            - is_line_item_level
            - databook_id
            - datasheet_id
            - date_field
            - payee_field
            - period_start_date
            - period_end_date
            - criteria_type
            - criteria_columns
            - columns_list [{system_name: <>, display_name: <>}, ...]
        """
        try:
            client_id = request.client_id
            login_user = request.user.username
            params = request.data
            logger = request.logger
            timestamp = datetime.now()
            plan_id = str(
                SimulatedCommissionAccessorFactory(client_id, COMMISSION_TYPE.FORECAST)
                .get_accessor()
                .get_plan_id_for_simulation_id(params["simulation_id"])
            )
            payee_email_id = params.get("payee_email_id")
            if plan_id is not None and not PlanPermissions(
                client_id, login_user
            ).can_user_view_plan(plan_id):
                return Response(
                    RESPONSE_PLAN_PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user,
                payee_email_id,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check view:payoutvalueothers permission for commission value access
            has_payout_value_permission = is_payout_value_permission(
                client_id, login_user
            )
            if not has_payout_value_permission and payee_email_id != login_user:
                return Response(
                    RESPONSE__PERMISSION_DENIED,
                    status=status.HTTP_403_FORBIDDEN,
                )

            logger.info("Begin: Export simulated records as csv")
            csv = export_simulated_records_as_csv(
                client_id,
                login_user,
                timestamp,
                params,
                logger,
                commission_type=COMMISSION_TYPE.FORECAST,
            )
            logger.info("End: Export simulated records as csv")
            return FileResponse(
                csv,
                as_attachment=True,
                filename=f"Simulation_{params['payee_email_id']}.csv",
            )
        except PermissionError as exc:
            log_me("EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV",
                    "details": str(exc),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV: {}".format(exc))
            return Response(
                {
                    "error": "EXCEPTION IN EXPORTING SIMULATED RECORDS AS CSV",
                    "details": str(exc),
                },
                status=(
                    status.HTTP_204_NO_CONTENT
                    if str(exc) == "NO_RECORDS_FOUND"
                    else status.HTTP_400_BAD_REQUEST
                ),
            )


class GetAutoCompleteContext(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_COMMISSIONPLAN.value,
                RbacPermissions.MANAGE_DATABOOK.value,
                RbacPermissions.VIEW_DATABOOK.value,
            ]
        ),
        name="dispatch",
    )
    @add_log_context_view("GetAutoCompleteContext")
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        context = request.data.get("context")
        context_meta_data = request.data.get("context_meta")
        databook_id = request.data.get("databook_id")
        datasheet_id = request.data.get("datasheet_id")
        is_line_item_level = request.data.get("is_line_item_level", True)
        is_plan_published = request.data.get("is_plan_published", False)
        is_ds_variables_required = request.data.get("is_ds_variables_required", False)
        has_intermediate_variables = request.data.get(
            "has_intermediate_variables", False
        )
        additional_source_variables = request.data.get(
            "additional_source_variables", []
        )
        transformation_order = request.data.get("transformation_order", 0)
        output_columns = request.data.get("output_columns", [])
        context_meta_data = ContextMetaDataModel(**context_meta_data)
        plan_id = request.data.get("plan_id")

        autocomplete_context_response = autocomplete_context.get_autocomplete_context(
            client_id=client_id,
            context=context,
            context_meta_data=context_meta_data,
            is_line_item_level=is_line_item_level,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            is_ds_variables_required=is_ds_variables_required,
            has_intermediate_variables=has_intermediate_variables,
            transformation_order=transformation_order,
            is_plan_published=is_plan_published,
            output_columns=output_columns,
            additional_source_variables=additional_source_variables,
            plan_id=plan_id,
        )

        return Response({"data": autocomplete_context_response})


class ValidateExpV2(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_COMMISSIONPLAN.value,
                RbacPermissions.MANAGE_DATABOOK.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        data = request.data
        return _validate_v2_expression(
            client_id, data, commission_type=COMMISSION_TYPE.COMMISSION
        )


class ForecastValidateExpV2(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_COMMISSIONPLAN.value,
                RbacPermissions.MANAGE_DATABOOK.value,
            ]
        ),
        name="dispatch",
    )
    @method_decorator(
        requires_features(CLIENT_FEATURES.SHOW_FORECAST.value),
        name="dispatch",
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        data = request.data
        return _validate_v2_expression(
            client_id, data, commission_type=COMMISSION_TYPE.FORECAST
        )


def _validate_v2_expression(client_id, data, commission_type):
    try:
        version, expression, context = "v1", data, None

        if isinstance(data, dict):
            # For v2 expression validation
            version, expression, additional_vars = (
                data.get("expression_version"),
                data.get("expression"),
                data.get("additional_variables", []),
            )
            context = ValidationContextModel(**data["meta"])

            additional_vars_modified = []
            for item in additional_vars:
                new_item = {
                    "system_name": item["value"],
                    "data_type": item["meta"]["data_type"],
                    "data_type_id": item["meta"]["data_type_id"],
                    "display_name": item["label"],
                }
                additional_vars_modified.append(new_item)

            context.additional_variables = additional_vars_modified

            result = ExpressionValidationServiceForV2(
                client_id=client_id, commission_type=commission_type
            ).validate_infix_wrapper(
                infix_exp=expression, context=context, version=version
            )
        elif isinstance(data, list):
            # For v1 expression validation
            result = ExpressionValidationServiceForV2(
                client_id=client_id, commission_type=commission_type
            ).validate_infix(infix_exp=expression)
        return Response(result, status=status.HTTP_201_CREATED)
    except ValueError as exc:
        result = {
            "status": "INVALID",
            "msg": exc.args[0] if len(exc.args) > 0 else "Invalid infix expression",
            "data_type": "INVALID",
            "data_type_id": -1,
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)
    except Exception:
        traceback.print_exc()
        return Response({"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST)
