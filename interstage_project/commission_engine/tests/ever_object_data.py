from enum import Enum

from django.utils import timezone

import commission_engine.utils.report_utils as report_utils
from commission_engine.accessors.report_enrichment_accessor import (
    CommissionReportEnrichmentAccessor,
)
from commission_engine.models import EverObject, EverObjectVariable
from commission_engine.models.commission_models import (
    Commission,
    InterCommission,
    InterQuotaErosion,
    QuotaErosion,
)
from commission_engine.models.settlement_models import Settlement
from spm.accessors.custom_field_accessor import CustomFieldsAccessor
from spm.models.quota_models import EmployeeQuota


def add_ever_variables():
    curr_time = timezone.now()

    EverObjectVariable.objects.all().delete()

    VAR = [
        {
            "ever_object_id": "commission",
            "system_name": "period_label",
            "display_name": "Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "period_start_date",
            "display_name": "Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission",
            "system_name": "period_end_date",
            "display_name": "Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission",
            "system_name": "payee_name",
            "display_name": "Payee",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "payee_email_id",
            "display_name": "Payee Email",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "commission",
            "system_name": "payout_freq",
            "display_name": "Payout Frequency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "plan_name",
            "display_name": "Commission Plan",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "plan_type",
            "display_name": "Commission Type",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "record_type",
            "display_name": "Record Type",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "criteria_name",
            "display_name": "Criteria",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "line_item_id",
            "display_name": "Line Item Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "tier_id",
            "display_name": "Tier Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "tier_name",
            "display_name": "Tier",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "quota_erosion",
            "display_name": "Quota Retirement",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission",
            "system_name": "amount",
            "display_name": "Commission Amount (Org Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission",
            "system_name": "payee_currency",
            "display_name": "Payee Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "conversion_rate",
            "display_name": "Currency Conversion Rate",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission",
            "system_name": "amount_payee_currency",
            "display_name": "Commission Amount (Payout Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission",
            "system_name": "databook_name",
            "display_name": "Databook",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "datasheet_name",
            "display_name": "Datasheet",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "is_locked",
            "display_name": "Is Locked",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "commission",
            "system_name": "locked_kd",
            "display_name": "Locked Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission",
            "system_name": "knowledge_begin_date",
            "display_name": "Updated at",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission",
            "system_name": "commission_plan_id",
            "display_name": "Plan Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "criteria_id",
            "display_name": "Criteria Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission",
            "system_name": "adjustment_id",
            "display_name": "Adjustment Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "fiscal_year",
            "display_name": "Fiscal Year",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "commission_plan_id",
            "display_name": "Plan Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "payee_email_id",
            "display_name": "Payee Email",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "period_start_date",
            "display_name": "Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "period_end_date",
            "display_name": "Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "commission_amount",
            "display_name": "Commission Amount",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "comm_adj_for_plan",
            "display_name": "Adjustment Amount",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "is_locked",
            "display_name": "Is Locked",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "is_paid",
            "display_name": "Is Paid",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "variable_pay",
            "display_name": "Variable Pay",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "payout_kd",
            "display_name": "Payout Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "locked_kd",
            "display_name": "Locked Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "commission_summary",
            "system_name": "draw_adjustment_amount",
            "display_name": "Draw Adjustment Amount",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "employee_id",
            "display_name": "Employee ID",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "payee_name",
            "display_name": "Payee Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "payee_email_id",
            "display_name": "Payee Email ID",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "designation",
            "display_name": "Designation",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "current_manager",
            "display_name": "Current Manager",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "payee_or_manager",
            "display_name": "Payee or Manager",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "individual_or_team_quota",
            "display_name": "Individual or Team Quota",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "joining_date",
            "display_name": "Joining Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "exit_date",
            "display_name": "Exit Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "employment_country",
            "display_name": "Employment Country",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "payout_currency",
            "display_name": "Payout Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "payout_frequency",
            "display_name": "Payout Frequency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "annual_variable_pay",
            "display_name": "Annual Variable Pay",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "variable_pay_as_per_period",
            "display_name": "Variable Pay As Per Period",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "period_start_date",
            "display_name": "Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "period_end_date",
            "display_name": "Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "is_hidden_quota",
            "display_name": "Is Hidden Quota",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_category_name",
            "display_name": "Quota Category Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_category_display_name",
            "display_name": "Quota Category",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_period",
            "display_name": "Quota Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_schedule",
            "display_name": "Quota Schedule",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "period_timeframe",
            "display_name": "Period Timeframe",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_period_start_date",
            "display_name": "Quota Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_schedule_start_date",
            "display_name": "Quota Schedule Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "target",
            "display_name": "Target",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "attained",
            "display_name": "Attained",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "quota_attainment_percentage",
            "display_name": "Quota Attainment Percentage",
            "data_type_id": 6,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "is_locked",
            "display_name": "Is Locked",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "quota_attainment",
            "system_name": "locked_date",
            "display_name": "Locked Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota",
            "system_name": "employee_email_id",
            "display_name": "Employee Email ID",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_fiscal_year",
            "display_name": "Quota Fiscal Year",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_category_name",
            "display_name": "Quota Category Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_category_display_name",
            "display_name": "Quota Category",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_ui_hidden",
            "display_name": "Quota UI Hidden?",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_period_time_frame",
            "display_name": "Quota Period Time Frame",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota",
            "system_name": "is_team_quota",
            "display_name": "Is Team Quota?",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_period",
            "display_name": "Quota Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_schedule",
            "display_name": "Quota Schedule",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_period_start_date",
            "display_name": "Quota Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota",
            "system_name": "quota_schedule_start_date",
            "display_name": "Quota Schedule Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "quota",
            "system_name": "fully_ramped_quota_value",
            "display_name": "Fully Ramped Quota Value",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "quota",
            "system_name": "ramp_percentage",
            "display_name": "Ramp Percentage",
            "data_type_id": 6,
        },
        {
            "ever_object_id": "quota",
            "system_name": "ramp_adjusted_quota_value",
            "display_name": "Ramp Adjusted Quota Value",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "user",
            "system_name": "effective_start_date",
            "display_name": "Effective Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "user",
            "system_name": "effective_end_date",
            "display_name": "Effective End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "user",
            "system_name": "employee_id",
            "display_name": "Employee Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "joining_date",
            "display_name": "Joining Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "user",
            "system_name": "time_zone",
            "display_name": "Current Time Zone",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "designation",
            "display_name": "Designation",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "employment_country",
            "display_name": "Employment Country",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "payout_frequency",
            "display_name": "Payout Frequency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "pay_currency",
            "display_name": "Payout Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "fixed_pay",
            "display_name": "Fixed Pay",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "user",
            "system_name": "payee_variable_pay",
            "display_name": "Variable Pay",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "user",
            "system_name": "first_name",
            "display_name": "First Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "last_name",
            "display_name": "Last Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "user_role",
            "display_name": "Current User Role",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "created_date",
            "display_name": "Created Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "user",
            "system_name": "created_by",
            "display_name": "Created By",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "user",
            "system_name": "exit_date",
            "display_name": "Exit Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "user",
            "system_name": "last_commission_date",
            "display_name": "Exit Last Commission Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "user",
            "system_name": "status",
            "display_name": "Status",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "employee_email_id",
            "display_name": "Employee Email Id",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "user",
            "system_name": "reporting_manager_email_id",
            "display_name": "Reporting Manager EmailId",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "user",
            "system_name": "reporting_manager_name",
            "display_name": "Reporting Manager Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "active_plan",
            "display_name": "Active Primary Plan",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "active_spiffs",
            "display_name": "Active Spiff Plan",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "active_quota_categories",
            "display_name": "Active Quota Categories",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "preferred_language",
            "display_name": "Preferred Language",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "user",
            "system_name": "payee_or_manager",
            "display_name": "Payee/Manager",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "period_label",
            "display_name": "Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "period_start_date",
            "display_name": "Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "period_end_date",
            "display_name": "Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "payee_name",
            "display_name": "Payee",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "payee_email_id",
            "display_name": "Payee Email",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "payout_freq",
            "display_name": "Payout Frequency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "plan_name",
            "display_name": "Commission Plan",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "plan_type",
            "display_name": "Commission Type",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "criteria_name",
            "display_name": "Criteria",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "line_item_id",
            "display_name": "Line Item Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "tier_id",
            "display_name": "Tier Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "tier_name",
            "display_name": "Tier",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "quota_erosion",
            "display_name": "Quota Retirement",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "amount",
            "display_name": "Commission Amount (Org Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "payee_currency",
            "display_name": "Payee Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "conversion_rate",
            "display_name": "Currency Conversion Rate",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "amount_payee_currency",
            "display_name": "Commission Amount (Payout Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "databook_name",
            "display_name": "Databook",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "datasheet_name",
            "display_name": "Datasheet",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "is_locked",
            "display_name": "Is Locked",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "locked_kd",
            "display_name": "Locked Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "knowledge_begin_date",
            "display_name": "Updated at",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "commission_plan_id",
            "display_name": "Plan Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_commission",
            "system_name": "criteria_id",
            "display_name": "Criteria Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "employee_id",
            "display_name": "Employee ID",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "payee_name",
            "display_name": "Payee Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "payee_email_id",
            "display_name": "Payee Email ID",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "designation",
            "display_name": "Designation",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "current_manager",
            "display_name": "Current Manager",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "payee_or_manager",
            "display_name": "Payee or Manager",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "individual_or_team_quota",
            "display_name": "Individual or Team Quota",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "joining_date",
            "display_name": "Joining Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "exit_date",
            "display_name": "Exit Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "employment_country",
            "display_name": "Employment Country",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "payout_currency",
            "display_name": "Payout Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "payout_freq",
            "display_name": "Payout Frequency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "annual_variable_pay",
            "display_name": "Annual Variable Pay",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "variable_pay_as_per_period",
            "display_name": "Variable Pay As Per Period",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "period_start_date",
            "display_name": "Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "period_end_date",
            "display_name": "Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "is_hidden_quota",
            "display_name": "Is Hidden Quota",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_category_name",
            "display_name": "Quota Category Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_category_display_name",
            "display_name": "Quota Category",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_period",
            "display_name": "Quota Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_schedule",
            "display_name": "Quota Schedule",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "period_timeframe",
            "display_name": "Period Timeframe",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_period_start_date",
            "display_name": "Quota Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_schedule_start_date",
            "display_name": "Quota Schedule Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "target",
            "display_name": "Target",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "attained",
            "display_name": "Attained",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "quota_attainment_percentage",
            "display_name": "Quota Attainment Percentage",
            "data_type_id": 6,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "is_locked",
            "display_name": "Is Locked",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "inter_quota_attainment",
            "system_name": "locked_date",
            "display_name": "Locked Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "commission_row_key",
            "display_name": "Commission Line Item Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "line_item_id",
            "display_name": "Line Item Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "payee_name",
            "display_name": "Payee",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "payee_email_id",
            "display_name": "Payee Email",
            "data_type_id": 12,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "payout_freq",
            "display_name": "Payout Frequency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "plan_type",
            "display_name": "Commission Plan Type",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "record_type",
            "display_name": "Record Type",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "plan_id",
            "display_name": "Commission Plan Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "plan_name",
            "display_name": "Commission Plan",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "settlement_rule_id",
            "display_name": "Settlement Criteria Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "settlement_criteria_name",
            "display_name": "Settlement Criteria Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "period_label",
            "display_name": "Settlement Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "period_start_date",
            "display_name": "Settlement Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "period_end_date",
            "display_name": "Settlement Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "settlement_date",
            "display_name": "Settlement Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "criteria_id",
            "display_name": "Earned Commission Criteria Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "criteria_name",
            "display_name": "Earned Commission Criteria Name",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "comm_period_label",
            "display_name": "Earned Commission Period",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "comm_period_start_date",
            "display_name": "Commission Period Start Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "comm_period_end_date",
            "display_name": "Commission Period End Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "commission_date",
            "display_name": "Commission Date",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "comm_amount",
            "display_name": "Earned Commission Amount (Org Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "amount",
            "display_name": "Settlement Commission Amount (Org Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "comm_amount_payee_currency",
            "display_name": "Earned Commission Amount (Payee Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "amount_payee_currency",
            "display_name": "Settlement Commission Amount (Payee Currency)",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "adjustment_id",
            "display_name": "Adjustment Id",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "conversion_rate",
            "display_name": "Currency Conversion Rate",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "org_currency",
            "display_name": "Org Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "payee_currency",
            "display_name": "Payee Currency",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "variable_pay",
            "display_name": "Variable Pay",
            "data_type_id": 1,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "settlement_databook",
            "display_name": "Settlement Data Book",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "settlement_datasheet",
            "display_name": "Settlement Data Sheet",
            "data_type_id": 4,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "knowledge_begin_date",
            "display_name": "Updated at",
            "data_type_id": 2,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "is_locked",
            "display_name": "Settlement Is Locked",
            "data_type_id": 3,
        },
        {
            "ever_object_id": "settlement",
            "system_name": "locked_kd",
            "display_name": "Settlement Locked Date",
            "data_type_id": 2,
        },
    ]

    try:
        vars = []
        for var in VAR:
            # EverObjectVariable.objects.bulk_create([
            vars.append(
                EverObjectVariable(
                    **{
                        "knowledge_begin_date": curr_time,
                        "is_deleted": False,
                        "additional_details": {
                            "server": "0.0.0.0",
                            "updated_by": "<EMAIL>",
                        },
                        "ever_object_id": var["ever_object_id"],
                        "data_type_id": var["data_type_id"],
                        "system_name": var["system_name"],
                        "display_name": var["display_name"],
                    }
                )
            )
        EverObjectVariable.objects.bulk_create(vars)
        # ])

    except Exception as e:
        raise e


def add_ever_objects():
    curr_time = timezone.now()

    EverObject.objects.all().delete()

    var = []
    try:
        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "settlement",
                    "name": "Settlement",
                    "data_origin": report_utils.DataOrigin.COMMISSION_OBJECT.value,
                    "source_table": Settlement.__name__,
                    "primary_key": [
                        "period_start_date",
                        "period_end_date",
                        "payee_email_id",
                        "plan_id",
                        "criteria_id",
                        "settlement_rule_id",
                        "commission_row_key",
                        "line_item_id",
                        "adjustment_id",
                    ],
                    "table_name": "report_object_data",
                }
            )
        )
        # EverObject.objects.bulk_create([
        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "commission",
                    "name": "Commission",
                    "data_origin": report_utils.DataOrigin.COMMISSION_OBJECT.value,
                    "source_table": Commission.__name__,
                    "primary_key": [
                        "period_start_date",
                        "period_end_date",
                        "payee_email_id",
                        "commission_plan_id",
                        "criteria_id",
                        "line_item_id",
                        "tier_id",
                        "adjustment_id",
                    ],
                    "table_name": "report_object_data",
                }
            )
        )
        # ])
        # EverObject.objects.bulk_create([
        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "commission_summary",
                    "name": "Commission Summary",
                    "data_origin": report_utils.DataOrigin.COMMISSION_OBJECT.value,
                    "source_table": Commission.__name__,
                    "primary_key": [
                        "fiscal_year",
                        "payee_email_id",
                        "period_start_date",
                        "period_end_date",
                        "commission_plan_id",
                    ],
                    "table_name": "report_object_data",
                }
            )
        )
        # ])
        # EverObject.objects.bulk_create([
        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "quota_attainment",
                    "name": "Quota Attainment",
                    "data_origin": report_utils.DataOrigin.COMMISSION_OBJECT.value,
                    "source_table": QuotaErosion.__name__,
                    "primary_key": [
                        "period_start_date",
                        "period_end_date",
                        "payee_email_id",
                        "quota_category_name",
                        "individual_or_team_quota",
                    ],
                    "table_name": "report_object_data",
                }
            )
        )
        # ])
        # EverObject.objects.bulk_create([
        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "quota",
                    "name": "Quota",
                    "data_origin": report_utils.DataOrigin.SYSTEM_OBJECT.value,
                    "source_table": EmployeeQuota.__name__,
                    "primary_key": [
                        "employee_email_id",
                        "quota_category_name",
                        "is_team_quota",
                        "quota_period_start_date",
                        "quota_schedule_start_date",
                    ],
                    "table_name": "system_object_data",
                }
            )
        )
        # ])
        # EverObject.objects.bulk_create([
        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "user",
                    "name": "User",
                    "data_origin": report_utils.DataOrigin.SYSTEM_OBJECT.value,
                    "source_table": None,
                    "primary_key": [
                        "employee_email_id",
                        "effective_start_date",
                        "effective_end_date",
                    ],
                    "table_name": "system_object_data",
                }
            )
        )

        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "inter_quota_attainment",
                    "name": "Inter Quota Attainment",
                    "data_origin": report_utils.DataOrigin.INTER_OBJECT.value,
                    "source_table": InterQuotaErosion.__name__,
                    "primary_key": [
                        "period_start_date",
                        "period_end_date",
                        "payee_email_id",
                        "quota_category_name",
                        "individual_or_team_quota",
                    ],
                    "table_name": "inter_object_data",
                }
            )
        )

        var.append(
            EverObject(
                **{
                    "knowledge_begin_date": curr_time,
                    "is_deleted": False,
                    "additional_details": {
                        "server": "0.0.0.0",
                        "updated_by": "<EMAIL>",
                    },
                    "ever_object_id": "inter_commission",
                    "name": "Inter Commission",
                    "data_origin": report_utils.DataOrigin.INTER_OBJECT.value,
                    "source_table": InterCommission.__name__,
                    "primary_key": [
                        "period_start_date",
                        "period_end_date",
                        "payee_email_id",
                        "commission_plan_id",
                        "criteria_id",
                        "line_item_id",
                        "tier_id",
                    ],
                    "table_name": "inter_object_data",
                }
            )
        )

        # ])
        EverObject.objects.bulk_create(var)
    except Exception as e:
        raise e
