from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, TypedDict
from unittest.mock import Mock, patch
from uuid import UUID, uuid4

import pytest
from django.core.cache import cache
from django.utils.timezone import make_aware
from pydash import flatten

from commission_engine.accessors.etl_config_accessor import (
    ApiAccessConfigAccessor,
    EnrichmentConfigAccessor,
    TransformationConfigAccessor,
)
from commission_engine.models.etl_config_models import (
    ApiAccessConfig,
    EnrichmentConfig,
    TransformationConfig,
)
from commission_engine.third_party_connections.hubspot import Hubspot, ThirdPartyApi
from interstage_project.utils import LogWithContext

# pylint: skip-file


class HubspotRecord(TypedDict, total=False):
    id: str
    properties: Optional[Dict[str, Any]]
    createdAt: str
    updatedAt: str
    archived: bool
    archivedAt: Optional[str]


class HubspotApiResponse(TypedDict):
    total: Optional[int]
    results: List[HubspotRecord]
    paging: Optional[Dict[str, Any]]


datetime_now = make_aware(datetime.now())
created_at = (datetime_now - timedelta(days=28)).isoformat()
updated_at = (datetime_now - timedelta(days=14)).isoformat()


@pytest.mark.hubspot
@pytest.mark.django_db
@patch.object(EnrichmentConfigAccessor, "get_api_enrichments_by_source_objects")
@patch.object(TransformationConfigAccessor, "get_records_by_integration_id")
@patch.object(ThirdPartyApi, "api_call")
class TestHubspot:
    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "client_id, object_id, config_object, is_validation, api_response, fields, enrichments, final_result",
        [
            (
                1,
                "deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="deals",
                    request_url="https://api.hubapi.com/crm/v3/objects/deals",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                True,
                [
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {},
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        destination_object_id="1",
                        source_field="hs_object_id",
                        destination_field="co_1_deal_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [
                    {
                        "id": f"deal_{i:03}",
                        "properties": {
                            "hs_object_id": f"deal_{i:03}",
                        },
                        "createdAt": created_at,
                        "updatedAt": updated_at,
                        "archived": False,
                    }
                    for i in range(1, 101)
                ],
            ),
            (
                1,
                "deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="deals",
                    request_url="https://api.hubapi.com/crm/v3/objects/deals",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                False,
                [
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(101, 201)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(201, 251)
                        ],
                    },
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        destination_object_id="1",
                        source_field="hs_object_id",
                        destination_field="co_1_deal_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                (
                    [
                        {
                            "id": f"deal_{i:03}",
                            "properties": {
                                "hs_object_id": f"deal_{i:03}",
                            },
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                        }
                        for i in range(1, 251)
                    ],
                    [],
                ),
            ),
        ],
    )
    def test_get_changed_records_list(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        is_validation: bool,
        api_response: List[HubspotApiResponse],
        fields: List[TransformationConfig],
        enrichments: List[EnrichmentConfig],
        final_result: Tuple[List[Dict[str, Any]], List[str]],
    ):
        mock_api_call.side_effect = api_response
        mock_get_records_by_integration_id.return_value = fields
        mock_get_api_enrichments_by_source_objects.return_value = enrichments

        result = Hubspot().get_changed_records(
            client_id=client_id,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            object_id=object_id,
            _unused_primary_kd=datetime_now,
            changes_start_time=datetime_now - timedelta(days=28),
            sync_mode="changes",
            config_object=config_object,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
            is_validation=is_validation,
        )

        if is_validation:
            # For validation, we expect just the records
            assert len(result) == len(final_result)
            assert result == final_result
        else:
            # For non-validation, we expect a tuple of (records, deleted_fields)
            changed_records, deleted_fields = result
            assert len(changed_records) == len(final_result[0])
            assert changed_records == final_result[0]
            assert len(deleted_fields) == len(final_result[1])
            assert deleted_fields == final_result[1]

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "client_id, object_id, config_object, is_validation, api_response, fields, enrichments, final_result",
        [
            (
                1,
                "deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="deals",
                    request_url="https://api.hubapi.com/crm/v3/objects/deals/search",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    request_body={"last_modified_date_column": "hs_lastmodifieddate"},
                    additional_data={"api_type": "search"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                True,
                [
                    {
                        "total": 500,
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "200",
                            }
                        },
                    },
                    {},
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        destination_object_id="1",
                        source_field="hs_object_id",
                        destination_field="co_1_deal_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                ([{"hs_object_id": f"deal_{i:03}"} for i in range(1, 101)], []),
            ),
            (
                1,
                "deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="deals",
                    request_url="https://api.hubapi.com/crm/v3/objects/deals/search",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    request_body={"last_modified_date_column": "hs_lastmodifieddate"},
                    additional_data={"api_type": "search"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                False,
                [
                    {
                        "total": 500,
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "200",
                            }
                        },
                    },
                    {
                        "total": 500,
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(101, 201)
                        ],
                        "paging": {
                            "next": {
                                "after": "400",
                            }
                        },
                    },
                    {
                        "total": 500,
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(201, 251)
                        ],
                    },
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        destination_object_id="1",
                        source_field="hs_object_id",
                        destination_field="co_1_deal_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                ([{"hs_object_id": f"deal_{i:03}"} for i in range(1, 251)], []),
            ),
        ],
    )
    def test_get_changed_records_search(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        is_validation: bool,
        api_response: List[HubspotApiResponse],
        fields: List[TransformationConfig],
        enrichments: List[EnrichmentConfig],
        final_result: List[Dict[str, Any]],
    ):
        mock_api_call.side_effect = api_response
        mock_get_records_by_integration_id.return_value = fields
        mock_get_api_enrichments_by_source_objects.return_value = enrichments

        result = Hubspot().get_changed_records(
            client_id=client_id,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            object_id=object_id,
            _unused_primary_kd=datetime_now,
            changes_start_time=datetime_now - timedelta(days=28),
            sync_mode="changes",
            config_object=config_object,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
            is_validation=is_validation,
        )

        if is_validation:
            # For validation, we expect just the records
            assert len(result) == len(final_result[0])
            assert result == final_result[0]
        else:
            # For non-validation, we expect a tuple of (records, deleted_fields)
            changed_records, deleted_fields = result
            assert len(changed_records) == len(final_result[0])
            assert changed_records == final_result[0]
            assert len(deleted_fields) == len(final_result[1])
            assert deleted_fields == final_result[1]

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "client_id, object_id, config_object, is_validation, api_response, fields, enrichments, final_result",
        [
            (
                1,
                "owners",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="owners",
                    request_url="https://api.hubapi.com/crm/v3/owners",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                True,
                [
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "teams": [],
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {},
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="owners",
                        destination_object_id="1",
                        source_field="id",
                        destination_field="co_1_owner_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [
                    {
                        "id": f"owner_{i:03}",
                        "email": f"owner_{i:03}@hubspot.com",
                        "firstName": f"Owner",
                        "lastName": f"{i:03}",
                        "userId": i,
                        "createdAt": created_at,
                        "updatedAt": updated_at,
                        "archived": False,
                        "teams": [],
                    }
                    for i in range(1, 101)
                ],
            ),
            (
                1,
                "owners",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="owners",
                    request_url="https://api.hubapi.com/crm/v3/owners",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                False,
                [
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "teams": [],
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "teams": [],
                            }
                            for i in range(101, 201)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "teams": [],
                            }
                            for i in range(201, 251)
                        ],
                    },
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="owners",
                        destination_object_id="1",
                        source_field="id",
                        destination_field="co_1_owner_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                (
                    [
                        {
                            "id": f"owner_{i:03}",
                            "email": f"owner_{i:03}@hubspot.com",
                            "firstName": f"Owner",
                            "lastName": f"{i:03}",
                            "userId": i,
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                            "teams": [],
                        }
                        for i in range(1, 251)
                    ],
                    [],
                ),
            ),
        ],
    )
    def test_get_changed_records_owners(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        is_validation: bool,
        api_response: List[HubspotApiResponse],
        fields: List[TransformationConfig],
        enrichments: List[EnrichmentConfig],
        final_result: List[Dict[str, Any]],
    ):
        mock_api_call.side_effect = api_response
        mock_get_records_by_integration_id.return_value = fields
        mock_get_api_enrichments_by_source_objects.return_value = enrichments

        changed_records = Hubspot().get_changed_records(
            client_id=client_id,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            object_id=object_id,
            _unused_primary_kd=datetime_now,
            changes_start_time=datetime_now - timedelta(days=28),
            sync_mode="changes",
            config_object=config_object,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
            is_validation=is_validation,
        )

        assert len(changed_records) == len(final_result)
        assert changed_records == final_result

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "client_id, object_id, config_object, is_validation, api_response, fields, enrichments, final_result",
        [
            (
                1,
                "pipelines_deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="pipelines_deals",
                    request_url="https://api.hubapi.com/crm/v3/pipelines/deals",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                True,
                [
                    {
                        "results": [
                            {
                                "label": f"Pipeline {i:03}",
                                "displayOrder": i,
                                "id": f"pipeline_{i:03}",
                                "stages": [
                                    {
                                        "label": f"Stage {j:03}",
                                        "displayOrder": j,
                                        "id": f"stage_{j:03}",
                                        "createdAt": created_at,
                                        "updatedAt": updated_at,
                                        "archived": False,
                                    }
                                    for j in range(1, 6)
                                ],
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(1, 11)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {},
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="pipelines_deals",
                        destination_object_id="1",
                        source_field="id",
                        destination_field="co_1_pipeline_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [
                    {
                        "label": f"Pipeline {i:03}",
                        "displayOrder": i,
                        "id": f"pipeline_{i:03}",
                        "stages": [
                            {
                                "label": f"Stage {j:03}",
                                "displayOrder": j,
                                "id": f"stage_{j:03}",
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for j in range(1, 6)
                        ],
                        "createdAt": created_at,
                        "updatedAt": updated_at,
                        "archived": False,
                    }
                    for i in range(1, 11)
                ],
            ),
            (
                1,
                "pipelines_deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="pipelines_deals",
                    request_url="https://api.hubapi.com/crm/v3/pipelines/deals",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                False,
                [
                    {
                        "results": [
                            {
                                "label": f"Pipeline {i:03}",
                                "displayOrder": i,
                                "id": f"pipeline_{i:03}",
                                "stages": [
                                    {
                                        "label": f"Stage {j:03}",
                                        "displayOrder": j,
                                        "id": f"stage_{j:03}",
                                        "createdAt": created_at,
                                        "updatedAt": updated_at,
                                        "archived": False,
                                    }
                                    for j in range(1, 6)
                                ],
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(1, 11)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "label": f"Pipeline {i:03}",
                                "displayOrder": i,
                                "id": f"pipeline_{i:03}",
                                "stages": [
                                    {
                                        "label": f"Stage {j:03}",
                                        "displayOrder": j,
                                        "id": f"stage_{j:03}",
                                        "createdAt": created_at,
                                        "updatedAt": updated_at,
                                        "archived": False,
                                    }
                                    for j in range(1, 6)
                                ],
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(11, 21)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "label": f"Pipeline {i:03}",
                                "displayOrder": i,
                                "id": f"pipeline_{i:03}",
                                "stages": [
                                    {
                                        "label": f"Stage {j:03}",
                                        "displayOrder": j,
                                        "id": f"stage_{j:03}",
                                        "createdAt": created_at,
                                        "updatedAt": updated_at,
                                        "archived": False,
                                    }
                                    for j in range(1, 6)
                                ],
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for i in range(21, 26)
                        ],
                    },
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="pipelines_deals",
                        destination_object_id="1",
                        source_field="id",
                        destination_field="co_1_pipeline_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                (
                    [
                        {
                            "label": f"Pipeline {i:03}",
                            "displayOrder": i,
                            "id": f"pipeline_{i:03}",
                            "stages": [
                                {
                                    "label": f"Stage {j:03}",
                                    "displayOrder": j,
                                    "id": f"stage_{j:03}",
                                    "createdAt": created_at,
                                    "updatedAt": updated_at,
                                    "archived": False,
                                }
                                for j in range(1, 6)
                            ],
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                        }
                        for i in range(1, 26)
                    ],
                    [],
                ),
            ),
        ],
    )
    def test_get_changed_records_pipelines(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        is_validation: bool,
        api_response: List[HubspotApiResponse],
        fields: List[TransformationConfig],
        enrichments: List[EnrichmentConfig],
        final_result: List[Dict[str, Any]],
    ):
        mock_api_call.side_effect = api_response
        mock_get_records_by_integration_id.return_value = fields
        mock_get_api_enrichments_by_source_objects.return_value = enrichments

        changed_records = Hubspot().get_changed_records(
            client_id=client_id,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            object_id=object_id,
            _unused_primary_kd=datetime_now,
            changes_start_time=datetime_now - timedelta(days=28),
            sync_mode="changes",
            config_object=config_object,
            _unused_snapshot_key="",
            _unused_destination_object_type="",
            integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
            is_validation=is_validation,
        )

        assert len(changed_records) == len(final_result)
        assert changed_records == final_result

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "client_id, object_id, config_object, is_validation, api_response, fields, enrichments, final_result",
        [
            (
                1,
                "deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="deals_delete",
                    request_url="https://api.hubapi.com/crm/v3/objects/deals",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                True,
                [
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {},
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        destination_object_id="1",
                        source_field="hs_object_id",
                        destination_field="co_1_deal_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [
                    {"Id": f"deal_{i:03}", "hs_object_id": f"deal_{i:03}"}
                    for i in range(1, 101)
                ],
            ),
            (
                1,
                "deals",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="deals_delete",
                    request_url="https://api.hubapi.com/crm/v3/objects/deals",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                False,
                [
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                            }
                            for i in range(101, 201)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                            }
                            for i in range(201, 251)
                        ],
                    },
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        destination_object_id="1",
                        source_field="hs_object_id",
                        destination_field="co_1_deal_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [
                    {"Id": f"deal_{i:03}", "hs_object_id": f"deal_{i:03}"}
                    for i in range(1, 251)
                ],
            ),
        ],
    )
    def test_get_deleted_records_list(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        is_validation: bool,
        api_response: List[HubspotApiResponse],
        fields: List[TransformationConfig],
        enrichments: List[EnrichmentConfig],
        final_result: List[Dict[str, Any]],
    ):
        mock_api_call.side_effect = api_response
        mock_get_records_by_integration_id.return_value = fields
        mock_get_api_enrichments_by_source_objects.return_value = enrichments

        deleted_records = Hubspot().get_deleted_records(
            client_id=client_id,
            object_id=object_id,
            _unused_primary_kd=datetime_now,
            changes_start_time=datetime_now - timedelta(days=28),
            _unused_sync_mode="changes",
            del_config_obj=config_object,
            _unused_destination_object_type="",
            integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
            is_validation=is_validation,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
        )

        assert len(deleted_records) == len(final_result)
        assert deleted_records == final_result

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "client_id, object_id, config_object, is_validation, api_response, fields, enrichments, final_result",
        [
            (
                1,
                "owners",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="owners",
                    request_url="https://api.hubapi.com/crm/v3/owners",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                True,
                [
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                                "teams": [],
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {},
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="owners",
                        destination_object_id="1",
                        source_field="id",
                        destination_field="co_1_owner_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [{"Id": f"owner_{i:03}"} for i in range(1, 101)],
            ),
            (
                1,
                "owners",
                ApiAccessConfig(
                    client_id=1,
                    knowledge_begin_date=datetime_now,
                    source_object_id="owners",
                    request_url="https://api.hubapi.com/crm/v3/owners",
                    request_type="get",
                    request_header={
                        "Content-type": "application/json",
                        "Authorization": "Bearer {access_token}",
                    },
                    additional_data={"api_type": "list"},
                    access_token_config_id=1,
                    integration="hubspot",
                    response_key="results",
                    integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                ),
                False,
                [
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                                "teams": [],
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                                "teams": [],
                            }
                            for i in range(101, 201)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/owners?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"owner_{i:03}",
                                "email": f"owner_{i:03}@hubspot.com",
                                "firstName": f"Owner",
                                "lastName": f"{i:03}",
                                "userId": i,
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": True,
                                "archivedAt": updated_at,
                                "teams": [],
                            }
                            for i in range(201, 251)
                        ],
                    },
                ],
                [
                    TransformationConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="owners",
                        destination_object_id="1",
                        source_field="id",
                        destination_field="co_1_owner_id",
                        field_type="String",
                        transformation_logic_id=None,
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [],
                [{"Id": f"owner_{i:03}"} for i in range(1, 251)],
            ),
        ],
    )
    def test_get_deleted_records_owners(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        is_validation: bool,
        api_response: List[HubspotApiResponse],
        fields: List[TransformationConfig],
        enrichments: List[EnrichmentConfig],
        final_result: List[Dict[str, Any]],
    ):
        mock_api_call.side_effect = api_response
        mock_get_records_by_integration_id.return_value = fields
        mock_get_api_enrichments_by_source_objects.return_value = enrichments

        deleted_records = Hubspot().get_deleted_records(
            client_id=client_id,
            object_id=object_id,
            _unused_primary_kd=datetime_now,
            changes_start_time=datetime_now - timedelta(days=28),
            _unused_sync_mode="changes",
            del_config_obj=config_object,
            _unused_destination_object_type="",
            integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
            is_validation=is_validation,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
        )

        assert len(deleted_records) == len(final_result)
        assert deleted_records == final_result

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "pipeline_data, final_result",
        [
            (
                {
                    "results": [
                        {
                            "label": f"Pipeline {i:03}",
                            "displayOrder": i,
                            "id": f"pipeline_{i:03}",
                            "stages": [
                                {
                                    "label": f"Stage {j:03}",
                                    "displayOrder": j,
                                    "id": f"stage_{j:03}",
                                    "createdAt": created_at,
                                    "updatedAt": updated_at,
                                    "archived": False,
                                }
                                for j in range(1, 6)
                                if i % 2 == 0
                            ],
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                        }
                        for i in range(1, 26)
                    ]
                },
                flatten(
                    [
                        [
                            {
                                "pipeline_id": f"pipeline_{i:03}",
                                "label": f"Stage {j:03}",
                                "displayOrder": j,
                                "id": f"stage_{j:03}",
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for j in range(1, 6)
                            if i % 2 == 0
                        ]
                        for i in range(1, 26)
                    ]
                ),
            ),
            (
                {
                    "results": [
                        {
                            "label": f"Pipeline {i:03}",
                            "displayOrder": i,
                            "id": f"pipeline_{i:03}",
                            "stages": [
                                {
                                    "label": f"Stage {j:03}",
                                    "displayOrder": j,
                                    "id": f"stage_{j:03}",
                                    "createdAt": created_at,
                                    "updatedAt": updated_at,
                                    "archived": False,
                                }
                                for j in range(1, 6)
                            ],
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                        }
                        for i in range(1, 26)
                    ]
                },
                flatten(
                    [
                        [
                            {
                                "pipeline_id": f"pipeline_{i:03}",
                                "label": f"Stage {j:03}",
                                "displayOrder": j,
                                "id": f"stage_{j:03}",
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                            }
                            for j in range(1, 6)
                        ]
                        for i in range(1, 26)
                    ]
                ),
            ),
        ],
    )
    def test_get_stages_records(
        self,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        pipeline_data,
        final_result,
    ):
        hubspot = Hubspot()
        hubspot.logger = LogWithContext({})
        stages_records = hubspot.get_stages_records(pipeline_data)

        assert len(stages_records) == len(final_result)
        assert stages_records == final_result

    @pytest.mark.django_db
    @patch.object(ApiAccessConfigAccessor, "get_obj_by_integration_id_and_source_id")
    @patch.object(cache, "get")
    @patch.object(cache, "set")
    @pytest.mark.parametrize(
        "client_id, filtered_result, list_of_enrichment, is_validation, api_config_object, api_response, final_result",
        [
            (
                1,
                [{"hs_object_id": f"deal_{i:03}"} for i in range(1, 101)],
                [
                    EnrichmentConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        ref_object_key="hs_object_id",
                        enrichment_resp_key="results",
                        enrichment_id_key="id",
                        source_new_key="company_object",
                        enrichment_type="api",
                        ref_api_config_obj="ref_companies",
                        additional_data={"associated_object_id": "companies"},
                    )
                ],
                True,
                [
                    ApiAccessConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="ref_companies",
                        request_url="https://api.hubapi.com/crm/v4/objects/deals?associations=companies",
                        request_type="get",
                        request_header={
                            "Content-type": "application/json",
                            "Authorization": "Bearer {access_token}",
                        },
                        access_token_config_id=1,
                        integration="hubspot",
                        response_key="results",
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "associations": {
                                    "companies": {
                                        "results": [
                                            {
                                                "id": f"company_{i:03}",
                                                "type": "deal_to_company",
                                            }
                                        ]
                                    }
                                },
                            }
                            for i in range(1, 101)
                        ],
                    },
                ],
                [
                    {
                        "hs_object_id": f"deal_{i:03}",
                        "company_object": {
                            "id": f"deal_{i:03}",
                            "properties": {
                                "hs_object_id": f"deal_{i:03}",
                            },
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                            "associations": {
                                "companies": {
                                    "results": [
                                        {
                                            "id": f"company_{i:03}",
                                            "type": "deal_to_company",
                                        }
                                    ]
                                }
                            },
                        },
                    }
                    for i in range(1, 101)
                ],
            ),
            (
                1,
                [{"hs_object_id": f"deal_{i:03}"} for i in range(1, 251)],
                [
                    EnrichmentConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="deals",
                        ref_object_key="hs_object_id",
                        enrichment_resp_key="results",
                        enrichment_id_key="id",
                        source_new_key="company_object",
                        enrichment_type="api",
                        ref_api_config_obj="ref_companies",
                        additional_data={"associated_object_id": "companies"},
                    )
                ],
                False,
                [
                    ApiAccessConfig(
                        client_id=1,
                        knowledge_begin_date=datetime_now,
                        source_object_id="ref_companies",
                        request_url="https://api.hubapi.com/crm/v4/objects/deals?associations=companies",
                        request_type="get",
                        request_header={
                            "Content-type": "application/json",
                            "Authorization": "Bearer {access_token}",
                        },
                        access_token_config_id=1,
                        integration="hubspot",
                        response_key="results",
                        integration_id=UUID("7d021baa-d223-4bdb-ab31-499e662d2230"),
                    )
                ],
                [
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "associations": {
                                    "companies": {
                                        "results": [
                                            {
                                                "id": f"company_{i:03}",
                                                "type": "deal_to_company",
                                            }
                                        ]
                                    }
                                },
                            }
                            for i in range(1, 101)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "associations": {
                                    "companies": {
                                        "results": [
                                            {
                                                "id": f"company_{i:03}",
                                                "type": "deal_to_company",
                                            }
                                        ]
                                    }
                                },
                            }
                            for i in range(101, 201)
                        ],
                        "paging": {
                            "next": {
                                "after": "after",
                                "link": "https://api.hubapi.com/crm/v3/objects/deals?after=after",
                            }
                        },
                    },
                    {
                        "results": [
                            {
                                "id": f"deal_{i:03}",
                                "properties": {
                                    "hs_object_id": f"deal_{i:03}",
                                },
                                "createdAt": created_at,
                                "updatedAt": updated_at,
                                "archived": False,
                                "associations": {
                                    "companies": {
                                        "results": [
                                            {
                                                "id": f"company_{i:03}",
                                                "type": "deal_to_company",
                                            }
                                        ]
                                    }
                                },
                            }
                            for i in range(201, 251)
                        ],
                    },
                ],
                [
                    {
                        "hs_object_id": f"deal_{i:03}",
                        "company_object": {
                            "id": f"deal_{i:03}",
                            "properties": {
                                "hs_object_id": f"deal_{i:03}",
                            },
                            "createdAt": created_at,
                            "updatedAt": updated_at,
                            "archived": False,
                            "associations": {
                                "companies": {
                                    "results": [
                                        {
                                            "id": f"company_{i:03}",
                                            "type": "deal_to_company",
                                        }
                                    ]
                                }
                            },
                        },
                    }
                    for i in range(1, 251)
                ],
            ),
        ],
    )
    def test_apply_api_enrichment(
        self,
        mock_set_cache,
        mock_get_cache,
        mock_get_obj_by_integration_id_and_source_id,
        mock_api_call,
        mock_get_records_by_integration_id,
        mock_get_api_enrichments_by_source_objects,
        client_id: int,
        filtered_result: Dict,
        list_of_enrichment: List[EnrichmentConfig],
        is_validation: bool,
        api_config_object: List[ApiAccessConfig],
        api_response: List[HubspotApiResponse],
        final_result: List[Dict[str, Any]],
    ):
        mock_set_cache.return_value = None
        mock_get_cache.return_value = None
        mock_get_obj_by_integration_id_and_source_id.side_effect = api_config_object
        mock_api_call.side_effect = api_response

        hubspot = Hubspot()
        hubspot.logger = LogWithContext({})
        enriched_result = hubspot.apply_api_enrichment(
            client_id=client_id,
            filtered_result=filtered_result,
            list_of_enrichment=list_of_enrichment,
            current_page_records=set(),
            visited=set(),
            integration_id=None,
            is_validation=is_validation,
        )

        assert len(enriched_result) == len(final_result)
        assert enriched_result == final_result


class TestHubspotPagination:
    @pytest.fixture
    def hubspot(self):
        return Hubspot()

    @pytest.fixture
    def mock_api_call(self):
        with patch.object(Hubspot, "api_call") as mock:
            yield mock

    @pytest.mark.parametrize("test_id", [1, 2, 3])
    def test_update_search_pagination_filter_with_existing_filters(
        self, hubspot, test_id
    ):
        """
        Test updating search pagination filters in body after 10,000 records
        """
        if test_id == 1:
            body = {
                "limit": 100,
                "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
            }
            last_page_record = {"properties": {"hs_object_id": "10000"}}
            updated_body = hubspot._update_search_pagination_filter(
                body, last_page_record
            )
            assert "filterGroups" in updated_body
            assert len(updated_body["filterGroups"]) == 1
            assert updated_body["filterGroups"][0]["filters"][0] == {
                "value": "10000",
                "operator": "GT",
                "propertyName": "hs_object_id",
            }
        last_page_record = {"properties": {"hs_object_id": "12345"}}
        if test_id == 2:
            body = {
                "limit": 100,
                "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
                "filterGroups": [
                    {
                        "filters": [
                            {
                                "value": "1738230400294",
                                "operator": "GTE",
                                "propertyName": "lastmodifieddate",
                            }
                        ]
                    }
                ],
            }
            last_page_record = {"properties": {"hs_object_id": "10000"}}
            expected_updated_body = {
                "limit": 100,
                "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
                "filterGroups": [
                    {
                        "filters": [
                            {
                                "value": "1738230400294",
                                "operator": "GTE",
                                "propertyName": "lastmodifieddate",
                            },
                            {
                                "value": "10000",
                                "operator": "GT",
                                "propertyName": "hs_object_id",
                            },
                        ]
                    }
                ],
            }
            updated_body = hubspot._update_search_pagination_filter(
                body, last_page_record
            )
            assert updated_body == expected_updated_body
        if test_id == 3:
            body = {
                "limit": 100,
                "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
                "filterGroups": [
                    {
                        "filters": [
                            {
                                "value": "1738230400294",
                                "operator": "GTE",
                                "propertyName": "lastmodifieddate",
                            },
                            {
                                "value": "10000",
                                "operator": "GT",
                                "propertyName": "hs_object_id",
                            },
                        ]
                    }
                ],
            }
            last_page_record = {"properties": {"hs_object_id": "20000"}}
            expected_updated_body = {
                "limit": 100,
                "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
                "filterGroups": [
                    {
                        "filters": [
                            {
                                "value": "1738230400294",
                                "operator": "GTE",
                                "propertyName": "lastmodifieddate",
                            },
                            {
                                "value": "20000",
                                "operator": "GT",
                                "propertyName": "hs_object_id",
                            },
                        ]
                    }
                ],
            }
            updated_body = hubspot._update_search_pagination_filter(
                body, last_page_record
            )
            assert updated_body == expected_updated_body

    @pytest.mark.django_db
    def test_search_api_basic_pagination(self, hubspot, mock_api_call):
        """
        Test basic pagination functionality with search API
        """
        # Setup mock responses for two pages
        mock_api_call.side_effect = [
            # First page
            {
                "results": [
                    {
                        "id": "1",
                        "properties": {"hs_object_id": "1", "name": "Record 1"},
                    },
                    {
                        "id": "2",
                        "properties": {"hs_object_id": "2", "name": "Record 2"},
                    },
                ],
                "paging": {"next": {"after": "2"}},
            },
            # Second page
            {
                "results": [
                    {
                        "id": "3",
                        "properties": {"hs_object_id": "3", "name": "Record 3"},
                    },
                    {
                        "id": "4",
                        "properties": {"hs_object_id": "4", "name": "Record 4"},
                    },
                ],
                "paging": {"next": {"after": None}},
            },
        ]

        config_obj = Mock(
            request_url="https://api.hubspot.com/crm/v3/objects/deals/search",
            additional_data={"api_type": "search"},
            request_body={"last_modified_date_column": "hs_lastmodifieddate"},
        )

        result = hubspot.get_changed_records(
            client_id=2006,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            object_id="deals",
            _unused_primary_kd=None,
            changes_start_time=None,
            sync_mode="all",
            config_object=config_obj,
            _unused_snapshot_key=None,
            _unused_destination_object_type=None,
            integration_id=UUID("b8995040-c290-4322-a216-92da228d5a18"),
            is_validation=False,
            upstream_etl_version="v1",
        )

        assert len(result) == 2
        assert [r.get("name") for r in result[0]] == [
            "Record 1",
            "Record 2",
            "Record 3",
            "Record 4",
        ]
        assert mock_api_call.call_count == 2

    @pytest.mark.django_db
    @pytest.mark.parametrize("test_id", [1, 2])
    def test_pagination_beyond_10000_records(self, hubspot, mock_api_call, test_id):
        """
        Test handling of pagination beyond HubSpot's 10,000 record limit by verifying:
        1. Proper updating of pagination filters when hitting the 10,000 record limit
        2. Correct continuation of pagination after updating filters
        3. Complete data retrieval across multiple record chunks totalling 25,000 records
        """
        # Setup mock responses simulating 25,000 total records across 3 chunks
        mock_api_call.side_effect = [
            # First chunk (records 1-10000)
            {
                "results": [
                    {
                        "id": str(i),
                        "properties": {
                            "hs_object_id": str(i),
                            "name": f"Record {i}",
                            "hs_lastmodifieddate": "1738230400294",
                        },
                    }
                    for i in range(1, 10001)
                ],
                "paging": {"next": {"after": "10000"}},
            },
            # Second chunk (records 10001-20000)
            {
                "results": [
                    {
                        "id": str(i),
                        "properties": {
                            "hs_object_id": str(i),
                            "name": f"Record {i}",
                            "hs_lastmodifieddate": "1738230400294",
                        },
                    }
                    for i in range(10001, 20001)
                ],
                "paging": {"next": {"after": "20000"}},
            },
            # Final chunk (records 20001-25000)
            {
                "results": [
                    {
                        "id": str(i),
                        "properties": {
                            "hs_object_id": str(i),
                            "name": f"Record {i}",
                            "hs_lastmodifieddate": "1738250000000",
                        },
                    }
                    for i in range(20001, 25001)
                ],
                "paging": {"next": {"after": None}},
            },
        ]

        changes_start_time = datetime.fromtimestamp(1738230400294 / 1000)

        config_obj = Mock(
            request_url="https://api.hubspot.com/crm/v3/objects/deals/search",
            additional_data={"api_type": "search"},
            request_body={"last_modified_date_column": "hs_lastmodifieddate"},
        )

        if test_id == 1:
            result = hubspot.get_changed_records(
                client_id=2006,
                e2e_sync_run_id=uuid4(),
                sync_run_id=uuid4(),
                object_id="deals",
                _unused_primary_kd=None,
                changes_start_time=None,
                sync_mode="all",
                config_object=config_obj,
                _unused_snapshot_key=None,
                _unused_destination_object_type=None,
                integration_id=UUID("b8995040-c290-4322-a216-92da228d5a18"),
                is_validation=False,
                upstream_etl_version="v1",
            )

        if test_id == 2:
            result = hubspot.get_changed_records(
                client_id=2006,
                e2e_sync_run_id=uuid4(),
                sync_run_id=uuid4(),
                object_id="deals",
                _unused_primary_kd=None,
                changes_start_time=changes_start_time,
                sync_mode="changes",
                config_object=config_obj,
                _unused_snapshot_key=None,
                _unused_destination_object_type=None,
                integration_id=UUID("b8995040-c290-4322-a216-92da228d5a18"),
                is_validation=False,
                upstream_etl_version="v1",
            )

        # Verify complete data retrieval by comparing total records all chunks
        assert len(result[0]) == 25000  # pylint:disable=possibly-used-before-assignment

        # Verify API calls
        assert mock_api_call.call_count == 3

        # Examine the filter updates at 10,000 record boundaries
        call_args_list = mock_api_call.call_args_list

        # Verify second chunk request includes added pagination filter leaving remaining filters unchanged
        second_call_body = call_args_list[1][1]["body"]
        print(second_call_body)
        assert all(
            [
                (
                    f["value"] == "10000"
                    if f["propertyName"] == "hs_object_id" and f["operator"] == "GT"
                    else True
                )
                for f in filter_group["filters"]
            ]
            for filter_group in second_call_body["filterGroups"]
        )

        # Verify third chunk request updates pagination filter for next range
        third_call_body = call_args_list[2][1]["body"]
        assert all(
            [
                (
                    f["value"] == "20000"
                    if f["propertyName"] == "hs_object_id" and f["operator"] == "GT"
                    else True
                )
                for f in filter_group["filters"]
            ]
            for filter_group in third_call_body["filterGroups"]
        )
