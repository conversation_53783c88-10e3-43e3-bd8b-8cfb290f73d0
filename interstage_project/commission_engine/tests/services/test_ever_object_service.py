import datetime
from unittest.mock import patch

from django.test import TestCase

from commission_engine.accessors.ever_object_accessor import EverObjectVariableAccessor
from commission_engine.services.ever_object_service import (
    _get_system_name_from_variable_id,
    get_all_objects,
    get_all_variables_for_object,
)


# ruff: noqa: S104
@patch("commission_engine.services.ever_object_service.check_client_features_exist")
@patch("commission_engine.utils.report_utils.get_client_features")
class TestEverObjectService(TestCase):

    def test_get_all_objects(
        self, mock_get_client_features, mock_check_client_features_exist
    ):
        mock_get_client_features.return_value = {"modules": ["ICM"]}
        mock_check_client_features_exist.return_value = {
            "expose_comm_reports_in_plan": True,
            "show_forecast": True,
        }
        result = get_all_objects(client_id=4012)
        expected_result = [
            {
                "temporal_id": 48,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission",
                "name": "Commission",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 49,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission_summary",
                "name": "Commission Summary",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "fiscal_year",
                    "payee_email_id",
                    "period_start_date",
                    "period_end_date",
                    "commission_plan_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 55,
                "knowledge_begin_date": datetime.datetime(
                    2024, 2, 6, 9, 18, 6, 771127, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "forecast_commission",
                "name": "Forecast Commission",
                "data_origin": "forecast_object",
                "source_table": "ForecastCommission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                ],
                "table_name": "forecast_object_data",
            },
            {
                "temporal_id": 56,
                "knowledge_begin_date": datetime.datetime(
                    2024, 2, 6, 9, 18, 6, 771127, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "forecast_quota_attainment",
                "name": "Forecast Quota Attainment",
                "data_origin": "forecast_object",
                "source_table": "ForecastQuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "forecast_object_data",
            },
            {
                "temporal_id": 54,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "inter_commission",
                "name": "Inter Commission",
                "data_origin": "inter_object",
                "source_table": "InterCommission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                ],
                "table_name": "inter_object_data",
            },
            {
                "temporal_id": 57,
                "knowledge_begin_date": datetime.datetime(
                    2024, 2, 6, 9, 18, 6, 771127, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "inter_forecast_commission",
                "name": "Inter Forecast Commission",
                "data_origin": "inter_forecast_object",
                "source_table": "InterForecastCommission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                ],
                "table_name": "inter_forecast_object_data",
            },
            {
                "temporal_id": 58,
                "knowledge_begin_date": datetime.datetime(
                    2024, 2, 6, 9, 18, 6, 771127, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "inter_forecast_quota_attainment",
                "name": "Inter Forecast Quota Attainment",
                "data_origin": "inter_forecast_object",
                "source_table": "InterForecastQuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "inter_forecast_object_data",
            },
            {
                "temporal_id": 53,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "inter_quota_attainment",
                "name": "Inter Quota Attainment",
                "data_origin": "inter_object",
                "source_table": "InterQuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "inter_object_data",
            },
            {
                "temporal_id": 51,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota",
                "name": "Quota",
                "data_origin": "system_object",
                "source_table": "EmployeeQuota",
                "primary_key": [
                    "employee_email_id",
                    "quota_category_name",
                    "is_team_quota",
                    "quota_period_start_date",
                    "quota_schedule_start_date",
                ],
                "table_name": "system_object_data",
            },
            {
                "temporal_id": 50,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota_attainment",
                "name": "Quota Attainment",
                "data_origin": "commission_object",
                "source_table": "QuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 47,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "settlement",
                "name": "Settlement",
                "data_origin": "commission_object",
                "source_table": "Settlement",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "plan_id",
                    "criteria_id",
                    "settlement_rule_id",
                    "commission_row_key",
                    "line_item_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 52,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "user",
                "name": "User",
                "data_origin": "system_object",
                "source_table": None,
                "primary_key": [
                    "employee_email_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                "table_name": "system_object_data",
            },
        ]
        self.assertEqual(result, expected_result)

    def test_get_all_objects_with_only_inter_objects(
        self, mock_get_client_features, mock_check_client_features_exist
    ):
        mock_get_client_features.return_value = {"modules": ["ICM"]}
        mock_check_client_features_exist.return_value = {
            "expose_comm_reports_in_plan": True,
            "show_forecast": False,
        }
        result = get_all_objects(client_id=4012)
        expected_result = [
            {
                "temporal_id": 48,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission",
                "name": "Commission",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 49,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission_summary",
                "name": "Commission Summary",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "fiscal_year",
                    "payee_email_id",
                    "period_start_date",
                    "period_end_date",
                    "commission_plan_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 54,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "inter_commission",
                "name": "Inter Commission",
                "data_origin": "inter_object",
                "source_table": "InterCommission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                ],
                "table_name": "inter_object_data",
            },
            {
                "temporal_id": 53,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "inter_quota_attainment",
                "name": "Inter Quota Attainment",
                "data_origin": "inter_object",
                "source_table": "InterQuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "inter_object_data",
            },
            {
                "temporal_id": 51,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota",
                "name": "Quota",
                "data_origin": "system_object",
                "source_table": "EmployeeQuota",
                "primary_key": [
                    "employee_email_id",
                    "quota_category_name",
                    "is_team_quota",
                    "quota_period_start_date",
                    "quota_schedule_start_date",
                ],
                "table_name": "system_object_data",
            },
            {
                "temporal_id": 50,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota_attainment",
                "name": "Quota Attainment",
                "data_origin": "commission_object",
                "source_table": "QuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 47,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "settlement",
                "name": "Settlement",
                "data_origin": "commission_object",
                "source_table": "Settlement",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "plan_id",
                    "criteria_id",
                    "settlement_rule_id",
                    "commission_row_key",
                    "line_item_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 52,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "user",
                "name": "User",
                "data_origin": "system_object",
                "source_table": None,
                "primary_key": [
                    "employee_email_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                "table_name": "system_object_data",
            },
        ]
        self.assertEqual(
            result,
            expected_result,
        )

    def test_get_all_objects_with_only_forceast_objects(
        self, mock_get_client_features, mock_check_client_features_exist
    ):
        mock_get_client_features.return_value = {"modules": ["ICM"]}
        mock_check_client_features_exist.return_value = {
            "expose_comm_reports_in_plan": False,
            "show_forecast": True,
        }
        result = get_all_objects(client_id=4012)
        expected_result = [
            {
                "temporal_id": 48,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission",
                "name": "Commission",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 49,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission_summary",
                "name": "Commission Summary",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "fiscal_year",
                    "payee_email_id",
                    "period_start_date",
                    "period_end_date",
                    "commission_plan_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 55,
                "knowledge_begin_date": datetime.datetime(
                    2024, 2, 6, 9, 18, 6, 771127, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "forecast_commission",
                "name": "Forecast Commission",
                "data_origin": "forecast_object",
                "source_table": "ForecastCommission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                ],
                "table_name": "forecast_object_data",
            },
            {
                "temporal_id": 56,
                "knowledge_begin_date": datetime.datetime(
                    2024, 2, 6, 9, 18, 6, 771127, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "forecast_quota_attainment",
                "name": "Forecast Quota Attainment",
                "data_origin": "forecast_object",
                "source_table": "ForecastQuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "forecast_object_data",
            },
            {
                "temporal_id": 51,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota",
                "name": "Quota",
                "data_origin": "system_object",
                "source_table": "EmployeeQuota",
                "primary_key": [
                    "employee_email_id",
                    "quota_category_name",
                    "is_team_quota",
                    "quota_period_start_date",
                    "quota_schedule_start_date",
                ],
                "table_name": "system_object_data",
            },
            {
                "temporal_id": 50,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota_attainment",
                "name": "Quota Attainment",
                "data_origin": "commission_object",
                "source_table": "QuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 47,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "settlement",
                "name": "Settlement",
                "data_origin": "commission_object",
                "source_table": "Settlement",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "plan_id",
                    "criteria_id",
                    "settlement_rule_id",
                    "commission_row_key",
                    "line_item_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 52,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "user",
                "name": "User",
                "data_origin": "system_object",
                "source_table": None,
                "primary_key": [
                    "employee_email_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                "table_name": "system_object_data",
            },
        ]
        self.assertEqual(result, expected_result)

    def test_get_all_objects_with_no_inter_and_forecast(
        self, mock_get_client_features, mock_check_client_features_exist
    ):
        mock_get_client_features.return_value = {"modules": ["ICM"]}
        mock_check_client_features_exist.return_value = {
            "expose_comm_reports_in_plan": False,
            "show_forecast": False,
        }
        result = get_all_objects(client_id=4012)
        expected_result = [
            {
                "temporal_id": 48,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission",
                "name": "Commission",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "commission_plan_id",
                    "criteria_id",
                    "line_item_id",
                    "tier_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 49,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "commission_summary",
                "name": "Commission Summary",
                "data_origin": "commission_object",
                "source_table": "Commission",
                "primary_key": [
                    "fiscal_year",
                    "payee_email_id",
                    "period_start_date",
                    "period_end_date",
                    "commission_plan_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 51,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota",
                "name": "Quota",
                "data_origin": "system_object",
                "source_table": "EmployeeQuota",
                "primary_key": [
                    "employee_email_id",
                    "quota_category_name",
                    "is_team_quota",
                    "quota_period_start_date",
                    "quota_schedule_start_date",
                ],
                "table_name": "system_object_data",
            },
            {
                "temporal_id": 50,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "quota_attainment",
                "name": "Quota Attainment",
                "data_origin": "commission_object",
                "source_table": "QuotaErosion",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "quota_category_name",
                    "individual_or_team_quota",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 47,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "settlement",
                "name": "Settlement",
                "data_origin": "commission_object",
                "source_table": "Settlement",
                "primary_key": [
                    "period_start_date",
                    "period_end_date",
                    "payee_email_id",
                    "plan_id",
                    "criteria_id",
                    "settlement_rule_id",
                    "commission_row_key",
                    "line_item_id",
                    "adjustment_id",
                ],
                "table_name": "report_object_data",
            },
            {
                "temporal_id": 52,
                "knowledge_begin_date": datetime.datetime(
                    2023, 5, 29, 8, 43, 9, 276736, tzinfo=datetime.timezone.utc
                ),
                "knowledge_end_date": None,
                "is_deleted": False,
                "additional_details": {
                    "server": "0.0.0.0",
                    "updated_by": "<EMAIL>",
                },
                "ever_object_id": "user",
                "name": "User",
                "data_origin": "system_object",
                "source_table": None,
                "primary_key": [
                    "employee_email_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                "table_name": "system_object_data",
            },
        ]
        self.assertEqual(result, expected_result)

    def test_get_all_variables_for_object(self, _1, _2):  # type: ignore
        result = get_all_variables_for_object(
            ever_object_id="commission", client_id=2005
        )
        expected_result = [
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "period_label_commission",
                "source_variable_id": None,
                "source_name_history": "Period << Commission",
                "is_enriched_variable": False,
                "system_name": "period_label",
                "display_name": "Period",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "period_start_date_commission",
                "source_variable_id": None,
                "source_name_history": "Period Start Date << Commission",
                "is_enriched_variable": False,
                "system_name": "period_start_date",
                "display_name": "Period Start Date",
                "data_type_id": 2,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "period_end_date_commission",
                "source_variable_id": None,
                "source_name_history": "Period End Date << Commission",
                "is_enriched_variable": False,
                "system_name": "period_end_date",
                "display_name": "Period End Date",
                "data_type_id": 2,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "payee_name_commission",
                "source_variable_id": None,
                "source_name_history": "Payee << Commission",
                "is_enriched_variable": False,
                "system_name": "payee_name",
                "display_name": "Payee",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "payee_email_id_commission",
                "source_variable_id": None,
                "source_name_history": "Payee Email << Commission",
                "is_enriched_variable": False,
                "system_name": "payee_email_id",
                "display_name": "Payee Email",
                "data_type_id": 12,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "payout_freq_commission",
                "source_variable_id": None,
                "source_name_history": "Payout Frequency << Commission",
                "is_enriched_variable": False,
                "system_name": "payout_freq",
                "display_name": "Payout Frequency",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "plan_name_commission",
                "source_variable_id": None,
                "source_name_history": "Commission Plan << Commission",
                "is_enriched_variable": False,
                "system_name": "plan_name",
                "display_name": "Commission Plan",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "plan_type_commission",
                "source_variable_id": None,
                "source_name_history": "Commission Type << Commission",
                "is_enriched_variable": False,
                "system_name": "plan_type",
                "display_name": "Commission Type",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "record_type_commission",
                "source_variable_id": None,
                "source_name_history": "Record Type << Commission",
                "is_enriched_variable": False,
                "system_name": "record_type",
                "display_name": "Record Type",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "criteria_name_commission",
                "source_variable_id": None,
                "source_name_history": "Criteria << Commission",
                "is_enriched_variable": False,
                "system_name": "criteria_name",
                "display_name": "Criteria",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "line_item_id_commission",
                "source_variable_id": None,
                "source_name_history": "Line Item Id << Commission",
                "is_enriched_variable": False,
                "system_name": "line_item_id",
                "display_name": "Line Item Id",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "tier_id_commission",
                "source_variable_id": None,
                "source_name_history": "Tier Id << Commission",
                "is_enriched_variable": False,
                "system_name": "tier_id",
                "display_name": "Tier Id",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "tier_name_commission",
                "source_variable_id": None,
                "source_name_history": "Tier << Commission",
                "is_enriched_variable": False,
                "system_name": "tier_name",
                "display_name": "Tier",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "quota_erosion_commission",
                "source_variable_id": None,
                "source_name_history": "Quota Retirement << Commission",
                "is_enriched_variable": False,
                "system_name": "quota_erosion",
                "display_name": "Quota Retirement",
                "data_type_id": 1,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "amount_commission",
                "source_variable_id": None,
                "source_name_history": "Commission Amount (Org Currency) << Commission",
                "is_enriched_variable": False,
                "system_name": "amount",
                "display_name": "Commission Amount (Org Currency)",
                "data_type_id": 1,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "payee_currency_commission",
                "source_variable_id": None,
                "source_name_history": "Payee Currency << Commission",
                "is_enriched_variable": False,
                "system_name": "payee_currency",
                "display_name": "Payee Currency",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "conversion_rate_commission",
                "source_variable_id": None,
                "source_name_history": "Currency Conversion Rate << Commission",
                "is_enriched_variable": False,
                "system_name": "conversion_rate",
                "display_name": "Currency Conversion Rate",
                "data_type_id": 1,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "amount_payee_currency_commission",
                "source_variable_id": None,
                "source_name_history": "Commission Amount (Payout Currency) << Commission",
                "is_enriched_variable": False,
                "system_name": "amount_payee_currency",
                "display_name": "Commission Amount (Payout Currency)",
                "data_type_id": 1,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "databook_name_commission",
                "source_variable_id": None,
                "source_name_history": "Databook << Commission",
                "is_enriched_variable": False,
                "system_name": "databook_name",
                "display_name": "Databook",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "datasheet_name_commission",
                "source_variable_id": None,
                "source_name_history": "Datasheet << Commission",
                "is_enriched_variable": False,
                "system_name": "datasheet_name",
                "display_name": "Datasheet",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "is_locked_commission",
                "source_variable_id": None,
                "source_name_history": "Is Locked << Commission",
                "is_enriched_variable": False,
                "system_name": "is_locked",
                "display_name": "Is Locked",
                "data_type_id": 3,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "locked_kd_commission",
                "source_variable_id": None,
                "source_name_history": "Locked Date << Commission",
                "is_enriched_variable": False,
                "system_name": "locked_kd",
                "display_name": "Locked Date",
                "data_type_id": 2,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "knowledge_begin_date_commission",
                "source_variable_id": None,
                "source_name_history": "Updated at << Commission",
                "is_enriched_variable": False,
                "system_name": "knowledge_begin_date",
                "display_name": "Updated at",
                "data_type_id": 2,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "commission_plan_id_commission",
                "source_variable_id": None,
                "source_name_history": "Plan Id << Commission",
                "is_enriched_variable": False,
                "system_name": "commission_plan_id",
                "display_name": "Plan Id",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "criteria_id_commission",
                "source_variable_id": None,
                "source_name_history": "Criteria Id << Commission",
                "is_enriched_variable": False,
                "system_name": "criteria_id",
                "display_name": "Criteria Id",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "adjustment_id_commission",
                "source_variable_id": None,
                "source_name_history": "Adjustment Id << Commission",
                "is_enriched_variable": False,
                "system_name": "adjustment_id",
                "display_name": "Adjustment Id",
                "data_type_id": 4,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_5_deal_no_2226a79c_ce57_4fb6_a983_df8f2c3b6881_commission",
                "source_variable_id": None,
                "source_name_history": "cond::Deal No << Commission",
                "system_name": "co_5_deal_no_2226a79c_ce57_4fb6_a983_df8f2c3b6881",
                "is_enriched_variable": True,
                "display_name": "cond::Deal No",
                "data_type_id": 1,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_4_deal_amount_a6087baa_8cbb_488a_847a_e62f80fe6db6_commission",
                "source_variable_id": None,
                "source_name_history": "sim::Deal Amount << Commission",
                "is_enriched_variable": True,
                "system_name": "co_4_deal_amount_a6087baa_8cbb_488a_847a_e62f80fe6db6",
                "display_name": "sim::Deal Amount",
                "data_type_id": 1,
            },
            {
                "source_id": "commission",
                "source_name": "Commission",
                "source_type": "report",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_deal_status_f5c04e29_8563_465d_8120_cb69c96e4f6e_commission",
                "source_variable_id": None,
                "source_name_history": "Quota::deal status << Commission",
                "is_enriched_variable": True,
                "system_name": "co_1_deal_status_f5c04e29_8563_465d_8120_cb69c96e4f6e",
                "display_name": "Quota::deal status",
                "data_type_id": 4,
            },
        ]

        self.assertCountEqual(result.model_dump(), expected_result)

    def test_get_system_name_from_variable_id(self, _1, _2):  # type: ignore
        ever_object_variables = EverObjectVariableAccessor().ever_obj_kd_aware()
        for ever_object_variable in ever_object_variables:
            variable_id = (
                ever_object_variable.system_name
                + "_"
                + ever_object_variable.ever_object_id
            )
            result = _get_system_name_from_variable_id(
                variable_id=variable_id,
                ever_object_id=ever_object_variable.ever_object_id,
            )
            self.assertEqual(result, ever_object_variable.system_name)
