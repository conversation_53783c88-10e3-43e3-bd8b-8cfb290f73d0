from types import SimpleNamespace
from unittest.mock import patch
from uuid import UUID, uuid4

import pytest

from commission_engine.services.task_tracker_service import (
    datasheet_task_details,
    get_connector_e2e_sync_detailed_status,
    get_connector_upstream_sync_detailed_status,
)


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestTaskTrackerService:
    def test_get_connector_e2e_sync_detailed_status(self):
        e2e_record = get_connector_e2e_sync_detailed_status(
            1,
            UUID("1d13afaa-4334-4994-8a9e-6e033968b899"),
            False,
            [
                "4b56a2fe-8ac8-47a6-93da-3a7267c8734c",
                "d4322b77-5a3e-45b4-8921-1cefe7302650",
            ],
        )
        assert e2e_record["e2e_sync_run_id"] == "1d13afaa-4334-4994-8a9e-6e033968b899"
        assert "upstream_sync" in e2e_record
        assert "datasheet_system_custom_object_sync" in e2e_record
        assert "commission_sync" in e2e_record
        assert "report_sync_commission_object" in e2e_record
        assert "datasheet_report_object_sync" in e2e_record

    def test_get_connector_upstream_sync_detailed_status(self):
        e2e_record = get_connector_upstream_sync_detailed_status(
            1,
            UUID("510b1c09-ed82-4887-badb-e36c4bf6c812"),
            False,
            [
                "4b56a2fe-8ac8-47a6-93da-3a7267c8734c",
                "d4322b77-5a3e-45b4-8921-1cefe7302650",
            ],
        )
        assert e2e_record["e2e_sync_run_id"] == "510b1c09-ed82-4887-badb-e36c4bf6c812"
        assert "upstream_sync" in e2e_record

    @patch(
        "commission_engine.services.task_tracker_service._get_datasheet_sync_details_from_cache"
    )
    @patch(
        "everstage_etl.services.datasheet_execution_context_service.retrieve_datasheet_execution_context"
    )
    @pytest.mark.parametrize(
        "test_id, client_id, e2e_sync_run_id, sync_run_id, sync_type, datasheet_count, stale_datasheet_count, expected_result",
        [
            # completed datasheet sync task
            (
                1,
                3015,
                "ad400e28-3154-4287-bf6b-c386f5de0bfb",
                "72362cfe-f0fa-4bc7-8f15-a6457f4da93e",
                None,
                1,
                1,
                {
                    "total": {"count": 1},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {
                        "count": 1,
                        "records": ["6c016194-a440-40ce-bda7-3dbc8d359452"],
                    },
                    "error": {"count": 0, "records": []},
                    "skipped": {"count": 0, "records": []},
                    "message": "1 out of 1 datasheets have stale data and need to be re-generated.",
                    "show_spinner": False,
                },
            ),
            # failed datasheet sync task
            (
                2,
                3015,
                "dc5549a8-debb-46a8-9c75-31ac2470a719",
                "8d4d8646-415a-45a3-8310-04509058d7db",
                None,
                1,
                1,
                {
                    "total": {"count": 1},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {"count": 0, "records": []},
                    "error": {
                        "count": 1,
                        "records": ["085dc6f3-c93f-4899-8cfb-56bf78483108"],
                    },
                    "skipped": {"count": 0, "records": []},
                    "message": "1 out of 1 datasheets have stale data and need to be re-generated.",
                    "show_spinner": False,
                },
            ),
            # queue not populated case
            (
                3,
                3015,
                uuid4(),
                uuid4(),
                None,
                0,
                0,
                {
                    "total": {"count": 0},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {"count": 0, "records": []},
                    "error": {"count": 0, "records": []},
                    "skipped": {"count": 0, "records": []},
                    "show_spinner": True,
                },
            ),
            # no cache case(this case is not possible)
            (
                4,
                3015,
                "6a3b6455-2aa8-4abb-a466-3aa4e25ea0f0",
                "83083307-497b-4981-b39c-f5ccf708c67e",
                None,
                1,
                1,
                {
                    "total": {"count": 1},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {
                        "count": 1,
                        "records": ["085dc6f3-c93f-4899-8cfb-56bf78483108"],
                    },
                    "error": {"count": 0, "records": []},
                    "skipped": {"count": 0, "records": []},
                    "show_spinner": False,
                    "message": "1 out of 1 datasheets have stale data and need to be re-generated.",
                },
            ),
            # Zero stale records case
            (
                5,
                3018,
                "d67d06de-127d-4cea-9e08-0e0bade9b280",
                "24140d0e-6bea-4999-ba25-fed2932a4825",
                None,
                1,
                0,
                {
                    "total": {"count": 0},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {"count": 0, "records": []},
                    "error": {"count": 0, "records": []},
                    "skipped": {"count": 0, "records": []},
                    "message": "Out of 1 datasheets none of them are stale",
                    "show_spinner": False,
                },
            ),
            # DB refresh based on object 38
            (
                6,
                3018,
                "09e0e9de-366c-4592-9cd1-ce9c481ead25",
                "ec976e02-548c-47f1-8665-716e7858401e",
                None,
                38,
                38,
                {
                    "total": {"count": 38},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {
                        "count": 21,
                        "records": [
                            "fd9452fd-023e-49d1-a4a0-e778d3a2ec2e",
                            "bafecb39-8968-4720-b783-d24b14fa9c92",
                            "5e81e779-9ddc-451c-b324-3a8af4da2c7a",
                            "13c8b56a-2d16-4e6f-8a63-dd3f66362c19",
                            "1c9c888d-8349-4cf5-9bcf-34484588de96",
                            "51df5fb9-9049-45ed-885a-57324bd745d8",
                            "82c5c47a-8a3f-406b-9662-2d2a13b7720a",
                            "755317b1-b3bd-4f7c-ad6a-1a0c2c9e9343",
                            "a2736357-37e6-409d-9362-cab5fdd79722",
                            "107e8d04-6a62-47eb-91ec-b272f4baf693",
                            "818c188f-1c8c-4fb1-861a-a9e67cdd4db7",
                            "ed9c27b5-2c3f-4e45-9b1c-720edb77ed86",
                            "e02f32b1-56e9-46f8-98f1-79736d81d5a6",
                            "2f9a571a-52ca-4284-9991-5aa0be3ad3fe",
                            "8acb1aa5-56ff-4c06-8199-84d055e9e4d1",
                            "f1bc1832-67d3-409c-88f1-d922e34caffc",
                            "2d98e225-fda8-4dd1-8bb0-69b88d64b50f",
                            "d6334935-4f13-4237-8c85-76c0b7681c7a",
                            "f72edbf6-c20b-4d7e-a9e6-140a82b1be70",
                            "7ae195dd-f30e-4e21-a05f-a293a1e5c4e3",
                            "21e7250c-44a7-4183-aec2-792d307ad377",
                        ],
                    },
                    "error": {
                        "count": 17,
                        "records": [
                            "dd6075f9-7374-4f3e-962f-a4f88887d2e1",
                            "e11ae674-a0f6-414d-8a21-a64384f875dc",
                            "c6a85425-750e-4d82-8274-0b2e7925b31a",
                            "e43ee3e9-599f-428b-8931-18bc3737c1f3",
                            "ce259ee0-963b-4c5a-a286-5ec75d9c4a74",
                            "976ee0d1-d3bf-4467-91fb-8707c87cb29e",
                            "d75be811-f381-4239-9448-c20c6f060bac",
                            "4c4994f2-61c3-4f55-ab98-2542175820c0",
                            "88b64800-49b2-4c7a-9f2b-c488be97addb",
                            "4772e51e-421b-442b-915d-9080761645b9",
                            "3845e454-e467-4a8b-a356-800fa82f867d",
                            "ad4c9bf0-bdeb-4fd0-8f55-679005828e94",
                            "aba0bc39-2998-4253-b840-1488a6cce590",
                            "db20f49b-2119-4232-9d25-d11dfdc49257",
                            "9a21c639-701b-47bd-a154-f480b7ab0103",
                            "c2eb06f4-2119-4ccd-8060-e97615115a72",
                            "c157fecd-ebf9-40f3-b762-6426a8a898a4",
                        ],
                    },
                    "skipped": {"count": 0, "records": []},
                    "message": "38 out of 38 datasheets have stale data and need to be re-generated.",
                    "show_spinner": False,
                },
            ),
            # Commission sync
            (
                7,
                3018,
                "090e2f8e-76c3-4976-a6cc-12e08e57fda5",
                "44691dca-d8d9-4fc5-9f7f-aa447103981c",
                "COMMISSION_DB_SYNC",
                15,
                7,
                {
                    "total": {"count": 7},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {"count": 0, "records": []},
                    "error": {
                        "count": 7,
                        "records": [
                            "c2eb06f4-2119-4ccd-8060-e97615115a72",
                            "9f86096b-afa9-4161-8bc0-8ec13d1897fa",
                            "4c4994f2-61c3-4f55-ab98-2542175820c0",
                            "976ee0d1-d3bf-4467-91fb-8707c87cb29e",
                            "c157fecd-ebf9-40f3-b762-6426a8a898a4",
                            "88b64800-49b2-4c7a-9f2b-c488be97addb",
                            "96a1ee4a-d5aa-47e1-99fb-a93452b1123c",
                        ],
                    },
                    "skipped": {"count": 0, "records": []},
                    "message": "7 out of 15 datasheets have stale data and need to be re-generated.",
                    "show_spinner": False,
                },
            ),
            # Settlement sync
            (
                8,
                3018,
                "9816ecfe-a3b4-4259-b202-27c8bfea1d9f",
                "33ce6c5c-0c06-4a30-8054-a0c536d542b7",
                "SETTLEMENT_DB_SYNC",
                15,
                7,
                {
                    "total": {"count": 7},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {"count": 0, "records": []},
                    "error": {
                        "count": 7,
                        "records": [
                            "c157fecd-ebf9-40f3-b762-6426a8a898a4",
                            "88b64800-49b2-4c7a-9f2b-c488be97addb",
                            "96a1ee4a-d5aa-47e1-99fb-a93452b1123c",
                            "9f86096b-afa9-4161-8bc0-8ec13d1897fa",
                            "4c4994f2-61c3-4f55-ab98-2542175820c0",
                            "c2eb06f4-2119-4ccd-8060-e97615115a72",
                            "976ee0d1-d3bf-4467-91fb-8707c87cb29e",
                        ],
                    },
                    "skipped": {"count": 0, "records": []},
                    "message": "7 out of 15 datasheets have stale data and need to be re-generated.",
                    "show_spinner": False,
                },
            ),
            # end to end sync
            (
                9,
                3018,
                "89339100-a815-45e9-8682-7fea44db2c6c",
                "0736e426-20ae-490a-a908-54929f616697",
                "SYSTEM_CUSTOM_DB_SYNC",
                33,
                18,
                {
                    "total": {"count": 18},
                    "wait": {"count": 0, "records": []},
                    "process": {"count": 0, "records": []},
                    "finish": {
                        "count": 2,
                        "records": [
                            "82c5c47a-8a3f-406b-9662-2d2a13b7720a",
                            "ed9c27b5-2c3f-4e45-9b1c-720edb77ed86",
                        ],
                    },
                    "error": {
                        "count": 16,
                        "records": [
                            "3845e454-e467-4a8b-a356-800fa82f867d",
                            "d4081bc0-fbf5-44f1-b7a6-fe41b9bb2287",
                            "ce259ee0-963b-4c5a-a286-5ec75d9c4a74",
                            "e57bc3bf-cbd6-46b1-ba1b-8851f5478968",
                            "db20f49b-2119-4232-9d25-d11dfdc49257",
                            "ad4c9bf0-bdeb-4fd0-8f55-679005828e94",
                            "aba0bc39-2998-4253-b840-1488a6cce590",
                            "9a21c639-701b-47bd-a154-f480b7ab0103",
                            "4772e51e-421b-442b-915d-9080761645b9",
                            "761e5243-9114-4e6a-8b9d-879e4343d65d",
                            "e43ee3e9-599f-428b-8931-18bc3737c1f3",
                            "c6a85425-750e-4d82-8274-0b2e7925b31a",
                            "d75be811-f381-4239-9448-c20c6f060bac",
                            "dd6075f9-7374-4f3e-962f-a4f88887d2e1",
                            "e11ae674-a0f6-414d-8a21-a64384f875dc",
                            "88d01b48-2bf2-45ef-92d6-369c450bd8f7",
                        ],
                    },
                    "skipped": {"count": 0, "records": []},
                    "message": "18 out of 33 datasheets have stale data and need to be re-generated.",
                    "show_spinner": False,
                },
            ),
        ],
    )
    def test_datasheet_sync_detailed_status(
        self,
        retrieve_datasheet_execution_context_mock,
        _get_datasheet_sync_details_from_cache_mock,
        test_id,
        client_id,
        e2e_sync_run_id,
        sync_run_id,  # pylint: disable=unused-argument
        sync_type,
        datasheet_count,
        stale_datasheet_count,
        expected_result,
    ):
        if test_id != 4:
            _get_datasheet_sync_details_from_cache_mock.return_value = {
                "datasheet_count": datasheet_count,
                "stale_datasheet_count": stale_datasheet_count,
            }
        else:
            _get_datasheet_sync_details_from_cache_mock.return_value = {}
        retrieve_datasheet_execution_context_mock.return_value = SimpleNamespace(
            skipped_stale_datasheets=[]
        )

        print(f"Test running for {test_id}--{sync_run_id}")
        task_details = datasheet_task_details(client_id, e2e_sync_run_id, sync_type)
        assert task_details.keys() == expected_result.keys()
        for key, value in task_details.items():
            if key in ["process", "finish", "error"]:
                # Cannot directly assert records since it's populated from dictionary
                assert value["count"] == expected_result[key]["count"]
                assert set(value["records"]) == set(expected_result[key]["records"])
            else:
                assert value == expected_result[key]

    @patch("commission_engine.services.client_feature_service.has_feature")
    @patch(
        "commission_engine.services.task_tracker_service.get_commission_calculation_detailed_status"
    )
    @patch("commission_engine.services.task_tracker_service.ExtractionConfigAccessor")
    @patch(
        "commission_engine.accessors.etl_housekeeping_accessor.UpstreamETLStatusReaderAccessor"
    )
    def test_get_connector_e2e_sync_detailed_status_with_mixed_statuses(
        self, mock_usra, mock_extraction_config, mock_get_commission, mock_has_feature
    ):
        """Test scenario where two tasks have same object_id but different integration_ids and statuses"""
        client_id = 1
        e2e_sync_run_id = UUID("442956d3-6aba-4f45-9b83-7d9bed0b8066")
        integration_ids = [
            "46453128-2f17-49c9-8855-5088d1c6dce9",  # failed task
            "324b6e15-ee1e-4e40-a3f9-a98cced0ae22",  # completed task
        ]

        # Mock extraction config accessor
        mock_extraction_config_instance = mock_extraction_config.return_value
        mock_extraction_config_instance.get_selected_tasks_for_task_group.return_value = [
            SimpleNamespace(
                integration_id="46453128-2f17-49c9-8855-5088d1c6dce9",
                task="SALESFORCE_LEAD_SYNC",
            ),
            SimpleNamespace(
                integration_id="324b6e15-ee1e-4e40-a3f9-a98cced0ae22",
                task="SALESFORCE_LEAD_SYNC",
            ),
        ]

        # Mock upstream status reader accessor
        mock_usra_instance = mock_usra.return_value
        mock_usra_instance.get_records_for_e2e_sync_run_id.return_value = [
            SimpleNamespace(
                integration_id="46453128-2f17-49c9-8855-5088d1c6dce9",
                object_id="SALESFORCE_LEAD_SYNC",
                sync_status="failed",
            ),
            SimpleNamespace(
                integration_id="324b6e15-ee1e-4e40-a3f9-a98cced0ae22",
                object_id="SALESFORCE_LEAD_SYNC",
                sync_status="complete",
            ),
        ]

        mock_has_feature.return_value = True
        mock_get_commission.return_value = {
            "datasheet_system_custom_object_sync": {"total": {"count": 0}},
            "commission_sync": {"total": {"count": 0}},
            "report_sync_commission_object": {"total": {"count": 0}},
            "datasheet_report_object_sync": {"total": {"count": 0}},
        }

        e2e_record = get_connector_e2e_sync_detailed_status(
            client_id,
            e2e_sync_run_id,
            all_objects_selected=False,
            integration_ids=integration_ids,
        )

        # Verify the result contains both statuses
        assert e2e_record["e2e_sync_run_id"] == str(e2e_sync_run_id)
        assert "upstream_sync" in e2e_record

        upstream_sync = e2e_record["upstream_sync"]

        print("upstream_sync", upstream_sync)

        # Should have one error (failed task) and one finish (completed task)
        assert upstream_sync["error"]["count"] == 1
        assert upstream_sync["finish"]["count"] == 1
        assert "SALESFORCE_LEAD_SYNC" in upstream_sync["error"]["records"]
        assert "SALESFORCE_LEAD_SYNC" in upstream_sync["finish"]["records"]

    @patch("commission_engine.services.client_feature_service.has_feature")
    @patch("commission_engine.services.task_tracker_service.ExtractionConfigAccessor")
    @patch(
        "commission_engine.accessors.etl_housekeeping_accessor.UpstreamETLStatusReaderAccessor"
    )
    def test_get_connector_upstream_sync_detailed_status_with_mixed_statuses(
        self, mock_usra, mock_extraction_config, mock_has_feature
    ):
        """Test scenario where two tasks have same object_id but different integration_ids and statuses"""
        client_id = 1
        e2e_sync_run_id = UUID("442956d3-6aba-4f45-9b83-7d9bed0b8066")
        integration_ids = [
            "46453128-2f17-49c9-8855-5088d1c6dce9",  # failed task
            "324b6e15-ee1e-4e40-a3f9-a98cced0ae22",  # completed task
        ]

        # Mock extraction config accessor
        mock_extraction_config_instance = mock_extraction_config.return_value
        mock_extraction_config_instance.get_selected_tasks_for_task_group.return_value = [
            SimpleNamespace(
                integration_id="46453128-2f17-49c9-8855-5088d1c6dce9",
                task="SALESFORCE_LEAD_SYNC",
            ),
            SimpleNamespace(
                integration_id="324b6e15-ee1e-4e40-a3f9-a98cced0ae22",
                task="SALESFORCE_LEAD_SYNC",
            ),
        ]

        # Mock upstream status reader accessor
        mock_usra_instance = mock_usra.return_value
        mock_usra_instance.get_records_for_e2e_sync_run_id.return_value = [
            SimpleNamespace(
                integration_id="46453128-2f17-49c9-8855-5088d1c6dce9",
                object_id="SALESFORCE_LEAD_SYNC",
                sync_status="failed",
            ),
            SimpleNamespace(
                integration_id="324b6e15-ee1e-4e40-a3f9-a98cced0ae22",
                object_id="SALESFORCE_LEAD_SYNC",
                sync_status="complete",
            ),
        ]

        mock_has_feature.return_value = True

        e2e_record = get_connector_upstream_sync_detailed_status(
            client_id,
            e2e_sync_run_id,
            all_objects_selected=False,
            integration_ids=integration_ids,
        )

        # Verify the result contains both statuses
        assert e2e_record["e2e_sync_run_id"] == str(e2e_sync_run_id)
        assert "upstream_sync" in e2e_record

        upstream_sync = e2e_record["upstream_sync"]

        # Should have one error (failed task) and one finish (completed task)
        assert upstream_sync["error"]["count"] == 1
        assert upstream_sync["finish"]["count"] == 1
        assert "SALESFORCE_LEAD_SYNC" in upstream_sync["error"]["records"]
        assert "SALESFORCE_LEAD_SYNC" in upstream_sync["finish"]["records"]
