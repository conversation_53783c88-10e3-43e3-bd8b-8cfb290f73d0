import datetime
from uuid import uuid4

import pandas as pd
import pytest
from django.utils import timezone

import spm.tests.models as spm_models
from commission_engine.services.data_sources.user_report_strategy import (
    UserReportStrategy,
)
from spm.models import CommissionPlan, PlanCriteria
from spm.models.config_models.employee_models import Employee, Hierarchy, PlanDetails


def create_employee_objects(
    email,
    first_name,
    last_name,
    send_notification,
    status=None,
    exit_date=None,
    created_date=timezone.now(),
    time_zone=None,
    employee_config=None,
    role="Payee",
    client_id=1,
    kd=timezone.now(),
):
    Employee.objects.create(
        employee_email_id=email,
        first_name=first_name,
        last_name=last_name,
        user_role=role,
        created_date=created_date,
        created_by="<EMAIL>",
        profile_picture=None,
        status=status,
        send_notification=send_notification,
        client_id=client_id,
        knowledge_begin_date=kd,
        time_zone=time_zone,
        employee_config=employee_config,
        exit_date=exit_date,
    )


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestUserReport:
    client_id = 1

    @pytest.mark.parametrize(
        "list_a,list_b,result",
        [
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 7, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 8, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 8, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "odi Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 28, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 9, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "WC Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 25, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 29, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Fielder",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 27, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 11, 26, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "coach",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": None,
                        "effective_start_date": datetime.datetime(
                            2022, 11, 28, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "AUS",
                        "designation": "coach",
                    },
                ],
                [
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 7, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 7, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 7, 20, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 7, 21, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 9, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 9, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 10, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 10, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": None,
                    },
                ],
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 7, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 8, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 8, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "odi Captain",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 7, 9, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 7, 20, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "payout_frequency": "Monthly",
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "WC Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 28, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 21, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "WC Captain",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 7, 29, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 9, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "payout_frequency": "Monthly",
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Fielder",
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2022, 9, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2022, 10, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "payout_frequency": "Monthly",
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Fielder",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 25, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 10, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Fielder",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 27, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 11, 26, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "coach",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": None,
                        "effective_start_date": datetime.datetime(
                            2022, 11, 28, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "AUS",
                        "designation": "coach",
                        "reporting_manager_email_id": "<EMAIL>",
                    },
                ],
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 7, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 8, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 8, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "odi Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 28, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 9, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "WC Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 25, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 29, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Fielder",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 27, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 11, 26, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "coach",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": None,
                        "effective_start_date": datetime.datetime(
                            2022, 11, 28, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "AUS",
                        "designation": "coach",
                    },
                ],
                [],
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 6, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 7, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 7, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Test Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 8, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 8, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "odi Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 7, 28, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 9, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "WC Captain",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 25, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 7, 29, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "Fielder",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": datetime.datetime(
                            2022, 11, 27, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 11, 26, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "GBR",
                        "designation": "coach",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "effective_end_date": None,
                        "effective_start_date": datetime.datetime(
                            2022, 11, 28, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": "1",
                        "joining_date": datetime.datetime(
                            2022, 7, 5, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "AUS",
                        "designation": "coach",
                    },
                ],
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "effective_end_date": None,
                        "effective_start_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    }
                ],
                [
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 1, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2024, 2, 29, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 3, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": None,
                    },
                ],
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "effective_end_date": datetime.datetime(
                            2023, 12, 31, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 1, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2024, 2, 29, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 3, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": None,
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                ],
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "effective_end_date": datetime.datetime(
                            2023, 12, 31, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 1, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2024, 2, 29, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 3, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": None,
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                ],
                [
                    {
                        "effective_start_date": datetime.datetime(
                            2024, 1, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2024, 1, 31, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "employee_email_id": "<EMAIL>",
                        "cf_21_text_1": "123",
                    },
                    {
                        "effective_start_date": datetime.datetime(
                            2024, 2, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": None,
                        "employee_email_id": "<EMAIL>",
                        "cf_21_text_1": "456",
                    },
                ],
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "effective_end_date": datetime.datetime(
                            2023, 12, 31, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "effective_start_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                    {
                        "effective_start_date": datetime.datetime(
                            2024, 1, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2024, 1, 31, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "employee_email_id": "<EMAIL>",
                        "cf_21_text_1": "123",
                        "reporting_manager_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 2, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": datetime.datetime(
                            2024, 2, 29, 23, 59, 59, 999999, tzinfo=timezone.utc
                        ),
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                        "cf_21_text_1": "456",
                    },
                    {
                        "reporting_manager_email_id": "<EMAIL>",
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": datetime.datetime(
                            2024, 3, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "effective_end_date": None,
                        "payout_frequency": "Monthly",
                        "pay_currency": "INR",
                        "fixed_pay": None,
                        "variable_pay": "0.00",
                        "employee_id": None,
                        "joining_date": datetime.datetime(
                            2022, 8, 1, 0, 0, tzinfo=timezone.utc
                        ),
                        "employment_country": "IND",
                        "designation": None,
                        "cf_21_text_1": "456",
                    },
                ],
            ),
        ],
    )
    def test_split_data_by_date_intervals(self, list_a, list_b, result):
        merged_rec = UserReportStrategy(
            client_id=self.client_id
        ).split_data_by_date_intervals(list_a, list_b)
        assert merged_rec == result

    def test_data_mapper(self):
        emp_ids = []
        emp_email = "<EMAIL>"
        mgr_email = "<EMAIL>"
        emp_date = timezone.now()
        create_employee_objects(
            email=emp_email,
            first_name=f"emp_first",
            last_name=f"emp_last",
            send_notification=False,
            created_date=emp_date,
            kd=emp_date,
        )

        create_employee_objects(
            email=mgr_email,
            first_name=f"mgr_first",
            last_name=f"mgr_last",
            send_notification=False,
            created_date=emp_date,
            kd=emp_date,
        )

        hierarchy_data = {
            "client_id": 1,
            "temporal_id": 978314245235356,
            "knowledge_begin_date": datetime.datetime(
                2022, 12, 15, 14, 8, 44, 217996, tzinfo=timezone.utc
            ),
            "knowledge_end_date": None,
            "is_deleted": False,
            "additional_details": None,
            "employee_email_id": "<EMAIL>",
            "reporting_manager_email_id": "<EMAIL>",
            "effective_start_date": datetime.datetime(
                2022, 11, 26, 0, 0, tzinfo=timezone.utc
            ),
            "effective_end_date": datetime.datetime(
                2022, 11, 27, 23, 59, 59, 999999, tzinfo=timezone.utc
            ),
        }

        Hierarchy.objects.create(**hierarchy_data)

        # hierarchy_map[emp_email] = [()]
        plan_id = uuid4()
        spiff_plan_id = uuid4()
        spm_models.create_plan_details(email_id=emp_email, plan_id=plan_id)
        spm_models.create_commission_plan_object(plan_id=plan_id, plan_name=f"plan_1")
        spm_models.create_commission_spiff_plan_object(
            id=spiff_plan_id, plan_name=f"spiff_1"
        )
        spm_models.create_plan_details(email_id=emp_email, plan_id=spiff_plan_id)
        criteria_types = ["CustomTeam", "CustomQuota"]
        for criteria_type in criteria_types:
            PlanCriteria.objects.create(
                plan_id=plan_id,
                criteria_id=uuid4(),
                criteria_name="test",
                criteria_type=criteria_type,
                criteria_description=None,
                criteria_display_order=None,
                criteria_data={"type": "Quota", "quota_name": "Primary"},
                criteria_column={},
                client_id=1,
                knowledge_begin_date=timezone.now(),
            )
        user_report = UserReportStrategy(client_id=self.client_id)
        user_report.data_mapper(employee_email_ids=[emp_email, mgr_email])

        assert (
            user_report.employee_map["<EMAIL>"]["employee_email_id"]
            is not None
        )
        assert (
            user_report.employee_map["<EMAIL>"]["employee_email_id"]
            is not None
        )

        assert user_report.hierarchy_map == {
            "<EMAIL>": [
                (
                    datetime.datetime(2022, 11, 26, 0, 0, tzinfo=timezone.utc),
                    datetime.datetime(
                        2022, 11, 27, 23, 59, 59, 999999, tzinfo=timezone.utc
                    ),
                )
            ]
        }
        assert user_report.employee_plan_map == {"<EMAIL>": "plan_1"}
        assert user_report.employee_quota_categories == {
            "<EMAIL>": {"Primary"}
        }
        assert user_report.employee_spiff_map == {"<EMAIL>": ["spiff_1"]}

    def test_transform_data_preferred_language(self):
        user_report = UserReportStrategy(client_id=self.client_id)
        # Prepare test data with different language keys
        data = pd.DataFrame(
            [
                {"employee_email_id": "<EMAIL>", "preferred_language": "en"},
                {"employee_email_id": "<EMAIL>", "preferred_language": "es"},
                {"employee_email_id": "<EMAIL>", "preferred_language": "pt"},
                {
                    "employee_email_id": "<EMAIL>",
                    "preferred_language": "fr",
                },  # unknown, should fallback to English
                {
                    "employee_email_id": "<EMAIL>"
                },  # missing, should fallback to English
            ]
        )
        # Patch employee_map to avoid key errors in other transform columns
        user_report.employee_map = {str(row["employee_email_id"]): {str(k): v for k, v in row.items()} for _, row in data.iterrows()}  # type: ignore
        # Patch required maps to avoid errors in other columns
        user_report.active_countries = {}
        user_report.employee_plan_map = {}
        user_report.employee_spiff_map = {}
        user_report.employee_quota_categories = {}
        user_report.role_permission_id_name_map = {}
        user_report.custom_calendar_map = {}
        user_report.hierarchy_map = {}
        user_report.quota_category_display_name_map = {}
        # Only test preferred_language column
        result = user_report.transform_data(data=data)
        assert list(result["preferred_language"]) == [
            "English",
            "Spanish",
            "Portuguese",
            "English",
            "English",
        ]
