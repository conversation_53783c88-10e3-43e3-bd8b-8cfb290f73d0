import datetime
from unittest.mock import patch

import pytest

from commission_engine.accessors.report_object_accessor import (
    latest_knowledge_date_for_report_objects,
)
from commission_engine.accessors.skd_pkd_map_accessor import ReportObjectPkdMapAccessor
from commission_engine.tests.accessors.utils import (
    set_client_feature,
    unset_client_feature,
)
from commission_engine.utils.general_data import ReportObject


@pytest.mark.django_db
@pytest.mark.commission_engine
@pytest.mark.parametrize(
    "client_id, report_objects, expected_result",
    [
        (
            3006,
            None,
            {
                "commission": datetime.datetime(
                    2023, 8, 9, 3, 40, 48, 367091, tzinfo=datetime.timezone.utc
                ),
                "commission_summary": None,
                "settlement": None,
                "quota_attainment": None,
                "quota": None,
                "quote": None,
                "quote_line_item": None,
                "quote_line_item_tier": None,
                "user": datetime.datetime(
                    2023, 8, 9, 3, 53, 31, 211837, tzinfo=datetime.timezone.utc
                ),
                "inter_commission": datetime.datetime(
                    2023, 8, 9, 3, 34, 38, 393496, tzinfo=datetime.timezone.utc
                ),
                "inter_quota_attainment": None,
                "forecast_commission": None,
                "forecast_quota_attainment": None,
                "inter_forecast_commission": None,
                "inter_forecast_quota_attainment": None,
                "payout": None,
                "fx_rate": None,
                "statement_approvals": None,
            },
        ),
        (
            3006,
            ["user", "inter_commission"],
            {
                "user": datetime.datetime(
                    2023, 8, 9, 3, 53, 31, 211837, tzinfo=datetime.timezone.utc
                ),
                "inter_commission": datetime.datetime(
                    2023, 8, 9, 3, 34, 38, 393496, tzinfo=datetime.timezone.utc
                ),
            },
        ),
        (
            3001,
            None,
            {
                "inter_commission": None,
                "commission_summary": None,
                "settlement": None,
                "inter_quota_attainment": None,
                "quota_attainment": None,
                "quota": None,
                "quote": None,
                "quote_line_item": None,
                "quote_line_item_tier": None,
                "commission": None,
                "user": None,
                "forecast_commission": None,
                "forecast_quota_attainment": None,
                "inter_forecast_commission": None,
                "inter_forecast_quota_attainment": None,
                "payout": None,
                "fx_rate": None,
                "statement_approvals": None,
            },
        ),
    ],
)
def test_latest_knowledge_date_for_report_objects(
    client_id, report_objects, expected_result
):
    """
    Test latest_knowledge_date_for_report_objects function
    """
    result = latest_knowledge_date_for_report_objects(
        client_id=client_id, report_object_ids=report_objects
    )
    assert result == expected_result


@pytest.mark.django_db
@pytest.mark.commission_engine
@patch(
    "commission_engine.snowflake_accessors.report_object_data_accessor.latest_knowledge_date_for_report_objects"
)
@patch.object(ReportObjectPkdMapAccessor, "get_reports_primary_kd")
class TestReportObjectPkdFeatureFlag:
    def test_feature_flag_postgres(
        self, mock_postgres_accessor, mock_snowflake_accessor
    ):
        client_id = 3006
        FEATURE_KEY = "object_knowledge_date_query_strategy"

        set_client_feature(client_id, FEATURE_KEY, "postgres")
        mock_report_object_ids = ["user", "commission", "quota"]

        mock_postgres_accessor.return_value = {}

        latest_knowledge_date_for_report_objects(client_id, mock_report_object_ids)

        mock_snowflake_accessor.assert_not_called()
        mock_postgres_accessor.assert_called_once()

        # Using this approach since order of report objects passed to Postgres accessor might differ
        assert set(mock_postgres_accessor.call_args.args[0]) == set(
            mock_report_object_ids
        )

    def test_feature_flag_snowflake(
        self, mock_postgres_accessor, mock_snowflake_accessor
    ):
        client_id = 3006
        FEATURE_KEY = "object_knowledge_date_query_strategy"

        set_client_feature(client_id, FEATURE_KEY, "snowflake")
        mock_report_object_ids = ["user", "commission", "quota"]

        mock_snowflake_accessor.return_value = {}

        latest_knowledge_date_for_report_objects(client_id, mock_report_object_ids)

        mock_postgres_accessor.assert_not_called()
        mock_snowflake_accessor.assert_called_once_with(
            client_id, mock_report_object_ids
        )

    def test_postgres_used_when_flag_not_set(
        self, mock_postgres_accessor, mock_snowflake_accessor
    ):
        client_id = 3006
        FEATURE_KEY = "object_knowledge_date_query_strategy"

        unset_client_feature(client_id, FEATURE_KEY)
        mock_report_object_ids = ["user", "commission", "quota"]

        mock_postgres_accessor.return_value = {}

        latest_knowledge_date_for_report_objects(client_id, mock_report_object_ids)

        mock_snowflake_accessor.assert_not_called()
        mock_postgres_accessor.assert_called_once()

        # Using this approach since order of report objects passed to Postgres accessor might differ
        assert set(mock_postgres_accessor.call_args.args[0]) == set(
            mock_report_object_ids
        )
