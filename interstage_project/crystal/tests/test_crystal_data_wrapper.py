import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from django.utils import timezone

from ..crystal_data_wrapper import CrystalDataWrapper
from ..exceptions.exceptions import CrystalException
from ..types import AuditDataType


@pytest.fixture
def crystal_data_wrapper():
    return CrystalDataWrapper(client_id=10041)


@pytest.fixture
def mock_extra_data():
    return AuditDataType(
        knowledge_begin_date=timezone.now(),
        last_modified_at=timezone.now(),
        last_modified_by="<EMAIL>",
        additional_details={},
    )


@pytest.mark.django_db
class TestCrystalDataWrapper:
    @pytest.mark.parametrize(
        "test_name,crystal_view_id,new_payees,expected_create_count",
        [
            (
                "all_new_payees",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                ["<EMAIL>", "<EMAIL>"],
                2,
            ),
            (
                "mixed_new_and_existing_payees",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                ["<EMAIL>", "<EMAIL>"],
                1,
            ),
            (
                "only_existing_payees",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                ["<EMAIL>"],
                0,
            ),
        ],
    )
    def test_add_payees_to_crystal_view(
        self,
        crystal_data_wrapper,
        mock_extra_data,
        test_name,
        crystal_view_id,
        new_payees,
        expected_create_count,
    ):
        """Test adding payees to a crystal view with different scenarios"""
        result = crystal_data_wrapper.add_payees_to_crystal_view(
            payees=new_payees,
            crystal_view_id=crystal_view_id,
            extra_data=mock_extra_data,
        )
        # Assert
        assert result == crystal_view_id

    @pytest.mark.parametrize(
        "test_name,crystal_view_id,payees_to_remove,expected_remove_count",
        [
            (
                "successfull_removal",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                ["<EMAIL>", "<EMAIL>"],
                2,
            ),
            (
                "no_payees_to_remove",
                "229709b0-d60a-4781-89fa-a693647b5f09",
                ["<EMAIL>"],
                0,
            ),
            (
                "mixed_payees",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                ["<EMAIL>", "<EMAIL>"],
                1,
            ),
            (
                "payee_not_in_crystal_view",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                ["<EMAIL>"],
                0,
            ),
        ],
    )
    def test_remove_payees_from_crystal_view(
        self,
        crystal_data_wrapper,
        mock_extra_data,
        test_name,
        crystal_view_id,
        payees_to_remove,
        expected_remove_count,
    ):
        """Test removing payees from a crystal view with different scenarios"""

        # Execute
        result = crystal_data_wrapper.remove_payees_from_crystal_view(
            payees=payees_to_remove,
            crystal_view_id=crystal_view_id,
            extra_data=mock_extra_data,
        )

        # Assert
        assert result == crystal_view_id

    @pytest.mark.parametrize(
        "test_name,crystal_view_id,filters,search_term,offset_value,limit_value,expected_result",
        [
            (
                "basic",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                None,
                None,
                None,
                {
                    "current_payees": [
                        {
                            "first_name": "Trent",
                            "last_name": "Arnold",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                            "crystal_mapping": [
                                {
                                    "view_id": uuid.UUID(
                                        "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                    ),
                                    "view_name": "Crystal A",
                                    "status": "published",
                                },
                                {
                                    "view_id": uuid.UUID(
                                        "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                    ),
                                    "view_name": "Crystal D",
                                    "status": "draft",
                                },
                            ],
                        }
                    ],
                    "available_payees": [
                        {
                            "first_name": "Cole",
                            "last_name": "Palmer",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Cristiano",
                            "last_name": "Ronaldo",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Dhanush",
                            "last_name": "S",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Lionel",
                            "last_name": "Messi",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "MS",
                            "last_name": "Dhoni",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Neymar",
                            "last_name": "Jr",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Nico",
                            "last_name": "Williams",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Rodrygo",
                            "last_name": "Jr",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Virat",
                            "last_name": "Kohli",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                    ],
                    "total_current_payees": 1,
                    "total_available_payees": 9,
                },
            ),
            (
                "with_filters",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                {
                    "user_groups": ["c964c434-817b-40cf-9dfd-b00a1e679965"],
                    "plans": ["1fda01cf-f713-4ede-a39a-4b9706589b52"],
                    "teams": ["85c27922-9f23-45a6-88df-340180a9402f"],
                    "designations": ["SDe 1"],
                },
                None,
                None,
                None,
                {
                    "current_payees": [],
                    "available_payees": [],
                    "total_current_payees": 0,
                    "total_available_payees": 0,
                },
            ),
            (
                "with_search",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                "dhan",
                None,
                None,
                {
                    "current_payees": [],
                    "available_payees": [
                        {
                            "first_name": "Dhanush",
                            "last_name": "S",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        }
                    ],
                    "total_current_payees": 0,
                    "total_available_payees": 1,
                },
            ),
            (
                "with_pagination",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                None,
                2,
                2,
                {
                    "current_payees": [],
                    "available_payees": [
                        {
                            "first_name": "Dhanush",
                            "last_name": "S",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                        {
                            "first_name": "Lionel",
                            "last_name": "Messi",
                            "email": "<EMAIL>",
                            "profile_picture": None,
                        },
                    ],
                    "total_current_payees": 0,
                    "total_available_payees": 2,
                },
            ),
        ],
    )
    def test_get_separated_payees_info(
        self,
        crystal_data_wrapper,
        test_name,
        crystal_view_id,
        filters,
        search_term,
        offset_value,
        limit_value,
        expected_result,
    ):
        """Test getting separated payees info with different scenarios"""

        # Execute
        result = crystal_data_wrapper.get_separated_payees_info(
            login_user="<EMAIL>",
            crystal_view_id=crystal_view_id,
            filters=filters,
            search_term=search_term,
            offset_value=offset_value,
            limit_value=limit_value,
        )

        # Sort crystal mappings by view_name in both result and expected_result
        if result and "current_payees" in result:
            for payee in result["current_payees"]:
                if "crystal_mapping" in payee:
                    payee["crystal_mapping"].sort(key=lambda x: x["view_name"])

        if expected_result and "current_payees" in expected_result:
            for payee in expected_result["current_payees"]:
                if "crystal_mapping" in payee:
                    payee["crystal_mapping"].sort(key=lambda x: x["view_name"])

        # Assert
        assert result == expected_result

    @pytest.mark.parametrize(
        "expected_result",
        [
            [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ]
        ],
    )
    def test_get_eligible_crystal_payees(self, crystal_data_wrapper, expected_result):
        """Test fetching eligible crystal payees and ensuring the list matches irrespective of ordering."""

        result = crystal_data_wrapper.get_eligible_crystal_payees()

        # Sort both lists to ensure order doesn't affect the assertion
        assert sorted(result) == sorted(expected_result)
