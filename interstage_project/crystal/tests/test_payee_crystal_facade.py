# pylint: disable-all

import datetime
import uuid
from decimal import Decimal
from unittest.mock import MagicMock, patch

import pytest
import pytz
from django.utils import timezone
from rest_framework import status

from commission_engine.utils.date_utils import convert_str_to_date, end_of_day
from crystal.crystal_calculator import WhatIfCalculator
from crystal.crystal_data_wrapper import CrystalDataWrapper
from crystal.exceptions.exceptions import CrystalException

from ..crystal_facade import PayeeCrystalBall, SetupCrystalBall
from ..types import AuditDataType
from ..utils.crystal_utils import CrystalAction, CrystalViewStatus
from .data_wrapper import MockCrystalDataWrapper
from .test_output import *


def xtest_apply_projections():
    """
    Check out this video on behaviour of apply projections on some basic scenarios to try out your values for actual test case :

    Link : https://www.loom.com/share/d6c0114784c34d28bdc09c43703e295e
    """
    client_id = 4
    payee_email = "<EMAIL>"
    knowledge_date = str(timezone.now())
    success_criteria_applied_overrides = [
        {
            "crystal_table_id": "41b7a58e-22fd-4c36-baaa-c51d4f8b9de5",
            "data": [
                {
                    "lhs_co_1_amount": 6000,
                    "rhs_co_5_amount": 0,
                    "lhs_co_1_id": 103,
                    "row_key": "103.0##::##none",
                    "lhs_co_1_dos": "30-Jun-2022",
                }
            ],
            "source_id": "2433d23f-efa4-4aeb-bce7-fd0fb67eb142",
            "deal_name_column_name": "lhs_co_1_id",
            "table_name": "joinerTable",
        },
        {
            "crystal_table_id": "de64655b-249d-48ef-9e77-ddda60c38a39",
            "data": [
                {
                    "co_1_amount": 1000,
                    "co_1_id": 50,
                    "row_key": "50",
                    "co_1_dos": "30-Jun-2022",
                }
            ],
            "source_id": "31460e56-c167-4196-9e2d-1e362e0dee8b",
            "deal_name_column_name": "co_1_id",
            "table_name": "co1table",
        },
        {
            "crystal_table_id": "7339f83f-5d01-4ba7-9876-85cf41a1f821",
            "data": [
                {
                    "co_1_amount": 1000,
                    "co_1_id": 50,
                    "row_key": "50",
                    "co_1_dos": "30-Jun-2022",
                }
            ],
            "source_id": 1,
            "deal_name_column_name": "co_1_id",
            "table_name": "irrelevantTable",
        },
    ]
    period_start_date = datetime.datetime(2022, 6, 1, 0, 0, tzinfo=timezone.utc)
    period_end_date = datetime.datetime(
        2022, 6, 30, 23, 59, 59, 999999, tzinfo=timezone.utc
    )
    version = 3
    id = uuid.uuid4()
    calculator = WhatIfCalculator(
        client_id=client_id,
        payee_email=payee_email,
        knowledge_date=knowledge_date,
        version=version,
        data_overrides=success_criteria_applied_overrides,
        period_start_date=period_start_date,
        period_end_date=period_end_date,
    )
    what_if_result_data = calculator.apply_projections()
    plan_id = "54e76416-**************-fa2ecb2a63c0"
    criteria_id = "75c3dfc1-3504-44c5-961b-214bb7d82715"
    return what_if_result_data[plan_id]["criteria_outputs"][criteria_id][
        "criteria_output"
    ]["commission"][0]["commission"] == Decimal("6000.00")


def xtest_crystal_get_data():
    payee_crystal = PayeeCrystalBall(1, "<EMAIL>", MockCrystalDataWrapper())
    query = {
        "crystal_table_id": 1,
        "source_id": "cust-obj-1",
        "email_field": "email",
        "display_conditions": [
            {
                "type": "FILTER",
                "col_name": "deal_status",
                "operator": "==",
                "value": "zombie",
            }
        ],
        "display_columns": ["id", "deal_name", "deal_amount", "deal_status"],
    }
    # TODO: validate login user
    data_tbl = payee_crystal.get_crystal_table_data(query, login_user="<EMAIL>")
    # print(data_tbl)
    assert len(data_tbl) != 0
    # Get names of all columns in a pandas dataframe
    assert set(data_tbl.columns) == set(
        ["id", "deal_name", "deal_amount", "row_key", "deal_status"]
    )
    # There should not be any value of deal_status == "open"
    assert len(data_tbl[data_tbl["deal_status"] == "open"]) == 0


def xtest_apply_success_actions():
    mock_wrapper = MockCrystalDataWrapper()
    payee_crystal = PayeeCrystalBall(1, "<EMAIL>", mock_wrapper)
    success_actions = mock_wrapper.get_crystal_view_success_actions(1)
    data_overrides = [
        {"custom_object_id": "cu-1", "data": [{"amount": 2000, "row_key": 3}]}
    ]
    overrides = payee_crystal._apply_success_actions_on_data_overrides(
        data_overrides, success_actions
    )
    assert overrides == [
        {
            "custom_object_id": "cu-1",
            "data": [{"amount": 2000, "row_key": 3, "deal_status": "closed"}],
        }
    ]


def xtest_calculate_commission():
    """
    Test commission calculation logic - we should mock out data fetching logic
    """
    client_id = 4
    payee = "<EMAIL>"
    crystal_view_id = "123e4567-e89b-12d3-a456-************"  # UUID
    data_overrides = [
        {
            "custom_object_id": 1,
            "data": [
                {"row_key": 1, "co_1_amount": 500},
                {"row_key": 3, "co_1_amount": 300},
            ],
        }
    ]
    result_v3 = PayeeCrystalBall(client_id, payee).calculate_commissions(
        crystal_view_id, data_overrides, timezone.now(), version=3
    )
    result_v2 = PayeeCrystalBall(client_id, payee).calculate_commissions(
        crystal_view_id, data_overrides, timezone.now(), version=2
    )
    assert result_v3 == result_v2


def test_commission_calcuation_to_ui_transformation():
    """
    Test the function that converts commission and quota calculation results into what the UI needs
    """
    pass


def test_map_payee_to_crystal_view():
    pass


@pytest.mark.django_db
@pytest.mark.parametrize(
    "is_manager_rollup_ed, payee, expected_team_members",
    [
        (
            True,
            "<EMAIL>",
            ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        ),
        (False, "<EMAIL>", ["<EMAIL>", "<EMAIL>"]),
        (
            True,
            "<EMAIL>",
            ["<EMAIL>", "<EMAIL>"],
        ),
        (False, "<EMAIL>", ["<EMAIL>", "<EMAIL>"]),
        (True, "<EMAIL>", ["<EMAIL>"]),
        (True, "<EMAIL>", ["<EMAIL>"]),
        (
            False,
            "<EMAIL>",
            [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
        ),
        (
            True,
            "<EMAIL>",
            [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
        ),
    ],
)
def test_get_payee_reportees(is_manager_rollup_ed, payee, expected_team_members):
    date = end_of_day(convert_str_to_date("2023-06-16"))
    client_id = 4
    team_members = CrystalDataWrapper(client_id).get_payee_reportees(
        payee, knowledge_date=date, is_manager_rollup_ed=is_manager_rollup_ed
    )
    assert set(team_members) == set(expected_team_members)


@pytest.mark.django_db
@patch("crystal.crystal_facade.get_client_timezone")
@patch("crystal.crystal_data_wrapper.get_client_fiscal_start_month")
class TestGetFuturePeriods:
    client_id = 4
    login_user = "<EMAIL>"

    weekly_payee = "<EMAIL>"
    monthly_payee = "<EMAIL>"
    quarterly_payee = "<EMAIL>"
    half_yearly_payee = "<EMAIL>"
    annual_payee = "<EMAIL>"

    # Testcases includes different start_dates, timezones and knowledge_date
    test_cases = [
        (
            1,
            "Asia/Calcutta",
            datetime.datetime(2025, 1, 1, 0, 5, tzinfo=pytz.timezone("Asia/Calcutta")),
        ),
        (
            3,
            "Africa/Abidjan",
            datetime.datetime(2025, 4, 1, 0, 5, tzinfo=pytz.timezone("Asia/Calcutta")),
        ),
        (
            7,
            "Asia/Calcutta",
            datetime.datetime(2025, 4, 1, 0, 5, tzinfo=pytz.timezone("Asia/Calcutta")),
        ),
        (1, "UTC", datetime.datetime(2024, 12, 31, 23, 59, tzinfo=pytz.UTC)),
    ]

    def test_get_future_periods_for_weekly_payee(self, mock_start_month, mock_timezone):
        mock_timezone.return_value = "UTC"
        knowledge_date = datetime.datetime(2025, 7, 3, 0, 0, tzinfo=pytz.UTC)
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.weekly_payee,
            login_user=self.login_user,
        )
        future_periods = payee_crystal.get_future_periods(knowledge_date)
        assert future_periods == weekly_payee_test_01

    def test_get_future_periods_for_weekly_payee_with_no_calendar(
        self, mock_start_month, mock_timezone
    ):
        mock_timezone.return_value = "UTC"
        knowledge_date = datetime.datetime(2028, 1, 3, 1, 0, tzinfo=pytz.UTC)
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.weekly_payee,
            login_user=self.login_user,
        )
        with pytest.raises(CrystalException, match="NO_PERIODS"):
            payee_crystal.get_future_periods(knowledge_date)

    def test_get_future_periods_for_weekly_payee_with_next_calendar(
        self, mock_start_month, mock_timezone
    ):
        knowledge_date = datetime.datetime(2026, 1, 12, 1, 0, tzinfo=pytz.UTC)
        mock_timezone.return_value = "UTC"
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.weekly_payee,
            login_user=self.login_user,
        )
        future_periods = payee_crystal.get_future_periods(knowledge_date)
        assert future_periods == weekly_payee_test_02

    @pytest.mark.parametrize(
        "start_month, time_zone, kd, expected_periods",
        [
            (*(test_cases[0][:3]), monthly_payee_test_01),
            (*(test_cases[1][:3]), monthly_payee_test_02),
            (*(test_cases[2][:3]), monthly_payee_test_03),
            (*(test_cases[3][:3]), monthly_payee_test_04),
        ],
    )
    def test_get_future_periods_for_monthly_payee(
        self,
        mock_start_month,
        mock_timezone,
        start_month,
        time_zone,
        kd,
        expected_periods,
    ):
        mock_timezone.return_value = time_zone
        mock_start_month.return_value = start_month
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.monthly_payee,
            login_user=self.login_user,
        )
        future_periods = payee_crystal.get_future_periods(kd)
        assert future_periods == expected_periods

    @pytest.mark.parametrize(
        "start_month, time_zone, kd, expected_periods",
        [
            (*(test_cases[0][:3]), quarterly_payee_test_01),
            (*(test_cases[1][:3]), quarterly_payee_test_02),
            (*(test_cases[2][:3]), quarterly_payee_test_03),
            (*(test_cases[3][:3]), quarterly_payee_test_04),
        ],
    )
    def test_get_future_periods_for_quarterly_payee(
        self,
        mock_start_month,
        mock_timezone,
        start_month,
        time_zone,
        kd,
        expected_periods,
    ):
        mock_timezone.return_value = time_zone
        mock_start_month.return_value = start_month
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.quarterly_payee,
            login_user=self.login_user,
        )
        future_periods = payee_crystal.get_future_periods(kd)
        assert future_periods == expected_periods

    @pytest.mark.parametrize(
        "start_month, time_zone, kd, expected_periods",
        [
            (*(test_cases[0][:3]), half_yearly_payee_test_01),
            (*(test_cases[1][:3]), half_yearly_payee_test_02),
            (*(test_cases[2][:3]), half_yearly_payee_test_03),
            (*(test_cases[3][:3]), half_yearly_payee_test_04),
        ],
    )
    def test_get_future_periods_for_half_yearly_payee(
        self,
        mock_start_month,
        mock_timezone,
        start_month,
        time_zone,
        kd,
        expected_periods,
    ):
        mock_timezone.return_value = time_zone
        mock_start_month.return_value = start_month
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.half_yearly_payee,
            login_user=self.login_user,
        )
        future_periods = payee_crystal.get_future_periods(kd)
        assert future_periods == expected_periods

    @pytest.mark.parametrize(
        "start_month, time_zone, kd, expected_periods",
        [
            (*(test_cases[0][:3]), annual_payee_test_01),
            (*(test_cases[1][:3]), annual_payee_test_02),
            (*(test_cases[2][:3]), annual_payee_test_03),
            (*(test_cases[3][:3]), annual_payee_test_04),
        ],
    )
    def test_get_future_periods_for_annual_payee(
        self,
        mock_start_month,
        mock_timezone,
        start_month,
        time_zone,
        kd,
        expected_periods,
    ):
        mock_timezone.return_value = time_zone
        mock_start_month.return_value = start_month
        payee_crystal = PayeeCrystalBall(
            client_id=self.client_id,
            desired_payee=self.annual_payee,
            login_user=self.login_user,
        )
        future_periods = payee_crystal.get_future_periods(kd)

        assert future_periods == expected_periods


@pytest.fixture
def crystal_facade():
    return SetupCrystalBall(client_id=10041)


@pytest.fixture
def mock_extra_data():
    return AuditDataType(
        knowledge_begin_date=timezone.now(),
        last_modified_at=timezone.now(),
        last_modified_by="<EMAIL>",
        additional_details={},
    )


@pytest.mark.django_db
class TestBulkAddCrystalViewUsers:
    @pytest.mark.parametrize(
        "test_name,crystal_view_id,search_term,filters,bulk_selection,expected_result",
        [
            (
                "success_all_selected",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                None,
                {"selected_all": True, "toggle_rows": []},
                {
                    "message": "Some payees are already part of other published crystal plans",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            }
                        ],
                        "total_current_payees": 9,
                        "total_available_payees": 1,
                    },
                    "already_mapped_payees": [
                        {
                            "employee_email_id": "<EMAIL>",
                            "employee_name": "Nico Williams",
                            "views_mapped": [
                                {
                                    "view_id": uuid.UUID(
                                        "229709b0-d60a-4781-89fa-a693647b5f09"
                                    ),
                                    "view_name": "Crystal B",
                                    "status": "published",
                                }
                            ],
                        }
                    ],
                    "failed_payees": 1,
                },
            ),
            (
                "success_toggle_rows",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                None,
                {
                    "selected_all": False,
                    "toggle_rows": ["<EMAIL>", "<EMAIL>"],
                },
                {
                    "message": "successfully added 2 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 3,
                        "total_available_payees": 7,
                    },
                },
            ),
            (
                "exclude_specific_users",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                None,
                {
                    "selected_all": True,
                    "toggle_rows": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                },
                {
                    "message": "successfully added 5 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 6,
                        "total_available_payees": 4,
                    },
                },
            ),
            (
                "user_in_published_plan",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                None,
                {
                    "selected_all": False,
                    "toggle_rows": ["<EMAIL>", "<EMAIL>"],
                },
                {
                    "message": "Some payees are already part of other published crystal plans",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 2,
                        "total_available_payees": 8,
                    },
                    "already_mapped_payees": [
                        {
                            "employee_email_id": "<EMAIL>",
                            "employee_name": "Nico Williams",
                            "views_mapped": [
                                {
                                    "view_id": uuid.UUID(
                                        "229709b0-d60a-4781-89fa-a693647b5f09"
                                    ),
                                    "view_name": "Crystal B",
                                    "status": "published",
                                }
                            ],
                        }
                    ],
                    "failed_payees": 1,
                },
            ),
            (
                "already_mapped_payees_with_draft_view",
                "f3255e31-36cb-45fc-b753-689362dcb0da",
                None,
                None,
                {
                    "selected_all": False,
                    "toggle_rows": ["<EMAIL>", "<EMAIL>"],
                },
                {
                    "message": "successfully added 2 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "229709b0-d60a-4781-89fa-a693647b5f09"
                                        ),
                                        "view_name": "Crystal B",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 3,
                        "total_available_payees": 7,
                    },
                },
            ),
            (
                "empty_users",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                None,
                None,
                {"selected_all": True, "toggle_rows": []},
                {
                    "message": "successfully added 0 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "229709b0-d60a-4781-89fa-a693647b5f09"
                                        ),
                                        "view_name": "Crystal B",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [],
                        "total_current_payees": 10,
                        "total_available_payees": 0,
                    },
                },
            ),
            (
                "with_search_term",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                "dhan",
                None,
                {"selected_all": True, "toggle_rows": []},
                {
                    "message": "successfully added 1 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 2,
                        "total_available_payees": 8,
                    },
                },
            ),
            (
                "with_filters",
                "3db42f03-e4e3-47ed-98e6-10f1f6d980c5",
                None,
                {
                    "user_groups": ["c964c434-817b-40cf-9dfd-b00a1e679965"],
                    "plans": ["1fda01cf-f713-4ede-a39a-4b9706589b52"],
                    "teams": ["85c27922-9f23-45a6-88df-340180a9402f"],
                    "designations": ["SDe 1"],
                },
                {"selected_all": True, "toggle_rows": []},
                {
                    "message": "successfully added 0 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 1,
                        "total_available_payees": 9,
                    },
                },
            ),
        ],
    )
    def test_bulk_add_crystal_view_users(
        self,
        crystal_facade,
        mock_extra_data,
        test_name,
        crystal_view_id,
        bulk_selection,
        expected_result,
        search_term,
        filters,
    ):
        """Test bulk adding users to a crystal view with different scenarios"""
        # Execute

        result = crystal_facade.bulk_action_crystal(
            crystal_view_id=crystal_view_id,
            bulk_selection=bulk_selection,
            crystal_action=CrystalAction.ADD_MODE.value,
            extra_data=mock_extra_data,
            login_user="<EMAIL>",
            search_term=search_term,
            filters=filters,
        )

        # Sort crystal mappings by view_name in both result and expected_result
        if (
            result
            and "updated_payees" in result
            and "current_payees" in result["updated_payees"]
        ):
            for payee in result["updated_payees"]["current_payees"]:
                if "crystal_mapping" in payee:
                    payee["crystal_mapping"].sort(key=lambda x: x["view_name"])

        if (
            expected_result
            and "updated_payees" in expected_result
            and "current_payees" in expected_result["updated_payees"]
        ):
            for payee in expected_result["updated_payees"]["current_payees"]:
                if "crystal_mapping" in payee:
                    payee["crystal_mapping"].sort(key=lambda x: x["view_name"])

        # Assert
        assert result == expected_result


@pytest.mark.django_db
class TestBulkDeleteCrystalViewUsers:
    @pytest.mark.parametrize(
        "test_name,crystal_view_id,search_term,filters,bulk_selection,expected_result,expected_exception",
        [
            (
                "success_all_selected",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                None,
                None,
                {"selected_all": True, "toggle_rows": []},
                None,
                CrystalException(
                    code="AT_LEAST_ONE_PAYEE_REQUIRED_IN_THE_CRYSTAL_VIEW",
                    status=status.HTTP_400_BAD_REQUEST,
                    payload={
                        "message": "At least one payee is required in the crystal view"
                    },
                ),
            ),
            (
                "success_toggle_rows",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                None,
                None,
                {
                    "selected_all": False,
                    "toggle_rows": ["<EMAIL>", "<EMAIL>"],
                },
                {
                    "message": "Successfully removed 2 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "229709b0-d60a-4781-89fa-a693647b5f09"
                                        ),
                                        "view_name": "Crystal B",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 8,
                        "total_available_payees": 2,
                    },
                },
                None,
            ),
            (
                "delete_all_but_one",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                None,
                None,
                {"selected_all": True, "toggle_rows": ["<EMAIL>"]},
                {
                    "message": "Successfully removed 9 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            },
                        ],
                        "total_current_payees": 1,
                        "total_available_payees": 9,
                    },
                },
                None,
            ),
            (
                "with_search_term",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                "dhan",
                None,
                {"selected_all": True, "toggle_rows": []},
                {
                    "message": "Successfully removed 1 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "229709b0-d60a-4781-89fa-a693647b5f09"
                                        ),
                                        "view_name": "Crystal B",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                            }
                        ],
                        "total_current_payees": 9,
                        "total_available_payees": 1,
                    },
                },
                None,
            ),
            (
                "with_filters",
                "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24",
                None,
                {
                    "user_groups": ["c964c434-817b-40cf-9dfd-b00a1e679965"],
                    "plans": ["1fda01cf-f713-4ede-a39a-4b9706589b52"],
                    "teams": ["85c27922-9f23-45a6-88df-340180a9402f"],
                    "designations": ["SDe 1"],
                },
                {"selected_all": True, "toggle_rows": []},
                {
                    "message": "Successfully removed 0 payees",
                    "updated_payees": {
                        "current_payees": [
                            {
                                "first_name": "Cole",
                                "last_name": "Palmer",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Cristiano",
                                "last_name": "Ronaldo",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Dhanush",
                                "last_name": "S",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Lionel",
                                "last_name": "Messi",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "MS",
                                "last_name": "Dhoni",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Neymar",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "f3255e31-36cb-45fc-b753-689362dcb0da"
                                        ),
                                        "view_name": "Crystal C",
                                        "status": "draft",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Nico",
                                "last_name": "Williams",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "229709b0-d60a-4781-89fa-a693647b5f09"
                                        ),
                                        "view_name": "Crystal B",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Rodrygo",
                                "last_name": "Jr",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Trent",
                                "last_name": "Arnold",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "3db42f03-e4e3-47ed-98e6-10f1f6d980c5"
                                        ),
                                        "view_name": "Crystal A",
                                        "status": "published",
                                    },
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                            {
                                "first_name": "Virat",
                                "last_name": "Kohli",
                                "email": "<EMAIL>",
                                "profile_picture": None,
                                "crystal_mapping": [
                                    {
                                        "view_id": uuid.UUID(
                                            "6eb81ae9-5904-4e1d-9e6d-1bf5788ebf24"
                                        ),
                                        "view_name": "Crystal D",
                                        "status": "draft",
                                    },
                                ],
                            },
                        ],
                        "available_payees": [],
                        "total_current_payees": 10,
                        "total_available_payees": 0,
                    },
                },
                None,
            ),
        ],
    )
    def test_bulk_delete_crystal_view_users(
        self,
        crystal_facade,
        mock_extra_data,
        test_name,
        crystal_view_id,
        search_term,
        filters,
        bulk_selection,
        expected_result,
        expected_exception,
    ):
        """Test bulk deleting users from a crystal view with different scenarios"""
        if expected_exception:
            with pytest.raises(CrystalException) as exc_info:
                crystal_facade.bulk_action_crystal(
                    crystal_view_id=crystal_view_id,
                    bulk_selection=bulk_selection,
                    crystal_action=CrystalAction.REMOVE_MODE.value,
                    extra_data=mock_extra_data,
                    login_user="<EMAIL>",
                    search_term=search_term,
                    filters=filters,
                )
            assert exc_info.value.code == expected_exception.code
            assert exc_info.value.status == expected_exception.status
            assert exc_info.value.payload == expected_exception.payload
        else:
            # Execute
            result = crystal_facade.bulk_action_crystal(
                crystal_view_id=crystal_view_id,
                bulk_selection=bulk_selection,
                crystal_action=CrystalAction.REMOVE_MODE.value,
                extra_data=mock_extra_data,
                login_user="<EMAIL>",
                search_term=search_term,
                filters=filters,
            )

            # Sort crystal mappings by view_name in both result and expected_result
            if (
                result
                and "updated_payees" in result
                and "current_payees" in result["updated_payees"]
            ):
                for payee in result["updated_payees"]["current_payees"]:
                    if "crystal_mapping" in payee:
                        payee["crystal_mapping"].sort(key=lambda x: x["view_name"])

            if (
                expected_result
                and "updated_payees" in expected_result
                and "current_payees" in expected_result["updated_payees"]
            ):
                for payee in expected_result["updated_payees"]["current_payees"]:
                    if "crystal_mapping" in payee:
                        payee["crystal_mapping"].sort(key=lambda x: x["view_name"])

            # Assert
            assert result == expected_result
