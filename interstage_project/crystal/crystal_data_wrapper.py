"""
Module that provides all data retrieval functions for crystal
"""

import logging
import uuid
from typing import List

import pandas as pd
import pydash
import pytz
from django.db import connection
from django.utils import timezone

from commission_engine.accessors.client_accessor import (
    get_client_fiscal_start_month,
    get_crystal_custom_calendar_future_periods,
)
from commission_engine.services.commission_calculation_service.team_calculator import (
    get_team_members,
)
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    get_datasheets_by_id,
    get_objects_by_datasheet_id,
    get_qualifying_objects,
    get_variables_for_ds_ids,
    is_datasheet_exists,
)
from commission_engine.utils.date_utils import get_future_periods_for_static_frequencies
from commission_engine.utils.general_data import (
    RBAC,
    RBACComponent,
    SegmentEvents,
    SegmentProperties,
    static_frequencies,
)
from commission_engine.utils.general_utils import set_accurate_column_types
from crystal.accessors.crystal_admin_accessor import (
    CrystalAdminAccessor,
    PayeeCrystalAccessor,
)
from crystal.exceptions.exceptions import CrystalException
from crystal.serializers.crystal_admin_settings_serializers import (
    CrystalPayeeSerializer,
    CrystalViewModelSerializer,
)
from crystal.types import (
    AuditDataType,
    CheckPayeesType,
    CrystalTableSettingsData,
    CrystalViewType,
    PayeeDetails,
)
from crystal.utils.crystal_utils import (
    KEY_NAME_COLUMN,
    CrystalViewStatus,
    get_display_names_map,
    validate_datasheet_variables_in_crystal,
)
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.config_services.employee_services import (
    get_all_employee_plans,
    get_all_employees,
    get_all_valid_employees_for_date,
    get_employees_name,
    get_employees_name_profile_picture,
)
from spm.services.custom_calendar_services import get_custom_periods
from spm.services.custom_object_services.custom_object_service import (
    all_variables_in_object,
    get_all_data_types,
    get_co_qualifying_objects,
    get_object_by_ids,
)
from spm.services.databook_services import (
    get_databook_by_ids,
    get_databook_id_by_datasheet_id,
)
from spm.services.period_label_services import get_period_label_map
from spm.services.rbac_services import get_data_permission, get_valid_payee_emails

from .utils.crystal_utils import get_psd_ped_for_payee

logger = logging.getLogger(__name__)


class CrystalDataWrapper:
    """
    Interface to fetch data for all crystal related operations
    """

    def __init__(self, client_id) -> None:
        self.client_id = client_id
        self.admin_accessor = CrystalAdminAccessor(self.client_id)

    def get_crystal_view(self, crystal_view_id):
        logger.info(
            "BEGIN : get_crystal_view", extra={"crystal_view_id": crystal_view_id}
        )
        view = self.admin_accessor.get_crystal_view(crystal_view_id)
        payee_dict = PayeeCrystalAccessor(self.client_id).get_payees(
            crystal_view_ids=[crystal_view_id]
        )
        logger.debug("Payees fetched", extra={"payee_dict": payee_dict})
        if not isinstance(crystal_view_id, uuid.UUID):
            crystal_view_id = uuid.UUID(crystal_view_id)
        view_data = self._populate_view_with_payee_info(
            view, payee_dict.get(crystal_view_id, [])
        )

        logger.debug(
            "Payee info fetched to populate view", extra={"view_data": view_data}
        )
        logger.info("END : get_crystal_view")
        return view_data

    def get_crystal_view_name(self, crystal_view_id):
        """
        Returns the name of the crystal view.
        """
        return self.admin_accessor.get_crystal_view_name(crystal_view_id)

    def get_all_crystal_views(self):
        logger.info("BEGIN : get_all_crystal_views")
        views = self.admin_accessor.get_all_crystal_views()
        view_ids = [view.crystal_view_id for view in views]
        payee_dict = PayeeCrystalAccessor(self.client_id).get_payees(
            crystal_view_ids=view_ids
        )
        views_data = [
            self._populate_view_with_payee_info(
                view, payee_dict.get(view.crystal_view_id, [])
            )
            for view in views
        ]
        logger.debug(
            "Payee info fetched to populate view", extra={"views_data": views_data}
        )
        logger.info("END : get_all_crystal_views")
        return views_data

    def save_crystal_view(
        self, view_data: CrystalViewType, extra_data: AuditDataType = None
    ) -> str:
        """
        Creates or updates a crystal view.  Since we use a bitemporal system, an update means 'invalidate and create'
        """
        logger.info("BEGIN : save_crystal_view")
        logger.debug(
            "save_crystal_view params",
            extra={"view_data": view_data, "extra_data": extra_data},
        )
        is_update = "crystal_view_id" in view_data
        data_to_save = {**view_data, **extra_data}
        view_id = None
        if is_update:
            logger.info("Updating view")
            logger.debug(
                "View data to be updated", extra={"data_to_save": data_to_save}
            )
            view_id = self._update_view(data_to_save=data_to_save)
        else:
            logger.info("Creating view")
            logger.debug(
                "View data to be created", extra={"data_to_save": data_to_save}
            )
            view_id = self._create_view(data_to_save=data_to_save)
        logger.info("END : save_crystal_view", extra={"view_id": view_id})
        return view_id

    def _create_view(self, data_to_save):
        logger.info("BEGIN : _create_view")
        logger.debug("_create_view param", extra={"data_to_save": data_to_save})
        view_id = uuid.uuid4()
        logger.info("Creating new crystal view", extra={"view_id": view_id})
        data_to_save.update({"crystal_view_id": view_id, "settings_data": []})
        # Validate view name if specified
        self._validate_view_name(data_to_save)
        serializer = CrystalViewModelSerializer(data=data_to_save)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        logger.info("END : _create_view", extra={"view_id": view_id})
        return view_id

    def _update_view(self, data_to_save):
        logger.info("BEGIN : _update_view")
        logger.debug("_update_view param", extra={"data_to_save": data_to_save})
        view_id = data_to_save["crystal_view_id"]
        view_name_edit = data_to_save.get("view_name_edit", None)
        if view_name_edit:
            # Validate crystal_view_name only when editing crystal_view
            logger.info("Validating view_name for edit operation")
            self._validate_view_name(data_to_save)
            data_to_save.pop("view_name_edit")

        logger.info("Updating crystal view", extra={"view_id": view_id})
        # Since we support partial updates, merge the existing data with the new data
        existing_data_model = self.admin_accessor.get_crystal_view(view_id)
        existing_data_dict = CrystalViewModelSerializer(
            existing_data_model,
            exclude=[
                "temporal_id",
                "knowledge_begin_date",
                "knowledge_end_date",
                "last_modified_at",
                "last_modified_by",
            ],
        ).data

        merged_data = {**existing_data_dict, **data_to_save}
        # partial=True lets you skip other required fields and just update the ones you want
        serializer = CrystalViewModelSerializer(data=merged_data, partial=True)
        serializer.is_valid(raise_exception=True)
        knowledge_date = data_to_save["knowledge_begin_date"]  # type: ignore
        self.admin_accessor.invalidate_crystal_view(
            view_id, knowledge_date=knowledge_date
        )
        serializer.save()
        logger.info("END : _update_view", extra={"view_id": view_id})
        return view_id

    def map_crystal_view_to_payees(
        self, payees, crystal_view_id, extra_data: AuditDataType = None
    ) -> str:
        """
        Invalidates previous payees mapped to crystal view and
        only map current payee list
        """
        logger.info("BEGIN : map_crystal_view_to_payees")
        logger.debug(
            "map_crystal_view_to_payees params",
            extra={
                "payees": payees,
                "crystal_view_id": crystal_view_id,
                "extra_data": extra_data,
            },
        )
        if not self.admin_accessor.does_crystal_view_exists(crystal_view_id):
            raise CrystalException(
                code="NO_CRYSTAL_VIEW_DATA",
                payload={"crystal_view_id": crystal_view_id},
            )

        payee_views = []
        for payee_email_id in payees:
            payee_view = {
                "payee": payee_email_id,
                "crystal_view_id": crystal_view_id,
            }
            payee_view.update(extra_data)
            payee_views.append(payee_view)
        logger.debug("Payee view info", extra={"payee_views": payee_views})
        pca = PayeeCrystalAccessor(self.client_id)
        pca.invalidate_all_payees(crystal_view_id, extra_data["knowledge_begin_date"])
        serializer = CrystalPayeeSerializer(data=payee_views, many=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        # update last_modified_at field for the mapped view
        view = self.admin_accessor.get_crystal_view(crystal_view_id)
        view.last_modified_at = extra_data["last_modified_at"]
        view.save()

        logger.info(
            "Mapped payees for a given crystal view",
            extra={"crystal_view_id": crystal_view_id},
        )
        return crystal_view_id

    def add_payees_to_crystal_view(
        self, payees: List[str], crystal_view_id: str, extra_data: AuditDataType = None
    ) -> str:
        """
        Adds new payees to an existing crystal view without invalidating existing payees.

        Args:
            payees: List of payee email IDs to add
            crystal_view_id: ID of the crystal view to add payees to
            extra_data: Audit data containing knowledge_begin_date and last_modified_at

        Returns:
            crystal_view_id: ID of the crystal view

        Raises:
            CrystalException: If crystal view doesn't exist
        """
        logger.info("BEGIN : add_payees_to_crystal_view")
        logger.debug(
            "add_payees_to_crystal_view params",
            extra={
                "payees": payees,
                "crystal_view_id": crystal_view_id,
                "extra_data": extra_data,
            },
        )

        payee_views = []
        for payee_email_id in payees:
            payee_view = {
                "payee": payee_email_id,
                "crystal_view_id": crystal_view_id,
                "additional_details": extra_data["additional_details"],
                "client_id": self.client_id,
                "knowledge_begin_date": extra_data["knowledge_begin_date"],
            }
            payee_views.append(payee_view)

        logger.debug("New payee views to add", extra={"payee_views": payee_views})

        # Save new payee mappings
        cpa = PayeeCrystalAccessor(self.client_id)
        cpa.create_objects(payee_views)

        # Update last_modified_at field for the admin view
        view = self.admin_accessor.get_crystal_view(crystal_view_id)
        view.last_modified_at = extra_data["last_modified_at"]
        view.save()

        logger.info(
            "Added new payees to crystal view",
            extra={
                "crystal_view_id": crystal_view_id,
                "new_payees_count": len(payees),
            },
        )
        return crystal_view_id

    def remove_payees_from_crystal_view(
        self, payees: List[str], crystal_view_id: str, extra_data: AuditDataType = None
    ) -> str:
        """
        Removes specific payees from a crystal view without affecting other payees.

        Args:
            payees: List of payee email IDs to remove
            crystal_view_id: ID of the crystal view to remove payees from
            extra_data: Audit data containing knowledge_begin_date and last_modified_at

        Returns:
            crystal_view_id: ID of the crystal view

        Raises:
            CrystalException: If crystal view doesn't exist
        """
        logger.info("BEGIN : remove_payees_from_crystal_view")
        logger.debug(
            "remove_payees_from_crystal_view params",
            extra={
                "payees": payees,
                "crystal_view_id": crystal_view_id,
                "extra_data": extra_data,
            },
        )

        # Get existing payees to validate removal
        pca = PayeeCrystalAccessor(self.client_id)
        existing_payees = pca.get_payees(crystal_view_ids=[crystal_view_id])
        existing_payee_set = set(existing_payees.get(uuid.UUID(crystal_view_id), []))

        # Filter out payees that don't exist in the view
        payees_to_remove = [payee for payee in payees if payee in existing_payee_set]

        if not payees_to_remove:
            logger.info("No payees to remove")
            return crystal_view_id

        # Invalidate the specified payee mappings
        pca.invalidate_payees(
            crystal_view_id=crystal_view_id,
            payee_list=payees_to_remove,
            ked=extra_data["knowledge_begin_date"],
        )

        # Update last_modified_at field for the view
        view = self.admin_accessor.get_crystal_view(crystal_view_id)
        view.last_modified_at = extra_data["last_modified_at"]
        view.save()

        logger.info(
            "Removed payees from crystal view",
            extra={
                "crystal_view_id": crystal_view_id,
                "removed_payees_count": len(payees_to_remove),
            },
        )
        return crystal_view_id

    def check_mapped_payees(self, crystal_view_id) -> CheckPayeesType:
        """
        returns payees data if they present in published view.
        """
        current_view_payee_dict = PayeeCrystalAccessor(self.client_id).get_payees(
            crystal_view_ids=[crystal_view_id]
        )
        crystal_view_id = uuid.UUID(crystal_view_id)
        current_view_payees = current_view_payee_dict[crystal_view_id]

        can_publish = True
        already_added_payees = []
        total_added_user_count = len(current_view_payees)

        crystal_payees = self._get_payee_views_map(
            exclude_view_id=crystal_view_id, status=CrystalViewStatus.PUBLISHED.value
        )  # get payees from published views
        current_view_payees_details = get_employees_name(
            self.client_id, current_view_payees
        )

        for payee in current_view_payees_details:
            if payee["employee_email_id"] in crystal_payees:  # if payee has other views
                payee_details = {}
                can_publish = False
                payee_details["views_mapped"] = crystal_payees[
                    payee["employee_email_id"]
                ]
                payee_details.update(payee)
                already_added_payees.append(payee_details)

        data = {
            "can_publish": can_publish,
            "total_added_users_count": total_added_user_count,
            "already_added_payees": already_added_payees,
        }

        return data

    def publish_view(self, view_id, extra_data: AuditDataType):
        logger.info("BEGIN : publish_view")
        logger.debug(
            "publish_view params", extra={"view_id": view_id, "extra_data": extra_data}
        )
        view = self.admin_accessor.get_crystal_view(view_id)
        already_added_payees = self.check_mapped_payees(crystal_view_id=view_id)[
            "already_added_payees"
        ]
        payee_list = [payee["employee_email_id"] for payee in already_added_payees]
        pca = PayeeCrystalAccessor(self.client_id)
        pca.invalidate_payees(
            crystal_view_id=view_id,
            payee_list=payee_list,
            ked=extra_data["knowledge_begin_date"],
        )
        if len(view.settings_data) == 0:
            raise CrystalException(
                code="NO_TABLES_FOR_THIS_VIEW",
                payload={"crystal_view_name": view.crystal_view_name},
            )
        view_id = self.save_crystal_view(view_data={"crystal_view_id": view_id, "status": "published"}, extra_data=extra_data)  # type: ignore
        return view_id

    def delete_view(
        self, view_id, extra_data: AuditDataType, can_delete_published_view=False
    ):
        view = self.admin_accessor.get_crystal_view(view_id)
        if (
            view.status
            in (CrystalViewStatus.DRAFT.value, CrystalViewStatus.TRANSIENT_EDIT.value)
            or can_delete_published_view
        ):
            self.admin_accessor.invalidate_crystal_view(
                view_id, extra_data["knowledge_begin_date"]
            )
            pca = PayeeCrystalAccessor(self.client_id)
            pca.invalidate_all_payees(view_id, extra_data["knowledge_begin_date"])
        else:
            raise Exception(
                f"CANNOT_DELETE_PUBLISHED_VIEW - {view.crystal_view_name}"
            )  # can be handled in future
        logger.info("END : delete_view")

    def get_crystal_table_settings(
        self, payee, view_id, table_id
    ) -> CrystalTableSettingsData:
        """
        returns crystal table settings
        """
        logger.info(
            "BEGIN : get_crystal_table_settings",
            extra={"payee": payee, "view_id": view_id, "table_id": table_id},
        )
        if not self.admin_accessor.does_crystal_view_exists(view_id):
            raise CrystalException(
                code="NO_CRYSTAL_VIEW_DATA", payload={"crystal_view_id": view_id}
            )

        tables = self.admin_accessor.get_tables_for_crystal_view(view_id)
        table_settings = next(
            (table for table in tables if table["crystal_table_id"] == table_id), None
        )
        view = self.admin_accessor.get_crystal_view(view_id)
        if table_settings is None:
            raise CrystalException(
                code="NO_TABLES_FOR_THIS_VIEW",
                payload={"crystal_view_name": view.crystal_view_name},
            )
        logger.info("END : get_crystal_table_settings")
        logger.debug("data", extra={"table_settings": table_settings})
        return table_settings

    def upsert_table_to_crystal_view(
        self,
        crystal_view_id,
        crystal_table_settings: CrystalTableSettingsData,
        extra_data: AuditDataType = None,
    ) -> str:
        # returns list of crystal_tables_settings or empty list if no tables/objects created for that view
        logger.info("BEGIN : upsert_table_to_crystal_view")
        logger.debug(
            "upsert_table_to_crystal_view params",
            extra={
                "crystal_view_id": crystal_view_id,
                "crystal_table_settings": crystal_table_settings,
                "extra_data": extra_data,
            },
        )
        crystal_table_settings = dict(crystal_table_settings)  # type: ignore
        tables = self.admin_accessor.get_tables_for_crystal_view(crystal_view_id)
        is_update = "crystal_table_id" in crystal_table_settings
        table_id = None
        insert_table_index = len(tables)

        if not validate_datasheet_variables_in_crystal(
            self.client_id, crystal_table_settings
        ):
            raise CrystalException(code="CRYSTAL_VIEW_MISSING_DATASHEET_VARIABLE")

        if is_update:
            table_id = crystal_table_settings["crystal_table_id"]
            logger.debug("Update operation", extra={"table_id": table_id})
            insert_table_index, tables = self._remove_table_from_view(table_id, tables)
        else:
            # Adding a table to the crystal view for the first time
            table_id = uuid.uuid4()
            logger.debug("Adding table", extra={"table_id": table_id})
            crystal_table_settings.update({"crystal_table_id": table_id})

            display_condition_field = pydash.map_(
                crystal_table_settings["display_conditions"], "col_name"
            )
            success_action_field = pydash.map_(
                crystal_table_settings["success_actions"], "column_name"
            )
            analytics_data = {
                "user_id": extra_data["last_modified_by"],
                "event_name": SegmentEvents.CREATE_VIEW.value,
                "event_properties": {
                    SegmentProperties.VIEW_NAME.value: crystal_table_settings[
                        "crystal_table_name"
                    ],
                    SegmentProperties.DISPLAY_CONDITION_FIELD.value: display_condition_field,
                    SegmentProperties.COUNT_OF_DISPLAY_CONDITIONS.value: len(
                        display_condition_field
                    ),
                    SegmentProperties.SUCCESS_ACTION_FIELD.value: success_action_field,
                    SegmentProperties.COUNT_OF_SUCCESS_ACTION.value: len(
                        success_action_field
                    ),
                    SegmentProperties.DATE_FIELD_NAME.value: crystal_table_settings[
                        "date_field"
                    ],
                    SegmentProperties.ROW_NAME_FIELD.value: crystal_table_settings[
                        "row_name"
                    ],
                    SegmentProperties.COUNT_OF_COLUMNS.value: len(
                        crystal_table_settings["display_columns"]
                    ),
                    SegmentProperties.COUNT_OF_EDITABLE_COLUMN_NAMES.value: len(
                        crystal_table_settings["editable_columns"]
                    ),
                    SegmentProperties.DATE_OF_CREATION.value: timezone.now().strftime(
                        "%d-%m-%Y"
                    ),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)

        self._validate_table_data(crystal_table_settings, tables)
        # TODO: Ordering of tables might become important in future
        tables.insert(insert_table_index, crystal_table_settings)
        self.save_crystal_view(view_data={"crystal_view_id": crystal_view_id, "settings_data": tables}, extra_data=extra_data)  # type: ignore
        logger.info("END : upsert_table_to_crystal_view", extra={"table_id": table_id})
        return table_id  # type: ignore

    def delete_table_from_crystal_view(
        self, crystal_view_id, crystal_table_id, extra_data: AuditDataType = None
    ):
        logger.info("BEGIN : delete_table_from_crystal_view")
        logger.debug(
            "delete_table_from_crystal_view parmas",
            extra={
                "crystal_view_id": crystal_view_id,
                "crystal_table_id": crystal_table_id,
                "extra_data": extra_data,
            },
        )
        tables = self.admin_accessor.get_tables_for_crystal_view(crystal_view_id)
        view = self.admin_accessor.get_crystal_view(crystal_view_id)
        if not tables:
            raise CrystalException(
                code="NO_TABLES_FOR_THIS_VIEW",
                payload={"crystal_view_name": view.crystal_view_name},
            )

        _, tables = self._remove_table_from_view(crystal_table_id, tables)
        self.save_crystal_view(view_data={"crystal_view_id": crystal_view_id, "settings_data": tables}, extra_data=extra_data)  # type: ignore
        logger.info("END : delete_table_from_crystal_view")

    def get_raw_dataframe(
        self,
        login_user,
        source_type=None,
        databook_id=None,
        source_id=None,
        knowledge_date=None,
        filters=None,
        payee_field=None,
        payee_email_tuple=None,
        table_id=None,
    ):
        if filters == None:
            filters = []
        # databook_id = 'e2e10ca6-d219-4e72-a77a-6fcd26cbcb77'
        logger.info(
            "BEGIN : get_raw_data",
            extra={"source_id": source_id, "knowledge_date": knowledge_date},
        )
        knowledge_date = (
            timezone.now() if not knowledge_date else knowledge_date
        )  # when the custom object data is uploaded
        data = None
        df = pd.DataFrame()
        if source_type == "custom_object":
            data = get_co_qualifying_objects(
                client_id=self.client_id,
                object_id=source_id,
                knowledge_date=knowledge_date,
                filters=[],
            )
            data_list = []
            for record in data:
                data = record.data
                data[KEY_NAME_COLUMN] = record.row_key
                data_list.append(data)
            df = pd.DataFrame(data_list)
        elif source_type == "datasheet":
            # The source datasheet need not to be present
            # in the same databook as the current datasheet is present
            databook_id = get_databook_id_by_datasheet_id(
                client_id=self.client_id, datasheet_id=source_id
            )

            if is_datasheet_exists(self.client_id, databook_id, source_id):
                df = get_qualifying_objects(
                    client_id=self.client_id,
                    knowledge_date=knowledge_date,
                    databook_id=databook_id,
                    datasheet_id=source_id,
                    login_user=login_user,
                    filters=filters,
                    payee_field=payee_field,
                    payee_email_tuple=payee_email_tuple,
                )
            else:
                logger.info(
                    "Datasheet does not exist, datasheet_id - %s, crystal_table_id - %s",
                    source_id,
                    table_id,
                )
        logger.info("END : get_raw_data")
        logger.debug("data", extra={"raw_dataframe": df.head(10)})
        return df

    def set_accurate_column_datatype(self, df, source_type, source_id):
        if source_type == "custom_object":
            variables = all_variables_in_object(
                client_id=self.client_id, custom_object_ids=source_id
            )
        if source_type == "datasheet":
            variables = get_objects_by_datasheet_id(
                client_id=self.client_id, datasheet_id=source_id
            )

        # since we create the dataframe using co_data, it won't contain those variables for which the data
        # has not been uploaded, hence fetching all the vars from variable table and creating a col in df,
        # for the missing variables(variables_to_include)
        variables_in_data = df.columns
        all_variables = [var.system_name for var in variables]
        variables_to_include = list(set(all_variables) - set(variables_in_data))

        for var in variables_to_include:
            df[var] = None

        if not df.empty:
            df = set_accurate_column_types(variables, df)
        return df

    def update_success_actions(self, crystal_table_settings: CrystalTableSettingsData):
        """
        Modify success actions based on crystal table settings.
        Eg: For date field columns, translate label into relative date fields.
        """
        databook_id = (
            crystal_table_settings["databook_id"]
            if "databook_id" in crystal_table_settings
            else None
        )
        success_actions = crystal_table_settings["success_actions"]
        modified_success_actions = {}
        for success_action in success_actions:
            column_name = success_action["column_name"]
            success_value = success_action["value"]
            display_names_map = get_display_names_map(
                client_id=self.client_id,
                source_type=crystal_table_settings["source_type"],
                source_id=crystal_table_settings["source_id"],
                system_column_names=[column_name],
                databook_id=databook_id,
            )
            data_type = display_names_map[column_name][1] if display_names_map else None
            if data_type == "Date":
                if success_value == "current_date":
                    success_value = timezone.now().strftime("%d-%m-%Y")
                # commented out now because for date_fields there is a date picker in UI, self.get_dates only work if date field has string value.
                # else:
                #     success_value = self.interpret_relative_date_string(client_id=self.client_id, label=success_value)
            modified_success_actions[column_name] = success_value
        return modified_success_actions

    def get_crystal_view_success_actions(self, crystal_view_id):
        """
        Return success_actions keyed by source_id,
        {
            1 : {"co_1_deal_status": "Closed Won", "co_1_invoice_id": 123_312312},
            2 : {"co_2_status": "Closed Won"}
        }
        """
        logger.info(
            "BEGIN : get_crystal_view_success_actions",
            extra={"crystal_view_id": crystal_view_id},
        )
        view_data = self.admin_accessor.get_crystal_view(crystal_view_id)
        settings_data_list = view_data.settings_data
        if not settings_data_list:
            raise CrystalException(
                code="NO_CRYSTAL_VIEW_DATA",
                payload={"crystal_view_name": view_data.crystal_view_name},
            )
        source_id_success_actions_map = {}
        for settings_data in settings_data_list:
            modified_success_actions = self.update_success_actions(settings_data)
            source_id_success_actions_map[settings_data["source_id"]] = (
                modified_success_actions
            )
        logger.info("END : get_crystal_view_success_actions")
        logger.debug(
            "data",
            extra={"source_id_success_actions_map": source_id_success_actions_map},
        )
        return source_id_success_actions_map

    def get_payees_info(
        self, login_user, crystal_view_id=None, is_payee=False, **kwargs
    ) -> List[PayeeDetails]:
        """
        returns payee details in the following structure -> List[PayeeDetails]
        """
        logger.info(
            "BEGIN: Get Payees Info user - %s , crsytal_view_id - %s, is_payee - %s",
            str(login_user),
            str(crystal_view_id),
            str(is_payee),
        )
        payees = self.get_payees(login_user, crystal_view_id=crystal_view_id)
        epa = get_all_valid_employees_for_date(client_id=self.client_id, as_dicts=True)

        revenue_payees = {}
        for emp_payroll in epa:
            revenue_payees[emp_payroll["employee_email_id"]] = emp_payroll["payee_role"]
        payee_list = []
        employee_email_ids = []
        for payee in payees:
            employee_email_ids.append(payee.employee_email_id)
        logger.debug(
            "Populated employee email ids",
            extra={"employee_email_ids": employee_email_ids},
        )

        employee_plans = get_all_employee_plans(
            client_id=self.client_id, employee_email_ids=employee_email_ids
        )
        comm_plan_mapped_payees = set()
        for entry in employee_plans:
            comm_plan_mapped_payees.add(entry["employee_email_id"])
        logger.debug(
            "Populated commission plan mapped payees",
            extra={"comm_plan_mapped_payees": comm_plan_mapped_payees},
        )
        if is_payee:
            search_term = kwargs.get("search_term", "")
            start_index = kwargs.get("start", 0)
            end_index = kwargs.get("end", 100)
            for payee in payees:
                if (
                    login_user == payee.employee_email_id
                    and self.contains_search_term(payee, search_term)
                ) or (
                    payee.employee_email_id in comm_plan_mapped_payees
                    and payee.employee_email_id in revenue_payees
                    and self.contains_search_term(payee, search_term)
                ):
                    payee_details = {
                        "email": payee.employee_email_id,
                        "name": payee.first_name + " " + payee.last_name,
                    }
                    payee_list.append(payee_details)
            payee_list = sorted(payee_list, key=lambda k: k["name"])
            payee_list = payee_list[start_index:end_index]
            logger.info("END : get_payees_info")
            logger.debug("data", extra={"payee_list": payee_list})
            return payee_list
        else:
            crystal_payees = self._get_payee_views_map()  # get all view's payees
            for payee in payees:
                payee_details = {
                    "first_name": payee.first_name,
                    "last_name": payee.last_name,
                    "email": payee.employee_email_id,
                    "profile_picture": payee.profile_picture,
                    "crystal_mapping": crystal_payees.get(payee.employee_email_id, []),
                    "has_commission_plan": payee.employee_email_id
                    in comm_plan_mapped_payees,
                    "is_revenue": payee.employee_email_id in revenue_payees
                    and revenue_payees[payee.employee_email_id] == "Revenue",
                }
                payee_list.append(payee_details)
        logger.info("END : get_payees_info")
        logger.debug("data", extra={"payee_list": payee_list})
        return payee_list

    def _get_payee_views_map(self, exclude_view_id="", status="all"):
        """
        status = "all" -> returns all views with mapped payees
        status = "True" -> returns draft views with mapped payees
        status = "False" -> returns published views with mapped payees
        exclude_view_id = "0f6ef312-a38b-498b-b345-7acf02987144" -> exclude this view and return all other views

        return {
            "payee1": ["crystal view 1", "crystal view 2"],
            "payee2": ["crystal view 2"]
        }
        """
        pca = PayeeCrystalAccessor(self.client_id).client_kd_aware()
        projection = ["crystal_view_id", "crystal_view_name", "status"]
        crystal_admin_details = CrystalAdminAccessor(
            self.client_id
        ).get_crystal_view_details(projection=projection)
        crystal_admin_settings_map = {}
        for crystal_view in crystal_admin_details:
            crystal_admin_settings_map[crystal_view[0]] = {
                "view_id": crystal_view[0],
                "view_name": crystal_view[1],
                "status": crystal_view[2],
            }
        crystal_payees = {}
        for crystal_payee in pca.iterator():
            if (
                crystal_payee.crystal_view_id != exclude_view_id
            ):  # exclude the given view id
                view = crystal_admin_settings_map[crystal_payee.crystal_view_id]

                if status in ("all", view["status"]):
                    if crystal_payee.payee not in crystal_payees:
                        crystal_payees[crystal_payee.payee] = [view]
                    else:
                        crystal_payees[crystal_payee.payee].append(view)
        return crystal_payees

    def get_payee_views_map(self, exclude_view_id="", status="all"):
        """
        Get a mapping of payees to their crystal views.

        Args:
            exclude_view_id: ID of the view to exclude from the results
            status: Filter views by status ("all", "True" for draft, "False" for published)

        Returns:
            Dictionary mapping payee emails to their crystal views:
            {
                "payee1": ["crystal view 1", "crystal view 2"],
                "payee2": ["crystal view 2"]
            }
        """
        return self._get_payee_views_map(exclude_view_id=exclude_view_id, status=status)

    def _get_all_custom_object_variables(self, data_types, custom_object_ids=None):
        custom_objects = get_object_by_ids(
            client_id=self.client_id, custom_object_ids=custom_object_ids, as_dicts=True
        )
        custom_object_ids = [co["custom_object_id"] for co in custom_objects]

        all_variables = all_variables_in_object(
            client_id=self.client_id, custom_object_ids=custom_object_ids, as_dicts=True
        )
        data_types_dict = dict((d.id, d.data_type) for d in data_types)
        variables_grouped_by_co_id = pydash.group_by(
            all_variables, lambda x: x["custom_object_id"]
        )
        all_source_variables = []
        for custom_object in custom_objects:
            object_variables = variables_grouped_by_co_id[
                custom_object["custom_object_id"]
            ]
            custom_object_variables = []
            for object_variable in object_variables:
                variable = pydash.pick(
                    object_variable, ["system_name", "display_name", "data_type_id"]
                )
                variable["data_type"] = data_types_dict[variable["data_type_id"]]
                custom_object_variables.append(variable)

            current_custom_object_variables = {
                "source_type": "custom_object",
                "id": custom_object["custom_object_id"],
                "name": custom_object["name"],
                "variables": custom_object_variables,
            }

            all_source_variables.append(current_custom_object_variables)
        return all_source_variables

    def _get_all_datasheet_variables(self, data_types, datasheet_ids=None):
        datasheets = get_datasheets_by_id(
            client_id=self.client_id, datasheet_ids=datasheet_ids, as_dicts=True
        )
        fetched_datasheet_ids = [ds["datasheet_id"] for ds in datasheets]
        fetched_databook_ids = [ds["databook_id"] for ds in datasheets]
        fetched_databook_ids = list(set(fetched_databook_ids))
        databook_id_name_map = {}
        databooks = get_databook_by_ids(
            client_id=self.client_id, databook_ids=fetched_databook_ids, as_dicts=True
        )
        for databook in databooks:
            databook_id_name_map[databook["databook_id"]] = databook["name"]
        all_variables = get_variables_for_ds_ids(
            client_id=self.client_id, datasheet_ids=fetched_datasheet_ids, as_dicts=True
        )
        data_types_dict = dict((d.id, d.data_type) for d in data_types)
        variables_grouped_by_ds_id = pydash.group_by(
            all_variables, lambda x: x["datasheet_id"]
        )
        all_source_variables = []
        for datasheet in datasheets:
            ds_variables = variables_grouped_by_ds_id[datasheet["datasheet_id"]]
            datasheet_variables = []
            for ds_variable in ds_variables:
                variable = pydash.pick(
                    ds_variable,
                    ["system_name", "display_name", "data_type_id", "meta_data"],
                )
                variable["data_type"] = data_types_dict[variable["data_type_id"]]
                datasheet_variables.append(variable)
            current_datasheet_variables = {
                "source_type": "datasheet",
                "databook_name": databook_id_name_map[datasheet["databook_id"]],
                "databook_id": datasheet["databook_id"],
                "id": datasheet["datasheet_id"],
                "name": datasheet["name"],
                "variables": datasheet_variables,
            }
            all_source_variables.append(current_datasheet_variables)
        return all_source_variables

    def get_source_meta_data(self, custom_object_ids=None, datasheet_ids=None):
        """
        Returns all custom_object and datasheet variables
        """
        logger.info(
            "BEGIN : get_source_meta_data",
            extra={
                "custom_object_ids": custom_object_ids,
                "datasheet_ids": datasheet_ids,
            },
        )

        data_types = get_all_data_types()

        all_co_variables = self._get_all_custom_object_variables(
            data_types, custom_object_ids=custom_object_ids
        )
        all_ds_variables = self._get_all_datasheet_variables(
            data_types, datasheet_ids=datasheet_ids
        )
        all_source_variables = [*all_co_variables, *all_ds_variables]

        logger.info("END : get_source_object_meta_data")
        logger.debug("data", extra={"all_source_variables": all_source_variables})
        return all_source_variables

    def get_payee_reportees(self, payee, knowledge_date, is_manager_rollup_ed):
        """
        Returns all reportees of a payee.
        Returns empty list if payee is not a manager.
        """
        if knowledge_date is None:
            knowledge_date = timezone.now()

        period_start_date, period_end_date = get_psd_ped_for_payee(
            client_id=self.client_id,
            knowledge_date=knowledge_date,
            payee_email=payee,
        )
        team_name = "& team"
        team_members = get_team_members(
            self.client_id, period_start_date, period_end_date, payee, team_name
        )
        if is_manager_rollup_ed:
            team_members_start = get_team_members(
                self.client_id,
                period_start_date,
                period_start_date,
                payee,
                team_name,
            )
            team_members = list(set(team_members).union(team_members_start))

        return team_members

    def get_payees(self, login_user_id, crystal_view_id=None):
        """
        Depending upon the role of the logged-in user, it returns -
        For admin: All payees
        For payee manager: All payees under the manager
        For payee: Return payee
        """
        logger.info("BEGIN : get_payees", extra={"login_user_id": login_user_id})
        as_of_date = timezone.now()
        pca = PayeeCrystalAccessor(self.client_id)
        data_permission_payout = get_data_permission(
            self.client_id, login_user_id, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        data_permission_quota = get_data_permission(
            self.client_id, login_user_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if (
            data_permission_payout
            and data_permission_payout["type"] == RBAC.ALL_DATA.value
            or data_permission_quota
            and data_permission_quota["type"] == RBAC.ALL_DATA.value
        ):
            if crystal_view_id is None:
                data = get_all_employees(
                    client_id=self.client_id, as_of_date=as_of_date
                )
            else:
                payees_dict = pca.get_payees(crystal_view_ids=[crystal_view_id])
                data = get_all_employees(
                    client_id=self.client_id,
                    as_of_date=as_of_date,
                    email_list=payees_dict[uuid.UUID(crystal_view_id)],
                )
        elif (
            data_permission_payout
            and data_permission_payout["type"] != RBAC.ALL_DATA.value
            or data_permission_quota
            and data_permission_quota["type"] != RBAC.ALL_DATA.value
        ):
            payee_email = get_valid_payee_emails(
                self.client_id, login_user_id, data_permission_payout
            ) + get_valid_payee_emails(
                self.client_id, login_user_id, data_permission_quota
            )
            data = get_all_employees(
                client_id=self.client_id,
                as_of_date=as_of_date,
                email_list=list(set(payee_email)),
            )
        else:
            data = []
        logger.info("END : get_payees")
        logger.debug("data", extra={"data": data})
        return data

    def clone_view(self, crystal_view_id, extra_data=None, clone_published_view=False):
        """
        returns cloned crystal view
        """
        new_crystal_view_id = uuid.uuid4()
        view = self.admin_accessor.get_crystal_view(crystal_view_id)
        view.temporal_id = None
        view.crystal_view_id = new_crystal_view_id
        view.source_view_id = crystal_view_id
        if clone_published_view:
            if view.status == CrystalViewStatus.DRAFT.value:
                raise Exception("CANNOT_CLONE_DRAFT_VIEW")
            view.status = CrystalViewStatus.TRANSIENT_EDIT.value
        else:
            view.crystal_view_name = view.crystal_view_name + "_copy"
            view.status = CrystalViewStatus.DRAFT.value
        view.save()
        payee_dict = PayeeCrystalAccessor(self.client_id).get_payees(
            crystal_view_ids=[crystal_view_id]
        )
        crystal_view_id = uuid.UUID(crystal_view_id)
        payees = payee_dict[crystal_view_id]
        self.map_crystal_view_to_payees(
            payees=payees, crystal_view_id=new_crystal_view_id, extra_data=extra_data
        )
        return new_crystal_view_id

    def update_cloned_view(
        self, crystal_view_id, discard=False, extra_data: AuditDataType = None
    ):
        """
        invalidates source view and updates cloned view status to "published"
        """
        cloned_view = self.admin_accessor.get_crystal_view(crystal_view_id)
        if cloned_view.status != CrystalViewStatus.TRANSIENT_EDIT.value:
            raise Exception("CANNOT_UPDATE_THIS_VIEW")
        source_view_id = cloned_view.source_view_id
        if discard is False:  # update cloned view
            self.delete_view(
                view_id=source_view_id,
                extra_data=extra_data,
                can_delete_published_view=True,
            )
            cloned_view.status = (
                CrystalViewStatus.DRAFT.value
                if cloned_view.settings_data == []
                else CrystalViewStatus.PUBLISHED.value
            )
            cloned_view.save()
            return crystal_view_id

        self.delete_view(view_id=crystal_view_id, extra_data=extra_data)
        return source_view_id

    def _validate_view_name(self, view_data: CrystalViewType):
        """
        This function will be used for create_view and edit_view
        """
        logger.info("BEGIN : _validate_view_name", extra={"view_data": view_data})
        view_id = view_data["crystal_view_id"]
        view_name = view_data.get("crystal_view_name", None)

        if view_name is None:
            raise CrystalException(code="CRYSTAL_VIEW_NAME_IS_NOT_PRESENT")
        if self.admin_accessor.does_crystal_view_name_exist(view_name, view_id):
            raise CrystalException(
                code="CRYSTAL_VIEW_NAME_ALREADY_EXISTS",
                payload={"crystal_view_name": view_name},
            )
        logger.info("END : _validate_view_name")

    def _remove_success_action_columns_from_editable_columns(
        self, crystal_table_settings: CrystalTableSettingsData
    ):
        # removing success actions columns from editable columns.
        success_actions = crystal_table_settings["success_actions"]
        editable_columns = crystal_table_settings["editable_columns"]
        if len(editable_columns) > 0:
            for action in success_actions:
                column_to_be_removed = action["column_name"]
                if column_to_be_removed in editable_columns:
                    editable_columns.remove(column_to_be_removed)
            crystal_table_settings["editable_columns"] = editable_columns

    def _validate_table_data(
        self,
        crystal_table_settings: CrystalTableSettingsData,
        tables: List[CrystalTableSettingsData],
    ):
        table_name = crystal_table_settings["crystal_table_name"]
        salesforce_object = crystal_table_settings.get("salesforce_object")
        if salesforce_object is not None:
            logger.info(
                "BEGIN : _validate_table_data",
                extra={"salesforce_object": salesforce_object},
            )
            if any(
                table["is_estimator"]
                and table.get("salesforce_object", "") == salesforce_object
                for table in tables
            ):
                raise CrystalException(
                    code="SALESFORCE_OBJECT_ALREADY_EXISTS",
                    payload={"salesforce_object": salesforce_object},
                )

        logger.info(
            "BEGIN : _validate_table_data",
            extra={"table_name": table_name, "tables": tables},
        )
        if any(table["crystal_table_name"] == table_name for table in tables):
            raise CrystalException(
                code="CRYSTAL_TABLE_NAME_ALREADY_EXISTS",
                payload={"crystal_table_name": table_name},
            )

        self._remove_success_action_columns_from_editable_columns(
            crystal_table_settings
        )
        logger.info("END : _validate_table_data")

    def _remove_table_from_view(self, table_id, tables):
        """
        Returns whether the table to remove exists as well as the updated list of tables as a tuple
        """
        logger.info(
            "BEGIN : _remove_table_from_view",
            extra={"table_id": table_id, "tables": tables},
        )
        crystal_table_id_to_remove = table_id
        updated_crystal_tables = list(
            filter(
                lambda x: x["crystal_table_id"] != crystal_table_id_to_remove, tables
            )
        )

        if len(tables) == len(updated_crystal_tables):
            raise CrystalException(
                code="NO_CRYSTAL_TABLE_DATA", payload={"crystal_table_id": table_id}
            )

        # Find the index of the removed table
        removed_table_index = None
        for i, table in enumerate(tables):
            if table["crystal_table_id"] == table_id:
                removed_table_index = i
                break

        logger.info(
            "END : _remove_table_from_view",
            extra={"updated_crystal_tables": updated_crystal_tables},
        )
        return removed_table_index, updated_crystal_tables

    def _populate_view_with_payee_info(self, view, payees):
        logger.info(
            "BEGIN : _populate_view_with_payee_info",
            extra={"view": view, "payees": payees},
        )
        payees_info = get_employees_name_profile_picture(self.client_id, payees)
        for payee_info in payees_info:
            # renaming key name from employee_email_id to email_id
            payee_info["email_id"] = payee_info.pop("employee_email_id")

        fields = (
            "crystal_view_id",
            "crystal_view_name",
            "crystal_view_description",
            "status",
            "settings_data",
            "last_modified_at",
            "last_modified_by",
        )
        serializer = CrystalViewModelSerializer(view, fields=fields)
        view_data = serializer.data

        last_modified_user_list = []
        last_modified_user_list.append(view_data["last_modified_by"])
        last_modified_by_info = get_employees_name(
            self.client_id, last_modified_user_list
        )
        # Default to not deleted
        is_deleted = False
        if not last_modified_by_info or last_modified_by_info[0] is None:
            # Try to get exited user info (simulate ms_teams/slack pattern)
            from spm.accessors.employee_accessor_v2 import EmployeeReadAccessor

            employee_accessor = EmployeeReadAccessor(self.client_id)
            deleted_user = employee_accessor.get_active_or_invalidated_employee(
                email_id=last_modified_user_list[0],
                projection=[
                    "employee_email_id",
                    "first_name",
                    "last_name",
                ],
            )
            if deleted_user:
                # Compose info for exited user
                last_modified_by_info = [
                    {
                        "email_id": deleted_user["employee_email_id"],
                        "first_name": deleted_user.get("first_name"),
                        "last_name": deleted_user.get("last_name"),
                    }
                ]
                is_deleted = True
            else:
                # No info at all
                last_modified_by_info = [
                    {
                        "email_id": None,
                        "first_name": None,
                        "last_name": None,
                    }
                ]
                is_deleted = True
        else:
            last_modified_by_info[0]["email_id"] = last_modified_by_info[0].pop(
                "employee_email_id"
            )
            is_deleted = False

        view_data.update(
            {
                "payees": payees_info,
                "last_modified_by": (
                    last_modified_by_info[0] if last_modified_by_info else None
                ),
                "is_deleted": is_deleted,
            }
        )
        logger.info("END : _populate_view_with_payee_info")
        logger.debug("data", extra={"view_data": view_data})
        return view_data

    def get_published_view_id(self, crystal_view_ids):
        return self.admin_accessor.get_published_view_id(crystal_view_ids)

    def contains_search_term(self, payee, search_term):
        """
        Returns true if search_term is present in payee's name or email
        """
        full_name = payee.first_name + " " + payee.last_name
        if search_term is None:
            return True
        search_term = search_term.strip().lower()
        if (
            full_name.startswith(search_term)
            or payee.employee_email_id.startswith(search_term)
            or payee.last_name.startswith(search_term)
        ):
            return True
        return False

    def get_future_periods(self, payee, time_zone, curr_date):
        """
        Returns future periods for a payee
        """
        logger.info("BEGIN : get_future_periods")
        employee_payroll_acc = EmployeePayrollAccessor(self.client_id)

        emp_payroll = employee_payroll_acc.get_current_valid_employee_payroll(
            curr_date, [payee]
        )

        emp_payroll = emp_payroll[0] if len(emp_payroll) > 0 else None

        if emp_payroll is None:
            raise CrystalException(code="NO_PAYROLL_DATA")

        payout_frequency = emp_payroll["payout_frequency"].lower()  # type: ignore

        start_month = get_client_fiscal_start_month(self.client_id)

        if payout_frequency not in static_frequencies:
            formatted_periods = self.get_future_custom_calendar_periods(
                payout_frequency, time_zone, curr_date
            )
        else:
            future_periods = get_future_periods_for_static_frequencies(
                client_id=self.client_id,
                curr_date=curr_date,
                start_month=start_month,
                time_prd=payout_frequency,
            )

            comm_periods = [
                (payout_frequency, period["start_date"], period["end_date"])
                for period in future_periods
            ]

            period_label_map = get_period_label_map(self.client_id, comm_periods)

            # Format the periods as required
            formatted_periods = [
                {
                    "label": period_label_map[
                        (payout_frequency, period["start_date"], period["end_date"])
                    ],
                    "value": f"{period['start_date'].strftime('%Y-%m-%d')} <-> {period['end_date'].strftime('%Y-%m-%d')}",
                }
                for period in future_periods
            ]

        if not formatted_periods:
            raise CrystalException(code="NO_PERIODS")

        logger.info("END : get_future_periods")
        return formatted_periods

    def get_future_custom_calendar_periods(
        self, payout_frequency, time_zone, curr_date
    ):
        """
        Returns future periods for a payee for custom calendar payout frequency
        """
        number_of_future_periods = get_crystal_custom_calendar_future_periods(
            self.client_id
        )
        custom_periods = get_custom_periods(self.client_id, payout_frequency)

        # Formatting the periods
        # - Parse period end date to given timezone and compare with current date
        # - Filter out past periods.
        # - Limit the number of future periods.

        formatted_periods = [
            {
                "label": period["period_label"],
                "value": f"{period['period_start_date'].strftime('%Y-%m-%d')} <-> {period['period_end_date'].strftime('%Y-%m-%d')}",
            }
            for period in custom_periods["periods"]
            if period["period_end_date"].replace(tzinfo=pytz.timezone(time_zone))
            >= curr_date
        ]
        formatted_periods = formatted_periods[:number_of_future_periods]

        return formatted_periods

    def apply_payee_filters(
        self, raw_sql_query: str, filters: dict
    ) -> tuple[str, dict]:
        """
        Apply filters to the SQL query for payee filtering.

        Args:
            raw_sql_query: The base SQL query
            filters: Dictionary containing filter values for different categories

        Returns:
            Tuple of (modified SQL query, parameters dictionary)
        """
        params = {}

        if filters.get("user_groups") is not None and len(filters["user_groups"]) > 0:
            params["user_groups"] = tuple(filters["user_groups"])
            raw_sql_query += """
            and exists (
                select 1 from user_group_members ugm
                where ugm.employee_email_id = e.employee_email_id
                and ugm.user_group_id in %(user_groups)s
                and ugm.knowledge_end_date is null
                and not ugm.is_deleted
                and ugm.client_id = %(client_id)s
            )"""

        if filters.get("teams") is not None and len(filters["teams"]) > 0:
            params["teams"] = tuple(filters["teams"])
            raw_sql_query += """
            and exists (
                select 1 from membership m
                where m.group_member_email_id = e.employee_email_id
                and m.team_id in %(teams)s
                and m.knowledge_end_date is null
                and not m.is_deleted
                and m.client_id = %(client_id)s
                and (
                    m.effective_start_date <= %(curr_date)s
                    and (
                        m.effective_end_date is null
                        or m.effective_end_date > %(curr_date)s
                    )
                )
            )"""

        if filters.get("plans") is not None and len(filters["plans"]) > 0:
            params["plans"] = tuple(filters["plans"])
            raw_sql_query += """
            and exists (
                select 1 from plan_details pp
                where pp.employee_email_id = e.employee_email_id
                and pp.plan_id in %(plans)s
                and pp.knowledge_end_date is null
                and not pp.is_deleted
                and pp.client_id = %(client_id)s
                and (
                    pp.effective_start_date <= %(curr_date)s
                    and (
                        pp.effective_end_date is null
                        or pp.effective_end_date > %(curr_date)s
                    )
                )
            )"""

        return raw_sql_query, params

    def _get_crystal_payees_sql(self, select_columns: str) -> str:
        """Return the base SQL used to fetch Crystal-eligible payees.

        Pass the desired SELECT clause via *select_columns* so callers can
        retrieve either the full employee details or only distinct email ids
        while sharing the common eligibility criteria.
        """
        return f"""
        SELECT {select_columns}
        FROM employee e
        JOIN employee_payroll_details epd ON epd.employee_email_id = e.employee_email_id
        JOIN (
            SELECT DISTINCT employee_email_id
            FROM plan_details
            WHERE knowledge_end_date IS NULL
              AND NOT is_deleted
              AND client_id = %(client_id)s
              AND (
                  effective_start_date <= %(curr_date)s
                  AND (
                      effective_end_date IS NULL
                      OR effective_end_date > %(curr_date)s
                  )
              )
        ) pd ON pd.employee_email_id = e.employee_email_id
        WHERE
            e.knowledge_end_date IS NULL
            AND NOT e.is_deleted
            AND e.client_id = %(client_id)s
            AND epd.knowledge_end_date IS NULL
            AND NOT epd.is_deleted
            AND epd.client_id = %(client_id)s
            AND epd.payee_role = 'Revenue'
            AND (
                epd.effective_start_date <= %(curr_date)s
                AND (
                    epd.effective_end_date IS NULL
                    OR epd.effective_end_date > %(curr_date)s
                )
            )
        """

    def get_eligible_crystal_payees(self) -> List[str]:
        """
        Returns a list of payee email ids that are eligible to be mapped to Crystal views.

        Eligibility criteria:
            1. Payee has an active employee payroll record with payee_role = 'Revenue'.
            2. Payee is mapped to at least one active commission plan.
            3. Temporal conditions (knowledge/effective dates) are valid for the current date.

        """
        logger.info("BEGIN : get_eligible_crystal_payees")
        raw_sql_query = self._get_crystal_payees_sql("DISTINCT e.employee_email_id")
        params = {
            "client_id": self.client_id,
            "curr_date": timezone.now(),
        }
        with connection.cursor() as cursor:
            cursor.execute(raw_sql_query, params)
            rows = cursor.fetchall()

        eligible_payees = [row[0] for row in rows] if rows else []
        logger.info("END : get_eligible_crystal_payees")
        return eligible_payees

    def get_separated_payees_info(
        self,
        login_user,
        crystal_view_id=None,
        search_term=None,
        offset_value=None,
        limit_value=None,
        filters=None,
    ):
        """
        Get payees separated into current payees (mapped to crystal view) and available payees (with active commission plans but not mapped)

        Args:
            login_user: The logged in user's email
            crystal_view_id: ID of the crystal view
            search_term: Search term to filter payees
            offset_value: Starting index for pagination
            limit_value: Number of records to return
            filters: Dictionary containing filter values for different categories

        Returns:
            Dictionary containing:
            - current_payees: List of payees currently mapped to the crystal view
            - available_payees: List of payees with active commission plans but not mapped
        """
        logger.info("BEGIN : get_separated_payees_info")
        logger.debug(
            "data",
            extra={
                "login_user": login_user,
                "crystal_view_id": crystal_view_id,
                "search_term": search_term,
                "offset_value": offset_value,
                "limit_value": limit_value,
                "filters": filters,
            },
        )

        # Base query to get all valid employees with commission plans
        raw_sql_query = self._get_crystal_payees_sql(
            "e.employee_email_id, e.first_name, e.last_name, e.profile_picture"
        )

        params = {"client_id": self.client_id}

        # Add current date to params
        params["curr_date"] = timezone.now()

        # Apply all filters if provided
        if filters:
            # Add designation filter
            if filters.get("designations"):
                params["designations"] = tuple(filters["designations"])
                raw_sql_query += """
                AND epd.designation IN %(designations)s"""

            # Apply remaining filters (user_groups, plans and teams)
            raw_sql_query, filter_params = self.apply_payee_filters(
                raw_sql_query, filters
            )
            params.update(filter_params)

        # Add search term filter if provided
        if search_term:
            raw_sql_query += """
            AND (
                concat(e.first_name, ' ', e.last_name) ilike %(search_term)s
                OR e.employee_email_id ilike %(search_term)s
            )"""
            params["search_term"] = f"%{search_term}%"

        # Add ORDER BY clause
        raw_sql_query += """
        ORDER BY e.first_name ASC, e.last_name ASC"""

        # Add pagination parameters if both are provided
        if limit_value is not None and offset_value is not None:
            params["limit_value"] = int(limit_value)
            params["offset_value"] = int(offset_value)
            raw_sql_query += """
            LIMIT %(limit_value)s OFFSET %(offset_value)s"""

        # Execute query to get all filtered employees
        with connection.cursor() as cursor:
            cursor.execute(raw_sql_query, params)
            all_employees = cursor.fetchall()

        # Convert to list of dictionaries
        employee_list = []
        if cursor.description and len(all_employees) > 0:
            cols = [desc[0] for desc in cursor.description]
            for row in all_employees:
                employee_dict = dict(zip(cols, row))
                employee_list.append(employee_dict)

        # Get current payees mapped to the crystal view
        current_payees = []
        available_payees = []
        if crystal_view_id:
            payee_dict = PayeeCrystalAccessor(self.client_id).get_payees(
                crystal_view_ids=[crystal_view_id]
            )
            current_payee_emails = payee_dict.get(uuid.UUID(crystal_view_id), [])
        else:
            current_payee_emails = []

        # Get crystal view mappings for all payees
        crystal_payees = self._get_payee_views_map()

        # Separate payees into current and available
        for employee in employee_list:
            payee_email = employee["employee_email_id"]

            payee_details = {
                "first_name": employee["first_name"],
                "last_name": employee["last_name"],
                "email": payee_email,
                "profile_picture": employee["profile_picture"],
            }

            if payee_email in current_payee_emails:
                payee_details["crystal_mapping"] = crystal_payees.get(payee_email, [])
                current_payees.append(payee_details)
            else:
                available_payees.append(payee_details)

        result = {
            "current_payees": current_payees,
            "available_payees": available_payees,
            "total_current_payees": len(current_payees),
            "total_available_payees": len(available_payees),
        }

        logger.info("END : get_separated_payees_info")
        logger.debug("data", extra={"result": result})
        return result
