# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test

import json

import graphene
from dateutil.parser import parse
from graphene_django.types import DjangoObjectType

from commission_engine.accessors import client_accessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    CommissionETLStatusReaderAccessor,
    DatabookETLStatusReaderAccessor,
    ETLSyncStatusReaderAccessor,
    ForecastETLStatusReaderAccessor,
    PayoutSnapshotETLStatusReaderAccessor,
    ReportETLStatusReaderAccessor,
    SettlementETLStatusReaderAccessor,
    SettlementSnapshotETLStatusReaderAccessor,
    UpstreamCsvETLStatusReaderAccessor,
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.models import ETLSyncStatus
from commission_engine.models.etl_housekeeping_models import UpstreamETLStatus
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.etl_sync_status_service import (
    get_recent_data_sync_status,
    get_recent_datasheet_databook_sync_status,
)
from commission_engine.services.task_tracker_service import (
    get_conclusive_status_for_wrapper_type,
    get_detailed_sync_status,
    get_e2e_detailed_status,
    get_failed_payee_period,
)
from commission_engine.utils.date_utils import first_day_of_month, last_day_of_month
from commission_engine.utils.general_data import (
    ETL_ACTIVITY,
    ETL_STATUS,
    RbacPermissions,
)
from everstage_ddd.upstream import FivetranSyncLogAccessor
from everstage_ddd.upstream.extraction.utils import get_upstream_timestamps
from interstage_project.auth_utils import permission_required
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor

from .mutations import UpdateUpstreamEtlStatus


def conditional_view_sync_details_permission():
    """
    Decorator factory that checks client feature 'enable_view_sync_details_permission' at runtime:
    - If True (or not present - default): requires VIEW_SYNC_DETAILS permission
    - If False: requires MANAGE_ALLADMINS permission

    This is a decorator factory pattern that creates the appropriate decorator based on
    runtime evaluation of client features (necessary for GraphQL resolvers where
    client_id is only available at request time).

    Usage:
        @conditional_view_sync_details_permission()
        def resolve_something(self, info, **kwargs):
            # resolver logic here
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get client_id from GraphQL info context
            info = args[1]  # args[0] is self, args[1] is info
            client_id = info.context.client_id

            # Get client features at runtime (necessary for GraphQL context)
            client_features = client_accessor.get_client_features(client_id) or {}

            # Check the feature flag - default to True if not present
            enable_view_sync_details_permission = client_features.get(
                "enable_view_sync_details_permission", True
            )

            # CPQ settings permission is required to authenticate view sync access for all CPQ users
            required_permission = [RbacPermissions.MANAGE_CPQ_SETTINGS.value]

            # Determine required permission based on feature flag for ICM users
            if enable_view_sync_details_permission:
                required_permission.append(RbacPermissions.VIEW_SYNC_DETAILS.value)
            else:
                required_permission.append(RbacPermissions.MANAGE_ALLADMINS.value)

            # Apply permission check directly instead of creating new decorator
            return permission_required(required_permission)(func)(*args, **kwargs)

        return wrapper

    return decorator


class SyncRunLogQuery:
    sync_run_logs = graphene.Field(
        graphene.JSONString, e2e_sync_run_id=graphene.String(), task=graphene.String()
    )

    @conditional_view_sync_details_permission()
    def resolve_sync_run_logs(self, info, e2e_sync_run_id, task):
        client_id = info.context.client_id

        sync_run_log = []

        if task in [
            ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
            ETL_ACTIVITY.CONNECTOR_UPSTREAM_SYNC.value,
        ]:
            sync_run_log += (
                UpstreamETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

            sync_run_log += (
                FivetranSyncLogAccessor(
                    client_id=client_id,
                    e2e_sync_run_id=e2e_sync_run_id,
                )
                .get_records_by_e2e_sync_id_and_status(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

            sync_run_log += (
                UpstreamCsvETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )
        if task in [
            ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
            ETL_ACTIVITY.REFRESH_DATABOOK.value,
            ETL_ACTIVITY.COMMISSION_CALCULATION.value,
            ETL_ACTIVITY.SETTLEMENT_CALCULATION.value,
            ETL_ACTIVITY.FORECAST_CALCULATION.value,
            "GenerateDatasheetData",
        ]:
            sync_run_log += (
                DatabookETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

        if task in [
            ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
            ETL_ACTIVITY.COMMISSION_CALCULATION.value,
            ETL_ACTIVITY.SETTLEMENT_CALCULATION.value,
        ]:
            sync_run_log += (
                CommissionETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

            sync_run_log += (
                SettlementETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

            sync_run_log += (
                PayoutSnapshotETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

            sync_run_log += (
                SettlementSnapshotETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

        if task == ETL_ACTIVITY.FORECAST_CALCULATION.value:
            sync_run_log += (
                ForecastETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

        if (
            task
            in [
                ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
                ETL_ACTIVITY.REFRESH_DATABOOK.value,
                ETL_ACTIVITY.REPORT_ETL.value,
                ETL_ACTIVITY.COMMISSION_CALCULATION.value,
                ETL_ACTIVITY.SETTLEMENT_CALCULATION.value,
                ETL_ACTIVITY.FORECAST_CALCULATION.value,
            ]
            or ETL_ACTIVITY.REPORT_ETL.value in task
        ):
            sync_run_log += (
                ReportETLStatusReaderAccessor(client_id=client_id)
                .get_records_by_e2e_sync_run_id(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_run_log", flat=True)
            )

        sync_run_log = [srl for srl in sync_run_log if srl is not None]

        return json.dumps(sync_run_log)


class ETLSyncStatustType(DjangoObjectType):
    class Meta:
        model = ETLSyncStatus

    updated_by = graphene.Field(graphene.String)
    user_name = graphene.Field(graphene.String)
    sync_period = graphene.Field(graphene.String)

    def resolve_updated_by(self, info):
        if self.audit and "updated_by" in self.audit:
            return self.audit["updated_by"]
        return ""

    def resolve_user_name(self, info):
        if self.audit and "user_name" in self.audit:
            return self.audit["user_name"]
        return ""

    def resolve_sync_status(self, info):
        status = self.sync_status
        if status == ETL_STATUS.COMPLETE.value or status == ETL_STATUS.SUCCESS.value:
            return "completed"
        elif status == ETL_STATUS.PARTIALLY_FAILED.value:
            return "partially failed"
        return status

    def resolve_sync_period(self, info):
        if self.params and "sync_period" in self.params:
            return self.params["sync_period"]
        return ""


class UpstreamEtlStatusType(DjangoObjectType):
    class Meta:
        model = UpstreamETLStatus


class EtlQuery:
    databook_etl_status = graphene.List(graphene.JSONString)
    commission_etl_status = graphene.List(graphene.JSONString)
    e2e_detailed_status = graphene.List(graphene.JSONString)
    e2e_wrapper_status = graphene.String()
    etl_status_history = graphene.List(
        ETLSyncStatustType, page_no=graphene.Int(), page_size=graphene.Int()
    )
    etl_status_count = graphene.Int()
    recent_data_sync_status = graphene.Field(ETLSyncStatustType)
    detailed_sync_status = graphene.Field(graphene.JSONString)
    detailed_sync_status_admin_ui = graphene.Field(
        graphene.JSONString, client_id=graphene.Int(), e2e_sync_run_id=graphene.String()
    )
    failed_payee_period = graphene.List(
        graphene.JSONString,
        e2e_sync_run_id=graphene.String(),
        fetch_data=graphene.Boolean(),
    )
    recent_databook_refresh_for_db_id = graphene.Field(
        graphene.JSONString, databook_id=graphene.String()
    )
    recent_datasheet_refresh_for_db_id = graphene.Field(
        graphene.JSONString,
        databook_id=graphene.String(),
        datasheet_id=graphene.String(),
    )
    recent_upstream_etl_status_for_integration_id = graphene.Field(
        UpstreamEtlStatusType,
        client_id=graphene.Int(required=True),
        integration_id=graphene.String(required=True),
    )

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_databook_etl_status(self, info, **kwargs):
        def get_status_from_lines(status_set):
            if len(status_set) == 1 and "complete" in status_set:
                return "finish"
            elif "failed" in status_set:
                return "error"
            elif "locked" in status_set or "started" in status_set:
                return "process"
            else:
                return "wait"

        databook_wrapper_objects = DatabookETLStatusReaderAccessor(
            info.context.client_id
        ).get_last_n_e2e_syncs(3)
        last_n_e2e_sync_id, e2e_to_databook_wrapper_map = [], {}
        for databook_wrapper_object in databook_wrapper_objects:
            last_n_e2e_sync_id.append(str(databook_wrapper_object["e2e_sync_run_id"]))
            e2e_to_databook_wrapper_map[
                str(databook_wrapper_object["e2e_sync_run_id"])
            ] = databook_wrapper_object
        final_list = []
        for e2e_sync_run_id in last_n_e2e_sync_id:
            e2e_record = {
                "e2e_sync_run_id": e2e_sync_run_id,
                "status": "",
                "DATASHEET_SYNC": {},
                "DATABOOK_WRAPPER_SYNC": {},
            }
            datasheet_id_and_status = list(
                DatabookETLStatusReaderAccessor(
                    info.context.client_id
                ).get_datasheet_id_and_status_for_task(
                    e2e_sync_run_id, "DATASHEET_SYNC"
                )
            )
            datasheet_id_set, datasheet_status_set = set(), set()
            for datasheet_id, status in datasheet_id_and_status:
                datasheet_id_set.add(datasheet_id)
                datasheet_status_set.add(status)
            e2e_record["DATASHEET_SYNC"]["datasheet_count"] = len(datasheet_id_set)
            e2e_record["DATASHEET_SYNC"]["sync_status"] = get_status_from_lines(
                datasheet_status_set
            )

            databook_wrapper_obj = (
                e2e_to_databook_wrapper_map[e2e_sync_run_id]
                if e2e_sync_run_id in e2e_to_databook_wrapper_map
                else {}
            )
            e2e_record["DATABOOK_WRAPPER_SYNC"]["sync_start_time"] = (
                str(databook_wrapper_obj["sync_start_time"])
                if "sync_start_time" in databook_wrapper_obj
                else ""
            )
            databook_wrapper_status_set = set()
            databook_wrapper_status_set.add(databook_wrapper_obj["sync_status"])
            e2e_record["DATABOOK_WRAPPER_SYNC"]["sync_status"] = get_status_from_lines(
                databook_wrapper_status_set
            )
            final_list.append(e2e_record)
        return final_list[::-1]

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_commission_etl_status(self, info, **kwargs):
        def get_status_from_lines(status_set):
            if len(status_set) == 1 and "complete" in status_set:
                return "finish"
            elif "failed" in status_set:
                return "error"
            elif "locked" in status_set or "started" in status_set:
                return "process"
            else:
                return "wait"

        commission_wrapper_objects = CommissionETLStatusReaderAccessor(
            info.context.client_id
        ).get_last_n_e2e_syncs(3)
        last_n_e2e_sync_id, e2e_to_comm_wrapper_map = [], {}
        for commission_wrapper_object in commission_wrapper_objects:
            last_n_e2e_sync_id.append(str(commission_wrapper_object["e2e_sync_run_id"]))
            e2e_to_comm_wrapper_map[
                str(commission_wrapper_object["e2e_sync_run_id"])
            ] = commission_wrapper_object
        ## for e2e_to_team_wrapper_map
        team_wrapper_objects = CommissionETLStatusReaderAccessor(
            info.context.client_id
        ).team_wrapper_criteria_for_e2e(last_n_e2e_sync_id)
        e2e_to_team_wrapper_map = {}
        for team_wrapper_object in team_wrapper_objects:
            e2e_to_team_wrapper_map[str(team_wrapper_object["e2e_sync_run_id"])] = (
                team_wrapper_object
            )
        ## --
        final_list = []
        for e2e_sync_run_id in last_n_e2e_sync_id:
            e2e_record = {
                "e2e_sync_run_id": e2e_sync_run_id,
                "status": "",
                "COMM_PAYEE_SYNC": {},
                "COMM_WRAPPER_SYNC": {},
                "TEAM_PAYEE_SYNC": {},
                "TEAM_WRAPPER_SYNC": {},
            }
            comm_payee_email_and_status = list(
                CommissionETLStatusReaderAccessor(
                    info.context.client_id
                ).get_payee_email_and_status_for_task(
                    e2e_sync_run_id, "COMMISSION_PAYEE_SYNC"
                )
            )
            comm_payee_email_set, comm_status_set = set(), set()
            for payee_email, status in comm_payee_email_and_status:
                comm_payee_email_set.add(payee_email)
                comm_status_set.add(status)
            e2e_record["COMM_PAYEE_SYNC"]["payee_count"] = len(comm_payee_email_set)
            e2e_record["COMM_PAYEE_SYNC"]["sync_status"] = get_status_from_lines(
                comm_status_set
            )

            comm_wrapper_obj = (
                e2e_to_comm_wrapper_map[e2e_sync_run_id]
                if e2e_sync_run_id in e2e_to_comm_wrapper_map
                else {}
            )
            e2e_record["COMM_WRAPPER_SYNC"]["sync_start_time"] = (
                str(comm_wrapper_obj["sync_start_time"])
                if "sync_start_time" in comm_wrapper_obj
                else ""
            )
            comm_wrapper_status_set = set()
            comm_wrapper_status_set.add(comm_wrapper_obj["sync_status"])
            e2e_record["COMM_WRAPPER_SYNC"]["sync_status"] = get_status_from_lines(
                comm_wrapper_status_set
            )
            # ----------------------------------------------------------------
            team_payee_email_and_status = list(
                CommissionETLStatusReaderAccessor(
                    info.context.client_id
                ).get_payee_email_and_status_for_task(
                    e2e_sync_run_id, "TEAM_PAYEE_SYNC"
                )
            )
            team_payee_email_set, team_status_set = set(), set()
            for payee_email, status in team_payee_email_and_status:
                team_payee_email_set.add(payee_email)
                team_status_set.add(status)
            e2e_record["TEAM_PAYEE_SYNC"]["payee_count"] = len(team_payee_email_set)
            e2e_record["TEAM_PAYEE_SYNC"]["sync_status"] = get_status_from_lines(
                team_status_set
            )
            team_wrapper_obj = (
                e2e_to_team_wrapper_map[e2e_sync_run_id]
                if e2e_sync_run_id in e2e_to_team_wrapper_map
                else {}
            )
            e2e_record["TEAM_WRAPPER_SYNC"]["sync_start_time"] = (
                str(team_wrapper_obj["sync_start_time"])
                if "sync_start_time" in team_wrapper_obj
                else ""
            )
            team_wrapper_status_set = set()
            if "sync_status" in team_wrapper_obj:
                team_wrapper_status_set.add(team_wrapper_obj["sync_status"])
            e2e_record["TEAM_WRAPPER_SYNC"]["sync_status"] = get_status_from_lines(
                team_wrapper_status_set
            )

            final_list.append(e2e_record)
        return final_list[::-1]

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_e2e_detailed_status(self, info, **kwargs):
        return get_e2e_detailed_status(info.context.client_id)

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_e2e_wrapper_status(self, info, **kwargs):
        upstream_etl_object = UpstreamETLStatusReaderAccessor(
            info.context.client_id
        ).get_last_n_e2e_syncs(1)
        if len(upstream_etl_object) == 0:
            return "finish"
        e2e_sync_run_id = upstream_etl_object[0]["e2e_sync_run_id"]
        return get_conclusive_status_for_wrapper_type(
            info.context.client_id, e2e_sync_run_id
        )

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_etl_status_history(self, info, **kwargs):
        client_id = info.context.client_id
        page_no = kwargs.get("page_no")
        page_size = kwargs.get("page_size")
        data_from = (page_no - 1) * page_size
        data_to = data_from + page_size

        # if the client has a feature flag allowing them to only show cron jobs marked as failed
        # then we show the cron jobs marked as failed
        if client_accessor.can_only_show_cron_marked_as_failed(client_id):
            status_history = ETLSyncStatusReaderAccessor(
                client_id
            ).get_etl_sync_status_records_with_cron_marked_as_failed(data_from, data_to)
        else:
            # else we show all the cron jobs
            status_history = ETLSyncStatusReaderAccessor(
                client_id
            ).get_etl_sync_status_records(data_from, data_to)
        user_emails = [
            status.audit["updated_by"]
            for status in status_history
            if status.audit and "updated_by" in status.audit
        ]
        employees = EmployeeAccessor(client_id).get_employees_name(user_emails)
        employee_name_dict = {}
        if employees:
            for employee in employees:
                employee_name_dict[employee["employee_email_id"]] = (
                    employee["first_name"] + " " + employee["last_name"]
                )

        for status in status_history:
            if status.audit and "updated_by" in status.audit:
                status.audit["user_name"] = employee_name_dict[
                    status.audit["updated_by"]
                ]
            if (
                status.task == "Report ETL"
                and status.params
                and "object" in status.params
            ):
                status.task = "Report ETL for " + status.params["object"]

            # If the sync_period_end is present, it is a multi period sync
            is_multi_period_sync = status.params.get("sync_period_end", False)

            if is_multi_period_sync:
                # When custom calendar is disabled, the sync_period and sync_period_end values may not accurately reflect
                # the true start and end dates of the multi-period sync (e.g., could be 31 Jan to 01 Jun instead of 01 Jan to 30 Jun).
                # This can cause confusion in the past activities page, as the displayed range may not match the actual processed months.
                # To address this, we normalize the dates to always show the first day of the start month and the last day of the end month,
                # ensuring the displayed sync range is accurate.
                # Post this fix, the sync_period and sync_period_end values will always be the first day of the start month and the last day of the end month.
                status.params["sync_period"] = get_formatted_multi_period_sync_string(
                    status.params["sync_period"],
                    status.params["sync_period_end"],
                    has_feature(client_id, "custom_calendar"),
                )

        return status_history

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_etl_status_count(self, info, **kwargs):
        client_id = info.context.client_id

        # if the client has a feature flag allowing them to only show cron jobs marked as failed
        # then we return the count of the cron jobs marked as failed
        if client_accessor.can_only_show_cron_marked_as_failed(client_id):
            return ETLSyncStatusReaderAccessor(
                client_id
            ).get_total_record_count_with_cron_marked_as_failed()
        else:
            # else we return the count of all the cron jobs
            return ETLSyncStatusReaderAccessor(client_id).get_total_record_count()

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_recent_data_sync_status(self, info, **kwargs):
        return get_recent_data_sync_status(info.context.client_id)

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_detailed_sync_status(self, info, **kwargs):
        e2e_sync_run_id = kwargs.get("e2e_sync_run_id")
        detailed_sync_status = get_detailed_sync_status(
            info.context.client_id, e2e_sync_run_id
        )
        return detailed_sync_status

    @permission_required(
        [RbacPermissions.MANAGE_ADMINUI.value, RbacPermissions.MANAGE_ETLSTATUS.value]
    )
    def resolve_detailed_sync_status_admin_ui(self, info, **kwargs):
        # Admin UI the client id is passed as a gql param.
        client_id = kwargs.get("client_id")
        e2e_sync_run_id = kwargs.get("e2e_sync_run_id")
        detailed_sync_status = get_detailed_sync_status(client_id, e2e_sync_run_id)
        return detailed_sync_status

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_failed_payee_period(self, info, **kwargs):
        e2e_sync_run_id = kwargs.get("e2e_sync_run_id")
        fetch_data = kwargs.get("fetch_data")
        return get_failed_payee_period(
            info.context.client_id, e2e_sync_run_id, fetch_data
        )

    @permission_required(
        [RbacPermissions.MANAGE_DATASETTINGS.value, RbacPermissions.VIEW_DATABOOK.value]
    )
    def resolve_recent_databook_refresh_for_db_id(self, info, **kwargs):
        client_id = info.context.client_id
        databook_id = kwargs.get("databook_id")
        return get_recent_datasheet_databook_sync_status(
            client_id=client_id,
            etl_activity=ETL_ACTIVITY.REFRESH_DATABOOK.value,
            databook_id=databook_id,
        )

    @permission_required(
        [RbacPermissions.MANAGE_DATASETTINGS.value, RbacPermissions.VIEW_DATABOOK.value]
    )
    def resolve_recent_datasheet_refresh_for_db_id(self, info, **kwargs):
        client_id = info.context.client_id
        datasheet_id = kwargs.get("datasheet_id")
        databook_id = kwargs.get("databook_id")
        return get_recent_datasheet_databook_sync_status(
            client_id=client_id,
            etl_activity=ETL_ACTIVITY.GENERATE_DATASHEET.value,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
        )

    @permission_required(
        [
            RbacPermissions.MANAGE_ADMINUI.value,
            RbacPermissions.MANAGE_INTEGRATIONS.value,
        ]
    )
    def resolve_recent_upstream_etl_status_for_integration_id(
        self, info, client_id, integration_id
    ):
        timestamps = get_upstream_timestamps(client_id, integration_id)
        u_etl_record = UpstreamETLStatusReaderAccessor(
            client_id=client_id
        ).get_last_successful_run_record_for_integration(integration_id=integration_id)
        if u_etl_record:
            u_etl_record.changes_end_time = timestamps["api_changes_synced_till"]
        return u_etl_record


class EtlMutation(graphene.ObjectType):
    update_upstream_etl_status = UpdateUpstreamEtlStatus.Field()


def get_formatted_multi_period_sync_string(
    sync_period, sync_period_end, has_custom_calendar
):
    """
    Returns a normalized and formatted sync period string for display.

    Args:
        sync_period (str): The start date string.
        sync_period_end (str): The end date string.
        has_custom_calendar (bool): Whether the client has a custom calendar.

    Returns:
        str: The formatted sync period string, e.g. '01 May 2025 - 31 Aug 2025'
    """
    if not has_custom_calendar:
        sync_period_date = parse(sync_period, dayfirst=True)
        sync_period = first_day_of_month(sync_period_date).strftime("%d %b %Y")
        sync_period_end_date = parse(sync_period_end, dayfirst=True)
        sync_period_end = last_day_of_month(sync_period_end_date).strftime("%d %b %Y")
    return f"{sync_period} - {sync_period_end}"
