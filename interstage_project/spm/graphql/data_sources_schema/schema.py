# pylint: disable=no-member,redefined-builtin
from datetime import datetime
from typing import Any, Dict, List, Optional

import graphene
from promise import Promise

from commission_engine.accessors.client_accessor import (
    get_approvals_report_object_enabled,
    get_fx_rate_report_object_enabled,
    get_payout_approvals_flag,
    get_payout_report_object_enabled,
    get_statement_approval_flag,
)
from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.ever_object_accessor import EverObjectAccessor
from commission_engine.snowflake_accessors.custom_object_data_accessor import (
    get_object_active_row_count,
)
from commission_engine.utils.general_data import RbacPermissions
from commission_engine.utils.report_utils import get_other_module_reports
from interstage_project.auth_utils import permission_required
from spm.services.custom_object_services.co_permission_services import (
    get_objects_excluded_for_user,
)
from spm.services.custom_object_services.data_sources_services import (
    get_linked_datasheets_for_object,
)


class LinkedDatasheetType(graphene.ObjectType):
    databook_id = graphene.String()
    databook_name = graphene.String()
    datasheet_id = graphene.String()
    datasheet_name = graphene.String()

    def resolve_databook_id(self, _):
        return self.get("databook_id")

    def resolve_databook_name(self, _):
        return self.get("databook_name")

    def resolve_datasheet_id(self, _):
        return self.get("datasheet_id")

    def resolve_datasheet_name(self, _):
        return self.get("datasheet_name")


class DataSourceVariableDataType(graphene.ObjectType):
    id = graphene.Int()
    data_type = graphene.String()

    def resolve_id(self, _) -> int:
        return int(self)

    def resolve_data_type(self, info) -> str:
        return info.context.field_data_type_loader.load(key=str(self))


class DataSourceVariable(graphene.ObjectType):
    system_name = graphene.String()
    display_name = graphene.String()
    data_type = graphene.Field(DataSourceVariableDataType)

    def resolve_system_name(self, _) -> str:
        return str(self.get("system_name"))

    def resolve_display_name(self, _) -> str:
        return str(self.get("display_name"))

    def resolve_data_type(self, _) -> str:
        return self.get("data_type_id")


class DataSource(graphene.ObjectType):
    id = graphene.String()
    name = graphene.String()
    type = graphene.String()
    source_connector = graphene.String()
    source_connection_name = graphene.String()
    source_connection_id = graphene.Int()
    synced_from_time = graphene.DateTime()
    last_synced_time = graphene.DateTime()
    sync_start_time = graphene.DateTime()
    initial_sync_date = graphene.DateTime()
    last_updated_time = graphene.DateTime()
    created_time = graphene.DateTime()
    sync_enabled = graphene.Boolean()
    is_fivetran_sync = graphene.Boolean()
    fields_count = graphene.Int()
    integration_additional_data = graphene.JSONString()
    integration_id = graphene.UUID()
    fields = graphene.List(DataSourceVariable)
    primary_keys = graphene.List(graphene.String)
    snapshot_keys = graphene.List(graphene.String)
    csv_header_map = graphene.JSONString()
    latest_sync_run_status = graphene.String()
    active_row_count = graphene.Int()
    ordered_columns = graphene.List(graphene.String)
    created_by = graphene.String()
    linked_datasheets = graphene.List(LinkedDatasheetType)

    def resolve_id(self, _) -> str:
        return str(self.get("id"))

    def resolve_name(self, _) -> str:
        return self.get("name")

    def resolve_created_time(self, _) -> datetime:
        if self.get("type") == "report":
            # Report objects are created by the system.
            return datetime.min

        return self.get("created_at")

    def resolve_last_updated_time(self, _) -> datetime:
        return self.get("last_updated_time")

    def resolve_type(self, _) -> str:
        return self.get("type")

    def resolve_source_connector(self, info) -> Optional[Promise]:
        if self.get("type") == "report":
            return None

        return info.context.source_connector_loader.load(key=self.get("id"))

    def resolve_source_connection_name(self, info) -> Optional[str]:
        if self.get("type") == "report":
            return None

        return info.context.source_connection_name_loader.load(key=self.get("id"))

    def resolve_source_connection_id(self, info) -> Optional[str]:
        if self.get("type") == "report":
            return None

        return info.context.source_connection_id_loader.load(key=self.get("id"))

    def resolve_synced_from_time(self, info) -> Optional[datetime]:
        return info.context.synced_from_time_loader.load(key=self.get("id"))

    def resolve_last_synced_time(self, info) -> Optional[datetime]:
        return info.context.last_synced_time_loader.load(key=self.get("id"))

    def resolve_initial_sync_date(self, info) -> Optional[datetime]:
        return info.context.initial_sync_date_loader.load(key=self.get("id"))

    def resolve_sync_start_time(self, info) -> Optional[datetime]:
        return info.context.sync_start_time_loader.load(key=self.get("id"))

    def resolve_sync_enabled(self, info) -> Optional[bool]:
        if self.get("type") == "report":
            return None

        return info.context.sync_enabled_loader.load(key=self.get("id"))

    def resolve_is_fivetran_sync(self, info) -> Optional[bool]:
        if self.get("type") == "report":
            return None

        return info.context.is_fivetran_sync_loader.load(key=self.get("id"))

    def resolve_fields_count(self, info) -> Promise:
        return info.context.fields_count_loader.load(key=self.get("id"))

    def resolve_integration_additional_data(self, info) -> Optional[Promise]:
        if self.get("type") == "report":
            return None
        return info.context.integration_additional_data_loader.load(key=self.get("id"))

    def resolve_integration_id(self, info) -> Optional[Promise]:
        if self.get("type") == "report":
            return None

        return info.context.integration_id_loader.load(key=self.get("id"))

    def resolve_latest_sync_run_status(self, info) -> Optional[Promise]:
        if self.get("type") == "report":
            return None

        return info.context.latest_sync_run_status_loader.load(key=self.get("id"))

    def resolve_fields(self, info) -> Promise:
        return info.context.fields_loader.load(key=self.get("id"))

    def resolve_primary_keys(self, _):
        return self.get("primary_keys")

    def resolve_snapshot_keys(self, _):
        return self.get("snapshot_keys")

    def resolve_csv_header_map(self, _):
        if self.get("type") == "report":
            return None

        return self.get("csv_header_map")

    def resolve_active_row_count(self, info):
        return get_object_active_row_count(
            client_id=info.context.client_id,
            object_id=self.get("id"),
            object_type=self.get("type"),
        )

    def resolve_ordered_columns(self, _):
        if self.get("type") == "report":
            return None

        return self.get("ordered_columns")

    def resolve_created_by(self, _):
        if self.get("type") == "report":
            return None

        return self.get("created_by")

    def resolve_linked_datasheets(self, info):
        return get_linked_datasheets_for_object(
            client_id=info.context.client_id, data_source_id=self.get("id")
        )


class DataSourcesQuery(object):
    """
    DataSources GraphQL queries entrypoint.
    """

    data_sources = graphene.List(DataSource)
    data_source = graphene.Field(DataSource, id=graphene.String(required=True))

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_REPORTENRICH.value,
        ]
    )
    def resolve_data_sources(self, info) -> List[Dict[str, Any]]:
        """
        Resolver for dataSources query.

        Returns:
            List[Dict[str, Any]]: Returns a list of custom objects and report objects.

        Business Logic:
            - Extracts user ID and client ID from GraphQL context.
            - Excludes inaccessible objects based on user permissions.
            - Fetches details for each data source (custom or report).
            - Constructs a list of data sources with normalized relevant attributes by combining both report and custom objects.
        """
        user_id = info.context.user
        client_id = info.context.client_id
        objects = []

        excluded_custom_object_ids = get_objects_excluded_for_user(
            client_id=client_id, user_id=user_id, object_type="object"
        )
        exculded_report_object_ids = get_objects_excluded_for_user(
            client_id=client_id, user_id=user_id, object_type="report"
        )
        exculded_report_object_ids.update(get_other_module_reports(client_id))
        if not get_payout_report_object_enabled(client_id):
            exculded_report_object_ids.add("payout")
        if not get_fx_rate_report_object_enabled(client_id):
            exculded_report_object_ids.add("fx_rate")
        if not (
            get_approvals_report_object_enabled(client_id)
            and get_payout_approvals_flag(client_id)
        ):
            exculded_report_object_ids.add("statement_approvals")

        else:
            if not get_statement_approval_flag(client_id):
                exculded_report_object_ids.add("statement_approvals")

        custom_objects = (
            CustomObjectAccessor(client_id)
            .get_objects_excluding_ids(excluded_custom_object_ids)
            .values(
                "custom_object_id",
                "name",
                "primary_key",
                "snapshot_key",
                "ordered_columns",
                "csv_header_map",
                "knowledge_begin_date",
                "created_at",
                "created_by",
            )
        )
        report_objects = (
            EverObjectAccessor()
            .get_ever_objects_excluding_ids(exculded_report_object_ids)
            .values("ever_object_id", "name", "primary_key", "knowledge_begin_date")
        )

        objects.extend(
            [
                {
                    "id": str(co.get("custom_object_id")),
                    "name": co.get("name"),
                    "primary_keys": co.get("primary_key"),
                    "snapshot_keys": co.get("snapshot_key"),
                    "ordered_columns": co.get("ordered_columns"),
                    "created_at": co.get("created_at"),
                    "csv_header_map": co.get("csv_header_map"),
                    "last_updated_time": co.get("knowledge_begin_date"),
                    "created_by": co.get("created_by"),
                    "type": "custom",
                }
                for co in custom_objects
            ]
        )
        objects.extend(
            [
                {
                    "id": ro.get("ever_object_id"),
                    "name": ro.get("name"),
                    "primary_keys": ro.get("primary_key"),
                    "last_updated_time": ro.get("knowledge_begin_date"),
                    "type": "report",
                    "created_at": datetime.min,
                }
                for ro in report_objects
            ]
        )

        return objects

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_REPORTENRICH.value,
        ]
    )
    def resolve_data_source(self, info, id: str) -> Dict[str, Any]:
        """
        Resolver for dataSource query.

        Returns:
            Dict[str, Any]: Returns the dataSource with relevant attributes.

        Business Logic:
            - Extracts user ID and client ID from GraphQL context.
            - Determine object type based on ID (passed as param):
                - "custom" if ID is numeric, "report" otherwise.
            - Excludes inaccessible objects based on user permissions.
            - Fetches details for the data source.
            - Returns the data source with normalized relevant attributes.
        """
        user_id = info.context.user
        client_id = info.context.client_id
        object_type = "custom" if id.isnumeric() else "report"

        if object_type == "custom":
            exculded_custom_object_ids = get_objects_excluded_for_user(
                client_id=client_id, user_id=user_id, object_type="object"
            )

            object = (
                CustomObjectAccessor(client_id)
                .get_objects_excluding_ids(exculded_custom_object_ids)
                .filter(custom_object_id=int(id))
                .values(
                    "custom_object_id",
                    "name",
                    "primary_key",
                    "snapshot_key",
                    "csv_header_map",
                    "knowledge_begin_date",
                    "created_at",
                    "created_by",
                )
                .get()
            )

            return {
                "id": str(object.get("custom_object_id")),
                "name": object.get("name"),
                "primary_keys": object.get("primary_key"),
                "snapshot_keys": object.get("snapshot_key"),
                "ordered_columns": object.get("ordered_columns"),
                "created_at": object.get("created_at"),
                "csv_header_map": object.get("csv_header_map"),
                "last_updated_time": object.get("knowledge_begin_date"),
                "created_by": object.get("created_by"),
                "type": "custom",
            }

        else:
            exculded_report_object_ids = get_objects_excluded_for_user(
                client_id=client_id, user_id=user_id, object_type="report"
            )
            exculded_report_object_ids.update(get_other_module_reports(client_id))
            object = (
                EverObjectAccessor()
                .get_ever_objects_excluding_ids(exculded_report_object_ids)
                .filter(ever_object_id=str(id))
                .values("ever_object_id", "name", "primary_key", "knowledge_begin_date")
                .get()
            )

            return {
                "id": object.get("ever_object_id"),
                "name": object.get("name"),
                "primary_keys": object.get("primary_key"),
                "last_updated_time": object.get("knowledge_begin_date"),
                "type": "report",
                "created_at": datetime.min,
            }
