import datetime
from unittest.mock import patch
from uuid import UUID

import pytest
import pytz
from django.utils import timezone

from commission_engine.tests.accessors.utils import (
    set_client_feature,
    unset_client_feature,
)
from commission_engine.utils.general_data import DatasheetDataRegenerateReason
from everstage_etl.databook_etl import (
    DatasheetExecutionDetails,
    TranformationInputDetails,
)
from spm.services.datasheet_graph.custom_exceptions import (
    IncludeStaleInformationQueryOnDatasheetGraphException,
)
from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph, DatasheetNode

UTC = pytz.UTC
time = timezone.now()
"""
                                                                         
        ..                                                                         ..
      .    .                     ..                                               .  .
     .      .                   .   .                    ..                      .    .
    .         .                .     .                  .  .                    .  C4  .
  .            .              .       .                .    .            +---- .        . ------------+
 .      C1       .           .         .              .      .           |     ...........            |
.                 .         .     C2    .            .   C3   .          |             |              |
....................       .             .          .          .         |             |              |
         |                .................         .............        |             |              |
         |                      |                    |                   |             |              |
         |                      |                    |                   |             |              |
         v                      v                    |                   |             |              |
   +-----------+           +------------+            |                   |             |              |
   |           |           |            |            |                   |             |              |
   |    D1     |           |      D2    +-------+    |                   v             v              v
   |           |           |            |       |    |                +---------+    +--------+  +-------+
   +--+--------+           +----+-------+       |    |                |         |    |        |  |       |
      |                         |               |    |                |    D6   |    |        |  |   D8  |
      |                         |               |    |                |         |    |  D7    |  |       |
      |                         |               |    |                |         |    |        |  |       |
      |                         |               |    |                +---------+    +--------+  +-------+
      |                         |               v    v
      |          +---------+    |             +-----------+
      +--------->|         |<---+             |           |
                 |   D3    |                  |   D4      |
                 |         |                  |           |
                 |         |                  |           |
                 +--+------+                  +-----------+
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |            +----------+        |
                    |            |          |        |
                    |            |          |<-------+
                    +----------->|     D5   |
                                 |          |
                                 |          |
                                 +----------+
                              
   
    C1,C2,C3,C4 -> Custom Objects
    D1,D2,D3,D4,D5,D6,D7,D8 -> Datasheet  
    
    client_id -> 3001
    databook_id -> "7283241f-42c9-48f6-834d-e853f0522f1b"

    datasheet_ids:-
        D1-> 'ab074ee8-4ae1-4126-9a7e-32062bc64c36',
        D2-> 'affbea4d-aaec-41a5-8f92-1ae6730dfeca',
        D3-> 'a748e86a-5261-401a-8424-d10557ae558a',
        D4-> '6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd',
        D5-> '079b5209-9377-4d44-90f0-92937eb1c575',
        D6-> '7f2df1a8-b5d2-4110-8d99-6afbc402cfff',
        D7-> '3b50a648-eee9-4de6-b1f5-478950100a4f',
        D8-> '4a4a23ca-11bb-4af9-96c0-9275be14bc55',

    
    custom_object_ids:-
        C1 -> 6
        C2 -> 7
        C3 -> 9
        C4 -> 10  
    

    
    - @patch decorator in this test to mock the knowledge_date_map_for_custom_and_report_objects
    function that is imported in the spm.services.datasheet_graph.datasheet_graph module.

    - The knowledge_date_map_for_custom_and_report_objects function is called in the DataSheetGraph class uses 
    snowflake_setup insted of snowflake setup we mock the function to return the value we want
    
    - Mocking this function allows us to simulate its behavior and return values that we specify,
    instead of calling the real function
    
    
    
    Steps to add more test in this
    
    1) Read this docs for Initial Unit test setup -> https://interstage.atlassian.net/wiki/spaces/TECH/pages/530939905/Unit+Test+Infra  
    2) Login into local host using the credentials for client_id 3001
    3) Add test using the app interface  
    4) Migrate your local changes to QA instances using this script -> https://github.com/Everstage/everstage-spm/blob/master/interstage_project/scripts/migrate_script.py
        steps to run the script
        https://everstage.slack.com/archives/C01FJERETRV/p1677679312733919
    
                     
    """


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_parent(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.parent(datasheet_id="7f2df1a8-b5d2-4110-8d99-6afbc402cfff")

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_parent",ans)
    expected_ans = {"4"}
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_children(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.children(datasheet_id="ab074ee8-4ae1-4126-9a7e-32062bc64c36")

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_children",ans)
    expected_ans = {"a748e86a-5261-401a-8424-d10557ae558a"}
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_ancestors(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.ancestors(datasheet_id="079b5209-9377-4d44-90f0-92937eb1c575")

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_ancestors", ans)

    expected_ans = {
        "1",
        "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
        "3",
        "2",
        "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
        "a748e86a-5261-401a-8424-d10557ae558a",
        "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
    }
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_descendants(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.descendants(
        datasheet_id="affbea4d-aaec-41a5-8f92-1ae6730dfeca"
    )

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_descendants", ans)

    expected_ans = {
        "a748e86a-5261-401a-8424-d10557ae558a",
        "079b5209-9377-4d44-90f0-92937eb1c575",
        "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
    }

    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_common_ancestors(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.common_ancestors(
        datasheet_id1="a748e86a-5261-401a-8424-d10557ae558a",
        datasheet_id2="6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
    )

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_common_ancestors", ans)
    expected_ans = {"2", "affbea4d-aaec-41a5-8f92-1ae6730dfeca"}

    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_common_descendants(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.common_descendants(
        datasheet_id1="affbea4d-aaec-41a5-8f92-1ae6730dfeca",
        datasheet_id2="6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
    )

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_common_descendants", ans)
    expected_ans = {"079b5209-9377-4d44-90f0-92937eb1c575"}

    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_roots(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.roots()

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_roots", ans)
    expected_ans = {"3", "1", "4", "2"}
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_leaves(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.leaves()

    ans = set()
    for x in temp:
        ans.add(x.node_id)

    # print("test_leaves", ans)
    expected_ans = {
        "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
        "079b5209-9377-4d44-90f0-92937eb1c575",
        "3b50a648-eee9-4de6-b1f5-478950100a4f",
        "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
    }
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, node_type, expected_result",
    [
        (
            3001,
            "7283241f-42c9-48f6-834d-e853f0522f1b",
            DatasheetNode,
            {
                "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
                "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
                "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
                "a748e86a-5261-401a-8424-d10557ae558a",
                "079b5209-9377-4d44-90f0-92937eb1c575",
                "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                "3b50a648-eee9-4de6-b1f5-478950100a4f",
                "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
            },
        ),
        # This databook contains GETUSERPROPERTIES transformation
        # Checking whether user object is added as a node here
        (
            9007,
            None,
            None,
            {
                "user",
                "6",
                "940dfc33-f8a0-46ad-88de-79450e10e1fa",
                "70944c1e-408f-41a3-b3f3-baa4edd1a1ec",
                "e786417b-0425-4eea-aa08-2f287ca10dd5",
                "4",
            },
        ),
    ],
)
def test_all_nodes(
    mock_custom_object_kd_map, client_id, databook_id, node_type, expected_result
):
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.all_nodes(node_type=node_type)
    ans = set()
    for x in temp:
        ans.add(x.node_id)

    assert ans == expected_result


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_all_datasheet_ids(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.all_datasheet_ids()
    ans = set()
    for x in temp:
        ans.add(x)

    # print("test_all_datasheet_ids", ans)

    expected_ans = {
        "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
        "a748e86a-5261-401a-8424-d10557ae558a",
        "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
        "3b50a648-eee9-4de6-b1f5-478950100a4f",
        "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
        "079b5209-9377-4d44-90f0-92937eb1c575",
        "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
        "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
    }
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_ancestors_by_type(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    data = datasheetgraph.ancestors_by_type(
        datasheet_id="079b5209-9377-4d44-90f0-92937eb1c575"
    )

    ans = {
        frozenset(node.node_id for node in data["custom_object"]),
        frozenset(node.node_id for node in data["datasheet"]),
    }

    expected_ans = {
        frozenset({"2", "1", "3"}),
        frozenset(
            {
                "a748e86a-5261-401a-8424-d10557ae558a",
                "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
                "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
            }
        ),
    }
    assert ans == expected_ans


# @pytest.mark.skip()
@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
def test_independent_subgraphs(mock_custom_object_kd_map):
    databook_id = "7283241f-42c9-48f6-834d-e853f0522f1b"
    client_id = 3001
    mock_custom_object_kd_map.return_value = {}
    datasheetgraph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    temp = datasheetgraph.independent_subgraphs()

    all_independent_subgraphs = {
        frozenset(node.node_id for node in subgraph) for subgraph in temp
    }

    # print("test_independent_subgraphs", all_independent_subgraphs)

    expected_ans = {
        frozenset(
            {
                "079b5209-9377-4d44-90f0-92937eb1c575",
                "1",
                "a748e86a-5261-401a-8424-d10557ae558a",
                "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
                "3",
                "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
                "2",
            }
        ),
        frozenset(
            {
                "4",
                "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
                "3b50a648-eee9-4de6-b1f5-478950100a4f",
                "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
            }
        ),
    }
    assert all_independent_subgraphs == expected_ans


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "datasheet_id, expected_result",
    [
        (
            "82f71a15-1771-45c9-ab52-8ca304f8bc83",
            {
                "is_report_data_stale": True,
                "reason": DatasheetDataRegenerateReason.ANCESTOR_REPORT_OBJECT_MODIFIED.value,
            },
        ),
        (
            "16606db0-4e8d-4a6a-ab62-d29871e42eae",
            {
                "is_report_data_stale": True,
                "reason": DatasheetDataRegenerateReason.SOURCE_REPORT_OBJECT_DATA_MODIFIED.value,
            },
        ),
        (
            "018de25b-de69-4c5a-a2da-23f2a361bfbb",
            {
                "is_report_data_stale": False,
                "reason": None,
            },
        ),
    ],
)
def test_is_report_data_stale(mock_custom_object_kd_map, datasheet_id, expected_result):
    # Some corner test cases will not capture on is_datasheet_data_stale test beacuse
    # _is_report_data_stale is called internally and after some if checks
    # only we return the value.

    # separate function to test corner cases of _is_report_data_stale.

    databook_id = "3c73337d-4e56-4d5e-942e-f62b555a813b"
    client_id = 3001
    utc = pytz.UTC
    mock_custom_object_kd_map.return_value = {
        "user": datetime.datetime(2028, 2, 21, 11, 39, 43, 755557).replace(tzinfo=utc),
    }
    datasheetgraph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=True,
    )
    ancestors_by_type = datasheetgraph.ancestors_by_type(datasheet_id=datasheet_id)
    result = datasheetgraph._is_report_data_stale(datasheet_id, ancestors_by_type)
    assert result == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, expected_result",
    [
        (
            3001,
            "8117108d-2bae-41de-bb19-b312ca54228e",
            {
                (
                    UUID("38dc5817-b100-41dd-87f9-0ffc93352349"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 37, 22, 562917, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 45, 48, 762524, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("3c14c96c-d630-44ad-9cd4-5c18bafc59b7"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 37, 48, 817525, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 44, 59, 766755, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("621533a8-4deb-4f6d-a696-4f62835e1a96"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 36, 47, 1778, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 46, 28, 891575, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("692f8d71-21f4-4dea-9747-3d87a765aeae"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 36, 58, 674111, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 46, 15, 513322, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("98a9cb82-e522-4094-ac5c-f60e133babb9"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 36, 17, 460326, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 46, 57, 25321, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
            },
        ),
        (
            3001,
            "26538f83-580d-40dd-bd43-93a2f171bc0f",
            {
                (
                    UUID("4ff9c74b-bf6d-491b-8cd5-46ef5547f283"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 16, 1, 6, 750606, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 43, 4, 101638, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                ),
                (
                    UUID("58e6c797-784b-43e2-a71b-cf8977bcafe0"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 16, 1, 6, 750606, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 39, 25, 575251, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 43, 19, 114140, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("9bc4add5-89b1-4117-b427-effeb69c2ff9"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 59, 281344, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 39, 1, 83840, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 44, 13, 807482, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("c0edce8b-8c71-471a-bf35-677b812ef525"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 16, 1, 6, 750606, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 40, 13, 167624, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 42, 38, 42815, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("eca81b9a-6075-46db-a546-232b682b5117"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 59, 281344, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 38, 35, 304388, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 44, 28, 108808, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
            },
        ),
        (
            3001,
            "7283241f-42c9-48f6-834d-e853f0522f1b",
            {
                (
                    UUID("a748e86a-5261-401a-8424-d10557ae558a"),
                    datetime.datetime(
                        2023, 3, 10, 10, 5, 34, 627026, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("affbea4d-aaec-41a5-8f92-1ae6730dfeca"),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 56, 586567, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 56, 586567, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("ab074ee8-4ae1-4126-9a7e-32062bc64c36"),
                    datetime.datetime(
                        2023, 3, 10, 10, 4, 49, 990191, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("7f2df1a8-b5d2-4110-8d99-6afbc402cfff"),
                    datetime.datetime(
                        2023, 3, 10, 10, 4, 20, 312410, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("3b50a648-eee9-4de6-b1f5-478950100a4f"),
                    datetime.datetime(
                        2023, 3, 10, 10, 4, 30, 147699, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("079b5209-9377-4d44-90f0-92937eb1c575"),
                    datetime.datetime(
                        2023, 3, 10, 10, 7, 7, 226237, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("4a4a23ca-11bb-4af9-96c0-9275be14bc55"),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 40, 321764, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 40, 321764, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd"),
                    datetime.datetime(
                        2023, 3, 10, 10, 22, 8, 656650, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 3, 10, 10, 22, 8, 656650, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
            },
        ),
        (
            3001,
            None,
            {
                (
                    UUID("343b27f2-4c43-482b-8135-c2dc04b12659"),
                    datetime.datetime(
                        2023, 5, 4, 2, 26, 16, 158857, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 26, 16, 158857, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("0b399893-a10a-455f-a6c7-69b1cc68d2fb"),
                    datetime.datetime(
                        2023, 7, 24, 15, 50, 10, 856707, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("ef98aef7-163d-4039-90ec-dd4a46d9cd34"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("d0c487b8-cc20-475b-88a4-0f352c0f28f2"),
                    datetime.datetime(
                        2023, 5, 4, 2, 26, 16, 158857, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 26, 16, 158857, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("2ddd0225-b8e1-4695-bd34-585d14c7f12c"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("a748e86a-5261-401a-8424-d10557ae558a"),
                    datetime.datetime(
                        2023, 3, 10, 10, 5, 34, 627026, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6b675ccc-34cd-46de-82fd-0fcb342d2a5a"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("c0edce8b-8c71-471a-bf35-677b812ef525"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 16, 1, 6, 750606, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 40, 13, 167624, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 42, 38, 42815, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("e67d588a-96cd-4836-9f7e-6f540b93e7aa"),
                    datetime.datetime(
                        2023, 5, 4, 1, 32, 11, 264441, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("15a059a8-861a-404b-818d-ea4fc8a89c86"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6729bd09-ff47-439c-9351-af5a2479822b"),
                    datetime.datetime(
                        2023, 7, 24, 15, 34, 58, 929886, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("4e39463f-00f0-4ed3-b6b7-f240c85ccb2c"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("382a06f5-004f-46da-b9ed-74f2adad31dd"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("1857263a-b9ee-4742-9ac3-************"),
                    datetime.datetime(
                        2023, 7, 24, 15, 12, 6, 678061, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("1623ab06-a264-4d4c-ad5d-4194b61b55b2"),
                    datetime.datetime(
                        2023, 7, 24, 15, 48, 3, 446781, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("5206bac2-fdf1-4b1f-966b-18796876ca2c"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("079b5209-9377-4d44-90f0-92937eb1c575"),
                    datetime.datetime(
                        2023, 3, 10, 10, 7, 7, 226237, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("89d406a2-834a-4ce7-bec3-77f254fc2af1"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("8a02cfb2-5988-4613-85c1-414735e516cc"),
                    datetime.datetime(
                        2023, 5, 4, 2, 16, 39, 68390, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 16, 39, 68390, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("c52fb917-3306-4b69-908d-7c45d8f1ed2c"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("0f1633d4-af2d-41e5-af59-704a0e573ed0"),
                    datetime.datetime(
                        2023, 5, 4, 2, 12, 25, 217664, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 12, 25, 217664, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 13, 13, 289407, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                ),
                (
                    UUID("f7e3c7c8-6fa7-4ec1-a41a-6be756085c9a"),
                    datetime.datetime(
                        2023, 5, 14, 18, 14, 35, 483485, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 14, 18, 42, 59, 357607, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("aeed160f-e499-474d-a239-f1dae3589cef"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("5e7bf79c-7fee-4d81-ba23-9523f92a109f"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("bdb082f3-c46e-4ba9-b6b9-bc159f687c58"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("65ade71b-c74f-43d3-83e8-7cf2bd8806dc"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("94d20128-8590-4ce8-ab3f-17c1006bfdbd"),
                    datetime.datetime(
                        2023, 5, 4, 2, 32, 42, 494924, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 32, 42, 494924, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("2d380751-226b-414a-9e64-5183237fcf33"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("4a2790c2-4920-48c9-991c-33b9ab3739c4"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("ab074ee8-4ae1-4126-9a7e-32062bc64c36"),
                    datetime.datetime(
                        2023, 3, 10, 10, 4, 49, 990191, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("83618115-b565-49c3-8340-a40a4fd91f7a"),
                    datetime.datetime(
                        2023, 7, 24, 15, 14, 44, 807272, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("71a25d46-f118-4a5d-ab0a-de9165146842"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("82f71a15-1771-45c9-ab52-8ca304f8bc83"),
                    datetime.datetime(
                        2023, 5, 4, 2, 36, 42, 454025, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 32, 42, 494924, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("db9d0d97-bee9-402e-a148-e6ece45ebddd"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("e08343ae-d8fb-4391-8315-b0961e1a25a0"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("2db4a5d5-fa43-470e-8562-5aeec1d34250"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("98a9cb82-e522-4094-ac5c-f60e133babb9"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 36, 17, 460326, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 46, 57, 25321, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("8114368e-7a0f-433a-b124-dcc6cde73e8f"),
                    datetime.datetime(
                        2023, 5, 4, 1, 33, 17, 909847, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("3305353e-9367-4329-a5b7-c878f547d3c4"),
                    datetime.datetime(
                        2023, 7, 24, 15, 16, 24, 738270, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("5777f92c-7db9-4e6b-bee8-85fdb014d32a"),
                    datetime.datetime(
                        2023, 7, 24, 15, 52, 12, 663676, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("2dd3c5cc-7f82-4247-a3fa-aa37feda6784"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("1144c3c1-1a52-4f6a-8dfe-6ed25b769ad1"),
                    datetime.datetime(
                        2023, 7, 24, 15, 10, 9, 418903, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("495cccd4-acbc-4810-8b62-14417a6f1b1b"),
                    datetime.datetime(
                        2023, 7, 24, 15, 37, 33, 690031, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("da8c0a02-2f03-4fc1-b8f2-1dcf567737d0"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("3c14c96c-d630-44ad-9cd4-5c18bafc59b7"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 37, 48, 817525, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 44, 59, 766755, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("16606db0-4e8d-4a6a-ab62-d29871e42eae"),
                    datetime.datetime(
                        2023, 5, 4, 2, 26, 16, 158857, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 26, 16, 158857, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("ad038a58-5bbf-4a28-80ec-0ec911c13701"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("03ec48ac-623d-4693-a0ec-b37b982e590e"),
                    datetime.datetime(
                        2023, 7, 24, 15, 18, 47, 132972, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("9d0e8a2c-a351-4937-af9f-eec2284d5bd1"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("f1a63db0-30c1-4501-b80e-e306dde689c6"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("e691fb21-93fe-4859-a51b-e93874b580ba"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("bcdadf42-4e9a-4ac4-a83f-6ff93d9cb48c"),
                    datetime.datetime(
                        2023, 7, 24, 15, 48, 43, 335112, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("025be5a7-c94f-4eba-95ae-60eaaacda458"),
                    datetime.datetime(
                        2023, 7, 24, 15, 11, 43, 770158, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("73ef28e6-58a4-4ca7-a0b5-a428b8f656a1"),
                    datetime.datetime(
                        2023, 7, 24, 15, 24, 16, 827118, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("dfe324a8-80e8-4f4c-b427-697dbcb40fd5"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("2817d89e-112a-4930-9813-4bae562acafb"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("7f004654-32b1-4a61-8293-7ac0bebf49c1"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd"),
                    datetime.datetime(
                        2023, 3, 10, 10, 22, 8, 656650, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 3, 10, 10, 22, 8, 656650, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("dd2ecdfc-a825-439a-9705-f8e4fa37534b"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("e72290d3-ed51-4783-ba8c-975c7314f041"),
                    datetime.datetime(
                        2023, 7, 24, 15, 9, 54, 149661, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("d63d9ab1-6ce5-4e2e-bc70-bfd40b3fd478"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("692f8d71-21f4-4dea-9747-3d87a765aeae"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 36, 58, 674111, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 46, 15, 513322, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("b4c55fba-1714-4196-9309-3bb878d1db59"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("76664888-44db-4022-bb4e-343a2937edc9"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6a3ef60d-ab4a-4104-8fc6-e891d0d5d04d"),
                    datetime.datetime(
                        2023, 7, 24, 15, 15, 45, 564681, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("666ed7ed-5a4f-45f0-bfa2-db1d23cb9d43"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("5c0a4e4f-27bf-4806-bb4c-568e61c30215"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("e82c384f-f8c9-4239-bd02-87d699f8df66"),
                    datetime.datetime(
                        2023, 5, 4, 2, 7, 16, 585170, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 9, 9, 685463, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    datetime.datetime(
                        2023, 5, 4, 2, 9, 58, 310113, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("178c4070-2dd4-414d-8a1b-61c30adaeb6c"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("beef65de-0cd5-4ee5-baf4-60501da75e73"),
                    datetime.datetime(
                        2023, 7, 24, 15, 37, 9, 266886, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("889a31ed-6600-47dd-a006-c6b3c32bf2ea"),
                    datetime.datetime(
                        2023, 5, 4, 2, 1, 51, 367207, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 1, 51, 367207, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("3a890719-71f6-4638-8455-9d089ec80179"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("73a695d9-d46e-4ab2-a698-6ebb371a141f"),
                    datetime.datetime(
                        2023, 5, 4, 2, 3, 44, 995840, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("cfa9bcc3-4f2d-428c-b903-cbda65cbe8b4"),
                    datetime.datetime(
                        2023, 7, 24, 15, 13, 1, 413862, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("affbea4d-aaec-41a5-8f92-1ae6730dfeca"),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 56, 586567, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 56, 586567, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("a1b8cf5a-7f27-4c2a-8a55-b34b61409e38"),
                    datetime.datetime(
                        2023, 7, 24, 15, 11, 23, 500751, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("2c49191c-2510-429a-8f02-cd74e8f4e482"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("be560680-9def-455c-8f32-0c0a5fab8117"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("621533a8-4deb-4f6d-a696-4f62835e1a96"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 36, 47, 1778, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 46, 28, 891575, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("4a4a23ca-11bb-4af9-96c0-9275be14bc55"),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 40, 321764, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 3, 10, 10, 21, 40, 321764, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("9bc4add5-89b1-4117-b427-effeb69c2ff9"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 59, 281344, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 39, 1, 83840, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 44, 13, 807482, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("f496559e-55ec-481e-b1fd-0452ede0f339"),
                    datetime.datetime(
                        2023, 7, 24, 15, 9, 32, 445652, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("4c238119-b8e6-4934-a382-3bfb0121af17"),
                    datetime.datetime(
                        2023, 5, 14, 18, 14, 35, 483485, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 14, 18, 44, 54, 148550, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    datetime.datetime(
                        2023, 5, 14, 18, 22, 28, 697799, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("73f9c8dd-b58d-479d-a38f-959a9dd2beea"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("67bf2dfa-c884-42c6-8e8a-b369b7063c8c"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("58e6c797-784b-43e2-a71b-cf8977bcafe0"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 16, 1, 6, 750606, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 39, 25, 575251, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 43, 19, 114140, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("8a445ffd-2c79-4856-8a63-d687710ae715"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("1a22aa22-b6da-408f-97ce-a52438d6dc62"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("d3276870-5f53-492c-b24a-fd53dbaced91"),
                    datetime.datetime(
                        2023, 7, 24, 15, 17, 59, 928581, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("38dc5817-b100-41dd-87f9-0ffc93352349"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 31, 14754, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 24, 642724, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 37, 22, 562917, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 45, 48, 762524, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("018de25b-de69-4c5a-a2da-23f2a361bfbb"),
                    datetime.datetime(
                        2023, 5, 4, 2, 3, 56, 930521, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 3, 56, 930521, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 4, 59, 837565, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 6, 18, 504055, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("f9dd1e84-4321-410d-af5d-d5187a408ed5"),
                    datetime.datetime(
                        2023, 7, 24, 15, 23, 9, 587761, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6e49bf84-0a9f-440f-b81f-e55a2d839449"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("7f2df1a8-b5d2-4110-8d99-6afbc402cfff"),
                    datetime.datetime(
                        2023, 3, 10, 10, 4, 20, 312410, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("e864a451-f696-4ce1-9550-7a895f1f2569"),
                    datetime.datetime(
                        2023, 5, 4, 2, 9, 9, 685463, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 4, 2, 9, 9, 685463, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("25c41904-8dcb-4de3-bfd6-bee657440c5e"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 32, 882901, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("ec58273d-d6ef-4192-8bbf-965bd68e424e"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("3b50a648-eee9-4de6-b1f5-478950100a4f"),
                    datetime.datetime(
                        2023, 3, 10, 10, 4, 30, 147699, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("ed0cc67b-5247-4210-a112-573880a09f6a"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("21cc34df-50e3-4cca-bf4b-16e3f2be3a33"),
                    datetime.datetime(
                        2023, 5, 14, 18, 23, 27, 161670, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 5, 14, 18, 44, 54, 148550, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("eca81b9a-6075-46db-a546-232b682b5117"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 49, 59, 281344, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 38, 35, 304388, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 44, 28, 108808, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                ),
                (
                    UUID("59d0d7da-e9a8-41fc-8174-a2ee3781802f"),
                    datetime.datetime(
                        2023, 7, 24, 15, 37, 53, 26081, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("6c820b01-ef76-43b1-96b3-c911c671c182"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 40, 365423, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("4ff9c74b-bf6d-491b-8cd5-46ef5547f283"),
                    datetime.datetime(
                        2023, 7, 14, 15, 34, 24, 510950, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 16, 1, 6, 750606, tzinfo=datetime.timezone.utc
                    ),
                    datetime.datetime(
                        2023, 7, 14, 15, 43, 4, 101638, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                ),
                (
                    UUID("061fe09f-c3d4-4d6f-bdf8-317c561cd9f1"),
                    datetime.datetime(
                        2023, 7, 24, 15, 13, 52, 944102, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("41a4435e-9d2c-4e34-809e-02797f25e6dc"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("7b5e8b3c-93f5-4224-af03-ff289b0b3579"),
                    datetime.datetime(
                        2023, 7, 24, 15, 27, 43, 451268, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
                (
                    UUID("d249eec3-c247-447f-86e0-34b0555f1236"),
                    datetime.datetime(
                        2023, 7, 27, 7, 36, 36, 456471, tzinfo=datetime.timezone.utc
                    ),
                    None,
                    None,
                    None,
                    None,
                    None,
                ),
            },
        ),
    ],
)
def test_execute_sql_query(
    mock_custom_object_kd_map, client_id, databook_id, expected_result
):
    utc = pytz.UTC
    mock_custom_object_kd_map.return_value = {
        "user": datetime.datetime(2028, 2, 21, 11, 39, 43, 755557).replace(tzinfo=utc),
    }
    datasheetgraph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=True,
    )
    result = datasheetgraph._execute_sql_query()
    result = _convert_timezones(result)
    assert set(result) == expected_result


def _convert_timezones(input_dict) -> set:
    """
    Convert all datetimes in the data to UTC timezone and represent the data in the specified format
    """
    result_set = set()

    for key, value in input_dict.items():
        result_tuple = (
            UUID(key),
            _convert_to_utc(value.get("datasheet_kbd", None)),
            _convert_to_utc(value.get("dbkd_pkd_kbd", None)),
            _convert_to_utc(value.get("max_local_kd", None)),
            _convert_to_utc(value.get("max_global_kd", None)),
            _convert_to_utc(value.get("last_reverted_local_kd", None)),
            _convert_to_utc(value.get("last_reverted_global_kd", None)),
        )
        result_set.add(result_tuple)

    return result_set


def _convert_to_utc(dt):
    """
    Convert a datetime to UTC timezone if it is timezone-aware
    """
    if dt is not None and isinstance(dt, datetime.datetime):
        if dt.tzinfo is not None:
            return dt.astimezone(timezone.utc).replace(tzinfo=datetime.timezone.utc)
        else:
            return dt.replace(tzinfo=datetime.timezone.utc)
    return None


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, expected_result",
    [
        (
            3001,
            "09ae7e15-528e-4458-ba0a-f5f5daf764ea",
            {
                "15a059a8-861a-404b-818d-ea4fc8a89c86": 1,
                "2817d89e-112a-4930-9813-4bae562acafb": 1,
                "382a06f5-004f-46da-b9ed-74f2adad31dd": 1,
                "41a4435e-9d2c-4e34-809e-02797f25e6dc": 1,
                "5c0a4e4f-27bf-4806-bb4c-568e61c30215": 1,
                "666ed7ed-5a4f-45f0-bfa2-db1d23cb9d43": 1,
                "7f004654-32b1-4a61-8293-7ac0bebf49c1": 1,
                "8a445ffd-2c79-4856-8a63-d687710ae715": 1,
                "e691fb21-93fe-4859-a51b-e93874b580ba": 1,
                "ef98aef7-163d-4039-90ec-dd4a46d9cd34": 1,
                "ec58273d-d6ef-4192-8bbf-965bd68e424e": 2,
                "c52fb917-3306-4b69-908d-7c45d8f1ed2c": 2,
                "d249eec3-c247-447f-86e0-34b0555f1236": 2,
                "2db4a5d5-fa43-470e-8562-5aeec1d34250": 2,
                "3a890719-71f6-4638-8455-9d089ec80179": 2,
                "6e49bf84-0a9f-440f-b81f-e55a2d839449": 2,
                "4a2790c2-4920-48c9-991c-33b9ab3739c4": 2,
                "1a22aa22-b6da-408f-97ce-a52438d6dc62": 2,
                "d63d9ab1-6ce5-4e2e-bc70-bfd40b3fd478": 2,
                "e08343ae-d8fb-4391-8315-b0961e1a25a0": 2,
                "5e7bf79c-7fee-4d81-ba23-9523f92a109f": 2,
                "ed0cc67b-5247-4210-a112-573880a09f6a": 2,
                "178c4070-2dd4-414d-8a1b-61c30adaeb6c": 3,
                "89d406a2-834a-4ce7-bec3-77f254fc2af1": 3,
            },
        ),
    ],
)
def test_compute_datasheet_order(
    mock_custom_object_kd_map, client_id, databook_id, expected_result
):
    """
    Test compute_datasheet_order method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.compute_datasheet_order()
    assert set(result) == set(expected_result)


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, node_id, expected_result",
    [
        (
            3001,
            "09ae7e15-528e-4458-ba0a-f5f5daf764ea",
            100,
            False,
        ),
    ],
)
def test_is_node_exists(
    mock_custom_object_kd_map, client_id, databook_id, node_id, expected_result
):
    """
    Test is_node_exists method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.is_node_exists(node_id=node_id)
    assert result == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, expected_result",
    [
        (
            3001,
            "09ae7e15-528e-4458-ba0a-f5f5daf764ea",
            "89d406a2-834a-4ce7-bec3-77f254fc2af1",
            {
                "1a22aa22-b6da-408f-97ce-a52438d6dc62",
                "5e7bf79c-7fee-4d81-ba23-9523f92a109f",
                "3a890719-71f6-4638-8455-9d089ec80179",
                "15a059a8-861a-404b-818d-ea4fc8a89c86",
                "5c0a4e4f-27bf-4806-bb4c-568e61c30215",
            },
        ),
        (
            3001,
            "09ae7e15-528e-4458-ba0a-f5f5daf764ea",
            "178c4070-2dd4-414d-8a1b-61c30adaeb6c",
            {
                "15a059a8-861a-404b-818d-ea4fc8a89c86",
                "7f004654-32b1-4a61-8293-7ac0bebf49c1",
                "5c0a4e4f-27bf-4806-bb4c-568e61c30215",
            },
        ),
    ],
)
def test_datasheets_used_in_transformation_spec(
    mock_custom_object_kd_map, client_id, databook_id, datasheet_id, expected_result
):
    """
    Test datasheets_used_in_transformation_spec method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.datasheets_used_in_transformation_spec(
        datasheet_id=datasheet_id
    )
    assert set(result) == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, expected_result",
    [
        (
            3008,
            "71b673b4-4e7a-4962-a9b4-707270ebaaea",
            "368b0aaf-85ad-4479-99ca-2af424fa9f0f",
            ["c3b759ff-f733-4ebe-ad70-d85881e8c662"],
        ),
        (
            3008,
            "71b673b4-4e7a-4962-a9b4-707270ebaaea",
            "daf224bc-0c76-476e-b8d7-056b762d55b7",
            ["04740d3a-c10d-44c5-9367-04de771a877c"],
        ),
        (
            3008,
            "71b673b4-4e7a-4962-a9b4-707270ebaaea",
            "04740d3a-c10d-44c5-9367-04de771a877c",
            [],
        ),
        (
            3008,
            "71b673b4-4e7a-4962-a9b4-707270ebaaea",
            "c3b759ff-f733-4ebe-ad70-d85881e8c662",
            [],
        ),
        (
            3001,
            "09ae7e15-528e-4458-ba0a-f5f5daf764ea",
            "178c4070-2dd4-414d-8a1b-61c30adaeb6c",
            [],
        ),
    ],
)
def test_hierarchy_reference_datasheet_ids(
    mock_custom_object_kd_map, client_id, databook_id, datasheet_id, expected_result
):
    """
    Test datasheets_used_in_transformation_spec method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.hierarchy_reference_datasheet_ids(
        datasheet_id=datasheet_id
    )
    assert result == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, datasheet_ids, expected_result",
    [
        (
            3001,
            [
                "0b399893-a10a-455f-a6c7-69b1cc68d2fb",
                "cfa9bcc3-4f2d-428c-b903-cbda65cbe8b4",
                "d3276870-5f53-492c-b24a-fd53dbaced91",
                "3305353e-9367-4329-a5b7-c878f547d3c4",
                "03ec48ac-623d-4693-a0ec-b37b982e590e",
                "6a3ef60d-ab4a-4104-8fc6-e891d0d5d04d",
                "73ef28e6-58a4-4ca7-a0b5-a428b8f656a1",
                "83618115-b565-49c3-8340-a40a4fd91f7a",
                "f9dd1e84-4321-410d-af5d-d5187a408ed5",
                "061fe09f-c3d4-4d6f-bdf8-317c561cd9f1",
            ],
            (
                {
                    (
                        frozenset(
                            {
                                "1144c3c1-1a52-4f6a-8dfe-6ed25b769ad1",
                                "a1b8cf5a-7f27-4c2a-8a55-b34b61409e38",
                                "1623ab06-a264-4d4c-ad5d-4194b61b55b2",
                                "025be5a7-c94f-4eba-95ae-60eaaacda458",
                                "e72290d3-ed51-4783-ba8c-975c7314f041",
                                "f496559e-55ec-481e-b1fd-0452ede0f339",
                                "1857263a-b9ee-4742-9ac3-************",
                            }
                        ),
                        frozenset(
                            {
                                "0b399893-a10a-455f-a6c7-69b1cc68d2fb",
                                "061fe09f-c3d4-4d6f-bdf8-317c561cd9f1",
                                "6a3ef60d-ab4a-4104-8fc6-e891d0d5d04d",
                                "83618115-b565-49c3-8340-a40a4fd91f7a",
                                "f9dd1e84-4321-410d-af5d-d5187a408ed5",
                                "d3276870-5f53-492c-b24a-fd53dbaced91",
                                "03ec48ac-623d-4693-a0ec-b37b982e590e",
                                "cfa9bcc3-4f2d-428c-b903-cbda65cbe8b4",
                                "73ef28e6-58a4-4ca7-a0b5-a428b8f656a1",
                                "3305353e-9367-4329-a5b7-c878f547d3c4",
                            }
                        ),
                    )
                },
                {
                    "061fe09f-c3d4-4d6f-bdf8-317c561cd9f1",
                    "1144c3c1-1a52-4f6a-8dfe-6ed25b769ad1",
                    "a1b8cf5a-7f27-4c2a-8a55-b34b61409e38",
                    "83618115-b565-49c3-8340-a40a4fd91f7a",
                    "025be5a7-c94f-4eba-95ae-60eaaacda458",
                    "1857263a-b9ee-4742-9ac3-************",
                    "d3276870-5f53-492c-b24a-fd53dbaced91",
                    "73ef28e6-58a4-4ca7-a0b5-a428b8f656a1",
                    "6a3ef60d-ab4a-4104-8fc6-e891d0d5d04d",
                    "0b399893-a10a-455f-a6c7-69b1cc68d2fb",
                    "1623ab06-a264-4d4c-ad5d-4194b61b55b2",
                    "e72290d3-ed51-4783-ba8c-975c7314f041",
                    "f496559e-55ec-481e-b1fd-0452ede0f339",
                    "03ec48ac-623d-4693-a0ec-b37b982e590e",
                    "cfa9bcc3-4f2d-428c-b903-cbda65cbe8b4",
                    "f9dd1e84-4321-410d-af5d-d5187a408ed5",
                    "3305353e-9367-4329-a5b7-c878f547d3c4",
                },
                17,
                set(),
            ),
        ),
        (
            3016,
            None,
            (
                {
                    (frozenset({"3c9e8d18-e069-4442-983d-3e393f05a8f5"}),),
                    (
                        frozenset({"4b17a482-b300-4119-b776-04601dcb03f6"}),
                        frozenset({"c6fb0751-8c49-4ec0-a617-e09d7f48e4a5"}),
                    ),
                    (frozenset({"b1d164da-aad0-456a-a7d6-acd5066ee569"}),),
                    (
                        frozenset({"799d836b-4eaf-4be8-8fdb-83fac2791e15"}),
                        frozenset({"bccadbcb-3536-4fd9-af1a-804005629fa9"}),
                    ),
                },
                {
                    "c6fb0751-8c49-4ec0-a617-e09d7f48e4a5",
                    "4b17a482-b300-4119-b776-04601dcb03f6",
                    "799d836b-4eaf-4be8-8fdb-83fac2791e15",
                    "b1d164da-aad0-456a-a7d6-acd5066ee569",
                    "3c9e8d18-e069-4442-983d-3e393f05a8f5",
                    "bccadbcb-3536-4fd9-af1a-804005629fa9",
                },
                6,
                set(),
            ),
        ),
        (
            3001,
            None,
            (
                {
                    (frozenset({"8a02cfb2-5988-4613-85c1-414735e516cc"}),),
                    (frozenset({"da8c0a02-2f03-4fc1-b8f2-1dcf567737d0"}),),
                    (frozenset({"e67d588a-96cd-4836-9f7e-6f540b93e7aa"}),),
                    (
                        frozenset({"25c41904-8dcb-4de3-bfd6-bee657440c5e"}),
                        frozenset(
                            {
                                "ad038a58-5bbf-4a28-80ec-0ec911c13701",
                                "db9d0d97-bee9-402e-a148-e6ece45ebddd",
                            }
                        ),
                    ),
                    (
                        frozenset({"4c238119-b8e6-4934-a382-3bfb0121af17"}),
                        frozenset(
                            {
                                "f7e3c7c8-6fa7-4ec1-a41a-6be756085c9a",
                                "21cc34df-50e3-4cca-bf4b-16e3f2be3a33",
                            }
                        ),
                    ),
                    (frozenset({"2c49191c-2510-429a-8f02-cd74e8f4e482"}),),
                    (frozenset({"9d0e8a2c-a351-4937-af9f-eec2284d5bd1"}),),
                    (
                        frozenset({"382a06f5-004f-46da-b9ed-74f2adad31dd"}),
                        frozenset({"d249eec3-c247-447f-86e0-34b0555f1236"}),
                    ),
                    (frozenset({"8a445ffd-2c79-4856-8a63-d687710ae715"}),),
                    (
                        frozenset({"dd2ecdfc-a825-439a-9705-f8e4fa37534b"}),
                        frozenset({"bdb082f3-c46e-4ba9-b6b9-bc159f687c58"}),
                    ),
                    (
                        frozenset({"e82c384f-f8c9-4239-bd02-87d699f8df66"}),
                        frozenset({"e864a451-f696-4ce1-9550-7a895f1f2569"}),
                    ),
                    (frozenset({"4a4a23ca-11bb-4af9-96c0-9275be14bc55"}),),
                    (
                        frozenset({"5206bac2-fdf1-4b1f-966b-18796876ca2c"}),
                        frozenset({"b4c55fba-1714-4196-9309-3bb878d1db59"}),
                    ),
                    (
                        frozenset(
                            {
                                "6c820b01-ef76-43b1-96b3-c911c671c182",
                                "f1a63db0-30c1-4501-b80e-e306dde689c6",
                            }
                        ),
                        frozenset(
                            {
                                "be560680-9def-455c-8f32-0c0a5fab8117",
                                "dfe324a8-80e8-4f4c-b427-697dbcb40fd5",
                            }
                        ),
                        frozenset({"71a25d46-f118-4a5d-ab0a-de9165146842"}),
                    ),
                    (
                        frozenset({"889a31ed-6600-47dd-a006-c6b3c32bf2ea"}),
                        frozenset(
                            {
                                "73a695d9-d46e-4ab2-a698-6ebb371a141f",
                                "018de25b-de69-4c5a-a2da-23f2a361bfbb",
                            }
                        ),
                    ),
                    (frozenset({"3b50a648-eee9-4de6-b1f5-478950100a4f"}),),
                    (
                        frozenset(
                            {
                                "ef98aef7-163d-4039-90ec-dd4a46d9cd34",
                                "15a059a8-861a-404b-818d-ea4fc8a89c86",
                                "7f004654-32b1-4a61-8293-7ac0bebf49c1",
                                "5c0a4e4f-27bf-4806-bb4c-568e61c30215",
                                "2817d89e-112a-4930-9813-4bae562acafb",
                                "41a4435e-9d2c-4e34-809e-02797f25e6dc",
                                "666ed7ed-5a4f-45f0-bfa2-db1d23cb9d43",
                                "e691fb21-93fe-4859-a51b-e93874b580ba",
                            }
                        ),
                        frozenset(
                            {
                                "5e7bf79c-7fee-4d81-ba23-9523f92a109f",
                                "1a22aa22-b6da-408f-97ce-a52438d6dc62",
                                "4a2790c2-4920-48c9-991c-33b9ab3739c4",
                                "ed0cc67b-5247-4210-a112-573880a09f6a",
                                "ec58273d-d6ef-4192-8bbf-965bd68e424e",
                                "6e49bf84-0a9f-440f-b81f-e55a2d839449",
                                "e08343ae-d8fb-4391-8315-b0961e1a25a0",
                                "c52fb917-3306-4b69-908d-7c45d8f1ed2c",
                                "d63d9ab1-6ce5-4e2e-bc70-bfd40b3fd478",
                                "3a890719-71f6-4638-8455-9d089ec80179",
                                "2db4a5d5-fa43-470e-8562-5aeec1d34250",
                            }
                        ),
                        frozenset(
                            {
                                "178c4070-2dd4-414d-8a1b-61c30adaeb6c",
                                "89d406a2-834a-4ce7-bec3-77f254fc2af1",
                            }
                        ),
                    ),
                    (frozenset({"0f1633d4-af2d-41e5-af59-704a0e573ed0"}),),
                    (
                        frozenset({"82f71a15-1771-45c9-ab52-8ca304f8bc83"}),
                        frozenset({"94d20128-8590-4ce8-ab3f-17c1006bfdbd"}),
                    ),
                    (frozenset({"67bf2dfa-c884-42c6-8e8a-b369b7063c8c"}),),
                    (frozenset({"2ddd0225-b8e1-4695-bd34-585d14c7f12c"}),),
                    (
                        frozenset(
                            {
                                "1144c3c1-1a52-4f6a-8dfe-6ed25b769ad1",
                                "a1b8cf5a-7f27-4c2a-8a55-b34b61409e38",
                                "025be5a7-c94f-4eba-95ae-60eaaacda458",
                                "e72290d3-ed51-4783-ba8c-975c7314f041",
                                "bcdadf42-4e9a-4ac4-a83f-6ff93d9cb48c",
                                "1623ab06-a264-4d4c-ad5d-4194b61b55b2",
                                "1857263a-b9ee-4742-9ac3-************",
                                "f496559e-55ec-481e-b1fd-0452ede0f339",
                            }
                        ),
                        frozenset(
                            {
                                "061fe09f-c3d4-4d6f-bdf8-317c561cd9f1",
                                "f9dd1e84-4321-410d-af5d-d5187a408ed5",
                                "0b399893-a10a-455f-a6c7-69b1cc68d2fb",
                                "cfa9bcc3-4f2d-428c-b903-cbda65cbe8b4",
                                "03ec48ac-623d-4693-a0ec-b37b982e590e",
                                "d3276870-5f53-492c-b24a-fd53dbaced91",
                                "73ef28e6-58a4-4ca7-a0b5-a428b8f656a1",
                                "6a3ef60d-ab4a-4104-8fc6-e891d0d5d04d",
                                "5777f92c-7db9-4e6b-bee8-85fdb014d32a",
                                "83618115-b565-49c3-8340-a40a4fd91f7a",
                                "3305353e-9367-4329-a5b7-c878f547d3c4",
                            }
                        ),
                        frozenset(
                            {
                                "6729bd09-ff47-439c-9351-af5a2479822b",
                                "7b5e8b3c-93f5-4224-af03-ff289b0b3579",
                            }
                        ),
                    ),
                    (
                        frozenset(
                            {
                                "76664888-44db-4022-bb4e-343a2937edc9",
                                "2dd3c5cc-7f82-4247-a3fa-aa37feda6784",
                            }
                        ),
                        frozenset({"aeed160f-e499-474d-a239-f1dae3589cef"}),
                        frozenset({"2d380751-226b-414a-9e64-5183237fcf33"}),
                    ),
                    (
                        frozenset(
                            {
                                "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                                "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
                            }
                        ),
                        frozenset(
                            {
                                "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
                                "a748e86a-5261-401a-8424-d10557ae558a",
                            }
                        ),
                        frozenset({"079b5209-9377-4d44-90f0-92937eb1c575"}),
                    ),
                    (
                        frozenset({"6b675ccc-34cd-46de-82fd-0fcb342d2a5a"}),
                        frozenset(
                            {
                                "73f9c8dd-b58d-479d-a38f-959a9dd2beea",
                                "65ade71b-c74f-43d3-83e8-7cf2bd8806dc",
                            }
                        ),
                    ),
                    (frozenset({"4e39463f-00f0-4ed3-b6b7-f240c85ccb2c"}),),
                    (frozenset({"7f2df1a8-b5d2-4110-8d99-6afbc402cfff"}),),
                    (
                        frozenset(
                            {
                                "343b27f2-4c43-482b-8135-c2dc04b12659",
                                "16606db0-4e8d-4a6a-ab62-d29871e42eae",
                            }
                        ),
                        frozenset({"d0c487b8-cc20-475b-88a4-0f352c0f28f2"}),
                        frozenset({"8114368e-7a0f-433a-b124-dcc6cde73e8f"}),
                    ),
                    (),
                    (frozenset({"59d0d7da-e9a8-41fc-8174-a2ee3781802f"}),),
                    (
                        frozenset({"beef65de-0cd5-4ee5-baf4-60501da75e73"}),
                        frozenset({"495cccd4-acbc-4810-8b62-14417a6f1b1b"}),
                    ),
                },
                {
                    "d0c487b8-cc20-475b-88a4-0f352c0f28f2",
                    "4a2790c2-4920-48c9-991c-33b9ab3739c4",
                    "e864a451-f696-4ce1-9550-7a895f1f2569",
                    "03ec48ac-623d-4693-a0ec-b37b982e590e",
                    "d3276870-5f53-492c-b24a-fd53dbaced91",
                    "e67d588a-96cd-4836-9f7e-6f540b93e7aa",
                    "bcdadf42-4e9a-4ac4-a83f-6ff93d9cb48c",
                    "6e49bf84-0a9f-440f-b81f-e55a2d839449",
                    "e08343ae-d8fb-4391-8315-b0961e1a25a0",
                    "9d0e8a2c-a351-4937-af9f-eec2284d5bd1",
                    "4e39463f-00f0-4ed3-b6b7-f240c85ccb2c",
                    "59d0d7da-e9a8-41fc-8174-a2ee3781802f",
                    "dfe324a8-80e8-4f4c-b427-697dbcb40fd5",
                    "25c41904-8dcb-4de3-bfd6-bee657440c5e",
                    "d63d9ab1-6ce5-4e2e-bc70-bfd40b3fd478",
                    "aeed160f-e499-474d-a239-f1dae3589cef",
                    "e82c384f-f8c9-4239-bd02-87d699f8df66",
                    "41a4435e-9d2c-4e34-809e-02797f25e6dc",
                    "e691fb21-93fe-4859-a51b-e93874b580ba",
                    "0b399893-a10a-455f-a6c7-69b1cc68d2fb",
                    "6b675ccc-34cd-46de-82fd-0fcb342d2a5a",
                    "2ddd0225-b8e1-4695-bd34-585d14c7f12c",
                    "ed0cc67b-5247-4210-a112-573880a09f6a",
                    "343b27f2-4c43-482b-8135-c2dc04b12659",
                    "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
                    "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
                    "83618115-b565-49c3-8340-a40a4fd91f7a",
                    "be560680-9def-455c-8f32-0c0a5fab8117",
                    "89d406a2-834a-4ce7-bec3-77f254fc2af1",
                    "16606db0-4e8d-4a6a-ab62-d29871e42eae",
                    "018de25b-de69-4c5a-a2da-23f2a361bfbb",
                    "dd2ecdfc-a825-439a-9705-f8e4fa37534b",
                    "5c0a4e4f-27bf-4806-bb4c-568e61c30215",
                    "bdb082f3-c46e-4ba9-b6b9-bc159f687c58",
                    "65ade71b-c74f-43d3-83e8-7cf2bd8806dc",
                    "5777f92c-7db9-4e6b-bee8-85fdb014d32a",
                    "079b5209-9377-4d44-90f0-92937eb1c575",
                    "061fe09f-c3d4-4d6f-bdf8-317c561cd9f1",
                    "5e7bf79c-7fee-4d81-ba23-9523f92a109f",
                    "cfa9bcc3-4f2d-428c-b903-cbda65cbe8b4",
                    "1a22aa22-b6da-408f-97ce-a52438d6dc62",
                    "2c49191c-2510-429a-8f02-cd74e8f4e482",
                    "82f71a15-1771-45c9-ab52-8ca304f8bc83",
                    "8114368e-7a0f-433a-b124-dcc6cde73e8f",
                    "94d20128-8590-4ce8-ab3f-17c1006bfdbd",
                    "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                    "666ed7ed-5a4f-45f0-bfa2-db1d23cb9d43",
                    "2dd3c5cc-7f82-4247-a3fa-aa37feda6784",
                    "3305353e-9367-4329-a5b7-c878f547d3c4",
                    "f9dd1e84-4321-410d-af5d-d5187a408ed5",
                    "382a06f5-004f-46da-b9ed-74f2adad31dd",
                    "1144c3c1-1a52-4f6a-8dfe-6ed25b769ad1",
                    "7f004654-32b1-4a61-8293-7ac0bebf49c1",
                    "71a25d46-f118-4a5d-ab0a-de9165146842",
                    "178c4070-2dd4-414d-8a1b-61c30adaeb6c",
                    "0f1633d4-af2d-41e5-af59-704a0e573ed0",
                    "73a695d9-d46e-4ab2-a698-6ebb371a141f",
                    "da8c0a02-2f03-4fc1-b8f2-1dcf567737d0",
                    "e72290d3-ed51-4783-ba8c-975c7314f041",
                    "beef65de-0cd5-4ee5-baf4-60501da75e73",
                    "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
                    "c52fb917-3306-4b69-908d-7c45d8f1ed2c",
                    "76664888-44db-4022-bb4e-343a2937edc9",
                    "b4c55fba-1714-4196-9309-3bb878d1db59",
                    "2db4a5d5-fa43-470e-8562-5aeec1d34250",
                    "3a890719-71f6-4638-8455-9d089ec80179",
                    "f7e3c7c8-6fa7-4ec1-a41a-6be756085c9a",
                    "73f9c8dd-b58d-479d-a38f-959a9dd2beea",
                    "67bf2dfa-c884-42c6-8e8a-b369b7063c8c",
                    "7b5e8b3c-93f5-4224-af03-ff289b0b3579",
                    "025be5a7-c94f-4eba-95ae-60eaaacda458",
                    "2d380751-226b-414a-9e64-5183237fcf33",
                    "6729bd09-ff47-439c-9351-af5a2479822b",
                    "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
                    "15a059a8-861a-404b-818d-ea4fc8a89c86",
                    "495cccd4-acbc-4810-8b62-14417a6f1b1b",
                    "5206bac2-fdf1-4b1f-966b-18796876ca2c",
                    "8a02cfb2-5988-4613-85c1-414735e516cc",
                    "2817d89e-112a-4930-9813-4bae562acafb",
                    "6c820b01-ef76-43b1-96b3-c911c671c182",
                    "889a31ed-6600-47dd-a006-c6b3c32bf2ea",
                    "1623ab06-a264-4d4c-ad5d-4194b61b55b2",
                    "ef98aef7-163d-4039-90ec-dd4a46d9cd34",
                    "a1b8cf5a-7f27-4c2a-8a55-b34b61409e38",
                    "21cc34df-50e3-4cca-bf4b-16e3f2be3a33",
                    "ad038a58-5bbf-4a28-80ec-0ec911c13701",
                    "3b50a648-eee9-4de6-b1f5-478950100a4f",
                    "73ef28e6-58a4-4ca7-a0b5-a428b8f656a1",
                    "ec58273d-d6ef-4192-8bbf-965bd68e424e",
                    "8a445ffd-2c79-4856-8a63-d687710ae715",
                    "d249eec3-c247-447f-86e0-34b0555f1236",
                    "db9d0d97-bee9-402e-a148-e6ece45ebddd",
                    "6a3ef60d-ab4a-4104-8fc6-e891d0d5d04d",
                    "1857263a-b9ee-4742-9ac3-************",
                    "4c238119-b8e6-4934-a382-3bfb0121af17",
                    "f496559e-55ec-481e-b1fd-0452ede0f339",
                    "a748e86a-5261-401a-8424-d10557ae558a",
                    "f1a63db0-30c1-4501-b80e-e306dde689c6",
                },
                108,
                set(),
            ),
        ),
    ],
)
def test_get_stale_datasheets_to_generate(
    mock_custom_object_kd_map,
    client_id,
    datasheet_ids,
    expected_result,
):
    """
    Test Get stale datasheets to generate
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=True
    )
    results = datasheet_graph.get_stale_datasheets_to_generate(
        datasheet_ids=datasheet_ids
    )
    assert results == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, datasheet_ids, force_invalidate_datasheet_ids, expected_result",
    [
        (
            3020,
            ["cdae5f92-c419-4747-b053-5c38d000a5b3"],
            ["cdae5f92-c419-4747-b053-5c38d000a5b3"],
            (
                {(frozenset({"cdae5f92-c419-4747-b053-5c38d000a5b3"}),)},
                {"cdae5f92-c419-4747-b053-5c38d000a5b3"},
                1,
                set(),
            ),
        ),
        (
            3020,
            ["cdae5f92-c419-4747-b053-5c38d000a5b3"],
            [],
            (
                {()},
                set(),
                1,
                set(),
            ),
        ),
    ],
)
def test_get_stale_datasheets_to_generate_with_force_invalidate(
    mock_custom_object_kd_map,
    client_id,
    datasheet_ids,
    force_invalidate_datasheet_ids,
    expected_result,
):
    """
    Test Get stale datasheets to generate with force invalidate
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=True
    )
    results = datasheet_graph.get_stale_datasheets_to_generate(
        datasheet_ids=datasheet_ids,
        force_invalidate_datasheet_ids=force_invalidate_datasheet_ids,
    )
    assert results == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, expected_result",
    [
        (
            3008,
            None,
            "daf224bc-0c76-476e-b8d7-056b762d55b7",
            [
                "daf224bc-0c76-476e-b8d7-056b762d55b7",
                "ddf0e78e-d4bb-48e4-8a49-828ee8031389",
                "f739562c-aa29-450a-b18d-ed0795fc3d5f",
            ],
        ),
        (
            3008,
            None,
            "04740d3a-c10d-44c5-9367-04de771a877c",
            [
                "04740d3a-c10d-44c5-9367-04de771a877c",
                "1dcc7ef7-a037-401d-8c01-d75b3e4c9be4",
                "f739562c-aa29-450a-b18d-ed0795fc3d5f",
                "ddf0e78e-d4bb-48e4-8a49-828ee8031389",
                "daf224bc-0c76-476e-b8d7-056b762d55b7",
            ],
        ),
        (
            3008,
            None,
            "c3b759ff-f733-4ebe-ad70-d85881e8c662",
            [
                "c3b759ff-f733-4ebe-ad70-d85881e8c662",
                "368b0aaf-85ad-4479-99ca-2af424fa9f0f",
            ],
        ),
    ],
)
def test_datasheet_childrens_list(
    mock_custom_object_kd_map, client_id, databook_id, datasheet_id, expected_result
):
    """
    Test datasheets_used_in_transformation_spec method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.datasheet_childrens_list(datasheet_id=datasheet_id)
    assert set(result) == set(expected_result)


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@patch("uuid.uuid4")
@pytest.mark.parametrize(
    "client_id, databook_id, expected_result",
    [
        (
            3008,
            None,
            {
                "9ebd14e7-6fa7-4a63-b8c4-e39147a973b0": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["9ebd14e7-6fa7-4a63-b8c4-e39147a973b0"],
                    datasheet_order=1,
                    datasheet_name="Accessories",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="9ebd14e7-6fa7-4a63-b8c4-e39147a973b0",
                        datasheet_name="Accessories",
                        source_id="3",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "daf224bc-0c76-476e-b8d7-056b762d55b7": DatasheetExecutionDetails(
                    databook_id="71b673b4-4e7a-4962-a9b4-707270ebaaea",
                    childrens=[
                        "daf224bc-0c76-476e-b8d7-056b762d55b7",
                        "ddf0e78e-d4bb-48e4-8a49-828ee8031389",
                        "f739562c-aa29-450a-b18d-ed0795fc3d5f",
                    ],
                    datasheet_order=2,
                    datasheet_name="hierarchy src_Copy",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="daf224bc-0c76-476e-b8d7-056b762d55b7",
                        datasheet_name="hierarchy src_Copy",
                        source_id="183",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents={"04740d3a-c10d-44c5-9367-04de771a877c"},
                    immediate_childrens={
                        "ddf0e78e-d4bb-48e4-8a49-828ee8031389",
                        "f739562c-aa29-450a-b18d-ed0795fc3d5f",
                    },
                ),
                "d1c0ea4f-450f-4ca9-9598-cb8a0c4c6627": DatasheetExecutionDetails(
                    databook_id="05a46f0c-724f-453d-8d7a-804868b3749e",
                    childrens=["d1c0ea4f-450f-4ca9-9598-cb8a0c4c6627"],
                    datasheet_order=1,
                    datasheet_name="ref sheet",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="d1c0ea4f-450f-4ca9-9598-cb8a0c4c6627",
                        datasheet_name="ref sheet",
                        source_id="182",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "c3b759ff-f733-4ebe-ad70-d85881e8c662": DatasheetExecutionDetails(
                    databook_id="71b673b4-4e7a-4962-a9b4-707270ebaaea",
                    childrens=[
                        "c3b759ff-f733-4ebe-ad70-d85881e8c662",
                        "368b0aaf-85ad-4479-99ca-2af424fa9f0f",
                    ],
                    datasheet_order=1,
                    datasheet_name="hierarchy ref circular dep",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="c3b759ff-f733-4ebe-ad70-d85881e8c662",
                        datasheet_name="hierarchy ref circular dep",
                        source_id="182",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens={"368b0aaf-85ad-4479-99ca-2af424fa9f0f"},
                ),
                "e4c17ffd-f412-420f-a8f4-92507d4b7548": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["e4c17ffd-f412-420f-a8f4-92507d4b7548"],
                    datasheet_order=1,
                    datasheet_name="Accounts",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="e4c17ffd-f412-420f-a8f4-92507d4b7548",
                        datasheet_name="Accounts",
                        source_id="1",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "160d4f98-718b-45b3-8769-5a5622c0f3c6": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["160d4f98-718b-45b3-8769-5a5622c0f3c6"],
                    datasheet_order=1,
                    datasheet_name="Deals_Copy",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="160d4f98-718b-45b3-8769-5a5622c0f3c6",
                        datasheet_name="Deals_Copy",
                        source_id="6",
                        source_type="object",
                        transformation_spec=[
                            {
                                "key": "189fa0dd-552f-433f-9a5d-acf7c1fa819e",
                                "type": "FILTER",
                                "value": None,
                                "col_name": "co_6_deal_id",
                                "operator": "IS_NOT_EMPTY",
                            }
                        ],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "0fe1ee87-2d23-43d0-9df9-9d51064afc77": DatasheetExecutionDetails(
                    databook_id="eec30859-c899-4376-b16b-be3c5dd287f5",
                    childrens=["0fe1ee87-2d23-43d0-9df9-9d51064afc77"],
                    datasheet_order=2,
                    datasheet_name="with current date as aod",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="0fe1ee87-2d23-43d0-9df9-9d51064afc77",
                        datasheet_name="with current date as aod",
                        source_id="183",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents={"ae28cc07-f7eb-414a-833e-c9b2c8fb0204"},
                    immediate_childrens=set(),
                ),
                "d3dba2d3-c0f1-4076-94d1-859bbc3009f3": DatasheetExecutionDetails(
                    databook_id="2515060f-e411-4ba4-a8b1-35f7f069727b",
                    childrens=["d3dba2d3-c0f1-4076-94d1-859bbc3009f3"],
                    datasheet_order=1,
                    datasheet_name="src",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="d3dba2d3-c0f1-4076-94d1-859bbc3009f3",
                        datasheet_name="src",
                        source_id="183",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "8f633738-23d1-4580-90b6-dec9e77e13e0": DatasheetExecutionDetails(
                    databook_id="2515060f-e411-4ba4-a8b1-35f7f069727b",
                    childrens=["8f633738-23d1-4580-90b6-dec9e77e13e0"],
                    datasheet_order=1,
                    datasheet_name="ref",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="8f633738-23d1-4580-90b6-dec9e77e13e0",
                        datasheet_name="ref",
                        source_id="182",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "1dcc7ef7-a037-401d-8c01-d75b3e4c9be4": DatasheetExecutionDetails(
                    databook_id="72f019d0-edc1-468b-a34e-56c02c6d4bbf",
                    childrens=["1dcc7ef7-a037-401d-8c01-d75b3e4c9be4"],
                    datasheet_order=2,
                    datasheet_name="hier_cross",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="1dcc7ef7-a037-401d-8c01-d75b3e4c9be4",
                        datasheet_name="hier_cross",
                        source_id="182",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents={"04740d3a-c10d-44c5-9367-04de771a877c"},
                    immediate_childrens=set(),
                ),
                "9b94e6a7-e026-4b65-b425-f6ab727fa3f9": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["9b94e6a7-e026-4b65-b425-f6ab727fa3f9"],
                    datasheet_order=1,
                    datasheet_name="Insurances",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="9b94e6a7-e026-4b65-b425-f6ab727fa3f9",
                        datasheet_name="Insurances",
                        source_id="4",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "ddf0e78e-d4bb-48e4-8a49-828ee8031389": DatasheetExecutionDetails(
                    databook_id="72f019d0-edc1-468b-a34e-56c02c6d4bbf",
                    childrens=["ddf0e78e-d4bb-48e4-8a49-828ee8031389"],
                    datasheet_order=3,
                    datasheet_name="hier_derived",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="ddf0e78e-d4bb-48e4-8a49-828ee8031389",
                        datasheet_name="hier_derived",
                        source_id="daf224bc-0c76-476e-b8d7-056b762d55b7",
                        source_type="datasheet",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents={"daf224bc-0c76-476e-b8d7-056b762d55b7"},
                    immediate_childrens=set(),
                ),
                "ae28cc07-f7eb-414a-833e-c9b2c8fb0204": DatasheetExecutionDetails(
                    databook_id="eec30859-c899-4376-b16b-be3c5dd287f5",
                    childrens=[
                        "ae28cc07-f7eb-414a-833e-c9b2c8fb0204",
                        "0fe1ee87-2d23-43d0-9df9-9d51064afc77",
                    ],
                    datasheet_order=1,
                    datasheet_name="hierarchy ref",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="ae28cc07-f7eb-414a-833e-c9b2c8fb0204",
                        datasheet_name="hierarchy ref",
                        source_id="182",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens={"0fe1ee87-2d23-43d0-9df9-9d51064afc77"},
                ),
                "f739562c-aa29-450a-b18d-ed0795fc3d5f": DatasheetExecutionDetails(
                    databook_id="71b673b4-4e7a-4962-a9b4-707270ebaaea",
                    childrens=["f739562c-aa29-450a-b18d-ed0795fc3d5f"],
                    datasheet_order=3,
                    datasheet_name="flatten",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="f739562c-aa29-450a-b18d-ed0795fc3d5f",
                        datasheet_name="flatten",
                        source_id="daf224bc-0c76-476e-b8d7-056b762d55b7",
                        source_type="datasheet",
                        transformation_spec=[
                            {
                                "key": "364bf9a7-19ce-44fd-a901-46e2698344f2",
                                "type": "FLATTEN",
                                "col_name": "cf_hierarchycalc",
                                "output_data_type": "String",
                                "flattened_col_name": "cf_hierarchycalc_flattened_33a4",
                            }
                        ],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents={"daf224bc-0c76-476e-b8d7-056b762d55b7"},
                    immediate_childrens=set(),
                ),
                "78f9755f-189a-4970-8f2c-e83ab57e8f24": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["78f9755f-189a-4970-8f2c-e83ab57e8f24"],
                    datasheet_order=1,
                    datasheet_name="Leads",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="78f9755f-189a-4970-8f2c-e83ab57e8f24",
                        datasheet_name="Leads",
                        source_id="5",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "368b0aaf-85ad-4479-99ca-2af424fa9f0f": DatasheetExecutionDetails(
                    databook_id="71b673b4-4e7a-4962-a9b4-707270ebaaea",
                    childrens=["368b0aaf-85ad-4479-99ca-2af424fa9f0f"],
                    datasheet_order=2,
                    datasheet_name="circular dep src",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="368b0aaf-85ad-4479-99ca-2af424fa9f0f",
                        datasheet_name="circular dep src",
                        source_id="183",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents={"c3b759ff-f733-4ebe-ad70-d85881e8c662"},
                    immediate_childrens=set(),
                ),
                "9ce1564c-148e-438e-9cd5-b973b7bf9e92": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["9ce1564c-148e-438e-9cd5-b973b7bf9e92"],
                    datasheet_order=1,
                    datasheet_name="Cars",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="9ce1564c-148e-438e-9cd5-b973b7bf9e92",
                        datasheet_name="Cars",
                        source_id="2",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
                "04740d3a-c10d-44c5-9367-04de771a877c": DatasheetExecutionDetails(
                    databook_id="71b673b4-4e7a-4962-a9b4-707270ebaaea",
                    childrens=[
                        "04740d3a-c10d-44c5-9367-04de771a877c",
                        "ddf0e78e-d4bb-48e4-8a49-828ee8031389",
                        "daf224bc-0c76-476e-b8d7-056b762d55b7",
                        "f739562c-aa29-450a-b18d-ed0795fc3d5f",
                        "1dcc7ef7-a037-401d-8c01-d75b3e4c9be4",
                    ],
                    datasheet_order=1,
                    datasheet_name="hierarchy ref 1",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="04740d3a-c10d-44c5-9367-04de771a877c",
                        datasheet_name="hierarchy ref 1",
                        source_id="182",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens={
                        "1dcc7ef7-a037-401d-8c01-d75b3e4c9be4",
                        "daf224bc-0c76-476e-b8d7-056b762d55b7",
                    },
                ),
                "70944c1e-408f-41a3-b3f3-baa4edd1a1ec": DatasheetExecutionDetails(
                    databook_id="0b90840c-78aa-4328-a66f-a44a6c679bcc",
                    childrens=["70944c1e-408f-41a3-b3f3-baa4edd1a1ec"],
                    datasheet_order=1,
                    datasheet_name="Deals",
                    details_for_snowflake_transformation=TranformationInputDetails(
                        datasheet_id="70944c1e-408f-41a3-b3f3-baa4edd1a1ec",
                        datasheet_name="Deals",
                        source_id="6",
                        source_type="object",
                        transformation_spec=[],
                    ),
                    sync_run_id=UUID("3215442f-8302-4ee8-b641-fc93425d6b89"),
                    immediate_parents=set(),
                    immediate_childrens=set(),
                ),
            },
        ),
    ],
)
def test_datasheet_details(
    mock_uuid4, mock_custom_object_kd_map, client_id, databook_id, expected_result
):
    """
    Test datasheets_used_in_transformation_spec method
    """
    mock_custom_object_kd_map.return_value = {}
    mock_uuid4.return_value = UUID("3215442f-8302-4ee8-b641-fc93425d6b89")
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.datasheet_details()

    # assert dict keys
    result_keys = set(result.keys())
    expected_result_keys = set(expected_result.keys())
    assert result_keys == expected_result_keys

    for key, value in result.items():
        expected_result_class = expected_result[key]
        result_class = result[key]
        assert expected_result_class.databook_id == result_class.databook_id
        assert expected_result_class.datasheet_order == result_class.datasheet_order
        assert set(expected_result_class.childrens) == set(result_class.childrens)
        assert expected_result_class.datasheet_name == result_class.datasheet_name
        assert expected_result_class.immediate_parents == result_class.immediate_parents
        assert (
            expected_result_class.immediate_childrens
            == result_class.immediate_childrens
        )
        assert (
            expected_result_class.details_for_snowflake_transformation
            == result_class.details_for_snowflake_transformation
        )


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, expected_result",
    [
        (
            3008,
            None,
            "04740d3a-c10d-44c5-9367-04de771a877c",
            "DiGraph with 14 nodes and 16 edges",
        ),
        (
            3008,
            None,
            "123",
            "The node_id 123 is not found in the datasheet graph for client_id 3008.",
        ),
    ],
)
def test_get_subgraph(
    mock_custom_object_kd_map, client_id, databook_id, datasheet_id, expected_result
):
    """
    Test datasheets_used_in_transformation_spec method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = None
    try:
        result = datasheet_graph.get_subgraph(datasheet_id=datasheet_id)
    except Exception as exc:
        result = exc
    assert str(result) == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, expected_result",
    [
        (
            3001,
            None,
            None,
            {
                "quota",
                "quota_attainment",
                "commission",
                "user",
                "inter_commission",
                "settlement",
                "commission_summary",
                "inter_quota_attainment",
                "forecast_commission",
                "forecast_quota_attainment",
                "inter_forecast_commission",
                "inter_forecast_quota_attainment",
                "payout",
                "fx_rate",
                "statement_approvals",
                "quote",
                "quote_line_item",
                "quote_line_item_tier",
            },
        ),
    ],
)
def test_associated_report_objects(
    mock_custom_object_kd_map,
    client_id,
    databook_id,
    datasheet_id,
    expected_result,
):
    """
    Test test_stale_report_object_ids method
    """
    mock_custom_object_kd_map.return_value = {}
    set_client_feature(client_id, "show_payout_report_object", True)
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.associated_report_objects(datasheet_id=datasheet_id)
    unset_client_feature(client_id, "show_payout_report_object")
    assert set(result) == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, expected_result",
    [
        (
            3001,
            "7283241f-42c9-48f6-834d-e853f0522f1b",
            {
                "3b50a648-eee9-4de6-b1f5-478950100a4f",
                "079b5209-9377-4d44-90f0-92937eb1c575",
                "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
                "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
                "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
                "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
                "a748e86a-5261-401a-8424-d10557ae558a",
            },
        ),
    ],
)
def test_datasheets_in_databook(
    mock_custom_object_kd_map, client_id, databook_id, expected_result
):
    """
    Test for datasheets_in_databook method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)
    result = datasheet_graph.datasheets(databook_id=databook_id)
    result_datasheet_ids = {datasheet.node_id for datasheet in result}
    assert result_datasheet_ids == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, expected_result",
    [
        (
            3018,
            {
                "72e18fe9-c267-4a99-89b0-e8d73373cc58",
                "cc48a2a9-2d6b-4fba-bde0-28b8a4c5a838",
                "889a3a5f-75dd-47ca-99dc-9ad79f4a375b",
                "82d55ae6-e2b5-44cb-817b-619b03f70b98",
                "e58d4bc1-f589-4ef7-9f86-1ea1b2340212",
                "f16f630a-0245-4094-9dff-c149d765ab27",
                "ac235969-478c-4f7b-80fa-717d3e6307c3",
            },
        ),
        (
            3001,
            {
                "7283241f-42c9-48f6-834d-e853f0522f1b",
                "09ae7e15-528e-4458-ba0a-f5f5daf764ea",
                "3c73337d-4e56-4d5e-942e-f62b555a813b",
                "f7395e4d-2d85-4e6b-9412-49d09de554ef",
                "557ffd85-5af4-42e7-944e-1119f42d8f6d",
                "ed9282db-7767-44f5-84a9-e836191073c9",
                "26538f83-580d-40dd-bd43-93a2f171bc0f",
                "8117108d-2bae-41de-bb19-b312ca54228e",
            },
        ),
    ],
)
def test_unqiue_databook_ids(mock_custom_object_kd_map, client_id, expected_result):
    """
    Test for unqiue_databook_ids method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id)
    result = datasheet_graph.unqiue_databook_ids()
    assert expected_result == result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, source_id, destination_id",
    [
        (
            3010,
            "commission",
            "34056b71-7234-4147-a0ed-94119d6a46a1",
        ),
    ],
)
def test_remove_path_succesful_case(
    mock_custom_object_kd_map, client_id, source_id, destination_id
):
    """
    Test for remove_path method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id)
    # Creating a edge between source and destination
    # and then test this function  whether it is removing the edge or not
    datasheet_graph.establish_paths(
        source_ids=[source_id], destination_ids=[destination_id]
    )

    result_establish_path = datasheet_graph.parent(datasheet_id=destination_id)

    result_node = list(result_establish_path)[0]

    assert result_node.node_id == source_id

    datasheet_graph.remove_path(source_ids=[source_id], destination_id=destination_id)

    result_remove_edge = datasheet_graph.parent(datasheet_id=destination_id)

    assert result_remove_edge == set()


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, source_id, destination_id",
    [
        (
            3010,
            "user",
            "34056b71-7234-4147-a0ed-94119d6a46a1",
        ),
    ],
)
def test_remove_path_exception_case(
    mock_custom_object_kd_map, client_id, source_id, destination_id
):
    """
    Test for remove_path exception method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id)

    with pytest.raises(Exception):
        datasheet_graph.remove_path(
            source_ids=[source_id], destination_id=destination_id
        )


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, node_ids, expected_result",
    [
        (3010, None, None, (5, 6)),
        (
            3001,
            "7283241f-42c9-48f6-834d-e853f0522f1b",
            [
                "4a4a23ca-11bb-4af9-96c0-9275be14bc55",
                "6c9fc1ff-ec14-4586-9fd7-7bb1728fbcdd",
                "7f2df1a8-b5d2-4110-8d99-6afbc402cfff",
                "a748e86a-5261-401a-8424-d10557ae558a",
                "ab074ee8-4ae1-4126-9a7e-32062bc64c36",
                "affbea4d-aaec-41a5-8f92-1ae6730dfeca",
            ],
            (2, 4),
        ),
        (
            3001,
            "7283241f-42c9-48f6-834d-e853f0522f1b",
            None,
            (4, 5),
        ),
    ],
)
def test_calculate_width_and_height_for_graph(
    mock_custom_object_kd_map, client_id, databook_id, node_ids, expected_result
):
    """
    Test for calculate_width_and_height_for_graph method
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(client_id=client_id, databook_id=databook_id)

    result = datasheet_graph.calculate_width_and_height_for_graph(node_ids=node_ids)
    assert result == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    "datasheet_id, expected_result",
    [
        (
            "e82c384f-f8c9-4239-bd02-87d699f8df66",
            IncludeStaleInformationQueryOnDatasheetGraphException(
                function_name="is_datasheet_data_stale"
            ).message,
        )
    ],
)
def test_include_stale_information_query_exception(datasheet_id, expected_result):
    databook_id = "3c73337d-4e56-4d5e-942e-f62b555a813b"
    client_id = 3001
    datasheetgraph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=False,
    )

    with pytest.raises(
        IncludeStaleInformationQueryOnDatasheetGraphException
    ) as exc_info:
        datasheetgraph.is_datasheet_data_stale(datasheet_id)

    assert str(exc_info.value) == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, test_description, expected_result",
    [
        (
            3020,
            "0f9d3109-2586-494b-be2a-f0d64ad3d337",
            "bf19a705-fb75-4879-af69-2c238824b659",
            "Local Adjustment check",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.LOCAL_ADJUSTMENT_ADDED.value,
            },
        ),
        (
            3020,
            "0f9d3109-2586-494b-be2a-f0d64ad3d337",
            "48494a91-4be1-4b5e-a74e-1515c80f57eb",
            "Ancestor Modified",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.ANCESTOR_MODIFIED.value,
            },
        ),
        (
            3020,
            "4ebc651b-1fff-4d23-801f-3b3caab266b0",
            "4d506572-81f1-4af4-8615-2956dc84c695",
            "Report object ancestor source stale",
            {
                "is_report_data_stale": True,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.SOURCE_REPORT_OBJECT_DATA_MODIFIED.value,
            },
        ),
        (
            3020,
            "6219a4e7-b3e1-4add-aeb8-1a4b0b52e830",
            "8bb60267-a762-47d2-9e5e-d093c82a2031",
            "Not stale check",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": False,
                "reason": None,
            },
        ),
        (
            3020,
            "0958a796-f168-4fd6-b823-1ab834be8e96",
            "cdb89435-e5ef-40a6-b1c3-3a1f028442b1",
            "Commission adjustment check",
            {
                "is_report_data_stale": True,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.SOURCE_REPORT_OBJECT_DATA_MODIFIED.value,
            },
        ),
        (
            3020,
            "0958a796-f168-4fd6-b823-1ab834be8e96",
            "f2d4d889-c93f-4ced-9318-62fd39b9a935",
            "Commission Summary report source stale check",
            {
                "is_report_data_stale": True,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.SOURCE_REPORT_OBJECT_DATA_MODIFIED.value,
            },
        ),
        (
            3020,
            "0f9d3109-2586-494b-be2a-f0d64ad3d337",
            "cdae5f92-c419-4747-b053-5c38d000a5b3",
            "Not stale check",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": False,
                "reason": None,
            },
        ),
        (
            3020,
            "0f9d3109-2586-494b-be2a-f0d64ad3d337",
            "386d5e93-e5fd-48fd-98d7-74b388830d2a",
            "Current datasheet config modified check -  calcfield modify",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.CURRENT_SHEET_CONFIG_MODIFIED.value,
            },
        ),
        (
            3020,
            "0f9d3109-2586-494b-be2a-f0d64ad3d337",
            "79cdca1c-b10b-4045-85dc-7d37a4285265",
            "Current datasheet config modified check - transform modify",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.CURRENT_SHEET_CONFIG_MODIFIED.value,
            },
        ),
        (
            3020,
            "2d739613-748d-4f15-8a3f-8fc59ea524ac",
            "82f34e94-b566-432b-b3f5-ef799fc974ed",
            "Ancestor datasheet config modified check - transform modify",
            {
                "is_report_data_stale": False,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.ANCESTOR_SHEET_CONFIG_MODIFIED.value,
            },
        ),
    ],
)
def test_is_datasheet_data_stale(
    mock_custom_object_kd_map,
    client_id,
    databook_id,
    datasheet_id,
    test_description,
    expected_result,
):
    """
    Test for is_datasheet_data_stale
    """
    mock_custom_object_kd_map.return_value = {}
    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=True,
    )

    result = datasheet_graph.is_datasheet_data_stale(datasheet_id=datasheet_id)
    assert result == expected_result


@pytest.mark.django_db
@patch(
    "spm.services.datasheet_graph.datasheet_graph.knowledge_date_map_for_custom_and_report_objects"
)
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, test_description, expected_result",
    [
        (
            3020,
            "4ebc651b-1fff-4d23-801f-3b3caab266b0",
            "4d506572-81f1-4af4-8615-2956dc84c695",
            "This test is to verify the datetime comparison in staleness check when one of the datetime is None here the latest knowledge date for user report is None",
            {
                "is_report_data_stale": True,
                "is_datasheet_stale": True,
                "reason": DatasheetDataRegenerateReason.SOURCE_REPORT_OBJECT_DATA_MODIFIED.value,
            },
        ),
    ],
)
def test_is_datasheet_data_stale_datetime_none_comparison(
    mock_custom_object_kd_map,
    client_id,
    databook_id,
    datasheet_id,
    test_description,
    expected_result,
):
    """
    Test for is_datasheet_data_stale when the latest knowledge date is None
    """
    mock_custom_object_kd_map.return_value = {"user_report": None}
    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=True,
    )

    result = datasheet_graph.is_datasheet_data_stale(datasheet_id=datasheet_id)
    assert result == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, test_description, expected_result",
    [
        (
            3001,
            None,
            "89d406a2-834a-4ce7-bec3-77f254fc2af1",
            "Datasheet which contains multiple transformations",
            {
                "5c0a4e4f-27bf-4806-bb4c-568e61c30215",
                "4a2790c2-4920-48c9-991c-33b9ab3739c4",
                "5e7bf79c-7fee-4d81-ba23-9523f92a109f",
                "3a890719-71f6-4638-8455-9d089ec80179",
                "15a059a8-861a-404b-818d-ea4fc8a89c86",
                "1a22aa22-b6da-408f-97ce-a52438d6dc62",
            },
        ),
        (
            3001,
            None,
            "67bf2dfa-c884-42c6-8e8a-b369b7063c8c",
            "Datasheet which is derived from custom object and doesn't have any datasheet parent",
            set(),
        ),
    ],
)
def test_immediate_datasheet_parent(
    client_id,
    databook_id,
    datasheet_id,
    test_description,
    expected_result,
):
    """
    Test function for immediate_datasheet_parent
    """
    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=True,
    )

    result = datasheet_graph.immediate_datasheet_parent(datasheet_id=datasheet_id)
    assert result == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    "client_id, databook_id, datasheet_id, test_description, expected_result",
    [
        (
            3001,
            None,
            "89d406a2-834a-4ce7-bec3-77f254fc2af1",
            "Datasheet that doesn't have any childrens",
            set(),
        ),
        (
            3001,
            None,
            "41a4435e-9d2c-4e34-809e-02797f25e6dc",
            "Datasheet having one immediate chidren",
            {"2db4a5d5-fa43-470e-8562-5aeec1d34250"},
        ),
    ],
)
def test_immediate_datasheet_children(
    client_id,
    databook_id,
    datasheet_id,
    test_description,
    expected_result,
):
    """
    Test fucntion for immediate_datasheet_children
    """
    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        databook_id=databook_id,
        include_stale_information_query=True,
    )

    result = datasheet_graph.immediate_datasheet_children(datasheet_id=datasheet_id)
    assert result == expected_result


@pytest.mark.django_db
@pytest.mark.parametrize(
    "client_id, custom_object_id, expected_result",
    [
        (
            3024,
            "1",
            {
                "13c8b56a-2d16-4e6f-8a63-dd3f66362c19",
                "aba0bc39-2998-4253-b840-1488a6cce590",
                "3845e454-e467-4a8b-a356-800fa82f867d",
                "d75be811-f381-4239-9448-c20c6f060bac",
                "95c72696-a920-44a8-9875-52ffb68e48cf",
                "761e5243-9114-4e6a-8b9d-879e4343d65d",
                "ad4c9bf0-bdeb-4fd0-8f55-679005828e94",
                "e11ae674-a0f6-414d-8a21-a64384f875dc",
                "dd6075f9-7374-4f3e-962f-a4f88887d2e1",
                "755317b1-b3bd-4f7c-ad6a-1a0c2c9e9343",
                "c916f416-61a9-4cc1-bc48-76235cf9c220",
                "26a4ae1e-f0b3-4ee4-9135-b8cd5aa0a74f",
                "7b5eeb70-8f51-4355-84ee-b2de3e5f1b19",
                "c6a85425-750e-4d82-8274-0b2e7925b31a",
                "d4081bc0-fbf5-44f1-b7a6-fe41b9bb2287",
                "db20f49b-2119-4232-9d25-d11dfdc49257",
            },
        ),
    ],
)
def test_all_dependent_datasheet_ids(
    client_id,
    custom_object_id,
    expected_result,
):
    """
    Test fucntion for immediate_datasheet_children
    """
    datasheet_graph = DataSheetGraph(client_id=client_id)

    result = datasheet_graph.all_dependent_datasheet_ids(
        custom_object_id=custom_object_id
    )
    assert result == expected_result
